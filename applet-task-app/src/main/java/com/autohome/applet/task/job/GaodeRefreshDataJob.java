package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GaodeRefreshDataJob {
    @Qualifier("lightapp")

    @XxlJob("GaodeRefreshDataOntimenote180")
    public ReturnT<String> gaodeRefreshDataOntimenote180() {
        Map<String, Object> param = new HashMap<>();
        try {
            //String url = "http://uc-car-openservice.cupid.autohome.com.cn/car/refreshNewBrandsByTime?token=496d2bc8-90e2-4720-ae48-9120c3a3fb39";
            String url = "https://uccaropenservice.api.autohome.com.cn/car/refreshNewBrandsByTime?token=496d2bc8-90e2-4720-ae48-9120c3a3fb39";
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance(10000).httpGet(url, param);
            Map result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<Map>() {
            });
            JobLog.info("refreshNewBrandsByTimeResult:" + result);
        } catch (BusinessException e) {
            log.error("refreshNewBrandsByTime", e);
        }
        return ReturnT.SUCCESS;
    }


}