package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.donate.DonateQueryModel;
import com.autohome.applet.service.donate.HuaweiDonateClass;
import com.autohome.applet.service.donate.IosDonateClass;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 捐赠数据入库(车家号、CMS、VIDEO)
 * <AUTHOR>
@Slf4j
@Component
public class DonateDataJob {
    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    private HuaweiDonateClass huaweiDonateClass;
    @Autowired
    private IosDonateClass iosDonateClass;

    //设置并发数量
    private final static int POOL_SIZE = 1; //Runtime.getRuntime().availableProcessors();
    private ForkJoinPool forkJoinPool = new ForkJoinPool(POOL_SIZE);
    @XxlJob("DonateHuaweiJob")
    public ReturnT<String> handleHuawei() {
        JobLog.info("DonateHuaweiJob.handle() POOL_SIZE:" + POOL_SIZE);
        String Param = XxlJobHelper.getJobParam();
        JobLog.info("param:" + Param);
        List<String> paramTime = getRecoverableCodes(Param);
//        List<LocalDate> dateList = dateList();//不需要日期列表
        //1.插入原创和车家号
        String beginDate = DateHelper.serialize(DateHelper.beforeNDaysDate(1), DateHelper.DATEFORMAT_ONLY_DATE) + " 00:00:00";//前一天
        String endDate = DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + " 00:00:00";//当天日期
        DonateQueryModel donateQueryModel = DonateQueryModel.builder()
                .beginDate(beginDate)
                .endDate(endDate)
                .daily(DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE))
                .build();
        JobLog.info("DonateHuaweiJob.handle() date begin:" + beginDate);
        huaweiDonateClass.doBusiness(donateQueryModel);
        JobLog.info("DonateHuaweiJob.handle() date end:" + endDate);
        return ReturnT.SUCCESS;
    }

    @XxlJob("DonateIosJob")
    public ReturnT<String> handleIos() {
        JobLog.info("DonateIosJob.handle() POOL_SIZE:" + POOL_SIZE);
        String Param = XxlJobHelper.getJobParam();
        JobLog.info("param:" + Param);
        List<String> paramTime = getRecoverableCodes(Param);
        List<LocalDate> dateList = dateList();
        //1.插入原创和车家号
        //1.插入原创和车家号
        String beginDate = DateHelper.serialize(DateHelper.beforeNDaysDate(1), DateHelper.DATEFORMAT_ONLY_DATE) + " 00:00:00";//前一天
        String endDate = DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + " 00:00:00";//当天日期
        DonateQueryModel donateQueryModel = DonateQueryModel.builder()
                .beginDate(beginDate)
                .endDate(endDate)
                .daily(DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE))
                .build();
        JobLog.info("DonateIosJob.handle() date begin:" + beginDate);
        iosDonateClass.doBusiness(donateQueryModel);
        JobLog.info("DonateIosJob.handle() date end:" + endDate);
        return ReturnT.SUCCESS;
    }

    /**
     * 删除30天前的数据
     * */
    @XxlJob("DeleteDonateJob30")
    public ReturnT<String> handleDelete30() {
        JobLog.info("DeleteDonateJob30.handle() POOL_SIZE:" + POOL_SIZE);
        String Param = XxlJobHelper.getJobParam();
        JobLog.info("param:" + Param);
        List<String> paramTime = getRecoverableCodes(Param);
        List<LocalDate> dateList = dateList();
        String endDate = DateHelper.serialize(DateHelper.beforeNDaysDate(29), DateHelper.DATEFORMAT_ONLY_DATE) + " 00:00:00";//前一天

        JobLog.info("DeleteDonateJob30.handle() date :" + endDate);
        int deleteData30 = huaweiDonateClass.deleteData30(endDate);
        JobLog.info("DeleteDonateJob30 delete huaweiDonateClass.deleteData30 SIZE:" + deleteData30);
        int deletedData30 = iosDonateClass.deleteData30(endDate);
        JobLog.info("DeleteDonateJob30 delete iosDonateClass.deleteData30 SIZE:" + deletedData30);
        return ReturnT.SUCCESS;
    }

    private List<LocalDate> dateList(){
        LocalDate today = LocalDate.now();
        LocalDate beginDate = today.minusMonths(1);
        List<LocalDate> dateList = Stream.iterate(beginDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(beginDate, today))
                .collect(Collectors.toList());
        Collections.reverse(dateList);
        return dateList;
    }

    private List<String> getRecoverableCodes(String param) {
        if(StringUtils.isEmpty(param)){
            return null;
        }
        return Arrays.stream(org.apache.commons.lang3.StringUtils.split(param, ","))
                .map(String::valueOf)
                .collect(Collectors.toList());
    }



}