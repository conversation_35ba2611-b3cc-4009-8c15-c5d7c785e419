package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.vrinfodata.BrandNewNew;
import com.autohome.applet.model.dto.vrinfodata.NewSeriesNew;
import com.autohome.applet.service.DealerDataToQqService;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.javaapi.CarService;
import com.autohome.applet.service.openapi.VRInfoService;
import com.autohome.applet.util.*;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 与QQ浏览器合作搜索结果页场景，通过指定品牌及车系词，展示之家卡片结果，从而带来流量和线索收益
 * 品牌卡、车系卡、评论卡、图片卡
 * 数据同步周期一天一次即可
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmlToQQJob {
    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    private DealerDataToQqService dealerDataToQqService;

    @Autowired
    private CarSeriesService carSeriesService;

    @Autowired
    private VRInfoService vrInfoService;

    @Autowired
    private CarService carService;

    //设置并发数量
    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();

//   @PostConstruct
//    public void init(){
////       handleForSeriesForDeleteV2();
////       UploadEvaluateToQQJobV3JobForDelete();
////       handleUploadPicToQQJobForDelete();
////       handleForSeriesForDelete();
////       uploadBrandToQQJobV3ForDelete();
//       handle();
//
////       handleForSeries();
////
////       UploadEvaluateToQQJobV3Job();
////
////       handleUploadPicToQQJob();
////
//       log.info("done");
//    }

    /**
     * 1.品牌卡
     * 以车系维度提供数据
     *
     * @return
     */
    @XxlJob("UploadBrandToQQJobV3")
    public ReturnT<String> handle() {
        JobLog.info("UploadBrandToQQJobV3.handle() POOL_SIZE:" + POOL_SIZE);

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV3 = AllCitiesUtil.getAllCityV2();//全量城市
        //2.加载品牌列表
        List<BrandForQQUtil.BrandForQQ> brandIdListV3 = BrandForQQUtil.getAllBrandForQQV3();
        //3.查询品牌下的在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();
        //4.查询所有品牌信息
        List<AllBrandInfoDto.BrandInfoDto> brandInfoDtoList = carSeriesService.getAllBrandInfos();
        //入参
        SeriesInfoBrandDto seriesInfoBrandParam = new SeriesInfoBrandDto();

        //5.获取车系排行榜信息
        seriesInfoBrandParam.setRank(carService.getrecranklistpageresult2());

        if (CollectionUtils.isEmpty(brandIdListV3)) {
            JobLog.info("brandIdList is null");
            return ReturnT.SUCCESS;
        }
        JobLog.info("需同步品牌数量V3:" + brandIdListV3.size());
        JobLog.info("需同步品牌V3:" + JacksonHelper.serialize(brandIdListV3));

        List<String> brandIds = brandIdListV3.stream().map(BrandForQQUtil.BrandForQQ::getBrandId).collect(Collectors.toList());

        List<AllBrandInfoDto.BrandInfoDto> intersectionBrandList = brandInfoDtoList.stream().filter(brandInfo -> brandIds.contains(brandInfo.getId() + "")).collect(Collectors.toList());

        //for (AllBrandInfoDto.BrandInfoDto obj : intersectionBrandList) {
        intersectionBrandList.parallelStream().forEach(obj->{
            try {
                //6.获取vr信息
                setVr(seriesInfoBrandParam, obj.getId() + "");
                //取出当前品牌下的所有在售停售车系
                List<Integer> seriesIds = seriesInfoDtoList.stream().filter(e -> e.getBrandid() == obj.getId()).map(SeriesInfoDto::getId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(seriesIds)) {
                    log.info("BrandId:{} 品牌下的在售和停产在售车系列表，seriesIds={}", obj.getId(), seriesIds);
                    //continue;
                    return;
                }
                //7.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
                List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
                Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity()));

                seriesInfoBrandParam.setCarSeriesInfoMap(carSeriesInfoMap);
                //上传数据
                dealerDataToQqService.uploadBrandToQQV3(obj, cityItemListV3, seriesInfoDtoList, seriesInfoBrandParam, false);
                JobLog.info(String.format("UploadBrandToQQJob:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));

            } catch (Exception ex) {
                log.error("UploadBrandToQQJob:error:brandId:{},obj:{}", obj.getId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadBrandToQQJob:error:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));
            }
        });

        //}
        return ReturnT.SUCCESS;
    }

    /**
     * 1.品牌卡----删除
     * 当删减索引词时使用
     *
     * @return
     */
    @XxlJob("UploadBrandToQQJobV3ForDelete")
    public ReturnT<String> uploadBrandToQQJobV3ForDelete() {
        JobLog.info("UploadBrandToQQJobV3.handle() POOL_SIZE:" + POOL_SIZE);

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV3 = AllCitiesUtil.getAllCityV2();//全量城市
        //2.加载品牌列表
        List<BrandForQQUtil.BrandForQQ> newBrandIdListV3 = BrandForQQUtil.getAllBrandForQQV3();//新的品牌列表
        List<BrandForQQUtil.BrandForQQ> brandIdList******** = BrandForQQUtil.getAllBrandForQQ********();//原有品牌列表
        //3.查询品牌下的在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();
        //4.查询所有品牌信息
        List<AllBrandInfoDto.BrandInfoDto> brandInfoDtoList = carSeriesService.getAllBrandInfos();
        //入参
        SeriesInfoBrandDto seriesInfoBrandParam = new SeriesInfoBrandDto();

        //5.获取车系排行榜信息
        seriesInfoBrandParam.setRank(carService.getrecranklistpageresult2());

        if (CollectionUtils.isEmpty(brandIdList********)) {
            JobLog.info("brandIdList is null");
            return ReturnT.SUCCESS;
        }
        JobLog.info("需同步品牌数量V3:" + brandIdList********.size());
        JobLog.info("需同步品牌V3:" + JacksonHelper.serialize(brandIdList********));
//        JobLog.info("原需同步品牌数量********:" + brandIdList********.size());
//        JobLog.info("原需同步品牌********:" + JacksonHelper.serialize(brandIdList********));

        List<String> brandIds = brandIdList********.stream().map(BrandForQQUtil.BrandForQQ::getBrandId).collect(Collectors.toList());
        List<String> newBrandIds = newBrandIdListV3.stream().map(BrandForQQUtil.BrandForQQ::getBrandId).collect(Collectors.toList());

        List<AllBrandInfoDto.BrandInfoDto> intersectionBrandList = brandInfoDtoList.stream().filter(brandInfo -> brandIds.contains(brandInfo.getId() + "")).collect(Collectors.toList());

        //for (AllBrandInfoDto.BrandInfoDto obj : intersectionBrandList) {
        intersectionBrandList.parallelStream().forEach(obj->{
            try {
                //6.获取vr信息
                setVr(seriesInfoBrandParam, obj.getId() + "");
                //取出当前品牌下的所有在售停售车系
                List<Integer> seriesIds = seriesInfoDtoList.stream().filter(e -> e.getBrandid() == obj.getId()).map(SeriesInfoDto::getId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(seriesIds)) {
                    log.info("BrandId:{} 品牌下的在售和停产在售车系列表，seriesIds={}", obj.getId(), seriesIds);
                    //continue;
                    return;
                }
                //7.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
                List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
                Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity()));

                seriesInfoBrandParam.setCarSeriesInfoMap(carSeriesInfoMap);
                //上传数据
                //判断是否需要删除(如果新的列表里包含原有的品牌id，不删除)
                if(!newBrandIds.contains(obj.getId())){
                    dealerDataToQqService.uploadBrandToQQV3(obj, cityItemListV3, seriesInfoDtoList, seriesInfoBrandParam, true);
                    JobLog.info(String.format("UploadBrandToQQJobDetele:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));
                }
                else{
                    JobLog.info(String.format("NoNeedDetele:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));
                }

            } catch (Exception ex) {
                log.error("UploadBrandToQQJob:error:brandId:{},obj:{}", obj.getId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadBrandToQQJob:error:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));
            }
        });

        //}
        return ReturnT.SUCCESS;
    }

    /**
     * 2.车系卡
     * 以车型维度提供数据
     *
     * @return
     */
    @XxlJob("UploadSeriesToQQJobV3")
    public ReturnT<String> handleForSeries() {
        JobLog.info("UploadSeriesToQQJobV3.handle() POOL_SIZE:" + POOL_SIZE);

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV2 = AllCitiesUtil.getAllCityV2();
        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> seriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();

        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = seriesInfoListV3.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        if (CollectionUtils.isEmpty(seriesInfoListV3)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需同步车系数量-V3:" + seriesInfoListV3.size());
        JobLog.info("需同步车系-V3:" + JacksonHelper.serialize(seriesInfoListV3));

        seriesInfoListV3.parallelStream().forEach(obj -> {
            SpecParamDto specParamDto = new SpecParamDto();
            specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
            specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

            try {
                //查询
                dealerDataToQqService.uploadSpecToQQV3(obj, cityItemListV2, specParamDto, false);
                JobLog.info(String.format("UploadSeriesToQQJobV3:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            } catch (Exception ex) {
                log.error("UploadSeriesToQQJobV3:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadSeriesToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }

        });

        return ReturnT.SUCCESS;
    }
    /**
     * 2.车系卡----删除
     * 当删减索引词时使用
     *
     * @return
     */
    @XxlJob("UploadSeriesToQQJobV3ForDelete")
    public ReturnT<String> handleForSeriesForDelete() {
        JobLog.info("UploadSeriesToQQJobV3ForDelete.handle() POOL_SIZE:" + POOL_SIZE);

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV2 = AllCitiesUtil.getAllCityV2();
        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> newSeriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3();
        List<SeriesForQQUtil.SeriesForQQ> seriesInfoList******** = SeriesForQQUtil.getSeriesForQQList********();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();

        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = seriesInfoList********.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());
        List<Integer> newSeriesIds = newSeriesInfoListV3.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        if (CollectionUtils.isEmpty(seriesInfoList********)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需同步车系数量-V3:" + seriesInfoList********.size());
        JobLog.info("需同步车系-V3:" + JacksonHelper.serialize(seriesInfoList********));

        seriesInfoList********.parallelStream().forEach(obj -> {
            SpecParamDto specParamDto = new SpecParamDto();
            specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
            specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

            try {
                //查询
                //判断是否需要删除(如果新的列表里包含原有的品牌id，不删除)
                if(!newSeriesIds.contains(Integer.valueOf(obj.getSeriesId()))){
                    dealerDataToQqService.uploadSpecToQQV3(obj, cityItemListV2, specParamDto, true);
                    JobLog.info(String.format("UploadBrandToQQJobDetele:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
                }
                else{
                    JobLog.info(String.format("NoNeedDelete:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
                }
            } catch (Exception ex) {
                log.error("UploadSeriesToQQJobV3:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadSeriesToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }

        });

        return ReturnT.SUCCESS;
    }

    /**
     * 2.车系卡----删除----版本2
     * 当删减索引词时使用
     *
     * @return
     */
    @XxlJob("UploadSeriesToQQJobV3ForDeleteV2")
    public ReturnT<String> handleForSeriesForDeleteV2() {
        JobLog.info("handleForSeriesForDeleteV2.handle() POOL_SIZE:" + POOL_SIZE);

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV2 = AllCitiesUtil.getAllCityV2();
        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> newSeriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3ForDelete();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();

        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = newSeriesInfoListV3.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        if (CollectionUtils.isEmpty(seriesInfoDtoList)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需删除的车系-V3:" + JacksonHelper.serialize(newSeriesInfoListV3));

        newSeriesInfoListV3.parallelStream().forEach(obj -> {
            SpecParamDto specParamDto = new SpecParamDto();
            specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
            specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

            try {
                //查询
                //判断是否需要删除(如果新的列表里包含原有的品牌id，不删除)
                dealerDataToQqService.uploadSpecToQQV3(obj, cityItemListV2, specParamDto, true);
                JobLog.info(String.format("UploadBrandToQQJobDetele:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
//                if(!seriesIds.contains(Integer.valueOf(obj.getSeriesId()))){
//                    dealerDataToQqService.uploadSpecToQQV3(obj, cityItemListV2, specParamDto, true);
//                    JobLog.info(String.format("UploadBrandToQQJobDetele:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
//                }
//                else{
//                    JobLog.info(String.format("NoNeedDelete:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
//                }
            } catch (Exception ex) {
                log.error("UploadSeriesToQQJobV3:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadSeriesToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }

        });

        return ReturnT.SUCCESS;
    }


    /**
     * 3.评论卡
     * 以车系维度提供数据，不区分城市
     *
     * @return
     */

    @XxlJob("UploadEvaluateToQQJobV3")
    public ReturnT<String> UploadEvaluateToQQJobV3Job() {

        JobLog.info("UploadEvaluateToQQJobV3开始执行, 线程数量:" + POOL_SIZE);
        //获取文件中车系列表
        List<SeriesForQQUtil.SeriesForQQ> customSeriesList = SeriesForQQUtil.getSeriesForQQListV3();

        //查询所有的在售车系
        List<SeriesInfoDto> allSeriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();

        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = customSeriesList.stream()
                .map(series -> Integer.parseInt(series.getSeriesId()))
                .collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        JobLog.info("需要同步的车系数量:" + customSeriesList.size());
        JobLog.info("需要同步的车系:" + JacksonHelper.serialize(customSeriesList));

        customSeriesList.forEach(s -> {
            try {
                SpecParamDto specParamDto = new SpecParamDto();
                specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

                dealerDataToQqService.uploadEvaluateCardToQQ(s, allSeriesInfoDtoList, specParamDto, false);
                JobLog.info(String.format("EvaluateCardXmlToQQJob:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper
                        .serialize(s)));
            } catch (Exception e) {
                log.error("EvaluateCardXmlToQQJob:error:seriesId:{},obj:{}", s.getSeriesId(), JacksonHelper.serialize(s), e);
                JobLog.info(String.format("UploadSeriesToQQJob:error:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper.serialize(s)));
            }
        });
        return ReturnT.SUCCESS;
    }

    /**
     * 3.评论卡----删除
     * 当删减索引词时使用
     *
     * @return
     */

    @XxlJob("UploadEvaluateToQQJobV3ForDelete")
    public ReturnT<String> UploadEvaluateToQQJobV3JobForDelete() {

        JobLog.info("UploadEvaluateToQQJobV3开始执行, 线程数量:" + POOL_SIZE);
        //获取文件中车系列表
        List<SeriesForQQUtil.SeriesForQQ> newCustomSeriesList = SeriesForQQUtil.getSeriesForQQListV3();
        //获取文件中车系列表
        List<SeriesForQQUtil.SeriesForQQ> customSeriesList202501 = SeriesForQQUtil.getSeriesForQQList********();

        //查询所有的在售车系
        List<SeriesInfoDto> allSeriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();

        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = customSeriesList202501.stream()
                .map(series -> Integer.parseInt(series.getSeriesId()))
                .collect(Collectors.toList());
        List<Integer> newSeriesIds = newCustomSeriesList.stream()
                .map(series -> Integer.parseInt(series.getSeriesId()))
                .collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        JobLog.info("需要同步的车系数量:" + customSeriesList202501.size());
        JobLog.info("需要同步的车系:" + JacksonHelper.serialize(customSeriesList202501));

        customSeriesList202501.forEach(s -> {
            try {
                SpecParamDto specParamDto = new SpecParamDto();
                specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

                if(!newSeriesIds.contains(Integer.valueOf(s.getSeriesId()))){
                    dealerDataToQqService.uploadEvaluateCardToQQ(s, allSeriesInfoDtoList, specParamDto, true);
                    JobLog.info(String.format("EvaluateCardXmlToQQJob:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper
                            .serialize(s)));
                }
                else{
                    JobLog.info(String.format("NoNeedDelete:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper
                            .serialize(s)));
                }
            } catch (Exception e) {
                log.error("EvaluateCardXmlToQQJob:error:seriesId:{},obj:{}", s.getSeriesId(), JacksonHelper.serialize(s), e);
                JobLog.info(String.format("UploadSeriesToQQJob:error:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper.serialize(s)));
            }
        });
        return ReturnT.SUCCESS;
    }

    /**
     * 4.图片卡
     * 以车系维度提供数据，不区分城市
     *
     * @return
     */
    @XxlJob("UploadPicToQQJobV3")
    public ReturnT<String> handleUploadPicToQQJob() {
        JobLog.info("handleUploadPicToQQJob.handle() POOL_SIZE:" + POOL_SIZE);
        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> seriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();
        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = seriesInfoListV3.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        if (CollectionUtils.isEmpty(seriesInfoListV3)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需同步pic数量-V3:" + seriesInfoListV3.size());
        JobLog.info("需同步pic-V3:" + JacksonHelper.serialize(seriesInfoListV3));

        seriesInfoListV3.forEach(obj -> {
            try {

                SpecParamDto specParamDto = new SpecParamDto();
                specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
                specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);
                //查询
                dealerDataToQqService.uploadPicToQQV3(obj, specParamDto, false);
                JobLog.info(String.format("UploadPicToQQJobV3:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            } catch (Exception ex) {
                log.error("UploadPicToQQJobV3:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadPicToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }
        });
        return ReturnT.SUCCESS;
    }

    /**
     * 4.图片卡 ----删除
     * 当删减索引词时使用
     *
     * @return
     */
    @XxlJob("UploadPicToQQJobV3ForDelete")
    public ReturnT<String> handleUploadPicToQQJobForDelete() {
        JobLog.info("handleUploadPicToQQJob.handle() POOL_SIZE:" + POOL_SIZE);
        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> newSeriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3();
        List<SeriesForQQUtil.SeriesForQQ> seriesInfoList******** = SeriesForQQUtil.getSeriesForQQList********();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();
        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = seriesInfoList********.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());
        List<Integer> newSeriesIds = newSeriesInfoListV3.stream().map(series -> Integer.parseInt(series.getSeriesId())).collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream().collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        if (CollectionUtils.isEmpty(seriesInfoList********)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需同步pic数量-V3:" + seriesInfoList********.size());
        JobLog.info("需同步pic-V3:" + JacksonHelper.serialize(seriesInfoList********));

        seriesInfoList********.forEach(obj -> {
            try {

                SpecParamDto specParamDto = new SpecParamDto();
                specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
                specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);
                //查询
                if(!newSeriesIds.contains(Integer.valueOf(obj.getSeriesId()))){
                    dealerDataToQqService.uploadPicToQQV3(obj, specParamDto, true);
                    JobLog.info(String.format("handleUploadPicToQQJobForDelete:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
                }
                else{
                    JobLog.info(String.format("NoNeedDelete:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
                }
            } catch (Exception ex) {
                log.error("UploadPicToQQJobV3:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadPicToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }
        });
        return ReturnT.SUCCESS;
    }


    /**
     * 通过品牌ID获取品牌下所有车系ID
     * set vr
     *
     * @param seriesInfoBrandParam
     * @param brandId
     */
    private void setVr(SeriesInfoBrandDto seriesInfoBrandParam, String brandId) {
        NewSeriesNew newSeriesNew = vrInfoService.getSerieslistHasVRByBrandId(Integer.parseInt(brandId));
        Set<Integer> vrSeriesIdSet = Stream.concat(newSeriesNew.getSellseries().stream().flatMap(fctlist -> fctlist.getSerieslist().stream()), newSeriesNew.getUnsellseries().stream().flatMap(fctlist -> fctlist.getSerieslist().stream())).map(BrandNewNew.Serieslist::getId).collect(Collectors.toSet());

        seriesInfoBrandParam.setSeriesIdsSet(vrSeriesIdSet);

    }
}