package com.autohome.applet.task.consumer;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.NamedThreadFactory;
import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo;
import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfoLog;
import com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo;
import com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfoLog;
import com.autohome.applet.service.SecondHandCarService;
import com.autohome.applet.util.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 1.二手车数据推送kafka
 * 2.小程序服务端消费kafka,存储到本地
 * 3.小程序服务端推送数据到百度阿拉丁
 * */
@Component
@Slf4j
public class SecondHandCarToAppletConsumer {
    @Autowired
    private KafkaProducer<String, String> sourceKafkaProducer;

    @Autowired
    private SecondHandCarService secondHandCarService;

    @Value("${spring.kafka.baidu.consumer.topic}")
    private String TOPIC;

    private final static int POOL_SIZE = 2 * Runtime.getRuntime().availableProcessors();
//    private final static int POOL_SIZE = 1;

//    @PostConstruct
//    public void init() {
//        try {
//            String org = "{\"lat\":\"31.2355511\",\"lng\":\"121.4761611\",\"org_address\":\"北京市海淀区电子大厦B座测试一下地址的长度，非常的长，超级长，太长了要展示什么样式yyyyyyyyy红红火火恍恍惚惚或或或或或或或或或或或\",\"org_case\":0,\"org_city\":\"上海\",\"org_commentnum\":0,\"org_district\":\"黄浦\",\"org_icon\":\"https://2sc2.autoimg.cn/escimg/g26/M05/EE/56/autohomecar__CjIFVmSrxQWACY1iAAOC6Wsjeiw991.jpg\",\"org_id\":\"225037\",\"org_name\":\"媒介环境调试20190702A全\",\"org_province\":\"上海\",\"org_score\":4.0,\"org_servicecount\":0,\"org_servicetab\":\"价格评估,代办验车,补办手续\",\"org_status\":1,\"org_worktime\":\"9:00-18:00\",\"org_year\":0}";
////            String org1 = "{\"org_address\":\"北京市海淀区电子大厦B座测试一下地址的长度，非常的长，超级长，太长了要展示什么样式yyyyyyyyy红红火火恍恍惚惚或或或或或或或或或或或\",\"org_case\":0,\"org_city\":\"上海\",\"org_commentnum\":0,\"org_district\":\"黄浦\",\"org_icon\":\"https://2sc2.autoimg.cn/escimg/g26/M05/EE/56/autohomecar__CjIFVmSrxQWACY1iAAOC6Wsjeiw991.jpg\",\"org_id\":\"164184\",\"org_name\":\"媒介环境调试20190702A全\",\"org_status\":0}";
////            String product = "{\"name\":\"奥迪A3 2021款 A3L Limousine 35 TFSI 时尚运动型\",\"org_avgprice\":0,\"org_id\":\"164184\",\"pic\":\"https://x.autoimg.cn/2sc/2023/2023-2/saas_placeholder_375x281_5.png\",\"service_id\":\"3193108\",\"status\":0,}";
////            String product1 = "{\"car_name\":\"奥迪A3 2021款 A3L Limousine 35 TFSI 时尚运动型\",\"finalprice\":\"11.00\",\"name\":\"奥迪A3 2021款 A3L Limousine 35 TFSI 时尚运动型\",\"org_avgprice\":0,\"org_id\":\"164184\",\"pic\":\"https://x.autoimg.cn/2sc/2023/2023-2/saas_placeholder_375x281_5.png\",\"service_id\":\"3193108\",\"status\":0,\"tabs\":\"准新车\",\"type\":0}";
////            log.info("org:{}", org);
////            log.info("product:{}", product);
//            sendMessage("org", org.replace("\n", "").replace(" ", "").replace(",}", "}"));
////            sendMessage("org", org1.replace("\n", "").replace(" ", "").replace(",}", "}"));
////            sendMessage("product", product.replace("\n", "").replace(" ", "").replace(",}", "}"));
////            sendMessage("product", product1.replace("\n", "").replace(" ", "").replace(",}", "}"));
////            sendMessage("product", product.replace("\n", "").replace(" ", "").replace(",}", "}"));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * hutool类库builder模式创建线程池，指定线程池名称，拒绝策略等
     */
    private final ExecutorService executorService = ExecutorBuilder.create()
            .setCorePoolSize(POOL_SIZE)
            .setMaxPoolSize(POOL_SIZE)
            .setThreadFactory(new NamedThreadFactory("SecondHandCarToAppletConsumer", false))
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .build();


    /**
     *  消费二手车推送的数据
     */
    @KafkaListener(id="${spring.kafka.baidu.consumer.group-id}"
            ,topics = {"${spring.kafka.baidu.consumer.topic}"}
            ,containerFactory = "secondCarToBaiduContainerFactory")
    public void secondCarToBaiduListener(ConsumerRecord<?, String> message) {
        try {
            executorService.execute(() -> {
                if("org".equals(message.key())){
                    doTaskOrg(message.value());
                }
                else{
                    doTaskProduct(message.value());
                }
            });
        }catch (Exception exception){
            log.error("secondCarToBaiduListener线程池异常",exception);
        }
        //LOGGER.info(message.value());
    }
    public void doTaskOrg(String data){
        if(StringUtils.isBlank(data)) {
            log.info("secondCarToBaiduListener data is null");
            return;
        }
        boolean saveResult = false;
        SecondHandCarOrgInfo secondHandCarOrgInfo = null;
        try{
            secondHandCarOrgInfo = JacksonHelper.deserialize(data, SecondHandCarOrgInfo.class);
            //判断是否有orgid
            if(StringUtils.isNotEmpty(secondHandCarOrgInfo.getOrgId())){
                if(secondHandCarOrgInfo.getOrgStatus() == 0){
                    //走删除逻辑
                    saveResult = secondHandCarService.updateOrgStatus(secondHandCarOrgInfo);
                }
                else{
                    saveResult = secondHandCarService.saveSecondHandCarOrgInfo(secondHandCarOrgInfo);
                }
            }
            else{
                log.error("doTaskOrg no orgid, data:{}", data);
            }
        }
        catch (Exception ex){
            log.error("doTaskOrg error, ex:", ex);
        }
        finally {
            //存入日志
            SecondHandCarOrgInfoLog secondHandCarOrgInfoLog = new SecondHandCarOrgInfoLog();
            copyProperties(secondHandCarOrgInfo, secondHandCarOrgInfoLog);
            secondHandCarOrgInfoLog.setFullData(data);
            secondHandCarOrgInfoLog.setSaveResult(saveResult ? 1 : 0);
            secondHandCarService.saveSecondHandCarOrgInfoLog(secondHandCarOrgInfoLog);
        }
        log.info("kafka data org ==== {}", data);
    }
    public void doTaskProduct(String data){
        if(StringUtils.isBlank(data)) {
            log.info("secondCarToBaiduListener data is null");
            return;
        }
        boolean saveResult = false;
        SecondHandCarProductInfo secondHandCarProductInfo = null;
        try{
            secondHandCarProductInfo = JacksonHelper.deserialize(data, SecondHandCarProductInfo.class);
            //判断是否有serverid
            if(StringUtils.isNotEmpty(secondHandCarProductInfo.getServiceId()) && StringUtils.isNotEmpty(secondHandCarProductInfo.getOrgId())){
                if(secondHandCarProductInfo.getStatus() == 0){
                    //走删除逻辑
                    saveResult = secondHandCarService.updateProductStatus(secondHandCarProductInfo);
                }
                else{
                    saveResult = secondHandCarService.saveSecondHandCarProductInfo(secondHandCarProductInfo);
                }
            }
            else{
                log.error("doTaskProduct no orgid or no servierid, data:{}", data);
            }
        }
        catch (Exception ex){
            log.error("doTaskProduct error, ex:", ex);
        }
        finally {
            //存入日志
            SecondHandCarProductInfoLog secondHandCarProductInfoLog = new SecondHandCarProductInfoLog();
            copyProperties(secondHandCarProductInfo, secondHandCarProductInfoLog);
            secondHandCarProductInfoLog.setFullData(data);
            secondHandCarProductInfoLog.setSaveResult(saveResult ? 1 : 0);
            secondHandCarService.saveSecondHandCarProductInfoLog(secondHandCarProductInfoLog);
        }
        log.info("kafka data product ==== {}", data);
    }

    public void sendMessage(String key, String message) {
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>(TOPIC,
                key, message);

        System.out.println("store->" + message);
        try {
            sourceKafkaProducer.send(producerRecord);
//            executorService.execute(() -> {
//                sourceKafkaProducer.send(producerRecord);
//            });
        } catch (Exception exception) {
            log.error("TestKafkaController kafkastore 线程池异常", exception);
        }
    }

    public void copyProperties(Object source, Object target) {
        if(source != null){
            BeanUtils.copyProperties(source, target);
        }
    }
}
