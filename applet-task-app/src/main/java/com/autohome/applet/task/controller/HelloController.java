package com.autohome.applet.task.controller;

import com.autohome.applet.dao.javaapi.mapper.HistoryDouyinSitemapMapper;
import com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap;
import com.autohome.applet.dao.javaapi.model.MiniTokenValue;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.douyin.DouYinBaseReturn;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 健康检查
 */
@Slf4j
@RestController
public class HelloController {

    @Autowired
    DouYinService douYinService;

    @Autowired
    HistoryDouyinSitemapMapper historyDouyinSitemapMapper;

    private static final DateTimeFormatter DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Autowired
    @Qualifier("resourceFeedPushToDBServiceImpl")
    private ResourceFeedPushService dbService;


    @GetMapping(value = "/hello")
    public String hello() {
        return "success";
    }

    @GetMapping(value = "/dt")
    public ReturnValue dt(int minitype) {


        Map<String, String> token = douYinService.selectAccessTokenByMiniType(minitype);

        return ReturnValue.buildSuccessResult(token);

    }

//    @GetMapping(value = "/sm")
//    public ReturnValue sm() {
//        List<String> list = new ArrayList<>();
//
//        list.add("carpackage/pages/series/index?seriesid=5998");
//        list.add("carpackage/pages/spec/index?seriesid=5998&specid=66887");
//
//        DouYinBaseReturn douYinBaseReturn = douYinService.uploadSiteMap(list);
//
//        return ReturnValue.buildSuccessResult(douYinBaseReturn);
//
//    }

    /**
     * 行情数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping(value = "/hq")
    public ReturnValue hq(String startTime, String endTime) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        if (StringUtils.isEmpty(startTime)) {

            startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        }
        if (StringUtils.isEmpty(endTime)) {

            endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);

        String url = "http://basecar.api.corpautohome.com/aigc/api/hangqing/gethangqinglist";
//        String url = "http://basecar-api.terra.corpautohome.com/aigc/api/hangqing/gethangqinglist";
//        String url = "http://basecar-api.cupid.autohome.com.cn/aigc/api/hangqing/gethangqinglist";
        douYinService.getHangQing(param, url, 1);

        return ReturnValue.buildSuccessResult("");

    }

    /**
     * 行情删除数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping(value = "/hqdel")
    public ReturnValue hqdel(String startTime, String endTime) {

        LocalDate yesterday = LocalDate.now().minusDays(1);
        if (StringUtils.isEmpty(startTime)) {

            startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        }
        if (StringUtils.isEmpty(endTime)) {

            endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String url = "http://basecar.api.corpautohome.com/aigc/api/hangqing/listDelArticleByTime";
//        String url = "http://basecar-api.terra.corpautohome.com/aigc/api/hangqing/listDelArticleByTime";
//        String url = "http://basecar-api.cupid.autohome.com.cn/aigc/api/hangqing/listDelArticleByTime";
        douYinService.getHangQing(param, url, -1);

        return ReturnValue.buildSuccessResult("");

    }

    /**
     * 问答数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping(value = "/wd")
    public ReturnValue wd(String startTime, String endTime) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        if (StringUtils.isEmpty(startTime)) {

            startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        }
        if (StringUtils.isEmpty(endTime)) {

            endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        }
        String url = "http://basecar.api.corpautohome.com/aigc/api/getarticlelist";
//        String url = "http://basecar-api.terra.corpautohome.com/aigc/api/getarticlelist";
//        String url = "http://basecar-api.cupid.autohome.com.cn/aigc/api/getarticlelist";
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        douYinService.getWenDa(param, url, 1);
        return ReturnValue.buildSuccessResult("");

    }

    /**
     * 问答删除数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping(value = "/wddel")
    public ReturnValue wddel(String startTime, String endTime) {

        LocalDate yesterday = LocalDate.now().minusDays(1);
        if (StringUtils.isEmpty(startTime)) {

            startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        }
        if (StringUtils.isEmpty(endTime)) {

            endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        }
        String url = "http://basecar.api.corpautohome.com/aigc/api/listDelArticleByTime";
//        String url = "http://basecar-api.terra.corpautohome.com/aigc/api/listDelArticleByTime";
//        String url = "http://basecar-api.cupid.autohome.com.cn/aigc/api/listDelArticleByTime";
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        douYinService.getWenDa(param, url, -1);
        return ReturnValue.buildSuccessResult("");

    }

    @GetMapping(value = "historyHq")
    public ReturnValue historyHq(String startTime, String endTime) {


        for (String times : getStartAndEndOfEachDay(startTime, endTime)) {
            String[] split = times.split("到");

            String startTimeTemp = split[0];
            String endTimeTemp = split[1];
            log.info("historyHq:执行行情 startTime={}，endTime={}",startTimeTemp,endTimeTemp);
            hq(startTimeTemp,endTimeTemp);
            log.info("historyHq:执行行情 startTime={}，endTime={}",startTimeTemp,endTimeTemp);
            hqdel(startTimeTemp,endTimeTemp);

        }
        return ReturnValue.buildSuccessResult("");

    }

    @GetMapping(value = "historyWd")
    public ReturnValue historyWd(String startTime, String endTime) {


        for (String times : getStartAndEndOfEachDay(startTime, endTime)) {
            String[] split = times.split("到");

            String startTimeTemp = split[0];
            String endTimeTemp = split[1];

            wd(startTimeTemp,endTimeTemp);
            log.info("historyWd:执行问答 startTime={}，endTime={}",startTimeTemp,endTimeTemp);
//            wddel(startTimeTemp,endTimeTemp);
//            log.info("historyWd:执行问答del startTime={}，endTime={}",startTimeTemp,endTimeTemp);

        }

        return ReturnValue.buildSuccessResult("");

    }


    /**
     * 返回开始和结束时段内每天起止时间
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> getStartAndEndOfEachDay(String start, String end) {
        LocalDate startDate = LocalDate.parse(start, DATE_FORMAT);
        LocalDate endDate = LocalDate.parse(end, DATE_FORMAT);

        List<String> result = new ArrayList<>();

        while (!startDate.isAfter(endDate)) {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = startDate.atTime(23, 59, 59);
            result.add(startDateTime.format(DATETIME_FORMAT) + "到" + endDateTime.format(DATETIME_FORMAT));
            startDate = startDate.plusDays(1);
        }

        return result;
    }

//    @GetMapping(value = "htb")
//    public ReturnT<String> HistoryToDb(String startTime,String endTime) {
//
//        for (String times : getStartAndEndOfEachDay(startTime, endTime)) {
//            String[] split = times.split("到");
//            String startTimeTemp = split[0];
//            String endTimeTemp = split[1];
//            PushResourceFeedDataQuery parmModel =  new PushResourceFeedDataQuery();
//            parmModel.setBizTypeList(Arrays.asList(1,5,12,13,33));
//            parmModel.setStarttime(startTimeTemp);
//            parmModel.setEndtime(endTimeTemp);
//
//            log.info("历史数据入库执行到 todb:parmModel={}  ",JacksonHelper.serialize(parmModel));
//            dbService.doPush(parmModel);
//        }
//
//        return ReturnT.SUCCESS;
//    }



    @GetMapping(value = "wdhistory")
    public ReturnT<String> wdhistoryToDb(String path) {
        Date date = new Date();
        HistoryDouyinSitemap row = new HistoryDouyinSitemap();
        row.setPagepath(path);
        row.setSource(101);
        row.setStatus(0);
        row.setIsDelete(0);
        row.setCreatedStime(date);
        row.setModifiedStime(date);
        historyDouyinSitemapMapper.insert(row);
        return ReturnT.SUCCESS;
    }
}
