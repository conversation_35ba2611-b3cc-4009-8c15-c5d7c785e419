package com.autohome.applet.task.job;

import com.autohome.applet.service.javaapi.DealerService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 经销商-智域通品牌活动数据缓存
 * <AUTHOR>
 */
@Slf4j
@Component
public class DealerBrandActivityJob {

    @Qualifier("javaDealerService")
    @Autowired
    DealerService dealerService;

    @XxlJob("DealerBrandActivityJob")
    public ReturnT<String> handle() {
        JobLog.info("DealerBrandActivityJob.handle()");
        dealerService.getDealerBrandActivity();
        return ReturnT.SUCCESS;
    }
}
