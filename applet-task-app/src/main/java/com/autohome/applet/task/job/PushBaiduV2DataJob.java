package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.time.Duration;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PushBaiduV2DataJob {

    @Autowired
    @Qualifier("resourceFeedPushToBaiduServiceImpl")
    private ResourceFeedPushService baiduService;

    @Autowired
    private DouYinService douYinService;

//    @PostConstruct
//    public void init() {
//        handle();
////        PushDouYinData();
////        PushResourceFeedDataQuery pushResourceFeedDataQuery = getRecoverableCodes("{\"starttime\":\"2024-07-02\",\"endtime\":\"2024-07-03\",\"offset\":0,\"count\":500,\"bizTypeList\":[1,5,12,13,33,74]}");
//    }

    /**
     * 百度笔记
     * 需求：https://doc.autohome.com.cn/docapi/page/share/share_uovwwQmvDM
     *
     * */
    @XxlJob("PushBaiduDataV2")
    public ReturnT<String> handle() {
//        String param = "{\"starttime\":\"2024-07-02\",\"endtime\":\"2024-07-06\"}";//XxlJobHelper.getJobParam();    // 获取参数
        String param = XxlJobHelper.getJobParam();    // 获取参数
        JobLog.info("param:" + param);
        PushResourceFeedDataQuery parmModel = getRecoverableCodes(param);
        //拆分时间段
        LocalDateTime startTime = DateHelper.dateToLocalDateTime(DateHelper.deserialize(parmModel.getStarttime() + " 00:00:00", DateHelper.DATEFORMAT_ONLY_DATE));
        LocalDateTime endTime = DateHelper.dateToLocalDateTime(DateHelper.deserialize(parmModel.getEndtime() + " 00:00:00", DateHelper.DATEFORMAT_ONLY_DATE));

        //按天拆分
        List<TimeSegment> segmentDays = getTimeSegments(startTime, endTime, 60 * 24);//每小时提供一次
        List<String> dateList = new ArrayList<>();
        for(int i = 0; i<segmentDays.size(); i++){
            dateList.add(DateHelper.localDateToDateTimeStr(segmentDays.get(i).getStart(), DateHelper.DATEFORMAT_ONLY_DATE));
        }
        for(int i = 0; i<dateList.size(); i++) {
            //删除
            int finalI = i;
            parmModel.getBizTypeList().forEach(o -> {
                douYinService.removeBaiduReportRedis(ResourceFeedPushService.BAIDU_3GC_KEY, dateList.get(finalI), o, 1, 0);
                douYinService.removeBaiduReportRedis(ResourceFeedPushService.BAIDU_3GC_KEY, dateList.get(finalI), o, 1, 1);
                douYinService.removeBaiduReportRedis(ResourceFeedPushService.BAIDU_3GC_KEY, dateList.get(finalI), o, 1, 2);
            });
        }


        List<TimeSegment> segments = getTimeSegments(startTime, endTime, 60);//每小时提供一次
        segments.forEach(obj -> {
            String tmpStartTime = DateHelper.localDateToDateTimeStr(obj.getStart(), DateHelper.DATEFORMAT_STANDARD);
            String tmpEndTime = DateHelper.localDateToDateTimeStr(obj.getEnd(), DateHelper.DATEFORMAT_STANDARD);
            parmModel.setStarttime(tmpStartTime);
            parmModel.setEndtime(tmpEndTime);
            parmModel.setOffset(0);//重置偏移量

            baiduService.doPush(parmModel);
        });

        //发送钉钉消息
        douYinService.baiduReport(dateList);

        return ReturnT.SUCCESS;
    }

    private PushResourceFeedDataQuery getRecoverableCodes(String param) {
        if(StringUtils.isEmpty(param)){
            return new PushResourceFeedDataQuery();
        }
        try{
            return JacksonHelper.deserialize(param, PushResourceFeedDataQuery.class);
        }
        catch (Exception ex){
            XxlJobHelper.log(ex);
        }
        return null;
    }

    public static List<TimeSegment> getTimeSegments(LocalDateTime startTime, LocalDateTime endTime, int interval) {
        long startMillis = startTime.toEpochSecond(ZoneOffset.UTC);
        long endMillis = endTime.toEpochSecond(ZoneOffset.UTC);

        List<TimeSegment> segments = new ArrayList<>();
        for (int i = 0; i < (int) ((endMillis - startMillis) / (interval * 60)); i++) {
            long segmentMillis = i * interval * 60;
            LocalDateTime segmentStart = LocalDateTime.ofEpochSecond(startMillis + segmentMillis, 0, ZoneOffset.UTC);
            LocalDateTime segmentEnd = segmentStart.plusMinutes(interval);
            segments.add(new TimeSegment(segmentStart, segmentEnd));
        }

        return segments;
    }

    @Data
    static class TimeSegment {
        LocalDateTime start;
        LocalDateTime end;

        public TimeSegment(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String toString() {
            return "(" + start + " - " + end + ")";
        }
    }

}
