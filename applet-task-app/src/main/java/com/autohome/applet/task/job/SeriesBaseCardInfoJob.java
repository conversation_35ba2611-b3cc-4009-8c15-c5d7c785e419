package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.model.dto.seriesbasecardinfo.SeriesTabInfoQuery;
import com.autohome.applet.service.DealerDataToQqService;
import com.autohome.applet.service.seriesbasecardinfo.SeriesBaseCardInfoServies;
import com.autohome.applet.util.AllCitiesUtil;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 车系综述页相关服务
 * 1.动态tab
 * */
@Slf4j
@Component
public class SeriesBaseCardInfoJob {

    @Autowired
    private SeriesBaseCardInfoServies seriesBaseCardInfoServies;
    @Autowired
    private DealerDataToQqService dealerDataToQqService;

    @Autowired
    @Resource(name = "lightapp")
    private RedisClient redisClient;

    //设置并发数量
    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private ForkJoinPool forkJoinPoolLimit = new ForkJoinPool(POOL_SIZE >= 2 ? (POOL_SIZE/2) : 1);
    private ForkJoinPool forkJoinPool = new ForkJoinPool(POOL_SIZE >= 2 ? (POOL_SIZE/2) : 1);

//    @PostConstruct
//    private void init(){
////        refreshSeriesTabInfoJob();
//        refreshSeriesTabInfoCityJob();
//    }

    /**
     * 车系的tab
     * */
    @XxlJob("RefreshSeriesTabInfoJob")
    public ReturnT<String> refreshSeriesTabInfoJob() {
        JobLog.info("RefreshSeriesTabInfoJob.handle() begin" );

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemList = AllCitiesUtil.getAllCityV2();//全量城市

        //查询所有的在售车系
        List<SeriesInfoDto> allSeriesInfoDtoList = dealerDataToQqService.getAllSeriesInfoDtoList();

        refreshSeriesTabInfoJob(allSeriesInfoDtoList, cityItemList);

        JobLog.info("RefreshSeriesTabInfoJob.handle() end" );
        return ReturnT.SUCCESS;
    }

    /**
     * 需要城市的tab
     * */
    @XxlJob("refreshSeriesTabInfoCityJob")
    public ReturnT<String> refreshSeriesTabInfoCityJob() {
        JobLog.info("refreshSeriesTabInfoCityJob.handle() begin" );

        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemList = AllCitiesUtil.getAllCityV2();//全量城市

        //查询所有的在售车系
        List<SeriesInfoDto> allSeriesInfoDtoList = dealerDataToQqService.getAllSeriesInfoDtoList();

        refreshSeriesTabInfoCityJob(allSeriesInfoDtoList, cityItemList);

        JobLog.info("refreshSeriesTabInfoCityJob.handle() end" );
        return ReturnT.SUCCESS;
    }

    private void refreshSeriesTabInfoJob(List<SeriesInfoDto> seriesInfoDtoList, List<AllCitiesUtil.CityItem> cityItemList){
        final int[] index = {0};
        seriesInfoDtoList.forEach(o -> {
            String content = "refreshSeriesTabInfoJob current index " + index[0] + " seriesId = " + o.getId() + " seriesName = " + o.getName();
            log.info(content);
            JobLog.info(content);
            //1.刷新资讯、视频
            forkJoinPoolLimit.submit(()->{
                seriesBaseCardInfoServies.refreshSeriesTabInfoDtosCache(o.getId());
                index[0]++ ;
            }).join();
        });
    }

    private void refreshSeriesTabInfoCityJob(List<SeriesInfoDto> seriesInfoDtoList, List<AllCitiesUtil.CityItem> cityItemList){
        final int[] index = {0};

        String mes = String.format("refreshSeriesTabInfoCityJob series %s , cities %s", seriesInfoDtoList.size(), cityItemList.size()) ;
        log.info(mes);
        JobLog.info(mes);

        for (SeriesInfoDto o : seriesInfoDtoList) {
            String content = String.format("refreshSeriesTabInfoCityJob current index %s seriesId = %s seriesName = %s", index[0], o.getId(), o.getName());

            String key = "refresh:series:tab:city:series:" + o.getId();
            boolean isLock = redisClient.setValueIfNotExists(key, String.valueOf(o.getId()));

            if (!isLock) {
                content = String.format("refreshSeriesTabInfoCityJob current has locked index %s seriesId = %s seriesName = %s", index[0], o.getId(), o.getName());
                log.info(content);
                JobLog.info(content);
                index[0]++;
                continue;
            }
            redisClient.set(key, o.getId(), 40, TimeUnit.MINUTES);
            log.info(content);
            JobLog.info(content);

            //1.刷新二手车、同级车
            //开启多线程
//            cityItemList.forEach(obj -> {
//                forkJoinPool.submit(() -> {
//                    try {
//                        seriesBaseCardInfoServies.refreshSeriesTabInfoCityDtosCache(o.getId(), obj);
//                        Thread.sleep(500);
//                    } catch (Exception ex) {
//                        log.error("refreshSeriesTabInfoCityJob:error:seriesId:{},city:{}", o.getId(), JacksonHelper.serialize(obj), ex);
//                    }
//                }).join();
//            });

            List<ForkJoinTask<?>> tasks = new ArrayList<>();
            for (AllCitiesUtil.CityItem obj : cityItemList) {
                ForkJoinTask<?> task = forkJoinPool.submit(() -> {
                    try {
                        seriesBaseCardInfoServies.refreshSeriesTabInfoCityDtosCache(o.getId(), obj);
                    } catch (Exception ex) {
                        log.error("refreshSeriesTabInfoCityJob:error:seriesId:{},city:{}", o.getId(), JacksonHelper.serialize(obj), ex);

                    }
                });
                tasks.add(task);
            }
            for (ForkJoinTask<?> task : tasks) {
                task.join();
            }
            index[0]++;
        }
    }
}
