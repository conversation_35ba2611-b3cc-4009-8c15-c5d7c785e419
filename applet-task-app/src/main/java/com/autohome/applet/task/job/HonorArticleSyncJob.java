package com.autohome.applet.task.job;

import com.autohome.applet.service.openapi.HonorService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HonorArticleSyncJob {

    @Autowired
    HonorService honorService;

    @XxlJob("honorArticleSyncJob")
    public ReturnT<String> handle() {
        log.info("HonorArticleSyncJob.handle()");
        JobLog.info("honorArticleSyncJob begin" );
        honorService.syncArticle(LocalDateTime.now().minusDays(2));
        return ReturnT.SUCCESS;
    }
}
