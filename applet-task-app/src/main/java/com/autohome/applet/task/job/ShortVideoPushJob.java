package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.ShortVideo.VideoAppletDto;
import com.autohome.applet.service.ShortVideoPushService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * 短视频推送
 * <AUTHOR>
 */
@Slf4j
@Component
public class ShortVideoPushJob {
    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    ShortVideoPushService shortVideoPushService;



/*    @PostConstruct
    public void init() {
        try {
            handle();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    //设置并发数量
    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private ForkJoinPool forkJoinPool = new ForkJoinPool(POOL_SIZE);

    @XxlJob("ShortVideoPush2345Job")
    public ReturnT<String> handle() {
        JobLog.info("ShortVideoPush2345Job.handle() POOL_SIZE:" + POOL_SIZE);
        String Param = XxlJobHelper.getJobParam();
        //String Param = "2023-07-19,2023-12-08";
        JobLog.info("param:" + Param);
        List<String> paramTime = getRecoverableCodes(Param);
        if(!CollectionUtils.isEmpty(paramTime)){
            getEveryDayPush(paramTime);
        }else {
            getLastHourPush();
        }
        return ReturnT.SUCCESS;
    }

    private ReturnT<String> getLastHourPush() {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 格式化输出当前时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String endTime = currentTime.format(formatter);
        // 获取前1小时的时间
        LocalDateTime oneHourAgo = currentTime.minusHours(1);
        // 格式化输出前1小时的时间
        String startTime = oneHourAgo.format(formatter);
        push(startTime, endTime);
        return ReturnT.SUCCESS;
    }

    private ReturnT<String> getEveryDayPush(List<String> paramTime) {
        String oneHourAgoString = paramTime.get(0);
        String currentTimeString = paramTime.get(1);
        Date startDate = DateHelper.deserialize(oneHourAgoString, DateHelper.DATEFORMAT_ONLY_DATE);
        Date endDate = DateHelper.deserialize(currentTimeString, DateHelper.DATEFORMAT_ONLY_DATE);
        LocalDate localStarDate = DateHelper.dateToLocalDate(startDate);
        LocalDate localEndDate = DateHelper.dateToLocalDate(endDate);
        long days = ChronoUnit.DAYS.between(localStarDate, localEndDate);
        for (int i = 1; i < days+1; i++) {
            LocalDate localStarAddDayDate = localStarDate.plusDays(i);
            if (!localStarAddDayDate.equals(localEndDate.plusDays(1))){
                LocalDate minusDays = localStarAddDayDate.minusDays(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String startTime = minusDays.format(formatter) + " 00:00:00";
                String endTime = localStarAddDayDate.format(formatter)+ " 00:00:00";
                push(startTime, endTime);
            }

        }
        return ReturnT.SUCCESS;
    }


    private ReturnT<String> push(String startTime , String endTime){

        //获取video短视频数据
        List<VideoAppletDto> videoAppletDtoList = shortVideoPushService.getVideoApplet(startTime , endTime);
        if(CollectionUtils.isEmpty(videoAppletDtoList)){
            return ReturnT.SUCCESS;
        }

        shortVideoPushService.uploadToShortVideoPush(videoAppletDtoList,startTime , endTime);

        return ReturnT.SUCCESS;
    }

    private List<String> getRecoverableCodes(String param) {
        if(StringUtils.isEmpty(param)){
            return null;
        }
        return Arrays.stream(org.apache.commons.lang3.StringUtils.split(param, ","))
                .map(String::valueOf)
                .collect(Collectors.toList());
    }



}