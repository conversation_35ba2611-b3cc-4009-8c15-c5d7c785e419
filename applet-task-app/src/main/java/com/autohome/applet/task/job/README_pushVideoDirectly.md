# 视频流式上传方法使用说明

## 概述

本项目提供了两种视频上传方法，都实现了**文件不落地**的功能，但采用了不同的技术方案：

1. **`pushVideoDirectly`** - 内存缓存方式：先下载到内存，再上传
2. **`pushVideoStreamDirectly`** - 真正的流式上传：边下载边上传，零内存占用

## 功能特点

1. **文件不落地**：视频数据直接在内存中处理，不会在本地磁盘创建临时文件
2. **内存限制**：设置了100MB的文件大小限制，防止内存溢出
3. **完善的错误处理**：包含网络异常、文件大小超限、上传失败等各种异常情况的处理
4. **详细的日志记录**：记录下载进度、上传状态等关键信息
5. **自动文件名提取**：从URL中智能提取文件名，支持各种URL格式

## 方法签名

```java
public void pushVideoDirectly(String videoUrl)
```

## 参数说明

- `videoUrl`: 视频的网络URL地址，支持HTTP/HTTPS协议

## 使用示例

### 单个视频上传

```java
@Autowired
private ZFBPushInfoJob zfbPushInfoJob;

public void uploadSingleVideo() {
    String videoUrl = "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4";
    zfbPushInfoJob.pushVideoDirectly(videoUrl);
}
```

### 批量视频上传

```java
public void uploadMultipleVideos() {
    String[] videoUrls = {
        "http://example.com/video1.mp4",
        "http://example.com/video2.mp4",
        "http://example.com/video3.mp4"
    };
    
    for (String videoUrl : videoUrls) {
        try {
            zfbPushInfoJob.pushVideoDirectly(videoUrl);
            // 添加延迟，避免请求过于频繁
            Thread.sleep(2000);
        } catch (Exception e) {
            log.error("视频上传失败: {}", videoUrl, e);
        }
    }
}
```

## 技术实现

### 核心流程

1. **下载视频到内存**：使用 `downloadVideoToMemory` 方法
   - 创建HTTP连接
   - 使用 `ByteArrayOutputStream` 收集数据
   - 实时监控文件大小，防止超限

2. **提取文件名**：使用 `extractFileNameFromUrl` 方法
   - 从URL路径中提取文件名
   - 处理各种边界情况（无扩展名、文件名过长等）

3. **创建FileItem**：使用字节数组构造函数
   ```java
   FileItem fileContent = new FileItem(fileName, videoData);
   ```

4. **上传到支付宝**：调用现有的 `uploadMaterial` 方法

5. **发布内容**：调用现有的 `pushContent` 方法

### 关键配置

- **最大文件大小**：100MB (100 * 1024 * 1024 bytes)
- **缓冲区大小**：8KB (8192 bytes)
- **连接超时**：10秒
- **读取超时**：60秒

## 错误处理

### 常见错误及解决方案

1. **文件过大**
   - 错误信息：`视频文件过大，大小: xxx bytes, 最大限制: xxx bytes`
   - 解决方案：压缩视频文件或调整MAX_FILE_SIZE配置

2. **网络连接失败**
   - 错误信息：`视频下载请求失败，响应码: xxx`
   - 解决方案：检查网络连接和URL有效性

3. **内存不足**
   - 错误信息：`OutOfMemoryError`
   - 解决方案：增加JVM内存或减小文件大小限制

4. **支付宝上传失败**
   - 错误信息：`支付宝素材上传失败`
   - 解决方案：检查支付宝配置和网络连接

## 性能考虑

1. **内存使用**：大视频文件会占用相应的内存空间，建议监控内存使用情况
2. **网络带宽**：下载大文件会消耗网络带宽，建议在低峰期执行
3. **并发控制**：避免同时处理多个大文件，建议串行处理或限制并发数

## 日志示例

```
2024-01-01 10:00:00 INFO  - 开始处理视频上传（文件不落地）, videoUrl: http://example.com/video.mp4
2024-01-01 10:00:05 INFO  - 视频下载进度: 10 MB
2024-01-01 10:00:10 INFO  - 视频下载完成，总大小: 52428800 bytes
2024-01-01 10:00:10 INFO  - 视频下载成功，文件大小: 52428800 bytes
2024-01-01 10:00:10 INFO  - 提取的文件名: video.mp4
2024-01-01 10:00:15 INFO  - 支付宝素材上传成功, fileId: 20240101xxxxxxxxxxxx
2024-01-01 10:00:16 INFO  - 视频内容发布成功, response: {...}
```

## 与原有方法的区别

| 特性 | pushVideo (原方法) | pushVideoDirectly (新方法) |
|------|-------------------|---------------------------|
| 文件存储 | 落地到磁盘 | 仅在内存中处理 |
| 磁盘IO | 有 | 无 |
| 内存使用 | 较少 | 较多 |
| 处理速度 | 较慢 | 较快 |
| 临时文件清理 | 需要 | 不需要 |
| 文件大小限制 | 无明确限制 | 100MB |

## 注意事项

1. **内存限制**：确保JVM有足够内存处理大文件
2. **网络稳定性**：确保网络连接稳定，避免下载中断
3. **URL有效性**：确保视频URL可访问且返回有效的视频文件
4. **支付宝配置**：确保支付宝相关配置正确
5. **并发控制**：避免同时处理过多大文件导致内存不足

## 测试建议

1. 使用小文件进行功能测试
2. 测试各种URL格式的兼容性
3. 测试网络异常情况的处理
4. 监控内存使用情况
5. 验证支付宝上传和发布功能
