package com.autohome.applet.task.config;

import cn.hutool.core.util.ObjectUtil;
import com.autohome.applet.util.JacksonHelper;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.Properties;


/**
 * 初始化多个KafkaListener配置
 */
@Configuration
public class KafkaConfig {

    private final  static Logger LOGGER = LoggerFactory.getLogger(KafkaConfig.class);

    /**
     * 二手车推送百度阿拉丁
     * @return
     */
    @Primary
    @ConfigurationProperties(prefix = "spring.kafka.baidu")
    @Bean(name = "secondCarToBaiduKafkaProperties")
    @Qualifier
    public KafkaProperties applogKafkaProperties(){
        return new KafkaProperties();
    }

    @Bean(name = "secondCarToBaiduContainerFactory")
    KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> secondCarToBaiduContainerFactory(@Autowired @Qualifier("secondCarToBaiduKafkaProperties") KafkaProperties properties) {
        return createKafkaContainerFactory(properties);
    }

    /**
     *  通过配置KafkaProperties创建KafkaListenerContainerFactory
     */
    private KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> createKafkaContainerFactory(KafkaProperties kafkaProperties) {
        ConsumerFactory<Integer, String> consumerFactory = new DefaultKafkaConsumerFactory<>(kafkaProperties.buildConsumerProperties());
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        Integer concurrent = kafkaProperties.getListener().getConcurrency();
        factory.setConcurrency(ObjectUtil.isEmpty(concurrent) ? Runtime.getRuntime().availableProcessors() : concurrent);
        factory.getContainerProperties().setPollTimeout(30000);
        //ContainerProperties.AckMode ackMode = kafkaProperties.getListener().getAckMode();
        //factory.getContainerProperties().setAckMode(ObjectUtil.isEmpty(ackMode) ? ContainerProperties.AckMode.MANUAL:ackMode);
        LOGGER.info("完成初始化kafka {}", JacksonHelper.serialize(kafkaProperties.buildConsumerProperties()));
        return factory;
    }

    @Bean(name = "sourceKafkaProducer")
    @Qualifier
    public KafkaProducer<String, String> sourceKafkaProducer(@Autowired @Qualifier("secondCarToBaiduKafkaProperties") KafkaProperties properties){
        Properties kafkaProp = new Properties();
        kafkaProp.put("bootstrap.servers", properties.getBootstrapServers());
        kafkaProp.put("client.id", properties.getConsumer().getClientId());
        kafkaProp.put("retries", 2); // 发送失败的最大尝试次数
        kafkaProp.put("batch.size", "1048576"); // 1MB
        kafkaProp.put("compression.type", "gzip");
        kafkaProp.put("linger.ms", "5");
        kafkaProp.put("buffer.memory", "67108864");// 64MB
        kafkaProp.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        kafkaProp.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        KafkaProducer producer= new KafkaProducer<String, String>(kafkaProp);

        return producer;
    }


    /**
     * 微信小程序kafka配置
     * @return 配置属性
     */
    @ConfigurationProperties(prefix = "spring.kafka.mp")
    @Bean(name = "mpKafkaProperties")
    @Qualifier
    public KafkaProperties mpKafkaProperties(){
        return new KafkaProperties();
    }

    @Bean(name = "mpContainerFactory")
    KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> mpContainerFactory(@Autowired @Qualifier("mpKafkaProperties") KafkaProperties properties) {
        return createKafkaContainerFactory(properties);
    }

    /**
     * 微信小程序kafka producer
     * @param properties 配置属性
     * @return kafka producer
     */
    @Bean(name = "mpKafkaProducer")
    @Qualifier
    public KafkaProducer<String, String> mpKafkaProducer(@Autowired @Qualifier("mpKafkaProperties") KafkaProperties properties){
        Properties kafkaProp = new Properties();
        kafkaProp.put("bootstrap.servers", properties.getBootstrapServers());
        kafkaProp.put("client.id", properties.getConsumer().getClientId());
        kafkaProp.put("retries", 2); // 发送失败的最大尝试次数
        kafkaProp.put("batch.size", "1048576"); // 1MB
        kafkaProp.put("compression.type", "gzip");
        kafkaProp.put("linger.ms", "5");
        kafkaProp.put("buffer.memory", "67108864");// 64MB
        kafkaProp.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        kafkaProp.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        KafkaProducer producer= new KafkaProducer<String, String>(kafkaProp);
        return producer;
    }

}
