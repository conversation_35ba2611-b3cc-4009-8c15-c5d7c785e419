package com.autohome.applet.task.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.NamedThreadFactory;
import com.autohome.applet.dao.javaapi.model.qrcode.QrCodeModel;
import com.autohome.applet.service.qrcode.QrCodeCleanService;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.MapUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QrCodeCleanJob {
    @Autowired
    QrCodeCleanService qrCodeCleanService;

    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();

    private static final ExecutorService executorService = ExecutorBuilder.create()
            .setCorePoolSize(POOL_SIZE)
            .setMaxPoolSize(POOL_SIZE)
            .setWorkQueue(new LinkedBlockingQueue<>(10240))
            .setThreadFactory(new NamedThreadFactory("QrCodeCleanJobHandler", false))
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .build();

    /**
     * todo 提交代码前去掉！！！
     */
/*    @PostConstruct
    public void init(){
        //qrCodeClean();
    }*/

    /**
     * 微信二维码清除任务
     * {"minId":88736742,"maxId":1104200000,"sence":"auto_open_from=autohomepc_","endTime":"2024-08-11","logStep":1,"pageSize":10,"jobType":"CLEAN_DATA"}
     * @return
     */
    @XxlJob("QrCodeCleanJob")
    public ReturnT<String> qrCodeClean() {
        String Param = XxlJobHelper.getJobParam();
        //Param = "{\"minId\":88776743,\"maxId\":88776843,\"sence\":\"auto_open_from=autohomepc_\",\"endTime\":\"2024-08-11\",\"logStep\":1,\"pageSize\":10,\"jobType\":\"CLEAN_IMG\"}";
        log.info("QrCodeCleanJob() Start:{}",Param);
        QrCodeCleanJobParam qrCodeCleanJobParam = Param==null ? null: JacksonHelper.deserialize(Param,QrCodeCleanJobParam.class);
        if(qrCodeCleanJobParam == null){
            qrCodeCleanJobParam = new QrCodeCleanJobParam();
        }

        log.info("param:{}" , qrCodeCleanJobParam);

        Date endTime = StringUtils.isNotBlank(qrCodeCleanJobParam.getEndTime()) ? DateUtil.parse(qrCodeCleanJobParam.getEndTime()) :
                DateUtil.offsetDay(DateUtil.date(), -180);

        int shardIndex = XxlJobHelper.getShardIndex()< 0 ? 0 :XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal()< 1 ? 1 :XxlJobHelper.getShardTotal();
        String jobType = qrCodeCleanJobParam.getJobType();
        if(!JobType.getNamesMap().containsKey(jobType)){
            log.error("jobType不存在:{}",jobType);
            return ReturnT.FAIL;
        }
        if(qrCodeCleanJobParam.getMinId() > qrCodeCleanJobParam.getMaxId()){
            log.info("qrCodeClean end... minId > maxId");
            return ReturnT.SUCCESS;
        }

        long startTime = System.currentTimeMillis();
        try{
            Tuple3<AtomicLong, AtomicLong, AtomicLong> tuple3 = cleanQrCodeDataAndImg(qrCodeCleanJobParam.getMinId(),
                    qrCodeCleanJobParam.getMaxId(),
                    qrCodeCleanJobParam.getPageSize(),
                    qrCodeCleanJobParam.getSence(),endTime,shardIndex,shardTotal,
                    qrCodeCleanJobParam.getLogStep(),jobType, qrCodeCleanJobParam.doSleepMs);
            log.info("qrCodeClean end...:success:{},fail:{},total:{},其他参数:{},{},{},logStep={},jobType={},执行时间:{}ms",tuple3.getT1(),tuple3.getT2(),tuple3.getT3(),qrCodeCleanJobParam,endTime,shardIndex,shardTotal,jobType,(System.currentTimeMillis()-startTime));
        }catch (Exception ex){
            log.error("QrCodeCleanJob ex:{}",qrCodeCleanJobParam,ex);
        }

        return ReturnT.SUCCESS;
    }

    private Tuple3<AtomicLong, AtomicLong, AtomicLong> cleanQrCodeDataAndImg(long minId, long maxId,int pageSize, String sence, Date endTime, int shardIndex, int shardTotal,int logStep,String jobType,int doSleepMs){
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong failCount = new AtomicLong(0);
        AtomicLong total = new AtomicLong(0);
        long lastId = minId;
        pageSize = pageSize < 1 ? 1000 : pageSize;
        log.info("cleanQrCodeDataAndImg start:{},{},{},{},logStep={},jobType={},shardIndex={},shardTotal={}",minId,maxId,sence,endTime,logStep,jobType,shardIndex,shardTotal);
        while (true){
            List<QrCodeModel> list;
            //图片清除是查已删除的数据来清除图片，其他的任务是查未删除的数据
            if(JobType.CLEAN_IMG.toString().equalsIgnoreCase(jobType)){
                list = qrCodeCleanService.getByPage(lastId,maxId,pageSize,shardIndex,shardTotal,true);
            }else{
                list = qrCodeCleanService.getByPage(lastId,maxId,pageSize,shardIndex,shardTotal,false);
            }
            log.info("cleanQrCodeDataAndImg getByPage:list.size:{},lastId:{},shardIndex:{},shardTotal:{}",list.size(),lastId,shardIndex,shardTotal);

            lastId = lastId+pageSize;
            if(CollectionUtils.isEmpty(list)){

            }else{
                if(JobType.CLEAN_DATA.toString().equalsIgnoreCase(jobType)){
                    cleanData(list,successCount,failCount,total,logStep,sence,endTime);
                }else if(JobType.CLEAN_IMG.toString().equalsIgnoreCase(jobType)){
                    cleanImg(list,successCount,failCount,total,logStep,sence,endTime,doSleepMs);
                }else if(JobType.CLEAN_DATA_AND_IMG.toString().equalsIgnoreCase(jobType)){
                    cleanDataAndImgOneByOne(list,successCount,failCount,total,logStep,sence,endTime,doSleepMs);
                }else{
                    throw new RuntimeException("不支持的操作:"+jobType);
                }
                if(lastId < list.get(list.size()-1).getQrCodeId()){
                    lastId = list.get(list.size()-1).getQrCodeId();
                }
            }
            if(lastId >= maxId){
                log.info("cleanQrCodeDataAndImg end getByPage lastId >= maxId:{},lastId:{},shardIndex:{},shardTotal:{}",maxId,lastId,shardIndex,shardTotal);
                break;
            }
        }
        return Tuples.of(successCount,failCount,total);
    }

    private void cleanData(List<QrCodeModel> list,AtomicLong successCount,AtomicLong failCount,AtomicLong total,int logStep,String sence,Date endTime){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        total.addAndGet(list.size());
        List<Long> qrIds = new ArrayList<>(list.size());
        for (QrCodeModel data : list) {
            if(data.getSence()!= null && data.getSence().startsWith(sence)
                    && data.getCreatedSTime().getTime()<= endTime.getTime()){
                qrIds.add(data.getQrCodeId());
            }
        }
        if(!CollectionUtils.isEmpty(qrIds)){
            qrCodeCleanService.updateQrCodeStatus(qrIds,QrCodeModel.DEL_STATUS);
            successCount.addAndGet(qrIds.size());
        }
    }

    private void cleanImg(List<QrCodeModel> list,AtomicLong successCount,AtomicLong failCount,AtomicLong total,int logStep,String sence,Date endTime,int doSleepMs){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<Long> qrIds = new CopyOnWriteArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (QrCodeModel data : list) {
            executorService.submit(() -> {
                boolean logInfo = (total.addAndGet(1) % logStep)==0;
                try {
                    if(data.getSence()!= null && data.getSence().startsWith(sence)
                            && data.getCreatedSTime().getTime()<= endTime.getTime()){
                        //先清除图片
                        boolean isSuccess = qrCodeCleanService.cleanQrCodeImg(data,logInfo);
                        if (isSuccess){
                            qrIds.add(data.getQrCodeId());
                            successCount.addAndGet(1);
                        }else{
                            failCount.addAndGet(1);
                        }
                        if(doSleepMs > 0){
                            Thread.sleep(doSleepMs);//todo fastdfs业务方：qps承受100
                        }
                    }else{
                        if(logInfo){
                            log.info("cleanImg该条数据不用处理:{}",data);
                        }
                    }
                } catch (Exception exception) {
                    log.error("cleanImg定时任务异常:{}",data, exception);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("cleanImg error:{}",e.getMessage(),e);
        }
        if(!CollectionUtils.isEmpty(qrIds)){
            qrCodeCleanService.updateQrCodeStatus(qrIds,QrCodeModel.DEL_IMG_STATUS);
        }
    }

    private void cleanDataAndImgOneByOne(List<QrCodeModel> list,AtomicLong successCount,AtomicLong failCount,AtomicLong total,int logStep,String sence,Date endTime,int doSleepMs){
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (QrCodeModel data : list) {
            executorService.submit(() -> {
                boolean logInfo = (total.addAndGet(1) % logStep)==0;
                try {
                    if(data.getSence()!= null && data.getSence().startsWith(sence)
                            && data.getCreatedSTime().getTime()<= endTime.getTime()){
                        //清除数据：先清除图片，成功后再删除这条数据
                        boolean isSuccess = qrCodeCleanService.cleanQrCodeAndImg(data,logInfo);
                        if (isSuccess){
                            successCount.addAndGet(1);
                        }else{
                            failCount.addAndGet(1);
                        }
                        if(doSleepMs > 0){
                            Thread.sleep(doSleepMs);//todo fastdfs业务方：qps承受100
                        }
                    }else{
                        if(logInfo){
                            log.info("cleanDataAndImgOneByOne该条数据不用处理:{}",data);
                        }
                    }
                } catch (Exception exception) {
                    log.error("cleanDataAndImgOneByOne定时任务异常:{}",data, exception);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("cleanDataAndImgOneByOne error:{}",e.getMessage(),e);
        }
    }

    public static enum JobType{
        CLEAN_DATA,
        CLEAN_IMG,
        CLEAN_DATA_AND_IMG;

        private final static Map<String, JobType> _NAMES_MAP =
                Arrays.stream(values()).collect(Collectors.toMap(Enum::toString, e -> e));

        public static Map<String, JobType> getNamesMap(){
            return _NAMES_MAP;
        }
    }

    @Data
    public static class QrCodeCleanJobParam{
        private long minId = 88736742;
        private long maxId = 1104200000;
        private int pageSize = 1000;
        private String sence = "auto_open_from=autohomepc_";
        private String endTime;
        //每几条数据打印一次日志，1为每行数据都打印日志
        private int logStep = 1;
        private String jobType = JobType.CLEAN_DATA.toString();
        private int doSleepMs = 0;//做完一次任务时睡眠多久
    }
}
