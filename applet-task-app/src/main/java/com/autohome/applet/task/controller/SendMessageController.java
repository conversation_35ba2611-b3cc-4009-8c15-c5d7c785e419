package com.autohome.applet.task.controller;

import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.vrinfodata.BrandNewNew;
import com.autohome.applet.model.dto.vrinfodata.NewSeriesNew;
import com.autohome.applet.service.DealerDataToQqService;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.javaapi.CarService;
import com.autohome.applet.service.javaapi.SerachCarsService;
import com.autohome.applet.service.openapi.VRInfoService;
import com.autohome.applet.util.*;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 推送kafka测试数据
 */
@Slf4j
@RestController
@ApiIgnore
public class SendMessageController {

    @Autowired
    private DealerDataToQqService dealerDataToQqService;

    @Autowired
    private CarSeriesService carSeriesService;
    @Autowired
    private CarService carService;
    @Autowired
    private VRInfoService vrInfoService;


    @Autowired
    SerachCarsService serachCarsService;
    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private ForkJoinPool forkJoinPool = new ForkJoinPool(POOL_SIZE);



    @GetMapping("/b1")
    public Object dtestPost() {
        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV3 = AllCitiesUtil.getAllCityV2();//全量城市
        //2.加载品牌列表
        List<BrandForQQUtil.BrandForQQ> brandIdListV3 = BrandForQQUtil.getAllBrandForQQV3();
        //3.查询品牌下的在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();
        //4.查询所有品牌信息
        List<AllBrandInfoDto.BrandInfoDto> brandInfoDtoList = carSeriesService.getAllBrandInfos();
        //入参
        SeriesInfoBrandDto seriesInfoBrandParam = new SeriesInfoBrandDto();

        //5.获取车系排行榜信息
        seriesInfoBrandParam.setRank(carService.getrecranklistpageresult2());

        if (CollectionUtils.isEmpty(brandIdListV3)) {
            JobLog.info("brandIdList is null");
            return ReturnT.SUCCESS;
        }
        JobLog.info("需同步品牌数量V3:" + brandIdListV3.size());
        JobLog.info("需同步品牌V3:" + JacksonHelper.serialize(brandIdListV3));

        List<String> brandIds = brandIdListV3.stream().map(BrandForQQUtil.BrandForQQ::getBrandId).collect(Collectors.toList());

        List<AllBrandInfoDto.BrandInfoDto> intersectionBrandList = brandInfoDtoList.stream()
                .filter(brandInfo -> brandIds.contains(brandInfo.getId() + ""))
                .collect(Collectors.toList());

//        forkJoinPool.submit(() -> {

            for (AllBrandInfoDto.BrandInfoDto obj : intersectionBrandList) {

                    try {
                    //6.获取vr信息
                    setVr(seriesInfoBrandParam, obj.getId() + "");
                    //取出当前品牌下的所有在售停售车系
                    List<Integer> seriesIds = seriesInfoDtoList.stream().filter(e -> e.getBrandid() == obj.getId()).map(SeriesInfoDto::getId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(seriesIds)) {
                        log.info("BrandId:{} 品牌下的在售和停产在售车系列表，seriesIds={}", obj.getId(), seriesIds);
                        continue;
                    }
                    //7.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
                    List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
                    Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                            .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity()));

                    seriesInfoBrandParam.setCarSeriesInfoMap(carSeriesInfoMap);
                    //上传数据
                    dealerDataToQqService.uploadBrandToQQV3(obj, cityItemListV3, seriesInfoDtoList, seriesInfoBrandParam, true);
                    JobLog.info(String.format("UploadBrandToQQJob:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));

                } catch (Exception ex) {
                    log.error("UploadBrandToQQJob:error:brandId:{},obj:{}", obj.getId(), JacksonHelper.serialize(obj), ex);
                    JobLog.info(String.format("UploadBrandToQQJob:error:brandId:%s,obj:%s", obj.getId(), JacksonHelper.serialize(obj)));
                }
            }
//        }).join();

        return "{\"returncode\":0,\"message\":\"\"}";
    }

    @GetMapping("/s2")
    public Object stestPost() {



        //1.加载城市列表
        List<AllCitiesUtil.CityItem> cityItemListV2 = AllCitiesUtil.getAllCityV2();
        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> seriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();


        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = seriesInfoListV3.stream()
                .map(series -> Integer.parseInt(series.getSeriesId()))
                .collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));

        if (CollectionUtils.isEmpty(seriesInfoListV3)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需同步车系数量-V3:" + seriesInfoListV3.size());
        JobLog.info("需同步车系-V3:" + JacksonHelper.serialize(seriesInfoListV3));

        seriesInfoListV3.parallelStream().forEach(obj -> {
            SpecParamDto specParamDto = new SpecParamDto();
            specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
            specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

            try {
                //查询
                dealerDataToQqService.uploadSpecToQQV3(obj, cityItemListV2, specParamDto, false);
                JobLog.info(String.format("UploadBrandToQQJobV2:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            } catch (Exception ex) {
                log.error("UploadSeriesToQQJobV2:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadSeriesToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }

        });


        return "{\"returncode\":0,\"message\":\"\"}";
    }


    @GetMapping("/p3")
    public Object picTest() {


        //2.加载车系列表
        List<SeriesForQQUtil.SeriesForQQ> seriesInfoListV3 = SeriesForQQUtil.getSeriesForQQListV3();
        //3.查询所有在售和停产在售车系列表 wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=6358641
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();


        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = seriesInfoListV3.stream()
                .map(series -> Integer.parseInt(series.getSeriesId()))
                .collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));



        if (CollectionUtils.isEmpty(seriesInfoListV3)) {
            JobLog.info("seriesInfoList is null");
            return ReturnT.SUCCESS;
        }

        JobLog.info("需同步pic数量-V3:" + seriesInfoListV3.size());
        JobLog.info("需同步pic-V3:" + JacksonHelper.serialize(seriesInfoListV3));

        seriesInfoListV3.forEach(obj -> {
            try {

                SpecParamDto specParamDto = new SpecParamDto();
                specParamDto.setSeriesInfoDtoList(seriesInfoDtoList);
                specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);
                //查询
                dealerDataToQqService.uploadPicToQQV3(obj, specParamDto, false);
                JobLog.info(String.format("UploadPicToQQJobV2:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            } catch (Exception ex) {
                log.error("UploadPicToQQJobV2:error:seriesId:{},obj:{}", obj.getSeriesId(), JacksonHelper.serialize(obj), ex);
                JobLog.info(String.format("UploadPicToQQJobV2:error:seriesId:%s,obj:%s", obj.getSeriesId(), JacksonHelper.serialize(obj)));
            }
        });


        return "{\"returncode\":0,\"message\":\"\"}";
    }

    @GetMapping("/pj4")
    public Object pjTest() {

        //获取文件中品牌列表
        List<BrandForQQUtil.BrandForQQ> customBrandList = BrandForQQUtil.getAllBrandForQQV3();

        //获取文件中车系列表
        List<SeriesForQQUtil.SeriesForQQ> customSeriesList = SeriesForQQUtil.getSeriesForQQListV3();

        //查询所有的在售车系
        List<SeriesInfoDto> allSeriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();

        //查询所有品牌
        List<AllBrandInfoDto.BrandInfoDto> allBrandInfoList = carSeriesService.getAllBrandInfos();

        //4.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<Integer> seriesIds = customSeriesList.stream()
                .map(series -> Integer.parseInt(series.getSeriesId()))
                .collect(Collectors.toList());

        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity(), (oldValue, newValue) -> oldValue));


        JobLog.info("需要同步的车系数量:" + customSeriesList.size());
        JobLog.info("需要同步的车系:" + JacksonHelper.serialize(customSeriesList));


        customSeriesList.forEach(s -> {
            try {

                SpecParamDto specParamDto = new SpecParamDto();
                specParamDto.setCarSeriesInfoMap(carSeriesInfoMap);

                dealerDataToQqService.uploadEvaluateCardToQQ(s, allSeriesInfoDtoList, specParamDto, false);
                JobLog.info(String.format("EvaluateCardXmlToQQJob:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper
                        .serialize(s)));
            } catch (Exception e) {
                log.error("EvaluateCardXmlToQQJob:error:seriesId:{},obj:{}", s.getSeriesId(), JacksonHelper.serialize(s), e);
                JobLog.info(String.format("UploadSeriesToQQJob:error:seriesId:%s,obj:%s", s.getSeriesId(), JacksonHelper.serialize(s)));
            }
        });


        return "{\"returncode\":0,\"message\":\"\"}";
    }

    /**
     * 通过品牌ID获取品牌下所有车系ID
     * set vr
     *
     * @param seriesInfoBrandParam
     * @param brandId
     */
    private void setVr(SeriesInfoBrandDto seriesInfoBrandParam, String brandId) {
        NewSeriesNew newSeriesNew = vrInfoService.getSerieslistHasVRByBrandId(Integer.parseInt(brandId));
        Set<Integer> vrSeriesIdSet = Stream.concat(
                        newSeriesNew.getSellseries().stream()
                                .flatMap(fctlist -> fctlist.getSerieslist().stream()),
                        newSeriesNew.getUnsellseries().stream()
                                .flatMap(fctlist -> fctlist.getSerieslist().stream())
                )
                .map(BrandNewNew.Serieslist::getId)
                .collect(Collectors.toSet());

        seriesInfoBrandParam.setSeriesIdsSet(vrSeriesIdSet);

    }

    /**
     * 通过品牌ID获取品牌下所有车系ID
     * set vr
     *
     * @param seriesInfoBrandParam
     * @param brandId
     */
    private void setSeriesInfo(SeriesInfoBrandDto seriesInfoBrandParam, String brandId, List<Integer> seriesIds) {
        //7.一次性查出该品牌下所有车系，并将该车系详情，避免内部循环多次调用
        List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = carSeriesInfoDTOList.stream()
                .collect(Collectors.toMap(CarSeriesInfoDTO::getSeriesid, Function.identity()));

        seriesInfoBrandParam.setCarSeriesInfoMap(carSeriesInfoMap);


    }

    public static void main(String[] args) {
        Map<String, List<String>> classification = new HashMap<>();

        classification.put("轿车", Arrays.asList("微型车", "小型车", "紧凑型车", "中型车", "中大型车", "大型车"));
        classification.put("SUV", Arrays.asList("小型SUV", "紧凑型SUV", "中型SUV", "中大型SUV", "大型SUV"));
        classification.put("MPV", Arrays.asList("紧凑型MPV", "中型MPV", "中大型MPV", "大型MPV"));


    }

    @GetMapping("/ca")
    public Object ca() {
        serachCarsService.cacheSeriesAttNum();
        return 1;
    }
}
