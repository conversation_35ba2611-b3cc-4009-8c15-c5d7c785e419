package com.autohome.applet.task.job;

import com.autohome.applet.dao.javaapi.model.AppletDoTaskLog;
import com.autohome.applet.dao.javaapi.model.AppletOpenAutohomeRelation;
import com.autohome.applet.dao.javaapi.model.dto.CLueDoJobDTO;
import com.autohome.applet.dao.javaapi.model.dto.DoJobResultDTO;
import com.autohome.applet.service.AppletDoTaskLogService;
import com.autohome.applet.service.AppletOpenAutohomeRelationService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.comm.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AppletDoTaskJob {
    @Autowired
    private AppletDoTaskLogService appletDoTaskLogService;
    @Autowired
    private AppletOpenAutohomeRelationService appletOpenAutohomeRelationService;

//    @PostConstruct
//    public void init() {
//        while (true){
//            try{
//                handle();
//                Thread.sleep(5000l);
//            }
//            catch (Exception ex){
//                log.error("job error", ex);
//            }
//        }
//    }
    //执行次数
    private static Integer  pageSize = 50;
    @XxlJob("AppletDoTaskJob")
    public ReturnT<String> handle() {
        //1 查询applet_do_task_log中数据（30天内 ,status==0）
        String startDate = DateHelper.serialize(DateHelper.beforeNDaysDate(30));
        String endDate = DateHelper.serialize(new Date());

        JobLog.info(String.format("AppletDoTaskJob getUnexecutedList startDate:%s,endDate:%s",startDate, endDate));

        //总执行数量
        AtomicInteger executeTotal = new AtomicInteger();
        AtomicInteger successTotal = new AtomicInteger();
        AtomicInteger failTotal = new AtomicInteger();

        List<AppletDoTaskLog> doTaskLogs ;
        Map<String, Integer> userIdMap ;
        Long lastId = 0L;
        do {
            //取30天内数据，每次取COUNT条记录
            doTaskLogs = appletDoTaskLogService.getUnexecutedList(startDate ,endDate,pageSize,lastId);
            if (CollectionUtils.isNotEmpty(doTaskLogs)) {
                JobLog.info(String.format("AppletDoTaskJob getUnexecutedList size:%s pageSize:%s lastId:%s",doTaskLogs.size(),pageSize,lastId));

                lastId = doTaskLogs.get(doTaskLogs.size()-1).getId();
                //过滤补全userid的记录
                //取第一个userId id正序 key:openId value:userId
                List<String> openIdList = doTaskLogs.stream().map(AppletDoTaskLog::getOpenid).collect(Collectors.toList());
                userIdMap = appletOpenAutohomeRelationService.getUserIdByOpenIds(openIdList,AppletOpenAutohomeRelation.Platform.WECHAT.platformType());

                if(userIdMap.isEmpty()){
                    JobLog.info(String.format("AppletDoTaskJob 待执行数据: %s, 可执行数据: 0",userIdMap.size()));
                    continue;
                };

                //3、重新完成任务，并更新applet_do_task_log表
                AtomicInteger success = new AtomicInteger();
                AtomicInteger fail = new AtomicInteger();

                for (int i = 0; i < doTaskLogs.size(); i++) {
                    AppletDoTaskLog info = doTaskLogs.get(i);
                    if (userIdMap.containsKey(info.getOpenid())) {
                        info.setUserId(userIdMap.get(info.getOpenid()).toString());
                        doTask(info);
                        if(info.getStatus() == AppletDoTaskLog.AppletDoTaskStatusEnum.SUCCESS.status()){
                            success.getAndIncrement();
                        }
                        else{
                            fail.getAndIncrement();
                        }
                    }else {
                        fail.getAndIncrement();
                        log.info("AppletDoTaskJob doTask userId is null, openid:{}",info.getOpenid());
                    }
                }

                JobLog.info(String.format("AppletDoTaskJob 本轮--可执行数据: %s, 成功数: %s, 失败数: %s",doTaskLogs.size(), success.get(), fail.get()));
                executeTotal.addAndGet(doTaskLogs.size());
                successTotal.addAndGet(success.get());
                failTotal.addAndGet(fail.get());

            }else {
                return ReturnT.SUCCESS;
            }
        }while (CollectionUtils.isNotEmpty(doTaskLogs) && doTaskLogs.size() >= pageSize );

        JobLog.info(String.format("AppletDoTaskJob end 总执行--> 可执行数据: %s, 成功数: %s, 失败数: %s",executeTotal.get(), successTotal.get(), failTotal.get()));
        JobLog.info("AppletDoTaskJob end.....");
        return ReturnT.SUCCESS;

    }


    private AppletDoTaskLog doTask(AppletDoTaskLog doTaskLog) {
        BaseModel ret =new BaseModel();

        //请求参数
        CLueDoJobDTO requestData = JacksonHelper.deserialize(doTaskLog.getRequestData(), CLueDoJobDTO.class);
        String userId = doTaskLog.getUserId();
        String code = doTaskLog.getCode();
        try {
            requestData.setUserId(Integer.parseInt(userId));
            log.info("AppletDoTaskJob doTask start userid {} , code {} , param:{}",userId, doTaskLog.getCode(), requestData);
            JobLog.info(String.format("AppletDoTaskJob doTask start userid %s , code %s , param:%s",userId, doTaskLog.getCode(), requestData));
            Map<String, String> params = new HashMap<>();
            params.put("code", code);
            params.put("userId", userId);
            params.put("deviceid", requestData.getDeviceid());
            params.put("getReward",String.valueOf(requestData.getGetReward()));
            params.put("ip",requestData.getIp());
            params.put("platform",requestData.getPlatform());
            params.put("refer",requestData.getRefer());
            params.put("reqtime",requestData.getReqtime());
            params.put("requestId",requestData.getRequestId());
            params.put("riskCode",String.valueOf(requestData.getRiskCode()));
            params.put("appversion",requestData.getAppversion());
            params.put("ua",requestData.getUa());
            params.put("_cache","clean");
            if(requestData.getBusiMap() != null){
                params.put("busiMap", JacksonHelper.serialize(requestData.getBusiMap()));
            }

            String tmpRet = HttpClientUtils.postParameters(doTaskLog.getRequestUrl(), params, 2000, 2000);
            if (StringUtils.isEmpty(tmpRet)) {
                log.error("AppletDoTaskJob doJob error userid {} , code {} , param:{}",userId, doTaskLog.getCode(), doTaskLog.getRequestData());
                JobLog.info(String.format("AppletDoTaskJob doJob error userid %s , code %s , param:%s",userId, doTaskLog.getCode(), doTaskLog.getRequestData()));
                doTaskLog.setStatus(AppletDoTaskLog.AppletDoTaskStatusEnum.FAIL.status());
                doTaskLog.setResponseCode(tmpRet);
                return doTaskLog;
            }
            ret = JacksonHelper.deserialize(tmpRet, new TypeReference<BaseModel<DoJobResultDTO>>() {
            });
            if (ret != null && ret.getReturncode() == 0) {
                doTaskLog.setStatus(AppletDoTaskLog.AppletDoTaskStatusEnum.SUCCESS.status());
                log.info("AppletDoTaskJob doJob success userid {} , code {} , param:{}， result:{}", userId,code, doTaskLog.getRequestData(), JacksonHelper.serialize(ret));
                JobLog.info(String.format("AppletDoTaskJob doJob success userid %s , code {} , param:{}， result:{}", userId,code, doTaskLog.getRequestData(), JacksonHelper.serialize(ret)));
            } else {
                log.warn("AppletDoTaskJob doJob warn userid {} , code {} , param:{}， result:{}",userId, code, doTaskLog.getRequestData(), JacksonHelper.serialize(ret));
                JobLog.warn(String.format("AppletDoTaskJob doJob warn userid {} , code {} , param:{}， result:{}",userId, code, doTaskLog.getRequestData(), JacksonHelper.serialize(ret)));
                doTaskLog.setStatus(AppletDoTaskLog.AppletDoTaskStatusEnum.FAIL.status());
            }
            return doTaskLog;
        } catch (Exception ex) {
            log.error("AppletDoTaskJob error. userid {} , code {} , param:{}, ex:{}", userId, code,doTaskLog.getRequestData(), ex);
            JobLog.error(String.format("AppletDoTaskJob error. userid %s , code %s , param:%s, ex:{}", userId, code,doTaskLog.getRequestData()), ex);
            doTaskLog.setStatus(AppletDoTaskLog.AppletDoTaskStatusEnum.FAIL.status());
            return doTaskLog;
        }finally {
            //更新
            doTaskLog.setType(AppletDoTaskLog.AppletDoTaskTypeEnum.ASYNC_DO_TASK.type());
            doTaskLog.setResponseStatus(ret.getReturncode() == 0 ? 200 : 500);
            doTaskLog.setResponseCode(ret.getReturncode().toString());
            doTaskLog.setResponseData(JacksonHelper.serialize(ret));
            doTaskLog.setRequestData(JacksonHelper.serialize(requestData));
            doTaskLog.setModifiedSTime(new Date());
            appletDoTaskLogService.updateByPrimaryKey(doTaskLog);
            return doTaskLog;
        }

    }


}
