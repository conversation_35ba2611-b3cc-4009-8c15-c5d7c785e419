package com.autohome.applet.task.job;

import com.alipay.api.FileItem;
import com.alipay.api.response.AlipayOpenFileUploadResponse;
import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentPublishResponse;
import com.alipay.api.response.AlipaySocialBaseLifeprodStandardcontentPublishResponse;
import com.autohome.applet.model.dto.maindata.MainDataArticleDataDto;
import com.autohome.applet.service.article.MaindataArticleService;
import com.autohome.applet.service.zfb.ZFBPushInfoService;
import com.autohome.applet.service.zfb.impl.Mp4DirectTransferService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

/**
 * 向支付宝推送之家资讯job
 * 需求wiki：https://doc.autohome.com.cn/docapi/page/share/share_17GwxSPPA7U
 */
@Slf4j
@Component
public class ZFBPushInfoJob {

    @Autowired
    private MaindataArticleService maindataArticleService;
    @Autowired
    private ZFBPushInfoService zfbPushInfoService;
    @Autowired
    private Mp4DirectTransferService mp4DirectTransferService;

    @PostConstruct
    public void init() {
//        pushLongContent();
        pushVideo();
    }



    /**
     * 长图文
     */
    public void pushLongContent() {
        //1、获取之家咨询数据
        //todo 查询时间
        //todo 数据入库
        MainDataArticleDataDto cmsArticleInfo = maindataArticleService.getCmsArticleInfo(1, 1, 1, "2021-01-01 00:00:00", "2021-05-01 00:00:00");

        //2、上报素材（封面图）
        //todo 没有图片情况
        cmsArticleInfo.getItems().forEach(item -> {
            String firstImageUrl = getFirstImageUrl(item.getContent());
            log.info("封面图 firstImageUrl:{}", firstImageUrl);

            //临时保存封面图
            String tempFilePath = downloadImage(firstImageUrl);
            log.info("临时保存封面图 tempFilePath:{}", tempFilePath);
            if (tempFilePath != null) {
                try {
                    FileItem fileContent = new FileItem(tempFilePath);
                    AlipayOpenFileUploadResponse fileUploadRes = zfbPushInfoService.uploadMaterial(fileContent);
                    String fileId = fileUploadRes.getFileId();
                    item.setFileId(fileId);
                    // 推送内容
                    AlipaySocialBaseLifeprodStandardcontentPublishResponse publishResponse = zfbPushInfoService.pushLongContent(item);

                }catch (Exception e) {
                    log.error("上传素材失败 e:{}", e);
                }finally {
                    // 删除临时文件
                    new File(tempFilePath).delete();
                    log.info("删除临时文件 tempFilePath:{}", tempFilePath);
                }
            }
        });
    }


    /**
     * 视频
     */
    public void pushVideo() {
        //1、获取之家咨询数据

        //2、下载视频
        // String url = "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4";
        String url = "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4";
//        String videoFileName = downloadVideo(url);
        String videoFileName = "C:\\Users\\<USER>\\Desktop\\normal_video.mp4";
        mp4DirectTransferService.transferMp4Directly("","");


        log.info("临时保存视频成功 videoFileName:{}", videoFileName);
        if (videoFileName != null) {
            try {
                // 上报素材
                FileItem fileContent = new FileItem(videoFileName);
                AlipayOpenFileUploadResponse fileUploadRes = zfbPushInfoService.uploadMaterial(fileContent);
                String fileId = fileUploadRes.getFileId();
                MainDataArticleDataDto.ArticleData articleData = new MainDataArticleDataDto.ArticleData();
                articleData.setFileId(fileId);
                AlipaySocialBaseContentlibStandardcontentPublishResponse publishResponse = zfbPushInfoService.pushContent(articleData);
                log.info("ZFBPushInfoService.pushVideo 上报素材-->调用结果" ,publishResponse.getBody());
            }catch (Exception e) {
                log.error("上传视频失败 e:{}", e);
            }finally {
                // 删除临时文件
//                new File(videoFileName).delete();
                log.info("删除临时文件 tempFilePath:{}", videoFileName);
            }
        }

    }


    /**
     * 获取文章内容中的第一张图片
     * @param html
     * @return
     */
    public static String getFirstImageUrl(String html) {
        try {
            Document doc = Jsoup.parse(html);
            Element firstImg = doc.select("img").first();
            return firstImg != null ? firstImg.attr("src") : null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 获取网络图片的base64
     * @param imageUrl
     * @return
     * @throws Exception
     */
    public static String downloadImage(String imageUrl)  {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);

            // 创建临时文件
            String tempFile = System.currentTimeMillis() + ".jpg";
            try (InputStream in = conn.getInputStream();
                 FileOutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
            return tempFile;
        }catch (Exception e) {
            log.error("下载文件失败", e);
        }
        return null;
    }

    /**
     * 获取网络视频base64
     */
    public static String downloadVideo(String videoUrl) {
        HttpURLConnection conn = null;
        InputStream in = null;
        RandomAccessFile out = null;
        try {
            URL url = new URL(videoUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10000);  // 10秒连接超时
            conn.setReadTimeout(60000);     // 60秒读取超时

            // 检查响应状态
            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("服务器响应码：" + responseCode);
            }

            // 获取文件信息
            String contentType = conn.getContentType();
            long fileSize = conn.getContentLengthLong();
            String fileName = generateFileName(videoUrl, contentType);

            // 创建临时文件
            File tempFile = new File(System.getProperty("java.io.tmpdir"), fileName);
            out = new RandomAccessFile(tempFile, "rw");
            out.setLength(fileSize);  // 预分配文件空间

            // 分块下载
            in = conn.getInputStream();
            byte[] buffer = new byte[8192];  // 8KB缓冲区
            int bytesRead;
            long totalRead = 0;
            int lastProgress = 0;

            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                totalRead += bytesRead;

                // 显示下载进度（可选）
                if (fileSize > 0) {
                    int progress = (int) (totalRead * 100 / fileSize);
                    if (progress != lastProgress) {
                        lastProgress = progress;
                        System.out.printf("下载进度: %d%%%n", progress);
                    }
                }
            }

//            return tempFile.getAbsolutePath();
            return fileName;

        } catch (IOException e) {
            System.err.println("下载失败：" + e.getMessage());
            return null;
        } finally {
            // 安全关闭资源
            closeQuietly(in);
            closeQuietly(out);
            disconnectQuietly(conn);
        }
    }

    private static   String generateFileName(String url, String contentType) {
        String extension = "";

        // 优先从Content-Type获取扩展名
        if (contentType != null) {
            switch (contentType.toLowerCase()) {
                case "video/mp4":       extension = ".mp4"; break;
                case "video/x-msvideo": extension = ".avi"; break;
                case "video/quicktime": extension = ".mov"; break;
                case "video/x-matroska":extension = ".mkv"; break;
                case "video/webm":      extension = ".webm"; break;
            }
        }

        // 从URL路径提取扩展名
        if (extension.isEmpty()) {
            int lastDot = url.lastIndexOf('.');
            if (lastDot != -1) {
                extension = url.substring(lastDot);
            }
        }

        // 生成唯一文件名
        String uniqueName = UUID.randomUUID().toString() + "_" + System.currentTimeMillis();
        return (extension.isEmpty() ? uniqueName : uniqueName + extension);
    }

    private static  void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {

            }
        }
    }

    private static  void disconnectQuietly(HttpURLConnection conn) {
        if (conn != null) {
            conn.disconnect();
        }
    }

}
