package com.autohome.applet.task.job;

import com.alipay.api.FileItem;
import com.alipay.api.response.AlipayOpenFileUploadResponse;
import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentPublishResponse;
import com.alipay.api.response.AlipaySocialBaseLifeprodStandardcontentPublishResponse;
import com.autohome.applet.model.dto.maindata.MainDataArticleDataDto;
import com.autohome.applet.service.article.MaindataArticleService;
import com.autohome.applet.service.zfb.ZFBPushInfoService;
import com.autohome.applet.service.zfb.impl.Mp4DirectTransferService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

/**
 * 向支付宝推送之家资讯job
 * 需求wiki：https://doc.autohome.com.cn/docapi/page/share/share_17GwxSPPA7U
 */
@Slf4j
@Component
public class ZFBPushInfoJob {

    @Autowired
    private MaindataArticleService maindataArticleService;
    @Autowired
    private ZFBPushInfoService zfbPushInfoService;
    @Autowired
    private Mp4DirectTransferService mp4DirectTransferService;

    @PostConstruct
    public void init() {
//        pushLongContent();
//        pushVideo();

        // 测试新的文件不落地视频上传方法（注释掉，避免每次启动都执行）
//        String testVideoUrl = "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4";
//        pushVideoDirectly(testVideoUrl);

        // 测试真正的流式上传方法（注释掉，避免每次启动都执行）
//        pushVideoStreamDirectly(testVideoUrl);
    }

    /**
     * 示例：批量处理视频上传
     * 可以根据实际需求调用此方法
     */
    public void batchPushVideos() {
        // 示例视频URL列表
        String[] videoUrls = {
            "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4",
            // 可以添加更多视频URL
        };

        for (String videoUrl : videoUrls) {
            try {
                log.info("开始处理视频: {}", videoUrl);
                pushVideoDirectly(videoUrl);

                // 添加延迟，避免请求过于频繁
                Thread.sleep(2000);
            } catch (Exception e) {
                log.error("处理视频失败: {}, error: {}", videoUrl, e.getMessage(), e);
            }
        }
    }

    /**
     * 测试方法：验证文件名提取功能
     */
    public void testExtractFileName() {
        String[] testUrls = {
            "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4",
            "https://example.com/video.avi",
            "https://example.com/path/without/extension",
            "https://example.com/"
        };

        for (String url : testUrls) {
            String fileName = extractFileNameFromUrl(url);
            log.info("URL: {} -> 文件名: {}", url, fileName);
        }
    }



    /**
     * 长图文
     */
    public void pushLongContent() {
        //1、获取之家咨询数据
        //todo 查询时间
        //todo 数据入库
        MainDataArticleDataDto cmsArticleInfo = maindataArticleService.getCmsArticleInfo(1, 1, 1, "2021-01-01 00:00:00", "2021-05-01 00:00:00");

        //2、上报素材（封面图）
        //todo 没有图片情况
        cmsArticleInfo.getItems().forEach(item -> {
            String firstImageUrl = getFirstImageUrl(item.getContent());
            log.info("封面图 firstImageUrl:{}", firstImageUrl);

            //临时保存封面图
            String tempFilePath = downloadImage(firstImageUrl);
            log.info("临时保存封面图 tempFilePath:{}", tempFilePath);
            if (tempFilePath != null) {
                try {
                    FileItem fileContent = new FileItem(tempFilePath);
                    AlipayOpenFileUploadResponse fileUploadRes = zfbPushInfoService.uploadMaterial(fileContent);
                    String fileId = fileUploadRes.getFileId();
                    item.setFileId(fileId);
                    // 推送内容
                    AlipaySocialBaseLifeprodStandardcontentPublishResponse publishResponse = zfbPushInfoService.pushLongContent(item);

                }catch (Exception e) {
                    log.error("上传素材失败 e:{}", e);
                }finally {
                    // 删除临时文件
                    new File(tempFilePath).delete();
                    log.info("删除临时文件 tempFilePath:{}", tempFilePath);
                }
            }
        });
    }


    /**
     * 视频
     */
    public void pushVideo() {
        //1、获取之家咨询数据

        //2、下载视频
        // String url = "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4";
        String url = "http://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4";
//        String videoFileName = downloadVideo(url);
        String videoFileName = "C:\\Users\\<USER>\\Desktop\\normal_video.mp4";
        mp4DirectTransferService.transferMp4Directly("","");


        log.info("临时保存视频成功 videoFileName:{}", videoFileName);
        if (videoFileName != null) {
            try {
                // 上报素材
                FileItem fileContent = new FileItem(videoFileName);
                AlipayOpenFileUploadResponse fileUploadRes = zfbPushInfoService.uploadMaterial(fileContent);
                String fileId = fileUploadRes.getFileId();
                MainDataArticleDataDto.ArticleData articleData = new MainDataArticleDataDto.ArticleData();
                articleData.setFileId(fileId);
                AlipaySocialBaseContentlibStandardcontentPublishResponse publishResponse = zfbPushInfoService.pushContent(articleData);
                log.info("ZFBPushInfoService.pushVideo 上报素材-->调用结果" ,publishResponse.getBody());
            }catch (Exception e) {
                log.error("上传视频失败 e:{}", e);
            }finally {
                // 删除临时文件
//                new File(videoFileName).delete();
                log.info("删除临时文件 tempFilePath:{}", videoFileName);
            }
        }

    }

    /**
     * 视频上传（文件不落地版本）
     * 从网络URL直接下载视频到内存，然后上传到支付宝素材接口
     * @param videoUrl 视频的网络URL
     */
    public void pushVideoDirectly(String videoUrl) {
        log.info("开始处理视频上传（文件不落地）, videoUrl: {}", videoUrl);

        try {
            // 1. 下载视频到内存
            byte[] videoData = downloadVideoToMemory(videoUrl);
            if (videoData == null || videoData.length == 0) {
                log.error("视频下载失败或文件为空, videoUrl: {}", videoUrl);
                return;
            }

            log.info("视频下载成功，文件大小: {} bytes", videoData.length);

            // 2. 从URL提取文件名
            String fileName = extractFileNameFromUrl(videoUrl);
            log.info("提取的文件名: {}", fileName);

            // 3. 创建FileItem对象（使用字节数组构造函数，避免文件落地）
            FileItem fileContent = new FileItem(fileName, videoData);

            // 4. 上报素材到支付宝
            AlipayOpenFileUploadResponse fileUploadRes = zfbPushInfoService.uploadMaterial(fileContent);
            if (fileUploadRes == null || !fileUploadRes.isSuccess()) {
                log.error("支付宝素材上传失败, response: {}", fileUploadRes != null ? fileUploadRes.getBody() : "null");
                return;
            }

            String fileId = fileUploadRes.getFileId();
            log.info("支付宝素材上传成功, fileId: {}", fileId);

            // 5. 发布内容
            MainDataArticleDataDto.ArticleData articleData = new MainDataArticleDataDto.ArticleData();
            articleData.setFileId(fileId);
            articleData.setTitle("汽车之家视频内容"); // 可以根据需要设置标题

            AlipaySocialBaseContentlibStandardcontentPublishResponse publishResponse = zfbPushInfoService.pushContent(articleData);
            if (publishResponse != null && publishResponse.isSuccess()) {
                log.info("视频内容发布成功, response: {}", publishResponse.getBody());
            } else {
                log.error("视频内容发布失败, response: {}", publishResponse != null ? publishResponse.getBody() : "null");
            }

        } catch (Exception e) {
            log.error("视频上传过程中发生异常, videoUrl: {}, error: {}", videoUrl, e.getMessage(), e);
        }
    }

    /**
     * 从网络URL下载视频到内存
     * @param videoUrl 视频URL
     * @return 视频字节数组，失败时返回null
     */
    private byte[] downloadVideoToMemory(String videoUrl) {
        // 设置最大文件大小限制（100MB）
        final int MAX_FILE_SIZE = 100 * 1024 * 1024;
        final int BUFFER_SIZE = 8192; // 8KB缓冲区

        HttpURLConnection conn = null;
        InputStream in = null;
        ByteArrayOutputStream out = null;

        try {
            URL url = new URL(videoUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10000);  // 10秒连接超时
            conn.setReadTimeout(60000);     // 60秒读取超时

            // 检查响应状态
            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("视频下载请求失败，响应码: {}, videoUrl: {}", responseCode, videoUrl);
                return null;
            }

            // 获取文件大小
            long contentLength = conn.getContentLengthLong();
            if (contentLength > MAX_FILE_SIZE) {
                log.error("视频文件过大，大小: {} bytes, 最大限制: {} bytes", contentLength, MAX_FILE_SIZE);
                return null;
            }

            // 开始下载
            in = conn.getInputStream();
            out = new ByteArrayOutputStream();

            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            long totalRead = 0;

            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                totalRead += bytesRead;

                // 检查文件大小限制
                if (totalRead > MAX_FILE_SIZE) {
                    log.error("下载过程中文件大小超过限制，已下载: {} bytes, 最大限制: {} bytes", totalRead, MAX_FILE_SIZE);
                    return null;
                }

                // 每下载10MB打印一次进度
                if (totalRead % (10 * 1024 * 1024) == 0) {
                    log.info("视频下载进度: {} MB", totalRead / (1024 * 1024));
                }
            }

            log.info("视频下载完成，总大小: {} bytes", totalRead);
            return out.toByteArray();

        } catch (Exception e) {
            log.error("下载视频时发生异常, videoUrl: {}, error: {}", videoUrl, e.getMessage(), e);
            return null;
        } finally {
            // 关闭资源
            if (out != null) {
                try { out.close(); } catch (Exception ignored) {}
            }
            if (in != null) {
                try { in.close(); } catch (Exception ignored) {}
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    /**
     * 从URL中提取文件名
     * @param url 视频URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        try {
            // 从URL路径中提取文件名
            String path = new URL(url).getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);

            // 如果文件名为空或没有扩展名，生成一个默认名称
            if (fileName.isEmpty() || !fileName.contains(".")) {
                fileName = "video_" + System.currentTimeMillis() + ".mp4";
            }

            // 如果文件名过长，截取前面部分
            if (fileName.length() > 100) {
                String extension = "";
                int dotIndex = fileName.lastIndexOf('.');
                if (dotIndex > 0) {
                    extension = fileName.substring(dotIndex);
                }
                fileName = "video_" + System.currentTimeMillis() + extension;
            }

            return fileName;
        } catch (Exception e) {
            log.warn("从URL提取文件名失败，使用默认名称, url: {}, error: {}", url, e.getMessage());
            return "video_" + System.currentTimeMillis() + ".mp4";
        }
    }

    /**
     * 视频上传（真正的流式上传版本）
     * 从网络URL直接流式上传到支付宝，完全不使用内存缓存，真正的边下载边上传
     * @param videoUrl 视频的网络URL
     */
    public void pushVideoStreamDirectly(String videoUrl) {
        log.info("开始处理视频流式上传（真正的流式处理）, videoUrl: {}", videoUrl);

        HttpURLConnection conn = null;
        InputStream inputStream = null;

        try {
            // 1. 建立到视频URL的连接
            URL url = new URL(videoUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10000);  // 10秒连接超时
            conn.setReadTimeout(60000);     // 60秒读取超时

            // 检查响应状态
            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("视频下载请求失败，响应码: {}, videoUrl: {}", responseCode, videoUrl);
                return;
            }

            // 获取文件信息
            long contentLength = conn.getContentLengthLong();
            String fileName = extractFileNameFromUrl(videoUrl);

            log.info("开始流式上传，文件名: {}, 文件大小: {} bytes", fileName, contentLength);

            // 2. 获取输入流
            inputStream = conn.getInputStream();

            // 3. 使用流式上传方法
            AlipayOpenFileUploadResponse fileUploadRes = zfbPushInfoService.uploadMaterialStream(fileName, inputStream, contentLength);

            if (fileUploadRes == null || !fileUploadRes.isSuccess()) {
                log.error("支付宝流式素材上传失败, response: {}", fileUploadRes != null ? fileUploadRes.getBody() : "null");
                return;
            }

            String fileId = fileUploadRes.getFileId();
            log.info("支付宝流式素材上传成功, fileId: {}", fileId);

            // 4. 发布内容
            MainDataArticleDataDto.ArticleData articleData = new MainDataArticleDataDto.ArticleData();
            articleData.setFileId(fileId);
            articleData.setTitle("汽车之家视频内容（流式上传）");

            AlipaySocialBaseContentlibStandardcontentPublishResponse publishResponse = zfbPushInfoService.pushContent(articleData);
            if (publishResponse != null && publishResponse.isSuccess()) {
                log.info("视频内容发布成功, response: {}", publishResponse.getBody());
            } else {
                log.error("视频内容发布失败, response: {}", publishResponse != null ? publishResponse.getBody() : "null");
            }

        } catch (Exception e) {
            log.error("流式视频上传过程中发生异常, videoUrl: {}, error: {}", videoUrl, e.getMessage(), e);
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try { inputStream.close(); } catch (Exception ignored) {}
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
    }


    /**
     * 获取文章内容中的第一张图片
     * @param html
     * @return
     */
    public static String getFirstImageUrl(String html) {
        try {
            Document doc = Jsoup.parse(html);
            Element firstImg = doc.select("img").first();
            return firstImg != null ? firstImg.attr("src") : null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 获取网络图片的base64
     * @param imageUrl
     * @return
     * @throws Exception
     */
    public static String downloadImage(String imageUrl)  {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);

            // 创建临时文件
            String tempFile = System.currentTimeMillis() + ".jpg";
            try (InputStream in = conn.getInputStream();
                 FileOutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
            return tempFile;
        }catch (Exception e) {
            log.error("下载文件失败", e);
        }
        return null;
    }

    /**
     * 获取网络视频base64
     */
    public static String downloadVideo(String videoUrl) {
        HttpURLConnection conn = null;
        InputStream in = null;
        RandomAccessFile out = null;
        try {
            URL url = new URL(videoUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10000);  // 10秒连接超时
            conn.setReadTimeout(60000);     // 60秒读取超时

            // 检查响应状态
            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("服务器响应码：" + responseCode);
            }

            // 获取文件信息
            String contentType = conn.getContentType();
            long fileSize = conn.getContentLengthLong();
            String fileName = generateFileName(videoUrl, contentType);

            // 创建临时文件
            File tempFile = new File(System.getProperty("java.io.tmpdir"), fileName);
            out = new RandomAccessFile(tempFile, "rw");
            out.setLength(fileSize);  // 预分配文件空间

            // 分块下载
            in = conn.getInputStream();
            byte[] buffer = new byte[8192];  // 8KB缓冲区
            int bytesRead;
            long totalRead = 0;
            int lastProgress = 0;

            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                totalRead += bytesRead;

                // 显示下载进度（可选）
                if (fileSize > 0) {
                    int progress = (int) (totalRead * 100 / fileSize);
                    if (progress != lastProgress) {
                        lastProgress = progress;
                        System.out.printf("下载进度: %d%%%n", progress);
                    }
                }
            }

//            return tempFile.getAbsolutePath();
            return fileName;

        } catch (IOException e) {
            System.err.println("下载失败：" + e.getMessage());
            return null;
        } finally {
            // 安全关闭资源
            closeQuietly(in);
            closeQuietly(out);
            disconnectQuietly(conn);
        }
    }

    private static   String generateFileName(String url, String contentType) {
        String extension = "";

        // 优先从Content-Type获取扩展名
        if (contentType != null) {
            switch (contentType.toLowerCase()) {
                case "video/mp4":       extension = ".mp4"; break;
                case "video/x-msvideo": extension = ".avi"; break;
                case "video/quicktime": extension = ".mov"; break;
                case "video/x-matroska":extension = ".mkv"; break;
                case "video/webm":      extension = ".webm"; break;
            }
        }

        // 从URL路径提取扩展名
        if (extension.isEmpty()) {
            int lastDot = url.lastIndexOf('.');
            if (lastDot != -1) {
                extension = url.substring(lastDot);
            }
        }

        // 生成唯一文件名
        String uniqueName = UUID.randomUUID().toString() + "_" + System.currentTimeMillis();
        return (extension.isEmpty() ? uniqueName : uniqueName + extension);
    }

    private static  void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {

            }
        }
    }

    private static  void disconnectQuietly(HttpURLConnection conn) {
        if (conn != null) {
            conn.disconnect();
        }
    }

}
