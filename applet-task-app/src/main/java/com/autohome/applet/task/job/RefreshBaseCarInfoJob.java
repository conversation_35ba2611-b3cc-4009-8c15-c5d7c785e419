package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.car.AllBrandInfoDto;
import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RefreshBaseCarInfoJob {
    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;
    @Autowired
    private CarSeriesService carSeriesService;

//    @PostConstruct
//    void init(){
//        handleAllSeriesInfo();
//    }

    /**
     * 1.刷新全量车系（所有车系放到一个key中）
     * 2.刷新全量车系（每个车系一个key）
     * */
    @XxlJob("CacheBaseInfoAllSeriesJob")
    public ReturnT<String> handleAllSeriesInfo() {
        List<SeriesInfoDto> seriesInfoDtoList = carSeriesService.getAllSeriesInfosFromUrlNoCache();
        if(CollectionUtils.isEmpty(seriesInfoDtoList)){
            JobLog.error("cache base series info error size " + seriesInfoDtoList.size(), new Throwable());
            return ReturnT.FAIL;
        }
        else{
            JobLog.info("cache series size " + seriesInfoDtoList.size());
            //刷新全量车系（所有车系放到一个key中）
            carSeriesService.refreshAllSeriesInfos(seriesInfoDtoList);
            //刷新全量车系（每个车系一个key）
            seriesInfoDtoList.forEach(o -> {
                String key = String.format(CarSeriesService.CACHE_SERIES_INFO_KEY, o.getId());
                redisClient.set(key, JacksonHelper.serialize(o), 3, TimeUnit.DAYS);
            });
            return ReturnT.SUCCESS;
        }
    }

    /**
     * 刷新全量车系（所有车系放到一个key中）
     * */
    @XxlJob("CacheBaseInfoAllBrandJob")
    public ReturnT<String> handleAllBrandInfo() {
        List<AllBrandInfoDto.BrandInfoDto> brandInfoDtoList = carSeriesService.refreshAllBrandInfos();
        if(CollectionUtils.isEmpty(brandInfoDtoList)){
            JobLog.error("cache brand error size " + brandInfoDtoList.size(), new Throwable());
            return ReturnT.FAIL;
        }
        else{
            JobLog.info("cache brand size " + brandInfoDtoList.size());
            return ReturnT.SUCCESS;
        }
    }
}
