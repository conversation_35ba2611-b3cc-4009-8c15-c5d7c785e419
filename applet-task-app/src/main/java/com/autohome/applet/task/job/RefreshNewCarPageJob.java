package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.model.dto.newcar.NewCarFocusCardDTO;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.newcar.NewCarService;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.omg.CORBA.PRIVATE_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 新车页面相关缓存
 * 包括：焦点图，热门新车列表，新车上市列表
 *
 * */
@Slf4j
@Component
public class RefreshNewCarPageJob {

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    private NewCarService newCarService;

    private static String APPLET_NEWCAR_FOCUSCARD = "applet:newcar:focuscard";

//    @PostConstruct
//    void init(){
//        try {
//            execute();
//        }
//        catch (Exception ex){
//
//        }
//    }

    @XxlJob("RefreshNewCarFocusCardJob")
    public ReturnT<String> execute(String... params) throws Exception {
        JobLog.info("--------------------RefreshNewCarPageJob 开始运行--------------------");
        long start = System.currentTimeMillis();
        try {
            log.info("卡片 =======> 更新焦点图卡片 start");
            refreshNewCarFocusCardJob();
            log.info("卡片 =======> 更新焦点图卡片 end");

            log.info("新车大事件 =======> 更新Top20车系内容混合数据表 start");
            refreshNewCarEventJob();
            log.info("新车大事件 =======> 更新Top20车系内容混合数据表 end");

            //log.info("北京车展 =======> 北京车展-车系精彩资讯-车展导航状态同步 start");
            //exhibitionNewsService.seriesNewsNaviDataSyng();
            //log.info("北京车展 =======> 北京车展-车系精彩资讯-车展导航状态同步 end");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        long end = System.currentTimeMillis();
        JobLog.info("RefreshNewCarPageJob 耗时:" + (end - start) / 1000 + "秒");
        return ReturnT.SUCCESS;
    }

    /**
     * 1.焦点图
     * 2.刷新全量车系（每个车系一个key）
     * */

    public void refreshNewCarFocusCardJob() {
        ReturnValue<List<NewCarFocusCardDTO>> returnValue = newCarService.getFocusCard();
        if(returnValue == null || returnValue.getReturncode() != 0 || CollectionUtils.isEmpty(returnValue.getResult())){
            JobLog.error("NewCarFocusCardDTO error", new Throwable());
        }
        else{
            redisClient.set(APPLET_NEWCAR_FOCUSCARD, JacksonHelper.serialize(returnValue), 3, TimeUnit.HOURS);
            JobLog.info("NewCarFocusCardDTO success  " + JacksonHelper.serialize(returnValue));
        }
    }

    /**
     * 1.大事件数据存储到数据表
     * */

    public void refreshNewCarEventJob() {
        HashMap<String, Integer> map = newCarService.newcareventDataSync();
        if(map == null || map.size() == 0){
            JobLog.error("refreshNewCarEventJob error", new Throwable());
        }
        else{
            JobLog.info(String.format("refreshNewCarEventJob success insert: %s ,update: %s ,delete: %s", map.get("insert"), map.get("update"), map.get("delete")));
        }
    }
}
