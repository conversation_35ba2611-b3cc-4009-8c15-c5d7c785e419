package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.CarSpecDetailDto;
import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.model.dto.netcoreapi.series.Spec;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.netcoreapi.SeriesService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 车型相关信息定时任务
 */
@Slf4j
@Component
public class SpecBaseInfoJob {
    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    private CarSeriesService carSeriesService;
    @Autowired
    private SeriesService seriesService;
    /**
     * 根据车系获取所有车型id--多线程
     * @param allSeriesIdList
     * @return
     */
    //设置并发数量
    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private ForkJoinPool forkJoinPool = new ForkJoinPool(POOL_SIZE);

    //车型信息redis key
    private static final String SPEC_DETAIL_KEY = "applet:basecar:spec:%s";

//    @PostConstruct
//    void init(){
//        SpecDetailJob();
//    }


    @XxlJob("SpecDetailJob")
    public ReturnT<String> SpecDetailJob() {
        //开始时间
        long startTime = System.currentTimeMillis();
        JobLog.info("SpecBaseInfoJob.SpecDetailJob.handle() begin.........");
        //获取全量车系id
        List<SeriesInfoDto> allSeriesInfos = carSeriesService.getAllSeriesInfos();
        JobLog.info("allSeriesList size " + allSeriesInfos.size());

        List<Integer> allSeriesIdList = allSeriesInfos.stream().map(seriesInfoDto -> seriesInfoDto.getId()).collect(Collectors.toList());

        //所有车型信息存到redis
        getAllSpecIdToRedis(allSeriesIdList);
        long totalTime =  System.currentTimeMillis() - startTime;
        JobLog.info("SpecBaseInfoJob.SpecDetailJob.handle() end,总耗时:"+totalTime+"ms");
        return ReturnT.SUCCESS;
    }



    /**F
     * 根据车系获取所有车型id
     * @param allSeriesIdList
     * @return
     */
    private void  getAllSpecIdToRedis(List<Integer> allSeriesIdList){

        AtomicInteger specCount = new AtomicInteger(0);
        JobLog.info("SecondHandCarToBaiduJob.handle() POOL_SIZE:" + POOL_SIZE+",allSeriesIdList size="+allSeriesIdList.size());
        //开启多线程
        forkJoinPool.submit(()->{
            allSeriesIdList.parallelStream().forEach(seriesId->{
                try {
                    //异步查询车型详情
                    CompletableFuture<ReturnValue<Spec>> specDetailFuture = seriesService.spec_detailbyseriesIdone(seriesId);
                    CompletableFuture.allOf(specDetailFuture);
                    ReturnValue<Spec> specReturnValue = specDetailFuture.get();
                    if (specReturnValue != null && specReturnValue.getReturncode() == 0  && specReturnValue.getResult() != null) {
                        //根据车型id 获取车型信息
                        specReturnValue.getResult().getSpecitems().forEach(specItem -> {
                            CarSpecDetailDto carSpecDetailDtos = getCarSpecDetailDtos(specItem.getId());
                            String key = String.format(SPEC_DETAIL_KEY,specItem.getId());
                            redisClient.set(key, JacksonHelper.serialize(carSpecDetailDtos), 3, TimeUnit.DAYS);
                            // 车型计数
                            specCount.incrementAndGet();
                        });
                    }else {
                        JobLog.warn("SecondHandCarToBaiduJob.getAllSpecId :error:seriesId:"+seriesId+",specReturnValue="+specReturnValue);
                    }
                } catch (Exception ex) {
                    JobLog.info("SecondHandCarToBaiduJob.getAllSpecId :error:seriesId:"+seriesId+",ex:"+ ex);
                }
            });
        }).join();
        log.info("SpecBaseInfoJob.SpecDetailJob.handle() end,车系共计："+allSeriesIdList.size()+",车型共计:"+specCount.get());
    }


    private CarSpecDetailDto getCarSpecDetailDtos(Integer specId){
        String url = "http://car.app.corpautohome.com/v2/getComponentValue";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "applet");
        param.put("component", "specDetail");
        param.put("specId", specId);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance(1000).httpGet(url, param);
        ReturnValue<CarSpecDetailDto> returnValue = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<CarSpecDetailDto>>() {
        });
        return returnValue == null?null:returnValue.getResult();
    }


}
