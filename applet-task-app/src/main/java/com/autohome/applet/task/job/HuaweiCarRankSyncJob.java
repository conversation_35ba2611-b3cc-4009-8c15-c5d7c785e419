package com.autohome.applet.task.job;

import com.alibaba.fastjson.JSONObject;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.util.*;
import com.autonews.comm.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class HuaweiCarRankSyncJob {

    private static final String APP_ID = "bc43c92d4cc141b9be97c596ae708045";
    private static final String APP_SECRET = "BiPIw8kemqtXrL+H7fIgQsNEj+8S8aNvAqszOiq2XT6JLXcCtyJmgDMaqP/tlv4=";
    private static final String APP_ACCESS_SOURCE = "autohome";
    private static final String APP_CHANNEL = "search";

//    @Scheduled(initialDelay = 5, fixedRate = 864000000)
    @XxlJob("HuaweiCarRankSyncJob")
    public ReturnT<String> handle() {
        JobLog.info("HuaweiCarRankSyncJob.handle()");
        return pushRank(LocalDate.now().minusMonths(1));
    }

    public List<Series> getRank(String month) {
        Map<String, Object> fields = new HashMap<>();
        fields.put("maxPrice", "90000000");
        fields.put("minPrice", "0");
        fields.put("month", month);
        fields.put("levels", "1,2,3,4,5,6,7,8,11,12,13,14,15,16,17,18,19,20");
        fields.put("flag", "true");
        fields.put("isNewenergy", "0");
        fields.put("_appid", "car");
        fields.put("manutypes", "");
        HttpHelper.HttpResult apiResult = HttpHelper.getInstance().httpGet(ApiUtil.SERIES_SALE_RANK_INNER, fields);
        if (apiResult.getBody() == null) {
            JobLog.warn("调用榜单数据结果失败");
            return null;
        }
        ReturnValue<RankData> deserialize = JacksonHelper.deserialize(apiResult.getBody(), new TypeReference<ReturnValue<RankData>>() {
        });
        if (deserialize != null && deserialize.getReturncode() == 0 && deserialize.getResult() != null) {
            return deserialize.getResult().getData();
        }
        return null;
    }

    private int retryCount = 0;

    // 推送传入日期所在月份的排行榜数据给华为负一屏榜单
    private ReturnT<String> pushRank(LocalDate now) {
        String right = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String left = now.minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<Series> leftRank = getRank(left);
        List<Series> rightRank = getRank(right);
        if (leftRank == null || rightRank == null) {
            if (retryCount++ > 5) {
                return ReturnT.FAIL;
            }
            return pushRank(now.minusMonths(1));
        }

        Map<String, Series> leftRankMap = leftRank.stream().collect(Collectors.toMap(item -> item.seriesId, item -> item));

        List<HotWordTemplate> list = new ArrayList<>();
        for (int i = 0; i < rightRank.size(); i++) {
            if (i >= 50) {
                break;
            }
            Series item = rightRank.get(i);
            String seriesName = item.getSeriesName();
            String seriesId = item.getSeriesId();
            String logo = item.getLogo();
            int saleCnt = item.getSaleCnt();
            String seriesprice = item.getSeriesPrice();

            HotWordTemplate hotWord = new HotWordTemplate();
            hotWord.setCacheDuration(24 * 3600 * 1000);
            hotWord.setWordId(Md5.md5(seriesId));
            hotWord.setName(seriesName);
            hotWord.setImageInfo(new ImageInfo(logo));

            // 跳转链接
            List<InteractionEx> actionInfo = new ArrayList<>();
            InteractionEx ex = new InteractionEx();
            ex.quickApp = new JSONObject();
            ex.quickApp.put("url", "hap://app/com.autohome.quickapp/PageTransfer?url=PageCarSeries%3Fseriesid%3D%24%7B"
                    + SecurityKit.encrypt(seriesId) + "%7D%26auto_open_from%3Dg_search_hw231011");
//            ex.quickApp.put("url", UriBuildHelper.QuickApp.buildQuickAppSeriesPage(seriesId, "hw_230420"));
            ex.quickApp.put("minPlatformVersion", "1070");
            ex.quickApp.put("minVersion", "8001");
            try {
                ex.webUrl = "https://quickapp-h5.autohome.com.cn/final-price?needDecrypt=1&keyid=241&linkid=4%7C127%7C1783%7C21306%7C205684%7C304835&seriesId="
                        + URLEncoder.encode("${" + SecurityKit.encrypt(seriesId) + "}", "utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            actionInfo.add(ex);
            hotWord.setActionInfo(actionInfo);

            ArrayList<AuxiliaryTextInfo> auxiliaryInfo1 = new ArrayList<>();
            auxiliaryInfo1.add(new AuxiliaryTextInfo(seriesprice, "YELLOW"));
            hotWord.setAuxiliaryInfo(auxiliaryInfo1);

            NumericalInfo numberInfo = new NumericalInfo();
            numberInfo.setNumerical(saleCnt);
            numberInfo.setUnit("");
            ArrayList<AuxiliaryTextInfo> auxiliaryInfo2 = new ArrayList<>();
            auxiliaryInfo2.add(new AuxiliaryTextInfo("销量", ""));
            numberInfo.setAuxiliaryInfo(auxiliaryInfo2);
            hotWord.setNumberInfo(numberInfo);

            Series series = leftRankMap.get(seriesId);
            if (series == null) {
                hotWord.setTrendType(HotWordTemplate.TREND_TYPE_NON);
            } else {
                if (item.getRn() > series.getRn()) {
                    hotWord.setTrendType(HotWordTemplate.TREND_TYPE_UP);
                } else if (item.getRn() < series.getRn()){
                    hotWord.setTrendType(HotWordTemplate.TREND_TYPE_DOWN);
                } else {
                    hotWord.setTrendType(HotWordTemplate.TREND_TYPE_NON);
                }
            }

            ex = new InteractionEx();
            ex.quickApp = new JSONObject();
            ex.quickApp.put("url", "hap://app/com.autohome.quickapp/PageTransfer?url=PageFinalPrice%3Fseriesid%3D%24%7B" +
                    SecurityKit.encrypt(seriesId) + "%7D%26auto_open_from%3Dg_search_hw231011");
//            ex.quickApp.put("url", UriBuildHelper.QuickApp.buildQuickAppPageOrderDeal(seriesId));
            ex.quickApp.put("minPlatformVersion", "1070");
            ex.quickApp.put("minVersion", "8001");
            try {
                ex.webUrl = "https://quickapp-h5.autohome.com.cn/final-price?needDecrypt=1&keyid=241&linkid=4%7C127%7C1783%7C21306%7C205684%7C304835&seriesId="
                        + URLEncoder.encode("${" + SecurityKit.encrypt(seriesId) + "}", "utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            hotWord.setLinkText(new LinkText("查成交价", ex));

            list.add(hotWord);
        }

        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("HotWordTemplate", list);
        HotMoreInfo hotMoreInfo = new HotMoreInfo();
        hotMoreInfo.setRpk_more_link("hap://app/com.autohome.quickapp/PackageDetails/PageRanking?auto_open_from=g_search_hw231011");
//        hotMoreInfo.setRpk_more_link("hap://app/com.autohome.quickapp/PackageDetails/PageRanking?auto_open_from=hw_230420");
        hotMoreInfo.setWeb_more_link("https://mina.autohome.com.cn/pa-bank/#/toolpackage/pages/car-ranking/index?type=sale&channel=hw_230420&auto_open_from=hw_230420");
        bodyMap.put("HotMoreInfo", hotMoreInfo);

        String requestBody = JsonUtils.toString(bodyMap);
        JobLog.info(requestBody);

        long timestamp = System.currentTimeMillis();
        String dataToSign = "app_id=" + APP_ID+ "&" + "timestamp=" + timestamp + "&" + "payload=" + requestBody;

        Map<String, Object> headers = new HashMap<>();
        headers.put("X-Request-Id", "BLJredCslUoROyMk3KrCmN3v1NpKcu1x"); // 32位随机字符串，用来作日志跟踪ID，建议带上
        headers.put("timestamp", timestamp);
        headers.put("appId", APP_ID); // 接入分配的appId,待分配
        headers.put("accessSource", APP_ACCESS_SOURCE); // 接入应用名称,待分配
        headers.put("channel", APP_CHANNEL); // 接入渠道,待分配
        headers.put("sign", sign(dataToSign));
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson("https://wisesearchcontent-drcn.ai.dbankcloud.com/search/v2/ws/hotword", requestBody, headers);
        int statusCode = httpResult.getStatusCode();
        String body = httpResult.getBody();
        JobLog.info("HuaweiCarRankSyncJob:statusCode(" + statusCode + ") body:" + body);
        return ReturnT.SUCCESS;
    }

    public String sign(String dataToSign) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(APP_SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKey);
            byte[] bytes = mac.doFinal(dataToSign.getBytes(StandardCharsets.UTF_8));
            return new String(Base64Utils.encode(bytes), StandardCharsets.UTF_8);
//            return Hex.encodeHexString(bytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            JobLog.error("Failed to generate pushDataSign", e);
        }
        return "sign";
    }

    @Data
    public static class HotWordTemplate {

        public static final String TREND_TYPE_UP = "1";
        public static final String TREND_TYPE_NON = "2";
        public static final String TREND_TYPE_DOWN = "3";

        private String wordId; // 唯一性ID
        private String name; // 热词名称
        private List<InteractionEx> actionInfo; // 热词跳转链接
        private long cacheDuration; // 该条热词失效时间，单位为毫秒，如果超过失效时间则端侧不展示
        private List<String> clickUrl; // 点击事件上报URL，端侧通过Get上报
        private List<String> exposureUrl; // 曝光上报url
        private ImageInfo imageInfo;
        private List<AuxiliaryTextInfo> auxiliaryInfo;
        private NumericalInfo numberInfo;
        private String trendType; // 趋势类型，“1”：上升趋势；“2”:下降趋势； “3”：趋势持平
        private LinkText linkText; // 按钮跳转链接
    }

    @Data
    public static class LinkText {
        private String text;
        private InteractionEx link;

        public LinkText(String text, InteractionEx link) {
            this.text = text;
            this.link = link;
        }

        public LinkText() {
        }
    }

    @Data
    public static class InteractionEx {
//        private JSONObject deepLink;
        private JSONObject quickApp;
        private String webUrl = "";
    }

    @Data
    public static class AuxiliaryTextInfo {
        private String text;
        private String color; // 枚举值[GRAY、YELLOW]

        public AuxiliaryTextInfo(String text, String color) {
            this.text = text;
            this.color = color;
        }

        public AuxiliaryTextInfo() {
        }
    }

    @Data
    public static class ImageInfo {
        private String url; // 图片URL

        public ImageInfo(String url) {
            this.url = url;
        }

        public ImageInfo() {
        }
    }

    @Data
    public static class NumericalInfo {
//        private IconInfo icon; // 图标，如上升、下降
        private float numerical; // 数字
        private String unit; // 单位
        private List<AuxiliaryTextInfo> auxiliaryInfo; // 支持2个
    }

//    @Data
//    public static class IconInfo {
//        // 热词的图标 0：默认；1：热；2：荐；3：新；4：推广 5：爆；6：沸 7：独家 8：首发 9：直播。
//        //         10：热度上升， 11 ：热度下降 12：置顶，13：专题 14：折线上升  15：自定义
//        private int icon;
//        private String iconUrl; // 热词的标记图标下载链接。icon=15需要提供
//    }

    @Data
    public static class HotMoreInfo {
        private String rpk_more_link; // 快应用链接
        private String web_more_link; // h5链接
    }

    @Data
    public static class RankData {
        private List<Series> data;
        private String content;
    }

    @Data
    public static class Series {

        @JsonProperty("manu_type")
        private String manuType;
        private String level;
        @JsonProperty("seriesid")
        private String seriesId;
        @JsonProperty("seriesname")
        private String seriesName;
        private String logo;
        private String month;
        @JsonProperty("salecnt")
        private int saleCnt;
        private int rn;
        @JsonProperty("minprice")
        private String minPrice;
        @JsonProperty("maxprice")
        private String maxPrice;
        @JsonProperty("seriesprice")
        private String seriesPrice;
    }
}
