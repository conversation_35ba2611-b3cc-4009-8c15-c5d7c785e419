package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.service.baidu.BaiduNotePushLogV3Service;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.util.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class PushNoteV3DataJob {

//    @Autowired
//    @Qualifier("biduNotePushToBaiduServiceImpl")
//    private ResourceFeedPushService baiduService;

    @Autowired
    private BaiduNoteUtil baiduNoteUtil;
    @Autowired
    private BaiduNotePushLogV3Service baiduNotePushLogV3Service;

//    @PostConstruct
//    public void init() {
//        JobLog.info("statr PushBaiduNoteV3  内容保存到数据库........");
//        handle();
//        JobLog.info("end PushBaiduNoteV3 内容保存到数据库........");
//    }

    /**
     * excel内容保存到数据库
     * 需求：https://doc.autohome.com.cn/docapi/page/share/share_uovwwQmvDM
     *
     * */
    @XxlJob("PushBaiduNoteV3")
    public ReturnT<String> handle() {

        //1.读取excel内容
        List<BaiduNoteUtil.BaiduNoteExcel> baiduNoteExcelList = baiduNoteUtil.readExcelWithImages("static/baidunote/汽车之家试产20篇内容-修改250609.xlsx");
//        List<BaiduNoteUtil.BaiduNoteExcel> baiduNoteExcelList = baiduNoteUtil.readExcelWithImages("static/baidunote/汽车之家试产1篇内容.xlsx");

        //2、存入数据库
        int size = baiduNotePushLogV3Service.handleBaiduNoteExcel(baiduNoteExcelList);
        JobLog.info("本次需要插入数据 "+size+" 条");

        //3、将数据推送到百度（按照exid判断，有exid修改，没有exid插入）
//        PushResourceFeedDataQuery pushResourceFeedDataQuery = new PushResourceFeedDataQuery();
//        pushResourceFeedDataQuery.setOffset(500);
//        baiduService.doPush(pushResourceFeedDataQuery);

        return ReturnT.SUCCESS;
    }

    private PushResourceFeedDataQuery getRecoverableCodes(String param) {
        if(StringUtils.isEmpty(param)){
            return new PushResourceFeedDataQuery();
        }
        try{
            return JacksonHelper.deserialize(param, PushResourceFeedDataQuery.class);
        }
        catch (Exception ex){
            XxlJobHelper.log(ex);
        }
        return null;
    }

    @Data
    static class TimeSegment {
        LocalDateTime start;
        LocalDateTime end;

        public TimeSegment(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String toString() {
            return "(" + start + " - " + end + ")";
        }
    }

}
