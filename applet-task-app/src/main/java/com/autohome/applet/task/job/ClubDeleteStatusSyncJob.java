package com.autohome.applet.task.job;

import com.autohome.applet.util.DateFormateUtil;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ClubDeleteStatusSyncJob {

    @Autowired
    @Qualifier("lightapp")
    RedisClient redisClient;

//    @Scheduled(initialDelay = 5, fixedRate = 5 * 60 * 1000)
    @XxlJob("ClubDeleteStatusSyncJob")
    public ReturnT<String> handle() {
        log.info("ClubDeleteStatusSyncJob.handle()");
        Map<String, Object> params = new HashMap<>();
        params.put("groupid", "applet_club_delete");
        params.put("filterSources", "club");
        params.put("filterTypes", "deletetopic");
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://httpactionmessage.api.lq.autohome.com.cn/get", params);
        if (httpResult.getStatusCode() == 200) {
            String body = httpResult.getBody();
            log.info("body:" + body);
            List<Item> messageList = JacksonHelper.deserialize(body, new TypeReference<List<Item>>() {
            });
            if (messageList != null) {
                Map<String, List<String>> map = new HashMap<>();
                for (Item item : messageList) {
                    Date deleteTime = new Date(item.getTimestamp() * 1000);
                    String yyyyMmDd = DateFormateUtil.getDateString("yyyy_MM_dd", deleteTime);
                    Message message = item.getMessage();
                    if (message != null) {
                        String bizId = message.getBizId();
                        List<String> list = map.computeIfAbsent(yyyyMmDd, k -> new ArrayList<>());
                        list.add(bizId);
                    }
                }

                map.forEach((k, v) -> {
                    String key = "applet:task:clubdeleteids:" + k;
                    Object o = redisClient.getValue(key);
                    String value = null;
                    if (o != null) {
                        value = o + "," + String.join(",", v);
                    } else {
                        value = String.join(",", v);
                    }
                    redisClient.setValue(key, value, 365, TimeUnit.DAYS);
                    log.info("add " + v.size() + " ids to day " + key);
                });
            }

        }
        return ReturnT.SUCCESS;
    }

    @Data
    public static class Item {
        private Message message;
        private long timestamp;
        private String version;
    }

    @Data
    public static class Message {
        private String source;
        private String type;
        private String bizId;
        private String secondBizId;
        private String data;
    }
}
