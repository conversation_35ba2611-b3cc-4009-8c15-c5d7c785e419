package com.autohome.applet.task.consumer;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.NamedThreadFactory;
import com.autohome.applet.model.dto.mp.MPEventTypeEnum;
import com.autohome.applet.model.dto.mp.MPMsgTypeEnum;
import com.autohome.applet.model.dto.mp.MPServiceEventDTO;
import com.autohome.applet.model.dto.mp.SubscribeEventModel;
import com.autohome.applet.model.dto.wechat.WxUnionIdResponse;
import com.autohome.applet.model.dto.wsmessage.SubscriptionRequest;
import com.autohome.applet.service.wxapi.WxOfficialAccountService;
import com.autohome.applet.service.wxmessage.WxServiceMessageService;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.comm.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class MPServiceEventConsumer {

    @Autowired
    private WxOfficialAccountService wxOfficialAccountService;

    @Autowired
    private WxServiceMessageService wxServiceMessageService;

    @Value("${wx.unionIdUrl}")
    private String unionIdUrl;

    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();

    /**
     * hutool类库builder模式创建线程池，指定线程池名称，拒绝策略等
     */
    private final ExecutorService executorService = ExecutorBuilder.create()
            .setCorePoolSize(POOL_SIZE)
            .setMaxPoolSize(POOL_SIZE)
            .setThreadFactory(new NamedThreadFactory("MPServiceEventConsumer", false))
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .build();

    /**
     *  消费微信服务号事件消息
     */
    @KafkaListener(id="${spring.kafka.mp.consumer.group-id}"
            ,topics = {"${spring.kafka.mp.consumer.topic}"}
            ,containerFactory = "mpContainerFactory")
    public void mpServiceEventListener(ConsumerRecord<?, String> message) {
        try {
            executorService.execute(() -> {
                MPServiceEventDTO mpServiceEventDTO = JacksonHelper.deserialize(message.value(), MPServiceEventDTO.class);
                if (mpServiceEventDTO != null && MPMsgTypeEnum.EVENT.getType().equals(mpServiceEventDTO.getMsgType()) && MPEventTypeEnum.SUBSCRIBE.getType().equals(mpServiceEventDTO.getEvent())) {
                    log.info("MPServiceEventConsumer mpServiceEventListener event:{}", mpServiceEventDTO);
                    // 1. 通过openid获取unionId
                    String unionId = getUnionId(mpServiceEventDTO.getFromUserName());
                    // 2. 调用wxServiceMessageService.subService完成订阅
                    subscribe(unionId, mpServiceEventDTO);
                }
            });
        }catch (Exception exception){
            log.error("mpServiceEventListener线程池异常",exception);
        }
    }

    /**
     * 获取unionId
     * @param openId openid
     * @return unionId
     */
    private String getUnionId(String openId) {
        String token = wxOfficialAccountService.getToken();
        WxUnionIdResponse response = HttpClientUtils.get(unionIdUrl + "?access_token=" + token + "&openid=" + openId + "&lang=zh_CN", WxUnionIdResponse.class);
        log.info("MPServiceEventConsumer getUnionId token={}, response={}", token, response);
        if (response.getSubscribe() == 0) return null;
        return response.getUnionid();
    }

    /**
     * 订阅 存储unionId
     * @param unionId unionID
     * @param mpServiceEventDTO kafka消费体
     */
    private void subscribe(String unionId, MPServiceEventDTO mpServiceEventDTO) {
        if (unionId != null) {
            String eventKey = mpServiceEventDTO.getEventKey();
            Integer bizType = 0;
            if (StringUtils.isNotBlank(eventKey) && eventKey.startsWith("qrscene_")) {
                SubscribeEventModel subscribeEventModel = JacksonHelper.deserialize(eventKey.substring(8), SubscribeEventModel.class);
                if (subscribeEventModel != null) {
                    bizType = subscribeEventModel.getSceneId();
                }
            }
            SubscriptionRequest request = new SubscriptionRequest();
            request.setBiztype(bizType);
            request.setUnionId(unionId);
            request.setFwhOpenId(mpServiceEventDTO.getFromUserName());
            wxServiceMessageService.subService(request);
        }
    }


}
