package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class PushBaiduNoteV3DataJob {
    @Autowired
    @Qualifier("biduNotePushToBaiduServiceImpl")
    private ResourceFeedPushService baiduService;

//    @PostConstruct
//    public void init() {
//        JobLog.info("statr PushBaiduNoteV3DataJob 数据库数据推送到百度........");
//        handle();
//        JobLog.info("end PushBaiduNoteV3DataJob 数据库数据推送到百度........");
//    }

    /**
     * 数据库数据推送到百度
     * @return
     */

    @XxlJob("PushBaiduNoteV3DataJob")
    public ReturnT<String> handle() {
        //、将数据推送到百度（按照exid判断，有exid修改，没有exid插入）
        JobLog.info("statr PushBaiduNoteV3DataJob 推送到百度 ........");
        PushResourceFeedDataQuery pushResourceFeedDataQuery = new PushResourceFeedDataQuery();
        pushResourceFeedDataQuery.setOffset(500);
        baiduService.doPush(pushResourceFeedDataQuery);
        JobLog.info("end PushBaiduNoteV3DataJob 推送到百度 ........");
        return ReturnT.SUCCESS;
    }
}
