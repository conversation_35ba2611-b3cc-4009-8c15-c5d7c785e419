package com.autohome.applet.task.job;
import com.autohome.applet.util.DateHelper;
import com.google.common.collect.Lists;

import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import javax.annotation.PostConstruct;
import java.util.Arrays;

@Slf4j
@Component
public class PushBaiduDataJob {

    @Autowired
    @Qualifier("resourceFeedPushToBaiduServiceImpl")
    private ResourceFeedPushService baiduService;

    @Autowired
    @Qualifier("resourceFeedPushToDouYinServiceImpl")
    private ResourceFeedPushService douyinService;

//    @PostConstruct
//    public void init() {
////        handle();
////        PushDouYinData();
////        PushResourceFeedDataQuery pushResourceFeedDataQuery = getRecoverableCodes("{\"starttime\":\"2024-07-02\",\"endtime\":\"2024-07-03\",\"offset\":0,\"count\":500,\"bizTypeList\":[1,5,12,13,33,74]}");
//    }

    /**
     * 需求：https://doc.autohome.com.cn/docapi/page/share/share_uovwwQmvDM
     *
     * */
    @XxlJob("PushBaiduData")
    public ReturnT<String> handle() {
        String param = XxlJobHelper.getJobParam();    // 获取参数
        JobLog.info("param:" + param);
        PushResourceFeedDataQuery parmModel = getRecoverableCodes(param);
        baiduService.doPush(parmModel);
        return ReturnT.SUCCESS;
    }

    /**抖音sitemap数据推送
     *  https://doc.autohome.com.cn/docapi/page/share/share_w31QY4Xsg4
     *
     * */
    @XxlJob("PushDouYinData")
    public ReturnT<String> PushDouYinData() {

        String param = XxlJobHelper.getJobParam();    // 获取参数
        JobLog.info("param:" + param);
        PushResourceFeedDataQuery parmModel = getRecoverableCodes(param);
        JobLog.info("param请求参数:" + JacksonHelper.serialize(parmModel));
        parmModel.setDaily(parmModel.getStarttime());
        douyinService.doPush(parmModel);
        return ReturnT.SUCCESS;
    }

    private PushResourceFeedDataQuery getRecoverableCodes(String param) {
        if(StringUtils.isEmpty(param)){
            return new PushResourceFeedDataQuery();
        }
        try{
            return JacksonHelper.deserialize(param, PushResourceFeedDataQuery.class);
        }
        catch (Exception ex){
            XxlJobHelper.log(ex);
        }
        return null;
    }

}
