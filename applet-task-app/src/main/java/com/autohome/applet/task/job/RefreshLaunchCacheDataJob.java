package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.BrandsTimeModel;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.NewCarLaunchHead;
import com.autohome.applet.service.javaapi.NewCarLaunchService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;


/**
 * <AUTHOR>
 * 刷新缓存（上市日历）
 */
@Slf4j
@Component
public class RefreshLaunchCacheDataJob {

    @Qualifier("newCarLaunchServiceImpl")
    @Autowired
    private NewCarLaunchService newCarLaunchService;

    @Qualifier("newCarLaunchServiceV2Impl")
    @Autowired
    private NewCarLaunchService newCarLaunchServiceV2;

//    @PostConstruct
//    public void init(){
//        RefreshNewCarLaunchCacheDataV2Job();
//    }

    /**
     * 刷新上市日历数据缓存
     * */
    @XxlJob("RefreshNewCarLaunchCacheDataJob")
    public ReturnT<String> refreshNewCarLaunchCacheDataJob() {
        //1. 先刷新head
        //2. 遍历head， 刷新所有月份和类型
        List<NewCarLaunchHead> newCarLaunchHeadList =  newCarLaunchService.getNewCarLaunchHeadNoCache();

        if(!CollectionUtils.isEmpty(newCarLaunchHeadList)){
            newCarLaunchHeadList.forEach(o -> {
                if(!CollectionUtils.isEmpty(o.getLevelidList())){
                    o.getLevelidList().forEach(ol -> {
                        List<BrandsTimeModel> brandsTimeModelList = newCarLaunchService.getNewCarLaunchListNoCache(o.getStartTime(), o.getEndTime(), ol.getLevelid(), o.getMonth());
                        StringBuilder sb = new StringBuilder();
                        sb.append(", params StartTime:")
                                .append(o.getStartTime())
                                .append(",EndTime:")
                                .append(o.getEndTime())
                                .append(",Levelid:")
                                .append(ol.getLevelid())
                                .append(",Month:")
                                .append(o.getMonth());
                        if(CollectionUtils.isEmpty(brandsTimeModelList)){
                            sb.insert(0, "RefreshNewCarLaunchCacheDataJob getNewCarLaunchListNoCache no data, 需要排查原因");
                            log.error(sb.toString());
                            JobLog.warn(sb.toString());
                        }
                        else{
                            sb.insert(0, "RefreshNewCarLaunchCacheDataJob getNewCarLaunchListNoCache success");
                            log.info(sb.toString());
                            JobLog.info(sb.toString());
                        }
                    });
                }
                else{
                    StringBuilder sb = new StringBuilder();
                    sb.append("RefreshNewCarLaunchCacheDataJob getLevelidList no data, 需要排查原因, params StartTime:")
                            .append(o.getStartTime())
                            .append(",EndTime:")
                            .append(o.getEndTime())
                            .append(",sum:")
                            .append(o.getSum())
                            .append(",Month:")
                            .append(o.getMonth());
                    log.error(sb.toString());
                    JobLog.warn(sb.toString());
                }
                StringBuilder sb = new StringBuilder();
                sb.append("RefreshNewCarLaunchCacheDataJob done , params StartTime:")
                        .append(o.getStartTime())
                        .append(",EndTime:")
                        .append(o.getEndTime())
                        .append(",sum:")
                        .append(o.getSum())
                        .append(",Month:")
                        .append(o.getMonth());
                log.info(sb.toString());
                JobLog.info(sb.toString());
            });
        }
        else{
            log.error("RefreshNewCarLaunchCacheDataJob NewCarLaunchHead no data, 需要排查原因");
            JobLog.warn("RefreshNewCarLaunchCacheDataJob NewCarLaunchHead no data, 需要排查原因");
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 刷新上市日历数据缓存
     * */
    @XxlJob("RefreshNewCarLaunchCacheDataV2Job")
    public ReturnT<String> RefreshNewCarLaunchCacheDataV2Job() {
        //1. 先刷新head
        //2. 遍历head， 刷新所有月份和类型
        List<NewCarLaunchHead> newCarLaunchHeadList =  newCarLaunchServiceV2.getNewCarLaunchHeadNoCache();

        if(!CollectionUtils.isEmpty(newCarLaunchHeadList)){
            newCarLaunchHeadList.forEach(o -> {
                if(!CollectionUtils.isEmpty(o.getLevelidList())){
                    o.getLevelidList().forEach(ol -> {
                        List<BrandsTimeModel> brandsTimeModelList = newCarLaunchServiceV2.getNewCarLaunchListNoCache(o.getStartTime(), o.getEndTime(), ol.getLevelid(), o.getMonth());
                        StringBuilder sb = new StringBuilder();
                        sb.append(", params StartTime:")
                                .append(o.getStartTime())
                                .append(",EndTime:")
                                .append(o.getEndTime())
                                .append(",Levelid:")
                                .append(ol.getLevelid())
                                .append(",Month:")
                                .append(o.getMonth());
                        if(CollectionUtils.isEmpty(brandsTimeModelList)){
                            sb.insert(0, "RefreshNewCarLaunchCacheDataJob getNewCarLaunchListNoCache no data, 需要排查原因");
                            log.error(sb.toString());
                            JobLog.warn(sb.toString());
                        }
                        else{
                            sb.insert(0, "RefreshNewCarLaunchCacheDataJob getNewCarLaunchListNoCache success");
                            log.info(sb.toString());
                            JobLog.info(sb.toString());
                        }
                    });
                }
                else{
                    StringBuilder sb = new StringBuilder();
                    sb.append("RefreshNewCarLaunchCacheDataJob getLevelidList no data, 需要排查原因, params StartTime:")
                            .append(o.getStartTime())
                            .append(",EndTime:")
                            .append(o.getEndTime())
                            .append(",sum:")
                            .append(o.getSum())
                            .append(",Month:")
                            .append(o.getMonth());
                    log.error(sb.toString());
                    JobLog.warn(sb.toString());
                }
                StringBuilder sb = new StringBuilder();
                sb.append("RefreshNewCarLaunchCacheDataJob done , params StartTime:")
                        .append(o.getStartTime())
                        .append(",EndTime:")
                        .append(o.getEndTime())
                        .append(",sum:")
                        .append(o.getSum())
                        .append(",Month:")
                        .append(o.getMonth());
                log.info(sb.toString());
                JobLog.info(sb.toString());
            });
        }
        else{
            log.error("RefreshNewCarLaunchCacheDataJob NewCarLaunchHead no data, 需要排查原因");
            JobLog.warn("RefreshNewCarLaunchCacheDataJob NewCarLaunchHead no data, 需要排查原因");
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


}