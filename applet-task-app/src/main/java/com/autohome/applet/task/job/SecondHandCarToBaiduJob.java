package com.autohome.applet.task.job;

import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo;
import com.autohome.applet.service.SecondHandCarService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 二手车数据推送百度
 * <AUTHOR>
 */
@Slf4j
@Component
public class SecondHandCarToBaiduJob {

    @Autowired
    SecondHandCarService secondHandCarService;

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

//    @PostConstruct
//    public void init() {
//        handle();
//    }

    //设置并发数量
    private final static int POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private ForkJoinPool forkJoinPool = new ForkJoinPool(POOL_SIZE);

    @XxlJob("SecondHandCarToBaiduJob")
    public ReturnT<String> handle() {
        JobLog.info("SecondHandCarToBaiduJob.handle() POOL_SIZE:" + POOL_SIZE);
        String Param = XxlJobHelper.getJobParam();
        JobLog.info("param:" + Param);
        List<String> orgIds = getRecoverableCodes(Param);

        if(!CollectionUtils.isEmpty(orgIds)){
            return handleForOrgIds(orgIds);
        }

//        redisClient.set(secondHandCarService.getUploadBaiduKey(),"1697802252893");

        //上次上报版本
        String lastVersionStr = redisClient.get(secondHandCarService.getUploadBaiduKey());
        long lastVersion = StringUtils.isEmpty(lastVersionStr) ? 0 : Long.parseLong(lastVersionStr);
        //本期版本
        long currentVersion = System.currentTimeMillis();
        //失败后设定版本(设定为大于本期版本, 方便下次继续上报)
        long errorVersion = DateHelper.beforeNDaysDate(-1).getTime();
        //查询上报时间
        List<SecondHandCarOrgInfo> secondCarToAppletOrgDtoList = secondHandCarService.getSecondCarToAppletOrgDtoList(lastVersion);
        if(CollectionUtils.isEmpty(secondCarToAppletOrgDtoList)){
            return ReturnT.SUCCESS;
        }
        JobLog.info("需同步门店数量:" + secondCarToAppletOrgDtoList.size());
        //开启多线程
        forkJoinPool.submit(()->{
            secondCarToAppletOrgDtoList.parallelStream().forEach(obj->{
                try {
                    secondHandCarService.uploadToBaidu(obj, lastVersion, currentVersion, errorVersion);
                } catch (Exception ex) {
                    log.error("SecondHandCarToBaiduJob:error:orgId:{},obj:{}", obj.getOrgId(), JacksonHelper.serialize(obj), ex);
                }
            });
        }).join();
        //修改当前版本号
        redisClient.set(secondHandCarService.getUploadBaiduKey(),currentVersion);
        log.info("SecondHandCarToBaiduJob currentVersion: {}", currentVersion);
        return ReturnT.SUCCESS;
    }

    @XxlJob("SecondHandCarAllToBaiduJob")
    public ReturnT<String> handleAll() {
        JobLog.info("SecondHandCarToBaiduJob.handle() POOL_SIZE:" + POOL_SIZE);
        String Param = XxlJobHelper.getJobParam();
        JobLog.info("param:" + Param);
        List<String> orgIds = getRecoverableCodes(Param);

        if(!CollectionUtils.isEmpty(orgIds)){
            return handleForOrgIds(orgIds);
        }

        //本期版本
        long currentVersion = System.currentTimeMillis();
        //失败后设定版本(设定为大于本期版本, 方便下次继续上报)
        long errorVersion = DateHelper.beforeNDaysDate(-1).getTime();
        //查询上报时间
        List<SecondHandCarOrgInfo> secondCarToAppletOrgDtoList = secondHandCarService.getSecondCarToAppletOrgDtoList(0L);
        if(CollectionUtils.isEmpty(secondCarToAppletOrgDtoList)){
            return ReturnT.SUCCESS;
        }
        JobLog.info("需同步门店数量:" + secondCarToAppletOrgDtoList.size());
        AtomicInteger counter = new AtomicInteger(0);
        //开启多线程
        forkJoinPool.submit(()->{
            secondCarToAppletOrgDtoList.parallelStream().forEach(obj->{
                try {
                    secondHandCarService.uploadToBaidu(obj, 0L, currentVersion, errorVersion);
                } catch (Exception ex) {
                    log.error("SecondHandCarToBaiduJob:error:orgId:{},obj:{}", obj.getOrgId(), JacksonHelper.serialize(obj), ex);
                }
                JobLog.info(String.format("SecondHandCarAllToBaiduJob 当前执行数量:%s, org:%s", counter.incrementAndGet(), JacksonHelper.serialize(obj)));
            });
        }).join();

        log.info("SecondHandCarToBaiduJob currentVersion: {}", currentVersion);
        return ReturnT.SUCCESS;
    }

    private ReturnT<String> handleForOrgIds(List<String> orgIds){
        //本期版本
        long currentVersion = System.currentTimeMillis();
        //失败后设定版本(设定为大于本期版本, 方便下次继续上报)
        long errorVersion = DateHelper.beforeNDaysDate(-1).getTime();
        //查询上报时间
        List<SecondHandCarOrgInfo> secondCarToAppletOrgDtoList = secondHandCarService.listByOrgIds(orgIds);
        if(CollectionUtils.isEmpty(secondCarToAppletOrgDtoList)){
            return ReturnT.SUCCESS;
        }
        JobLog.info("需同步门店数量:" + secondCarToAppletOrgDtoList.size());
        //开启多线程
        forkJoinPool.submit(()->{
            secondCarToAppletOrgDtoList.parallelStream().forEach(obj->{
                try {
                    secondHandCarService.uploadToBaidu(obj, 0L, currentVersion, errorVersion);
                } catch (Exception ex) {
                    log.error("SecondHandCarToBaiduJob:error:orgId:{},obj:{}", obj.getOrgId(), JacksonHelper.serialize(obj), ex);
                }
            });
        }).join();
        return ReturnT.SUCCESS;
    }

    private List<String> getRecoverableCodes(String param) {
        if(StringUtils.isEmpty(param)){
            return null;
        }
        return Arrays.stream(org.apache.commons.lang3.StringUtils.split(param, ","))
                .map(String::valueOf)
                .collect(Collectors.toList());
    }
}