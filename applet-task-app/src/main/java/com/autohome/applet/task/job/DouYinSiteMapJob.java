package com.autohome.applet.task.job;

import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class DouYinSiteMapJob {

    @Autowired
    DouYinService douYinService;
    private static final DateTimeFormatter DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 行情数据推送
     *
     * @return
     */
    @XxlJob("DouYinSiteMapHangQingJob")
    public ReturnT<String> DouYinSiteMapHangQingJob() {
        JobLog.info("DouYinSiteMapHangQingJob  begin");
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        String endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);

        String url = "http://basecar.api.corpautohome.com/aigc/api/hangqing/gethangqinglist";
        Object hangQing = douYinService.getHangQing(param, url, 1);
        JobLog.info("DouYinSiteMapHangQingJob  end" + hangQing);
        return ReturnT.SUCCESS;
    }

    /**
     * 行情删除数据推送
     *
     * @return
     */
    @XxlJob("DouYinSiteMapHangQingDelJob")
    public ReturnT<String> hangQing() {
        JobLog.info("DouYinSiteMapHangQingDelJob  begin");
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        String endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        String url = "http://basecar.api.corpautohome.com/aigc/api/hangqing/listDelArticleByTime";
        Object hangQing = douYinService.getHangQing(param, url, -1);
        JobLog.info("DouYinSiteMapHangQingDelJob  end" + hangQing);
        return ReturnT.SUCCESS;
    }

    /**
     * 问答sitemap推送
     *
     * @return
     */
    @XxlJob("DouYinSiteMapWenDaJob")
    public ReturnT<String> DouYinSiteMapWenDaJob() {
        JobLog.info("DouYinSiteMapWenDaJob  begin");
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        String endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        String url = "http://basecar.api.corpautohome.com/aigc/api/getarticlelist";
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        Object wenDa = douYinService.getWenDa(param, url, 1);
        JobLog.info("DouYinSiteMapWenDaJob  end" + wenDa);
        return ReturnT.SUCCESS;
    }

    /**
     * 问答删除推送
     *
     * @return
     */
    @XxlJob("DouYinSiteMapWenDaDelJob")
    public ReturnT<String> DouYinSiteMapWenDaDelJob() {
        JobLog.info("DouYinSiteMapWenDaDelJob  begin");
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String startTime = LocalDateTime.of(yesterday, LocalTime.MIN).format(DATETIME_FORMAT);
        String endTime = LocalDateTime.of(yesterday, LocalTime.MAX).withNano(0).format(DATETIME_FORMAT);
        String url = "http://basecar.api.corpautohome.com/aigc/api/listDelArticleByTime";
        Map<String, Object> param = new HashMap<>();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        Object wenDa = douYinService.getWenDa(param, url, -1);
        JobLog.info("DouYinSiteMapWenDaDelJob  end" + wenDa);
        return ReturnT.SUCCESS;
    }


    /**
     * 推送报告，推送钉钉群
     *
     * @return
     */
    @XxlJob("DouYinSiteMapReport")
    public ReturnT<String> DouYinSiteMapReport() {
        JobLog.info("DouYinSiteMapReport  begin");
        Object report = douYinService.report();
        JobLog.info("DouYinSiteMapReport  end" + report);
        return ReturnT.SUCCESS;
    }


    /**
     * 抖音sitemap补足逻辑
     *
     * @return
     */
    @XxlJob("DouYinSiteMapFillInJob")
    public ReturnT<String> douYinSiteMapFillInJob() {
        JobLog.info("douYinSiteMapFillInJob  begin");

        Object report = douYinService.douYinSiteMapFillIn();
        JobLog.info("douYinSiteMapFillInJob  end" + report);
        return ReturnT.SUCCESS;
    }
//    @PostConstruct
//    public void test(){
//        DouYinSiteMapWenDaJob();
//        BaiDu3gcReport();
//        douYinSiteMapFillInJob();
//    }


    /**
     * 推送报告，百度钉钉群
     *
     * @return
     */
    @XxlJob("BaiDu3gcReport")
    public ReturnT<String> BaiDu3gcReport() {
        JobLog.info("BaiDu3gcReport  begin");
        Object report = douYinService.baiduReport();
        JobLog.info("BaiDu3gcReport  end" + report);
        return ReturnT.SUCCESS;
    }
}
