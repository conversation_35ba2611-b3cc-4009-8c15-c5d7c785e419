package com.autohome.applet.task.job;

import com.autohome.applet.service.javaapi.CarService;
import com.autohome.applet.service.javaapi.RankService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 车系基础信息和口碑数据
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("SpringJavaAutowiringInspection")
public class SeriesBaseAndKoubeiScoreJob {

    @Autowired
    private RankService rankService;
    @Autowired
    private CarService carService;

//    @Scheduled(initialDelay = 5, fixedRate = 1800 * 1000L)
//    public void doIt() {
//        handle();
//    }
//    @PostConstruct
//    public void init() {
//        try {
//            handleForSeries();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @XxlJob("SeriesBaseAndKoubeiScoreJob")
    public ReturnT<String> handle() {
        JobLog.info("SeriesBaseAndKoubeiScoreJob.handle() begin" );
        rankService.cacheSeriesInfo();
        return ReturnT.SUCCESS;
    }

    @XxlJob("AllSeriesInfoJob")
    public ReturnT<String> handleForSeries() {
        carService.cacheSeriesAllInfo();
        return ReturnT.SUCCESS;
    }
}
