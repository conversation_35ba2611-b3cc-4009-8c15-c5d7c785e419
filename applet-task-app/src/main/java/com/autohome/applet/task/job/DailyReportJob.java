package com.autohome.applet.task.job;

import com.autohome.applet.service.DailyReportService;
import com.autohome.applet.service.impl.DailyReportServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DailyReportJob {

    @Autowired
    DailyReportService dailyReportService;

    @XxlJob("DailyReportJob")
    public ReturnT<String> handle() {
        Map<String, List<DailyReportServiceImpl.DingRobot>> mapDingRobot = new HashMap<>();
        ArrayList<DailyReportServiceImpl.DingRobot> lead = new ArrayList<>();
        lead.add(new DailyReportServiceImpl.DingRobot(
                   "https://oapi.dingtalk.com/robot/send?access_token=88b3745848818617a4ccb41876a998f68974924b500cbc439c5272092f886282",
                "SEC96f8c4a04820a82aeb99f6d8635a681fbff652a0e876a8fc7d0ed0011e6386d5"
        ));
        lead.add(new DailyReportServiceImpl.DingRobot(
                "https://oapi.dingtalk.com/robot/send?access_token=348c8cc22c97dbce3867fa9415daa03e3581ab6a86f21527a3b3e674e56d6fff",
                "SECf144e862b3845acc0f82a71c9bb0ddbd9c1022bcc5649f4ed161868109c726d0"
        ));
        lead.add(new DailyReportServiceImpl.DingRobot(
                "https://oapi.dingtalk.com/robot/send?access_token=801fe2acb4b96fb12ea3dfeaeb45137f37ec90c30a7d6bf2953727697bc41385",
                "SEC06abfca46013059d157826556cd1dc8ec40815d9443a732ddd6e3ec848ec3983"
        ));
        mapDingRobot.put("LeadBuyUseCarDaily", lead);
        ArrayList<DailyReportServiceImpl.DingRobot> panoramic = new ArrayList<>();
        panoramic.add(new DailyReportServiceImpl.DingRobot(
                "https://oapi.dingtalk.com/robot/send?access_token=88b3745848818617a4ccb41876a998f68974924b500cbc439c5272092f886282",
                "SEC96f8c4a04820a82aeb99f6d8635a681fbff652a0e876a8fc7d0ed0011e6386d5"
        ));
        panoramic.add(new DailyReportServiceImpl.DingRobot(
                "https://oapi.dingtalk.com/robot/send?access_token=348c8cc22c97dbce3867fa9415daa03e3581ab6a86f21527a3b3e674e56d6fff",
                "SECf144e862b3845acc0f82a71c9bb0ddbd9c1022bcc5649f4ed161868109c726d0"
        ));
        panoramic.add(new DailyReportServiceImpl.DingRobot(
                "https://oapi.dingtalk.com/robot/send?access_token=801fe2acb4b96fb12ea3dfeaeb45137f37ec90c30a7d6bf2953727697bc41385",
                "SEC06abfca46013059d157826556cd1dc8ec40815d9443a732ddd6e3ec848ec3983"
        ));
        mapDingRobot.put("PanoramicBuyUseCarDaily", panoramic);

        dailyReportService.dailyReport(LocalDate.now().minusDays(1), mapDingRobot, false);
        return ReturnT.SUCCESS;
    }
}
