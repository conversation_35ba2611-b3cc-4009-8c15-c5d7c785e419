package com.autohome.applet.task;

import com.autonews.comm.fileUpload.FileUploader;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Arrays;

@EnableScheduling
@MapperScan("com.autohome.applet.dao")
@SpringBootApplication(exclude = {HibernateJpaAutoConfiguration.class, DataSourceAutoConfiguration.class,
        RabbitAutoConfiguration.class,
}, scanBasePackages = "com.autohome")
public class TaskApplication {

    public static void main(String[] args) {
        ApplicationContext applicationContext = SpringApplication.run(TaskApplication.class, args);
//        FileUploader.settingFileUploader("appletdfs", "305c300d06092a864886f70d0101010500034b0030480241008aaba57f910bd6a6732a7dc9404756ff30d3181fb79f30e4e4207e6b95fea51176665213fad2d76ba201e1251f12feef0c54fc4bb9dd3782d22ff44e15dbbd2d0203010001", "www3.autoimg.cn", "lightapp");
        //new SpringApplicationBuilder(TaskApplication.class).properties("spring.config.name=application").bannerMode(Banner.Mode.OFF).web(WebApplicationType.NONE).run(args);
    }


}
