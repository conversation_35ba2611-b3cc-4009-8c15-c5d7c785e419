package com.autohome.applet.task.job;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.autohome.applet.model.dto.original.*;
import com.autohome.applet.model.enums.DataTypeEnum;
import com.autohome.applet.model.enums.OpsPmModuleEnum;
import com.autohome.applet.util.*;
import com.autonews.springboot.util.RedisClient;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toList;

/**
 *  同步原创栏目数据
 *  <wiki src="https://doc.autohome.com.cn/docapi/page/share/share_yUVmMKJp3I"></wiki>
 *  存储的数据用于首页原创tab下的原创栏目
 */

@Component
@Slf4j
public class OGCVipVideoColumnJob {

    private final String videoTagListUrl = "http://la.corpautohome.com/video/video_tag_list";

    private final String getVideosByAuthorIdOfLatestUrl = "http://v.api.lq.autohome.com.cn/Wcf/VideoService.svc/GetVideosByAuthorIdOfLatest";

    private final String getTopStarUsersUrl = "http://v.api.lq.autohome.com.cn/Wcf/VideoService.svc/GetTopStarUsers";

    private final String getSOpsDataResultUrl = "http://cms.api.autohome.com.cn/CmsJava/Position/GetList";

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;
    
    /**
     * TOP50 原创栏目缓存key
     */
    private static final String OGC_VIP_VIDEO_ORIGINAL_KEY = "OGC:VIP:VIDEO:ORIGINAL";


    @XxlJob("OGCVipVideoColumnJob")
    public ReturnT<String> handle() {
        JobLog.info("--------------------OGCVipVideoColumnJob start--------------------");
        long start = System.currentTimeMillis();
        syncDealData();
        long end = System.currentTimeMillis();
        JobLog.info("OGCVipVideoColumnJob耗时:{" +(end - start) / 1000+ "}秒");
        return ReturnT.SUCCESS;
    }

    private void syncDealData() {
        JobLog.info("开始运行");
        //取最新发布的内容数据，目前是取了 2500条数据，也可以从作者维度取数据
        List<NewsFinalResult> newsDataList = getNewsList();
        if(CollectionUtils.isEmpty(newsDataList)) {
            JobLog.info("OGCVipVideoColumnJob 重磅栏目 - 获取原创内容数据异常，未拿到有效数据");
            return;
        }
        //处理内容数据，翻转到编辑维度，存入缓存线，存TOP50编辑 和 存 单编辑维度 缓存
        dealData(newsDataList);
    }
    
    /***
    * 取内容列表，通过内容列表翻转到编辑角色上
    */
    private List<NewsFinalResult> getNewsList() {
        List<NewsFinalResult> newsDataList = new ArrayList<>();
        try {
            newsDataList.addAll(getNewsDataFromApi());
        } catch (Exception ex) {
            JobLog.error("OGCVipVideoColumnJob 重磅栏目服务 - 数据同步服务,从接口读取新闻列表数据出现异常!", ex);
        }
        return newsDataList;
    }
    
    private void dealData(List<NewsFinalResult> list) {
        Integer count = 30;
        
        //取原创栏目账号（去重）
        List<Integer> authorIds = list.stream().map(NewsFinalResult::getUserid).collect(toList());
        List<Integer> authorIdsTmp = authorIds.stream().distinct().collect(toList());
        //获取【全部栏目】最新5条视频内容
        Map<Integer, List<ObjectInfoModel>> editWorksMap = getAuthorAndVideosMap();
        //转为通用结构
        Map<Integer, OGCStartEditorsResult> allColumnMap = dealAllVideoColumnToOGCStartEditorsResultMap(editWorksMap);
        
        //取原创栏目作者最后发布内容的时间关系
        Map<Integer, Long> editorPubTimeMap = new HashMap<>();
        for (Integer uid : authorIdsTmp ) {
            if(editWorksMap.containsKey(uid)) {
                List<ObjectInfoModel> l = editWorksMap.get(uid);
                if(!CollectionUtils.isEmpty(l)) {
                    long pt = l.get(0).getPublishtime().getTime();;
                    if (pt > 0) {
                        editorPubTimeMap.put(uid, pt);
                    }
                }
            }
        }
        
        //按最新发布内容的给作者排序
        List<Integer> top50UserIds = new ArrayList<>();
        if(!editorPubTimeMap.isEmpty()) {
            //按作者最新发布的内容的时间给作者排序
            List<Map.Entry<Integer, Long>> epMap = new ArrayList<>(editorPubTimeMap.entrySet());
            epMap.sort((e1, e2) -> -(e1.getValue().compareTo(e2.getValue())));
            top50UserIds.addAll(epMap.stream().map(Map.Entry::getKey).collect(toList()));
        }

        //存储【原创栏目】模块的数据入缓存
        dealVipColumnToCache(2, top50UserIds, allColumnMap, count);
    }
    
    //单维度缓存所有栏目内容信息
    private Map<Integer, OGCStartEditorsResult> dealAllVideoColumnToOGCStartEditorsResultMap(Map<Integer, List<ObjectInfoModel>> smap) {
        Map<Integer, OGCStartEditorsResult> map = new HashMap<>();
        if(!smap.isEmpty()) {
            for (Map.Entry<Integer, List<ObjectInfoModel>> m : smap.entrySet()) {
                if (CollectionUtils.isEmpty(m.getValue())) continue;
                Integer key = m.getKey();
                OGCStartEditorsResult model = new OGCStartEditorsResult();
                model.setAuthorid(key);
                model.setFromtype(0);
                model.setWork(m.getValue().get(0));
                
                map.put(key, model);
            }
        }
        return map;
    }

    private void dealVipColumnToCache(Integer site, List<Integer> top50UserIds, Map<Integer, OGCStartEditorsResult> allColumnMap, Integer count) {
        List<Integer> top50Tmp = new ArrayList<>(top50UserIds);
        //取运营位数据
        List<Integer> opsIDS = getOpsVideoAuthorList(site);
        if(!CollectionUtils.isEmpty(opsIDS)) {
            //先去掉原集合重复的
            opsIDS.forEach(top50Tmp::remove);
            //运营数据插入到集合内
            top50Tmp.addAll(0, opsIDS);
        }
        List<OGCStartEditorsResult> descList = new ArrayList<>();
        for (Integer uid : top50Tmp ) {
            if(allColumnMap.containsKey(uid)) {
                OGCStartEditorsResult item = allColumnMap.get(uid);
                item.setFromtype(0);
                if (opsIDS.contains(uid)) {
                    item.setFromtype(1);
                }
                descList.add(item);
                if (descList.size() >= count) {
                    break;
                }
            }
        }
        
        if(!CollectionUtils.isEmpty(descList)) {
            redisClient.set(OGC_VIP_VIDEO_ORIGINAL_KEY, JacksonHelper.serialize(descList), 24 * 60 * 60, TimeUnit.SECONDS);
        }
    }

    /**
     * 同步原创视频数据
     */
    private List<NewsFinalResult> getNewsDataFromApi() {
        List<NewsFinalResult> autoVideoList = Lists.newArrayList();
        boolean hasMore = true;
        String searchAfter = "";
        for (int i = 1; i <= 50; i++) {
            if (hasMore) {
                try {
                    STagVideoListResult smodel = getVideoListByTag("23",searchAfter,50);
                    if (smodel != null && smodel.getResult() != null && CollectionUtil.isNotEmpty(smodel.getResult().getItems())) {
                        hasMore = smodel.getResult().isHas_more();
                        searchAfter = StringUtils.isNotEmpty(smodel.getResult().getSearch_after()) ? smodel.getResult().getSearch_after() : "";
                        autoVideoList.addAll(convertMainDataOriginalVideoList(smodel.getResult().getItems()));
                    }
                } catch (Exception e) {
                    JobLog.error("OGCVipVideoColumnJob getNewsDataFromApi error!", e);
                }
            }
        }
        return autoVideoList;
    }
    
    /**
     * 转换主数据源接口原创视频列表
     */
    private List<NewsFinalResult> convertMainDataOriginalVideoList(List<STagVideoListResult.VideoInfo> videoInfoList) {
        List<NewsFinalResult> autoVideoList = new ArrayList<>();
        for (STagVideoListResult.VideoInfo videoInfo : videoInfoList) {
            if (videoInfo != null && videoInfo.getBiz_id() != 0) {
                //过滤掉"汽车工具人"的视频
                if(SafeParamUtil.toSafeInt(videoInfo.getAuthor_id()) == 242194531) {
                    continue;
                }
                NewsFinalResult newsFinalResult = new NewsFinalResult();
                newsFinalResult.setCode(videoInfo.getBiz_id());
                newsFinalResult.setDataType(DataTypeEnum.AutoVideo.getValue());
                newsFinalResult.setTitle(StringUtils.isNotEmpty(videoInfo.getTitle()) ? videoInfo.getTitle() : "");
                newsFinalResult.setType(CollectionUtil.isNotEmpty(videoInfo.getVideo_tag_names())
                        ? SafeParamUtil.toSafeString(videoInfo.getVideo_tag_names().get(0)) : "");
                Date publishTime = StringUtils.isNotEmpty(videoInfo.getPublish_time()) ? DateHelper.deserialize(videoInfo.getPublish_time(), DateHelper.DATEFORMAT_NEW) : new Date();
                newsFinalResult.setPublishtime(publishTime);
                newsFinalResult.setUpdateTime(publishTime);
                newsFinalResult.setImgUrl(StringUtils.isNotEmpty(videoInfo.getImg_url_16x9()) ? videoInfo.getImg_url_16x9() : "");
                newsFinalResult.setVideoImgUrl(StringUtils.isNotEmpty(videoInfo.getImg_url_16x9()) ? videoInfo.getImg_url_16x9() : "");
                newsFinalResult.setIndexDetail(StringUtils.isNotEmpty(videoInfo.getSummary()) ? HtmlXssUtil.filterHtml(videoInfo.getSummary()) : "");
                //replyCount 里面存的 PlayCount 播放数(非回复数)
                newsFinalResult.setReplyCount(Long.valueOf(videoInfo.getVv()).intValue());
                newsFinalResult.setSubjectid(0);
                newsFinalResult.setCms_seriesid(CollectionUtil.isNotEmpty(videoInfo.getSeries_ids())
                        ? StringUtils.join(videoInfo.getSeries_ids().toArray(),",") : "");
                newsFinalResult.setCms_tags(StringUtils.isNotEmpty(videoInfo.getAuthor_name()) ? videoInfo.getAuthor_name() : "");
                newsFinalResult.setViewcount(videoInfo.getReply_count());
                newsFinalResult.setCms_contentclass("");
                newsFinalResult.setJumptype(SafeParamUtil.toSafeInt(Long.valueOf(videoInfo.getDuration()).intValue()));
                newsFinalResult.setVideocardtype(0);
                newsFinalResult.setSummary(StringUtils.isNotEmpty(videoInfo.getVideo_source()) ? videoInfo.getVideo_source() : "");
                newsFinalResult.setEditorid(0);
                newsFinalResult.setUserid(SafeParamUtil.toSafeInt(videoInfo.getAuthor_id(), 0));
                newsFinalResult.setUsername(SafeParamUtil.toSafeString(videoInfo.getAuthor_name()));
                autoVideoList.add(newsFinalResult);
            }
        }
        return autoVideoList;
    }
    
    private Map<Integer, List<ObjectInfoModel>> getAuthorAndVideosMap() {
        Map<Integer, List<ObjectInfoModel>> map = new HashMap<>();
        List<Long> authorIds = getAllVideoAuthorIdList();
        if(CollectionUtil.isNotEmpty(authorIds)) {
            for (Long id: authorIds ) {
                List<ObjectInfoModel> list = new ArrayList<>();
                try {
                    SOriginalAuthorVideoListResult smodel = getOriginalAuthorLatestVideo(Integer.parseInt(id.toString()), "0", 5, 0);
                    if(smodel != null && smodel.getResult() != null && CollectionUtil.isNotEmpty(smodel.getResult().getItems())) {
                        for (SOriginalVideoEntity item : smodel.getResult().getItems()) {
                            if(item != null) {
                                ObjectInfoModel model = convertSOriginalVideoEntityToVideoInfoModel(item);
                                if(model != null) {
                                    list.add(model);
                                }
                            }
                        }
                    }
                } catch (Exception ex) {
                    JobLog.error("OGCVipVideoColumnJob getAuthorAndVideosMap error!", ex);
                }
                if(CollectionUtil.isNotEmpty(list)) {
                    map.put(SafeParamUtil.toSafeInt(id), list);
                }
            }
        }
        
        return map;
    }
    
    /**
     * 转换主数据源接口原创视频列表
     * @param sItem
     */
    private ObjectInfoModel convertSOriginalVideoEntityToVideoInfoModel(SOriginalVideoEntity sItem) {
        ObjectInfoModel objectInfoModel = null;
        if (sItem != null && sItem.getId() != 0) {
            objectInfoModel = new ObjectInfoModel();
            objectInfoModel.setId(sItem.getId());
            objectInfoModel.setMediatype(DataTypeEnum.Video.getValue());
            objectInfoModel.setTitle(StringUtils.isNotEmpty(sItem.getApptitle()) ? sItem.getApptitle() : sItem.getTitle());
            Date publishTime = StringUtils.isNotEmpty(sItem.getPublishtime()) ? DateHelper.parseDateFromWithDateStr(sItem.getPublishtime()) : new Date();
            objectInfoModel.setPublishtime(publishTime);
            objectInfoModel.setUpdateTime(publishTime);
            objectInfoModel.setImgUrl(StringUtils.isNotEmpty(sItem.getCoverimage2()) ? sItem.getCoverimage2() : sItem.getCoverimage());
            objectInfoModel.setSummary(SafeParamUtil.toSafeString(sItem.getDescription()));
            //replyCount 里面存的 PlayCount 播放数(非回复数)
            objectInfoModel.setReplycount(Long.valueOf(sItem.getPlaycount()).intValue());
            objectInfoModel.setSeriesids(StringUtils.join(sItem.getRelationseriesids(), ","));
            objectInfoModel.setUserid(0L);
            objectInfoModel.setUsername("");
            if(sItem.getAuthor() != null &&SafeParamUtil.toSafeInt(sItem.getAuthor().getUserid()) > 0) {
                objectInfoModel.setUserid(SafeParamUtil.toSafeLong(sItem.getAuthor().getUserid(), 0L));
                objectInfoModel.setUsername(SafeParamUtil.toSafeString(sItem.getAuthor().getNickname()));
                objectInfoModel.setHeadimg(SafeParamUtil.toSafeString(sItem.getAuthor().getAvatar()));;
            }
            objectInfoModel.setPlaycount(sItem.getPlaycount());
            //视频时长
            objectInfoModel.setDuration(SafeParamUtil.toSafeInt(sItem.getDuration()));
            objectInfoModel.setDirection(sItem.getVerify());
            //视频源ID
            objectInfoModel.setVideoid(SafeParamUtil.toSafeString(sItem.getVideoid()));
        }
        
        return objectInfoModel;
    }
    
    //获取OGC视频的全部作者ID
    private List<Long> getAllVideoAuthorIdList() {
        List<Long> videoAuthors = new ArrayList<>();

        Map<String, Object> params = new HashMap<>();
        params.put("count", 1000);
        params.put("isFollowUser", true);
        params.put("_appid", "app");

        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(getTopStarUsersUrl, params);
            if (httpResult.isSuccess()) {
                UserVideoReturnValueBean resultJson = JSON.parseObject(httpResult.getBody(), UserVideoReturnValueBean.class);
                if (resultJson != null && resultJson.getReturncode() == 0) {
                    List<VideoUserBean> videoUserBeanList = resultJson.getResult();
                    if (videoUserBeanList != null && !videoUserBeanList.isEmpty()) {
                        videoAuthors.addAll(videoUserBeanList.stream().map(VideoUserBean::getUserid).collect(toList()));
                    }
                }
            }
        } catch (Exception ex) {
            JobLog.error("OGCVipVideoColumnJob getAllVideoAuthorIdList isFollowUser=true error!", ex);
        }

    
        try {
            params.put("isFollowUser", false);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(getTopStarUsersUrl, params);
            if (httpResult.isSuccess()) {
                UserVideoReturnValueBean resultJson = JSON.parseObject(httpResult.getBody(), UserVideoReturnValueBean.class);
                if (resultJson != null && resultJson.getReturncode() == 0) {
                    List<VideoUserBean> videoUserBeanList = resultJson.getResult();
                    if (videoUserBeanList != null && !videoUserBeanList.isEmpty()) {
                        videoAuthors.addAll(videoUserBeanList.stream().map(VideoUserBean::getUserid).collect(toList()));
                    }
                }
            }
        } catch (Exception ex) {
            JobLog.error("OGCVipVideoColumnJob getAllVideoAuthorIdList isFollowUser=false error!", ex);
        }
        return videoAuthors;
    }


    //获取强运营作者ids
    private List<Integer> getOpsVideoAuthorList(Integer site) {
        List<Integer> ids = new ArrayList<>();
        JobLog.info("重磅栏目服务（site={"+site+"}）位 数据同步服务,从定点投放后台取运营数据 start");
        try {
            OpsPmModuleEnum opsPmModuleEnum  = site == 1 ? OpsPmModuleEnum.ORIGINAL_VIPCOLUMN : OpsPmModuleEnum.ORIGINAL_ORICOLUMN;
            SOpsDataResult smodel = getSOpsDataResult(opsPmModuleEnum, 1, 10);
            if(smodel != null && smodel.getResult() != null && CollectionUtil.isNotEmpty(smodel.getResult().getList())) {
                smodel.getResult().getList().sort(Comparator.comparing(SOpsDataResult.OpsDatItem::getOrderNo));
                smodel.getResult().getList().forEach(f -> {
                    if(SafeParamUtil.toSafeInt(f.getObjId()) > 0) {
                        ids.add(SafeParamUtil.toSafeInt(f.getObjId()));
                    }
                });
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        JobLog.info("OGCVipVideoColumnJob 重磅栏目服务（site={" + site + "}）位 数据同步服务,从定点投放后台取运营数据 end: result:{" + JacksonHelper.serialize(ids) + "}");
        return ids;
    }

    /**
     * 根据标签获取视频
     * <wiki src="http://wiki.corpautohome.com/pages/viewpage.action?pageId=124981738"></wiki>
     * 示例：<a href="http://maindata.api.autohome.com.cn/data/more/video_tag_list?_appid=app&page_size=10&tag_ids=23&order_type=publish_time_desc&search_after=">...</a>
     * @param tagIds 标签id
     * @param searchAfter 加载更多标识
     * @param pageSize 返回数量
     */
    private STagVideoListResult getVideoListByTag(String tagIds, String searchAfter,int pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "app");
        params.put("tag_ids", tagIds);
        params.put("search_after", searchAfter);
        params.put("page_size", pageSize);
        params.put("order_type", "publish_time_desc");
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(videoTagListUrl, params);
        if (httpResult.isSuccess()) {
            return JacksonHelper.deserialize(httpResult.getBody(), STagVideoListResult.class);
        } else {
            JobLog.warn("OGCVipVideoColumnJob getVideoListByTag error! params:{"+JacksonHelper.serialize(params)+"}, httpResult:{"+JacksonHelper.serialize(httpResult)+"}");
            return null;
        }
    }

    /**
     * 获取原创视频作者视频列表
     * wiki: <a href="http://vapi.cupid.autohome.com.cn/swagger-ui.html#!/video45controller/GetVideosByAuthorIdOfLatestUsingGET">...</a>
     * 示例: <a href="http://vapi.cupid.autohome.com.cn/Wcf/VideoService.svc/GetVideosByAuthorIdOfLatest?authorId=79195414&pageId=2203030&count=15&_appid=app">...</a>
     * @param authorId 作者userid
     * @param pageId 翻页标识
     * @param count 返回数量
     * @param direct 0-初始化 1-下拉刷新 2-上拉加载
     */
    public SOriginalAuthorVideoListResult getOriginalAuthorLatestVideo(int authorId, String pageId, int count, int direct) {
        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "app");
        params.put("authorId", authorId);
        params.put("pageId", pageId);
        params.put("count", count);
        params.put("direct", direct);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(getVideosByAuthorIdOfLatestUrl, params);
        if (httpResult.isSuccess()) {
            return JacksonHelper.deserialize(httpResult.getBody(), SOriginalAuthorVideoListResult.class);
        } else {
            JobLog.warn("OGCVipVideoColumnJob getOriginalAuthorLatestVideo error! params:{"+JacksonHelper.serialize(params)+"}, httpResult:{"+JacksonHelper.serialize(httpResult)+"}");
            return null;
        }
    }

    /**
     * 获取定点投放平台置顶模块的数据
     * wiki：
     * <a href="https://cms.api.autohome.com.cn/CmsJava/Position/GetList?moduleId=20&pageIndex=1&pagesize=5">...</a>
     */
    public SOpsDataResult getSOpsDataResult(OpsPmModuleEnum moduleid, Integer pageindex, Integer pagesize) {

        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "app");
        params.put("moduleId", moduleid.getValue());
        params.put("pageIndex", pageindex);
        params.put("pagesize", pagesize);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(getSOpsDataResultUrl, params);
        if (httpResult.isSuccess()) {
            return JacksonHelper.deserialize(httpResult.getBody(), SOpsDataResult.class);
        } else {
            JobLog.warn("OGCVipVideoColumnJob getSOpsDataResult error! params:{"+JacksonHelper.serialize(params)+"}, httpResult:{"+JacksonHelper.serialize(httpResult)+"}");
        }

        return null;
    }
}
