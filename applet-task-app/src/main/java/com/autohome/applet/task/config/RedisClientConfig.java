package com.autohome.applet.task.config;

import com.autonews.springboot.util.RedisClient;
import com.autonews.springboot.util.redis.RedisClientImpl;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.NettyCustomizer;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.List;

/**
 * redis-ext特殊设置心跳
 * 暂时先放web项目里，因为config项目未引入RedisClient包
 * add by pengyi 2024-04-08
 */
@Slf4j
@Configuration
public class RedisClientConfig {
    @Autowired
    List<RedisClient> redises;

    @PostConstruct
    public void init(){
        if(redises != null){
            redises.forEach(redisClient->{
                Field privateStringField = null;
                try {
                    privateStringField = RedisClientImpl.class.
                            getDeclaredField("redisTemplateclient");
                } catch (NoSuchFieldException e) {
                    log.error("privateStringField NoSuchFieldException",e);
                }

                //关闭对此特定字段实例的访问检查
                privateStringField.setAccessible(true);

                RedisTemplate redisTemplate = null;
                try {
                    redisTemplate = (RedisTemplate) privateStringField.get(redisClient);
                } catch (IllegalAccessException e) {
                    log.error("privateStringField Exception",e);
                }
                if(redisTemplate == null){
                    log.warn("redisTemplate is null");
                    return;
                }
                LettuceConnectionFactory lettuceConnectionFactory = (LettuceConnectionFactory) redisTemplate.getConnectionFactory();
                lettuceConnectionFactory.setValidateConnection(true);
                DefaultClientResources defaultClientResources = (DefaultClientResources) lettuceConnectionFactory.getClientResources();
                if(defaultClientResources == null){
                    log.warn("defaultClientResources is null:{}",redisTemplate);
                    return;
                }
                Field privateStringField2 = null;
                try {
                    privateStringField2 = DefaultClientResources.class.
                            getDeclaredField("nettyCustomizer");
                    privateStringField2.setAccessible(true);
                    privateStringField2.set(defaultClientResources, new NettyCustomizer() {
                        @Override
                        public void afterChannelInitialized(Channel channel) {
                            channel.pipeline().addLast(
                                    new IdleStateHandler(30, 30, 30));
                            channel.pipeline().addLast(new ChannelDuplexHandler() {
                                @Override
                                public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
                                    if (evt instanceof IdleStateEvent) {
                                        ctx.disconnect();
                                    }
                                }
                            });
                        }

                        @Override
                        public void afterBootstrapInitialized(Bootstrap bootstrap) {
                        }
                    }); // 为指定field设置新值
                    log.info("{} set success",redisTemplate);
                } catch (Exception e) {
                    log.error("privateStringField2 Exception",e);
                }
            });
        }
    }

}
