package com.autohome.applet.task.job;

import com.autohome.applet.model.dto.original.*;
import com.autohome.applet.model.dto.usercenter.UserInfoDTO;
import com.autohome.applet.service.javaapi.UserCenterService;
import com.autohome.applet.util.*;
import com.autonews.springboot.util.RedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * * 仅服务APP首页推荐 - 仅APP首页使用
 */

@Component
@Slf4j
public class UserFromContentJob {

    private static final String USER_CACHE_PREFIX = "original_user_data_";

    // fixme temp update
//    private static final int EXPIRE_TIME = 60 * 60 * 24 * 7;
    private static final int EXPIRE_TIME = 60 * 60 * 24;

    @Qualifier("lightapp")
    @Autowired
    private RedisClient redisClient;

    @Autowired
    private UserCenterService userCenterService;

    @XxlJob("UserFromContentJob")
    public ReturnT<String> handle() {
        JobLog.info("--------------------UserFromContentJob 开始运行--------------------");
        long start = System.currentTimeMillis();
        String articleUrl = "http://cms.api.autohome.com.cn/Wcf/MiscService.svc/GetExperBlog?_appid=app";
        getAndUpdateArticleData(articleUrl);
        String cheJiaHaoUrl = "http://chejiahao.api.lq.autohome.com.cn/UserService.svc/GetPageAllAuthors?_appid=app&count=100&orderType=0&returnAll=False&isloadwechat=False&pageId=";
        getAndUpdateCheJiaHaoData(cheJiaHaoUrl);
        String videoUrl1 = "http://v.api.lq.autohome.com.cn/Wcf/VideoService.svc/GetTopStarUsers?count=1000&isFollowUser=true&_appid=app";
        String videoUrl2 = "http://v.api.lq.autohome.com.cn/Wcf/VideoService.svc/GetTopStarUsers?count=1000&isFollowUser=false&_appid=app";
        getAndUpdateVideoData(videoUrl1);
        getAndUpdateVideoData(videoUrl2);
        long end = System.currentTimeMillis();
        JobLog.info("UserFromContentJob 耗时:{" + (end - start) / 1000 + "}秒");
        return ReturnT.SUCCESS;
    }

    public void getAndUpdateArticleData(String url) {
        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
            if (httpResult.isSuccess()) {
                ArticleReturnValueBean resultJson = JacksonHelper.deserialize(httpResult.getBody(), ArticleReturnValueBean.class);
                if (resultJson != null && resultJson.getReturncode() == 0) {
                    List<UserArticleBean> list = resultJson.getResult().getList();
                    Map<Long, UserRedisBean> resultMap = new HashMap<>();
                    while (list != null && !list.isEmpty()) {
                        int num = 0;
                        List<Integer> ids = new ArrayList<>();
                        for (int i = list.size() - 1; i >= 0; i--) {
                            num = num + 1;
                            if (num >= 10) {
                                break;
                            } else {
                                UserArticleBean item = list.get(i);
                                UserRedisBean userRedisBean = new UserRedisBean();
                                userRedisBean.setUserId(item.getMemberid());
                                userRedisBean.setDesc(item.getTitle());
                                userRedisBean.setUserType(1);
                                userRedisBean.setUserId_sec(item.getEditorid());
                                userRedisBean.setNickName_sec(item.getNickname());
                                userRedisBean.setHeadImg_sec(item.getMemberpic());
                                resultMap.put(userRedisBean.getUserId(), userRedisBean);
                                ids.add(SafeParamUtil.toSafeInt(userRedisBean.getUserId()));
                                list.remove(item);
                            }
                        }
                        // 补充用户信息
                        buildUserRedisBeanMap(ids, resultMap);
                    }
                    for (UserRedisBean valueArticle : resultMap.values()) {
                        updateCache(valueArticle);
                    }
                } else {
                    JobLog.warn("UserFromContentJob getAndUpdateArticleData warning, return code is not 0, request url:{"+url+"}");
                }
            }
        } catch (Exception ex) {
            JobLog.error("UserFromContentJob getAndUpdateArticleData error! url:{"+url+"}", ex);
        }
    }

    public void getAndUpdateCheJiaHaoData(String url) {
        try {
            String urlTemp = "http://chejiahao.api.lq.autohome.com.cn/UserService.svc/GetPageAllAuthors?_appid=app&count=100&orderType=0&returnAll=False&isloadwechat=False&pageId=";
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
            if (httpResult.isSuccess()) {
                ChejiahaoReturnValueBean resultJson = JacksonHelper.deserialize(httpResult.getBody(), ChejiahaoReturnValueBean.class);
                if (resultJson != null && resultJson.getReturncode() == 0) {
                    List<UserChejiahaoBean> list = resultJson.getResult().getItems();
                    if (list != null && !list.isEmpty()) {
                        JobLog.info("UserFromContentJob getAndUpdateCheJiaHaoData total count:{"+resultJson.getResult().getTotalcount()+"}");
                        Map<Long, UserRedisBean> resultMap = new HashMap<>();
                        String lastPageId = list.get(list.size() - 1).getPageid();
                        String nextUrl = urlTemp + lastPageId;
                        while (!list.isEmpty()) {
                            int num = 0;
                            List<Integer> ids = new ArrayList<>();
                            for (int i = list.size() - 1; i >= 0; i--) {
                                num = num + 1;
                                if (num >= 10) {
                                    break;
                                } else {
                                    UserChejiahaoBean item = list.get(i);
                                    UserRedisBean userRedisBean = new UserRedisBean();
                                    userRedisBean.setUserId(item.getUserid());
                                    userRedisBean.setDesc(item.getIntroduction());

                                    userRedisBean.setNickName_sec(item.getNickname()); //车家号没有第二ID
                                    userRedisBean.setHeadImg_sec(item.getAvatar());
                                    userRedisBean.setUserType(3);
                                    resultMap.put(userRedisBean.getUserId(), userRedisBean);
                                    ids.add(SafeParamUtil.toSafeInt(userRedisBean.getUserId()));
                                    list.remove(item);
                                }
                            }
                            buildUserRedisBeanMap(ids, resultMap);
                        }
                        for (UserRedisBean userRedisBean : resultMap.values()) {
                            updateCache(userRedisBean);
                        }
                        JobLog.info("UserFromContentJob getAndUpdateCheJiaHaoData nextUrl:{"+nextUrl+"}");
                        getAndUpdateCheJiaHaoData(nextUrl);
                    } else {
                        JobLog.info("UserFromContentJob getAndUpdateCheJiaHaoData completed!");
                    }
                } else {
                    JobLog.warn("UserFromContentJob getAndUpdateCheJiaHaoData warning! url:{"+url+"}, return code is not 0!");
                }
            }
        } catch (Exception ex) {
            JobLog.error("UserFromContentJob getAndUpdateCheJiaHaoData error! url:{"+url+"}", ex);
        }
    }

    public void getAndUpdateVideoData(String url) {
        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
            if (httpResult.isSuccess()) {
                UserVideoReturnValueBean resultJson = JacksonHelper.deserialize(httpResult.getBody(), UserVideoReturnValueBean.class);
                if (resultJson != null && resultJson.getReturncode() == 0) {
                    List<VideoUserBean> videoUserBeanList = resultJson.getResult();
                    if (videoUserBeanList != null && !videoUserBeanList.isEmpty()) {
                        JobLog.info("UserFromContentJob getAndUpdateVideoData total count:{"+videoUserBeanList.size()+"}");
                        List<Long> uidList = videoUserBeanList.stream().map(VideoUserBean::getUserid).collect(Collectors.toList());
                        Map<Long, Integer> workscountMap = getVideoAuthorWorksCountMap(uidList);
                        Map<Long, UserRedisBean> resultMap = new HashMap<>();
                        while (!videoUserBeanList.isEmpty()) {
                            int num = 0;
                            List<Integer> ids = new ArrayList<>();
                            for (int i = videoUserBeanList.size() - 1; i >= 0; i--) {
                                num = num + 1;
                                if (num >= 10) {
                                    break;
                                } else {
                                    VideoUserBean item = videoUserBeanList.get(i);
                                    UserRedisBean userRedisBean = new UserRedisBean();
                                    userRedisBean.setUserId(item.getUserid());
                                    userRedisBean.setNickName(item.getNickname());
                                    userRedisBean.setHeadImg(item.getAvatar());
                                    userRedisBean.setDesc(item.getDescription());
                                    userRedisBean.setUserType(2);
                                    userRedisBean.setWorkscount(0);
                                    if (workscountMap.containsKey(item.getUserid())) {
                                        userRedisBean.setWorkscount(workscountMap.get(item.getUserid()));
                                    }
                                    resultMap.put(userRedisBean.getUserId(), userRedisBean);
                                    ids.add(SafeParamUtil.toSafeInt(userRedisBean.getUserId()));
                                    videoUserBeanList.remove(item);
                                }
                            }
                            buildUserRedisBeanMap(ids, resultMap);
                        }
                        for (UserRedisBean value : resultMap.values()) {
                            updateCacheVideo(value);
                        }
                    } else {
                        JobLog.warn("UserFromContentJob getAndUpdateVideoData warning! url:{"+url+"}, return list is empty!");
                    }
                } else {
                    JobLog.warn("UserFromContentJob getAndUpdateVideoData warning! url:{"+url+"}, return code is not 0!");
                }
            }
        } catch (Exception ex) {
            JobLog.error("UserFromContentJob getAndUpdateVideoData error! url:{"+url+"}", ex);
        }
    }

    private Map<Long, Integer> getVideoAuthorWorksCountMap(List<Long> allUserIds) {
        Map<Long, Integer> map = new HashMap<>();
        for (Long uid : allUserIds) {
            try {
                int userID = SafeParamUtil.toSafeInt(uid);
                SOriginalAuthorVideoListResult result = getSOriginalAuthorVideoListResult(userID, 1, 1);
                if(result !=null && result.getResult() != null && CollectionUtils.isNotEmpty(result.getResult().getItems())) {
                    SOriginalVideoEntity item = result.getResult().getItems().get(0);
                    if(item != null && item.getAuthor() != null) {
                        map.put(uid, SafeParamUtil.toSafeInt(item.getAuthor().getVideocount(), 0));
                    }
                }
            } catch (Exception ex) {
                JobLog.error("UserFromContentJob getVideoAuthorWorksCountMap error! uid:{"+uid+"}", ex);
            }
        }
        return map;
    }

    public SOriginalAuthorVideoListResult getSOriginalAuthorVideoListResult(int authorId, Integer start, int count) {
        StringBuffer sb = new StringBuffer();
        sb.append("http://v.api.lq.autohome.com.cn/Wcf/VideoService.svc/GetVideosByAuthorId")
                .append("?_appid=").append("app")
                .append("&authorId=").append(authorId)
                .append("&start=").append(start)
                .append("&count=").append(count);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(sb.toString());
        if (httpResult.isSuccess()) {
            return JacksonHelper.deserialize(httpResult.getBody(), SOriginalAuthorVideoListResult.class);
        }
        return null;
    }

    private void updateCacheVideo(UserRedisBean userRedisBean) {
        String cacheKey = USER_CACHE_PREFIX + userRedisBean.getUserId();
        String verStr = redisClient.get(cacheKey);

        if (!StringUtils.isBlank(verStr)) {
            UserRedisBean result = JacksonHelper.deserialize(verStr, UserRedisBean.class);
            if (result != null && StringUtils.isNotBlank(result.getNickName_sec())) {
                userRedisBean.setUserId_sec(result.getUserId_sec());
                userRedisBean.setNickName_sec(result.getNickName_sec());
                userRedisBean.setHeadImg_sec(result.getHeadImg_sec());
            }
        }
        try {
//            redisClient.set(cacheKey, JacksonHelper.serialize(userRedisBean), EXPIRE_TIME, TimeUnit.SECONDS);
        } catch (Exception ex) {
            JobLog.error("UserFromContentJob updateCacheVideo error!", ex);
        }
    }


    private void updateCache(UserRedisBean userRedisBean) {
        try {
            String userCacheKey = USER_CACHE_PREFIX + userRedisBean.getUserId();
            redisClient.set(userCacheKey, JacksonHelper.serialize(userRedisBean), EXPIRE_TIME, TimeUnit.SECONDS);
        } catch (Exception ex) {
            JobLog.error("UserFromContentJob updateCache 写入缓存Cache Error!", ex);
        }
    }

    public void buildUserRedisBeanMap(List<Integer> ids, Map<Long, UserRedisBean> resultMap) {
        List<UserInfoDTO> userInfoList = userCenterService.getUserInfoList(ids);
        for (UserInfoDTO userBaseBean : userInfoList) {
            Long userId = Long.valueOf(userBaseBean.getUserid());
            if (resultMap.containsKey(userId)) {
                resultMap.get(userId).setNickName(userBaseBean.getNewnickname());
                resultMap.get(userId).setHeadImg(userBaseBean.getHeadimage());
                String regTime = "";
                if (userBaseBean.getAddDate() != null) {
                    try {
                        Date rgt = DateUtils.addHours(userBaseBean.getAddDate(), 8);
                        regTime = DateHelper.serialize(rgt);
                    } catch (Exception ex) {
                        JobLog.error("UserFromContentJob buildUserRedisBeanMap convert time error!", ex);
                    }
                }
                resultMap.get(userId).setRegtime(regTime);
            }
        }
    }


}
