package com.autohome.applet.task.job;

import com.autohome.applet.service.javaapi.SerachCarsService;
import com.autohome.applet.util.JobLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 百度，百家号内容推送任务
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("SpringJavaAutowiringInspection")
public class SearchCarSeriesHotRankJob {

    @Autowired
    SerachCarsService serachCarsService;

//    @PostConstruct
//    private void init(){
//        handle();
//    }

    @XxlJob("SearchCarSeriesHotRankJob")
    public ReturnT<String> handle() {
        JobLog.info("SearchCarSeriesHotRankJob.handle() begin" );
        serachCarsService.cacheSeriesAttNum();
         return ReturnT.SUCCESS;
    }
}
