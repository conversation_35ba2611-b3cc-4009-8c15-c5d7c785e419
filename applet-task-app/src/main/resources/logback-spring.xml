<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false" scanPeriod="1 seconds">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty name="LOG_PATH" source="logging.file.path" defaultValue="/data/app/log/"/>
    <property name="LOG_PATTERN" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] %-5level %logger{36} logSeq:[%X{TraceId}] %method %line - %msg%n"></property>
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="daily" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <file>${LOG_PATH}/project_info.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.autohome.dealer.lib.logback.CsvLayout">
                <department>usercenter</department>
                <team>uc.support</team>
                <project>applet-new-task</project>
            </layout>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${LOG_PATH}/project_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件最多 500MB, 10天的日志周期，最大不能超过5GB -->
            <maxFileSize>500MB</maxFileSize>
            <totalSizeCap>1GB</totalSizeCap>
            <!--日志保留时长-->
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="daily_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <file>${LOG_PATH}/project_error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${LOG_PATH}/project_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件最多 500MB, 10天的日志周期，最大不能超过10GB -->
            <maxFileSize>500MB</maxFileSize>
            <totalSizeCap>1GB</totalSizeCap>
            <!--日志保留时长-->
            <maxHistory>5</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.autohome.dealer.lib.logback.CsvLayout">
                <department>usercenter</department>
                <team>uc.support</team>
                <project>applet-new-task</project>
            </layout>
        </encoder>
    </appender>
    <logger name="io.lettuce.core.protocol" level="error"/>
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="daily"/>
        <appender-ref ref="daily_error"/>
    </root>

</configuration>