server:
  port: 8080
spring:
  profiles:
    include: common-online
  datasource:
    db-autoopen:
      url: ************************************************************************************************************************************
      username: AutoOpenWriter
      password: 19EABEAB-B30A-4514-8E24-5D86FFE85F7C
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-autoopen-read:
      url: ************************************************************************************************************************************
      username: AutoOpenReader
      password: 0409B5D1-03F4-47DA-BFD2-B6FE0B8007B4
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-weixin:
      url: ********************************************************************************************************************************
      username: WeiXin_Writer
      password: 81C7CF78-1AFB-4814-941F-D30878E89FA9
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-weixin-read:
      url: ********************************************************************************************************************************
      username: WeiXin_Writer
      password: 81C7CF78-1AFB-4814-941F-D30878E89FA9
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-carstatisticsdata:
      url: *************************************************************************************************************************************************************************************************************************************************
      username: car_statistics_data_wr
      password: 0vQLDi2zEja(nN5k
      driver-class-name: com.mysql.cj.jdbc.Driver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
      min-idle: 10
      max-pool-size: 30
      connection-timeout: 30000
    db-content-read:
      url: *********************************************************************************************************************************;
      username: cmsapiReader
      password: 1B0B0CF7-B4DF-475F-B927-DB102C27DD4C
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-liveplatform:
      url: **********************************************************************************************************************************************
      username: live_platformWriter
      password: F814AC22-27DD-4892-BD14-A754209BC42A
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-liveplatform-read:
      url: ***************************************************************************************************************************************************
      username: live_platformReader
      password: 363B19A0-55F2-4454-8B93-C95D230D07F2
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-appletbigdata:
      url: ****************************************************************************************************************************************************************************************************************************************************************
      username: applet_acc_wr
      password: b8oXB0Y6jRn_W4C0
      driver-class-name: com.mysql.cj.jdbc.Driver
#    db-wxapp:
#      url: *****************************************************************************************************************
#      username: wxappwriter
#      password: 0e@SAcqxZ8rBzcbwshMU
#      driver-class-name: com.mysql.cj.jdbc.Driver
  kafka:
    baidu:
      bootstrap-servers: yz-public02-broker0.kafka.mq.corpautohome.com:9092,yz-public02-broker1.kafka.mq.corpautohome.com:9092,yz-public02-broker2.kafka.mq.corpautohome.com:9092
      consumer:
        client-id: b6553dcd97634f5089b3fa7d3ba0663b
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        auto-offset-reset: latest
        topic: carsecondhand_to_baidu_online
        group-id: carsecondhand_to_baidu_online_20231027
        enable-auto-commit: true
      listener:
        concurrency: 1
    mp:
      bootstrap-servers: lf-online-bussiness01-broker0.kafka.mq.corpautohome.com:9092,lf-online-bussiness01-broker1.kafka.mq.corpautohome.com:9092,lf-online-bussiness01-broker2.kafka.mq.corpautohome.com:9092
      consumer:
        client-id: f94a38b49a4141c4b43c0b9ca73d7d42
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        auto-offset-reset: latest
        topic: wechat-service-account-event-msg
        group-id: wechat-service-account-event-msg-********
        enable-auto-commit: true
      listener:
        concurrency: 1
logging:
  file:
    path: /data/app/log/
# xxl-job配置
xxl:
  job:
    enable: true
    admin:
      addresses: http://uag-job.corpautohome.com/xxl-job-admin
    executor:
      address:
      appname: applet-task
      bindip:
      port: 7999
      logpath: /opt/log/applog
      logretentiondays: 30
    accessToken: xMrVFjivwewvVriOeikYJFJIpUEktBGWrZMrBkhSXJS
zhiyutong:
  api:
    activityList: http://zhiyutong.api.corpautohome.com/appletapi/collect/page/activityList
duanshipin_2345:
  upload:
    url: http://v-api.2345.com/shortVideo/interface/pushData

#钉钉消息推送地址
dingding:
  douyin:
    accesstoken: https://oapi.dingtalk.com/robot/send?access_token=5591d263770206cccf349be7ba79ed91c0334dc449dc07876b94413eb2580d89
    secret: SEC91aa51ed724f7050a815735ae3f1bb13f1ed905a31ca09f7571b1eb952bcd61a
  baidu:
    accesstoken: https://oapi.dingtalk.com/robot/send?access_token=b80e9e03dbb9872455919216e6abb6553b3822a4a6ec6271469183377e619b4c
    secret: SECe34741475b972c00326f996b68c4df4c630296eeb5c0e1f2e455e661b38a16b1