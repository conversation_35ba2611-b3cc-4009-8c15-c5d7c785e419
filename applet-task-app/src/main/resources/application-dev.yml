server:
  port: 8091
spring:
  profiles:
    include: common-dev
  datasource:
    db-autoopen:
      url: ******************************************************************************************************************;
      username: AutoOpenAdmin
      password: 694030AB-7800-45E8-AA3A-09A84D27E8E9
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-autoopen-read:
      url: ******************************************************************************************************************;
      username: AutoOpenAdmin
      password: 694030AB-7800-45E8-AA3A-09A84D27E8E9
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000

    db-weixin:
#      url: *****************************************************************************************************************;
#      username: sa
#      password: autohome
      url: ****************************************************************************************************************;
      username: AutoOpenAdmin
      password: 694030AB-7800-45E8-AA3A-09A84D27E8E9
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
      master: true
    db-weixin-read:
      url: ****************************************************************************************************************;
      username: AutoOpenAdmin
      password: 694030AB-7800-45E8-AA3A-09A84D27E8E9
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-carstatisticsdata:
      url: ************************************************************************************************************************************************************************************************************************
      username: nroot
      password: AuTo!3%7
      driver-class-name: com.mysql.cj.jdbc.Driver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
      min-idle: 10
      max-pool-size: 30
      connection-timeout: 30000
    db-liveplatform:
      url: *****************************************************************************************************************************
      username: sa
      password: autohome
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-liveplatform-read:
      url: *****************************************************************************************************************************
      username: db_reader
      password: autohome
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      test-on-borrow: true
      test-while-idle: true
      time-between-eviction-runs-millis: 3600000
    db-appletbigdata:
      url: ***************************************************************************************************************************************************************************************************************************
      username: nroot
      password: AuTo!3%7
      driver-class-name: com.mysql.cj.jdbc.Driver
#    db-wxapp:
#      url: ****************************************************************************************************************************************************************
#      username: nroot_wr
#      password: 0tI_pcHK04bE8o5l
#      driver-class-name: com.mysql.cj.jdbc.Driver

  kafka:
    baidu:
      bootstrap-servers: 10.168.100.16:9093,10.168.100.17:9093,10.168.100.18:9093
      consumer:
        client-id: b6553dcd97634f5089b3fa7d3ba0663b
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        auto-offset-reset: latest
        topic: carsecondhand_to_baidu_test
        group-id: carsecondhand_to_baidu_test_202310081
        enable-auto-commit: true
      listener:
        concurrency: 1
    mp:
      bootstrap-servers: lf-test-broker0.kafka.mq.corpautohome.com:9092,lf-test-broker1.kafka.mq.corpautohome.com:9092,lf-test-broker2.kafka.mq.corpautohome.com:9092
      consumer:
        client-id: f94a38b49a4141c4b43c0b9ca73d7d42
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        auto-offset-reset: latest
        topic: wechat-service-account-event-msg
        group-id: wechat-service-account-event-msg-********
        enable-auto-commit: true
      listener:
        concurrency: 1
logging:
  file:
    path: /opt/log/applog
  level:
    com:
      autohome:
        applet:
          dao:
            javaapi:
              mapper: debug
# xxl-job配置
xxl:
  job:
    enable: false
    admin:
      addresses: http://localhost:5050/xxl-job-admin/
    executor:
      address:
      appname: applet-task-dev
      bindip:
      port: 7999
      logpath: /opt/log/applog
      logretentiondays: 30
    accessToken: 3eb88a7cad714912b5d1d0e5fa8da18c

zhiyutong:
  api:
    activityList: http://zhiyutong.api.applet.terra.corpautohome.com/appletapi/collect/page/activityList

duanshipin_2345:
  upload:
    url: http://v-api.2345.com/shortVideo/interface/pushData

#钉钉消息推送地址
dingding:
  douyin:
    accesstoken: https://oapi.dingtalk.com/robot/send?access_token=5591d263770206cccf349be7ba79ed91c0334dc449dc07876b94413eb2580d89
    secret: SEC91aa51ed724f7050a815735ae3f1bb13f1ed905a31ca09f7571b1eb952bcd61a
  baidu:
    accesstoken: https://oapi.dingtalk.com/robot/send?access_token=b80e9e03dbb9872455919216e6abb6553b3822a4a6ec6271469183377e619b4c
    secret: SECe34741475b972c00326f996b68c4df4c630296eeb5c0e1f2e455e661b38a16b1