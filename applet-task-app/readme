1. 调用凭证接口
https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Get_access_token.html
获取汽车之家公众号调用凭证
接口调用请求说明：
    https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    请求方式: GET
    参数说明:
        grant_type  获取access_token填写client_credential         获取方式： 固定值：client_credential
        appid       第三方用户唯一凭证                              获取方式： private static final String appId = "wx0c1bd63e1faca849";
        secret      第三方用户唯一凭证密钥，即appsecret              获取方式：  private static final String appSecret = "4a7482c799a22408f8f1d3f2d9f81b65";

2. 获取 openid
https://developers.weixin.qq.com/doc/offiaccount/User_Management/Getting_a_User_List.html
批量获取汽车之家公众号用户openid
接口调用请求说明：
    https://api.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&next_openid=NEXT_OPENID
    请求方式: GET
    参数说明:
        access_token  调用接口凭证   获取方式： 接口调用凭证接口拿到的access_token
        next_openid     上一批列表的最后一个OPENID，不填默认从头开始拉取

3. 获取 uinonid
https://developers.weixin.qq.com/doc/offiaccount/User_Management/Get_users_basic_information_UnionID.html#UinonId
通过openid获取对应unionid
接口调用请求说明：
    https://api.weixin.qq.com/cgi-bin/user/info?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN
    请求方式: GET
        参数说明:
            access_token  调用接口凭证   获取方式： 接口调用凭证接口拿到的access_token
            openid     获取方式：调用第2个接口获取的 openid
            lang     返回国家地区语言版本，zh_CN 简体，zh_TW 繁体，en 英语