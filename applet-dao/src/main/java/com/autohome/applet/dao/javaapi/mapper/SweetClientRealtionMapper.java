package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SweetClientRealtion;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@DS("db-weixin-read")
@Mapper
@Repository
public interface SweetClientRealtionMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SweetClientRealtion row);

    SweetClientRealtion selectByPrimaryKey(Integer id);

    List<SweetClientRealtion> selectAll();

    int updateByPrimaryKey(SweetClientRealtion row);
}