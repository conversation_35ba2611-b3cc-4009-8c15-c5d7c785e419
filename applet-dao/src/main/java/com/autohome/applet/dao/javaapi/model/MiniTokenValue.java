package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class MiniTokenValue {
    private Integer id;

    private String tokenvalue;

    private Integer minitype;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTokenvalue() {
        return tokenvalue;
    }

    public void setTokenvalue(String tokenvalue) {
        this.tokenvalue = tokenvalue == null ? null : tokenvalue.trim();
    }

    public Integer getMinitype() {
        return minitype;
    }

    public void setMinitype(Integer minitype) {
        this.minitype = minitype;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }
}