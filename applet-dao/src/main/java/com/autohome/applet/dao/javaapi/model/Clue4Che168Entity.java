package com.autohome.applet.dao.javaapi.model;


public class Clue4Che168Entity {

  /**
   * null
   */
  private int id;

  /**
   * 手机号
   */
  private String keyPhone;

  /**
   * 姓名
   */
  private String keyName;

  /**
   * null
   */
  private String keyUsedCarBusinessId;

  /**
   * 用户id
   */
  private int keyUserId;

  /**
   * ip
   */
  private String keyClientOrderIp;

  /**
   * linkid
   */
  private String keyInsideLinkidId;

  /**
   * outid
   */
  private String keyOutsidePvareaidId;

  /**
   * 规则引擎状态0待审，1通过，2未
   */
  private int ruleengineStatus;

  /**
   * 规则引擎结果
   */
  private String ruleengineResult;

  /**
   * 线索pushid
   */
  private String pushId;

  /**
   * 推送状态，0未推送，1推送成功，
   */
  private int pushStatus;

  /**
   * 推送结果
   */
  private String pushResult;

  /**
   * 推送数据
   */
  private String pushData;

  /**
   * 创建时间
   */
  private java.util.Date createdSTime;

  /**
   * 是否删除
   */
  private boolean isDel;

  /**
   * 翻页标识
   */
  private String pageId;

  /**
   * 翻页标识
   */
  private String pushAppKey;

  /**
   * 翻页标识
   */
  private Integer keyUsedCarSourceId;

  /**
   * 翻页标识
   */
  private Integer sourceId;

  /**
   * 翻页标识
   */
  private Integer keySupplyBusinessId;

  /**
   * 翻页标识
   */
  private Integer keyOrderCityid;



  /**
   * null
   */
  public int getId() {
    return id;
  }

  /**
   * null
   */
  public void setId(int id) {
    this.id = id;
  }


  /**
   * 手机号
   */
  public String getKeyPhone() {
    return keyPhone;
  }

  /**
   * 手机号
   */
  public void setKeyPhone(String keyPhone) {
    this.keyPhone = keyPhone;
  }


  /**
   * 姓名
   */
  public String getKeyName() {
    return keyName;
  }

  /**
   * 姓名
   */
  public void setKeyName(String keyName) {
    this.keyName = keyName;
  }


  /**
   * null
   */
  public String getKeyUsedCarBusinessId() {
    return keyUsedCarBusinessId;
  }

  /**
   * null
   */
  public void setKeyUsedCarBusinessId(String keyUsedCarBusinessId) {
    this.keyUsedCarBusinessId = keyUsedCarBusinessId;
  }


  /**
   * 用户id
   */
  public int getKeyUserId() {
    return keyUserId;
  }

  /**
   * 用户id
   */
  public void setKeyUserId(int keyUserId) {
    this.keyUserId = keyUserId;
  }


  /**
   * ip
   */
  public String getKeyClientOrderIp() {
    return keyClientOrderIp;
  }

  /**
   * ip
   */
  public void setKeyClientOrderIp(String keyClientOrderIp) {
    this.keyClientOrderIp = keyClientOrderIp;
  }


  /**
   * linkid
   */
  public String getKeyInsideLinkidId() {
    return keyInsideLinkidId;
  }

  /**
   * linkid
   */
  public void setKeyInsideLinkidId(String keyInsideLinkidId) {
    this.keyInsideLinkidId = keyInsideLinkidId;
  }


  /**
   * outid
   */
  public String getKeyOutsidePvareaidId() {
    return keyOutsidePvareaidId;
  }

  /**
   * outid
   */
  public void setKeyOutsidePvareaidId(String keyOutsidePvareaidId) {
    this.keyOutsidePvareaidId = keyOutsidePvareaidId;
  }


  /**
   * 规则引擎状态0待审，1通过，2未
   */
  public int getRuleengineStatus() {
    return ruleengineStatus;
  }

  /**
   * 规则引擎状态0待审，1通过，2未
   */
  public void setRuleengineStatus(int ruleengineStatus) {
    this.ruleengineStatus = ruleengineStatus;
  }


  /**
   * 规则引擎结果
   */
  public String getRuleengineResult() {
    return ruleengineResult;
  }

  /**
   * 规则引擎结果
   */
  public void setRuleengineResult(String ruleengineResult) {
    this.ruleengineResult = ruleengineResult;
  }


  /**
   * 线索pushid
   */
  public String getPushId() {
    return pushId;
  }

  /**
   * 线索pushid
   */
  public void setPushId(String pushId) {
    this.pushId = pushId;
  }


  /**
   * 推送状态，0未推送，1推送成功，
   */
  public int getPushStatus() {
    return pushStatus;
  }

  /**
   * 推送状态，0未推送，1推送成功，
   */
  public void setPushStatus(int pushStatus) {
    this.pushStatus = pushStatus;
  }


  /**
   * 推送结果
   */
  public String getPushResult() {
    return pushResult;
  }

  /**
   * 推送结果
   */
  public void setPushResult(String pushResult) {
    this.pushResult = pushResult;
  }


  /**
   * 推送数据
   */
  public String getPushData() {
    return pushData;
  }

  /**
   * 推送数据
   */
  public void setPushData(String pushData) {
    this.pushData = pushData;
  }


  /**
   * 创建时间
   */
  public java.util.Date getCreatedSTime() {
    return createdSTime;
  }

  /**
   * 创建时间
   */
  public void setCreatedSTime(java.util.Date createdSTime) {
    this.createdSTime = createdSTime;
  }


  /**
   * 是否删除
   */
  public boolean getIsDel() {
    return isDel;
  }

  /**
   * 是否删除
   */
  public void setIsDel(boolean isDel) {
    this.isDel = isDel;
  }


  public String getPageId() {
    return pageId;
  }

  public void setPageId(String pageId) {
    this.pageId = pageId;
  }


  public String getPushAppKey() {
    return pushAppKey;
  }

  public void setPushAppKey(String pushAppKey) {
    this.pushAppKey = pushAppKey;
  }


  public Integer getKeyUsedCarSourceId() {
    return keyUsedCarSourceId;
  }

  public void setKeyUsedCarSourceId(Integer keyUsedCarSourceId) {
    this.keyUsedCarSourceId = keyUsedCarSourceId;
  }

  public Integer getSourceId() {
    return sourceId;
  }

  public void setSourceId(Integer sourceId) {
    this.sourceId = sourceId;
  }

  public Integer getKeySupplyBusinessId() {
    return keySupplyBusinessId;
  }

  public void setKeySupplyBusinessId(Integer keySupplyBusinessId) {
    this.keySupplyBusinessId = keySupplyBusinessId;
  }

  public Integer getKeyOrderCityid() {
    return keyOrderCityid;
  }

  public void setKeyOrderCityid(Integer keyOrderCityid) {
    this.keyOrderCityid = keyOrderCityid;
  }
}
