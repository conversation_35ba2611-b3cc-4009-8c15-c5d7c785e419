package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class LeadBuyUseCarDailyRepoterDingding {
    private Integer id;

    private String dt;

    private String totalLeadSum;

    private String totalLeadChainratio;

    private String totalLeadChainratiodiff;

    private String totalLeadYearonyear;

    private String totalLeadYearonyeardiff;

    private String totalLeadMonthavg;

    private String appLeadSum;

    private String appLeadRate;

    private String appLeadChainratio;

    private String appLeadChainratiodiff;

    private String appLeadYearonyear;

    private String appLeadYearonyeardiff;

    private String appLeadMonthavg;

    private String miniprogramLeadSum;

    private String miniprogramLeadRate;

    private String miniprogramLeadChainratio;

    private String miniprogramLeadChainratiodiff;

    private String miniprogramLeadYearonyear;

    private String miniprogramLeadYearonyeardiff;

    private String miniprogramLeadMonthavg;

    private String liveLeadSum;

    private String liveLeadRate;

    private String liveLeadChainratio;

    private String liveLeadChainratiodiff;

    private String liveLeadYearonyear;

    private String liveLeadYearonyeardiff;

    private String liveLeadMonthavg;

    private String communityLeadSum;

    private String communityLeadRate;

    private String communityLeadChainratio;

    private String communityLeadChainratiodiff;

    private String communityLeadYearonyear;

    private String communityLeadYearonyeardiff;

    private String communityLeadMonthavg;

    private String hotchatLeadSum;

    private String hotchatLeadRate;

    private String hotchatLeadChainratio;

    private String hotchatLeadChainratiodiff;

    private String hotchatLeadYearonyear;

    private String hotchatLeadYearonyeardiff;

    private String hotchatLeadMonthavg;

    private String default0;

    private String default1;

    private String default2;

    private String default3;

    private String default4;

    private String default5;

    private String default6;

    private String default7;

    private String default8;

    private String default9;

    private Integer isDel;

    private Date createdStime;

    private Date modifiedStime;

    private String appLeadrateChainratiodiff;

    private String appLeadrateYearonyeardiff;

    private String miniprogramLeadrateChainratiodiff;

    private String miniprogramLeadrateYearonyeardiff;

    private String liveLeadrateChainratiodiff;

    private String liveLeadrateYearonyeardiff;

    private String communityLeadrateChainratiodiff;

    private String communityLeadrateYearonyeardiff;

    private String hotchatLeadrateChainratiodiff;

    private String hotchatLeadrateYearonyeardiff;

    private String pcLeadSum;

    private String pcLeadChainratio;

    private String pcLeadChainratiodiff;

    private String pcLeadYearonyear;

    private String pcLeadYearonyeardiff;

    private String pcLeadRate;

    private String pcLeadrateChainratiodiff;

    private String pcLeadrateYearonyeardiff;

    private String pcLeadMonthavg;

    private String pcLeadrateMonthavg;

    private String mLeadSum;

    private String mLeadChainratio;

    private String mLeadChainratiodiff;

    private String mLeadYearonyear;

    private String mLeadYearonyeardiff;

    private String mLeadRate;

    private String mLeadrateChainratiodiff;

    private String mLeadrateYearonyeardiff;

    private String mLeadMonthavg;

    private String mLeadrateMonthavg;

    private String appLeadrateMonthavg;

    private String miniprogramLeadrateMonthavg;

    private String liveLeadrateMonthavg;

    private String communityLeadrateMonthavg;

    private String hotchatLeadrateMonthavg;

    private String totalPhoneSum;
    private String totalPhoneChainRatio;
    private String totalPhoneChainRatioDiff;
    private String totalPhoneYearOnYear;
    private String totalPhoneYearOnYearDiff;
    private String totalPhoneMonthAvg;

    private String appPhoneSum;
    private String appPhoneRate;
    private String appPhoneChainRatio;
    private String appPhoneChainRatioDiff;
    private String appPhoneYearOnYear;
    private String appPhoneYearOnYearDiff;
    private String appPhoneMonthAvg;
    private String appPhoneRateChainRatioDiff;
    private String appPhoneRateYearOnYearDiff;
    private String appPhoneRateMonthAvg;

    private String miniprogramPhoneSum;
    private String miniprogramPhoneRate;
    private String miniprogramPhoneChainRatio;
    private String miniprogramPhoneChainRatioDiff;
    private String miniprogramPhoneYearOnYear;
    private String miniprogramPhoneYearOnYearDiff;
    private String miniprogramPhoneMonthAvg;
    private String miniprogramPhoneRateChainRatioDiff;
    private String miniprogramPhoneRateYearOnYearDiff;
    private String miniprogramPhoneRateMonthAvg;

    private String pcPhoneSum;
    private String pcPhoneRate;
    private String pcPhoneChainRatio;
    private String pcPhoneChainRatioDiff;
    private String pcPhoneYearOnYear;
    private String pcPhoneYearOnYearDiff;
    private String pcPhoneMonthAvg;
    private String pcPhoneRateChainRatioDiff;
    private String pcPhoneRateYearOnYearDiff;
    private String pcPhoneRateMonthAvg;

    private String mPhoneSum;
    private String mPhoneRate;
    private String mPhoneChainRatio;
    private String mPhoneChainRatioDiff;
    private String mPhoneYearOnYear;
    private String mPhoneYearOnYearDiff;
    private String mPhoneMonthAvg;
    private String mPhoneRateChainRatioDiff;
    private String mPhoneRateYearOnYearDiff;
    private String mPhoneRateMonthAvg;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt == null ? null : dt.trim();
    }

    public String getTotalLeadSum() {
        return totalLeadSum;
    }

    public void setTotalLeadSum(String totalLeadSum) {
        this.totalLeadSum = totalLeadSum == null ? null : totalLeadSum.trim();
    }

    public String getTotalLeadChainratio() {
        return totalLeadChainratio;
    }

    public void setTotalLeadChainratio(String totalLeadChainratio) {
        this.totalLeadChainratio = totalLeadChainratio == null ? null : totalLeadChainratio.trim();
    }

    public String getTotalLeadChainratiodiff() {
        return totalLeadChainratiodiff;
    }

    public void setTotalLeadChainratiodiff(String totalLeadChainratiodiff) {
        this.totalLeadChainratiodiff = totalLeadChainratiodiff == null ? null : totalLeadChainratiodiff.trim();
    }

    public String getTotalLeadYearonyear() {
        return totalLeadYearonyear;
    }

    public void setTotalLeadYearonyear(String totalLeadYearonyear) {
        this.totalLeadYearonyear = totalLeadYearonyear == null ? null : totalLeadYearonyear.trim();
    }

    public String getTotalLeadYearonyeardiff() {
        return totalLeadYearonyeardiff;
    }

    public void setTotalLeadYearonyeardiff(String totalLeadYearonyeardiff) {
        this.totalLeadYearonyeardiff = totalLeadYearonyeardiff == null ? null : totalLeadYearonyeardiff.trim();
    }

    public String getTotalLeadMonthavg() {
        return totalLeadMonthavg;
    }

    public void setTotalLeadMonthavg(String totalLeadMonthavg) {
        this.totalLeadMonthavg = totalLeadMonthavg == null ? null : totalLeadMonthavg.trim();
    }

    public String getAppLeadSum() {
        return appLeadSum;
    }

    public void setAppLeadSum(String appLeadSum) {
        this.appLeadSum = appLeadSum == null ? null : appLeadSum.trim();
    }

    public String getAppLeadRate() {
        return appLeadRate;
    }

    public void setAppLeadRate(String appLeadRate) {
        this.appLeadRate = appLeadRate == null ? null : appLeadRate.trim();
    }

    public String getAppLeadChainratio() {
        return appLeadChainratio;
    }

    public void setAppLeadChainratio(String appLeadChainratio) {
        this.appLeadChainratio = appLeadChainratio == null ? null : appLeadChainratio.trim();
    }

    public String getAppLeadChainratiodiff() {
        return appLeadChainratiodiff;
    }

    public void setAppLeadChainratiodiff(String appLeadChainratiodiff) {
        this.appLeadChainratiodiff = appLeadChainratiodiff == null ? null : appLeadChainratiodiff.trim();
    }

    public String getAppLeadYearonyear() {
        return appLeadYearonyear;
    }

    public void setAppLeadYearonyear(String appLeadYearonyear) {
        this.appLeadYearonyear = appLeadYearonyear == null ? null : appLeadYearonyear.trim();
    }

    public String getAppLeadYearonyeardiff() {
        return appLeadYearonyeardiff;
    }

    public void setAppLeadYearonyeardiff(String appLeadYearonyeardiff) {
        this.appLeadYearonyeardiff = appLeadYearonyeardiff == null ? null : appLeadYearonyeardiff.trim();
    }

    public String getAppLeadMonthavg() {
        return appLeadMonthavg;
    }

    public void setAppLeadMonthavg(String appLeadMonthavg) {
        this.appLeadMonthavg = appLeadMonthavg == null ? null : appLeadMonthavg.trim();
    }

    public String getMiniprogramLeadSum() {
        return miniprogramLeadSum;
    }

    public void setMiniprogramLeadSum(String miniprogramLeadSum) {
        this.miniprogramLeadSum = miniprogramLeadSum == null ? null : miniprogramLeadSum.trim();
    }

    public String getMiniprogramLeadRate() {
        return miniprogramLeadRate;
    }

    public void setMiniprogramLeadRate(String miniprogramLeadRate) {
        this.miniprogramLeadRate = miniprogramLeadRate == null ? null : miniprogramLeadRate.trim();
    }

    public String getMiniprogramLeadChainratio() {
        return miniprogramLeadChainratio;
    }

    public void setMiniprogramLeadChainratio(String miniprogramLeadChainratio) {
        this.miniprogramLeadChainratio = miniprogramLeadChainratio == null ? null : miniprogramLeadChainratio.trim();
    }

    public String getMiniprogramLeadChainratiodiff() {
        return miniprogramLeadChainratiodiff;
    }

    public void setMiniprogramLeadChainratiodiff(String miniprogramLeadChainratiodiff) {
        this.miniprogramLeadChainratiodiff = miniprogramLeadChainratiodiff == null ? null : miniprogramLeadChainratiodiff.trim();
    }

    public String getMiniprogramLeadYearonyear() {
        return miniprogramLeadYearonyear;
    }

    public void setMiniprogramLeadYearonyear(String miniprogramLeadYearonyear) {
        this.miniprogramLeadYearonyear = miniprogramLeadYearonyear == null ? null : miniprogramLeadYearonyear.trim();
    }

    public String getMiniprogramLeadYearonyeardiff() {
        return miniprogramLeadYearonyeardiff;
    }

    public void setMiniprogramLeadYearonyeardiff(String miniprogramLeadYearonyeardiff) {
        this.miniprogramLeadYearonyeardiff = miniprogramLeadYearonyeardiff == null ? null : miniprogramLeadYearonyeardiff.trim();
    }

    public String getMiniprogramLeadMonthavg() {
        return miniprogramLeadMonthavg;
    }

    public void setMiniprogramLeadMonthavg(String miniprogramLeadMonthavg) {
        this.miniprogramLeadMonthavg = miniprogramLeadMonthavg == null ? null : miniprogramLeadMonthavg.trim();
    }

    public String getLiveLeadSum() {
        return liveLeadSum;
    }

    public void setLiveLeadSum(String liveLeadSum) {
        this.liveLeadSum = liveLeadSum == null ? null : liveLeadSum.trim();
    }

    public String getLiveLeadRate() {
        return liveLeadRate;
    }

    public void setLiveLeadRate(String liveLeadRate) {
        this.liveLeadRate = liveLeadRate == null ? null : liveLeadRate.trim();
    }

    public String getLiveLeadChainratio() {
        return liveLeadChainratio;
    }

    public void setLiveLeadChainratio(String liveLeadChainratio) {
        this.liveLeadChainratio = liveLeadChainratio == null ? null : liveLeadChainratio.trim();
    }

    public String getLiveLeadChainratiodiff() {
        return liveLeadChainratiodiff;
    }

    public void setLiveLeadChainratiodiff(String liveLeadChainratiodiff) {
        this.liveLeadChainratiodiff = liveLeadChainratiodiff == null ? null : liveLeadChainratiodiff.trim();
    }

    public String getLiveLeadYearonyear() {
        return liveLeadYearonyear;
    }

    public void setLiveLeadYearonyear(String liveLeadYearonyear) {
        this.liveLeadYearonyear = liveLeadYearonyear == null ? null : liveLeadYearonyear.trim();
    }

    public String getLiveLeadYearonyeardiff() {
        return liveLeadYearonyeardiff;
    }

    public void setLiveLeadYearonyeardiff(String liveLeadYearonyeardiff) {
        this.liveLeadYearonyeardiff = liveLeadYearonyeardiff == null ? null : liveLeadYearonyeardiff.trim();
    }

    public String getLiveLeadMonthavg() {
        return liveLeadMonthavg;
    }

    public void setLiveLeadMonthavg(String liveLeadMonthavg) {
        this.liveLeadMonthavg = liveLeadMonthavg == null ? null : liveLeadMonthavg.trim();
    }

    public String getCommunityLeadSum() {
        return communityLeadSum;
    }

    public void setCommunityLeadSum(String communityLeadSum) {
        this.communityLeadSum = communityLeadSum == null ? null : communityLeadSum.trim();
    }

    public String getCommunityLeadRate() {
        return communityLeadRate;
    }

    public void setCommunityLeadRate(String communityLeadRate) {
        this.communityLeadRate = communityLeadRate == null ? null : communityLeadRate.trim();
    }

    public String getCommunityLeadChainratio() {
        return communityLeadChainratio;
    }

    public void setCommunityLeadChainratio(String communityLeadChainratio) {
        this.communityLeadChainratio = communityLeadChainratio == null ? null : communityLeadChainratio.trim();
    }

    public String getCommunityLeadChainratiodiff() {
        return communityLeadChainratiodiff;
    }

    public void setCommunityLeadChainratiodiff(String communityLeadChainratiodiff) {
        this.communityLeadChainratiodiff = communityLeadChainratiodiff == null ? null : communityLeadChainratiodiff.trim();
    }

    public String getCommunityLeadYearonyear() {
        return communityLeadYearonyear;
    }

    public void setCommunityLeadYearonyear(String communityLeadYearonyear) {
        this.communityLeadYearonyear = communityLeadYearonyear == null ? null : communityLeadYearonyear.trim();
    }

    public String getCommunityLeadYearonyeardiff() {
        return communityLeadYearonyeardiff;
    }

    public void setCommunityLeadYearonyeardiff(String communityLeadYearonyeardiff) {
        this.communityLeadYearonyeardiff = communityLeadYearonyeardiff == null ? null : communityLeadYearonyeardiff.trim();
    }

    public String getCommunityLeadMonthavg() {
        return communityLeadMonthavg;
    }

    public void setCommunityLeadMonthavg(String communityLeadMonthavg) {
        this.communityLeadMonthavg = communityLeadMonthavg == null ? null : communityLeadMonthavg.trim();
    }

    public String getHotchatLeadSum() {
        return hotchatLeadSum;
    }

    public void setHotchatLeadSum(String hotchatLeadSum) {
        this.hotchatLeadSum = hotchatLeadSum == null ? null : hotchatLeadSum.trim();
    }

    public String getHotchatLeadRate() {
        return hotchatLeadRate;
    }

    public void setHotchatLeadRate(String hotchatLeadRate) {
        this.hotchatLeadRate = hotchatLeadRate == null ? null : hotchatLeadRate.trim();
    }

    public String getHotchatLeadChainratio() {
        return hotchatLeadChainratio;
    }

    public void setHotchatLeadChainratio(String hotchatLeadChainratio) {
        this.hotchatLeadChainratio = hotchatLeadChainratio == null ? null : hotchatLeadChainratio.trim();
    }

    public String getHotchatLeadChainratiodiff() {
        return hotchatLeadChainratiodiff;
    }

    public void setHotchatLeadChainratiodiff(String hotchatLeadChainratiodiff) {
        this.hotchatLeadChainratiodiff = hotchatLeadChainratiodiff == null ? null : hotchatLeadChainratiodiff.trim();
    }

    public String getHotchatLeadYearonyear() {
        return hotchatLeadYearonyear;
    }

    public void setHotchatLeadYearonyear(String hotchatLeadYearonyear) {
        this.hotchatLeadYearonyear = hotchatLeadYearonyear == null ? null : hotchatLeadYearonyear.trim();
    }

    public String getHotchatLeadYearonyeardiff() {
        return hotchatLeadYearonyeardiff;
    }

    public void setHotchatLeadYearonyeardiff(String hotchatLeadYearonyeardiff) {
        this.hotchatLeadYearonyeardiff = hotchatLeadYearonyeardiff == null ? null : hotchatLeadYearonyeardiff.trim();
    }

    public String getHotchatLeadMonthavg() {
        return hotchatLeadMonthavg;
    }

    public void setHotchatLeadMonthavg(String hotchatLeadMonthavg) {
        this.hotchatLeadMonthavg = hotchatLeadMonthavg == null ? null : hotchatLeadMonthavg.trim();
    }

    public String getDefault0() {
        return default0;
    }

    public void setDefault0(String default0) {
        this.default0 = default0 == null ? null : default0.trim();
    }

    public String getDefault1() {
        return default1;
    }

    public void setDefault1(String default1) {
        this.default1 = default1 == null ? null : default1.trim();
    }

    public String getDefault2() {
        return default2;
    }

    public void setDefault2(String default2) {
        this.default2 = default2 == null ? null : default2.trim();
    }

    public String getDefault3() {
        return default3;
    }

    public void setDefault3(String default3) {
        this.default3 = default3 == null ? null : default3.trim();
    }

    public String getDefault4() {
        return default4;
    }

    public void setDefault4(String default4) {
        this.default4 = default4 == null ? null : default4.trim();
    }

    public String getDefault5() {
        return default5;
    }

    public void setDefault5(String default5) {
        this.default5 = default5 == null ? null : default5.trim();
    }

    public String getDefault6() {
        return default6;
    }

    public void setDefault6(String default6) {
        this.default6 = default6 == null ? null : default6.trim();
    }

    public String getDefault7() {
        return default7;
    }

    public void setDefault7(String default7) {
        this.default7 = default7 == null ? null : default7.trim();
    }

    public String getDefault8() {
        return default8;
    }

    public void setDefault8(String default8) {
        this.default8 = default8 == null ? null : default8.trim();
    }

    public String getDefault9() {
        return default9;
    }

    public void setDefault9(String default9) {
        this.default9 = default9 == null ? null : default9.trim();
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public String getAppLeadrateChainratiodiff() {
        return appLeadrateChainratiodiff;
    }

    public void setAppLeadrateChainratiodiff(String appLeadrateChainratiodiff) {
        this.appLeadrateChainratiodiff = appLeadrateChainratiodiff == null ? null : appLeadrateChainratiodiff.trim();
    }

    public String getAppLeadrateYearonyeardiff() {
        return appLeadrateYearonyeardiff;
    }

    public void setAppLeadrateYearonyeardiff(String appLeadrateYearonyeardiff) {
        this.appLeadrateYearonyeardiff = appLeadrateYearonyeardiff == null ? null : appLeadrateYearonyeardiff.trim();
    }

    public String getMiniprogramLeadrateChainratiodiff() {
        return miniprogramLeadrateChainratiodiff;
    }

    public void setMiniprogramLeadrateChainratiodiff(String miniprogramLeadrateChainratiodiff) {
        this.miniprogramLeadrateChainratiodiff = miniprogramLeadrateChainratiodiff == null ? null : miniprogramLeadrateChainratiodiff.trim();
    }

    public String getMiniprogramLeadrateYearonyeardiff() {
        return miniprogramLeadrateYearonyeardiff;
    }

    public void setMiniprogramLeadrateYearonyeardiff(String miniprogramLeadrateYearonyeardiff) {
        this.miniprogramLeadrateYearonyeardiff = miniprogramLeadrateYearonyeardiff == null ? null : miniprogramLeadrateYearonyeardiff.trim();
    }

    public String getLiveLeadrateChainratiodiff() {
        return liveLeadrateChainratiodiff;
    }

    public void setLiveLeadrateChainratiodiff(String liveLeadrateChainratiodiff) {
        this.liveLeadrateChainratiodiff = liveLeadrateChainratiodiff == null ? null : liveLeadrateChainratiodiff.trim();
    }

    public String getLiveLeadrateYearonyeardiff() {
        return liveLeadrateYearonyeardiff;
    }

    public void setLiveLeadrateYearonyeardiff(String liveLeadrateYearonyeardiff) {
        this.liveLeadrateYearonyeardiff = liveLeadrateYearonyeardiff == null ? null : liveLeadrateYearonyeardiff.trim();
    }

    public String getCommunityLeadrateChainratiodiff() {
        return communityLeadrateChainratiodiff;
    }

    public void setCommunityLeadrateChainratiodiff(String communityLeadrateChainratiodiff) {
        this.communityLeadrateChainratiodiff = communityLeadrateChainratiodiff == null ? null : communityLeadrateChainratiodiff.trim();
    }

    public String getCommunityLeadrateYearonyeardiff() {
        return communityLeadrateYearonyeardiff;
    }

    public void setCommunityLeadrateYearonyeardiff(String communityLeadrateYearonyeardiff) {
        this.communityLeadrateYearonyeardiff = communityLeadrateYearonyeardiff == null ? null : communityLeadrateYearonyeardiff.trim();
    }

    public String getHotchatLeadrateChainratiodiff() {
        return hotchatLeadrateChainratiodiff;
    }

    public void setHotchatLeadrateChainratiodiff(String hotchatLeadrateChainratiodiff) {
        this.hotchatLeadrateChainratiodiff = hotchatLeadrateChainratiodiff == null ? null : hotchatLeadrateChainratiodiff.trim();
    }

    public String getHotchatLeadrateYearonyeardiff() {
        return hotchatLeadrateYearonyeardiff;
    }

    public void setHotchatLeadrateYearonyeardiff(String hotchatLeadrateYearonyeardiff) {
        this.hotchatLeadrateYearonyeardiff = hotchatLeadrateYearonyeardiff == null ? null : hotchatLeadrateYearonyeardiff.trim();
    }

    public String getPcLeadSum() {
        return pcLeadSum;
    }

    public void setPcLeadSum(String pcLeadSum) {
        this.pcLeadSum = pcLeadSum == null ? null : pcLeadSum.trim();
    }

    public String getPcLeadChainratio() {
        return pcLeadChainratio;
    }

    public void setPcLeadChainratio(String pcLeadChainratio) {
        this.pcLeadChainratio = pcLeadChainratio == null ? null : pcLeadChainratio.trim();
    }

    public String getPcLeadChainratiodiff() {
        return pcLeadChainratiodiff;
    }

    public void setPcLeadChainratiodiff(String pcLeadChainratiodiff) {
        this.pcLeadChainratiodiff = pcLeadChainratiodiff == null ? null : pcLeadChainratiodiff.trim();
    }

    public String getPcLeadYearonyear() {
        return pcLeadYearonyear;
    }

    public void setPcLeadYearonyear(String pcLeadYearonyear) {
        this.pcLeadYearonyear = pcLeadYearonyear == null ? null : pcLeadYearonyear.trim();
    }

    public String getPcLeadYearonyeardiff() {
        return pcLeadYearonyeardiff;
    }

    public void setPcLeadYearonyeardiff(String pcLeadYearonyeardiff) {
        this.pcLeadYearonyeardiff = pcLeadYearonyeardiff == null ? null : pcLeadYearonyeardiff.trim();
    }

    public String getPcLeadRate() {
        return pcLeadRate;
    }

    public void setPcLeadRate(String pcLeadRate) {
        this.pcLeadRate = pcLeadRate == null ? null : pcLeadRate.trim();
    }

    public String getPcLeadrateChainratiodiff() {
        return pcLeadrateChainratiodiff;
    }

    public void setPcLeadrateChainratiodiff(String pcLeadrateChainratiodiff) {
        this.pcLeadrateChainratiodiff = pcLeadrateChainratiodiff == null ? null : pcLeadrateChainratiodiff.trim();
    }

    public String getPcLeadrateYearonyeardiff() {
        return pcLeadrateYearonyeardiff;
    }

    public void setPcLeadrateYearonyeardiff(String pcLeadrateYearonyeardiff) {
        this.pcLeadrateYearonyeardiff = pcLeadrateYearonyeardiff == null ? null : pcLeadrateYearonyeardiff.trim();
    }

    public String getPcLeadMonthavg() {
        return pcLeadMonthavg;
    }

    public void setPcLeadMonthavg(String pcLeadMonthavg) {
        this.pcLeadMonthavg = pcLeadMonthavg == null ? null : pcLeadMonthavg.trim();
    }

    public String getPcLeadrateMonthavg() {
        return pcLeadrateMonthavg;
    }

    public void setPcLeadrateMonthavg(String pcLeadrateMonthavg) {
        this.pcLeadrateMonthavg = pcLeadrateMonthavg == null ? null : pcLeadrateMonthavg.trim();
    }

    public String getmLeadSum() {
        return mLeadSum;
    }

    public void setmLeadSum(String mLeadSum) {
        this.mLeadSum = mLeadSum == null ? null : mLeadSum.trim();
    }

    public String getmLeadChainratio() {
        return mLeadChainratio;
    }

    public void setmLeadChainratio(String mLeadChainratio) {
        this.mLeadChainratio = mLeadChainratio == null ? null : mLeadChainratio.trim();
    }

    public String getmLeadChainratiodiff() {
        return mLeadChainratiodiff;
    }

    public void setmLeadChainratiodiff(String mLeadChainratiodiff) {
        this.mLeadChainratiodiff = mLeadChainratiodiff == null ? null : mLeadChainratiodiff.trim();
    }

    public String getmLeadYearonyear() {
        return mLeadYearonyear;
    }

    public void setmLeadYearonyear(String mLeadYearonyear) {
        this.mLeadYearonyear = mLeadYearonyear == null ? null : mLeadYearonyear.trim();
    }

    public String getmLeadYearonyeardiff() {
        return mLeadYearonyeardiff;
    }

    public void setmLeadYearonyeardiff(String mLeadYearonyeardiff) {
        this.mLeadYearonyeardiff = mLeadYearonyeardiff == null ? null : mLeadYearonyeardiff.trim();
    }

    public String getmLeadRate() {
        return mLeadRate;
    }

    public void setmLeadRate(String mLeadRate) {
        this.mLeadRate = mLeadRate == null ? null : mLeadRate.trim();
    }

    public String getmLeadrateChainratiodiff() {
        return mLeadrateChainratiodiff;
    }

    public void setmLeadrateChainratiodiff(String mLeadrateChainratiodiff) {
        this.mLeadrateChainratiodiff = mLeadrateChainratiodiff == null ? null : mLeadrateChainratiodiff.trim();
    }

    public String getmLeadrateYearonyeardiff() {
        return mLeadrateYearonyeardiff;
    }

    public void setmLeadrateYearonyeardiff(String mLeadrateYearonyeardiff) {
        this.mLeadrateYearonyeardiff = mLeadrateYearonyeardiff == null ? null : mLeadrateYearonyeardiff.trim();
    }

    public String getmLeadMonthavg() {
        return mLeadMonthavg;
    }

    public void setmLeadMonthavg(String mLeadMonthavg) {
        this.mLeadMonthavg = mLeadMonthavg == null ? null : mLeadMonthavg.trim();
    }

    public String getmLeadrateMonthavg() {
        return mLeadrateMonthavg;
    }

    public void setmLeadrateMonthavg(String mLeadrateMonthavg) {
        this.mLeadrateMonthavg = mLeadrateMonthavg == null ? null : mLeadrateMonthavg.trim();
    }

    public String getAppLeadrateMonthavg() {
        return appLeadrateMonthavg;
    }

    public void setAppLeadrateMonthavg(String appLeadrateMonthavg) {
        this.appLeadrateMonthavg = appLeadrateMonthavg == null ? null : appLeadrateMonthavg.trim();
    }

    public String getMiniprogramLeadrateMonthavg() {
        return miniprogramLeadrateMonthavg;
    }

    public void setMiniprogramLeadrateMonthavg(String miniprogramLeadrateMonthavg) {
        this.miniprogramLeadrateMonthavg = miniprogramLeadrateMonthavg == null ? null : miniprogramLeadrateMonthavg.trim();
    }

    public String getLiveLeadrateMonthavg() {
        return liveLeadrateMonthavg;
    }

    public void setLiveLeadrateMonthavg(String liveLeadrateMonthavg) {
        this.liveLeadrateMonthavg = liveLeadrateMonthavg == null ? null : liveLeadrateMonthavg.trim();
    }

    public String getCommunityLeadrateMonthavg() {
        return communityLeadrateMonthavg;
    }

    public void setCommunityLeadrateMonthavg(String communityLeadrateMonthavg) {
        this.communityLeadrateMonthavg = communityLeadrateMonthavg == null ? null : communityLeadrateMonthavg.trim();
    }

    public String getHotchatLeadrateMonthavg() {
        return hotchatLeadrateMonthavg;
    }

    public void setHotchatLeadrateMonthavg(String hotchatLeadrateMonthavg) {
        this.hotchatLeadrateMonthavg = hotchatLeadrateMonthavg == null ? null : hotchatLeadrateMonthavg.trim();
    }

    public String getTotalPhoneSum() {
        return totalPhoneSum;
    }

    public void setTotalPhoneSum(String totalPhoneSum) {
        this.totalPhoneSum = totalPhoneSum;
    }

    public String getTotalPhoneChainRatio() {
        return totalPhoneChainRatio;
    }

    public void setTotalPhoneChainRatio(String totalPhoneChainRatio) {
        this.totalPhoneChainRatio = totalPhoneChainRatio;
    }

    public String getTotalPhoneChainRatioDiff() {
        return totalPhoneChainRatioDiff;
    }

    public void setTotalPhoneChainRatioDiff(String totalPhoneChainRatioDiff) {
        this.totalPhoneChainRatioDiff = totalPhoneChainRatioDiff;
    }

    public String getTotalPhoneYearOnYear() {
        return totalPhoneYearOnYear;
    }

    public void setTotalPhoneYearOnYear(String totalPhoneYearOnYear) {
        this.totalPhoneYearOnYear = totalPhoneYearOnYear;
    }

    public String getTotalPhoneYearOnYearDiff() {
        return totalPhoneYearOnYearDiff;
    }

    public void setTotalPhoneYearOnYearDiff(String totalPhoneYearOnYearDiff) {
        this.totalPhoneYearOnYearDiff = totalPhoneYearOnYearDiff;
    }

    public String getTotalPhoneMonthAvg() {
        return totalPhoneMonthAvg;
    }

    public void setTotalPhoneMonthAvg(String totalPhoneMonthAvg) {
        this.totalPhoneMonthAvg = totalPhoneMonthAvg;
    }

    public String getAppPhoneSum() {
        return appPhoneSum;
    }

    public void setAppPhoneSum(String appPhoneSum) {
        this.appPhoneSum = appPhoneSum;
    }

    public String getAppPhoneRate() {
        return appPhoneRate;
    }

    public void setAppPhoneRate(String appPhoneRate) {
        this.appPhoneRate = appPhoneRate;
    }

    public String getAppPhoneChainRatio() {
        return appPhoneChainRatio;
    }

    public void setAppPhoneChainRatio(String appPhoneChainRatio) {
        this.appPhoneChainRatio = appPhoneChainRatio;
    }

    public String getAppPhoneChainRatioDiff() {
        return appPhoneChainRatioDiff;
    }

    public void setAppPhoneChainRatioDiff(String appPhoneChainRatioDiff) {
        this.appPhoneChainRatioDiff = appPhoneChainRatioDiff;
    }

    public String getAppPhoneYearOnYear() {
        return appPhoneYearOnYear;
    }

    public void setAppPhoneYearOnYear(String appPhoneYearOnYear) {
        this.appPhoneYearOnYear = appPhoneYearOnYear;
    }

    public String getAppPhoneYearOnYearDiff() {
        return appPhoneYearOnYearDiff;
    }

    public void setAppPhoneYearOnYearDiff(String appPhoneYearOnYearDiff) {
        this.appPhoneYearOnYearDiff = appPhoneYearOnYearDiff;
    }

    public String getAppPhoneMonthAvg() {
        return appPhoneMonthAvg;
    }

    public void setAppPhoneMonthAvg(String appPhoneMonthAvg) {
        this.appPhoneMonthAvg = appPhoneMonthAvg;
    }

    public String getAppPhoneRateChainRatioDiff() {
        return appPhoneRateChainRatioDiff;
    }

    public void setAppPhoneRateChainRatioDiff(String appPhoneRateChainRatioDiff) {
        this.appPhoneRateChainRatioDiff = appPhoneRateChainRatioDiff;
    }

    public String getAppPhoneRateYearOnYearDiff() {
        return appPhoneRateYearOnYearDiff;
    }

    public void setAppPhoneRateYearOnYearDiff(String appPhoneRateYearOnYearDiff) {
        this.appPhoneRateYearOnYearDiff = appPhoneRateYearOnYearDiff;
    }

    public String getAppPhoneRateMonthAvg() {
        return appPhoneRateMonthAvg;
    }

    public void setAppPhoneRateMonthAvg(String appPhoneRateMonthAvg) {
        this.appPhoneRateMonthAvg = appPhoneRateMonthAvg;
    }

    public String getMiniprogramPhoneSum() {
        return miniprogramPhoneSum;
    }

    public void setMiniprogramPhoneSum(String miniprogramPhoneSum) {
        this.miniprogramPhoneSum = miniprogramPhoneSum;
    }

    public String getMiniprogramPhoneRate() {
        return miniprogramPhoneRate;
    }

    public void setMiniprogramPhoneRate(String miniprogramPhoneRate) {
        this.miniprogramPhoneRate = miniprogramPhoneRate;
    }

    public String getMiniprogramPhoneChainRatio() {
        return miniprogramPhoneChainRatio;
    }

    public void setMiniprogramPhoneChainRatio(String miniprogramPhoneChainRatio) {
        this.miniprogramPhoneChainRatio = miniprogramPhoneChainRatio;
    }

    public String getMiniprogramPhoneChainRatioDiff() {
        return miniprogramPhoneChainRatioDiff;
    }

    public void setMiniprogramPhoneChainRatioDiff(String miniprogramPhoneChainRatioDiff) {
        this.miniprogramPhoneChainRatioDiff = miniprogramPhoneChainRatioDiff;
    }

    public String getMiniprogramPhoneYearOnYear() {
        return miniprogramPhoneYearOnYear;
    }

    public void setMiniprogramPhoneYearOnYear(String miniprogramPhoneYearOnYear) {
        this.miniprogramPhoneYearOnYear = miniprogramPhoneYearOnYear;
    }

    public String getMiniprogramPhoneYearOnYearDiff() {
        return miniprogramPhoneYearOnYearDiff;
    }

    public void setMiniprogramPhoneYearOnYearDiff(String miniprogramPhoneYearOnYearDiff) {
        this.miniprogramPhoneYearOnYearDiff = miniprogramPhoneYearOnYearDiff;
    }

    public String getMiniprogramPhoneMonthAvg() {
        return miniprogramPhoneMonthAvg;
    }

    public void setMiniprogramPhoneMonthAvg(String miniprogramPhoneMonthAvg) {
        this.miniprogramPhoneMonthAvg = miniprogramPhoneMonthAvg;
    }

    public String getMiniprogramPhoneRateChainRatioDiff() {
        return miniprogramPhoneRateChainRatioDiff;
    }

    public void setMiniprogramPhoneRateChainRatioDiff(String miniprogramPhoneRateChainRatioDiff) {
        this.miniprogramPhoneRateChainRatioDiff = miniprogramPhoneRateChainRatioDiff;
    }

    public String getMiniprogramPhoneRateYearOnYearDiff() {
        return miniprogramPhoneRateYearOnYearDiff;
    }

    public void setMiniprogramPhoneRateYearOnYearDiff(String miniprogramPhoneRateYearOnYearDiff) {
        this.miniprogramPhoneRateYearOnYearDiff = miniprogramPhoneRateYearOnYearDiff;
    }

    public String getMiniprogramPhoneRateMonthAvg() {
        return miniprogramPhoneRateMonthAvg;
    }

    public void setMiniprogramPhoneRateMonthAvg(String miniprogramPhoneRateMonthAvg) {
        this.miniprogramPhoneRateMonthAvg = miniprogramPhoneRateMonthAvg;
    }

    public String getPcPhoneSum() {
        return pcPhoneSum;
    }

    public void setPcPhoneSum(String pcPhoneSum) {
        this.pcPhoneSum = pcPhoneSum;
    }

    public String getPcPhoneRate() {
        return pcPhoneRate;
    }

    public void setPcPhoneRate(String pcPhoneRate) {
        this.pcPhoneRate = pcPhoneRate;
    }

    public String getPcPhoneChainRatio() {
        return pcPhoneChainRatio;
    }

    public void setPcPhoneChainRatio(String pcPhoneChainRatio) {
        this.pcPhoneChainRatio = pcPhoneChainRatio;
    }

    public String getPcPhoneChainRatioDiff() {
        return pcPhoneChainRatioDiff;
    }

    public void setPcPhoneChainRatioDiff(String pcPhoneChainRatioDiff) {
        this.pcPhoneChainRatioDiff = pcPhoneChainRatioDiff;
    }

    public String getPcPhoneYearOnYear() {
        return pcPhoneYearOnYear;
    }

    public void setPcPhoneYearOnYear(String pcPhoneYearOnYear) {
        this.pcPhoneYearOnYear = pcPhoneYearOnYear;
    }

    public String getPcPhoneYearOnYearDiff() {
        return pcPhoneYearOnYearDiff;
    }

    public void setPcPhoneYearOnYearDiff(String pcPhoneYearOnYearDiff) {
        this.pcPhoneYearOnYearDiff = pcPhoneYearOnYearDiff;
    }

    public String getPcPhoneMonthAvg() {
        return pcPhoneMonthAvg;
    }

    public void setPcPhoneMonthAvg(String pcPhoneMonthAvg) {
        this.pcPhoneMonthAvg = pcPhoneMonthAvg;
    }

    public String getPcPhoneRateChainRatioDiff() {
        return pcPhoneRateChainRatioDiff;
    }

    public void setPcPhoneRateChainRatioDiff(String pcPhoneRateChainRatioDiff) {
        this.pcPhoneRateChainRatioDiff = pcPhoneRateChainRatioDiff;
    }

    public String getPcPhoneRateYearOnYearDiff() {
        return pcPhoneRateYearOnYearDiff;
    }

    public void setPcPhoneRateYearOnYearDiff(String pcPhoneRateYearOnYearDiff) {
        this.pcPhoneRateYearOnYearDiff = pcPhoneRateYearOnYearDiff;
    }

    public String getPcPhoneRateMonthAvg() {
        return pcPhoneRateMonthAvg;
    }

    public void setPcPhoneRateMonthAvg(String pcPhoneRateMonthAvg) {
        this.pcPhoneRateMonthAvg = pcPhoneRateMonthAvg;
    }

    public String getmPhoneSum() {
        return mPhoneSum;
    }

    public void setmPhoneSum(String mPhoneSum) {
        this.mPhoneSum = mPhoneSum;
    }

    public String getmPhoneRate() {
        return mPhoneRate;
    }

    public void setmPhoneRate(String mPhoneRate) {
        this.mPhoneRate = mPhoneRate;
    }

    public String getmPhoneChainRatio() {
        return mPhoneChainRatio;
    }

    public void setmPhoneChainRatio(String mPhoneChainRatio) {
        this.mPhoneChainRatio = mPhoneChainRatio;
    }

    public String getmPhoneChainRatioDiff() {
        return mPhoneChainRatioDiff;
    }

    public void setmPhoneChainRatioDiff(String mPhoneChainRatioDiff) {
        this.mPhoneChainRatioDiff = mPhoneChainRatioDiff;
    }

    public String getmPhoneYearOnYear() {
        return mPhoneYearOnYear;
    }

    public void setmPhoneYearOnYear(String mPhoneYearOnYear) {
        this.mPhoneYearOnYear = mPhoneYearOnYear;
    }

    public String getmPhoneYearOnYearDiff() {
        return mPhoneYearOnYearDiff;
    }

    public void setmPhoneYearOnYearDiff(String mPhoneYearOnYearDiff) {
        this.mPhoneYearOnYearDiff = mPhoneYearOnYearDiff;
    }

    public String getmPhoneMonthAvg() {
        return mPhoneMonthAvg;
    }

    public void setmPhoneMonthAvg(String mPhoneMonthAvg) {
        this.mPhoneMonthAvg = mPhoneMonthAvg;
    }

    public String getmPhoneRateChainRatioDiff() {
        return mPhoneRateChainRatioDiff;
    }

    public void setmPhoneRateChainRatioDiff(String mPhoneRateChainRatioDiff) {
        this.mPhoneRateChainRatioDiff = mPhoneRateChainRatioDiff;
    }

    public String getmPhoneRateYearOnYearDiff() {
        return mPhoneRateYearOnYearDiff;
    }

    public void setmPhoneRateYearOnYearDiff(String mPhoneRateYearOnYearDiff) {
        this.mPhoneRateYearOnYearDiff = mPhoneRateYearOnYearDiff;
    }

    public String getmPhoneRateMonthAvg() {
        return mPhoneRateMonthAvg;
    }

    public void setmPhoneRateMonthAvg(String mPhoneRateMonthAvg) {
        this.mPhoneRateMonthAvg = mPhoneRateMonthAvg;
    }
}