package com.autohome.applet.dao.javaapi.model.wxapp;


import com.autohome.applet.dao.javaapi.model.wxapp.topic.ContentCard;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

/**
 * 详情页Model
 * Created by pengy<PERSON> on 2016/12/14.
 */
public class DetailModel {
    private int id;
    private String title;
    private String headerImg;
    private String name;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date publishTime;
    private List<ContentCard> contentCards;
    private String contentHtml;
    private String Img;
    private int likeCount;
    private int replyCount;
    private Video video;
    private int memberId;
    private int bbsid;
    private String bbs;
    private List<KoubeiInfo> koubei;
    private int liveroomId;
    private int ispoll;
    private String url;
    private String tsource;
    private int tdelete;

    private int tispoll;

    private String subtitle;

    private int views;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getIspoll() {
        return ispoll;
    }

    public void setIspoll(int ispoll) {
        this.ispoll = ispoll;
    }

    public int getLiveroomId() {
        return liveroomId;
    }

    public void setLiveroomId(int liveroomId) {
        this.liveroomId = liveroomId;
    }

    public String getImg() {
        return Img;
    }

    public void setImg(String img) {
        Img = img;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public List<ContentCard> getContentCards() {
        return contentCards;
    }

    public void setContentCards(List<ContentCard> contentCards) {
        this.contentCards = contentCards;
    }

    public String getContentHtml() {
        return contentHtml;
    }

    public void setContentHtml(String contentHtml) {
        this.contentHtml = contentHtml;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public int getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(int replyCount) {
        this.replyCount = replyCount;
    }

    public Video getVideo() {
        return video;
    }

    public void setVideo(Video video) {
        this.video = video;
    }

    public int getMemberId() {
        return memberId;
    }

    public void setMemberId(int memberId) {
        this.memberId = memberId;
    }

    public int getBbsid() {
        return bbsid;
    }

    public void setBbsid(int bbsid) {
        this.bbsid = bbsid;
    }

    public String getBbs() {
        return bbs;
    }

    public void setBbs(String bbs) {
        this.bbs = bbs;
    }

    public List<KoubeiInfo> getKoubei() {
        return koubei;
    }

    public void setKoubei(List<KoubeiInfo> koubei) {
        this.koubei = koubei;
    }

    public int getViews() {
        return views;
    }

    public void setViews(int views) {
        this.views = views;
    }

    public static class Video {
        private String img;
        private String playUrl;

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getPlayUrl() {
            return playUrl;
        }

        public void setPlayUrl(String playUrl) {
            this.playUrl = playUrl;
        }
    }

    public static class KoubeiInfo {
        public KoubeiInfo() {
        }

        public KoubeiInfo(String key, String value) {
            this.key = key;
            this.value = value;
        }

        private String key;
        private String value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }


    }

    public String getPubprovincename() {
        return pubprovincename;
    }

    public void setPubprovincename(String pubprovincename) {
        this.pubprovincename = pubprovincename;
    }

    private String pubprovincename;

    public String getTsource() {
        return tsource;
    }

    public void setTsource(String tsource) {
        this.tsource = tsource;
    }

    public int getTdelete() {
        return tdelete;
    }

    public void setTdelete(int tdelete) {
        this.tdelete = tdelete;
    }

    public int getTispoll() {
        return tispoll;
    }

    public void setTispoll(int tispoll) {
        this.tispoll = tispoll;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }
}

