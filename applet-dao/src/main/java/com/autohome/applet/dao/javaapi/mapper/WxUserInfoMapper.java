package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.WxUserInfo;
import com.autohome.applet.dao.javaapi.model.WxUserInfoExample;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
@Mapper
@DS("db-appletbigdata")
@Repository
public interface WxUserInfoMapper {
    long countByExample(WxUserInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WxUserInfo row);

    int insertSelective(WxUserInfo row);

    List<WxUserInfo> selectByExample(WxUserInfoExample example);

    List<WxUserInfo> selectAll();

    WxUserInfo selectByPrimaryKey(Long id);

    WxUserInfo selectByOpenid(String openid);

    int updateByPrimaryKeySelective(WxUserInfo row);

    int updateByPrimaryKey(WxUserInfo row);
}