package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.AppletUserbehaviorhistorySeries;
import com.autohome.applet.dao.javaapi.model.donate.DonateDaoQuery;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-appletbigdata")
@Repository
public interface AppletUserbehaviorhistorySeriesMapper {
    /**
     * 分页查询设备号id
     * */
    List<AppletUserbehaviorhistorySeries> listByQuery(DonateDaoQuery donateQuery);
}