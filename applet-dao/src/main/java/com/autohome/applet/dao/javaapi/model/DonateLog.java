package com.autohome.applet.dao.javaapi.model;

import java.math.BigDecimal;
import java.util.Date;

public class DonateLog {
    private Long id;

    private String daily;

    //1:华为, 2:苹果
    private Integer donateType;

    private String uniqueldentifier;

    private String imageid;

    private String describe;

    private String title;

    private String keyWords;

    private String logoUrl;

    private String groupId;

    private String dataUrl;

    private Date metadataModificationDate;

    private String activityType;

    private BigDecimal rankingHint;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    /**
     * "01",产品库车系页；
     * "06",原创图文；
     * "07",原创视频；
     * "12",车家号长文；
     * "13",车家号图文；
     * "14",车家号视频；
     * "20",销量排行；
     * "21",选车页；
     * "22",现金补贴；
     * */
    private String viewMark;

    /**
     * "01",历史内容；
     * "02",推荐内容；
     * */
    private String dataSource;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDaily() {
        return daily == null ? "" : daily;
    }

    public void setDaily(String daily) {
        this.daily = daily == null ? "" : daily.trim();
    }

    public Integer getDonateType() {
        return donateType == null ? 0 : donateType;
    }

    public void setDonateType(Integer donateType) {
        this.donateType = donateType == null ? 0 : donateType;
    }

    public String getUniqueldentifier() {
        return uniqueldentifier == null ? "" : uniqueldentifier;
    }

    public void setUniqueldentifier(String uniqueldentifier) {
        this.uniqueldentifier = uniqueldentifier == null ? null : uniqueldentifier.trim();
    }

    public String getImageid() {
        return imageid == null ? "" : imageid;
    }

    public void setImageid(String imageid) {
        this.imageid = imageid == null ? null : imageid.trim();
    }

    public String getDescribe() {
        return describe == null ? "" : describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe == null ? null : describe.trim();
    }

    public String getTitle() {
        return title == null ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getKeyWords() {
        return keyWords == null ? "" : keyWords;
    }

    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords == null ? null : keyWords.trim();
    }

    public String getLogoUrl() {
        return logoUrl == null ? "" : logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl == null ? null : logoUrl.trim();
    }

    public String getGroupId() {
        return groupId == null ? "" : groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    public String getDataUrl() {
        return dataUrl == null ? "" : dataUrl;
    }

    public void setDataUrl(String dataUrl) {
        this.dataUrl = dataUrl == null ? null : dataUrl.trim();
    }

    public Date getMetadataModificationDate() {
        return metadataModificationDate;
    }

    public void setMetadataModificationDate(Date metadataModificationDate) {
        this.metadataModificationDate = metadataModificationDate;
    }

    public String getActivityType() {
        return activityType == null ? "" : activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType == null ? null : activityType.trim();
    }

    public BigDecimal getRankingHint() {
        return rankingHint;
    }

    public void setRankingHint(BigDecimal rankingHint) {
        this.rankingHint = rankingHint;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public String getViewMark() {
        return viewMark == null ? "" : viewMark;
    }

    public void setViewMark(String viewMark) {
        this.viewMark = viewMark == null ? null : viewMark.trim();
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
}