package com.autohome.applet.dao.netcoreapi.model;

import java.util.Date;

public class SweetClient {

    private Integer SweetId;
    private String SweetName;
    private Integer ClientType;
    private String JumpTo;
    private String Img;
    private Date StartTime;
    private Date EndTime;
    private Integer JumpType;
    private String OtherAppid;
    private String ReportPoint;
    private Integer SortNum;
    private String ShareDesc;
    private String ShareImg;
    private String ShowPoint;
    private Integer NeedLogin;

    public Integer getSweetId() {
        return SweetId;
    }

    public void setSweetId(Integer sweetId) {
        SweetId = sweetId;
    }

    public String getSweetName() {
        return SweetName;
    }

    public void setSweetName(String sweetName) {
        SweetName = sweetName;
    }

    public Integer getClientType() {
        return ClientType;
    }

    public void setClientType(Integer clientType) {
        ClientType = clientType;
    }

    public String getJumpTo() {
        return JumpTo;
    }

    public void setJumpTo(String jumpTo) {
        JumpTo = jumpTo;
    }

    public String getImg() {
        return Img;
    }

    public void setImg(String img) {
        Img = img;
    }

    public Date getStartTime() {
        return StartTime;
    }

    public void setStartTime(Date startTime) {
        StartTime = startTime;
    }

    public Date getEndTime() {
        return EndTime;
    }

    public void setEndTime(Date endTime) {
        EndTime = endTime;
    }

    public Integer getJumpType() {
        return JumpType;
    }

    public void setJumpType(Integer jumpType) {
        JumpType = jumpType;
    }

    public String getOtherAppid() {
        return OtherAppid;
    }

    public void setOtherAppid(String otherAppid) {
        OtherAppid = otherAppid;
    }

    public String getReportPoint() {
        return ReportPoint;
    }

    public void setReportPoint(String reportPoint) {
        ReportPoint = reportPoint;
    }

    public Integer getSortNum() {
        return SortNum;
    }

    public void setSortNum(Integer sortNum) {
        SortNum = sortNum;
    }

    public String getShareDesc() {
        return ShareDesc;
    }

    public void setShareDesc(String shareDesc) {
        ShareDesc = shareDesc;
    }

    public String getShareImg() {
        return ShareImg;
    }

    public void setShareImg(String shareImg) {
        ShareImg = shareImg;
    }

    public String getShowPoint() {
        return ShowPoint;
    }

    public void setShowPoint(String showPoint) {
        ShowPoint = showPoint;
    }

    public Integer getNeedLogin() {
        return NeedLogin;
    }

    public void setNeedLogin(Integer needLogin) {
        NeedLogin = needLogin;
    }
}
