package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

/**
 * 小程序关联任务表
 */
public class AppletDoTaskLog {
    private Long id;
    /**
     * 开放平台id
     */
    private String openid;
    /**
     * 活动平台任务code（活动平台分配，通过此code可以查是什么活动奖品）
     */
    private String code;
    /**
     * 开放平台唯一id
     */
    private String unionid;
    /**
     * 之家用户id
     */
    private String userId;
    /**
     * 类型  1 直接做任务， 2 异步做任务
     */
    private int type;
    /**
     * 状态 0-默认未执行，1-执行成功，2-执行失败
     */
    private int status;
    /**
     * 请求url
     */
    private String requestUrl;
    /**
     * 请求参数，存储的是json
     */
    private String requestData;
    /**
     * 第一次推送时间
     */
    private Date firstRequestTime;
    /**
     * 返回的returncode
     */
    private String responseCode;
    /**
     * 返回的状态码 200，500
     */
    private int responseStatus;
    private String responseData;
    private Date createdSTime;
    private Date modifiedSTime;
    private int isDel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestData() {
        return requestData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public Date getFirstRequestTime() {
        return firstRequestTime;
    }

    public void setFirstRequestTime(Date firstRequestTime) {
        this.firstRequestTime = firstRequestTime;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public int getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(int responseStatus) {
        this.responseStatus = responseStatus;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public Date getCreatedSTime() {
        return createdSTime;
    }

    public void setCreatedSTime(Date createdSTime) {
        this.createdSTime = createdSTime;
    }

    public Date getModifiedSTime() {
        return modifiedSTime;
    }

    public void setModifiedSTime(Date modifiedSTime) {
        this.modifiedSTime = modifiedSTime;
    }

    public int getIsDel() {
        return isDel;
    }

    public void setIsDel(int isDel) {
        this.isDel = isDel;
    }

    public enum AppletDoTaskTypeEnum  {
        /**
         * 类型 0 未执行任务 1 直接做任务， 2 异步做任务
         */
        NO_TASK(0,"未执行任务"),
        DO_TASK(1,"直接做任务"),
        ASYNC_DO_TASK(2,"异步做任务");

        private int type;
        private String desc;

        AppletDoTaskTypeEnum(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public int type() {
            return type;
        }
        public String desc() {
            return desc;
        }
    }
    public enum AppletDoTaskStatusEnum  {
        /**
         * 状态 0-默认未执行，1-执行成功，2-执行失败
         */

        UNEXECUTED(0,"未执行"),
        SUCCESS(1,"成功"),
        FAIL(2,"失败");

        private int status;
        private String desc;

        AppletDoTaskStatusEnum(int status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public int status() {
            return status;
        }
        public String desc() {
            return desc;
        }
    }
}
