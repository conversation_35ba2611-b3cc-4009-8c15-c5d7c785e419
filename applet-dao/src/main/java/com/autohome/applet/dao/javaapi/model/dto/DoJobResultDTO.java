package com.autohome.applet.dao.javaapi.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoJobResultDTO {
    private int score;
    private int series;
    private int count;
    private int assignmentId;
    private int intervalTotalTimes;
    private int intervalCompletedTimes;
    private int rewardScene;
}

