package com.autohome.applet.dao.netcoreapi.mapper.content;

import com.autohome.applet.dao.netcoreapi.model.ExpertBlog;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("db-content-read")
public interface ExpertBlogMapper {

    List<ExpertBlog> GetExperBlogListByEditorIds(@Param("idlist") List<Integer> idlist);
}
