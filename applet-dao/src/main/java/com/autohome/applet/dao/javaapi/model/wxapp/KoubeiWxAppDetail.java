package com.autohome.applet.dao.javaapi.model.wxapp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 口碑微信小程序最终页接口返回(这里主要是为了获得口碑标题和口碑的购买目的)
 * Created by pen<PERSON><PERSON> on 2016/12/29.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class KoubeiWxAppDetail {
    private String feelingsummary;//标题
    private Purpose[] purpose;

    public String getFeelingsummary() {
        return feelingsummary;
    }

    public void setFeelingsummary(String feelingsummary) {
        this.feelingsummary = feelingsummary;
    }

    public Purpose[] getPurpose() {
        return purpose;
    }

    public void setPurpose(Purpose[] purpose) {
        this.purpose = purpose;
    }

    public String getPurposeStr() {
        if (purpose == null || purpose.length <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(purpose[0].getPurposename());
        for (int i = 1; i < purpose.length; i++) {
            sb.append(",");
            sb.append(purpose[i].getPurposename());
        }
        return sb.toString();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Purpose {
        private int eid;
        private int purposeid;
        private String purposename;

        public int getEid() {
            return eid;
        }

        public void setEid(int eid) {
            this.eid = eid;
        }

        public int getPurposeid() {
            return purposeid;
        }

        public void setPurposeid(int purposeid) {
            this.purposeid = purposeid;
        }

        public String getPurposename() {
            return purposename;
        }

        public void setPurposename(String purposename) {
            this.purposename = purposename;
        }
    }
}
