package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.DonateLog;
import com.autohome.applet.dao.javaapi.model.donate.DonateDaoQuery;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface DonateLogMapper {
    int insert(DonateLog row);

    DonateLog selectByPrimaryKey(Long id);

    List<DonateLog> selectAll();

    int updateByPrimaryKey(DonateLog row);

    int deleteBatch(@Param("daily") String daily, @Param("donateType") Integer donateType, @Param("endDate") String endDate);

    int insertBatch(@Param("donateLogList") List<DonateLog> donateLogList);

    int countByQuery(DonateDaoQuery donateDaoQuery);

    List<DonateLog> selectByQuery(DonateDaoQuery donateDaoQuery);
}