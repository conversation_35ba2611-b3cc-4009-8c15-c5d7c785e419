package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class AppletOpenAutohomeRelation {

    private Long id;

    private String openid;

    private String unionid;

    private int userId;

    private int platform;

    private Date firstLoginDate;

    private String firstLoginIp;

    private String firstLoginPosition;

    private Date lastLoginDate;

    private String lastLoginIp;

    private String lastLoginPosition;

    private Date createdSTime;

    private Date modifiedSTime;

    private int isDel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public Date getFirstLoginDate() {
        return firstLoginDate;
    }

    public void setFirstLoginDate(Date firstLoginDate) {
        this.firstLoginDate = firstLoginDate;
    }

    public String getFirstLoginIp() {
        return firstLoginIp;
    }

    public void setFirstLoginIp(String firstLoginIp) {
        this.firstLoginIp = firstLoginIp;
    }

    public String getFirstLoginPosition() {
        return firstLoginPosition;
    }

    public void setFirstLoginPosition(String firstLoginPosition) {
        this.firstLoginPosition = firstLoginPosition;
    }

    public Date getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(Date lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    public String getLastLoginPosition() {
        return lastLoginPosition;
    }

    public void setLastLoginPosition(String lastLoginPosition) {
        this.lastLoginPosition = lastLoginPosition;
    }

    public Date getCreatedSTime() {
        return createdSTime;
    }

    public void setCreatedSTime(Date createdSTime) {
        this.createdSTime = createdSTime;
    }

    public Date getModifiedSTime() {
        return modifiedSTime;
    }

    public void setModifiedSTime(Date modifiedSTime) {
        this.modifiedSTime = modifiedSTime;
    }

    public int getIsDel() {
        return isDel;
    }

    public void setIsDel(int isDel) {
        this.isDel = isDel;
    }

    public enum Platform{

        WECHAT(1,"微信"),
        ;
        private int platformType;
        private String desc;

        Platform(int platformType, String desc) {
            this.platformType = platformType;
            this.desc = desc;
        }

        public String desc(){
            return desc;
        }

        public int platformType(){
            return platformType;
        }
    }
}
