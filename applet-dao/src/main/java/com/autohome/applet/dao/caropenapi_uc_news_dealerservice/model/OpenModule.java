package com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ha<PERSON>hanfeng on 2019/4/25.
 */
public class OpenModule {
    public OpenModule() {
    }

    /**
     * @param moduleId
     * @param moduleName
     * @param moduleType
     * @param sortNum
     */
    public OpenModule(int moduleId, String moduleName, int moduleType, int sortNum) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
        this.moduleType = moduleType;
        this.sortNum = sortNum;
    }

    //    ModuleId
    private int moduleId;

    //    ModuleName
    private String moduleName;

    //    ModuleType
    private int moduleType;

    //    SortNum
    private int sortNum;

    private List moduleData;

    @JsonInclude(JsonInclude.Include.ALWAYS)
    private String more = StringUtils.EMPTY;

    public String getMore() {
        return more;
    }

    public void setMore(String more) {
        this.more = more;
    }

    public List getModuleData() {
        if (moduleData == null) {
            return new ArrayList();
        }
        return moduleData;
    }

    public void setModuleData(List moduleData) {
        this.moduleData = moduleData;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public int getModuleType() {
        return moduleType;
    }

    public void setModuleType(int moduleType) {
        this.moduleType = moduleType;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }
}
