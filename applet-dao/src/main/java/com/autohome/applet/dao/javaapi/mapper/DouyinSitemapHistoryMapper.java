package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.DouyinSitemapHistory;
import com.autohome.applet.dao.javaapi.model.douyin.DouYinSiteMapHistoryDto;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
@Mapper
@DS("db-autoopen")
@Repository
public interface DouyinSitemapHistoryMapper {
    String fields = "" +
            "T.id   ,  \n" +
            "T.pagepath     \n" +
            ",T.source    \n" ;
    int deleteByPrimaryKey(Long id);

    int insert(DouyinSitemapHistory row);

    DouyinSitemapHistory selectByPrimaryKey(Long id);

    List<DouyinSitemapHistory> selectAll();

    int updateByPrimaryKey(DouyinSitemapHistory row);

    @Select("SELECT TOP (#{pageSize}) " + fields + " FROM douyin_sitemap_history T WITH(NOLOCK) " +
            " WHERE status =0  " +
            "  ORDER BY id ASC")
    List<DouYinSiteMapHistoryDto> getDouyinSitemapHistoryList(
                                     @Param("pageSize")   int pageSize);
    @Update("update   douyin_sitemap_history  set status =1 " +
            " WHERE id =  #{id}   " )
    int updateById(@Param("id")   int id);

    int insertBatch(List<DouyinSitemapHistory> row);
}