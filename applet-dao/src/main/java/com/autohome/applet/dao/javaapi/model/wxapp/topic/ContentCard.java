package com.autohome.applet.dao.javaapi.model.wxapp.topic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by pengyi on 2016/12/9.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContentCard {
    private static final ContentCard hasDeletedMainContentCard = new ContentCard(2, "本楼已被管理员删除", "", null);

    public ContentCard() {

    }

    public ContentCard(int ctype, String des, String url, Object otherattributes) {
        this.ctype = ctype;
        this.des = des;
        this.url = url;
        this.otherattributes = otherattributes;
    }

    private int ctype;
    private String des;
    private String url;
    private Object otherattributes;


    public int getCtype() {
        return ctype;
    }

    public void setCtype(int ctype) {
        this.ctype = ctype;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Object getOtherattributes() {
        return otherattributes;
    }

    public void setOtherattributes(Object otherattributes) {
        this.otherattributes = otherattributes;
    }

    public static final ContentCard getHasDeletedMainContentCard() {
        return hasDeletedMainContentCard;
    }
}
