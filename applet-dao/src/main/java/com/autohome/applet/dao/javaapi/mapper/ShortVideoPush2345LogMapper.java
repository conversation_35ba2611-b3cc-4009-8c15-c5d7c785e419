package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.ShortVideoPush2345Log;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface ShortVideoPush2345LogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ShortVideoPush2345Log row);

    ShortVideoPush2345Log selectByPrimaryKey(Long id);

    List<ShortVideoPush2345Log> selectAll();

    int updateByPrimaryKey(ShortVideoPush2345Log row);
}