package com.autohome.applet.dao.javaapi.model.donate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DonateDaoQuery {
    private String deviceId;
    private int deviceHashId;
    private Long searchAfter;
    private int pageSize;
    private String daily;
    private int donateType;

    private int tableIndex;

    private long getTableIndex(){
        return deviceHashId % 30;
    }
}
