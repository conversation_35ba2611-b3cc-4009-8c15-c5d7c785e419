package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class PanoramicBuyUseCarDailyRepoterDingding {
    private Integer id;

    private String dt;

    private String totalFlowSum;

    private String totalFlowChainratio;

    private String totalFlowYearonyear;

    private String totalFlowMonthavg;

    private String totalLeadSum;

    private String totalLeadChainratio;

    private String totalLeadChainratiodiff;

    private String totalLeadYearonyear;

    private String totalLeadYearonyeardiff;

    private String totalLeadMonthavg;

    private String totalLeadrateSum;

    private String totalLeadrateChainratiodiff;

    private String totalLeadrateYearonyeardiff;

    private String totalLeadrateMonthavg;

    private String miniprogramFlowSum;

    private String miniprogramFlowChainratio;

    private String miniprogramFlowYearonyear;

    private String miniprogramFlowMonthavg;

    private String miniprogramLeadSum;

    private String miniprogramLeadChainratio;

    private String miniprogramLeadChainratiodiff;

    private String miniprogramLeadYearonyear;

    private String miniprogramLeadYearonyeardiff;

    private String miniprogramLeadMonthavg;

    private String miniprogramLeadrateSum;

    private String miniprogramLeadrateChainratiodiff;

    private String miniprogramLeadrateYearonyeardiff;

    private String miniprogramLeadrateMonthavg;

    private String gaodeFlowSum;

    private String gaodeFlowChainratio;

    private String gaodeFlowYearonyear;

    private String gaodeFlowMonthavg;

    private String gaodeLeadSum;

    private String gaodeLeadChainratio;

    private String gaodeLeadChainratiodiff;

    private String gaodeLeadYearonyear;

    private String gaodeLeadYearonyeardiff;

    private String gaodeLeadMonthavg;

    private String gaodeLeadrateSum;

    private String gaodeLeadrateChainratiodiff;

    private String gaodeLeadrateYearonyeardiff;

    private String gaodeLeadrateMonthavg;

    private String default0;

    private String default1;

    private String default2;

    private String default3;

    private String default4;

    private String default5;

    private String default6;

    private String default7;

    private String default8;

    private String default9;

    private Integer isDel;

    private Date createdStime;

    private Date modifiedStime;

    private String miniprogramPhoneSum;
    private String miniprogramPhoneChainRatio;
    private String miniprogramPhoneChainRatioDiff;
    private String miniprogramPhoneYearOnYear;
    private String miniprogramPhoneYearOnYearDiff;
    private String miniprogramPhoneMonthAvg;

    private String miniprogramFlowPhoneRate;
    private String miniprogramFlowphoneRateChainRatioDiff;
    private String miniprogramFlowphoneRateYearOnYearDiff;
    private String miniprogramFlowphoneRateMonthAvg;

    private String miniprogramLeadPhoneRate;
    private String miniprogramLeadphoneRateChainRatioDiff;
    private String miniprogramLeadphoneRateYearOnYearDiff;
    private String miniprogramLeadphoneRateMonthAvg;

    private String cooperationFlowSum;
    private String cooperationFlowChainRatio;
    private String cooperationFlowYearOnYear;
    private String cooperationFlowMonthAvg;

    private String cooperationLeadSum;
    private String cooperationLeadChainRatio;
    private String cooperationLeadChainRatioDiff;
    private String cooperationLeadYearOnYear;
    private String cooperationLeadYearOnYearDiff;
    private String cooperationLeadMonthAvg;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt == null ? null : dt.trim();
    }

    public String getTotalFlowSum() {
        return totalFlowSum;
    }

    public void setTotalFlowSum(String totalFlowSum) {
        this.totalFlowSum = totalFlowSum == null ? null : totalFlowSum.trim();
    }

    public String getTotalFlowChainratio() {
        return totalFlowChainratio;
    }

    public void setTotalFlowChainratio(String totalFlowChainratio) {
        this.totalFlowChainratio = totalFlowChainratio == null ? null : totalFlowChainratio.trim();
    }

    public String getTotalFlowYearonyear() {
        return totalFlowYearonyear;
    }

    public void setTotalFlowYearonyear(String totalFlowYearonyear) {
        this.totalFlowYearonyear = totalFlowYearonyear == null ? null : totalFlowYearonyear.trim();
    }

    public String getTotalFlowMonthavg() {
        return totalFlowMonthavg;
    }

    public void setTotalFlowMonthavg(String totalFlowMonthavg) {
        this.totalFlowMonthavg = totalFlowMonthavg == null ? null : totalFlowMonthavg.trim();
    }

    public String getTotalLeadSum() {
        return totalLeadSum;
    }

    public void setTotalLeadSum(String totalLeadSum) {
        this.totalLeadSum = totalLeadSum == null ? null : totalLeadSum.trim();
    }

    public String getTotalLeadChainratio() {
        return totalLeadChainratio;
    }

    public void setTotalLeadChainratio(String totalLeadChainratio) {
        this.totalLeadChainratio = totalLeadChainratio == null ? null : totalLeadChainratio.trim();
    }

    public String getTotalLeadChainratiodiff() {
        return totalLeadChainratiodiff;
    }

    public void setTotalLeadChainratiodiff(String totalLeadChainratiodiff) {
        this.totalLeadChainratiodiff = totalLeadChainratiodiff == null ? null : totalLeadChainratiodiff.trim();
    }

    public String getTotalLeadYearonyear() {
        return totalLeadYearonyear;
    }

    public void setTotalLeadYearonyear(String totalLeadYearonyear) {
        this.totalLeadYearonyear = totalLeadYearonyear == null ? null : totalLeadYearonyear.trim();
    }

    public String getTotalLeadYearonyeardiff() {
        return totalLeadYearonyeardiff;
    }

    public void setTotalLeadYearonyeardiff(String totalLeadYearonyeardiff) {
        this.totalLeadYearonyeardiff = totalLeadYearonyeardiff == null ? null : totalLeadYearonyeardiff.trim();
    }

    public String getTotalLeadMonthavg() {
        return totalLeadMonthavg;
    }

    public void setTotalLeadMonthavg(String totalLeadMonthavg) {
        this.totalLeadMonthavg = totalLeadMonthavg == null ? null : totalLeadMonthavg.trim();
    }

    public String getTotalLeadrateSum() {
        return totalLeadrateSum;
    }

    public void setTotalLeadrateSum(String totalLeadrateSum) {
        this.totalLeadrateSum = totalLeadrateSum == null ? null : totalLeadrateSum.trim();
    }

    public String getTotalLeadrateChainratiodiff() {
        return totalLeadrateChainratiodiff;
    }

    public void setTotalLeadrateChainratiodiff(String totalLeadrateChainratiodiff) {
        this.totalLeadrateChainratiodiff = totalLeadrateChainratiodiff == null ? null : totalLeadrateChainratiodiff.trim();
    }

    public String getTotalLeadrateYearonyeardiff() {
        return totalLeadrateYearonyeardiff;
    }

    public void setTotalLeadrateYearonyeardiff(String totalLeadrateYearonyeardiff) {
        this.totalLeadrateYearonyeardiff = totalLeadrateYearonyeardiff == null ? null : totalLeadrateYearonyeardiff.trim();
    }

    public String getTotalLeadrateMonthavg() {
        return totalLeadrateMonthavg;
    }

    public void setTotalLeadrateMonthavg(String totalLeadrateMonthavg) {
        this.totalLeadrateMonthavg = totalLeadrateMonthavg == null ? null : totalLeadrateMonthavg.trim();
    }

    public String getMiniprogramFlowSum() {
        return miniprogramFlowSum;
    }

    public void setMiniprogramFlowSum(String miniprogramFlowSum) {
        this.miniprogramFlowSum = miniprogramFlowSum == null ? null : miniprogramFlowSum.trim();
    }

    public String getMiniprogramFlowChainratio() {
        return miniprogramFlowChainratio;
    }

    public void setMiniprogramFlowChainratio(String miniprogramFlowChainratio) {
        this.miniprogramFlowChainratio = miniprogramFlowChainratio == null ? null : miniprogramFlowChainratio.trim();
    }

    public String getMiniprogramFlowYearonyear() {
        return miniprogramFlowYearonyear;
    }

    public void setMiniprogramFlowYearonyear(String miniprogramFlowYearonyear) {
        this.miniprogramFlowYearonyear = miniprogramFlowYearonyear == null ? null : miniprogramFlowYearonyear.trim();
    }

    public String getMiniprogramFlowMonthavg() {
        return miniprogramFlowMonthavg;
    }

    public void setMiniprogramFlowMonthavg(String miniprogramFlowMonthavg) {
        this.miniprogramFlowMonthavg = miniprogramFlowMonthavg == null ? null : miniprogramFlowMonthavg.trim();
    }

    public String getMiniprogramLeadSum() {
        return miniprogramLeadSum;
    }

    public void setMiniprogramLeadSum(String miniprogramLeadSum) {
        this.miniprogramLeadSum = miniprogramLeadSum == null ? null : miniprogramLeadSum.trim();
    }

    public String getMiniprogramLeadChainratio() {
        return miniprogramLeadChainratio;
    }

    public void setMiniprogramLeadChainratio(String miniprogramLeadChainratio) {
        this.miniprogramLeadChainratio = miniprogramLeadChainratio == null ? null : miniprogramLeadChainratio.trim();
    }

    public String getMiniprogramLeadChainratiodiff() {
        return miniprogramLeadChainratiodiff;
    }

    public void setMiniprogramLeadChainratiodiff(String miniprogramLeadChainratiodiff) {
        this.miniprogramLeadChainratiodiff = miniprogramLeadChainratiodiff == null ? null : miniprogramLeadChainratiodiff.trim();
    }

    public String getMiniprogramLeadYearonyear() {
        return miniprogramLeadYearonyear;
    }

    public void setMiniprogramLeadYearonyear(String miniprogramLeadYearonyear) {
        this.miniprogramLeadYearonyear = miniprogramLeadYearonyear == null ? null : miniprogramLeadYearonyear.trim();
    }

    public String getMiniprogramLeadYearonyeardiff() {
        return miniprogramLeadYearonyeardiff;
    }

    public void setMiniprogramLeadYearonyeardiff(String miniprogramLeadYearonyeardiff) {
        this.miniprogramLeadYearonyeardiff = miniprogramLeadYearonyeardiff == null ? null : miniprogramLeadYearonyeardiff.trim();
    }

    public String getMiniprogramLeadMonthavg() {
        return miniprogramLeadMonthavg;
    }

    public void setMiniprogramLeadMonthavg(String miniprogramLeadMonthavg) {
        this.miniprogramLeadMonthavg = miniprogramLeadMonthavg == null ? null : miniprogramLeadMonthavg.trim();
    }

    public String getMiniprogramLeadrateSum() {
        return miniprogramLeadrateSum;
    }

    public void setMiniprogramLeadrateSum(String miniprogramLeadrateSum) {
        this.miniprogramLeadrateSum = miniprogramLeadrateSum == null ? null : miniprogramLeadrateSum.trim();
    }

    public String getMiniprogramLeadrateChainratiodiff() {
        return miniprogramLeadrateChainratiodiff;
    }

    public void setMiniprogramLeadrateChainratiodiff(String miniprogramLeadrateChainratiodiff) {
        this.miniprogramLeadrateChainratiodiff = miniprogramLeadrateChainratiodiff == null ? null : miniprogramLeadrateChainratiodiff.trim();
    }

    public String getMiniprogramLeadrateYearonyeardiff() {
        return miniprogramLeadrateYearonyeardiff;
    }

    public void setMiniprogramLeadrateYearonyeardiff(String miniprogramLeadrateYearonyeardiff) {
        this.miniprogramLeadrateYearonyeardiff = miniprogramLeadrateYearonyeardiff == null ? null : miniprogramLeadrateYearonyeardiff.trim();
    }

    public String getMiniprogramLeadrateMonthavg() {
        return miniprogramLeadrateMonthavg;
    }

    public void setMiniprogramLeadrateMonthavg(String miniprogramLeadrateMonthavg) {
        this.miniprogramLeadrateMonthavg = miniprogramLeadrateMonthavg == null ? null : miniprogramLeadrateMonthavg.trim();
    }

    public String getGaodeFlowSum() {
        return gaodeFlowSum;
    }

    public void setGaodeFlowSum(String gaodeFlowSum) {
        this.gaodeFlowSum = gaodeFlowSum == null ? null : gaodeFlowSum.trim();
    }

    public String getGaodeFlowChainratio() {
        return gaodeFlowChainratio;
    }

    public void setGaodeFlowChainratio(String gaodeFlowChainratio) {
        this.gaodeFlowChainratio = gaodeFlowChainratio == null ? null : gaodeFlowChainratio.trim();
    }

    public String getGaodeFlowYearonyear() {
        return gaodeFlowYearonyear;
    }

    public void setGaodeFlowYearonyear(String gaodeFlowYearonyear) {
        this.gaodeFlowYearonyear = gaodeFlowYearonyear == null ? null : gaodeFlowYearonyear.trim();
    }

    public String getGaodeFlowMonthavg() {
        return gaodeFlowMonthavg;
    }

    public void setGaodeFlowMonthavg(String gaodeFlowMonthavg) {
        this.gaodeFlowMonthavg = gaodeFlowMonthavg == null ? null : gaodeFlowMonthavg.trim();
    }

    public String getGaodeLeadSum() {
        return gaodeLeadSum;
    }

    public void setGaodeLeadSum(String gaodeLeadSum) {
        this.gaodeLeadSum = gaodeLeadSum == null ? null : gaodeLeadSum.trim();
    }

    public String getGaodeLeadChainratio() {
        return gaodeLeadChainratio;
    }

    public void setGaodeLeadChainratio(String gaodeLeadChainratio) {
        this.gaodeLeadChainratio = gaodeLeadChainratio == null ? null : gaodeLeadChainratio.trim();
    }

    public String getGaodeLeadChainratiodiff() {
        return gaodeLeadChainratiodiff;
    }

    public void setGaodeLeadChainratiodiff(String gaodeLeadChainratiodiff) {
        this.gaodeLeadChainratiodiff = gaodeLeadChainratiodiff == null ? null : gaodeLeadChainratiodiff.trim();
    }

    public String getGaodeLeadYearonyear() {
        return gaodeLeadYearonyear;
    }

    public void setGaodeLeadYearonyear(String gaodeLeadYearonyear) {
        this.gaodeLeadYearonyear = gaodeLeadYearonyear == null ? null : gaodeLeadYearonyear.trim();
    }

    public String getGaodeLeadYearonyeardiff() {
        return gaodeLeadYearonyeardiff;
    }

    public void setGaodeLeadYearonyeardiff(String gaodeLeadYearonyeardiff) {
        this.gaodeLeadYearonyeardiff = gaodeLeadYearonyeardiff == null ? null : gaodeLeadYearonyeardiff.trim();
    }

    public String getGaodeLeadMonthavg() {
        return gaodeLeadMonthavg;
    }

    public void setGaodeLeadMonthavg(String gaodeLeadMonthavg) {
        this.gaodeLeadMonthavg = gaodeLeadMonthavg == null ? null : gaodeLeadMonthavg.trim();
    }

    public String getGaodeLeadrateSum() {
        return gaodeLeadrateSum;
    }

    public void setGaodeLeadrateSum(String gaodeLeadrateSum) {
        this.gaodeLeadrateSum = gaodeLeadrateSum == null ? null : gaodeLeadrateSum.trim();
    }

    public String getGaodeLeadrateChainratiodiff() {
        return gaodeLeadrateChainratiodiff;
    }

    public void setGaodeLeadrateChainratiodiff(String gaodeLeadrateChainratiodiff) {
        this.gaodeLeadrateChainratiodiff = gaodeLeadrateChainratiodiff == null ? null : gaodeLeadrateChainratiodiff.trim();
    }

    public String getGaodeLeadrateYearonyeardiff() {
        return gaodeLeadrateYearonyeardiff;
    }

    public void setGaodeLeadrateYearonyeardiff(String gaodeLeadrateYearonyeardiff) {
        this.gaodeLeadrateYearonyeardiff = gaodeLeadrateYearonyeardiff == null ? null : gaodeLeadrateYearonyeardiff.trim();
    }

    public String getGaodeLeadrateMonthavg() {
        return gaodeLeadrateMonthavg;
    }

    public void setGaodeLeadrateMonthavg(String gaodeLeadrateMonthavg) {
        this.gaodeLeadrateMonthavg = gaodeLeadrateMonthavg == null ? null : gaodeLeadrateMonthavg.trim();
    }

    public String getDefault0() {
        return default0;
    }

    public void setDefault0(String default0) {
        this.default0 = default0 == null ? null : default0.trim();
    }

    public String getDefault1() {
        return default1;
    }

    public void setDefault1(String default1) {
        this.default1 = default1 == null ? null : default1.trim();
    }

    public String getDefault2() {
        return default2;
    }

    public void setDefault2(String default2) {
        this.default2 = default2 == null ? null : default2.trim();
    }

    public String getDefault3() {
        return default3;
    }

    public void setDefault3(String default3) {
        this.default3 = default3 == null ? null : default3.trim();
    }

    public String getDefault4() {
        return default4;
    }

    public void setDefault4(String default4) {
        this.default4 = default4 == null ? null : default4.trim();
    }

    public String getDefault5() {
        return default5;
    }

    public void setDefault5(String default5) {
        this.default5 = default5 == null ? null : default5.trim();
    }

    public String getDefault6() {
        return default6;
    }

    public void setDefault6(String default6) {
        this.default6 = default6 == null ? null : default6.trim();
    }

    public String getDefault7() {
        return default7;
    }

    public void setDefault7(String default7) {
        this.default7 = default7 == null ? null : default7.trim();
    }

    public String getDefault8() {
        return default8;
    }

    public void setDefault8(String default8) {
        this.default8 = default8 == null ? null : default8.trim();
    }

    public String getDefault9() {
        return default9;
    }

    public void setDefault9(String default9) {
        this.default9 = default9 == null ? null : default9.trim();
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public String getMiniprogramPhoneSum() {
        return miniprogramPhoneSum;
    }

    public void setMiniprogramPhoneSum(String miniprogramPhoneSum) {
        this.miniprogramPhoneSum = miniprogramPhoneSum;
    }

    public String getMiniprogramPhoneChainRatio() {
        return miniprogramPhoneChainRatio;
    }

    public void setMiniprogramPhoneChainRatio(String miniprogramPhoneChainRatio) {
        this.miniprogramPhoneChainRatio = miniprogramPhoneChainRatio;
    }

    public String getMiniprogramPhoneChainRatioDiff() {
        return miniprogramPhoneChainRatioDiff;
    }

    public void setMiniprogramPhoneChainRatioDiff(String miniprogramPhoneChainRatioDiff) {
        this.miniprogramPhoneChainRatioDiff = miniprogramPhoneChainRatioDiff;
    }

    public String getMiniprogramPhoneYearOnYear() {
        return miniprogramPhoneYearOnYear;
    }

    public void setMiniprogramPhoneYearOnYear(String miniprogramPhoneYearOnYear) {
        this.miniprogramPhoneYearOnYear = miniprogramPhoneYearOnYear;
    }

    public String getMiniprogramPhoneYearOnYearDiff() {
        return miniprogramPhoneYearOnYearDiff;
    }

    public void setMiniprogramPhoneYearOnYearDiff(String miniprogramPhoneYearOnYearDiff) {
        this.miniprogramPhoneYearOnYearDiff = miniprogramPhoneYearOnYearDiff;
    }

    public String getMiniprogramPhoneMonthAvg() {
        return miniprogramPhoneMonthAvg;
    }

    public void setMiniprogramPhoneMonthAvg(String miniprogramPhoneMonthAvg) {
        this.miniprogramPhoneMonthAvg = miniprogramPhoneMonthAvg;
    }

    public String getMiniprogramFlowPhoneRate() {
        return miniprogramFlowPhoneRate;
    }

    public void setMiniprogramFlowPhoneRate(String miniprogramFlowPhoneRate) {
        this.miniprogramFlowPhoneRate = miniprogramFlowPhoneRate;
    }

    public String getMiniprogramFlowphoneRateChainRatioDiff() {
        return miniprogramFlowphoneRateChainRatioDiff;
    }

    public void setMiniprogramFlowphoneRateChainRatioDiff(String miniprogramFlowphoneRateChainRatioDiff) {
        this.miniprogramFlowphoneRateChainRatioDiff = miniprogramFlowphoneRateChainRatioDiff;
    }

    public String getMiniprogramFlowphoneRateYearOnYearDiff() {
        return miniprogramFlowphoneRateYearOnYearDiff;
    }

    public void setMiniprogramFlowphoneRateYearOnYearDiff(String miniprogramFlowphoneRateYearOnYearDiff) {
        this.miniprogramFlowphoneRateYearOnYearDiff = miniprogramFlowphoneRateYearOnYearDiff;
    }

    public String getMiniprogramFlowphoneRateMonthAvg() {
        return miniprogramFlowphoneRateMonthAvg;
    }

    public void setMiniprogramFlowphoneRateMonthAvg(String miniprogramFlowphoneRateMonthAvg) {
        this.miniprogramFlowphoneRateMonthAvg = miniprogramFlowphoneRateMonthAvg;
    }

    public String getMiniprogramLeadPhoneRate() {
        return miniprogramLeadPhoneRate;
    }

    public void setMiniprogramLeadPhoneRate(String miniprogramLeadPhoneRate) {
        this.miniprogramLeadPhoneRate = miniprogramLeadPhoneRate;
    }

    public String getMiniprogramLeadphoneRateChainRatioDiff() {
        return miniprogramLeadphoneRateChainRatioDiff;
    }

    public void setMiniprogramLeadphoneRateChainRatioDiff(String miniprogramLeadphoneRateChainRatioDiff) {
        this.miniprogramLeadphoneRateChainRatioDiff = miniprogramLeadphoneRateChainRatioDiff;
    }

    public String getMiniprogramLeadphoneRateYearOnYearDiff() {
        return miniprogramLeadphoneRateYearOnYearDiff;
    }

    public void setMiniprogramLeadphoneRateYearOnYearDiff(String miniprogramLeadphoneRateYearOnYearDiff) {
        this.miniprogramLeadphoneRateYearOnYearDiff = miniprogramLeadphoneRateYearOnYearDiff;
    }

    public String getMiniprogramLeadphoneRateMonthAvg() {
        return miniprogramLeadphoneRateMonthAvg;
    }

    public void setMiniprogramLeadphoneRateMonthAvg(String miniprogramLeadphoneRateMonthAvg) {
        this.miniprogramLeadphoneRateMonthAvg = miniprogramLeadphoneRateMonthAvg;
    }

    public String getCooperationFlowSum() {
        return cooperationFlowSum;
    }

    public void setCooperationFlowSum(String cooperationFlowSum) {
        this.cooperationFlowSum = cooperationFlowSum;
    }

    public String getCooperationFlowChainRatio() {
        return cooperationFlowChainRatio;
    }

    public void setCooperationFlowChainRatio(String cooperationFlowChainRatio) {
        this.cooperationFlowChainRatio = cooperationFlowChainRatio;
    }

    public String getCooperationFlowYearOnYear() {
        return cooperationFlowYearOnYear;
    }

    public void setCooperationFlowYearOnYear(String cooperationFlowYearOnYear) {
        this.cooperationFlowYearOnYear = cooperationFlowYearOnYear;
    }

    public String getCooperationFlowMonthAvg() {
        return cooperationFlowMonthAvg;
    }

    public void setCooperationFlowMonthAvg(String cooperationFlowMonthAvg) {
        this.cooperationFlowMonthAvg = cooperationFlowMonthAvg;
    }

    public String getCooperationLeadSum() {
        return cooperationLeadSum;
    }

    public void setCooperationLeadSum(String cooperationLeadSum) {
        this.cooperationLeadSum = cooperationLeadSum;
    }

    public String getCooperationLeadChainRatio() {
        return cooperationLeadChainRatio;
    }

    public void setCooperationLeadChainRatio(String cooperationLeadChainRatio) {
        this.cooperationLeadChainRatio = cooperationLeadChainRatio;
    }

    public String getCooperationLeadChainRatioDiff() {
        return cooperationLeadChainRatioDiff;
    }

    public void setCooperationLeadChainRatioDiff(String cooperationLeadChainRatioDiff) {
        this.cooperationLeadChainRatioDiff = cooperationLeadChainRatioDiff;
    }

    public String getCooperationLeadYearOnYear() {
        return cooperationLeadYearOnYear;
    }

    public void setCooperationLeadYearOnYear(String cooperationLeadYearOnYear) {
        this.cooperationLeadYearOnYear = cooperationLeadYearOnYear;
    }

    public String getCooperationLeadYearOnYearDiff() {
        return cooperationLeadYearOnYearDiff;
    }

    public void setCooperationLeadYearOnYearDiff(String cooperationLeadYearOnYearDiff) {
        this.cooperationLeadYearOnYearDiff = cooperationLeadYearOnYearDiff;
    }

    public String getCooperationLeadMonthAvg() {
        return cooperationLeadMonthAvg;
    }

    public void setCooperationLeadMonthAvg(String cooperationLeadMonthAvg) {
        this.cooperationLeadMonthAvg = cooperationLeadMonthAvg;
    }
}