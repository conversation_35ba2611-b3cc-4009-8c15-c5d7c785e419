package com.autohome.applet.dao.netcoreapi.model;

import java.util.Date;

public class ExpertBlog {
    private Integer eblogid;

    private String emembername;

    private Integer ememberid;

    private String eblogname;

    private Integer ereplycount;

    private Byte eisclose;

    private Date eadddate;

    private String einteoduce;

    private Integer eeditorid;

    private String enickname;

    private String ememberpic;

    private String echannelurl;

    private Byte esex;

    private Date ebirthday;

    private Date ejoindate;

    private String edepartment;

    private String eposition;

    private String ehobby;

    private String estatus;

    private String echannelname;

    private String epicturesquare;

    private String ememberpicnew;

    private String esignature;

    private Byte elevel;

    private String etitle;

    public Integer getEblogid() {
        return eblogid;
    }

    public void setEblogid(Integer eblogid) {
        this.eblogid = eblogid;
    }

    public String getEmembername() {
        return emembername;
    }

    public void setEmembername(String emembername) {
        this.emembername = emembername == null ? null : emembername.trim();
    }

    public Integer getEmemberid() {
        return ememberid;
    }

    public void setEmemberid(Integer ememberid) {
        this.ememberid = ememberid;
    }

    public String getEblogname() {
        return eblogname;
    }

    public void setEblogname(String eblogname) {
        this.eblogname = eblogname == null ? null : eblogname.trim();
    }

    public Integer getEreplycount() {
        return ereplycount;
    }

    public void setEreplycount(Integer ereplycount) {
        this.ereplycount = ereplycount;
    }

    public Byte getEisclose() {
        return eisclose;
    }

    public void setEisclose(Byte eisclose) {
        this.eisclose = eisclose;
    }

    public Date getEadddate() {
        return eadddate;
    }

    public void setEadddate(Date eadddate) {
        this.eadddate = eadddate;
    }

    public String getEinteoduce() {
        return einteoduce;
    }

    public void setEinteoduce(String einteoduce) {
        this.einteoduce = einteoduce == null ? null : einteoduce.trim();
    }

    public Integer getEeditorid() {
        return eeditorid;
    }

    public void setEeditorid(Integer eeditorid) {
        this.eeditorid = eeditorid;
    }

    public String getEnickname() {
        return enickname;
    }

    public void setEnickname(String enickname) {
        this.enickname = enickname == null ? null : enickname.trim();
    }

    public String getEmemberpic() {
        return ememberpic;
    }

    public void setEmemberpic(String ememberpic) {
        this.ememberpic = ememberpic == null ? null : ememberpic.trim();
    }

    public String getEchannelurl() {
        return echannelurl;
    }

    public void setEchannelurl(String echannelurl) {
        this.echannelurl = echannelurl == null ? null : echannelurl.trim();
    }

    public Byte getEsex() {
        return esex;
    }

    public void setEsex(Byte esex) {
        this.esex = esex;
    }

    public Date getEbirthday() {
        return ebirthday;
    }

    public void setEbirthday(Date ebirthday) {
        this.ebirthday = ebirthday;
    }

    public Date getEjoindate() {
        return ejoindate;
    }

    public void setEjoindate(Date ejoindate) {
        this.ejoindate = ejoindate;
    }

    public String getEdepartment() {
        return edepartment;
    }

    public void setEdepartment(String edepartment) {
        this.edepartment = edepartment == null ? null : edepartment.trim();
    }

    public String getEposition() {
        return eposition;
    }

    public void setEposition(String eposition) {
        this.eposition = eposition == null ? null : eposition.trim();
    }

    public String getEhobby() {
        return ehobby;
    }

    public void setEhobby(String ehobby) {
        this.ehobby = ehobby == null ? null : ehobby.trim();
    }

    public String getEstatus() {
        return estatus;
    }

    public void setEstatus(String estatus) {
        this.estatus = estatus == null ? null : estatus.trim();
    }

    public String getEchannelname() {
        return echannelname;
    }

    public void setEchannelname(String echannelname) {
        this.echannelname = echannelname == null ? null : echannelname.trim();
    }

    public String getEpicturesquare() {
        return epicturesquare;
    }

    public void setEpicturesquare(String epicturesquare) {
        this.epicturesquare = epicturesquare == null ? null : epicturesquare.trim();
    }

    public String getEmemberpicnew() {
        return ememberpicnew;
    }

    public void setEmemberpicnew(String ememberpicnew) {
        this.ememberpicnew = ememberpicnew == null ? null : ememberpicnew.trim();
    }

    public String getEsignature() {
        return esignature;
    }

    public void setEsignature(String esignature) {
        this.esignature = esignature == null ? null : esignature.trim();
    }

    public Byte getElevel() {
        return elevel;
    }

    public void setElevel(Byte elevel) {
        this.elevel = elevel;
    }

    public String getEtitle() {
        return etitle;
    }

    public void setEtitle(String etitle) {
        this.etitle = etitle == null ? null : etitle.trim();
    }
}