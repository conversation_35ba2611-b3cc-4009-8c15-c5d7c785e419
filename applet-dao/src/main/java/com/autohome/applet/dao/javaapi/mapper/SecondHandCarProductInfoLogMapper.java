package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfoLog;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface SecondHandCarProductInfoLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SecondHandCarProductInfoLog row);

    SecondHandCarProductInfoLog selectByPrimaryKey(Long id);

    List<SecondHandCarProductInfoLog> selectAll();

    int updateByPrimaryKey(SecondHandCarProductInfoLog row);
}