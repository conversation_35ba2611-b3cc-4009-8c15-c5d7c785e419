package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.AppletOpenAutohomeRelation;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface AppletOpenAutohomeRelationMapper {
    List<AppletOpenAutohomeRelation> getUserIdByOpenIds(@Param("openIdList") List<String> openIdList,@Param("platformType") Integer platformType);
}
