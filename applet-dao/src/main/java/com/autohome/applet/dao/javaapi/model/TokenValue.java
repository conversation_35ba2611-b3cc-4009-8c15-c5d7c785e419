package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class TokenValue {
    private Integer id;

    private String tokenvalue;

    private Integer wxtype;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    private byte[] rv;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTokenvalue() {
        return tokenvalue;
    }

    public void setTokenvalue(String tokenvalue) {
        this.tokenvalue = tokenvalue == null ? null : tokenvalue.trim();
    }

    public Integer getWxtype() {
        return wxtype;
    }

    public void setWxtype(Integer wxtype) {
        this.wxtype = wxtype;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public byte[] getRv() {
        return rv;
    }

    public void setRv(byte[] rv) {
        this.rv = rv;
    }
}