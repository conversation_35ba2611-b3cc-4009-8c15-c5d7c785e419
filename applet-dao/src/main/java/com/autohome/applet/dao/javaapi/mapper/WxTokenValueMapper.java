package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.WxTokenValue;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-weixin-read")
@Repository
public interface WxTokenValueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WxTokenValue row);

    WxTokenValue selectByPrimaryKey(Integer id);

    List<WxTokenValue> selectAll();

    int updateByPrimaryKey(WxTokenValue row);

    String getTop1();
}