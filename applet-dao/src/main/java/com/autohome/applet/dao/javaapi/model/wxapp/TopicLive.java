package com.autohome.applet.dao.javaapi.model.wxapp;

import com.fasterxml.jackson.annotation.JsonAlias;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/3/10.
 */
public class TopicLive {
    @JsonAlias(value = {"topicid", "topicId"})
    private int topicId;
    @JsonAlias(value = {"liveid", "liveId"})
    private int liveId;

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public int getLiveId() {
        return liveId;
    }

    public void setLiveId(int liveId) {
        this.liveId = liveId;
    }
}
