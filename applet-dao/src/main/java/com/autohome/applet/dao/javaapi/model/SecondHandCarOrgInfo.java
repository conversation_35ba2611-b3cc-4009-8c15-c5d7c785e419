package com.autohome.applet.dao.javaapi.model;

import com.fasterxml.jackson.annotation.JsonAlias;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

public class SecondHandCarOrgInfo {
    private Long id;
    @JsonAlias({"org_id"})
    private String orgId;

    @JsonAlias({"org_name"})
    private String orgName;

    @JsonAlias({"branch_name"})
    private String branchName;

    @JsonAlias({"org_icon"})
    private String orgIcon;

    @JsonAlias({"org_score"})
    private BigDecimal orgScore;

    @JsonAlias({"org_commentnum"})
    private int orgCommentnum;

    @JsonAlias({"org_avgprice"})
    private int orgAvgprice;

    @JsonAlias({"org_area"})
    private String orgArea;

    @JsonAlias({"org_province"})
    private String orgProvince;

    @JsonAlias({"org_city"})
    private String orgCity;

    @JsonAlias({"org_district"})
    private String orgDistrict;

    @JsonAlias({"lng"})
    private String lng;

    @JsonAlias({"lat"})
    private String lat;

    @JsonAlias({"org_commentdetail"})
    private String orgCommentdetail;

    @JsonAlias({"org_servicetab"})
    private String orgServicetab;

    @JsonAlias({"org_year"})
    private int orgYear;

    @JsonAlias({"org_case"})
    private int orgCase;

    @JsonAlias({"org_servicecount"})
    private int orgServicecount;

    @JsonAlias({"org_brand"})
    private String orgBrand;

    @JsonAlias({"org_category"})
    private String orgCategory;

    @JsonAlias({"org_url"})
    private String orgUrl;

    @JsonAlias({"org_worktime"})
    private String orgWorktime;

    @JsonAlias({"org_workday"})
    private String orgWorkday;

    @JsonAlias({"org_address"})
    private String orgAddress;

    @JsonAlias({"org_tel"})
    private String orgTel;

    @JsonAlias({"org_sourcename"})
    private String orgSourcename;

    private Integer siteid;

    @JsonAlias({"org_gift"})
    private String orgGift;

    @JsonAlias({"org_package"})
    private String orgPackage;

    /**
     * org_status: 门店状态，门店状态(1/0，1-状态正常（含门店下班后打烊），0-状态不正常（如门店下线
     * */
    @JsonAlias({"org_status"})
    private int orgStatus;

    @JsonAlias({"org_verticalpic"})
    private String orgVerticalpic;

    @JsonAlias({"org_description"})
    private String orgDescription;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    private Long version;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId == null ? "" : orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getOrgName() {
        return orgName == null ? "" : orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getBranchName() {
        return branchName == null ? "" : branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName == null ? null : branchName.trim();
    }

    public String getOrgIcon() {
        return orgIcon == null ? "" : orgIcon;
    }

    public void setOrgIcon(String orgIcon) {
        this.orgIcon = orgIcon == null ? null : orgIcon.trim();
    }

    public BigDecimal getOrgScore() {
        return orgScore;
    }

    public void setOrgScore(BigDecimal orgScore) {
        this.orgScore = orgScore;
    }

    public Integer getOrgCommentnum() {
        Optional<Integer> optionalInteger = Optional.ofNullable(orgCommentnum);
        if (optionalInteger.isPresent()) {
            return orgCommentnum;
        } else {
            return 0;
        }
    }

    public void setOrgCommentnum(Integer orgCommentnum) {
        this.orgCommentnum = orgCommentnum;
    }

    public String getOrgArea() {
        return orgArea == null ? "" : orgArea;
    }

    public void setOrgArea(String orgArea) {
        this.orgArea = orgArea == null ? null : orgArea.trim();
    }

    public String getOrgProvince() {
        return orgProvince == null ? "" : orgProvince;
    }

    public void setOrgProvince(String orgProvince) {
        this.orgProvince = orgProvince == null ? null : orgProvince.trim();
    }

    public String getOrgCity() {
        return orgCity == null ? "" : orgCity;
    }

    public void setOrgCity(String orgCity) {
        this.orgCity = orgCity == null ? null : orgCity.trim();
    }

    public String getOrgDistrict() {
        return orgDistrict == null ? "" : orgDistrict;
    }

    public void setOrgDistrict(String orgDistrict) {
        this.orgDistrict = orgDistrict == null ? null : orgDistrict.trim();
    }

    public String getLng() {
        return lng == null ? "" : lng;
    }

    public void setLng(String lng) {
        this.lng = lng == null ? null : lng.trim();
    }

    public String getLat() {
        return lat == null ? "" : lat;
    }

    public void setLat(String lat) {
        this.lat = lat == null ? null : lat.trim();
    }

    public String getOrgCommentdetail() {
        return orgCommentdetail == null ? "" : orgCommentdetail;
    }

    public void setOrgCommentdetail(String orgCommentdetail) {
        this.orgCommentdetail = orgCommentdetail == null ? null : orgCommentdetail.trim();
    }

    public String getOrgServicetab() {
        return orgServicetab == null ? "" : orgServicetab;
    }

    public void setOrgServicetab(String orgServicetab) {
        this.orgServicetab = orgServicetab == null ? null : orgServicetab.trim();
    }

    public Integer getOrgYear() {
        Optional<Integer> optionalInteger = Optional.ofNullable(orgYear);
        if (optionalInteger.isPresent()) {
            return orgYear;
        } else {
            return 0;
        }
    }

    public void setOrgYear(Integer orgYear) {
        this.orgYear = orgYear;
    }

    public Integer getOrgCase() {
        Optional<Integer> optionalInteger = Optional.ofNullable(orgCase);
        if (optionalInteger.isPresent()) {
            return orgCase;
        } else {
            return 0;
        }
    }

    public void setOrgCase(Integer orgCase) {
        this.orgCase = orgCase;
    }

    public Integer getOrgServicecount() {
        Optional<Integer> optionalInteger = Optional.ofNullable(orgServicecount);
        if (optionalInteger.isPresent()) {
            return orgServicecount;
        } else {
            return 0;
        }
    }

    public void setOrgServicecount(Integer orgServicecount) {
        this.orgServicecount = orgServicecount;
    }

    public String getOrgBrand() {
        return orgBrand == null ? "" : orgBrand;
    }

    public void setOrgBrand(String orgBrand) {
        this.orgBrand = orgBrand == null ? null : orgBrand.trim();
    }

    public String getOrgCategory() {
        return orgCategory == null ? "" : orgCategory;
    }

    public void setOrgCategory(String orgCategory) {
        this.orgCategory = orgCategory == null ? null : orgCategory.trim();
    }

    public String getOrgUrl() {
        return orgUrl == null ? "" : orgUrl;
    }

    public void setOrgUrl(String orgUrl) {
        this.orgUrl = orgUrl == null ? null : orgUrl.trim();
    }

    public String getOrgWorktime() {
        return orgWorktime == null ? "" : orgWorktime;
    }

    public void setOrgWorktime(String orgWorktime) {
        this.orgWorktime = orgWorktime == null ? null : orgWorktime.trim();
    }

    public String getOrgWorkday() {
        return orgWorkday == null ? "" : orgWorkday;
    }

    public void setOrgWorkday(String orgWorkday) {
        this.orgWorkday = orgWorkday == null ? null : orgWorkday.trim();
    }

    public String getOrgAddress() {
        return orgAddress == null ? "" : orgAddress;
    }

    public void setOrgAddress(String orgAddress) {
        this.orgAddress = orgAddress == null ? null : orgAddress.trim();
    }

    public String getOrgTel() {
        return orgTel == null ? "" : orgTel;
    }

    public void setOrgTel(String orgTel) {
        this.orgTel = orgTel == null ? null : orgTel.trim();
    }

    public String getOrgSourcename() {
        return orgSourcename == null ? "" : orgSourcename;
    }

    public void setOrgSourcename(String orgSourcename) {
        this.orgSourcename = orgSourcename == null ? null : orgSourcename.trim();
    }

    public Integer getSiteid() {
        return siteid == null ? 0 : siteid;
    }

    public void setSiteid(Integer siteid) {
        this.siteid = siteid;
    }

    public String getOrgGift() {
        return orgGift == null ? "" : orgGift;
    }

    public void setOrgGift(String orgGift) {
        this.orgGift = orgGift == null ? null : orgGift.trim();
    }

    public String getOrgPackage() {
        return orgPackage == null ? "" : orgPackage;
    }

    public void setOrgPackage(String orgPackage) {
        this.orgPackage = orgPackage == null ? null : orgPackage.trim();
    }

    public Integer getOrgStatus() {
        Optional<Integer> optionalInteger = Optional.ofNullable(orgStatus);
        if (optionalInteger.isPresent()) {
            return orgStatus;
        } else {
            return 0;
        }
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public String getOrgVerticalpic() {
        return orgVerticalpic == null ? "" : orgVerticalpic;
    }

    public void setOrgVerticalpic(String orgVerticalpic) {
        this.orgVerticalpic = orgVerticalpic == null ? null : orgVerticalpic.trim();
    }

    public String getOrgDescription() {
        return orgDescription == null ? "" : orgDescription;
    }

    public void setOrgDescription(String orgDescription) {
        this.orgDescription = orgDescription == null ? null : orgDescription.trim();
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel == null ? 0 : isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public void setOrgCommentnum(int orgCommentnum) {
        this.orgCommentnum = orgCommentnum;
    }

    public int getOrgAvgprice() {
        return orgAvgprice;
    }

    public void setOrgAvgprice(int orgAvgprice) {
        this.orgAvgprice = orgAvgprice;
    }

    public void setOrgYear(int orgYear) {
        this.orgYear = orgYear;
    }

    public void setOrgCase(int orgCase) {
        this.orgCase = orgCase;
    }

    public void setOrgServicecount(int orgServicecount) {
        this.orgServicecount = orgServicecount;
    }

    public void setOrgStatus(int orgStatus) {
        this.orgStatus = orgStatus;
    }
}