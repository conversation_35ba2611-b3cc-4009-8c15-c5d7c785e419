package com.autohome.applet.dao.javaapi.model.wxapp;

import com.autohome.applet.dao.javaapi.model.wxapp.topic.ContentCard;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.util.Date;
import java.util.List;

/**
 * Created by pengyi on 2016/12/13.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ArticleDetailsResult {
    public ArticleDetailsResult() {
    }

    private int id;
    private String title;
    private String shortTitle;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;
    private String source;
    private String type;
    private int authorI;
    private String author;
    private int class1;
    private String class1Name;
    private int class2;
    private String class2Name;
    private String seriesIds;
    private int seriesId;
    private int dealerId;
    private String content;
    private String description;
    private int clickCount;
    //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatetime;
    private boolean isclosecomment;
    @JsonProperty("Images")
    private Image[] images;
    @JsonProperty("IsPublish")
    private boolean isPublish;
    private List<ContentCard> contentCards;
    private String img;
    private int replyCount;

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    @JsonDeserialize(using = CustomTimestampDeserializer.class)
    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getAuthorI() {
        return authorI;
    }

    public void setAuthorI(int authorI) {
        this.authorI = authorI;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public int getClass1() {
        return class1;
    }

    public void setClass1(int class1) {
        this.class1 = class1;
    }

    public String getClass1Name() {
        return class1Name;
    }

    public void setClass1Name(String class1Name) {
        this.class1Name = class1Name;
    }

    public int getClass2() {
        return class2;
    }

    public void setClass2(int class2) {
        this.class2 = class2;
    }

    public String getClass2Name() {
        return class2Name;
    }

    public void setClass2Name(String class2Name) {
        this.class2Name = class2Name;
    }

    public String getSeriesIds() {
        return seriesIds;
    }

    public void setSeriesIds(String seriesIds) {
        this.seriesIds = seriesIds;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getDealerId() {
        return dealerId;
    }

    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getClickCount() {
        return clickCount;
    }

    public void setClickCount(int clickCount) {
        this.clickCount = clickCount;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    @JsonDeserialize(using = CustomTimestampDeserializer.class)
    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public boolean isclosecomment() {
        return isclosecomment;
    }

    public void setIsclosecomment(boolean isclosecomment) {
        this.isclosecomment = isclosecomment;
    }

    public Image[] getImages() {
        return images;
    }

    public void setImages(Image[] images) {
        this.images = images;
    }

    public boolean isPublish() {
        return isPublish;
    }

    public void setPublish(boolean publish) {
        isPublish = publish;
    }

    public List<ContentCard> getContentCards() {
        return contentCards;
    }

    public void setContentCards(List<ContentCard> contentCards) {
        this.contentCards = contentCards;
    }

    public int getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(int replyCount) {
        this.replyCount = replyCount;
    }

    public static class Image {

        private int GroupId;

        private String Text;

        private String Img;

        private String ImgTitle;

        private String ImgLink;

        private int Type;

        private String VideoId;

        public void setGroupId(int GroupId) {
            this.GroupId = GroupId;
        }

        public int getGroupId() {
            return this.GroupId;
        }

        public void setText(String Text) {
            this.Text = Text;
        }

        public String getText() {
            return this.Text;
        }

        public void setImg(String Img) {
            this.Img = Img;
        }

        public String getImg() {
            return this.Img;
        }

        public void setImgTitle(String ImgTitle) {
            this.ImgTitle = ImgTitle;
        }

        public String getImgTitle() {
            return this.ImgTitle;
        }

        public void setImgLink(String ImgLink) {
            this.ImgLink = ImgLink;
        }

        public String getImgLink() {
            return this.ImgLink;
        }

        public void setType(int Type) {
            this.Type = Type;
        }

        public int getType() {
            return this.Type;
        }

        public void setVideoId(String VideoId) {
            this.VideoId = VideoId;
        }

        public String getVideoId() {
            return this.VideoId;
        }


    }
}
