package com.autohome.applet.dao.javaapi.model.wxapp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
 * Created by pengyi on 2016/12/13.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoContentResult {
    /*            "id": 96369,
                        "videoid": "514238A53912B303",
                        "title": "最强较真女司机 我为啥要给你驾驶证？",
                        "shorttitle": "当交警遇到较真的女司机",
                        "picurl": "http://www2.autoimg.cn/newsdfs/g5/M0E/BA/22/120x90_0_autohomecar__wKjB0lhGXneAQB5RAADCLJFZeyU488.jpg",
                        "pic_120": null,
                        "pic_160": "http://www3.autoimg.cn/newsdfs/g14/M14/B6/E1/160x90_0_autohomecar__wKgH1VhGXneAZ_X5AACecGLkwwY418.jpg",
                        "pic_400": "http://www2.autoimg.cn/newsdfs/g5/M0E/BA/22/400x300_0_autohomecar__wKjB0lhGXneAQB5RAADCLJFZeyU488.jpg",
                        "pic_600": null,
                        "duration": "162",
                        "playtimes": 286787,
                        "youkuvideokey": "",
                        "commentnum": 5761,
                        "inputtime": "2016/12/6 14:45:13",
                        "categoryid": 4,
                        "categoryname": "花边",
                        "link": "http://v.autohome.com.cn/v-96369.html",
                        "description": "视频中这位昆明交警可能遇到了其职业生涯中最较真的女司机，请你给她一个出示驾照的理由。",
                        "nickName": "Mansory",
                        "videoaddress": "http://v.youku.com/player/getM3U8/vid//type/mp4/v.m3u8",
                        "updatetime": "/Date(1481006713000)/",
                        "PageId": null*/
    private int id;
    private String videoid;
    private String title;
    private String picurl;
    private int commentnum;
    private String description;
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date inputtime;
    private String nickName;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getVideoid() {
        return videoid;
    }

    public void setVideoid(String videoid) {
        this.videoid = videoid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPicurl() {
        return picurl;
    }

    public void setPicurl(String picurl) {
        this.picurl = picurl;
    }

    public int getCommentnum() {
        return commentnum;
    }

    public void setCommentnum(int commentnum) {
        this.commentnum = commentnum;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getInputtime() {
        return inputtime;
    }

    public void setInputtime(Date inputtime) {
        this.inputtime = inputtime;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
}
