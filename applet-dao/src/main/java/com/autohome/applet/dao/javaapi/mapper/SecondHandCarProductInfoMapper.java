package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface SecondHandCarProductInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SecondHandCarProductInfo row);

    SecondHandCarProductInfo selectByPrimaryKey(Long id);

    List<SecondHandCarProductInfo> selectAll();

    int updateByPrimaryKey(SecondHandCarProductInfo row);

    int save(SecondHandCarProductInfo row);

    /**
     * 根据商品id和最新的上传版本号, 更新待上传版本
     * */
    int updateVersionByServiceIds(@Param("serviceIds") List<String> serviceIds, @Param("version") Long version);
    /**
     * 查询org下大于当前版本号的数据
     * */
    List<SecondHandCarProductInfo> listByVersion(@Param("orgId") String orgId, @Param("version") Long version);
    /**
     * 查询org下大于当前版本号的数据
     * */
    int countMoreThanByVersion(@Param("orgId") String orgId, @Param("version") Long version);
    /**
     * 更新车源状态
     * */
    int updateStatusByServiceId(@Param("serviceId") String serviceId, @Param("version") Long version, @Param("status") Integer status);
}