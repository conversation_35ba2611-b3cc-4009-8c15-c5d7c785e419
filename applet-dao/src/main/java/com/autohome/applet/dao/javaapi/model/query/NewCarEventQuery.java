package com.autohome.applet.dao.javaapi.model.query;

import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NewCarEventQuery {
    private Integer objid;
    private Integer objtype;
    /**
     * 必须按照示例格式
     * 示例： ,车系id1,
     * */
    private String seriesid;
    /**
     * yyyyMMdd
     * */
    private String publishDate;

    private List<Integer> idList;
}
