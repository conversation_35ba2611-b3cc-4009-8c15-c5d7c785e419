package com.autohome.applet.dao.javaapi.model;

public class ResourceFeedWithBLOBs extends ResourceFeed {
    private String title;

    private String author;

    private String authorId;

    private String imgUrl;

    private String modifyTime;

    private String summary;

    private String replyCount;

    private String viewCount;

    private String isDelete;

    private String content;

    private String subjectId;

    private String cmsSeriesIds;

    private String cmsContentClass;

    private String cmsTags;

    private String categoryTags;

    private String recommendTime;

    private String duration;

    private String graphicImgList;

    private String updateAt;

    private String authorIcon;

    private String vId;

    private String cmsSeriesNames;

    private String nlpTagsChoose2;

    private String isCloseComment;

    private String graphicImgList3;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author == null ? null : author.trim();
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId == null ? null : authorId.trim();
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl == null ? null : imgUrl.trim();
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime == null ? null : modifyTime.trim();
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary == null ? null : summary.trim();
    }

    public String getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(String replyCount) {
        this.replyCount = replyCount == null ? null : replyCount.trim();
    }

    public String getViewCount() {
        return viewCount;
    }

    public void setViewCount(String viewCount) {
        this.viewCount = viewCount == null ? null : viewCount.trim();
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete == null ? null : isDelete.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId == null ? null : subjectId.trim();
    }

    public String getCmsSeriesIds() {
        return cmsSeriesIds;
    }

    public void setCmsSeriesIds(String cmsSeriesIds) {
        this.cmsSeriesIds = cmsSeriesIds == null ? null : cmsSeriesIds.trim();
    }

    public String getCmsContentClass() {
        return cmsContentClass;
    }

    public void setCmsContentClass(String cmsContentClass) {
        this.cmsContentClass = cmsContentClass == null ? null : cmsContentClass.trim();
    }

    public String getCmsTags() {
        return cmsTags;
    }

    public void setCmsTags(String cmsTags) {
        this.cmsTags = cmsTags == null ? null : cmsTags.trim();
    }

    public String getCategoryTags() {
        return categoryTags;
    }

    public void setCategoryTags(String categoryTags) {
        this.categoryTags = categoryTags == null ? null : categoryTags.trim();
    }

    public String getRecommendTime() {
        return recommendTime;
    }

    public void setRecommendTime(String recommendTime) {
        this.recommendTime = recommendTime == null ? null : recommendTime.trim();
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration == null ? null : duration.trim();
    }

    public String getGraphicImgList() {
        return graphicImgList;
    }

    public void setGraphicImgList(String graphicImgList) {
        this.graphicImgList = graphicImgList == null ? null : graphicImgList.trim();
    }

    public String getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(String updateAt) {
        this.updateAt = updateAt == null ? null : updateAt.trim();
    }

    public String getAuthorIcon() {
        return authorIcon;
    }

    public void setAuthorIcon(String authorIcon) {
        this.authorIcon = authorIcon == null ? null : authorIcon.trim();
    }

    public String getvId() {
        return vId;
    }

    public void setvId(String vId) {
        this.vId = vId == null ? null : vId.trim();
    }

    public String getCmsSeriesNames() {
        return cmsSeriesNames;
    }

    public void setCmsSeriesNames(String cmsSeriesNames) {
        this.cmsSeriesNames = cmsSeriesNames == null ? null : cmsSeriesNames.trim();
    }

    public String getNlpTagsChoose2() {
        return nlpTagsChoose2;
    }

    public void setNlpTagsChoose2(String nlpTagsChoose2) {
        this.nlpTagsChoose2 = nlpTagsChoose2 == null ? null : nlpTagsChoose2.trim();
    }

    public String getIsCloseComment() {
        return isCloseComment;
    }

    public void setIsCloseComment(String isCloseComment) {
        this.isCloseComment = isCloseComment == null ? null : isCloseComment.trim();
    }

    public String getGraphicImgList3() {
        return graphicImgList3;
    }

    public void setGraphicImgList3(String graphicImgList3) {
        this.graphicImgList3 = graphicImgList3 == null ? null : graphicImgList3.trim();
    }
}