package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.wxmsg.WxMessage;
import com.autohome.applet.dao.javaapi.model.wxmsg.WxMessageQuery;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-weixin")
@Repository
public interface WxMessageMapper {
    int insert(WxMessage row);
    /**
     * 查询数量(未发送或者发送中的数量)
     * */
    int countWxMessage(WxMessageQuery wxMessageQuery);
    /**
     * 查询列表
     * */
    List<WxMessage> listWxMessage(WxMessageQuery wxMessageQuery);
}