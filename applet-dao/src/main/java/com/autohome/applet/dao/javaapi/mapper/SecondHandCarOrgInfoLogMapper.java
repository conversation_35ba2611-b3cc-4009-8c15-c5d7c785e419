package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfoLog;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface SecondHandCarOrgInfoLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SecondHandCarOrgInfoLog row);

    SecondHandCarOrgInfoLog selectByPrimaryKey(Long id);

    List<SecondHandCarOrgInfoLog> selectAll();

    int updateByPrimaryKey(SecondHandCarOrgInfoLog row);
}