package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.PanoramicBuyUseCarDailyRepoterDingding;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@DS("db-liveplatform-read")
@Mapper
@Repository
public interface PanoramicBuyUseCarDailyRepoterDingdingMapper {

    PanoramicBuyUseCarDailyRepoterDingding getReport(@Param("dt") String dt);
}