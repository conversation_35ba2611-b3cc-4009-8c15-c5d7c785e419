package com.autohome.applet.dao.javaapi.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CLueDoJobDTO {
    /**
     * 活动平台分配的code
     * */
    private String code;
    private Integer userId;
    private Integer getReward;
    private String requestId;
    /**
     * 风控状态，默认为0（主要应用于完成任务前过风控）
     * */
    private int riskCode;

    /**
     * 以下为活动风控需要的参数
     * */
    private String sessionid;
    private String refer;
    private String ua;
    private String ip;
    /**
     * 用户行为时间，格式为yyyy-MM-dd HH:mm:ss
     * */
    private String reqtime;
    private String sessionip;
    /**
     * APP-ANDROID，APP-IOS ，M-ANDROID ，M-IOS , PC，WX（小程序）
     * */
    private String platform;
    private String appversion;
    /**
     * 业务app上报 或 app_deviceid cookie值，小程序端是 openid
     * */
    private String deviceid;
    /**
     * 用户token
     * */
    private String userToken;

    private HashMap busiMap;

}
