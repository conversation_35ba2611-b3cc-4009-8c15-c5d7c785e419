package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.TokenValue;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen-read")
@Repository
public interface TokenValueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TokenValue row);

    TokenValue selectByPrimaryKey(Integer id);

    List<TokenValue> selectAll();

    int updateByPrimaryKey(TokenValue row);

    String getAccessTokenForType(@Param("wxType") int wxType);
}