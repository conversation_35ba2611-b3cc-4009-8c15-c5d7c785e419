package com.autohome.applet.dao.javaapi.model.wxapp;

import java.util.Date;

public class UserView {
    private Integer id;

    private Integer uid;

    private Integer exId;

    private Integer type;

    private Date createTime;

    private Date modifyTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }

    public Integer getExId() {
        return exId;
    }

    public void setExId(Integer exId) {
        this.exId = exId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "UserView{" + "id=" + id + ", uid=" + uid + ", exId=" + exId + ", type=" + type + ", createTime=" + createTime + ", modifyTime=" +
                modifyTime + '}';
    }
}