package com.autohome.applet.dao.javaapi.model.wxapp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by pengyi on 2016/12/13.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoAddressResult {
    /*     "mediainfos": [
        {
            "status": 1,
                "mid": "514238A53912B303",
                "vtag": 78257,
                "mt": 1,
                "img": "http://img2.autoimg.cn/g8/M05/BE/A6/wKgH3lhGWyyAN0N1AAA6b0sEXmo065.jpg",
                "copies": [
            {
                "quality": 100,
                    "desp": "标清",
                    "playurl": "http://m10.play.vp.autohome.com.cn/flvs/E3BD4E39114FD258/2016-12-06/514238A53912B303-100.mp4?key=EB4A0D52845273603F8A929A6AA45A0F&time=1481625575"
            },
            {
                "quality": 200,
                    "desp": "高清",
                    "playurl": "http://m9.play.vp.autohome.com.cn/flvs/E3BD4E39114FD258/2016-12-06/514238A53912B303-200.mp4?key=490EC4E58B81C446B69B6FEFE051C51F&time=1481625575"
            }
                ]*/
    private MediaInfo[] mediainfos;

    public MediaInfo[] getMediainfos() {
        return mediainfos;
    }

    public void setMediainfos(MediaInfo[] mediainfos) {
        this.mediainfos = mediainfos;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MediaInfo {
        private String mid;
        private String img;
        private MediaCopy[] copies;

        public String getMid() {
            return mid;
        }

        public void setMid(String mid) {
            this.mid = mid;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public MediaCopy[] getCopies() {
            return copies;
        }

        public void setCopies(MediaCopy[] copies) {
            this.copies = copies;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MediaCopy {
        private int quality;
        private String desp;
        private String playurl;

        public int getQuality() {
            return quality;
        }

        public void setQuality(int quality) {
            this.quality = quality;
        }

        public String getDesp() {
            return desp;
        }

        public void setDesp(String desp) {
            this.desp = desp;
        }

        public String getPlayurl() {
            return playurl;
        }

        public void setPlayurl(String playurl) {
            this.playurl = playurl;
        }
    }
}
