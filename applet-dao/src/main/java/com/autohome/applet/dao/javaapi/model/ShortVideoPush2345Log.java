package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class ShortVideoPush2345Log {
    private Long id;

    private String authorIds;

    private String bizIds;

    private String mainDataType;

    private String pushType;

    private String synBeginTime;

    private String synEndTime;

    private String requestUrl;

    private String requestData;

    private String requestCode;

    private Integer requestStatus;

    private String responseData;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthorIds() {
        return authorIds;
    }

    public void setAuthorIds(String authorIds) {
        this.authorIds = authorIds == null ? null : authorIds.trim();
    }

    public String getBizIds() {
        return bizIds;
    }

    public void setBizIds(String bizIds) {
        this.bizIds = bizIds == null ? null : bizIds.trim();
    }

    public String getMainDataType() {
        return mainDataType;
    }

    public void setMainDataType(String mainDataType) {
        this.mainDataType = mainDataType == null ? null : mainDataType.trim();
    }

    public String getPushType() {
        return pushType;
    }

    public void setPushType(String pushType) {
        this.pushType = pushType == null ? null : pushType.trim();
    }

    public String getSynBeginTime() {
        return synBeginTime;
    }

    public void setSynBeginTime(String synBeginTime) {
        this.synBeginTime = synBeginTime == null ? null : synBeginTime.trim();
    }

    public String getSynEndTime() {
        return synEndTime;
    }

    public void setSynEndTime(String synEndTime) {
        this.synEndTime = synEndTime == null ? null : synEndTime.trim();
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl == null ? null : requestUrl.trim();
    }

    public String getRequestData() {
        return requestData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData == null ? null : requestData.trim();
    }

    public String getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(String requestCode) {
        this.requestCode = requestCode == null ? null : requestCode.trim();
    }

    public Integer getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(Integer requestStatus) {
        this.requestStatus = requestStatus;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData == null ? null : responseData.trim();
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }
}