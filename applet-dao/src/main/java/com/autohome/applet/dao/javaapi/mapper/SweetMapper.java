package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.Sweet;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-weixin-read")
@Repository("managerSweetMapper")
public interface SweetMapper {

    @DS("db-weixin-read")
    @Select({
            "INSERT INTO [Sweet](                    \n",
            "  [SweetName]                    \n",
            "  ,[ClientType]                    \n",
            "  ,[JumpTo]                    \n",
            "  ,[Img]                    \n",
            "  ,[StartTime]                    \n",
            "  ,[EndTime]                    \n",
            "  ,[SortNum]                    \n",
            "  ,[Status]                    \n",
            "  ,[Recycled]                    \n",
            "  ,[Modified_STime]                    \n",
            "  ,[Created_STime]                    \n",
            "  ,[JumpType]                    \n",
            "  ,[OtherAppid]                    \n",
            "  ,[ReportPoint]                    \n",
            "  ,[ShareImg]                    \n",
            "  ,[ShareDesc]                    \n",
            "  ,[ContentType]                    \n",
            "  ,[ShowPoint]                    \n",
            "  ,[Ext]                    \n",
            "  ,[NeedLogin]                    \n",
            "  ,[CategoryPart]                    \n",
            "  ,[ForceSort]                    \n",
            ")                              \n",
            "VALUES(                        \n",
            "  #{sweetname}                    \n",
            "  ,#{clienttype}                    \n",
            "  ,#{jumpto}                    \n",
            "  ,#{img}                    \n",
            "  ,#{starttime}                    \n",
            "  ,#{endtime}                    \n",
            "  ,#{sortnum}                    \n",
            "  ,#{status}                    \n",
            "  ,#{recycled}                    \n",
            ",GETDATE()                     \n",
            ",GETDATE()                     \n",
            "  ,#{jumptype}                    \n",
            "  ,#{otherappid}                    \n",
            "  ,#{reportpoint}                    \n",
            "  ,#{shareimg}                    \n",
            "  ,#{sharedesc}                    \n",
            "  ,#{contenttype}                    \n",
            "  ,#{showpoint}                    \n",
            "  ,#{ext}                    \n",
            "  ,#{needlogin}                    \n",
            "  ,#{categorypart}                    \n",
            "  ,#{forcesort}                    \n",
            ")                              \n",
            "SELECT @@IDENTITY              \n",
    })
    int insert(Sweet row);

    Sweet selectByPrimaryKey(Integer sweetid);

    List<Sweet> selectAll();


    Sweet getLastForClientType(int clientType);

    List<Sweet> getHomeFeed7();

    List<Sweet> getHomeFocus2();

    List<Sweet> getDetailAd(int clientType);

    void del(@Param("sweetId") Integer sweetId);
    void resetSweet(Sweet updateSweet);

    void resetSweet7All();

    void resetSweet2All();

    void updateById(@Param("sweetId") Integer sweetId);
}