package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.AppletDoTaskLog;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface AppletDoTaskLogMapper {
    List<AppletDoTaskLog> getUnexecutedList(@Param("startDate") String startDate, @Param("endDate")String endDate, @Param("pageSize") Integer pageSize, @Param("lastId")Long lastId);

    void updateByPrimaryKey(AppletDoTaskLog doTaskLog);
}
