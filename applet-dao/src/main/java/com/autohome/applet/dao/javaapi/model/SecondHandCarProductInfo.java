package com.autohome.applet.dao.javaapi.model;

import com.fasterxml.jackson.annotation.JsonAlias;

import java.util.Date;

public class SecondHandCarProductInfo {
    private Long id;

    @JsonAlias({"service_id"})
    private String serviceId;

    private String name;

    private Integer type;

    private String pic;

    private String tabs;

    @JsonAlias({"info_url"})
    private String infoUrl;

    private String comment;

    private String description;

    private String originalprice;

    private String finalprice;

    private String category;

    /**
     * status: 状态，1/0，1-状态正常，0-状态不正常（如商产品下线/下架/售罄/过期/失效等）
     * */
    private Integer status;

    @JsonAlias({"org_id"})
    private String orgId;

    @JsonAlias({"car_name"})
    private String carName;

    @JsonAlias({"car_brand"})
    private String carBrand;

    @JsonAlias({"car_gear"})
    private String carGear;

    @JsonAlias({"car_volume"})
    private String carVolume;

    @JsonAlias({"car_type"})
    private String carType;

    @JsonAlias({"car_seat"})
    private String carSeat;

    @JsonAlias({"car_year"})
    private String carYear;

    @JsonAlias({"car_plate"})
    private String carPlate;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    private Long version;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceId() {
        return serviceId == null ? "" : serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId == null ? null : serviceId.trim();
    }

    public String getName() {
        return name == null ? "" : name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getType() {
        return type == null ? 0 : type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPic() {
        return pic == null ? "" : pic;
    }

    public void setPic(String pic) {
        this.pic = pic == null ? null : pic.trim();
    }

    public String getTabs() {
        return tabs == null ? "" : tabs;
    }

    public void setTabs(String tabs) {
        this.tabs = tabs == null ? null : tabs.trim();
    }

    public String getInfoUrl() {
        return infoUrl == null ? "" : infoUrl;
    }

    public void setInfoUrl(String infoUrl) {
        this.infoUrl = infoUrl == null ? null : infoUrl.trim();
    }

    public String getComment() {
        return comment == null ? "" : comment;
    }

    public void setComment(String comment) {
        this.comment = comment == null ? null : comment.trim();
    }

    public String getDescription() {
        return description == null ? "" : description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getOriginalprice() {
        return originalprice == null ? "" : originalprice;
    }

    public void setOriginalprice(String originalprice) {
        this.originalprice = originalprice == null ? null : originalprice.trim();
    }

    public String getFinalprice() {
        return finalprice == null ? "" : finalprice;
    }

    public void setFinalprice(String finalprice) {
        this.finalprice = finalprice == null ? null : finalprice.trim();
    }

    public String getCategory() {
        return category == null ? "" : category;
    }

    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    public Integer getStatus() {
        return status == null ? 0 : status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOrgId() {
        return orgId == null ? "" : orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getCarName() {
        return carName == null ? "" : carName;
    }

    public void setCarName(String carName) {
        this.carName = carName == null ? null : carName.trim();
    }

    public String getCarBrand() {
        return carBrand == null ? "" : carBrand;
    }

    public void setCarBrand(String carBrand) {
        this.carBrand = carBrand == null ? null : carBrand.trim();
    }

    public String getCarGear() {
        return carGear == null ? "" : carGear;
    }

    public void setCarGear(String carGear) {
        this.carGear = carGear == null ? null : carGear.trim();
    }

    public String getCarVolume() {
        return carVolume == null ? "" : carVolume;
    }

    public void setCarVolume(String carVolume) {
        this.carVolume = carVolume == null ? null : carVolume.trim();
    }

    public String getCarType() {
        return carType == null ? "" : carType;
    }

    public void setCarType(String carType) {
        this.carType = carType == null ? null : carType.trim();
    }

    public String getCarSeat() {
        return carSeat == null ? "" : carSeat;
    }

    public void setCarSeat(String carSeat) {
        this.carSeat = carSeat == null ? null : carSeat.trim();
    }

    public String getCarYear() {
        return carYear == null ? "" : carYear;
    }

    public void setCarYear(String carYear) {
        this.carYear = carYear == null ? null : carYear.trim();
    }

    public String getCarPlate() {
        return carPlate == null ? "" : carPlate;
    }

    public void setCarPlate(String carPlate) {
        this.carPlate = carPlate == null ? null : carPlate.trim();
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel == null ? 0 : isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}