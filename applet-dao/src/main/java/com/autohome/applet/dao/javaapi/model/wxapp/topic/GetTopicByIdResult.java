package com.autohome.applet.dao.javaapi.model.wxapp.topic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by pengyi on 2017/1/12.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTopicByIdResult {
    private int isdelete;
    private int isclose;
    private int replycount;
    private int iscard;
    private int ispoll;
    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getIspoll() {
        return ispoll;
    }

    public void setIspoll(int ispoll) {
        this.ispoll = ispoll;
    }

    public int getIsdelete() {
        return isdelete;
    }

    public void setIsdelete(int isdelete) {
        this.isdelete = isdelete;
    }

    public int getIsclose() {
        return isclose;
    }

    public void setIsclose(int isclose) {
        this.isclose = isclose;
    }

    public int getReplycount() {
        return replycount;
    }

    public void setReplycount(int replycount) {
        this.replycount = replycount;
    }

    public int getIscard() {
        return iscard;
    }

    public void setIscard(int iscard) {
        this.iscard = iscard;
    }
}
