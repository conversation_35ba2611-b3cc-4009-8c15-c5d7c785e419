package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.WxServiceAccount;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-weixin")
@Repository
public interface WxServiceAccountMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxServiceAccount row);

    WxServiceAccount selectByPrimaryKey(Long id);

    List<WxServiceAccount> selectAll();

    int updateByPrimaryKey(WxServiceAccount row);

    WxServiceAccount getWxServiceAccountByUnionId(@Param("unionId")String unionId);
}
