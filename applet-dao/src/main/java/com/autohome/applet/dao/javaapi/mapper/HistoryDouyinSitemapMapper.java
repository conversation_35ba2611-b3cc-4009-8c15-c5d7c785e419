package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap;
import com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemapExample;
import com.autohome.applet.dao.javaapi.model.douyin.DouYinSiteMapHistoryDto;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
@DS("db-carstatisticsdata")
@Mapper
@Repository
public interface HistoryDouyinSitemapMapper {
    String fields = "" +
            "id   ,  \n" +
            "pagepath     \n" +
            ", source    \n" ;
    long countByExample(HistoryDouyinSitemapExample example);

    int deleteByPrimaryKey(Long id);

    int insert(HistoryDouyinSitemap row);


    int insertBatch(List<HistoryDouyinSitemap> list);

    int insertSelective(HistoryDouyinSitemap row);

    List<HistoryDouyinSitemap> selectByExample(HistoryDouyinSitemapExample example);

    HistoryDouyinSitemap selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HistoryDouyinSitemap row);

    int updateByPrimaryKey(HistoryDouyinSitemap row);

    @Update("update   history_douyin_sitemap  set status =1 " +
            " WHERE id =  #{id}   " )
    int updateById(@Param("id")   int id);

    @Select("SELECT   " + fields + " FROM history_douyin_sitemap  " +
            " WHERE status =0  " +
            "  ORDER BY id ASC"+
            "  limit #{pageSize}"
    )
    List<DouYinSiteMapHistoryDto> getDouyinSitemapHistoryList(
            @Param("pageSize")   int pageSize);
}