package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.OpenUserAlipay;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("db-autoopen")
public interface OpenUserAlipayMapper {

    OpenUserAlipay selectByPrimaryKey(Integer id);

    Integer updateDecryptKey(@Param("alipayappid") String alipayAppId, @Param("decryptKey") String decryptKey);

    @DS("db-autoopen-read")
    String getDecryptKeyById(@Param("id") Integer dealerId);
}
