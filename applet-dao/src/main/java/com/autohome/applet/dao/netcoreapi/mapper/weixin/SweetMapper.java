package com.autohome.applet.dao.netcoreapi.mapper.weixin;

import com.autohome.applet.dao.netcoreapi.model.SweetClient;
import com.autohome.applet.dao.netcoreapi.model.SweetClientCombine;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS("db-weixin-read")
public interface SweetMapper {

    @Select("SELECT DISTINCT\n" +
            "       S.SweetId,\n" +
            "       S.SweetName,\n" +
            "       S.ClientType,\n" +
            "       S.JumpTo,\n" +
            "       S.Img,\n" +
            "       S.StartTime,\n" +
            "       S.EndTime,\n" +
            "       S.JumpType,\n" +
            "       S.OtherAppid,\n" +
            "       S.ReportPoint,\n" +
            "       S.SortNum,\n" +
            "       <PERSON>.ShareDesc,\n" +
            "       <PERSON>.ShareImg,\n" +
            "       <PERSON>.ShowPoint,\n" +
            "       S.NeedLogin\n" +
            "FROM Sweet S WITH (NOLOCK)\n" +
            "    INNER JOIN SweetClientRealtion A WITH (NOLOCK)\n" +
            "        ON S.SweetId = A.Sid\n" +
            "    INNER JOIN [SweetCity] SC WITH (NOLOCK)\n" +
            "        ON SC.[Sweetid] = S.SweetId\n" +
            "WHERE S.ClientType = #{clientType}\n" +
            "      AND S.Recycled = 0\n" +
            "      AND GETDATE()\n" +
            "      BETWEEN S.StartTime AND S.EndTime\n" +
            "      AND S.Status = #{status}\n" +
            "      AND A.Cid = #{cid}\n" +
            "      AND A.is_del = 0\n" +
            "      AND\n" +
            "      (\n" +
            "          SC.Country = 1\n" +
            "          OR\n" +
            "          (\n" +
            "              SC.ProvinceId = #{province}\n" +
            "              AND SC.Cityid = 0\n" +
            "          )\n" +
            "          OR\n" +
            "          (\n" +
            "              SC.Cityid = #{cityid}\n" +
            "              AND #{cityid} > 0\n" +
            "          )\n" +
            "      )\n" +
            "      AND SC.[is_del] = 0\n" +
            "ORDER BY S.SortNum,\n" +
            "         S.EndTime DESC")
    List<SweetClient> GetActionSweetClient(@Param("cid") String cid, @Param("clientType") int clientType, @Param("status") int status, @Param("cityid") int cityid, @Param("province") int province);

    @Select("SELECT DISTINCT\n" +
            "       S.SweetId,\n" +
            "       S.SweetName,\n" +
            "       S.ClientType,\n" +
            "       S.JumpTo,\n" +
            "       S.Img,\n" +
            "       S.StartTime,\n" +
            "       S.EndTime,\n" +
            "       S.JumpType,\n" +
            "       S.OtherAppid,\n" +
            "       S.ReportPoint,\n" +
            "       S.SortNum,\n" +
            "       S.ShareDesc,\n" +
            "       S.ShareImg,\n" +
            "       S.ShowPoint,\n" +
            "       S.NeedLogin\n" +
            "FROM Sweet S WITH (NOLOCK)\n" +
            "    INNER JOIN SweetClientRealtion A WITH (NOLOCK)\n" +
            "        ON S.SweetId = A.Sid\n" +
            "    INNER JOIN SweetSecdRelation R WITH (NOLOCK)\n" +
            "        ON R.Sid = A.Sid\n" +
            "    INNER JOIN [SweetCity] SC WITH (NOLOCK)\n" +
            "        ON SC.[Sweetid] = S.SweetId\n" +
            "WHERE S.ClientType = #{clientType}\n" +
            "      AND S.Recycled = 0\n" +
            "      AND GETDATE()\n" +
            "      BETWEEN S.StartTime AND S.EndTime\n" +
            "      AND S.Status = #{status}\n" +
            "      AND A.Cid = #{cid}\n" +
            "      AND R.is_del = 0\n" +
            "      AND A.is_del = 0\n" +
            "      AND R.Rid = #{rid}\n" +
            "      AND\n" +
            "      (\n" +
            "          SC.Country = 1\n" +
            "          OR\n" +
            "          (\n" +
            "              SC.ProvinceId = #{province}\n" +
            "              AND SC.Cityid = 0\n" +
            "          )\n" +
            "          OR\n" +
            "          (\n" +
            "              SC.Cityid = #{cityid}\n" +
            "              AND #{cityid} > 0\n" +
            "          )\n" +
            "      )\n" +
            "      AND SC.[is_del] = 0\n" +
            "ORDER BY S.SortNum,\n" +
            "         EndTime DESC")
    List<SweetClient> GetActionSweetClientWithrid(@Param("cid") String cid, @Param("clientType") int clientType, @Param("status") int status, @Param("rid") String rid, @Param("cityid") int cityid, @Param("province") int province);

    @Select("select distinct S.SweetId,S.SweetName,S.ClientType,S.JumpTo,S.Img,S.StartTime,S.EndTime,S.JumpType,OtherAppid,ReportPoint,\n" +
            " ShareDesc,ShareImg,SortNum,ShowPoint,NeedLogin,ForceSort   \n" +
            "from Sweet S WITH(NOLOCK) \n" +
            "inner join SweetClientRealtion A WITH(NOLOCK) on S.SweetId=A.Sid \n" +
            "inner join [SweetCity] SC WITH(NOLOCK) on SC.[SweetId] = S.SweetId\n" +
            "where  S.ClientType=#{clientType} and S.Recycled=0 and   GETDATE() BETWEEN S.StartTime and S.EndTime and S.Status=#{status} and A.cid=#{cid} and A.is_del=0 \n" +
            "AND SC.[is_del] = 0\n" +
            "AND (SC.Country = 1 OR (SC.ProvinceId = #{province} AND SC.Cityid=0)  OR (SC.Cityid = #{cityid} AND #{cityid}>0))\n" +
            "order by S.SortNum")
    List<SweetClientCombine> GetActionSweetClientCombine(@Param("cid") String cid, @Param("clientType") int clientType, @Param("status") int status, @Param("cityid") int cityid, @Param("province") int province);

}
