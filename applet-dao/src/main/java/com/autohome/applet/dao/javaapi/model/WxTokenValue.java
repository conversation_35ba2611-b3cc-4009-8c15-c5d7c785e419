package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class WxTokenValue {
    private Integer id;

    private String tokenvalue;

    private Date createdStime;

    private Date modifiedStime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTokenvalue() {
        return tokenvalue;
    }

    public void setTokenvalue(String tokenvalue) {
        this.tokenvalue = tokenvalue == null ? null : tokenvalue.trim();
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }
}