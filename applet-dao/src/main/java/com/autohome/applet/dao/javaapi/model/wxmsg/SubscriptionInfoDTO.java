package com.autohome.applet.dao.javaapi.model.wxmsg;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;

import java.util.Date;

@Data
public class SubscriptionInfoDTO  {

    private Integer userId;
    private Integer id;

    private String xcxOpenId;
    private String deviceId;

    private String unionId;

    private Integer bizType;

    private Integer pm;

    private Date createdStime;
    @JsonView()
    private Date modifiedStime;

    private Integer isDel;

    private Integer seriesId;

    private String fwhOpenId;
    @JsonView()
    private Integer status;




}