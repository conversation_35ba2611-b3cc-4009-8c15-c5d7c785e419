package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.NewCarEvent;
import com.autohome.applet.dao.javaapi.model.query.NewCarEventQuery;
import com.autonews.database.db.DS;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface NewCarEventMapper {
    int deleteBatch(NewCarEventQuery query);
    int insertBatch(List<NewCarEvent> list);
    int updateBatch(List<NewCarEvent> list);
    NewCarEvent getByDbId(Long dbid);
    List<NewCarEvent> getByQuery(NewCarEventQuery query);

}