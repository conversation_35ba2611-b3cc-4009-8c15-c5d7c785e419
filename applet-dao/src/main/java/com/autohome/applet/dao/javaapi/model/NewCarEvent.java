package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class NewCarEvent {
    private Integer id;

    private Long dbid;

    private Integer objid;

    private Integer objtype;

    private String title;

    private String imgurl;

    private Date publishtime;

    private Date updatetime;

    private Long authorid;

    private String authorname;

    private String headimg;

    private String indexdetail;

    private Long replycount;

    private Long playcount;

    private Long likecount;

    private Long viewcount;

    private String jumpurl;

    private String imglist;

    private Byte closecomment;

    private String videosource;

    private Integer duration;

    private String seriesids;


    private String seriesnames;

    private Byte direction;

    private Byte cmsrefine;

    private String extdata;

    private Byte isDel;

    private Date createdStime;

    private Date modifiedStime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDbid() {
        return dbid;
    }

    public void setDbid(Long dbid) {
        this.dbid = dbid;
    }

    public Integer getObjid() {
        return objid;
    }

    public void setObjid(Integer objid) {
        this.objid = objid;
    }

    public Integer getObjtype() {
        return objtype;
    }

    public void setObjtype(Integer objtype) {
        this.objtype = objtype;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getImgurl() {
        return imgurl;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl == null ? null : imgurl.trim();
    }

    public Date getPublishtime() {
        return publishtime;
    }

    public void setPublishtime(Date publishtime) {
        this.publishtime = publishtime;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Long getAuthorid() {
        return authorid;
    }

    public void setAuthorid(Long authorid) {
        this.authorid = authorid;
    }

    public String getAuthorname() {
        return authorname;
    }

    public void setAuthorname(String authorname) {
        this.authorname = authorname == null ? null : authorname.trim();
    }

    public String getHeadimg() {
        return headimg;
    }

    public void setHeadimg(String headimg) {
        this.headimg = headimg == null ? null : headimg.trim();
    }

    public String getIndexdetail() {
        return indexdetail;
    }

    public void setIndexdetail(String indexdetail) {
        this.indexdetail = indexdetail == null ? null : indexdetail.trim();
    }

    public Long getReplycount() {
        return replycount;
    }

    public void setReplycount(Long replycount) {
        this.replycount = replycount;
    }

    public Long getPlaycount() {
        return playcount;
    }

    public void setPlaycount(Long playcount) {
        this.playcount = playcount;
    }

    public Long getLikecount() {
        return likecount;
    }

    public void setLikecount(Long likecount) {
        this.likecount = likecount;
    }

    public Long getViewcount() {
        return viewcount;
    }

    public void setViewcount(Long viewcount) {
        this.viewcount = viewcount;
    }

    public String getJumpurl() {
        return jumpurl;
    }

    public void setJumpurl(String jumpurl) {
        this.jumpurl = jumpurl == null ? null : jumpurl.trim();
    }

    public String getImglist() {
        return imglist;
    }

    public void setImglist(String imglist) {
        this.imglist = imglist == null ? null : imglist.trim();
    }

    public Byte getClosecomment() {
        return closecomment;
    }

    public void setClosecomment(Byte closecomment) {
        this.closecomment = closecomment;
    }

    public String getVideosource() {
        return videosource;
    }

    public void setVideosource(String videosource) {
        this.videosource = videosource == null ? null : videosource.trim();
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getSeriesids() {
        return seriesids;
    }

    public void setSeriesids(String seriesids) {
        this.seriesids = seriesids == null ? null : seriesids.trim();
    }

    public Byte getDirection() {
        return direction;
    }

    public void setDirection(Byte direction) {
        this.direction = direction;
    }

    public Byte getCmsrefine() {
        return cmsrefine;
    }

    public void setCmsrefine(Byte cmsrefine) {
        this.cmsrefine = cmsrefine;
    }

    public String getExtdata() {
        return extdata;
    }

    public void setExtdata(String extdata) {
        this.extdata = extdata == null ? null : extdata.trim();
    }

    public Byte getIsDel() {
        return isDel;
    }

    public void setIsDel(Byte isDel) {
        this.isDel = isDel;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public String getSeriesnames() {
        return seriesnames;
    }

    public void setSeriesnames(String seriesnames) {
        this.seriesnames = seriesnames;
    }
}