package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface BrandSeriesUploadQqMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BrandSeriesUploadQq row);

    BrandSeriesUploadQq selectByPrimaryKey(Long id);

    List<BrandSeriesUploadQq> selectAll();

    int updateByPrimaryKey(BrandSeriesUploadQq row);

    int updateByPrimary(BrandSeriesUploadQq row);

    int save(BrandSeriesUploadQq row);
}