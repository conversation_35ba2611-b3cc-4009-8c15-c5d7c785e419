package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.UserSettings;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen-read")
@Repository
public interface UserSettingsMapper {
    int deleteByPrimaryKey(Integer id);

    @DS("db-autoopen")
    int insert(UserSettings row);

    @DS("db-autoopen")
    int insertNew(UserSettings row);

    UserSettings selectByPrimaryKey(Integer id);

    List<UserSettings> selectAll();

    @DS("db-autoopen")
    int updateByPrimaryKey(UserSettings row);

    Integer getPersonalizedRec(@Param("openId") String openId);

    UserSettings getUserSettings(@Param("openId") String openId);
}