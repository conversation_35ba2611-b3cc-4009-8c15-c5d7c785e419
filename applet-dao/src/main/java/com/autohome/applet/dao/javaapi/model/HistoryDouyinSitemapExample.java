package com.autohome.applet.dao.javaapi.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HistoryDouyinSitemapExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public HistoryDouyinSitemapExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPagepathIsNull() {
            addCriterion("pagepath is null");
            return (Criteria) this;
        }

        public Criteria andPagepathIsNotNull() {
            addCriterion("pagepath is not null");
            return (Criteria) this;
        }

        public Criteria andPagepathEqualTo(String value) {
            addCriterion("pagepath =", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathNotEqualTo(String value) {
            addCriterion("pagepath <>", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathGreaterThan(String value) {
            addCriterion("pagepath >", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathGreaterThanOrEqualTo(String value) {
            addCriterion("pagepath >=", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathLessThan(String value) {
            addCriterion("pagepath <", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathLessThanOrEqualTo(String value) {
            addCriterion("pagepath <=", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathLike(String value) {
            addCriterion("pagepath like", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathNotLike(String value) {
            addCriterion("pagepath not like", value, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathIn(List<String> values) {
            addCriterion("pagepath in", values, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathNotIn(List<String> values) {
            addCriterion("pagepath not in", values, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathBetween(String value1, String value2) {
            addCriterion("pagepath between", value1, value2, "pagepath");
            return (Criteria) this;
        }

        public Criteria andPagepathNotBetween(String value1, String value2) {
            addCriterion("pagepath not between", value1, value2, "pagepath");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeIsNull() {
            addCriterion("created_stime is null");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeIsNotNull() {
            addCriterion("created_stime is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeEqualTo(Date value) {
            addCriterion("created_stime =", value, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeNotEqualTo(Date value) {
            addCriterion("created_stime <>", value, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeGreaterThan(Date value) {
            addCriterion("created_stime >", value, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeGreaterThanOrEqualTo(Date value) {
            addCriterion("created_stime >=", value, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeLessThan(Date value) {
            addCriterion("created_stime <", value, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeLessThanOrEqualTo(Date value) {
            addCriterion("created_stime <=", value, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeIn(List<Date> values) {
            addCriterion("created_stime in", values, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeNotIn(List<Date> values) {
            addCriterion("created_stime not in", values, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeBetween(Date value1, Date value2) {
            addCriterion("created_stime between", value1, value2, "createdStime");
            return (Criteria) this;
        }

        public Criteria andCreatedStimeNotBetween(Date value1, Date value2) {
            addCriterion("created_stime not between", value1, value2, "createdStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeIsNull() {
            addCriterion("modified_stime is null");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeIsNotNull() {
            addCriterion("modified_stime is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeEqualTo(Date value) {
            addCriterion("modified_stime =", value, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeNotEqualTo(Date value) {
            addCriterion("modified_stime <>", value, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeGreaterThan(Date value) {
            addCriterion("modified_stime >", value, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_stime >=", value, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeLessThan(Date value) {
            addCriterion("modified_stime <", value, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeLessThanOrEqualTo(Date value) {
            addCriterion("modified_stime <=", value, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeIn(List<Date> values) {
            addCriterion("modified_stime in", values, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeNotIn(List<Date> values) {
            addCriterion("modified_stime not in", values, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeBetween(Date value1, Date value2) {
            addCriterion("modified_stime between", value1, value2, "modifiedStime");
            return (Criteria) this;
        }

        public Criteria andModifiedStimeNotBetween(Date value1, Date value2) {
            addCriterion("modified_stime not between", value1, value2, "modifiedStime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}