package com.autohome.applet.dao.javaapi.model;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
@Data
@Builder

public class WxServiceAccount {
    private Long id;

    private String fwhOpenId;

    private String unionId;

    private Date createdStime;

    private Date modifiedStime;

    private Integer isDel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFwhOpenId() {
        return fwhOpenId;
    }

    public void setFwhOpenId(String fwhOpenId) {
        this.fwhOpenId = fwhOpenId == null ? null : fwhOpenId.trim();
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId == null ? null : unionId.trim();
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }
}