package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.EnterpriseGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface EnterpriseGroupMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(EnterpriseGroup row);

    EnterpriseGroup selectByPrimaryKey(Integer id);

    List<EnterpriseGroup> selectAll();

    int updateByPrimaryKey(EnterpriseGroup row);

    List<EnterpriseGroup> selectBySeriesId(@Param("seriesId") int seriesId);
}