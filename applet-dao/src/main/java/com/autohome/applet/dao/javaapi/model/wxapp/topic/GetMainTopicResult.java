package com.autohome.applet.dao.javaapi.model.wxapp.topic;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
 * Created by pengyi on 2017/1/12.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetMainTopicResult {
    private int t_memberid;
    private String t_membername;
    private String t_memberpic;
    private String title;
    private Object t_content;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tdate;
    private String bbs;
    private int bbsid;
    private int ispoll;
    private int tispoll;
    private int tdelete;
    private int iscard;
    private int replycounts;
    private String orgincontent;
    private String tsource; // 站子发贴类型
    // 这是新编辑器的枚举 PC.CARD.CREATOR：3GC图文发布器 PC.CARD.CREATOR.VIDEO:3GC发布器，纯视频入口
    private String subtitle;

    private int views;

    public String getOrgincontent() {
        return orgincontent;
    }

    public void setOrgincontent(String orgincontent) {
        this.orgincontent = orgincontent;
    }

    public int getTispoll() {
        return tispoll;
    }

    public void setTispoll(int tispoll) {
        this.tispoll = tispoll;
    }

    public int getIspoll() {
        return ispoll;
    }

    public void setIspoll(int ispoll) {
        this.ispoll = ispoll;
    }

    public int getT_memberid() {
        return t_memberid;
    }

    public void setT_memberid(int t_memberid) {
        this.t_memberid = t_memberid;
    }

    public String getT_membername() {
        return t_membername;
    }

    public void setT_membername(String t_membername) {
        this.t_membername = t_membername;
    }

    public String getT_memberpic() {
        return t_memberpic;
    }

    public void setT_memberpic(String t_memberpic) {
        this.t_memberpic = t_memberpic;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getT_content() {
        return t_content;
    }

    public void setT_content(Object t_content) {
        this.t_content = t_content;
    }

    public Date getTdate() {
        return tdate;
    }

    public void setTdate(Date tdate) {
        this.tdate = tdate;
    }

    public String getBbs() {
        return bbs;
    }

    public void setBbs(String bbs) {
        this.bbs = bbs;
    }

    public int getBbsid() {
        return bbsid;
    }

    public void setBbsid(int bbsid) {
        this.bbsid = bbsid;
    }

    public int getTdelete() {
        return tdelete;
    }

    public void setTdelete(int tdelete) {
        this.tdelete = tdelete;
    }

    public int getIscard() {
        return iscard;
    }

    public void setIscard(int iscard) {
        this.iscard = iscard;
    }

    public int getReplycounts() {
        return replycounts;
    }

    public void setReplycounts(int replycounts) {
        this.replycounts = replycounts;
    }
    /*    public static void main(String[] args) throws IOException {
        String json = "{\"topicmain\":{\"t_memberid\":10419552,\"t_membername\":\"stefsun2\",\"t_memberpic\":\"userheaders/g6/M0A/1F/64/120X120_0_q87_autohomecar__wKgH3FfDzAKAXmX4AABEI8pHlwQ008.jpg\",\"title\":\"有人加过98油吗，效果咋样？\",\"t_content\":[{\"cardid\":1,\"url\":\"\",\"des\":\"但是发生地方稍等粉色的是\",\"ctype\":2,\"otherattributes\":\"\"},{\"cardid\":2,\"url\":\"http://v.youku.com/v_show/id_XMTM3MDM3NDIyNA==.html?from=y1.3-idx-uhome-1519-20887.205805-205902.4-2\",\"des\":\"\",\"ctype\":4,\"otherattributes\":{\"_vid\":\"XMTM3MDM3NDIyNA==\",\"_type\":\"youku\"}}],\"tdate\":\"2017-01-07 08:46:35\",\"views\":300,\"t_seriesname\":\"思铭\",\"t_iscarowner\":1,\"topictype\":\"\",\"t_approvebrand\":0,\"tclose\":0,\"bbs\":\"c\",\"bbsid\":2751,\"bbsname\":\"思铭论坛\",\"seriesid\":2751,\"tdelete\":0,\"tispoll\":0,\"t_lastpostdate\":\"2017-01-11 00:40:24\",\"t_lasteditdate\":\"2017-01-07 08:46:35\",\"tsource\":\"PC.CARD\",\"issolve\":0,\"solveid\":0,\"iscard\":1,\"lon\":\"\",\"lat\":\"\",\"postaddrss\":\"\",\"landmark\":\"\"}}";
        //json="{\"topicmain\":{\"t_memberid\":10419552,\"t_membername\":\"stefsun2\",\"t_memberpic\":\"userheaders/g6/M0A/1F/64/120X120_0_q87_autohomecar__wKgH3FfDzAKAXmX4AABEI8pHlwQ008.jpg\",\"title\":\"有人加过98油吗，效果咋样？\",\"t_content\":\"<div layer1=\\\"text-s\\\"></div><div class=\\\"tz-paragraph\\\"><img src=\\\"http://x.autoimg.cn/club/smiles/44.gif\\\" class=\\\"spic\\\" />是不是动力猛增，一脚下去窜的不要不要的</div><div layer1=\\\"text-e\\\"></div><br />\",\"tdate\":\"2017-01-07 08:46:35\",\"views\":300,\"t_seriesname\":\"思铭\",\"t_iscarowner\":1,\"topictype\":\"\",\"t_approvebrand\":0,\"tclose\":0,\"bbs\":\"c\",\"bbsid\":2751,\"bbsname\":\"思铭论坛\",\"seriesid\":2751,\"tdelete\":0,\"tispoll\":0,\"t_lastpostdate\":\"2017-01-11 00:40:24\",\"t_lasteditdate\":\"2017-01-07 08:46:35\",\"tsource\":\"PC.CARD\",\"issolve\":0,\"solveid\":0,\"iscard\":1,\"lon\":\"\",\"lat\":\"\",\"postaddress\":\"\",\"landmark\":\"\"}}";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES);
        objectMapper.enable(MapperFeature.DEFAULT_VIEW_INCLUSION);
        objectMapper.disable(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        GetMainTopicResult maintopicResult = objectMapper.readValue(json, GetMainTopicResult.class);
        DetailModel detailModel = new DetailModel();
        GetMainTopicResult.MainTopic mainTopic = maintopicResult.getTopicmain();
        if (mainTopic.getTdelete() > 0){
            detailModel.setTitle("\"帖子被删除(或屏蔽)\"");
            List<ContentCard> contentCards = new ArrayList<>(1);
            contentCards.add(ContentCard.getHasDeletedMainContentCard());
            detailModel.setContentCards(contentCards);
        }else{
            detailModel.setBbs(mainTopic.getBbs());
            detailModel.setBbsid(mainTopic.getBbsid());
            detailModel.setMemberId(mainTopic.getT_memberid());
            detailModel.setReplyCount(mainTopic.getReplycount());
            detailModel.setTitle(mainTopic.getTitle());
            detailModel.setPublishTime(mainTopic.getTdate());
            detailModel.setName(mainTopic.getT_membername());
            //解析帖子
            if (mainTopic.getIscard() == 1){
                List<ContentCard> contentCards = new ArrayList<>();
                ArrayList<LinkedHashMap<String,Object>> cardList = (ArrayList)mainTopic.getT_content();
                for (LinkedHashMap<String,Object> card : cardList){
                    ContentCard c = new ContentCard();
                    c.setCtype((Integer) card.get("ctype"));
                    c.setDes((String) card.get("des"));
                    c.setOtherattributes(card.get("otherattributes"));
                    c.setUrl((String)card.getOrDefault("url",""));
                    contentCards.add(c);
                }
                detailModel.setContentCards(contentCards);
            }
        }
        System.out.println(maintopicResult);
    }*/

    public String getPubprovincename() {
        return pubprovincename;
    }

    public void setPubprovincename(String pubprovincename) {
        this.pubprovincename = pubprovincename;
    }

    private String pubprovincename;

    public String getTsource() {
        return tsource;
    }

    public void setTsource(String tsource) {
        this.tsource = tsource;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public int getViews() {
        return views;
    }

    public void setViews(int views) {
        this.views = views;
    }
}
