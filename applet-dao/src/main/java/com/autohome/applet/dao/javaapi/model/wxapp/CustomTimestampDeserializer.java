package com.autohome.applet.dao.javaapi.model.wxapp;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by pengyi on 2016/12/14.
 */
//解析/Date(1481374937000)/
public class CustomTimestampDeserializer extends JsonDeserializer<Date> {
    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        JsonToken currentToken = jsonParser.getCurrentToken();
        if (currentToken == JsonToken.VALUE_STRING) {
            String d = jsonParser.getText().trim();
            if (d == null || d.isEmpty()) {
                return null;
            }
            d = d.replaceAll("/Date\\((\\d+)(.*?)\\)/", "$1");
            if (d.isEmpty()) {
                return null;
            }
            //return stampToDate(d.replaceAll("/Date\\((\\d+)(.*?)\\)/","$1"));
            return stampToDate(d);
        }
        //TODO log
        return null;
    }

    /*
     * 将时间戳转换为时间
     */
    public static String stampToDateStr(String s, String format) {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        long lt = new Long(s);
        Date date = new Date(lt);
        res = simpleDateFormat.format(date);
        return res;
    }

    public static Date stampToDate(String s) {
        long lt = new Long(s);
        return new Date(lt);
    }
}
