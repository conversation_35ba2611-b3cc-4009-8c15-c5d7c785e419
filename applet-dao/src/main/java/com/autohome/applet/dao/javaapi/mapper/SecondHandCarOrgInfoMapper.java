package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface SecondHandCarOrgInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SecondHandCarOrgInfo row);

    SecondHandCarOrgInfo selectByPrimaryKey(Long id);

    List<SecondHandCarOrgInfo> selectAll();

    int updateByPrimaryKey(SecondHandCarOrgInfo row);

    int save(SecondHandCarOrgInfo row);

    /**
     * 根据门店id和最新的上传版本号, 更新待上传版本
     * */
    int updateVersionByOrgId(@Param("orgId") String orgId, @Param("version") Long version);

    List<SecondHandCarOrgInfo> listByVersion(Long version);

    List<SecondHandCarOrgInfo> listByOrgIds(@Param("orgIds") List<String> orgIds);

    /**
     * 更新门店状态
     * */
    int updateStatusByOrgId(@Param("orgId") String orgId, @Param("version") Long version, @Param("orgStatus") Integer orgStatus);
}