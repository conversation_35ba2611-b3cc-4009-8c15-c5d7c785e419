package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class OpenUserAlipay {
    private Integer id;

    private Date modifiedStime;

    private Date createdStime;

    private Integer openuserid;

    private Date alipayauthtime;

    private String alipayappid;

    private String alipayauthtoken;

    private String alipayrefreshtoken;

    private Byte isDel;

    private String alipayuserid;

    private Integer alipayauthstatus;

    private Date cancelauthtime;

    private Date publishtime;

    private String firstlicensepic;

    private String secondlicensepic;

    private byte[] rv;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Integer getOpenuserid() {
        return openuserid;
    }

    public void setOpenuserid(Integer openuserid) {
        this.openuserid = openuserid;
    }

    public Date getAlipayauthtime() {
        return alipayauthtime;
    }

    public void setAlipayauthtime(Date alipayauthtime) {
        this.alipayauthtime = alipayauthtime;
    }

    public String getAlipayappid() {
        return alipayappid;
    }

    public void setAlipayappid(String alipayappid) {
        this.alipayappid = alipayappid == null ? null : alipayappid.trim();
    }

    public String getAlipayauthtoken() {
        return alipayauthtoken;
    }

    public void setAlipayauthtoken(String alipayauthtoken) {
        this.alipayauthtoken = alipayauthtoken == null ? null : alipayauthtoken.trim();
    }

    public String getAlipayrefreshtoken() {
        return alipayrefreshtoken;
    }

    public void setAlipayrefreshtoken(String alipayrefreshtoken) {
        this.alipayrefreshtoken = alipayrefreshtoken == null ? null : alipayrefreshtoken.trim();
    }

    public Byte getIsDel() {
        return isDel;
    }

    public void setIsDel(Byte isDel) {
        this.isDel = isDel;
    }

    public String getAlipayuserid() {
        return alipayuserid;
    }

    public void setAlipayuserid(String alipayuserid) {
        this.alipayuserid = alipayuserid == null ? null : alipayuserid.trim();
    }

    public Integer getAlipayauthstatus() {
        return alipayauthstatus;
    }

    public void setAlipayauthstatus(Integer alipayauthstatus) {
        this.alipayauthstatus = alipayauthstatus;
    }

    public Date getCancelauthtime() {
        return cancelauthtime;
    }

    public void setCancelauthtime(Date cancelauthtime) {
        this.cancelauthtime = cancelauthtime;
    }

    public Date getPublishtime() {
        return publishtime;
    }

    public void setPublishtime(Date publishtime) {
        this.publishtime = publishtime;
    }

    public String getFirstlicensepic() {
        return firstlicensepic;
    }

    public void setFirstlicensepic(String firstlicensepic) {
        this.firstlicensepic = firstlicensepic == null ? null : firstlicensepic.trim();
    }

    public String getSecondlicensepic() {
        return secondlicensepic;
    }

    public void setSecondlicensepic(String secondlicensepic) {
        this.secondlicensepic = secondlicensepic == null ? null : secondlicensepic.trim();
    }

    public byte[] getRv() {
        return rv;
    }

    public void setRv(byte[] rv) {
        this.rv = rv;
    }
}