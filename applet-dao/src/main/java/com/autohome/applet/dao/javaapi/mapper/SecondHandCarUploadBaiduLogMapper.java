package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SecondHandCarUploadBaiduLog;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface SecondHandCarUploadBaiduLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SecondHandCarUploadBaiduLog row);

    SecondHandCarUploadBaiduLog selectByPrimaryKey(Long id);

    List<SecondHandCarUploadBaiduLog> selectAll();

    int updateByPrimaryKey(SecondHandCarUploadBaiduLog row);
}