package com.autohome.applet.dao.javaapi.model;

import java.util.Date;

public class Sweet {
    private Integer sweetid;

    private String sweetname;

    private Integer clienttype;

    private String jumpto;

    private String img;

    private Date starttime;

    private Date endtime;

    private Integer sortnum;

    private Integer status;

    private Boolean recycled;

    private Date modifiedStime;

    private Date createdStime;

    private Integer jumptype;

    private String otherappid;

    private String reportpoint;

    private String sharedesc;

    private String shareimg;

    private Integer contenttype;

    private String showpoint;

    private String ext;

    private Integer needlogin;

    private Integer categorypart;

    private Integer forcesort;

    public Integer getSweetid() {
        return sweetid;
    }

    public void setSweetid(Integer sweetid) {
        this.sweetid = sweetid;
    }

    public String getSweetname() {
        return sweetname;
    }

    public void setSweetname(String sweetname) {
        this.sweetname = sweetname == null ? null : sweetname.trim();
    }

    public Integer getClienttype() {
        return clienttype;
    }

    public void setClienttype(Integer clienttype) {
        this.clienttype = clienttype;
    }

    public String getJumpto() {
        return jumpto;
    }

    public void setJumpto(String jumpto) {
        this.jumpto = jumpto == null ? null : jumpto.trim();
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img == null ? null : img.trim();
    }

    public Date getStarttime() {
        return starttime;
    }

    public void setStarttime(Date starttime) {
        this.starttime = starttime;
    }

    public Date getEndtime() {
        return endtime;
    }

    public void setEndtime(Date endtime) {
        this.endtime = endtime;
    }

    public Integer getSortnum() {
        return sortnum;
    }

    public void setSortnum(Integer sortnum) {
        this.sortnum = sortnum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getRecycled() {
        return recycled;
    }

    public void setRecycled(Boolean recycled) {
        this.recycled = recycled;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Integer getJumptype() {
        return jumptype;
    }

    public void setJumptype(Integer jumptype) {
        this.jumptype = jumptype;
    }

    public String getOtherappid() {
        return otherappid;
    }

    public void setOtherappid(String otherappid) {
        this.otherappid = otherappid == null ? null : otherappid.trim();
    }

    public String getReportpoint() {
        return reportpoint;
    }

    public void setReportpoint(String reportpoint) {
        this.reportpoint = reportpoint == null ? null : reportpoint.trim();
    }

    public String getSharedesc() {
        return sharedesc;
    }

    public void setSharedesc(String sharedesc) {
        this.sharedesc = sharedesc == null ? null : sharedesc.trim();
    }

    public String getShareimg() {
        return shareimg;
    }

    public void setShareimg(String shareimg) {
        this.shareimg = shareimg == null ? null : shareimg.trim();
    }

    public Integer getContenttype() {
        return contenttype;
    }

    public void setContenttype(Integer contenttype) {
        this.contenttype = contenttype;
    }

    public String getShowpoint() {
        return showpoint;
    }

    public void setShowpoint(String showpoint) {
        this.showpoint = showpoint == null ? null : showpoint.trim();
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext == null ? null : ext.trim();
    }

    public Integer getNeedlogin() {
        return needlogin;
    }

    public void setNeedlogin(Integer needlogin) {
        this.needlogin = needlogin;
    }

    public Integer getCategorypart() {
        return categorypart;
    }

    public void setCategorypart(Integer categorypart) {
        this.categorypart = categorypart;
    }

    public Integer getForcesort() {
        return forcesort;
    }

    public void setForcesort(Integer forcesort) {
        this.forcesort = forcesort;
    }
}