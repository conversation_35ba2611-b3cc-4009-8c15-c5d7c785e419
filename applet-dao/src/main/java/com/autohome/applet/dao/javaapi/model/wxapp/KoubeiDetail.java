package com.autohome.applet.dao.javaapi.model.wxapp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
 * 口碑详情(不包含title和用户感受)
 * Created by pen<PERSON><PERSON> on 2016/12/29.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class KoubeiDetail {
    private int memberid;
    private String membername;
    private String membericon;
    private int brandid;
    private String brandname;
    private int seriesid;
    private String seriesname;
    private int specid;
    private String specname;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportdate;
    private int commentcount;
    private int helpfulcount;
    private String content;
    private CarInfo carinfo;
    private PicInfo[] piclist;


    public int getHelpfulcount() {
        return helpfulcount;
    }

    public void setHelpfulcount(int helpfulcount) {
        this.helpfulcount = helpfulcount;
    }

    public int getMemberid() {
        return memberid;
    }

    public void setMemberid(int memberid) {
        this.memberid = memberid;
    }

    public String getMembername() {
        return membername;
    }

    public void setMembername(String membername) {
        this.membername = membername;
    }

    public String getMembericon() {
        return membericon;
    }

    public void setMembericon(String membericon) {
        if (membericon == null || membericon.isEmpty()) {
            this.membericon = "http://x.autoimg.cn/club/wxapp/male_default.png";
        }
        else {this.membericon = membericon;}
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public Date getReportdate() {
        return reportdate;
    }

    public void setReportdate(Date reportdate) {
        this.reportdate = reportdate;
    }

    public int getCommentcount() {
        return commentcount;
    }

    public void setCommentcount(int commentcount) {
        this.commentcount = commentcount;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public CarInfo getCarinfo() {
        return carinfo;
    }

    public void setCarinfo(CarInfo carinfo) {
        this.carinfo = carinfo;
    }

    public PicInfo[] getPiclist() {
        return piclist;
    }

    public void setPiclist(PicInfo[] piclist) {
        this.piclist = piclist;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CarInfo {
        /*        boughtprice	float	购买价格
        boughtdate	string	购车时间
        boughtaddress	string	购买地点
        actualoilcomsumption	string	油耗
        isbattery	int	0：油耗车，1：电动车
        drivenkiloms	string	行驶里程
        space	string	空间
        power	string	动力
        maneuverability	string	操控
        comfortabelness	string	舒适性
        apperance	string	外观
        internals	string	内饰
        costefficient	string	性价比
        consumptionscore	string	油耗打分*/
        private float boughtprice;//购买价格
        private String boughtdate;//购车时间
        private String boughtaddress;//购买地点
        private String consumption;
        private String consumptionscore;//油耗打分
        private int isbattery;//0：油耗车，1：电动车
        private String drivenkiloms;//行驶里程
        private String space;//空间
        private String power;//动力
        private String maneuverability;//操控
        private String actualoilcomsumption;//油耗
        private String comfortabelness;//舒适性
        private String apperance;//外观
        private String internals;//内饰
        private String costefficient;//性价比

        public float getBoughtprice() {
            return boughtprice;
        }

        public void setBoughtprice(float boughtprice) {
            this.boughtprice = boughtprice;
        }

        public String getBoughtdate() {
            return boughtdate;
        }

        public void setBoughtdate(String boughtdate) {
            this.boughtdate = boughtdate;
        }

        public String getBoughtaddress() {
            return boughtaddress;
        }

        public void setBoughtaddress(String boughtaddress) {
            this.boughtaddress = boughtaddress;
        }

        public String getConsumption() {
            return consumption;
        }

        public void setConsumption(String consumption) {
            this.consumption = consumption;
        }

        public String getConsumptionscore() {
            return consumptionscore;
        }

        public void setConsumptionscore(String consumptionscore) {
            this.consumptionscore = consumptionscore;
        }

        public int getIsbattery() {
            return isbattery;
        }

        public void setIsbattery(int isbattery) {
            this.isbattery = isbattery;
        }

        public String getDrivenkiloms() {
            return drivenkiloms;
        }

        public void setDrivenkiloms(String drivenkiloms) {
            this.drivenkiloms = drivenkiloms;
        }

        public String getSpace() {
            return space;
        }

        public void setSpace(String space) {
            this.space = space;
        }

        public String getPower() {
            return power;
        }

        public void setPower(String power) {
            this.power = power;
        }

        public String getManeuverability() {
            return maneuverability;
        }

        public void setManeuverability(String maneuverability) {
            this.maneuverability = maneuverability;
        }

        public String getActualoilcomsumption() {
            return actualoilcomsumption;
        }

        public void setActualoilcomsumption(String actualoilcomsumption) {
            this.actualoilcomsumption = actualoilcomsumption;
        }

        public String getComfortabelness() {
            return comfortabelness;
        }

        public void setComfortabelness(String comfortabelness) {
            this.comfortabelness = comfortabelness;
        }

        public String getApperance() {
            return apperance;
        }

        public void setApperance(String apperance) {
            this.apperance = apperance;
        }

        public String getInternals() {
            return internals;
        }

        public void setInternals(String internals) {
            this.internals = internals;
        }

        public String getCostefficient() {
            return costefficient;
        }

        public void setCostefficient(String costefficient) {
            this.costefficient = costefficient;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PicInfo {
        private String bigurl;

        public String getBigurl() {
            return bigurl;
        }

        public void setBigurl(String bigurl) {
            this.bigurl = bigurl;
        }
    }
}
