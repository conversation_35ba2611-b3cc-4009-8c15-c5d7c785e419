package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.WxMiniAccount;
import com.autohome.applet.dao.javaapi.model.wxmsg.SubscriptionInfoDTO;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Mapper
@DS("db-weixin")
@Repository

public interface WxMiniAccountMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxMiniAccount row);

    WxMiniAccount selectByPrimaryKey(Long id);

    List<WxMiniAccount> selectAll();

    List<SubscriptionInfoDTO> getSubInfo(@Param("deviceId")String deviceId,@Param("bizType")int bizType);
    WxMiniAccount getWxMiniAccountByDeviceId(@Param("deviceId")String deviceId,@Param("bizType")int bizType );
    WxMiniAccount getWxMiniAccountByUnionId(@Param("unionId")String unionId,@Param("bizType")int bizType);
    int updateByPrimaryKey(WxMiniAccount row);
    int updateById(@Param("id") Long id);
}