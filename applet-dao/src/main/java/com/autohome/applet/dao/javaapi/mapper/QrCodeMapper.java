package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.qrcode.QrCodeModel;
import com.autonews.database.db.DS;
import com.autonews.springboot.mybatis.LanguageDriver.SimpleSelectInExtendedLanguageDriver;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@DS("db-weixin-read")
@Mapper
@Repository
public interface QrCodeMapper {
    String fields = "" +
            "T.QRCodeId     \n" +
            ",T.PageUrl    \n" +
            ",T.<PERSON>ce    \n" +
            ",T.ImgUrl    \n" +
            ",T.Status    \n" +
            ",T.Created_STime    \n" +
            "";

    /**
     * 分页查询未删除的QrCode
     * @param lastId
     * @param shardingIndex 不知道传啥的话就传0，这个主要是给xxl job分片广播使用
     * @param shardingTotal 不知道传啥的话就传1，这个主要是给xxl job分片广播使用
     * @return
     */
    @Select("SELECT TOP (#{pageSize}) " + fields + " FROM QrCode T WITH(NOLOCK) " +
            " WHERE QRCodeId > #{lastId} and QRCodeId < #{maxId} " +
            " and QRCodeId % #{shardingTotal} = #{shardingIndex} and Status in (0,1) ORDER BY QRCodeId ASC")
    List<QrCodeModel> getUnDelByPage(@Param("lastId") long lastId,
                                     @Param("maxId") long maxId,
                                @Param("pageSize")   int pageSize,
                              @Param("shardingIndex") int shardingIndex,
                              @Param("shardingTotal") int shardingTotal);

    /**
     * 分页查询已删除的QrCode
     * @param lastId
     * @param shardingIndex 不知道传啥的话就传0，这个主要是给xxl job分片广播使用
     * @param shardingTotal 不知道传啥的话就传1，这个主要是给xxl job分片广播使用
     * @return
     */
    @Select("SELECT TOP (#{pageSize}) " + fields + " FROM QrCode T WITH(NOLOCK) " +
            " WHERE QRCodeId > #{lastId} and QRCodeId < #{maxId} " +
            " and QRCodeId % #{shardingTotal} = #{shardingIndex} and Status=1000 ORDER BY QRCodeId ASC")
    List<QrCodeModel> getDelByPage(@Param("lastId") long lastId,
                                   @Param("maxId") long maxId,
                                     @Param("pageSize")   int pageSize,
                                     @Param("shardingIndex") int shardingIndex,
                                     @Param("shardingTotal") int shardingTotal);

    /**
     更新QrCode的状态
     0：初始化  1：线上状态   1000：删除状态
     * @param qRCodeId
     * @return
     */
    @DS("db-weixin")
    @Update("update TOP (1) QrCode set Status=#{status},Modified_STime=getdate() where QRCodeId=#{qRCodeId}")
    boolean updateQrCodeStatus(@Param("qRCodeId") long qRCodeId,@Param("status") int status);

    /**
     更新QrCode的状态
     0：初始化  1：线上状态   1000：删除状态
     * @param qRCodeIds
     * @return
     */
    @DS("db-weixin")
    @Update("<script>update TOP (10000) QrCode set Status=#{status},Modified_STime=getdate() where QRCodeId in " +
            "<foreach collection='qRCodeIds' item='item' separator=',' open='(' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    int updateQrCodeStatusByIds(@Param("qRCodeIds") List<Long> qRCodeIds,@Param("status") int status);
}
