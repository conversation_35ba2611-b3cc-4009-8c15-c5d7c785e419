package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.ResourceFeed;
import com.autohome.applet.dao.javaapi.model.ResourceFeedWithBLOBs;
import com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO;
import com.autohome.applet.dao.javaapi.model.query.ResourceFeedPushDataQuery;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@DS("db-carstatisticsdata")
@Mapper
@Repository
public interface ResourceFeedMapper {
    List<ResourceFeedWithBLOBs> listResourceFeedWithBLOBs(ResourceFeedPushDataQuery query);

    List<PushResourceFeedCountDTO> countResourceFeedWithBLOBs(ResourceFeedPushDataQuery query);
}