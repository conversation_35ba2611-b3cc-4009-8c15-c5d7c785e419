package com.autohome.applet.dao.javaapi.model.wxapp.topic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by pengyi on 2016/12/15.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImageOtherAttr {
    public ImageOtherAttr() {
    }

    public ImageOtherAttr(int width, int height) {
        this.width = width;
        this.height = height;
    }

    private int width;
    private int height;

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }
}
