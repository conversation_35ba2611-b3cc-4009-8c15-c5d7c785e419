package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.LeadBuyUseCarDailyRepoterDingding;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@DS("db-liveplatform-read")
@Mapper
@Repository
public interface LeadBuyUseCarDailyRepoterDingdingMapper {

    LeadBuyUseCarDailyRepoterDingding getReport(String format);
}