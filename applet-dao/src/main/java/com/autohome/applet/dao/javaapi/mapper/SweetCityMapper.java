package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.SweetCity;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@DS("db-weixin-read")
@Mapper
@Repository
public interface SweetCityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SweetCity row);

    SweetCity selectByPrimaryKey(Long id);

    List<SweetCity> selectAll();

    int updateByPrimaryKey(SweetCity row);

    int insertCountry(int sweetId);
}