package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface BaiduNotePushLogV3Mapper {
    /**
     * 根据推送状态查询笔记推送记录
     * @param pushState
     * @return
     */
    List<BaiduNotePushLogV3> getBaiduNote(Integer pushState);

    Integer getNoPushCount(Integer pushState);

    /**
     * 批量插入笔记推送记录
     */
    int batchInsert(List<BaiduNotePushLogV3> list);


    BaiduNotePushLogV3 getByExId(Integer exId);

    int updateByExId(BaiduNotePushLogV3 baiduNotePushLogV3);

    int insert(BaiduNotePushLogV3 baiduNotePushLogV3);


}
