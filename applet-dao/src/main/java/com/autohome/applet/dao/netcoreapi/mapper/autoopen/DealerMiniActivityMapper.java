package com.autohome.applet.dao.netcoreapi.mapper.autoopen;

import com.autohome.applet.dao.netcoreapi.model.ActivityList;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS("db-autoopen-read")
public interface DealerMiniActivityMapper {

    @Select("SELECT TOP 3\n" +
            "       (\n" +
            "           SELECT COUNT(Id)FROM DealerMiniActivityVisit WHERE Aid = T.Aid\n" +
            "       ) AS VisitCount,\n" +
            "       (\n" +
            "           SELECT COUNT(Id)FROM DealerMiniActivitySubmit WHERE [ObjId] = T.Aid\n" +
            "       ) AS SubmitCount,\n" +
            "       R.[Rank],\n" +
            "       T.Aid,\n" +
            "       T.Modified_STime,\n" +
            "       T.Created_STime,\n" +
            "       T.RV,\n" +
            "       T.DealerId,\n" +
            "       T.DealerName,\n" +
            "       T.DealerIcon,\n" +
            "       T.Title,\n" +
            "       T.StartTime,\n" +
            "       T.EndTime,\n" +
            "       T.Img,\n" +
            "       T.Type,\n" +
            "       T.BrandId,\n" +
            "       T.BrandName,\n" +
            "       T.Cid,\n" +
            "       T.Cname,\n" +
            "       T.VisitCount,\n" +
            "       T.SubmitCount,\n" +
            "       T.Detail,\n" +
            "       T.is_del,\n" +
            "       T.Recom,\n" +
            "       T.Status,\n" +
            "       T.PublishTime,\n" +
            "       T.SeriesId,\n" +
            "       T.LonGc,\n" +
            "       T.LatGc,\n" +
            "       T.SeriesName\n" +
            "FROM DealerMiniActivity T WITH (NOLOCK)\n" +
            "    INNER JOIN DealerMiniActivityIntegralRank AS R\n" +
            "        ON T.DealerId = R.DealerId\n" +
            "WHERE T.Recom = 1\n" +
            "      AND T.Status = 1\n" +
            "      AND T.is_del = 0\n" +
            "      AND T.Cid = #{cid}\n" +
            "      AND T.is_del = 0\n" +
            "      AND R.RankDay = CONVERT(varchar(100), GETDATE(), 23)\n" +
            "ORDER BY R.[Rank]")
    List<ActivityList> GetActivityList(@Param("cid") int cityId);
}
