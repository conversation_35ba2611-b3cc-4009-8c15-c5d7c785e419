package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.MiniTokenValue;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
@Mapper
@DS("db-weixin")
@Repository
public interface MiniTokenValueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MiniTokenValue row);

    MiniTokenValue selectByPrimaryKey(Integer id);

    List<MiniTokenValue> selectAll();

    int updateByPrimaryKey(MiniTokenValue row);

    String getTop1(Integer minitype);
}