package com.autohome.applet.dao.caropenapi_uc_news_dealerservice.mapper.autoopen;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.FctActivity;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.OpenInfo;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.OpenModule;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.SortSeries;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created by hanshanfeng on 2019/4/25.
 */
@Mapper
@DS("db-autoopen-read")
public interface OpenModuleRepository {

    @Select("SELECT OM.[ModuleId],\n" +
            "       OM.[ModuleName],\n" +
            "       OM.[ModuleType],\n" +
            "       OM.[SortNum]\n" +
            "FROM [OpenModule] OM WITH (NOLOCK)\n" +
            "    INNER JOIN OpenUser U WITH (NOLOCK)\n" +
            "        ON OM.[OpenUserId] = U.[OpenUserId]\n" +
            "WHERE U.[UserType] = #{userType}\n" +
            "      AND U.Id = #{fctid}\n" +
            "      AND OM.[Recycled] = 0\n" +
            "      AND OM.[BdStatus] = 1\n" +
            "      AND U.Status = 1\n" +
            "      AND U.Recycled = 0\n" +
            "ORDER BY OM.[SortNum] ASC;")
    List<OpenModule> getOpenMouduleByDealerIdAndBdStatus(@Param("fctid") int fctid, @Param("userType") int userType);

    @Select("SELECT OM.[ModuleId],\n" +
            "       OM.[ModuleName],\n" +
            "       OM.[ModuleType],\n" +
            "       OM.[SortNum]\n" +
            "FROM [OpenModule] OM WITH (NOLOCK)\n" +
            "    INNER JOIN OpenUser U WITH (NOLOCK)\n" +
            "        ON OM.[OpenUserId] = U.[OpenUserId]\n" +
            "WHERE U.[UserType] = #{userType}\n" +
            "      AND U.Id = #{fctid}\n" +
            "      AND OM.[Recycled] = 0\n" +
            "      AND OM.[Status] = 1\n" +
            "      AND U.Status = 1\n" +
            "      AND U.Recycled = 0\n" +
            "ORDER BY OM.[SortNum] ASC;")
    List<OpenModule> getOpenMouduleByDealerId(@Param("fctid") int fctid, @Param("userType") int userType);

    @Select("SELECT TOP 1000 [SeriesId]\n" +
            "      ,[OpenUserId]\n" +
            "      ,[RecommendStatus]\n" +
            "      ,[RecommendTime]\n" +
            "      ,[TopStatus]\n" +
            "      ,[TopTime]\n" +
            "      ,[SortNum]\n" +
            "      ,[Status]\n" +
            "      ,[Recycled]\n" +
            "      ,[Modified_STime]\n" +
            "      ,[Created_STime]\n" +
            "      ,[RV]\n" +
            "  FROM [AutoOpen].[dbo].[OpenSeries]  WITH(NOLOCK)\n" +
            "  WHERE SeriesId in (${seriesids})")
    List<SortSeries> getSortSeriesByFctId(@Param("seriesids") String seriesids);

    @Select("SELECT  A.[ActivityId]\n" +
            "     ,[RecommendStatus]\n" +
            "     ,A.[Title]\n" +
            "     ,A.[JumpTo]\n" +
            "     ,[Img],A.[StartTime]\n" +
            "  FROM [OpenActivity] A WITH(NOLOCK)\n" +
            "     INNER JOIN [OpenUser] U WITH(NOLOCK) ON A.[OpenUserId] = U.[OpenUserId]\n" +
            " WHERE U.Id = #{fctid} --这里是厂商或经销商编号\n" +
            "     AND U.UserType = #{userType} --1厂商，2经销商\n" +
            "     AND A.[Recycled] = 0\n" +
            "     AND A.[StartTime] <= GETDATE()\n" +
            "     AND A.[EndTime] >= GETDATE()\n" +
            "     AND A.[ShowStatus] = 1\n" +
            "     AND A.[Recycled] = 0 " +
            " ORDER BY A.[RecommendStatus] DESC\n" +
            "      ,A.[RecommendTime] DESC\n" +
            "      ,A.[StartTime] DESC\n" +
            "    ,A.[Created_STime] DESC")
    List<FctActivity> getFctActivitys(@Param("fctid") int fctid, @Param("userType") int userType);

    @Select("SELECT TOP 10 S.[InfoType] ,--1车家号长文，2车家号图文，3车家号视频，11原创\n" +
            "    S.[SourceInfoId]," +
            " S.[RecommendStatus] ," +
            " S.InfoPublishTime " +
            "  FROM [OpenInfo] S WITH(NOLOCK)\n" +
            "     INNER JOIN [OpenUser] U WITH(NOLOCK) ON S.[OpenUserId] = U.[OpenUserId]\n" +
            "WHERE S.[ShowStatus] = 1\n" +
            "    AND S.Recycled = 0\n" +
            "   AND  U.UserType = #{userType}\n" +
            "     AND U.Id = #{fctid}\n" +
            "ORDER BY S.[RecommendTime] DESC,S.[InfoPublishTime] DESC")
    List<OpenInfo> getFctOpenInfo(@Param("fctid") int fctid, @Param("userType") int userType);
}
