package com.autohome.applet.dao.javaapi.model.qrcode;

import lombok.Data;

@Data
public class QrCodeModel {
    /**
     * 自增编号
     */
    private long qrCodeId;

    /**
     * 页面地址
     */
    private String pageUrl;

    /**
     * 参数
     */
    private String sence;

    /**
     * 二维码地址
     */
    private String imgUrl;

    /**
     * 状态，0未关联地址，1已关联地址
     */
    private int status;

    /**
     * 创建时间
     */
    private java.util.Date createdSTime;

    public final static int DEL_STATUS = 1000;//数据软删除状态

    public final static int DEL_IMG_STATUS = 1001;//图片删除状态

    public final static int DEL_DATA_AND_IMG_STATUS = 1002;//数据+图片删除状态
}
