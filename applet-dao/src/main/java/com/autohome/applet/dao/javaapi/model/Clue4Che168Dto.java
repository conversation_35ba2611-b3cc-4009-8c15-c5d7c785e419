package com.autohome.applet.dao.javaapi.model;

public class Clue4Che168Dto {


    private String keyPhone; // 手机号：必填（已支持）  //入库
    private Integer keyUsedCarSourceId;// 车源id：必填
    private Integer keyCardCityId;//:车源城市id
    private String keyName;//：选填 姓名  //入库
    private Integer keyPurposeBrandId;//：选填 意向品牌
    private Integer keyPurposeFactoryId;//：选填 意向厂商
    private Integer keyCarAudiId;//：选填 意向车系
    private Integer keyCarTypeId;//： 选填 意向车型
    private String keyUsedCarBusinessId;//：商家id
    private Integer keyUserId;//：userid
    private String keyClientOrderIp;//： 必填(已支持) 客户端下单IP 用来限制IP询价数量的字段吗
    private String keyExt5;//：外采媒体ID key_ext5
    private Integer keySupplyBusinessId;//:线索供应业务方ID
    private String keyOrderTime;//线索产生时间：必填；yyyy-MM-dd HH:mm:ss（已支持）
    private String keyInsideLinkidId;
    private String keyExt6;
    private String keyExt7;
    private String pushAppKey;
    private Integer splitCode;
    private Integer keyPrivateFlag;
    private Integer keyTypeId;
    private Integer keyOrderCityId;
    private String keyExt4;
    //private Long keyReleaseId;
    // fromtype;// 来源：必填，120 汽车之家小程序原生二手车（放在json里）
    // offertype;// 类型：必填，固定传0；(放在json里）
    // detailpageurl;// 当前页面url：必填（放到json里）
    // sourceid;// 询价入口来源：必填；4 详情页底部询价按钮（放在json里）
    // cartype;//：必填； 10优先置顶（CPL）、20精准推广（CPC）、30本地推荐（CPT）、40本地刷新推荐（CPT）、50异地推荐（CPT）、 90智选推荐（CPD），100智选刷新（CPD）（放到json里）
    // sessionid;// 拼接至 keyExt7 2048
    // deviceid;// 拼接至 keyExt7


//    public Long getKeyReleaseId() {
//        return 322653L;
//        //return keyReleaseId;
//    }
//
//    public void setKeyReleaseId(Long keyReleaseId) {
//        this.keyReleaseId = keyReleaseId;
//    }


    public String getKeyExt4() {
        return keyExt4;
    }

    public void setKeyExt4(String keyExt4) {
        this.keyExt4 = keyExt4;
    }

    public Integer getKeyOrderCityId() {
        return keyOrderCityId;
    }

    public void setKeyOrderCityId(Integer keyOrderCityId) {
        this.keyOrderCityId = keyOrderCityId;
    }

    public String getKeyPhone() {
        return keyPhone;
    }

    public void setKeyPhone(String keyPhone) {
        this.keyPhone = keyPhone;
    }

    public Integer getKeyUsedCarSourceId() {
        return keyUsedCarSourceId;
    }

    public void setKeyUsedCarSourceId(Integer keyUsedCarSourceId) {
        this.keyUsedCarSourceId = keyUsedCarSourceId;
    }

    public Integer getKeyCardCityId() {
        return keyCardCityId;
    }

    public void setKeyCardCityId(Integer keyCardCityId) {
        this.keyCardCityId = keyCardCityId;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public Integer getKeyPurposeBrandId() {
        return keyPurposeBrandId;
    }

    public void setKeyPurposeBrandId(Integer keyPurposeBrandId) {
        this.keyPurposeBrandId = keyPurposeBrandId;
    }

    public Integer getKeyPurposeFactoryId() {
        return keyPurposeFactoryId;
    }

    public void setKeyPurposeFactoryId(Integer keyPurposeFactoryId) {
        this.keyPurposeFactoryId = keyPurposeFactoryId;
    }

    public Integer getKeyCarAudiId() {
        return keyCarAudiId;
    }

    public void setKeyCarAudiId(Integer keyCarAudiId) {
        this.keyCarAudiId = keyCarAudiId;
    }


    public Integer getKeyCarTypeId() {
        return keyCarTypeId;
    }

    public void setKeyCarTypeId(Integer keyCarTypeId) {
        this.keyCarTypeId = keyCarTypeId;
    }

    public String getKeyInsideLinkidId() {
        return keyInsideLinkidId;
    }

    public void setKeyInsideLinkidId(String keyInsideLinkidId) {
        this.keyInsideLinkidId = keyInsideLinkidId;
    }

    public String getKeyUsedCarBusinessId() {
        return keyUsedCarBusinessId;
    }

    public void setKeyUsedCarBusinessId(String keyUsedCarBusinessId) {
        this.keyUsedCarBusinessId = keyUsedCarBusinessId;
    }

    public Integer getKeyUserId() {
        return keyUserId;
    }

    public void setKeyUserId(Integer keyUserId) {
        this.keyUserId = keyUserId;
    }

    public String getKeyClientOrderIp() {
        return keyClientOrderIp;
    }

    public void setKeyClientOrderIp(String keyClientOrderIp) {
        this.keyClientOrderIp = keyClientOrderIp;
    }

    public String getKeyExt5() {
        return "";
        //return keyExt5;
    }

    public void setKeyExt5(String keyExt5) {
        this.keyExt5 = keyExt5;
    }

    public Integer getKeySupplyBusinessId() {
        return keySupplyBusinessId;
    }

    public void setKeySupplyBusinessId(Integer keySupplyBusinessId) {
        this.keySupplyBusinessId = keySupplyBusinessId;
    }

    public String getKeyOrderTime() {
        return keyOrderTime;
    }

    public void setKeyOrderTime(String keyOrderTime) {
        this.keyOrderTime = keyOrderTime;
    }

    public String getKeyExt7() {
        return keyExt7;
    }

    public void setKeyExt7(String keyExt7) {
        this.keyExt7 = keyExt7;
    }

    public String getKeyExt6() {
        return keyExt6;
    }

    public void setKeyExt6(String keyExt6) {
        this.keyExt6 = keyExt6;
    }

    public String getPushAppKey() {
        return pushAppKey;
    }

    public void setPushAppKey(String pushAppKey) {
        this.pushAppKey = pushAppKey;
    }

    public Integer getSplitCode() {
        return 0;
        //return splitCode;
    }

    public void setSplitCode(Integer splitCode) {
        this.splitCode = splitCode;
    }

    public Integer getKeyPrivateFlag() {
        return 0;
        //return keyPrivateFlag;
    }

    public void setKeyPrivateFlag(Integer keyPrivateFlag) {
        this.keyPrivateFlag = keyPrivateFlag;
    }

    public Integer getKeyTypeId() {
        return 8;
        //return keyTypeId;
    }

    public void setKeyTypeId(Integer keyTypeId) {
        this.keyTypeId = keyTypeId;
    }
}
