package com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model;

import java.util.Date;

public class UserSettings {
    private Integer id;

    private String openid;

    private Integer personalizedrec;

    private Byte isDel;

    private Date createdStime;

    private Date modifiedStime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid == null ? null : openid.trim();
    }

    public Integer getPersonalizedrec() {
        return personalizedrec;
    }

    public void setPersonalizedrec(Integer personalizedrec) {
        this.personalizedrec = personalizedrec;
    }

    public Byte getIsDel() {
        return isDel;
    }

    public void setIsDel(Byte isDel) {
        this.isDel = isDel;
    }

    public Date getCreatedStime() {
        return createdStime;
    }

    public void setCreatedStime(Date createdStime) {
        this.createdStime = createdStime;
    }

    public Date getModifiedStime() {
        return modifiedStime;
    }

    public void setModifiedStime(Date modifiedStime) {
        this.modifiedStime = modifiedStime;
    }
}