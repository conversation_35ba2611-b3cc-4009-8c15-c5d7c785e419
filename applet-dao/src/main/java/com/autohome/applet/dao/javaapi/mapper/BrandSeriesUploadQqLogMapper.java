package com.autohome.applet.dao.javaapi.mapper;

import com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQqLog;
import com.autonews.database.db.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("db-autoopen")
@Repository
public interface BrandSeriesUploadQqLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BrandSeriesUploadQqLog row);

    BrandSeriesUploadQqLog selectByPrimaryKey(Long id);

    List<BrandSeriesUploadQqLog> selectAll();

    int updateByPrimaryKey(BrandSeriesUploadQqLog row);
}