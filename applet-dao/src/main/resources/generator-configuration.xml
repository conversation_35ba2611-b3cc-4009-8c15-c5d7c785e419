<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
<!--        <classPathEntry location="/Users/<USER>/.m2/repository/com/microsoft/sqlserver/mssql-jdbc/11.2.1.jre8/mssql-jdbc-11.2.1.jre8.jar"/>-->
<!--    <classPathEntry location="C:\Users\<USER>\.m2\repository\com\microsoft\sqlserver\mssql-jdbc\11.2.1.jre8\mssql-jdbc-11.2.1.jre8.jar"/>-->
    <classPathEntry location="C:\Users\<USER>\.m2\repository\com\microsoft\sqlserver\mssql-jdbc\11.2.1.jre8\mssql-jdbc-11.2.1.jre8.jar"/>
    <context id="AutoOpen" targetRuntime="MyBatis3Simple">
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.microsoft.sqlserver.jdbc.SQLServerDriver"
                        connectionURL="******************************************************************************************************************;"
                        userId="AutoOpenAdmin"
                        password="694030AB-7800-45E8-AA3A-09A84D27E8E9">
        </jdbcConnection>
<!--        <jdbcConnection driverClass="com.microsoft.sqlserver.jdbc.SQLServerDriver"-->
<!--                        connectionURL="****************************************************************************************************************;"-->
<!--                        userId="AutoOpenAdmin"-->
<!--                        password="694030AB-7800-45E8-AA3A-09A84D27E8E9">-->
<!--        </jdbcConnection>-->

        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
        </javaTypeResolver>


        <!---->
        <javaModelGenerator targetPackage="com.autohome.applet.dao.javaapi.model" targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
            <property name="useCamelCase" value="true"/>
        </javaModelGenerator>


        <sqlMapGenerator targetPackage="javaapi.mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>


        <javaClientGenerator type="XMLMAPPER" targetPackage="com.autohome.applet.dao.javaapi.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <table tableName="newcarevent" domainObjectName="NewCarEvent"/>

<!--        <table tableName="TestDriveForm" domainObjectName="TestDriveForm"/>-->
<!--        <table tableName="SweetCity" domainObjectName="SweetCity"/>-->
        <table tableName="minitokenvalue" domainObjectName="MiniTokenValue"></table>
<!--        <table tableName="second_hand_car_org_info_log" domainObjectName="SecondHandCarOrgInfoLog"></table>-->
<!--        <table tableName="second_hand_car_product_info" domainObjectName="SecondHandCarProductInfo"></table>-->
<!--        <table tableName="second_hand_car_product_info_log" domainObjectName="SecondHandCarProductInfoLog"></table>-->
<!--        <table tableName="local_link_report_log" domainObjectName="LocalLinkReportLog"></table>-->
        <!--<table tableName="brand_series_upload_qq_log" domainObjectName="BrandSeriesUploadQqLog"></table>-->
<!--        <table tableName="second_hand_car_product_info_log" domainObjectName="SecondHandCarProductInfoLog"></table>-->
        <!--
                <table schema="social_ec" tableName="second_hand_car_product_info" domainObjectName="second_hand_car_product_info"></table>
                <table schema="social_ec" tableName="sec_tab_product" domainObjectName="TabProduct"></table>
        -->

    </context>
</generatorConfiguration>