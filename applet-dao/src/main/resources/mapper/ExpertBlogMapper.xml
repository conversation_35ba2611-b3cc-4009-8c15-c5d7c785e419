<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.netcoreapi.mapper.content.ExpertBlogMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.netcoreapi.model.ExpertBlog">
        <id column="eBlogId" jdbcType="INTEGER" property="eblogid"/>
        <result column="eMemberName" jdbcType="VARCHAR" property="emembername"/>
        <result column="eMemberId" jdbcType="INTEGER" property="ememberid"/>
        <result column="eBlogName" jdbcType="VARCHAR" property="eblogname"/>
        <result column="eReplyCount" jdbcType="INTEGER" property="ereplycount"/>
        <result column="eIsClose" jdbcType="TINYINT" property="eisclose"/>
        <result column="eAddDate" jdbcType="TIMESTAMP" property="eadddate"/>
        <result column="eInteoduce" jdbcType="VARCHAR" property="einteoduce"/>
        <result column="eEditorId" jdbcType="INTEGER" property="eeditorid"/>
        <result column="eNickName" jdbcType="VARCHAR" property="enickname"/>
        <result column="eMemberPic" jdbcType="VARCHAR" property="ememberpic"/>
        <result column="eChannelurl" jdbcType="VARCHAR" property="echannelurl"/>
        <result column="eSex" jdbcType="TINYINT" property="esex"/>
        <result column="eBirthday" jdbcType="TIMESTAMP" property="ebirthday"/>
        <result column="eJoinDate" jdbcType="TIMESTAMP" property="ejoindate"/>
        <result column="eDepartment" jdbcType="NVARCHAR" property="edepartment"/>
        <result column="ePosition" jdbcType="NVARCHAR" property="eposition"/>
        <result column="eHobby" jdbcType="NVARCHAR" property="ehobby"/>
        <result column="eStatus" jdbcType="NVARCHAR" property="estatus"/>
        <result column="eChannelName" jdbcType="NVARCHAR" property="echannelname"/>
        <result column="ePictureSquare" jdbcType="VARCHAR" property="epicturesquare"/>
        <result column="eMemberPicNew" jdbcType="VARCHAR" property="ememberpicnew"/>
        <result column="eSignature" jdbcType="NVARCHAR" property="esignature"/>
        <result column="eLevel" jdbcType="TINYINT" property="elevel"/>
        <result column="eTitle" jdbcType="VARCHAR" property="etitle"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from ExpertBlog
        where eBlogId = #{eblogid,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.autohome.applet.dao.netcoreapi.model.ExpertBlog">
        insert into ExpertBlog (eBlogId, eMemberName, eMemberId,
                                eBlogName, eReplyCount, eIsClose,
                                eAddDate, eInteoduce, eEditorId,
                                eNickName, eMemberPic, eChannelurl,
                                eSex, eBirthday, eJoinDate,
                                eDepartment, ePosition, eHobby,
                                eStatus, eChannelName, ePictureSquare,
                                eMemberPicNew, eSignature, eLevel,
                                eTitle)
        values (#{eblogid,jdbcType=INTEGER}, #{emembername,jdbcType=VARCHAR}, #{ememberid,jdbcType=INTEGER},
                #{eblogname,jdbcType=VARCHAR}, #{ereplycount,jdbcType=INTEGER}, #{eisclose,jdbcType=TINYINT},
                #{eadddate,jdbcType=TIMESTAMP}, #{einteoduce,jdbcType=VARCHAR}, #{eeditorid,jdbcType=INTEGER},
                #{enickname,jdbcType=VARCHAR}, #{ememberpic,jdbcType=VARCHAR}, #{echannelurl,jdbcType=VARCHAR},
                #{esex,jdbcType=TINYINT}, #{ebirthday,jdbcType=TIMESTAMP}, #{ejoindate,jdbcType=TIMESTAMP},
                #{edepartment,jdbcType=NVARCHAR}, #{eposition,jdbcType=NVARCHAR}, #{ehobby,jdbcType=NVARCHAR},
                #{estatus,jdbcType=NVARCHAR}, #{echannelname,jdbcType=NVARCHAR}, #{epicturesquare,jdbcType=VARCHAR},
                #{ememberpicnew,jdbcType=VARCHAR}, #{esignature,jdbcType=NVARCHAR}, #{elevel,jdbcType=TINYINT},
                #{etitle,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.netcoreapi.model.ExpertBlog">
        update ExpertBlog
        set eMemberName    = #{emembername,jdbcType=VARCHAR},
            eMemberId      = #{ememberid,jdbcType=INTEGER},
            eBlogName      = #{eblogname,jdbcType=VARCHAR},
            eReplyCount    = #{ereplycount,jdbcType=INTEGER},
            eIsClose       = #{eisclose,jdbcType=TINYINT},
            eAddDate       = #{eadddate,jdbcType=TIMESTAMP},
            eInteoduce     = #{einteoduce,jdbcType=VARCHAR},
            eEditorId      = #{eeditorid,jdbcType=INTEGER},
            eNickName      = #{enickname,jdbcType=VARCHAR},
            eMemberPic     = #{ememberpic,jdbcType=VARCHAR},
            eChannelurl    = #{echannelurl,jdbcType=VARCHAR},
            eSex           = #{esex,jdbcType=TINYINT},
            eBirthday      = #{ebirthday,jdbcType=TIMESTAMP},
            eJoinDate      = #{ejoindate,jdbcType=TIMESTAMP},
            eDepartment    = #{edepartment,jdbcType=NVARCHAR},
            ePosition      = #{eposition,jdbcType=NVARCHAR},
            eHobby         = #{ehobby,jdbcType=NVARCHAR},
            eStatus        = #{estatus,jdbcType=NVARCHAR},
            eChannelName   = #{echannelname,jdbcType=NVARCHAR},
            ePictureSquare = #{epicturesquare,jdbcType=VARCHAR},
            eMemberPicNew  = #{ememberpicnew,jdbcType=VARCHAR},
            eSignature     = #{esignature,jdbcType=NVARCHAR},
            eLevel         = #{elevel,jdbcType=TINYINT},
            eTitle         = #{etitle,jdbcType=VARCHAR}
        where eBlogId = #{eblogid,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select eBlogId,
               eMemberName,
               eMemberId,
               eBlogName,
               eReplyCount,
               eIsClose,
               eAddDate,
               eInteoduce,
               eEditorId,
               eNickName,
               eMemberPic,
               eChannelurl,
               eSex,
               eBirthday,
               eJoinDate,
               eDepartment,
               ePosition,
               eHobby,
               eStatus,
               eChannelName,
               ePictureSquare,
               eMemberPicNew,
               eSignature,
               eLevel,
               eTitle
        from ExpertBlog
        where eBlogId = #{eblogid,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select eBlogId,
               eMemberName,
               eMemberId,
               eBlogName,
               eReplyCount,
               eIsClose,
               eAddDate,
               eInteoduce,
               eEditorId,
               eNickName,
               eMemberPic,
               eChannelurl,
               eSex,
               eBirthday,
               eJoinDate,
               eDepartment,
               ePosition,
               eHobby,
               eStatus,
               eChannelName,
               ePictureSquare,
               eMemberPicNew,
               eSignature,
               eLevel,
               eTitle
        from ExpertBlog
    </select>


    <select id="GetExperBlogListByEditorIds" resultMap="BaseResultMap">
        SELECT A.eBlogId,
               A.eMemberId,
               A.eMemberPic,
               A.eNickName,
               A.eBlogName,
               A.eDepartment,
               A.eEditorId,
               A.eChannelName,
               A.ePosition,
               --B.Allview,
               A.eSex,
               A.eBirthday,
               A.eHobby,
               A.eJoinDate,
               A.ePictureSquare    AS PictureSquare,
               (SELECT COUNT(1)
                FROM Articles C (NOLOCK)
                WHERE A.eEditorId = C.Editor
                  AND C.isPublish = 1
                  AND C.isDel = 0) AS ArticleCount,
               (CASE
                    WHEN ePosition = '总监' THEN
                        1
                    WHEN ePosition = '总编' THEN
                        2
                    WHEN ePosition = '高级主编' THEN
                        3
                    WHEN ePosition = '主编' THEN
                        4
                    WHEN ePosition = '责任编辑' THEN
                        5
                    WHEN ePosition = '编辑' THEN
                        6
                    ELSE
                        7
                   END
                   )               AS eJbType
        FROM ExpertBlog AS A WITH (NOLOCK)
    INNER JOIN BlogPV AS B
        WITH (NOLOCK)
        ON B.BlogID = A.eBlogId
        WHERE A.eEditorId IN
        <foreach collection="idlist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ORDER BY A.eChannelName ASC,
            eJbType,
            B.Allview DESC;
    </select>

</mapper>