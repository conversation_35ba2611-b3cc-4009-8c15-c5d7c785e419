<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.netcoreapi.mapper.content.BlogPVMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.netcoreapi.model.BlogPV">
        <id column="BlogID" jdbcType="INTEGER" property="blogid"/>
        <result column="TodayView" jdbcType="INTEGER" property="todayview"/>
        <result column="YesterdayView" jdbcType="INTEGER" property="yesterdayview"/>
        <result column="Allview" jdbcType="BIGINT" property="allview"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from BlogPV
        where BlogID = #{blogid,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.autohome.applet.dao.netcoreapi.model.BlogPV">
        insert into BlogPV (BlogID, TodayView, YesterdayView,
                            Allview)
        values (#{blogid,jdbcType=INTEGER}, #{todayview,jdbcType=INTEGER}, #{yesterdayview,jdbcType=INTEGER},
                #{allview,jdbcType=BIGINT})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.netcoreapi.model.BlogPV">
        update BlogPV
        set TodayView     = #{todayview,jdbcType=INTEGER},
            YesterdayView = #{yesterdayview,jdbcType=INTEGER},
            Allview       = #{allview,jdbcType=BIGINT}
        where BlogID = #{blogid,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select BlogID, TodayView, YesterdayView, Allview
        from BlogPV
        where BlogID = #{blogid,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select BlogID, TodayView, YesterdayView, Allview
        from BlogPV
    </select>
</mapper>