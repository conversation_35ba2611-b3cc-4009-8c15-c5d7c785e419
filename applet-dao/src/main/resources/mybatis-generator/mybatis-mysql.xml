<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="mybatis-generator/jdbc.properties"/>

    <context id="mysql" targetRuntime="MyBatis3">
        <commentGenerator>
            <property name="suppressDate" value="false"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.mysql.driverClassName}"
                        connectionURL="${jdbc.mysql.url}"
                        userId="${jdbc.mysql.username}"
                        password="${jdbc.mysql.password}"/>


        <javaModelGenerator targetPackage="com.autohome.applet.dao.javaapi.model" targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
            <property name="useCamelCase" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="javaapi.mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.autohome.applet.dao.javaapi.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>


        <table tableName="resource_feed" domainObjectName="ResourceFeed" enableCountByExample="true" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="true" selectByExampleQueryId="true" />
<!--        <table tableName="applet_userbehaviorhistory_hash_0" domainObjectName="AppletUserbehaviorhistoryHash" enableCountByExample="true" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="true" selectByExampleQueryId="true" />-->

    </context>
</generatorConfiguration>