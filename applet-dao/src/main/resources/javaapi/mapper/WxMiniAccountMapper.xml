<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.WxMiniAccountMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.WxMiniAccount">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="xcx_open_id" jdbcType="VARCHAR" property="xcxOpenId"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="biz_type" jdbcType="INTEGER" property="bizType"/>
        <result column="source_id" jdbcType="INTEGER" property="sourceId"/>
        <result column="series_id" jdbcType="VARCHAR" property="seriesId"/>
        <result column="spec_id" jdbcType="VARCHAR" property="specId"/>
        <result column="city_id" jdbcType="INTEGER" property="cityId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="pm" jdbcType="INTEGER" property="pm"/>
        <result column="pluginversion" jdbcType="VARCHAR" property="pluginversion"/>
        <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime"/>
        <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime"/>
        <result column="is_del" jdbcType="INTEGER" property="isDel"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wx_mini_account
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.WxMiniAccount">
        insert into wx_mini_account (user_id, device_id,
                                     xcx_open_id, union_id, biz_type,
                                     source_id, series_id, spec_id,
                                     city_id, status, pm,
                                     pluginversion, Created_STime, Modified_STime,
                                     is_del)
        values (#{userId,jdbcType=INTEGER}, #{deviceId,jdbcType=VARCHAR},
                #{xcxOpenId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER},
                #{sourceId,jdbcType=INTEGER}, #{seriesId,jdbcType=VARCHAR}, #{specId,jdbcType=VARCHAR},
                #{cityId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{pm,jdbcType=INTEGER},
                #{pluginversion,jdbcType=VARCHAR}, #{createdStime,jdbcType=TIMESTAMP},
                #{modifiedStime,jdbcType=TIMESTAMP},
                #{isDel,jdbcType=INTEGER})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.WxMiniAccount">
        update wx_mini_account
        set user_id        = #{userId,jdbcType=INTEGER},
            device_id      = #{deviceId,jdbcType=VARCHAR},
            xcx_open_id    = #{xcxOpenId,jdbcType=VARCHAR},
            union_id       = #{unionId,jdbcType=VARCHAR},
            biz_type       = #{bizType,jdbcType=INTEGER},
            source_id      = #{sourceId,jdbcType=INTEGER},
            series_id      = #{seriesId,jdbcType=VARCHAR},
            spec_id        = #{specId,jdbcType=VARCHAR},
            city_id        = #{cityId,jdbcType=INTEGER},
            status         = #{status,jdbcType=INTEGER},
            pm             = #{pm,jdbcType=INTEGER},
            pluginversion  = #{pluginversion,jdbcType=VARCHAR},
            Created_STime  = #{createdStime,jdbcType=TIMESTAMP},
            Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
            is_del         = #{isDel,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select id,
               user_id,
               device_id,
               xcx_open_id,
               union_id,
               biz_type,
               source_id,
               series_id,
               spec_id,
               city_id,
               status,
               pm,
               pluginversion,
               Created_STime,
               Modified_STime,
               is_del
        from wx_mini_account
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select id,
               user_id,
               device_id,
               xcx_open_id,
               union_id,
               biz_type,
               source_id,
               series_id,
               spec_id,
               city_id,
               status,
               pm,
               pluginversion,
               Created_STime,
               Modified_STime,
               is_del
        from wx_mini_account
    </select>


    <select id="getSubInfo" resultType="com.autohome.applet.dao.javaapi.model.wxmsg.SubscriptionInfoDTO">
        select a.id,
               a.user_id,
               a.device_id,
               a.xcx_open_id,
               a.union_id,
               a.biz_type,
               a.pm,
               a.series_id,
               a.Created_STime,
               a.Modified_STime,
               b.fwh_open_id,
               a.status
        from wx_mini_account a with (nolock),
             wx_service_account  b with (nolock)

        where a.union_id = b.union_id
          and a.device_id = #{deviceId}
        <if test="bizType != -1">
            and a.biz_type = #{bizType}
        </if>

    </select>


    <select id="getWxMiniAccountByDeviceId" resultMap="BaseResultMap">
        select id,
               user_id,
               device_id,
               xcx_open_id,
               union_id,
               biz_type,
               source_id,
               series_id,
               spec_id,
               city_id,
               status,
               pm,
               pluginversion,
               Created_STime,
               Modified_STime,
               is_del
        from wx_mini_account with (nolock)
        where device_id = #{deviceId}
          and biz_type = #{bizType}
    </select>
    <select id="getWxMiniAccountByUnionId" resultMap="BaseResultMap">
        select id,
               user_id,
               device_id,
               xcx_open_id,
               union_id,
               biz_type,
               source_id,
               series_id,
               spec_id,
               city_id,
               status,
               pm,
               pluginversion,
               Created_STime,
               Modified_STime,
               is_del
        from wx_mini_account with (nolock)
        where union_id = #{unionId}
          and biz_type = #{bizType}
    </select>

    <update id="updateById" parameterType="java.lang.Long">
        update wx_mini_account
        set status = 0
        where id = #{id}

    </update>

</mapper>