<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.WxUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.WxUserInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="subscribe" jdbcType="INTEGER" property="subscribe" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="headimgurl" jdbcType="VARCHAR" property="headimgurl" />
    <result column="subscribe_time" jdbcType="VARCHAR" property="subscribeTime" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="groupid" jdbcType="VARCHAR" property="groupid" />
    <result column="tagid_list" jdbcType="VARCHAR" property="tagidList" />
    <result column="subscribe_scene" jdbcType="VARCHAR" property="subscribeScene" />
    <result column="qr_scene" jdbcType="VARCHAR" property="qrScene" />
    <result column="qr_scene_str" jdbcType="VARCHAR" property="qrSceneStr" />
    <result column="created_stime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="modified_stime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, subscribe, openid, nickname, sex, language, city, province, country, headimgurl, 
    subscribe_time, unionid, remark, groupid, tagid_list, subscribe_scene, qr_scene, 
    qr_scene_str, created_stime, modified_stime, is_del
  </sql>
  <select id="selectByExample" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_user_info
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByOpenid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wx_user_info
    where openid = #{openId}
    and is_del = 0
  </select>

  <select id="selectAll" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfoExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wx_user_info
    where is_del = 0
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_user_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfo">
    insert into wx_user_info (subscribe, openid,
      nickname, sex, language, 
      city, province, country, 
      headimgurl, subscribe_time, unionid, 
      remark, groupid, tagid_list, 
      subscribe_scene, qr_scene, qr_scene_str
      )
    values (#{subscribe,jdbcType=INTEGER}, #{openid,jdbcType=VARCHAR},
      #{nickname,jdbcType=VARCHAR}, #{sex,jdbcType=INTEGER}, #{language,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, 
      #{headimgurl,jdbcType=VARCHAR}, #{subscribeTime,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{groupid,jdbcType=VARCHAR}, #{tagidList,jdbcType=VARCHAR}, 
      #{subscribeScene,jdbcType=VARCHAR}, #{qrScene,jdbcType=VARCHAR}, #{qrSceneStr,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfo">
    insert into wx_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="subscribe != null">
        subscribe,
      </if>
      <if test="openid != null">
        openid,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="language != null">
        language,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="headimgurl != null">
        headimgurl,
      </if>
      <if test="subscribeTime != null">
        subscribe_time,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="groupid != null">
        groupid,
      </if>
      <if test="tagidList != null">
        tagid_list,
      </if>
      <if test="subscribeScene != null">
        subscribe_scene,
      </if>
      <if test="qrScene != null">
        qr_scene,
      </if>
      <if test="qrSceneStr != null">
        qr_scene_str,
      </if>
      <if test="createdStime != null">
        created_stime,
      </if>
      <if test="modifiedStime != null">
        modified_stime,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="subscribe != null">
        #{subscribe,jdbcType=INTEGER},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="headimgurl != null">
        #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="subscribeTime != null">
        #{subscribeTime,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="groupid != null">
        #{groupid,jdbcType=VARCHAR},
      </if>
      <if test="tagidList != null">
        #{tagidList,jdbcType=VARCHAR},
      </if>
      <if test="subscribeScene != null">
        #{subscribeScene,jdbcType=VARCHAR},
      </if>
      <if test="qrScene != null">
        #{qrScene,jdbcType=VARCHAR},
      </if>
      <if test="qrSceneStr != null">
        #{qrSceneStr,jdbcType=VARCHAR},
      </if>
      <if test="createdStime != null">
        #{createdStime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedStime != null">
        #{modifiedStime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfoExample" resultType="java.lang.Long">
    select count(*) from wx_user_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfo">
    update wx_user_info
    <set>
      <if test="subscribe != null">
        subscribe = #{subscribe,jdbcType=INTEGER},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        language = #{language,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="headimgurl != null">
        headimgurl = #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="subscribeTime != null">
        subscribe_time = #{subscribeTime,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="groupid != null">
        groupid = #{groupid,jdbcType=VARCHAR},
      </if>
      <if test="tagidList != null">
        tagid_list = #{tagidList,jdbcType=VARCHAR},
      </if>
      <if test="subscribeScene != null">
        subscribe_scene = #{subscribeScene,jdbcType=VARCHAR},
      </if>
      <if test="qrScene != null">
        qr_scene = #{qrScene,jdbcType=VARCHAR},
      </if>
      <if test="qrSceneStr != null">
        qr_scene_str = #{qrSceneStr,jdbcType=VARCHAR},
      </if>
      <if test="createdStime != null">
        created_stime = #{createdStime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedStime != null">
        modified_stime = #{modifiedStime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.WxUserInfo">
    update wx_user_info
    set subscribe = #{subscribe,jdbcType=INTEGER},
      openid = #{openid,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=INTEGER},
      language = #{language,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      headimgurl = #{headimgurl,jdbcType=VARCHAR},
      subscribe_time = #{subscribeTime,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      groupid = #{groupid,jdbcType=VARCHAR},
      tagid_list = #{tagidList,jdbcType=VARCHAR},
      subscribe_scene = #{subscribeScene,jdbcType=VARCHAR},
      qr_scene = #{qrScene,jdbcType=VARCHAR},
      qr_scene_str = #{qrSceneStr,jdbcType=VARCHAR}
    where openid = #{openid}
  </update>
</mapper>