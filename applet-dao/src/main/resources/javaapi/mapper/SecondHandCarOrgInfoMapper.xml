<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SecondHandCarOrgInfoMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="branch_name" jdbcType="VARCHAR" property="branchName" />
    <result column="org_icon" jdbcType="VARCHAR" property="orgIcon" />
    <result column="org_score" jdbcType="NUMERIC" property="orgScore" />
    <result column="org_commentnum" jdbcType="INTEGER" property="orgCommentnum" />
    <result column="org_avgprice" jdbcType="INTEGER" property="orgAvgprice" />
    <result column="org_area" jdbcType="VARCHAR" property="orgArea" />
    <result column="org_province" jdbcType="VARCHAR" property="orgProvince" />
    <result column="org_city" jdbcType="VARCHAR" property="orgCity" />
    <result column="org_district" jdbcType="VARCHAR" property="orgDistrict" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="org_commentdetail" jdbcType="VARCHAR" property="orgCommentdetail" />
    <result column="org_servicetab" jdbcType="VARCHAR" property="orgServicetab" />
    <result column="org_year" jdbcType="INTEGER" property="orgYear" />
    <result column="org_case" jdbcType="INTEGER" property="orgCase" />
    <result column="org_servicecount" jdbcType="INTEGER" property="orgServicecount" />
    <result column="org_brand" jdbcType="VARCHAR" property="orgBrand" />
    <result column="org_category" jdbcType="VARCHAR" property="orgCategory" />
    <result column="org_url" jdbcType="VARCHAR" property="orgUrl" />
    <result column="org_worktime" jdbcType="VARCHAR" property="orgWorktime" />
    <result column="org_workday" jdbcType="VARCHAR" property="orgWorkday" />
    <result column="org_address" jdbcType="VARCHAR" property="orgAddress" />
    <result column="org_tel" jdbcType="VARCHAR" property="orgTel" />
    <result column="org_sourcename" jdbcType="VARCHAR" property="orgSourcename" />
    <result column="siteid" jdbcType="VARCHAR" property="siteid" />
    <result column="org_gift" jdbcType="VARCHAR" property="orgGift" />
    <result column="org_package" jdbcType="VARCHAR" property="orgPackage" />
    <result column="org_status" jdbcType="INTEGER" property="orgStatus" />
    <result column="org_verticalpic" jdbcType="VARCHAR" property="orgVerticalpic" />
    <result column="org_description" jdbcType="VARCHAR" property="orgDescription" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from second_hand_car_org_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo">
    insert into second_hand_car_org_info (org_id, org_name,
      branch_name, org_icon, org_score, 
      org_commentnum, org_avgprice, org_area, 
      org_province, org_city, org_district, 
      lng, lat, org_commentdetail, 
      org_servicetab, org_year, org_case, 
      org_servicecount, org_brand, org_category, 
      org_url, org_worktime, org_workday, 
      org_address, org_tel, org_sourcename, 
      siteid, org_gift, org_package, 
      org_status, org_verticalpic, org_description, 
      Created_STime, Modified_STime, is_del, 
      version)
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
      #{branchName,jdbcType=VARCHAR}, #{orgIcon,jdbcType=VARCHAR}, #{orgScore,jdbcType=NUMERIC}, 
      #{orgCommentnum,jdbcType=INTEGER}, #{orgAvgprice,jdbcType=INTEGER}, #{orgArea,jdbcType=VARCHAR}, 
      #{orgProvince,jdbcType=VARCHAR}, #{orgCity,jdbcType=VARCHAR}, #{orgDistrict,jdbcType=VARCHAR}, 
      #{lng,jdbcType=VARCHAR}, #{lat,jdbcType=VARCHAR}, #{orgCommentdetail,jdbcType=VARCHAR}, 
      #{orgServicetab,jdbcType=VARCHAR}, #{orgYear,jdbcType=INTEGER}, #{orgCase,jdbcType=INTEGER}, 
      #{orgServicecount,jdbcType=INTEGER}, #{orgBrand,jdbcType=VARCHAR}, #{orgCategory,jdbcType=VARCHAR}, 
      #{orgUrl,jdbcType=VARCHAR}, #{orgWorktime,jdbcType=VARCHAR}, #{orgWorkday,jdbcType=VARCHAR}, 
      #{orgAddress,jdbcType=VARCHAR}, #{orgTel,jdbcType=VARCHAR}, #{orgSourcename,jdbcType=VARCHAR}, 
      #{siteid,jdbcType=VARCHAR}, #{orgGift,jdbcType=VARCHAR}, #{orgPackage,jdbcType=VARCHAR}, 
      #{orgStatus,jdbcType=INTEGER}, #{orgVerticalpic,jdbcType=VARCHAR}, #{orgDescription,jdbcType=VARCHAR}, 
      #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, 
      #{version,jdbcType=BIGINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo">
    update second_hand_car_org_info
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      branch_name = #{branchName,jdbcType=VARCHAR},
      org_icon = #{orgIcon,jdbcType=VARCHAR},
      org_score = #{orgScore,jdbcType=NUMERIC},
      org_commentnum = #{orgCommentnum,jdbcType=INTEGER},
      org_avgprice = #{orgAvgprice,jdbcType=INTEGER},
      org_area = #{orgArea,jdbcType=VARCHAR},
      org_province = #{orgProvince,jdbcType=VARCHAR},
      org_city = #{orgCity,jdbcType=VARCHAR},
      org_district = #{orgDistrict,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      org_commentdetail = #{orgCommentdetail,jdbcType=VARCHAR},
      org_servicetab = #{orgServicetab,jdbcType=VARCHAR},
      org_year = #{orgYear,jdbcType=INTEGER},
      org_case = #{orgCase,jdbcType=INTEGER},
      org_servicecount = #{orgServicecount,jdbcType=INTEGER},
      org_brand = #{orgBrand,jdbcType=VARCHAR},
      org_category = #{orgCategory,jdbcType=VARCHAR},
      org_url = #{orgUrl,jdbcType=VARCHAR},
      org_worktime = #{orgWorktime,jdbcType=VARCHAR},
      org_workday = #{orgWorkday,jdbcType=VARCHAR},
      org_address = #{orgAddress,jdbcType=VARCHAR},
      org_tel = #{orgTel,jdbcType=VARCHAR},
      org_sourcename = #{orgSourcename,jdbcType=VARCHAR},
      siteid = #{siteid,jdbcType=VARCHAR},
      org_gift = #{orgGift,jdbcType=VARCHAR},
      org_package = #{orgPackage,jdbcType=VARCHAR},
      org_status = #{orgStatus,jdbcType=INTEGER},
      org_verticalpic = #{orgVerticalpic,jdbcType=VARCHAR},
      org_description = #{orgDescription,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, org_id, org_name, branch_name, org_icon, org_score, org_commentnum, org_avgprice, 
    org_area, org_province, org_city, org_district, lng, lat, org_commentdetail, org_servicetab, 
    org_year, org_case, org_servicecount, org_brand, org_category, org_url, org_worktime, 
    org_workday, org_address, org_tel, org_sourcename, siteid, org_gift, org_package, 
    org_status, org_verticalpic, org_description, Created_STime, Modified_STime, is_del, 
    version
    from second_hand_car_org_info with(nolock)
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, org_id, org_name, branch_name, org_icon, org_score, org_commentnum, org_avgprice, 
    org_area, org_province, org_city, org_district, lng, lat, org_commentdetail, org_servicetab, 
    org_year, org_case, org_servicecount, org_brand, org_category, org_url, org_worktime, 
    org_workday, org_address, org_tel, org_sourcename, siteid, org_gift, org_package, 
    org_status, org_verticalpic, org_description, Created_STime, Modified_STime, is_del, 
    version
    from second_hand_car_org_info with(nolock)
  </select>

  <update id="save" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo">
    MERGE INTO second_hand_car_org_info AS target
    USING (VALUES (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
    #{branchName,jdbcType=VARCHAR}, #{orgIcon,jdbcType=VARCHAR}, #{orgScore,jdbcType=NUMERIC},
    #{orgCommentnum,jdbcType=INTEGER}, #{orgAvgprice,jdbcType=INTEGER}, #{orgArea,jdbcType=VARCHAR},
    #{orgProvince,jdbcType=VARCHAR}, #{orgCity,jdbcType=VARCHAR}, #{orgDistrict,jdbcType=VARCHAR},
    #{lng,jdbcType=VARCHAR}, #{lat,jdbcType=VARCHAR}, #{orgCommentdetail,jdbcType=VARCHAR},
    #{orgServicetab,jdbcType=VARCHAR}, #{orgYear,jdbcType=INTEGER}, #{orgCase,jdbcType=INTEGER},
    #{orgServicecount,jdbcType=INTEGER}, #{orgBrand,jdbcType=VARCHAR}, #{orgCategory,jdbcType=VARCHAR},
    #{orgUrl,jdbcType=VARCHAR}, #{orgWorktime,jdbcType=VARCHAR}, #{orgWorkday,jdbcType=VARCHAR},
    #{orgAddress,jdbcType=VARCHAR}, #{orgTel,jdbcType=VARCHAR}, #{orgSourcename,jdbcType=VARCHAR},
    #{siteid,jdbcType=VARCHAR}, #{orgGift,jdbcType=VARCHAR}, #{orgPackage,jdbcType=VARCHAR},
    #{orgStatus,jdbcType=INTEGER}, #{orgVerticalpic,jdbcType=VARCHAR}, #{orgDescription,jdbcType=VARCHAR},
    #{modifiedStime,jdbcType=TIMESTAMP}, #{version,jdbcType=BIGINT}
    )) AS source (org_id, org_name,
    branch_name, org_icon, org_score,
    org_commentnum, org_avgprice, org_area,
    org_province, org_city, org_district,
    lng, lat, org_commentdetail,
    org_servicetab, org_year, org_case,
    org_servicecount, org_brand, org_category,
    org_url, org_worktime, org_workday,
    org_address, org_tel, org_sourcename,
    siteid, org_gift, org_package,
    org_status, org_verticalpic, org_description,
    Modified_STime, version
    )
    ON target.org_id = source.org_id
    WHEN MATCHED THEN
    UPDATE SET org_id=source.org_id, org_name=source.org_name,
    branch_name=source.branch_name, org_icon=source.org_icon, org_score=source.org_score,
    org_commentnum=source.org_commentnum, org_avgprice=source.org_avgprice, org_area=source.org_area,
    org_province=source.org_province, org_city=source.org_city,
    lng=source.lng, lat=source.lat, org_commentdetail=source.org_commentdetail,
    org_servicetab=source.org_servicetab, org_year=source.org_year, org_case=source.org_case,
    org_servicecount=source.org_servicecount, org_brand=source.org_brand, org_category=source.org_category,
    org_url=source.org_url, org_worktime=source.org_worktime, org_workday=source.org_workday,
    org_address=source.org_address, org_tel=source.org_tel, org_sourcename=source.org_sourcename,
    siteid=source.siteid, org_gift=source.org_gift, org_package=source.org_package,
    org_status=source.org_status, org_verticalpic=source.org_verticalpic, org_description=source.org_description,
    Modified_STime=source.Modified_STime, version=source.version
    WHEN NOT MATCHED THEN
    INSERT (org_id, org_name,
    branch_name, org_icon, org_score,
    org_commentnum, org_avgprice, org_area,
    org_province, org_city, org_district,
    lng, lat, org_commentdetail,
    org_servicetab, org_year, org_case,
    org_servicecount, org_brand, org_category,
    org_url, org_worktime, org_workday,
    org_address, org_tel, org_sourcename,
    siteid, org_gift, org_package,
    org_status, org_verticalpic, org_description,
    Modified_STime, version
    ) VALUES (source.org_id, source.org_name,
    source.branch_name, source.org_icon, source.org_score,
    source.org_commentnum, source.org_avgprice, source.org_area,
    source.org_province, source.org_city, source.org_district,
    source.lng, source.lat, source.org_commentdetail,
    source.org_servicetab, source.org_year, source.org_case,
    source.org_servicecount, source.org_brand, source.org_category,
    source.org_url, source.org_worktime, source.org_workday,
    source.org_address, source.org_tel, source.org_sourcename,
    source.siteid, source.org_gift, source.org_package,
    source.org_status, source.org_verticalpic, source.org_description,
    source.Modified_STime, source.version
    );
  </update>

  <update id="updateVersionByOrgId">
    update second_hand_car_org_info
    set version = #{version},
    Modified_STime = getdate()
    where org_id = #{orgId}
  </update>

  <select id="listByVersion" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select top 20000 *
    from second_hand_car_org_info  with(nolock)
    where version &gt; #{version,jdbcType=BIGINT}
    order by version, Modified_STime
  </select>

  <update id="updateStatusByOrgId">
    update second_hand_car_org_info
    set
    org_status = #{orgStatus},
    Modified_STime = getdate(),
    version = #{version}
    where org_id = #{orgId}
  </update>

  <select id="listByOrgIds" parameterType="java.lang.String" resultMap="BaseResultMap">
    select *
    from second_hand_car_org_info  with(nolock)
    where org_id IN
    <foreach collection="orgIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>
</mapper>