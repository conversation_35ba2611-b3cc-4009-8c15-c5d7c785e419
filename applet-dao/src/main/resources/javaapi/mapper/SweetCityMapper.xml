<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SweetCityMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.SweetCity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="Sweetid" jdbcType="INTEGER" property="sweetid" />
    <result column="Cityid" jdbcType="INTEGER" property="cityid" />
    <result column="CityName" jdbcType="VARCHAR" property="cityname" />
    <result column="ProvinceId" jdbcType="INTEGER" property="provinceid" />
    <result column="ProvinceName" jdbcType="VARCHAR" property="provincename" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="Country" jdbcType="INTEGER" property="country" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SweetCity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.SweetCity">
    insert into SweetCity (Sweetid, Cityid,
      CityName, ProvinceId, ProvinceName, 
      Created_STime, Modified_STime, is_del, 
      Country)
    values (#{sweetid,jdbcType=INTEGER}, #{cityid,jdbcType=INTEGER},
      #{cityname,jdbcType=VARCHAR}, #{provinceid,jdbcType=INTEGER}, #{provincename,jdbcType=VARCHAR}, 
      #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, 
      #{country,jdbcType=INTEGER})
  </insert>
  <insert id="insertCountry">
    insert into SweetCity (Sweetid, Cityid,
                           CityName, ProvinceId, ProvinceName,
                           Created_STime, Modified_STime, is_del,
                           Country)
    values (#{sweetid,jdbcType=INTEGER}, 0, '', 0, '', getdate(), getdate(), 0, 1)
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.SweetCity">
    update SweetCity
    set Sweetid = #{sweetid,jdbcType=INTEGER},
      Cityid = #{cityid,jdbcType=INTEGER},
      CityName = #{cityname,jdbcType=VARCHAR},
      ProvinceId = #{provinceid,jdbcType=INTEGER},
      ProvinceName = #{provincename,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      Country = #{country,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, Sweetid, Cityid, CityName, ProvinceId, ProvinceName, Created_STime, Modified_STime, 
    is_del, Country
    from SweetCity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, Sweetid, Cityid, CityName, ProvinceId, ProvinceName, Created_STime, Modified_STime, 
    is_del, Country
    from SweetCity
  </select>
</mapper>