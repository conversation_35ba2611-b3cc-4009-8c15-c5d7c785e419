<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SecondHandCarUploadBaiduLogMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.SecondHandCarUploadBaiduLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="service_id" jdbcType="VARCHAR" property="serviceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
    <result column="request_code" jdbcType="VARCHAR" property="requestCode" />
    <result column="request_status" jdbcType="INTEGER" property="requestStatus" />
    <result column="response_data" jdbcType="VARCHAR" property="responseData" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from second_hand_car_upload_baidu_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarUploadBaiduLog">
    insert into second_hand_car_upload_baidu_log (org_id, org_name,
      service_id, service_name, status, 
      request_url, request_data, request_code, 
      request_status, response_data)
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
      #{serviceId,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{requestUrl,jdbcType=VARCHAR}, #{requestData,jdbcType=VARCHAR}, #{requestCode,jdbcType=VARCHAR}, 
      #{requestStatus,jdbcType=INTEGER}, #{responseData,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarUploadBaiduLog">
    update second_hand_car_upload_baidu_log
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      request_data = #{requestData,jdbcType=VARCHAR},
      request_code = #{requestCode,jdbcType=VARCHAR},
      request_status = #{requestStatus,jdbcType=INTEGER},
      response_data = #{responseData,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, org_id, org_name, service_id, service_name, status, request_url, request_data, 
    request_code, request_status, response_data, Created_STime, Modified_STime, is_del
    from second_hand_car_upload_baidu_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, org_id, org_name, service_id, service_name, status, request_url, request_data, 
    request_code, request_status, response_data, Created_STime, Modified_STime, is_del
    from second_hand_car_upload_baidu_log
  </select>
</mapper>