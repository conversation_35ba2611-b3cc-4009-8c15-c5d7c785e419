<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.HistoryDouyinSitemapMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pagepath" jdbcType="VARCHAR" property="pagepath" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="created_stime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="modified_stime" jdbcType="TIMESTAMP" property="modifiedStime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pagepath, source, status, is_delete, created_stime, modified_stime
  </sql>
  <select id="selectByExample" parameterType="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from history_douyin_sitemap
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from history_douyin_sitemap
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from history_douyin_sitemap
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap">
    insert into history_douyin_sitemap (id, pagepath, source, 
      status, is_delete, created_stime, 
      modified_stime)
    values (#{id,jdbcType=BIGINT}, #{pagepath,jdbcType=VARCHAR}, #{source,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{createdStime,jdbcType=TIMESTAMP}, 
      #{modifiedStime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap">
    insert into history_douyin_sitemap
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pagepath != null">
        pagepath,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createdStime != null">
        created_stime,
      </if>
      <if test="modifiedStime != null">
        modified_stime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pagepath != null">
        #{pagepath,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createdStime != null">
        #{createdStime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedStime != null">
        #{modifiedStime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemapExample" resultType="java.lang.Long">
    select count(*) from history_douyin_sitemap
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap">
    update history_douyin_sitemap
    <set>
      <if test="pagepath != null">
        pagepath = #{pagepath,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="createdStime != null">
        created_stime = #{createdStime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedStime != null">
        modified_stime = #{modifiedStime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap">
    update history_douyin_sitemap
    set pagepath = #{pagepath,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=INTEGER},
      created_stime = #{createdStime,jdbcType=TIMESTAMP},
      modified_stime = #{modifiedStime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into history_douyin_sitemap (  pagepath, source,
    status, is_delete, created_stime,
    modified_stime)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      ( #{item.pagepath,jdbcType=VARCHAR}, #{item.source,jdbcType=INTEGER},
      #{item.status,jdbcType=INTEGER}, #{item.isDelete,jdbcType=INTEGER}, #{item.createdStime,jdbcType=TIMESTAMP},
      #{item.modifiedStime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>