<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.OpenUserAlipayMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.OpenUserAlipay">
        <id column="Id" jdbcType="INTEGER" property="id"/>
        <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime"/>
        <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime"/>
        <result column="OpenuserId" jdbcType="INTEGER" property="openuserid"/>
        <result column="AlipayAuthTime" jdbcType="TIMESTAMP" property="alipayauthtime"/>
        <result column="AlipayAppId" jdbcType="NVARCHAR" property="alipayappid"/>
        <result column="AlipayAuthToken" jdbcType="NVARCHAR" property="alipayauthtoken"/>
        <result column="AlipayRefreshToken" jdbcType="NVARCHAR" property="alipayrefreshtoken"/>
        <result column="is_del" jdbcType="TINYINT" property="isDel"/>
        <result column="AlipayUserId" jdbcType="VARCHAR" property="alipayuserid"/>
        <result column="AlipayAuthStatus" jdbcType="INTEGER" property="alipayauthstatus"/>
        <result column="CancelAuthTime" jdbcType="TIMESTAMP" property="cancelauthtime"/>
        <result column="PublishTime" jdbcType="TIMESTAMP" property="publishtime"/>
        <result column="FirstLicensePic" jdbcType="VARCHAR" property="firstlicensepic"/>
        <result column="SecondLicensePic" jdbcType="VARCHAR" property="secondlicensepic"/>
        <result column="RV" jdbcType="BINARY" property="rv"/>
        <result column="DecryptKey" jdbcType="NVARCHAR" property="decryptkey"/>
    </resultMap>
    <resultMap id="BaseResultMapSimpl" type="com.autohome.applet.dao.javaapi.model.OpenUserAlipaySimpl">
        <result column="FirstLicensePic" jdbcType="VARCHAR" property="firstlicensepic"/>
        <result column="SecondLicensePic" jdbcType="VARCHAR" property="secondlicensepic"/>
        <result column="DecryptKey" jdbcType="VARCHAR" property="decryptkey"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from OpenUserAlipay
        where Id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.OpenUserAlipay">
        insert into OpenUserAlipay (Id, Modified_STime, Created_STime,
                                    OpenuserId, AlipayAuthTime, AlipayAppId,
                                    AlipayAuthToken, AlipayRefreshToken,
                                    is_del, AlipayUserId, AlipayAuthStatus,
                                    CancelAuthTime, PublishTime, FirstLicensePic,
                                    SecondLicensePic, RV)
        values (#{id,jdbcType=INTEGER}, #{modifiedStime,jdbcType=TIMESTAMP}, #{createdStime,jdbcType=TIMESTAMP},
                #{openuserid,jdbcType=INTEGER}, #{alipayauthtime,jdbcType=TIMESTAMP}, #{alipayappid,jdbcType=NVARCHAR},
                #{alipayauthtoken,jdbcType=NVARCHAR}, #{alipayrefreshtoken,jdbcType=NVARCHAR},
                #{isDel,jdbcType=TINYINT}, #{alipayuserid,jdbcType=VARCHAR}, #{alipayauthstatus,jdbcType=INTEGER},
                #{cancelauthtime,jdbcType=TIMESTAMP}, #{publishtime,jdbcType=TIMESTAMP}, #{firstlicensepic,jdbcType=VARCHAR},
                #{secondlicensepic,jdbcType=VARCHAR}, #{rv,jdbcType=BINARY})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.OpenUserAlipay">
        update OpenUserAlipay
        set Modified_STime     = #{modifiedStime,jdbcType=TIMESTAMP},
            Created_STime      = #{createdStime,jdbcType=TIMESTAMP},
            OpenuserId         = #{openuserid,jdbcType=INTEGER},
            AlipayAuthTime     = #{alipayauthtime,jdbcType=TIMESTAMP},
            AlipayAppId        = #{alipayappid,jdbcType=NVARCHAR},
            AlipayAuthToken    = #{alipayauthtoken,jdbcType=NVARCHAR},
            AlipayRefreshToken = #{alipayrefreshtoken,jdbcType=NVARCHAR},
            is_del             = #{isDel,jdbcType=TINYINT},
            AlipayUserId       = #{alipayuserid,jdbcType=VARCHAR},
            AlipayAuthStatus   = #{alipayauthstatus,jdbcType=INTEGER},
            CancelAuthTime     = #{cancelauthtime,jdbcType=TIMESTAMP},
            PublishTime        = #{publishtime,jdbcType=TIMESTAMP},
            FirstLicensePic    = #{firstlicensepic,jdbcType=VARCHAR},
            SecondLicensePic   = #{secondlicensepic,jdbcType=VARCHAR},
            RV                 = #{rv,jdbcType=BINARY}
        where Id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select Id,
               Modified_STime,
               Created_STime,
               OpenuserId,
               AlipayAuthTime,
               AlipayAppId,
               AlipayAuthToken,
               AlipayRefreshToken,
               is_del,
               AlipayUserId,
               AlipayAuthStatus,
               CancelAuthTime,
               PublishTime,
               FirstLicensePic,
               SecondLicensePic,
               RV
        from OpenUserAlipay
        where Id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select Id,
               Modified_STime,
               Created_STime,
               OpenuserId,
               AlipayAuthTime,
               AlipayAppId,
               AlipayAuthToken,
               AlipayRefreshToken,
               is_del,
               AlipayUserId,
               AlipayAuthStatus,
               CancelAuthTime,
               PublishTime,
               FirstLicensePic,
               SecondLicensePic,
               RV
        from OpenUserAlipay
    </select>

    <update id="updateDecryptKey">
        update OpenUserAlipay
        set DecryptKey     = #{decryptKey,jdbcType=NVARCHAR},
            Modified_STime = GETDATE()
        where AlipayAppId = #{alipayappid,jdbcType=NVARCHAR}
    </update>

    <select id="getDecryptKeyById" resultType="String">
        SELECT T.DecryptKey
        FROM OpenUserAlipay T WITH (NOLOCK)
	        LEFT JOIN OpenUser u
        WITH (NOLOCK)
        ON T.OpenUserId = u.OpenUserId
        WHERE u.Id = #{id,jdbcType=INTEGER}
    </select>
</mapper>