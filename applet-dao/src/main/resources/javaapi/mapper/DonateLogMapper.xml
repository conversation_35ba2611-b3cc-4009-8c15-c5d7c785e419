<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.DonateLogMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.DonateLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="daily" jdbcType="VARCHAR" property="daily" />
    <result column="donate_type" jdbcType="INTEGER" property="donateType" />
    <result column="uniqueldentifier" jdbcType="VARCHAR" property="uniqueldentifier" />
    <result column="imageid" jdbcType="VARCHAR" property="imageid" />
    <result column="describe" jdbcType="VARCHAR" property="describe" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="key_words" jdbcType="VARCHAR" property="keyWords" />
    <result column="logo_url" jdbcType="VARCHAR" property="logoUrl" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="data_url" jdbcType="VARCHAR" property="dataUrl" />
    <result column="metadata_modification_date" jdbcType="TIMESTAMP" property="metadataModificationDate" />
    <result column="activity_type" jdbcType="VARCHAR" property="activityType" />
    <result column="ranking_hint" jdbcType="DECIMAL" property="rankingHint" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="view_mark" jdbcType="VARCHAR" property="viewMark" />
  </resultMap>
  <sql id="Base_Column_List">
    id, daily, donate_type, uniqueldentifier, imageid, describe, title, key_words,
    logo_url, group_id, data_url, metadata_modification_date, activity_type, ranking_hint, Created_STime, Modified_STime,
    is_del, view_mark
  </sql>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.DonateLog">
    insert into donate_log (id, daily, donate_type, 
      uniqueldentifier, imageid, describe, 
      title, key_words, logo_url, 
      group_id, data_url, metadata_modification_date, 
      activity_type, ranking_hint, Created_STime, 
      Modified_STime, is_del, view_mark)
    values (#{id,jdbcType=BIGINT}, #{daily,jdbcType=VARCHAR}, #{donateType,jdbcType=INTEGER}, 
      #{uniqueldentifier,jdbcType=VARCHAR}, #{imageid,jdbcType=VARCHAR}, #{describe,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{keyWords,jdbcType=VARCHAR}, #{logoUrl,jdbcType=VARCHAR}, 
      #{groupId,jdbcType=VARCHAR}, #{dataUrl,jdbcType=VARCHAR}, #{metadataModificationDate,jdbcType=TIMESTAMP}, 
      #{activityType,jdbcType=VARCHAR}, #{rankingHint,jdbcType=DECIMAL}, #{createdStime,jdbcType=TIMESTAMP}, 
      #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, #{viewMark,jdbcType=VARCHAR}
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.DonateLog">
    update donate_log
    set daily = #{daily,jdbcType=VARCHAR},
      donate_type = #{donateType,jdbcType=INTEGER},
      uniqueldentifier = #{uniqueldentifier,jdbcType=VARCHAR},
      imageid = #{imageid,jdbcType=VARCHAR},
      describe = #{describe,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      key_words = #{keyWords,jdbcType=VARCHAR},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
    data_url = #{dataUrl,jdbcType=VARCHAR},
    view_mark = #{viewMark,jdbcType=VARCHAR},
      metadata_modification_date = #{metadataModificationDate,jdbcType=TIMESTAMP},
      activity_type = #{activityType,jdbcType=VARCHAR},
      ranking_hint = #{rankingHint,jdbcType=DECIMAL},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
    is_del = #{isDel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, daily, donate_type, uniqueldentifier, imageid, describe, title, key_words, 
    logo_url, group_id, data_url, metadata_modification_date, activity_type, ranking_hint, 
    Created_STime, Modified_STime, is_del, view_mark
    from donate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, daily, donate_type, uniqueldentifier, imageid, describe, title, key_words, 
    logo_url, group_id, data_url, metadata_modification_date, activity_type, ranking_hint, 
    Created_STime, Modified_STime, is_del, view_mark
    from donate_log
  </select>
  <delete id="deleteBatch">
    delete from donate_log
    <where>
       and donate_type = #{donateType}
      <if test="daily != null and daily != ''">
        and daily = #{daily}
      </if>
      <if test="endDate != null and endDate != ''">
        and Created_STime &lt; #{endDate}
      </if>
    </where>
  </delete>
  <insert id="insertBatch" parameterType="com.autohome.applet.dao.javaapi.model.DonateLog">
    insert into donate_log (daily, donate_type,
    uniqueldentifier, imageid, describe,
    title, key_words, logo_url,
    group_id, data_url, metadata_modification_date,
    activity_type, ranking_hint, view_mark)
    values
    <foreach collection="donateLogList" item="item" index="index" separator=",">
      (#{item.daily,jdbcType=VARCHAR}, #{item.donateType,jdbcType=INTEGER},
      #{item.uniqueldentifier,jdbcType=VARCHAR}, #{item.imageid,jdbcType=VARCHAR}, #{item.describe,jdbcType=VARCHAR},
      #{item.title,jdbcType=VARCHAR}, #{item.keyWords,jdbcType=VARCHAR}, #{item.logoUrl,jdbcType=VARCHAR},
      #{item.groupId,jdbcType=VARCHAR}, #{item.dataUrl,jdbcType=VARCHAR}, #{item.metadataModificationDate,jdbcType=TIMESTAMP},
      #{item.activityType,jdbcType=VARCHAR}, #{item.rankingHint,jdbcType=DECIMAL}, #{item.viewMark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="countByQuery" parameterType="com.autohome.applet.dao.javaapi.model.donate.DonateDaoQuery" resultType="java.lang.Integer">
    select count(*) c
    from donate_log with(nolock)
    <where>
      donate_type = #{donateType}
      <if test="daily != null">
        and daily = #{daily,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.autohome.applet.dao.javaapi.model.donate.DonateDaoQuery" resultMap="BaseResultMap">
    select top ${pageSize}
    <include refid="Base_Column_List" />
    from donate_log with(nolock)
    <where>
      donate_type = #{donateType}
      <if test="searchAfter != null and searchAfter != 0">
        and id &lt; #{searchAfter}
      </if>
    </where>
    order by id desc
  </select>
</mapper>