<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.AppletUserbehaviorhistorySeriesMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.AppletUserbehaviorhistorySeries">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="hc_deviceid" jdbcType="BIGINT" property="hcDeviceid" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="view_mark" jdbcType="VARCHAR" property="viewMark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="series_id" jdbcType="INTEGER" property="seriesId" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="total" jdbcType="INTEGER" property="total" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="created_stime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="modified_stime" jdbcType="TIMESTAMP" property="modifiedStime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, hc_deviceid, device_id, platform, user_id, view_mark, create_time, series_id, 
    series_name, total, is_del, created_stime, modified_stime
  </sql>
  <select id="listByQuery" parameterType="com.autohome.applet.dao.javaapi.model.donate.DonateDaoQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from applet_userbehaviorhistory_series_#{tableIndex}
    <where>
      hc_deviceid = #{deviceHashId}
      <if test="deviceId != null and deviceId != '' ">
        and device_id = #{deviceId}
      </if>
      <if test="searchAfter != null and searchAfter != 0">
        and id &lt; #{searchAfter}
      </if>
    </where>
    order by id desc
    limit #{pageSize}
  </select>
</mapper>