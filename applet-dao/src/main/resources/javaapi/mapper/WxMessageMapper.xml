<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.WxMessageMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.wxmsg.WxMessage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="series_id" jdbcType="INTEGER" property="seriesId" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="spec_id" jdbcType="INTEGER" property="specId" />
    <result column="pm" jdbcType="INTEGER" property="pm" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.wxmsg.WxMessage">
    insert into wx_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="seriesName != null">
        series_name,
      </if>
      <if test="specId != null">
        spec_id,
      </if>
      <if test="pm != null">
        pm,
      </if>
      <if test="unionId != null">
        union_id
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId},
      </if>
      <if test="openId != null">
        #{openId},
      </if>
      <if test="bizType != null">
        #{bizType},
      </if>
      <if test="seriesId != null">
        #{seriesId},
      </if>
      <if test="seriesName != null">
        #{seriesName},
      </if>
      <if test="specId != null">
        #{specId},
      </if>
      <if test="pm != null">
        #{pm},
      </if>
      <if test="unionId != null">
        #{unionId}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.wxmsg.WxMessage">
    update wx_message
    set user_id = #{userId,jdbcType=INTEGER},
      open_id = #{openId,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=INTEGER},
        series_id = #{seriesId,jdbcType=INTEGER},
        series_name = #{seriesName,jdbcType=VARCHAR},
      spec_id = #{specId,jdbcType=INTEGER},
      pm = #{pm,jdbcType=INTEGER},
      union_id = #{unionId,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countWxMessage" parameterType="com.autohome.applet.dao.javaapi.model.wxmsg.WxMessageQuery" resultType="java.lang.Integer">
    select count(*) c
    from wx_message with(nolock)
    <where>
      status = 0
      AND open_id = #{openId}
      AND biz_type = #{bizType}
      AND Created_STime >= #{beginDate}
      AND Created_STime &lt; #{endDate}
    </where>
  </select>

  <select id="listWxMessage" resultMap="BaseResultMap">
    select id, user_id, open_id, biz_type, series_id, series_name, spec_id, pm, union_id, send_time,
           Created_STime, Modified_STime, is_del, status
    from wx_message with(nolock)
    <where>
      status = 0
      AND open_id = #{openId}
      AND biz_type = #{bizType}
      AND Created_STime >= #{beginDate}
      AND Created_STime &lt; #{endDate}
    </where>
  </select>
</mapper>