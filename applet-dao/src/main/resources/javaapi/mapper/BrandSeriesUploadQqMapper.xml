<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.BrandSeriesUploadQqMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="brand_id" jdbcType="INTEGER" property="brandId" />
    <result column="brand_id_encode" jdbcType="VARCHAR" property="brandIdEncode" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="series_id" jdbcType="INTEGER" property="seriesId" />
    <result column="series_id_encode" jdbcType="VARCHAR" property="seriesIdEncode" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="spec_id" jdbcType="VARCHAR" property="specId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="card_type" jdbcType="INTEGER" property="cardType" />
    <result column="card_status" jdbcType="INTEGER" property="cardStatus" />
    <result column="query" jdbcType="VARCHAR" property="query" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
    <result column="request_code" jdbcType="VARCHAR" property="requestCode" />
    <result column="request_status" jdbcType="INTEGER" property="requestStatus" />
    <result column="response_data" jdbcType="VARCHAR" property="responseData" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from brand_series_upload_qq
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq">
    insert into brand_series_upload_qq (id, brand_id, brand_id_encode, 
      brand_name, series_id, series_id_encode, 
      series_name, city_code, city_name, 
      card_type, card_status, query, 
      request_url, request_data, request_code, 
      request_status, response_data, Created_STime, 
      Modified_STime, is_del, spec_id)
    values (#{id,jdbcType=BIGINT}, #{brandId,jdbcType=INTEGER}, #{brandIdEncode,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{seriesId,jdbcType=INTEGER}, #{seriesIdEncode,jdbcType=VARCHAR}, 
      #{seriesName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=INTEGER}, #{cardStatus,jdbcType=INTEGER}, #{query,jdbcType=VARCHAR}, 
      #{requestUrl,jdbcType=VARCHAR}, #{requestData,jdbcType=VARCHAR}, #{requestCode,jdbcType=VARCHAR}, 
      #{requestStatus,jdbcType=INTEGER}, #{responseData,jdbcType=VARCHAR}, #{createdStime,jdbcType=TIMESTAMP}, 
      #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, #{specId,jdbcType=INTEGER})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq">
    update brand_series_upload_qq
    set brand_id = #{brandId,jdbcType=INTEGER},
      brand_id_encode = #{brandIdEncode,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      series_id = #{seriesId,jdbcType=INTEGER},
      series_id_encode = #{seriesIdEncode,jdbcType=VARCHAR},
      series_name = #{seriesName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=INTEGER},
      card_status = #{cardStatus,jdbcType=INTEGER},
      query = #{query,jdbcType=VARCHAR},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      request_data = #{requestData,jdbcType=VARCHAR},
      request_code = #{requestCode,jdbcType=VARCHAR},
      request_status = #{requestStatus,jdbcType=INTEGER},
      response_data = #{responseData,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      spec_id = #{specId,jdbcType=VARCHAR},
      is_del = #{isDel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimary" parameterType="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq">
    update brand_series_upload_qq
    set brand_id_encode = #{brandIdEncode,jdbcType=VARCHAR},
        brand_name = #{brandName,jdbcType=VARCHAR},
        series_id_encode = #{seriesIdEncode,jdbcType=VARCHAR},
        series_name = #{seriesName,jdbcType=VARCHAR},
        city_name = #{cityName,jdbcType=VARCHAR},
        card_status = #{cardStatus,jdbcType=INTEGER},
        query = #{query,jdbcType=VARCHAR},
        request_url = #{requestUrl,jdbcType=VARCHAR},
        request_data = #{requestData,jdbcType=VARCHAR},
        request_code = #{requestCode,jdbcType=VARCHAR},
        request_status = #{requestStatus,jdbcType=INTEGER},
        response_data = #{responseData,jdbcType=VARCHAR},
        Created_STime = #{createdStime,jdbcType=TIMESTAMP},
        Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
        is_del = #{isDel,jdbcType=INTEGER}
    where brand_id = #{brandId,jdbcType=INTEGER}
        AND series_id = #{seriesId,jdbcType=INTEGER}
        AND city_code = #{cityCode,jdbcType=VARCHAR}
        AND card_type = #{cardType,jdbcType=INTEGER}
        AND spec_id = #{specId,jdbcType=INTEGER}
  </update>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, brand_id, brand_id_encode, brand_name, series_id, series_id_encode, series_name, 
    city_code, city_name, card_type, card_status, query, request_url, request_data, request_code, 
    request_status, response_data, Created_STime, Modified_STime, is_del, spec_id
    from brand_series_upload_qq
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, brand_id, brand_id_encode, brand_name, series_id, series_id_encode, series_name, 
    city_code, city_name, card_type, card_status, query, request_url, request_data, request_code, 
    request_status, response_data, Created_STime, Modified_STime, is_del, spec_id
    from brand_series_upload_qq
  </select>

  <update id="save" parameterType="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq">
    MERGE
    INTO
    brand_series_upload_qq AS target
    USING (
    VALUES (#{brandId,jdbcType=INTEGER}, #{brandName,jdbcType=VARCHAR}, #{brandIdEncode,jdbcType=VARCHAR},
    #{seriesId,jdbcType=INTEGER}, #{seriesName,jdbcType=VARCHAR}, #{seriesIdEncode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
    #{cityName,jdbcType=VARCHAR}, #{cardType,jdbcType=INTEGER}, #{cardStatus,jdbcType=INTEGER},
    #{query,jdbcType=VARCHAR}, #{requestUrl,jdbcType=VARCHAR}, #{requestData,jdbcType=VARCHAR},
    #{requestCode,jdbcType=VARCHAR}, #{requestStatus,jdbcType=INTEGER}, #{responseData,jdbcType=VARCHAR}
    , #{modifiedStime,jdbcType=TIMESTAMP}, #{specId,jdbcType=INTEGER}
    )) AS source (brand_id, brand_name, brand_id_encode,
    series_id, series_name, series_id_encode, city_code,
    city_name, card_type, card_status,
    query, request_url, request_data,
    request_code, request_status, response_data,
    Modified_STime,spec_id
    )
    ON
    target.brand_id = source.brand_id
    AND target.series_id = source.series_id
    AND target.city_code = source.city_code
    AND target.card_type = source.card_type
    AND target.spec_id = source.spec_id
    WHEN MATCHED THEN
    UPDATE
    SET
    brand_id = source.brand_id
    , brand_name = source.brand_name
    , brand_id_encode = source.brand_id_encode
    ,series_id = source.series_id
    , series_name = source.series_name
    , series_id_encode = source.series_id_encode
    , city_code = source.city_code
    ,city_name = source.city_name
    , card_type = source.card_type
    , card_status = source.card_status
    ,query = source.query
    , request_url = source.request_url
    , request_data = source.request_data
    , request_code = source.request_code
    , request_status = source.request_status
    , response_data = source.response_data
    , Modified_STime = source.Modified_STime
        , spec_id = source.spec_id
    WHEN NOT MATCHED THEN
    INSERT
    (brand_id, brand_name, brand_id_encode,
    series_id, series_name, series_id_encode, city_code,
    city_name, card_type, card_status,
    query, request_url, request_data,
    request_code, request_status, response_data,
    Modified_STime , spec_id
    )
    VALUES (source.brand_id, source.brand_name, source.brand_id_encode,
    source.series_id, source.series_name, source.series_id_encode, source.city_code,
    source.city_name, source.card_type, source.card_status,
    source.query, source.request_url, source.request_data,
    source.request_code, source.request_status, source.response_data,
    source.Modified_STime,source.spec_id);
  </update>
</mapper>