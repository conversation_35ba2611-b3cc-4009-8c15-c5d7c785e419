<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.DouyinSitemapHistoryMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.DouyinSitemapHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pagepath" jdbcType="VARCHAR" property="pagepath" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="DBA_RV" jdbcType="TIMESTAMP" property="dbaRv" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="RV" jdbcType="BINARY" property="rv" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from douyin_sitemap_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.DouyinSitemapHistory">
    insert into douyin_sitemap_history (id, pagepath, Created_STime, 
      Modified_STime, is_del, status, 
      DBA_RV, source, RV)
    values (#{id,jdbcType=BIGINT}, #{pagepath,jdbcType=VARCHAR}, #{createdStime,jdbcType=TIMESTAMP}, 
      #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{dbaRv,jdbcType=TIMESTAMP}, #{source,jdbcType=INTEGER}, #{rv,jdbcType=BINARY})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.DouyinSitemapHistory">
    update douyin_sitemap_history
    set pagepath = #{pagepath,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      DBA_RV = #{dbaRv,jdbcType=TIMESTAMP},
      source = #{source,jdbcType=INTEGER},
      RV = #{rv,jdbcType=BINARY}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, pagepath, Created_STime, Modified_STime, is_del, status, DBA_RV, source, 
    RV
    from douyin_sitemap_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, pagepath, Created_STime, Modified_STime, is_del, status, DBA_RV, source, 
    RV
    from douyin_sitemap_history
  </select>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into douyin_sitemap_history ( pagepath, is_del, status,
    source )
    values
    <foreach collection="list" item="item" index="index" separator=",">
      ( #{item.pagepath,jdbcType=VARCHAR},  #{item.isDel,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},  #{item.source,jdbcType=INTEGER} )
    </foreach>
  </insert>
</mapper>