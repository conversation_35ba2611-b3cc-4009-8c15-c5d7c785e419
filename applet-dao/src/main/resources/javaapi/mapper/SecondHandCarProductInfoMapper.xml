<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SecondHandCarProductInfoMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_id" jdbcType="VARCHAR" property="serviceId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="tabs" jdbcType="VARCHAR" property="tabs" />
    <result column="info_url" jdbcType="VARCHAR" property="infoUrl" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="originalprice" jdbcType="VARCHAR" property="originalprice" />
    <result column="finalprice" jdbcType="VARCHAR" property="finalprice" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="car_name" jdbcType="VARCHAR" property="carName" />
    <result column="car_brand" jdbcType="VARCHAR" property="carBrand" />
    <result column="car_gear" jdbcType="VARCHAR" property="carGear" />
    <result column="car_volume" jdbcType="VARCHAR" property="carVolume" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="car_seat" jdbcType="VARCHAR" property="carSeat" />
    <result column="car_year" jdbcType="VARCHAR" property="carYear" />
    <result column="car_plate" jdbcType="VARCHAR" property="carPlate" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from second_hand_car_product_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo">
    insert into second_hand_car_product_info (id, service_id, name, 
      type, pic, tabs, info_url, 
      comment, description, originalprice, 
      finalprice, category, status, 
      org_id, car_name, car_brand, 
      car_gear, car_volume, car_type, 
      car_seat, car_year, car_plate, 
      Created_STime, Modified_STime, is_del, 
      version)
    values (#{id,jdbcType=BIGINT}, #{serviceId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{pic,jdbcType=VARCHAR}, #{tabs,jdbcType=VARCHAR}, #{infoUrl,jdbcType=VARCHAR}, 
      #{comment,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{originalprice,jdbcType=VARCHAR}, 
      #{finalprice,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{orgId,jdbcType=VARCHAR}, #{carName,jdbcType=VARCHAR}, #{carBrand,jdbcType=VARCHAR}, 
      #{carGear,jdbcType=VARCHAR}, #{carVolume,jdbcType=VARCHAR}, #{carType,jdbcType=VARCHAR}, 
      #{carSeat,jdbcType=VARCHAR}, #{carYear,jdbcType=VARCHAR}, #{carPlate,jdbcType=VARCHAR}, 
      #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, 
      #{version,jdbcType=BIGINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo">
    update second_hand_car_product_info
    set service_id = #{serviceId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      pic = #{pic,jdbcType=VARCHAR},
      tabs = #{tabs,jdbcType=VARCHAR},
      info_url = #{infoUrl,jdbcType=VARCHAR},
      comment = #{comment,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      originalprice = #{originalprice,jdbcType=VARCHAR},
      finalprice = #{finalprice,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=VARCHAR},
      car_name = #{carName,jdbcType=VARCHAR},
      car_brand = #{carBrand,jdbcType=VARCHAR},
      car_gear = #{carGear,jdbcType=VARCHAR},
      car_volume = #{carVolume,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=VARCHAR},
      car_seat = #{carSeat,jdbcType=VARCHAR},
      car_year = #{carYear,jdbcType=VARCHAR},
      car_plate = #{carPlate,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, service_id, name, type, pic, tabs, info_url, comment, description, originalprice, 
    finalprice, category, status, org_id, car_name, car_brand, car_gear, car_volume, 
    car_type, car_seat, car_year, car_plate, Created_STime, Modified_STime, is_del, version
    from second_hand_car_product_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, service_id, name, type, pic, tabs, info_url, comment, description, originalprice, 
    finalprice, category, status, org_id, car_name, car_brand, car_gear, car_volume, 
    car_type, car_seat, car_year, car_plate, Created_STime, Modified_STime, is_del, version
    from second_hand_car_product_info
  </select>

  <update id="save" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo">
    MERGE
    INTO
    second_hand_car_product_info AS target
    USING (
    VALUES (#{serviceId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
    #{type,jdbcType=INTEGER}, #{pic,jdbcType=VARCHAR}, #{tabs,jdbcType=VARCHAR}, #{infoUrl,jdbcType=VARCHAR},
    #{comment,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{originalprice,jdbcType=VARCHAR},
    #{finalprice,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
    #{orgId,jdbcType=VARCHAR}, #{carName,jdbcType=VARCHAR}, #{carBrand,jdbcType=VARCHAR},
    #{carGear,jdbcType=VARCHAR}, #{carVolume,jdbcType=VARCHAR}, #{carType,jdbcType=VARCHAR},
    #{carSeat,jdbcType=VARCHAR}, #{carYear,jdbcType=VARCHAR}, #{carPlate,jdbcType=VARCHAR},
    #{modifiedStime,jdbcType=TIMESTAMP}, #{version,jdbcType=BIGINT}
    )) AS source (service_id,
    name,
    type,
    pic,
    tabs,
    info_url,
    comment,
    description,
    originalprice,
    finalprice,
    category,
    status,
    org_id,
    car_name,
    car_brand,
    car_gear,
    car_volume,
    car_type,
    car_seat,
    car_year,
    car_plate,
    Modified_STime,
    version)
    ON
    target.service_id = source.service_id
    WHEN MATCHED THEN
    UPDATE
    SET
    service_id=source.service_id,
    name=source.name,
    type=source.type,
    pic=source.pic,
    tabs=source.tabs,
    info_url=source.info_url,
    comment=source.comment,
    description=source.description,
    originalprice=source.originalprice,
    finalprice=source.finalprice,
    category=source.category,
    status=source.status,
    org_id=source.org_id,
    car_name=source.car_name,
    car_brand=source.car_brand,
    car_gear=source.car_gear,
    car_volume=source.car_volume,
    car_type=source.car_type,
    car_seat=source.car_seat,
    car_year=source.car_year,
    car_plate=source.car_plate,
    Modified_STime=source.Modified_STime,
    version=source.version
    WHEN NOT MATCHED THEN
    INSERT
    (service_id,
    name,
    type,
    pic,
    tabs,
    info_url,
    comment,
    description,
    originalprice,
    finalprice,
    category,
    status,
    org_id,
    car_name,
    car_brand,
    car_gear,
    car_volume,
    car_type,
    car_seat,
    car_year,
    car_plate,
    Modified_STime,
    version
    )
    VALUES (service_id,
    source.name,
    source.type,
    source.pic,
    source.tabs,
    source.info_url,
    source.comment,
    source.description,
    source.originalprice,
    source.finalprice,
    source.category,
    source.status,
    source.org_id,
    source.car_name,
    source.car_brand,
    source.car_gear,
    source.car_volume,
    source.car_type,
    source.car_seat,
    source.car_year,
    source.car_plate,
    source.Modified_STime,
    source.version);
  </update>
  <update id="updateVersionByServiceIds">
    update second_hand_car_product_info
    set version = #{version},
    Modified_STime = getdate()
    where service_id in
    <foreach collection="serviceIds" item="serviceId" index="index" open="(" close=")" separator=",">
      #{serviceId}
    </foreach>
  </update>
  <select id="listByVersion" resultMap="BaseResultMap">
    select top 2000 *
    from second_hand_car_product_info WITH(NOLOCK)
    where org_id=#{orgId} and version > #{version} and name != '' and finalprice != '' and pic != ''
    order by version, Modified_STime
  </select>
  <select id="countMoreThanByVersion" resultType="java.lang.Integer">
    select count(service_id)
    from second_hand_car_product_info WITH(NOLOCK)
    where org_id=#{orgId} and version > #{version}
  </select>
  <update id="updateStatusByServiceId">
    update second_hand_car_product_info
    set
    status = #{status},
    Modified_STime = getdate(),
    version = #{version}
    where service_id = #{serviceId}
  </update>
</mapper>