<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.ShortVideoPush2345LogMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.ShortVideoPush2345Log">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="author_ids" jdbcType="VARCHAR" property="authorIds" />
    <result column="biz_ids" jdbcType="VARCHAR" property="bizIds" />
    <result column="main_data_type" jdbcType="VARCHAR" property="mainDataType" />
    <result column="push_type" jdbcType="VARCHAR" property="pushType" />
    <result column="syn_begin_time" jdbcType="VARCHAR" property="synBeginTime" />
    <result column="syn_end_time" jdbcType="VARCHAR" property="synEndTime" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
    <result column="request_code" jdbcType="VARCHAR" property="requestCode" />
    <result column="request_status" jdbcType="INTEGER" property="requestStatus" />
    <result column="response_data" jdbcType="VARCHAR" property="responseData" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from short_video_push_2345_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.autohome.applet.dao.javaapi.model.ShortVideoPush2345Log">
    insert into short_video_push_2345_log (author_ids, biz_ids,
      main_data_type, push_type, syn_begin_time, 
      syn_end_time, request_url, request_data
      )
    values (#{authorIds,jdbcType=VARCHAR}, #{bizIds,jdbcType=VARCHAR},
      #{mainDataType,jdbcType=VARCHAR}, #{pushType,jdbcType=VARCHAR}, #{synBeginTime,jdbcType=VARCHAR}, 
      #{synEndTime,jdbcType=VARCHAR}, #{requestUrl,jdbcType=VARCHAR}, #{requestData,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.ShortVideoPush2345Log">
    update short_video_push_2345_log
    set
      request_code = #{requestCode,jdbcType=VARCHAR},
      request_status = #{requestStatus,jdbcType=INTEGER},
      response_data = #{responseData,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, author_ids, biz_ids, main_data_type, push_type, syn_begin_time, syn_end_time, 
    request_url, request_data, request_code, request_status, response_data, Created_STime, 
    Modified_STime, is_del
    from short_video_push_2345_log with(nolock)
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, author_ids, biz_ids, main_data_type, push_type, syn_begin_time, syn_end_time, 
    request_url, request_data, request_code, request_status, response_data, Created_STime, 
    Modified_STime, is_del
    from short_video_push_2345_log with(nolock)
  </select>
</mapper>