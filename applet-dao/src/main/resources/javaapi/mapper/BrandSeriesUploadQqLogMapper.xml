<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.BrandSeriesUploadQqLogMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQqLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="brand_id" jdbcType="INTEGER" property="brandId" />
    <result column="brand_id_encode" jdbcType="VARCHAR" property="brandIdEncode" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="series_id" jdbcType="INTEGER" property="seriesId" />
    <result column="series_id_encode" jdbcType="VARCHAR" property="seriesIdEncode" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="card_type" jdbcType="INTEGER" property="cardType" />
    <result column="card_status" jdbcType="INTEGER" property="cardStatus" />
    <result column="query" jdbcType="VARCHAR" property="query" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_data" jdbcType="VARCHAR" property="requestData" />
    <result column="request_code" jdbcType="VARCHAR" property="requestCode" />
    <result column="request_status" jdbcType="INTEGER" property="requestStatus" />
    <result column="response_data" jdbcType="VARCHAR" property="responseData" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="spec_id" jdbcType="INTEGER" property="specId" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from brand_series_upload_qq_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQqLog">
    insert into brand_series_upload_qq_log (brand_id, brand_id_encode,
      brand_name, series_id, series_id_encode, 
      series_name, city_code, city_name, 
      card_type, card_status, query, 
      request_url, request_data, request_code, 
      request_status, response_data, spec_id)
    values (#{brandId,jdbcType=INTEGER}, #{brandIdEncode,jdbcType=VARCHAR},
      #{brandName,jdbcType=VARCHAR}, #{seriesId,jdbcType=INTEGER}, #{seriesIdEncode,jdbcType=VARCHAR}, 
      #{seriesName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=INTEGER}, #{cardStatus,jdbcType=INTEGER}, #{query,jdbcType=VARCHAR}, 
      #{requestUrl,jdbcType=VARCHAR}, #{requestData,jdbcType=VARCHAR}, #{requestCode,jdbcType=VARCHAR}, 
      #{requestStatus,jdbcType=INTEGER}, #{responseData,jdbcType=VARCHAR},#{specId,jdbcType=INTEGER})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQqLog">
    update brand_series_upload_qq_log
    set brand_id = #{brandId,jdbcType=INTEGER},
      brand_id_encode = #{brandIdEncode,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      series_id = #{seriesId,jdbcType=INTEGER},
      series_id_encode = #{seriesIdEncode,jdbcType=VARCHAR},
      series_name = #{seriesName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=INTEGER},
      card_status = #{cardStatus,jdbcType=INTEGER},
      query = #{query,jdbcType=VARCHAR},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      request_data = #{requestData,jdbcType=VARCHAR},
      request_code = #{requestCode,jdbcType=VARCHAR},
      request_status = #{requestStatus,jdbcType=INTEGER},
      response_data = #{responseData,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      spec_id = #{specId,jdbcType=INTEGER},
      is_del = #{isDel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, brand_id, brand_id_encode, brand_name, series_id, series_id_encode, series_name, 
    city_code, city_name, card_type, card_status, query, request_url, request_data, request_code, 
    request_status, response_data, Created_STime, Modified_STime, is_del,spec_id
    from brand_series_upload_qq_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, brand_id, brand_id_encode, brand_name, series_id, series_id_encode, series_name, 
    city_code, city_name, card_type, card_status, query, request_url, request_data, request_code, 
    request_status, response_data, Created_STime, Modified_STime, is_del,spec_id
    from brand_series_upload_qq_log
  </select>
</mapper>