<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.Clue4Che168Mapper">

    <insert id="add"  parameterType="com.autohome.applet.dao.javaapi.model.Clue4Che168">
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
            SELECT @@IDENTITY
        </selectKey>
        insert into Clue4Che168 (KeyPhone, KeyName, keyUsedCarBusinessId, keyUserId, keyClientOrderIp,
        keyInsideLinkidId, keyOutsidePvareaidId, RuleengineStatus, RuleengineResult,
        PushId, PushStatus, <PERSON>ush<PERSON><PERSON>ult, <PERSON>ushD<PERSON>, Created_STime, Modified_STime,
        is_del, Push<PERSON><PERSON><PERSON>ey, KeyUsedCarSourceId, SourceId, KeySupplyBusinessId,
        KeyOrderCityid)
        VALUES (#{keyPhone}, #{keyName}, #{keyUsedCarBusinessId}, #{keyUserId}, #{keyClientOrderIp},
        #{keyInsideLinkidId}, #{keyOutsidePvareaidId}, #{ruleengineStatus}, #{ruleengineResult}, #{pushId},
        #{pushStatus}, #{pushResult}, #{pushData}, GETDATE(), GETDATE(), #{isDel}, #{pushAppKey},
        #{keyUsedCarSourceId}, #{sourceId}, #{keySupplyBusinessId}, #{keyOrderCityid})
    </insert>


    <update id="update" parameterType="com.autohome.applet.dao.javaapi.model.Clue4Che168">
        update Clue4Che168
        SET KeyPhone             = #{keyPhone}
          , KeyName              = #{keyName}
          , keyUsedCarBusinessId = #{keyUsedCarBusinessId}
          , keyUserId            = #{keyUserId}
          , keyClientOrderIp     = #{keyClientOrderIp}
          , keyInsideLinkidId    = #{keyInsideLinkidId}
          , keyOutsidePvareaidId = #{keyOutsidePvareaidId}
          , RuleengineStatus     = #{ruleengineStatus}
          , RuleengineResult     = #{ruleengineResult}
          , PushId               = #{pushId}
          , PushStatus           = #{pushStatus}
          , PushResult           = #{pushResult}
          , PushData             = #{pushData}
          , Modified_STime       = GETDATE()
          , is_del               = #{isDel}
        where Id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
