<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.ResourceFeedMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.ResourceFeed">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="publish_time" jdbcType="VARCHAR" property="publishTime" />
    <result column="created_stime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="modified_stime" jdbcType="TIMESTAMP" property="modifiedStime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.autohome.applet.dao.javaapi.model.ResourceFeedWithBLOBs">
    <result column="title" jdbcType="LONGVARCHAR" property="title" />
    <result column="author" jdbcType="LONGVARCHAR" property="author" />
    <result column="author_id" jdbcType="LONGVARCHAR" property="authorId" />
    <result column="img_url" jdbcType="LONGVARCHAR" property="imgUrl" />
    <result column="modify_time" jdbcType="LONGVARCHAR" property="modifyTime" />
    <result column="summary" jdbcType="LONGVARCHAR" property="summary" />
    <result column="reply_count" jdbcType="LONGVARCHAR" property="replyCount" />
    <result column="view_count" jdbcType="LONGVARCHAR" property="viewCount" />
    <result column="is_delete" jdbcType="LONGVARCHAR" property="isDelete" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="subject_id" jdbcType="LONGVARCHAR" property="subjectId" />
    <result column="cms_series_ids" jdbcType="LONGVARCHAR" property="cmsSeriesIds" />
    <result column="cms_content_class" jdbcType="LONGVARCHAR" property="cmsContentClass" />
    <result column="cms_tags" jdbcType="LONGVARCHAR" property="cmsTags" />
    <result column="category_tags" jdbcType="LONGVARCHAR" property="categoryTags" />
    <result column="recommend_time" jdbcType="LONGVARCHAR" property="recommendTime" />
    <result column="duration" jdbcType="LONGVARCHAR" property="duration" />
    <result column="graphic_img_list" jdbcType="LONGVARCHAR" property="graphicImgList" />
    <result column="update_at" jdbcType="LONGVARCHAR" property="updateAt" />
    <result column="author_icon" jdbcType="LONGVARCHAR" property="authorIcon" />
    <result column="v_id" jdbcType="LONGVARCHAR" property="vId" />
    <result column="cms_series_names" jdbcType="LONGVARCHAR" property="cmsSeriesNames" />
    <result column="nlp_tags_choose2" jdbcType="LONGVARCHAR" property="nlpTagsChoose2" />
    <result column="is_close_comment" jdbcType="LONGVARCHAR" property="isCloseComment" />
    <result column="graphic_img_list3" jdbcType="LONGVARCHAR" property="graphicImgList3" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_id, biz_type, publish_time, created_stime, modified_stime
  </sql>
  <sql id="Blob_Column_List">
    title, author, author_id, img_url, modify_time, summary, reply_count, view_count, 
    is_delete, content, subject_id, cms_series_ids, cms_content_class, cms_tags, category_tags, 
    recommend_time, duration, graphic_img_list, update_at, author_icon, v_id, cms_series_names, 
    nlp_tags_choose2, is_close_comment, graphic_img_list3
  </sql>
  <select id="listResourceFeedWithBLOBs" parameterType="com.autohome.applet.dao.javaapi.model.query.ResourceFeedPushDataQuery" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from resource_feed
    <where>
      Publish_time >= #{starttime}
        AND Publish_time &lt; #{endtime}
      <if test="bizTypeList != null">
        AND biz_type IN
        <foreach collection="bizTypeList" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by Publish_time asc, id desc
    limit #{count} offset #{offset}
  </select>

  <select id="countResourceFeedWithBLOBs" parameterType="com.autohome.applet.dao.javaapi.model.query.ResourceFeedPushDataQuery" resultType="com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO">
    select
    count(*) count, biz_type bizType
    from resource_feed
    <where>
      Publish_time >= #{starttime}
      AND Publish_time &lt; #{endtime}
      <if test="bizTypeList != null">
        AND biz_type IN
        <foreach collection="bizTypeList" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </where>
    group by biz_type
  </select>
</mapper>