<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.NewCarEventMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.NewCarEvent">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dbid" jdbcType="BIGINT" property="dbid" />
    <result column="objid" jdbcType="INTEGER" property="objid" />
    <result column="objtype" jdbcType="INTEGER" property="objtype" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="imgurl" jdbcType="VARCHAR" property="imgurl" />
    <result column="publishtime" jdbcType="TIMESTAMP" property="publishtime" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="authorid" jdbcType="BIGINT" property="authorid" />
    <result column="authorname" jdbcType="VARCHAR" property="authorname" />
    <result column="headimg" jdbcType="VARCHAR" property="headimg" />
    <result column="indexdetail" jdbcType="VARCHAR" property="indexdetail" />
    <result column="replycount" jdbcType="BIGINT" property="replycount" />
    <result column="playcount" jdbcType="BIGINT" property="playcount" />
    <result column="likecount" jdbcType="BIGINT" property="likecount" />
    <result column="viewcount" jdbcType="BIGINT" property="viewcount" />
    <result column="jumpurl" jdbcType="VARCHAR" property="jumpurl" />
    <result column="imglist" jdbcType="VARCHAR" property="imglist" />
    <result column="closecomment" jdbcType="TINYINT" property="closecomment" />
    <result column="videosource" jdbcType="VARCHAR" property="videosource" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="seriesids" jdbcType="VARCHAR" property="seriesids" />
    <result column="seriesnames" jdbcType="VARCHAR" property="seriesnames" />
    <result column="direction" jdbcType="TINYINT" property="direction" />
    <result column="cmsrefine" jdbcType="TINYINT" property="cmsrefine" />
    <result column="extdata" jdbcType="VARCHAR" property="extdata" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
    <result column="created_stime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="modified_stime" jdbcType="TIMESTAMP" property="modifiedStime" />
  </resultMap>
  <delete id="deleteBatch" parameterType="com.autohome.applet.dao.javaapi.model.query.NewCarEventQuery">
    delete from newcarevent
    <where>
      id IN
      <foreach collection="idList" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </where>
  </delete>
  <insert id="insertBatch" parameterType="com.autohome.applet.dao.javaapi.model.NewCarEvent">
    insert into newcarevent (dbid, objid,
      objtype, title, imgurl, 
      publishtime, updatetime, authorid, 
      authorname, headimg, indexdetail, 
      replycount, playcount, likecount, 
      viewcount, jumpurl, imglist, 
      closecomment, videosource, duration,
    seriesids, seriesnames, direction, cmsrefine,
      extdata)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      ( #{item.dbid,jdbcType=BIGINT}, #{item.objid,jdbcType=INTEGER},
      #{item.objtype,jdbcType=INTEGER}, #{item.title,jdbcType=VARCHAR}, #{item.imgurl,jdbcType=VARCHAR},
      #{item.publishtime,jdbcType=TIMESTAMP}, #{item.updatetime,jdbcType=TIMESTAMP}, #{item.authorid,jdbcType=BIGINT},
      #{item.authorname,jdbcType=VARCHAR}, #{item.headimg,jdbcType=VARCHAR}, #{item.indexdetail,jdbcType=VARCHAR},
      #{item.replycount,jdbcType=BIGINT}, #{item.playcount,jdbcType=BIGINT}, #{item.likecount,jdbcType=BIGINT},
      #{item.viewcount,jdbcType=BIGINT}, #{item.jumpurl,jdbcType=VARCHAR}, #{item.imglist,jdbcType=VARCHAR},
      #{item.closecomment,jdbcType=TINYINT}, #{item.videosource,jdbcType=VARCHAR}, #{item.duration,jdbcType=INTEGER},
      #{item.seriesids,jdbcType=VARCHAR}, #{item.seriesnames,jdbcType=VARCHAR}, #{item.direction,jdbcType=TINYINT}, #{item.cmsrefine,jdbcType=TINYINT},
      #{item.extdata,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="updateBatch" parameterType="com.autohome.applet.dao.javaapi.model.NewCarEvent">
    <foreach collection="list" item="item" index="index" separator=";">
      update newcarevent
      set dbid = #{item.dbid,jdbcType=BIGINT},
      objid = #{item.objid,jdbcType=INTEGER},
      objtype = #{item.objtype,jdbcType=INTEGER},
      title = #{item.title,jdbcType=VARCHAR},
      imgurl = #{item.imgurl,jdbcType=VARCHAR},
      publishtime = #{item.publishtime,jdbcType=TIMESTAMP},
      updatetime = #{item.updatetime,jdbcType=TIMESTAMP},
      authorid = #{item.authorid,jdbcType=BIGINT},
      authorname = #{item.authorname,jdbcType=VARCHAR},
      headimg = #{item.headimg,jdbcType=VARCHAR},
      indexdetail = #{item.indexdetail,jdbcType=VARCHAR},
      replycount = #{item.replycount,jdbcType=BIGINT},
      playcount = #{item.playcount,jdbcType=BIGINT},
      likecount = #{item.likecount,jdbcType=BIGINT},
      viewcount = #{item.viewcount,jdbcType=BIGINT},
      jumpurl = #{item.jumpurl,jdbcType=VARCHAR},
      imglist = #{item.imglist,jdbcType=VARCHAR},
      closecomment = #{item.closecomment,jdbcType=TINYINT},
      videosource = #{item.videosource,jdbcType=VARCHAR},
      duration = #{item.duration,jdbcType=INTEGER},
      seriesids = #{item.seriesids,jdbcType=VARCHAR},
      seriesnames = #{item.seriesnames,jdbcType=VARCHAR},
      direction = #{item.direction,jdbcType=TINYINT},
      cmsrefine = #{item.cmsrefine,jdbcType=TINYINT},
      extdata = #{item.extdata,jdbcType=VARCHAR},
      is_del = #{item.isDel,jdbcType=TINYINT},
      modified_stime = #{item.modifiedStime,jdbcType=TIMESTAMP}
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <select id="getByDbId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, dbid, objid, objtype, title, imgurl, publishtime, updatetime, authorid,
           authorname, headimg, indexdetail, replycount, playcount, likecount, viewcount, jumpurl,
           imglist, closecomment, videosource, duration, seriesids, seriesnames, direction, cmsrefine, extdata,
           is_del, created_stime, modified_stime
    from newcarevent(nolock)
    where dbid = #{dbid}
  </select>
  <select id="getByQuery" parameterType="com.autohome.applet.dao.javaapi.model.query.NewCarEventQuery" resultMap="BaseResultMap">
    select id, dbid, objid, objtype, title, imgurl, publishtime, updatetime, authorid,
           authorname, headimg, indexdetail, replycount, playcount, likecount, viewcount, jumpurl,
           imglist, closecomment, videosource, duration, seriesids, seriesnames, direction, cmsrefine, extdata,
           is_del, created_stime, modified_stime
    from newcarevent(nolock)
    <where>
      1=1
      <if test="objid != null and objid > 0">
        and objid = #{objid}
      </if>
      <if test="objtype != null and objtype > 0">
        and objtype = #{objtype}
      </if>
      <if test="seriesid != null">
        and seriesids like #{seriesid}
      </if>
      <if test="publishDate != null">
        and publishtime > #{publishDate}
      </if>
    </where>
  </select>
</mapper>