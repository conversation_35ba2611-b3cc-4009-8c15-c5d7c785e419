<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.WxServiceAccountMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.WxServiceAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fwh_open_id" jdbcType="VARCHAR" property="fwhOpenId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_service_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.WxServiceAccount">
    insert into wx_service_account (  fwh_open_id, union_id,
      Created_STime, Modified_STime, is_del
      )
    values (  #{fwhOpenId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR},
      #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.WxServiceAccount">
    update wx_service_account
    set fwh_open_id = #{fwhOpenId,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, fwh_open_id, union_id, Created_STime, Modified_STime, is_del
    from wx_service_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, fwh_open_id, union_id, Created_STime, Modified_STime, is_del
    from wx_service_account
  </select>

  <select id="getWxServiceAccountByUnionId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select id, fwh_open_id, union_id, Created_STime, Modified_STime, is_del
    from wx_service_account with (nolock)
    where union_id = #{unionId}
  </select>

</mapper>