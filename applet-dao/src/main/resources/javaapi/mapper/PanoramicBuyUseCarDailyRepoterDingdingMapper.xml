<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.PanoramicBuyUseCarDailyRepoterDingdingMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.PanoramicBuyUseCarDailyRepoterDingding">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
    <result column="total_flow_sum" jdbcType="VARCHAR" property="totalFlowSum" />
    <result column="total_flow_chainRatio" jdbcType="VARCHAR" property="totalFlowChainratio" />
    <result column="total_flow_yearOnYear" jdbcType="VARCHAR" property="totalFlowYearonyear" />
    <result column="total_flow_monthAvg" jdbcType="VARCHAR" property="totalFlowMonthavg" />
    <result column="total_lead_sum" jdbcType="VARCHAR" property="totalLeadSum" />
    <result column="total_lead_chainRatio" jdbcType="VARCHAR" property="totalLeadChainratio" />
    <result column="total_lead_chainRatioDiff" jdbcType="VARCHAR" property="totalLeadChainratiodiff" />
    <result column="total_lead_yearOnYear" jdbcType="VARCHAR" property="totalLeadYearonyear" />
    <result column="total_lead_yearOnYearDiff" jdbcType="VARCHAR" property="totalLeadYearonyeardiff" />
    <result column="total_lead_monthAvg" jdbcType="VARCHAR" property="totalLeadMonthavg" />
    <result column="total_leadRate_sum" jdbcType="VARCHAR" property="totalLeadrateSum" />
    <result column="total_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="totalLeadrateChainratiodiff" />
    <result column="total_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="totalLeadrateYearonyeardiff" />
    <result column="total_leadRate_monthAvg" jdbcType="VARCHAR" property="totalLeadrateMonthavg" />
    <result column="miniprogram_flow_sum" jdbcType="VARCHAR" property="miniprogramFlowSum" />
    <result column="miniprogram_flow_chainRatio" jdbcType="VARCHAR" property="miniprogramFlowChainratio" />
    <result column="miniprogram_flow_yearOnYear" jdbcType="VARCHAR" property="miniprogramFlowYearonyear" />
    <result column="miniprogram_flow_monthAvg" jdbcType="VARCHAR" property="miniprogramFlowMonthavg" />
    <result column="miniprogram_lead_sum" jdbcType="VARCHAR" property="miniprogramLeadSum" />
    <result column="miniprogram_lead_chainRatio" jdbcType="VARCHAR" property="miniprogramLeadChainratio" />
    <result column="miniprogram_lead_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramLeadChainratiodiff" />
    <result column="miniprogram_lead_yearOnYear" jdbcType="VARCHAR" property="miniprogramLeadYearonyear" />
    <result column="miniprogram_lead_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramLeadYearonyeardiff" />
    <result column="miniprogram_lead_monthAvg" jdbcType="VARCHAR" property="miniprogramLeadMonthavg" />
    <result column="miniprogram_leadRate_sum" jdbcType="VARCHAR" property="miniprogramLeadrateSum" />
    <result column="miniprogram_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramLeadrateChainratiodiff" />
    <result column="miniprogram_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramLeadrateYearonyeardiff" />
    <result column="miniprogram_leadRate_monthAvg" jdbcType="VARCHAR" property="miniprogramLeadrateMonthavg" />
    <result column="gaode_flow_sum" jdbcType="VARCHAR" property="gaodeFlowSum" />
    <result column="gaode_flow_chainRatio" jdbcType="VARCHAR" property="gaodeFlowChainratio" />
    <result column="gaode_flow_yearOnYear" jdbcType="VARCHAR" property="gaodeFlowYearonyear" />
    <result column="gaode_flow_monthAvg" jdbcType="VARCHAR" property="gaodeFlowMonthavg" />
    <result column="gaode_lead_sum" jdbcType="VARCHAR" property="gaodeLeadSum" />
    <result column="gaode_lead_chainRatio" jdbcType="VARCHAR" property="gaodeLeadChainratio" />
    <result column="gaode_lead_chainRatioDiff" jdbcType="VARCHAR" property="gaodeLeadChainratiodiff" />
    <result column="gaode_lead_yearOnYear" jdbcType="VARCHAR" property="gaodeLeadYearonyear" />
    <result column="gaode_lead_yearOnYearDiff" jdbcType="VARCHAR" property="gaodeLeadYearonyeardiff" />
    <result column="gaode_lead_monthAvg" jdbcType="VARCHAR" property="gaodeLeadMonthavg" />
    <result column="gaode_leadRate_sum" jdbcType="VARCHAR" property="gaodeLeadrateSum" />
    <result column="gaode_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="gaodeLeadrateChainratiodiff" />
    <result column="gaode_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="gaodeLeadrateYearonyeardiff" />
    <result column="gaode_leadRate_monthAvg" jdbcType="VARCHAR" property="gaodeLeadrateMonthavg" />
    <result column="default_0" jdbcType="VARCHAR" property="default0" />
    <result column="default_1" jdbcType="VARCHAR" property="default1" />
    <result column="default_2" jdbcType="VARCHAR" property="default2" />
    <result column="default_3" jdbcType="VARCHAR" property="default3" />
    <result column="default_4" jdbcType="VARCHAR" property="default4" />
    <result column="default_5" jdbcType="VARCHAR" property="default5" />
    <result column="default_6" jdbcType="VARCHAR" property="default6" />
    <result column="default_7" jdbcType="VARCHAR" property="default7" />
    <result column="default_8" jdbcType="VARCHAR" property="default8" />
    <result column="default_9" jdbcType="VARCHAR" property="default9" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="miniprogram_phone_sum" jdbcType="VARCHAR" property="miniprogramPhoneSum"/>
    <result column="miniprogram_phone_chainRatio" jdbcType="VARCHAR" property="miniprogramPhoneChainRatio"/>
    <result column="miniprogram_phone_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramPhoneChainRatioDiff"/>
    <result column="miniprogram_phone_yearOnYear" jdbcType="VARCHAR" property="miniprogramPhoneYearOnYear"/>
    <result column="miniprogram_phone_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramPhoneYearOnYearDiff"/>
    <result column="miniprogram_phone_monthAvg" jdbcType="VARCHAR" property="miniprogramPhoneMonthAvg"/>
    <result column="miniprogram_flow_phone_rate" jdbcType="VARCHAR" property="miniprogramFlowPhoneRate"/>
    <result column="miniprogram_flowphoneRate_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramFlowphoneRateChainRatioDiff"/>
    <result column="miniprogram_flowphoneRate_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramFlowphoneRateYearOnYearDiff"/>
    <result column="miniprogram_flowphoneRate_monthAvg" jdbcType="VARCHAR" property="miniprogramFlowphoneRateMonthAvg"/>
    <result column="miniprogram_lead_phone_rate" jdbcType="VARCHAR" property="miniprogramLeadPhoneRate"/>
    <result column="miniprogram_leadphoneRate_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramLeadphoneRateChainRatioDiff"/>
    <result column="miniprogram_leadphoneRate_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramLeadphoneRateYearOnYearDiff"/>
    <result column="miniprogram_leadphoneRate_monthAvg" jdbcType="VARCHAR" property="miniprogramLeadphoneRateMonthAvg"/>
    <result column="cooperation_flow_sum" jdbcType="VARCHAR" property="cooperationFlowSum"/>
    <result column="cooperation_flow_chainRatio" jdbcType="VARCHAR" property="cooperationFlowChainRatio"/>
    <result column="cooperation_flow_yearOnYear" jdbcType="VARCHAR" property="cooperationFlowYearOnYear"/>
    <result column="cooperation_flow_monthAvg" jdbcType="VARCHAR" property="cooperationFlowMonthAvg"/>
    <result column="cooperation_lead_sum" jdbcType="VARCHAR" property="cooperationLeadSum"/>
    <result column="cooperation_lead_chainRatio" jdbcType="VARCHAR" property="cooperationLeadChainRatio"/>
    <result column="cooperation_lead_chainRatioDiff" jdbcType="VARCHAR" property="cooperationLeadChainRatioDiff"/>
    <result column="cooperation_lead_yearOnYear" jdbcType="VARCHAR" property="cooperationLeadYearOnYear"/>
    <result column="cooperation_lead_yearOnYearDiff" jdbcType="VARCHAR" property="cooperationLeadYearOnYearDiff"/>
    <result column="cooperation_lead_monthAvg" jdbcType="VARCHAR" property="cooperationLeadMonthAvg"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, dt, total_flow_sum, total_flow_chainRatio, total_flow_yearOnYear, total_flow_monthAvg, 
    total_lead_sum, total_lead_chainRatio, total_lead_chainRatioDiff, total_lead_yearOnYear, 
    total_lead_yearOnYearDiff, total_lead_monthAvg, total_leadRate_sum, total_leadRate_chainRatioDiff, 
    total_leadRate_yearOnYearDiff, total_leadRate_monthAvg, miniprogram_flow_sum, miniprogram_flow_chainRatio, 
    miniprogram_flow_yearOnYear, miniprogram_flow_monthAvg, miniprogram_lead_sum, miniprogram_lead_chainRatio, 
    miniprogram_lead_chainRatioDiff, miniprogram_lead_yearOnYear, miniprogram_lead_yearOnYearDiff, 
    miniprogram_lead_monthAvg, miniprogram_leadRate_sum, miniprogram_leadRate_chainRatioDiff, 
    miniprogram_leadRate_yearOnYearDiff, miniprogram_leadRate_monthAvg, gaode_flow_sum, 
    gaode_flow_chainRatio, gaode_flow_yearOnYear, gaode_flow_monthAvg, gaode_lead_sum, 
    gaode_lead_chainRatio, gaode_lead_chainRatioDiff, gaode_lead_yearOnYear, gaode_lead_yearOnYearDiff, 
    gaode_lead_monthAvg, gaode_leadRate_sum, gaode_leadRate_chainRatioDiff, gaode_leadRate_yearOnYearDiff, 
    gaode_leadRate_monthAvg, default_0, default_1, default_2, default_3, default_4, default_5, 
    default_6, default_7, default_8, default_9, is_del, Created_STime, Modified_STime
    ,[miniprogram_phone_sum]
      ,[miniprogram_phone_chainRatio]
      ,[miniprogram_phone_chainRatioDiff]
      ,[miniprogram_phone_yearOnYear]
      ,[miniprogram_phone_yearOnYearDiff]
      ,[miniprogram_phone_monthAvg]
      ,[miniprogram_flow_phone_rate]
      ,[miniprogram_flowphoneRate_chainRatioDiff]
      ,[miniprogram_flowphoneRate_yearOnYearDiff]
      ,[miniprogram_flowphoneRate_monthAvg]
      ,[miniprogram_lead_phone_rate]
      ,[miniprogram_leadphoneRate_chainRatioDiff]
      ,[miniprogram_leadphoneRate_yearOnYearDiff]
      ,[miniprogram_leadphoneRate_monthAvg]
    ,cooperation_flow_sum
    ,cooperation_flow_chainRatio
	,cooperation_flow_yearOnYear
	,cooperation_flow_monthAvg
	,cooperation_lead_sum
	,cooperation_lead_chainRatio
	,cooperation_lead_chainRatioDiff
	,cooperation_lead_yearOnYear
	,cooperation_lead_yearOnYearDiff
	,cooperation_lead_monthAvg
  </sql>
  <select id="getReport"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from [Replication].[dbo].panoramic_buyUseCar_dailyRepoter_dingding
    where dt=#{dt}
  </select>
</mapper>