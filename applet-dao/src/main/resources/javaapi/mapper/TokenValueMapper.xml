<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.TokenValueMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.TokenValue">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="tokenValue" jdbcType="VARCHAR" property="tokenvalue" />
    <result column="wxtype" jdbcType="INTEGER" property="wxtype" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="RV" jdbcType="BINARY" property="rv" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from TokenValue
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.TokenValue">
    insert into TokenValue (Id, tokenValue, wxtype, 
      Created_STime, Modified_STime, is_del, 
      RV)
    values (#{id,jdbcType=INTEGER}, #{tokenvalue,jdbcType=VARCHAR}, #{wxtype,jdbcType=INTEGER}, 
      #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}, 
      #{rv,jdbcType=BINARY})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.TokenValue">
    update TokenValue
    set tokenValue = #{tokenvalue,jdbcType=VARCHAR},
      wxtype = #{wxtype,jdbcType=INTEGER},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      RV = #{rv,jdbcType=BINARY}
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select Id, tokenValue, wxtype, Created_STime, Modified_STime, is_del, RV
    from TokenValue
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select Id, tokenValue, wxtype, Created_STime, Modified_STime, is_del, RV
    from TokenValue
  </select>
    <select id="getAccessTokenForType" resultType="java.lang.String">
      SELECT top 1 tokenValue
      FROM [TokenValue] T WITH(NOLOCK)
      WHERE is_del=0 and wxtype=#{wxType} order by id desc
    </select>
</mapper>