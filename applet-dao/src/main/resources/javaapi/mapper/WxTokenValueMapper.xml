<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.WxTokenValueMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.WxTokenValue">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="tokenValue" jdbcType="VARCHAR" property="tokenvalue" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from wxtokenvalue
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.WxTokenValue">
    insert into wxtokenvalue (Id, tokenValue, Created_STime, 
      Modified_STime)
    values (#{id,jdbcType=INTEGER}, #{tokenvalue,jdbcType=VARCHAR}, #{createdStime,jdbcType=TIMESTAMP}, 
      #{modifiedStime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.WxTokenValue">
    update wxtokenvalue
    set tokenValue = #{tokenvalue,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select Id, tokenValue, Created_STime, Modified_STime
    from wxtokenvalue
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select Id, tokenValue, Created_STime, Modified_STime
    from wxtokenvalue
  </select>
    <select id="getTop1" resultType="java.lang.String">
      SELECT top 1 tokenValue FROM [Wxtokenvalue] T WITH(NOLOCK)  Order by Created_STime desc
    </select>
</mapper>