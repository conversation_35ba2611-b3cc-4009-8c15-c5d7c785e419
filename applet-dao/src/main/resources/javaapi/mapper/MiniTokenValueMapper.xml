<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.MiniTokenValueMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.MiniTokenValue">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="tokenValue" jdbcType="VARCHAR" property="tokenvalue" />
    <result column="minitype" jdbcType="INTEGER" property="minitype" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from minitokenvalue
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.MiniTokenValue">
    insert into minitokenvalue (Id, tokenValue, minitype, 
      Created_STime, Modified_STime, is_del
      )
    values (#{id,jdbcType=INTEGER}, #{tokenvalue,jdbcType=VARCHAR}, #{minitype,jdbcType=INTEGER}, 
      #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=INTEGER}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.MiniTokenValue">
    update minitokenvalue
    set tokenValue = #{tokenvalue,jdbcType=VARCHAR},
      minitype = #{minitype,jdbcType=INTEGER},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER}
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select Id, tokenValue, minitype, Created_STime, Modified_STime, is_del
    from minitokenvalue
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select Id, tokenValue, minitype, Created_STime, Modified_STime, is_del
    from minitokenvalue
  </select>


  <select id="getTop1" resultType="java.lang.String">
    SELECT top 1 tokenValue FROM [minitokenvalue] T WITH(NOLOCK)   where minitype = #{minitype,jdbcType=INTEGER}  Order by Created_STime desc
  </select>
</mapper>