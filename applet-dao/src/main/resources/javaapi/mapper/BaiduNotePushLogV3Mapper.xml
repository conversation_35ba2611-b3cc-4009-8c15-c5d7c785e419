<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.BaiduNotePushLogV3Mapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="ex_id" property="exId" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="imgs" property="imgs" jdbcType="VARCHAR"/>
        <result column="push_state" property="pushState" jdbcType="INTEGER"/>
        <result column="push_time" property="pushTime" jdbcType="TIMESTAMP"/>
        <result column="push_date" property="pushDate" jdbcType="VARCHAR"/>
        <result column="Created_STime" property="createdSTime" jdbcType="TIMESTAMP"/>
        <result column="Modified_STime" property="modifiedSTime" jdbcType="TIMESTAMP"/>
        <result column="is_del" property="isDel" jdbcType="INTEGER"/>
        <result column="author_name" property="authorName" jdbcType="VARCHAR"/>
        <result column="author_img" property="authorImg" jdbcType="VARCHAR"/>
        <result column="op_account" property="opAccount" jdbcType="VARCHAR"/>
        <result column="approval_status" property="approvalStatus" jdbcType="INTEGER"/>
        <result column="city_id" property="cityId" jdbcType="VARCHAR"/>
        <result column="city_name" property="cityName" jdbcType="VARCHAR"/>
        <result column="province_id" property="provinceId" jdbcType="VARCHAR"/>
        <result column="province_name" property="provinceName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通用字段集合 -->
    <sql id="Base_Column_List">
        id, ex_id, title, content, imgs, push_state, push_time, push_date,
        Created_STime, Modified_STime, is_del, author_name, author_img, op_account,
        approval_status, city_id, city_name, province_id, province_name
    </sql>

    <!-- 插入语句（排除自增ID） -->
    <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO baidu_note_push_log_v3 (
            ex_id, title, content, imgs, push_state,
           author_name, author_img, op_account,push_time, push_date,
            approval_status, city_id, city_name, province_id, province_name
        ) VALUES (
                     #{exId}, #{title}, #{content}, #{imgs}, #{pushState},
                     #{authorName}, #{authorImg}, #{opAccount},#{pushTime}, #{pushDate},
                     #{approvalStatus}, #{cityId}, #{cityName}, #{provinceId}, #{provinceName}
                 )
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateByExId" parameterType="com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3">
        UPDATE baidu_note_push_log_v3
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="imgs != null">imgs = #{imgs},</if>
            <if test="pushState != null">push_state = #{pushState},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="pushDate != null">push_date = #{pushDate},</if>
            <if test="modifiedSTime != null">Modified_STime = #{modifiedSTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="authorName != null">author_name = #{authorName},</if>
            <if test="authorImg != null">author_img = #{authorImg},</if>
            <if test="opAccount != null">op_account = #{opAccount},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="provinceName != null">province_name = #{provinceName}</if>
        </set>
        WHERE ex_id = #{exId}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List"/>
        FROM baidu_note_push_log_v3(nolock)
        WHERE id = #{id}
    </select>

<!--    根据推送状态查询笔记推送记录-->
    <select id="getBaiduNote" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM baidu_note_push_log_v3(nolock)
        WHERE push_state = #{pushState}
    </select>

    <select id="getNoPushCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM baidu_note_push_log_v3(nolock)
        WHERE push_state = #{pushState}
    </select>

    <insert id="batchInsert" >
        insert into baidu_note_push_log_v3
        (ex_id, title, content, imgs, push_state, push_time, push_date,
        Created_STime, Modified_STime, is_del, author_name, author_img, op_account,
        approval_status, city_id, city_name, province_id, province_name)
        values
        <foreach collection="list" item="item" index="index" separator=",">
           (
            #{item.exId}, #{item.title}, #{item.content}, #{item.imgs}, #{item.pushState}, #{item.pushTime}, #{item.pushDate},
            #{item.createdSTime}, #{item.modifiedSTime}, #{item.isDel}, #{item.authorName}, #{item.authorImg}, #{item.opAccount},
            #{item.approvalStatus}, #{item.cityId}, #{item.cityName}, #{item.provinceId}, #{item.provinceName}
            )
        </foreach>
    </insert>

<!--    根据ex_id字段判断，有则更新，没有则新增-->
    <insert id="insertOrUpdate" parameterType="com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        <!--  判断是否存在  -->
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
            SELECT ISNULL( (SELECT TOP 1 id
            FROM baidu_note_push_log_v3
            WHERE ex_id = #{exId}), NULL )
        </selectKey>
        <!--  存在则更新  -->
        <if test="id != null">
            UPDATE baidu_note_push_log_v3
            <set>
                <if test="title != null">title = #{title},</if>
                <if test="content != null">content = #{content},</if>
                <if test="imgs != null">imgs = #{imgs},</if>
                <if test="pushState != null">push_state = #{pushState},</if>
                <if test="pushTime != null">push_time = #{pushTime},</if>
                <if test="pushDate != null">push_date = #{pushDate},</if>
                <if test="createdSTime != null">Created_STime = #{createdSTime},</if>
                <if test="modifiedSTime != null">Modified_STime = #{modifiedSTime},</if>
                <if test="isDel != null">is_del = #{isDel},</if>
                <if test="authorName != null">author_name = #{authorName},</if>
                <if test="authorImg != null">author_img = #{authorImg},</if>
                <if test="opAccount != null">op_account = #{opAccount},</if>
                <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
                <if test="cityId != null">city_id = #{cityId},</if>
                <if test="cityName != null">city_name = #{cityName},</if>
                <if test="provinceId != null">province_id = #{provinceId},</if>
                <if test="provinceName != null">province_name = #{provinceName},</if>
                modified_time = NOW()
            </set>
            WHERE id = #{id}
        </if>
        <!--  不存在则新增  -->
        <if test="id == null">
            INSERT INTO baidu_note_push_log_v3
            (
            ex_id, title, content, imgs, push_state, push_time, push_date,
            Created_STime, Modified_STime, is_del, author_name, author_img, op_account,
            approval_status, city_id, city_name, province_id, province_name
            )
            VALUES
            (
            #{exId}, #{title}, #{content}, #{imgs}, #{pushState}, #{pushTime}, #{pushDate},
            #{createdSTime}, #{modifiedSTime}, #{isDel}, #{authorName}, #{authorImg}, #{opAccount},
            #{approvalStatus}, #{cityId}, #{cityName}, #{provinceId}, #{provinceName}
            )
        </if>
    </insert>

    <select id="getByExId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM baidu_note_push_log_v3(nolock)
        WHERE ex_id = #{exId}
    </select>


</mapper>