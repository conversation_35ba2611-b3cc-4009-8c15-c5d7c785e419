<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SecondHandCarProductInfoLogMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfoLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_id" jdbcType="VARCHAR" property="serviceId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="tabs" jdbcType="VARCHAR" property="tabs" />
    <result column="info_url" jdbcType="VARCHAR" property="infoUrl" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="originalprice" jdbcType="VARCHAR" property="originalprice" />
    <result column="finalprice" jdbcType="VARCHAR" property="finalprice" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="full_data" jdbcType="VARCHAR" property="fullData" />
    <result column="save_result" jdbcType="INTEGER" property="saveResult" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="car_name" jdbcType="VARCHAR" property="carName" />
    <result column="car_brand" jdbcType="VARCHAR" property="carBrand" />
    <result column="car_gear" jdbcType="VARCHAR" property="carGear" />
    <result column="car_volume" jdbcType="VARCHAR" property="carVolume" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="car_seat" jdbcType="VARCHAR" property="carSeat" />
    <result column="car_year" jdbcType="VARCHAR" property="carYear" />
    <result column="car_plate" jdbcType="VARCHAR" property="carPlate" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from second_hand_car_product_info_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfoLog">
    insert into second_hand_car_product_info_log (service_id, name,
      type, pic, tabs, info_url, 
      comment, description, originalprice, 
      finalprice, category, status, 
      full_data, save_result, org_id, 
      car_name, car_brand, car_gear, 
      car_volume, car_type, car_seat, 
      car_year, car_plate)
    values (#{serviceId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{type,jdbcType=INTEGER}, #{pic,jdbcType=VARCHAR}, #{tabs,jdbcType=VARCHAR}, #{infoUrl,jdbcType=VARCHAR}, 
      #{comment,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{originalprice,jdbcType=VARCHAR}, 
      #{finalprice,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{fullData,jdbcType=VARCHAR}, #{saveResult,jdbcType=INTEGER}, #{orgId,jdbcType=VARCHAR}, 
      #{carName,jdbcType=VARCHAR}, #{carBrand,jdbcType=VARCHAR}, #{carGear,jdbcType=VARCHAR}, 
      #{carVolume,jdbcType=VARCHAR}, #{carType,jdbcType=VARCHAR}, #{carSeat,jdbcType=VARCHAR}, 
      #{carYear,jdbcType=VARCHAR}, #{carPlate,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfoLog">
    update second_hand_car_product_info_log
    set service_id = #{serviceId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      pic = #{pic,jdbcType=VARCHAR},
      tabs = #{tabs,jdbcType=VARCHAR},
      info_url = #{infoUrl,jdbcType=VARCHAR},
      comment = #{comment,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      originalprice = #{originalprice,jdbcType=VARCHAR},
      finalprice = #{finalprice,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      full_data = #{fullData,jdbcType=VARCHAR},
      save_result = #{saveResult,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=VARCHAR},
      car_name = #{carName,jdbcType=VARCHAR},
      car_brand = #{carBrand,jdbcType=VARCHAR},
      car_gear = #{carGear,jdbcType=VARCHAR},
      car_volume = #{carVolume,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=VARCHAR},
      car_seat = #{carSeat,jdbcType=VARCHAR},
      car_year = #{carYear,jdbcType=VARCHAR},
      car_plate = #{carPlate,jdbcType=VARCHAR},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, service_id, name, type, pic, tabs, info_url, comment, description, originalprice, 
    finalprice, category, status, full_data, save_result, org_id, car_name, car_brand, 
    car_gear, car_volume, car_type, car_seat, car_year, car_plate, Created_STime, Modified_STime, 
    is_del
    from second_hand_car_product_info_log  with(nolock)
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, service_id, name, type, pic, tabs, info_url, comment, description, originalprice, 
    finalprice, category, status, full_data, save_result, org_id, car_name, car_brand, 
    car_gear, car_volume, car_type, car_seat, car_year, car_plate, Created_STime, Modified_STime, 
    is_del
    from second_hand_car_product_info_log  with(nolock)
  </select>
</mapper>