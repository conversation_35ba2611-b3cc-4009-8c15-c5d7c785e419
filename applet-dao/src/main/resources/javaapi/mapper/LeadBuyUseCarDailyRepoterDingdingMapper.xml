<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.LeadBuyUseCarDailyRepoterDingdingMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.LeadBuyUseCarDailyRepoterDingding">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dt" jdbcType="VARCHAR" property="dt"/>
        <result column="total_lead_sum" jdbcType="VARCHAR" property="totalLeadSum"/>
        <result column="total_lead_chainRatio" jdbcType="VARCHAR" property="totalLeadChainratio"/>
        <result column="total_lead_chainRatioDiff" jdbcType="VARCHAR" property="totalLeadChainratiodiff"/>
        <result column="total_lead_yearOnYear" jdbcType="VARCHAR" property="totalLeadYearonyear"/>
        <result column="total_lead_yearOnYearDiff" jdbcType="VARCHAR" property="totalLeadYearonyeardiff"/>
        <result column="total_lead_monthAvg" jdbcType="VARCHAR" property="totalLeadMonthavg"/>
        <result column="app_lead_sum" jdbcType="VARCHAR" property="appLeadSum"/>
        <result column="app_lead_rate" jdbcType="VARCHAR" property="appLeadRate"/>
        <result column="app_lead_chainRatio" jdbcType="VARCHAR" property="appLeadChainratio"/>
        <result column="app_lead_chainRatioDiff" jdbcType="VARCHAR" property="appLeadChainratiodiff"/>
        <result column="app_lead_yearOnYear" jdbcType="VARCHAR" property="appLeadYearonyear"/>
        <result column="app_lead_yearOnYearDiff" jdbcType="VARCHAR" property="appLeadYearonyeardiff"/>
        <result column="app_lead_monthAvg" jdbcType="VARCHAR" property="appLeadMonthavg"/>
        <result column="miniprogram_lead_sum" jdbcType="VARCHAR" property="miniprogramLeadSum"/>
        <result column="miniprogram_lead_rate" jdbcType="VARCHAR" property="miniprogramLeadRate"/>
        <result column="miniprogram_lead_chainRatio" jdbcType="VARCHAR" property="miniprogramLeadChainratio"/>
        <result column="miniprogram_lead_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramLeadChainratiodiff"/>
        <result column="miniprogram_lead_yearOnYear" jdbcType="VARCHAR" property="miniprogramLeadYearonyear"/>
        <result column="miniprogram_lead_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramLeadYearonyeardiff"/>
        <result column="miniprogram_lead_monthAvg" jdbcType="VARCHAR" property="miniprogramLeadMonthavg"/>
        <result column="live_lead_sum" jdbcType="VARCHAR" property="liveLeadSum"/>
        <result column="live_lead_rate" jdbcType="VARCHAR" property="liveLeadRate"/>
        <result column="live_lead_chainRatio" jdbcType="VARCHAR" property="liveLeadChainratio"/>
        <result column="live_lead_chainRatioDiff" jdbcType="VARCHAR" property="liveLeadChainratiodiff"/>
        <result column="live_lead_yearOnYear" jdbcType="VARCHAR" property="liveLeadYearonyear"/>
        <result column="live_lead_yearOnYearDiff" jdbcType="VARCHAR" property="liveLeadYearonyeardiff"/>
        <result column="live_lead_monthAvg" jdbcType="VARCHAR" property="liveLeadMonthavg"/>
        <result column="community_lead_sum" jdbcType="VARCHAR" property="communityLeadSum"/>
        <result column="community_lead_rate" jdbcType="VARCHAR" property="communityLeadRate"/>
        <result column="community_lead_chainRatio" jdbcType="VARCHAR" property="communityLeadChainratio"/>
        <result column="community_lead_chainRatioDiff" jdbcType="VARCHAR" property="communityLeadChainratiodiff"/>
        <result column="community_lead_yearOnYear" jdbcType="VARCHAR" property="communityLeadYearonyear"/>
        <result column="community_lead_yearOnYearDiff" jdbcType="VARCHAR" property="communityLeadYearonyeardiff"/>
        <result column="community_lead_monthAvg" jdbcType="VARCHAR" property="communityLeadMonthavg"/>
        <result column="hotchat_lead_sum" jdbcType="VARCHAR" property="hotchatLeadSum"/>
        <result column="hotchat_lead_rate" jdbcType="VARCHAR" property="hotchatLeadRate"/>
        <result column="hotchat_lead_chainRatio" jdbcType="VARCHAR" property="hotchatLeadChainratio"/>
        <result column="hotchat_lead_chainRatioDiff" jdbcType="VARCHAR" property="hotchatLeadChainratiodiff"/>
        <result column="hotchat_lead_yearOnYear" jdbcType="VARCHAR" property="hotchatLeadYearonyear"/>
        <result column="hotchat_lead_yearOnYearDiff" jdbcType="VARCHAR" property="hotchatLeadYearonyeardiff"/>
        <result column="hotchat_lead_monthAvg" jdbcType="VARCHAR" property="hotchatLeadMonthavg"/>
        <result column="default_0" jdbcType="VARCHAR" property="default0"/>
        <result column="default_1" jdbcType="VARCHAR" property="default1"/>
        <result column="default_2" jdbcType="VARCHAR" property="default2"/>
        <result column="default_3" jdbcType="VARCHAR" property="default3"/>
        <result column="default_4" jdbcType="VARCHAR" property="default4"/>
        <result column="default_5" jdbcType="VARCHAR" property="default5"/>
        <result column="default_6" jdbcType="VARCHAR" property="default6"/>
        <result column="default_7" jdbcType="VARCHAR" property="default7"/>
        <result column="default_8" jdbcType="VARCHAR" property="default8"/>
        <result column="default_9" jdbcType="VARCHAR" property="default9"/>
        <result column="is_del" jdbcType="INTEGER" property="isDel"/>
        <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime"/>
        <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime"/>
        <result column="app_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="appLeadrateChainratiodiff"/>
        <result column="app_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="appLeadrateYearonyeardiff"/>
        <result column="miniprogram_leadRate_chainRatioDiff" jdbcType="VARCHAR"
                property="miniprogramLeadrateChainratiodiff"/>
        <result column="miniprogram_leadRate_yearOnYearDiff" jdbcType="VARCHAR"
                property="miniprogramLeadrateYearonyeardiff"/>
        <result column="live_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="liveLeadrateChainratiodiff"/>
        <result column="live_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="liveLeadrateYearonyeardiff"/>
        <result column="community_leadRate_chainRatioDiff" jdbcType="VARCHAR"
                property="communityLeadrateChainratiodiff"/>
        <result column="community_leadRate_yearOnYearDiff" jdbcType="VARCHAR"
                property="communityLeadrateYearonyeardiff"/>
        <result column="hotchat_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="hotchatLeadrateChainratiodiff"/>
        <result column="hotchat_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="hotchatLeadrateYearonyeardiff"/>
        <result column="pc_lead_sum" jdbcType="VARCHAR" property="pcLeadSum"/>
        <result column="pc_lead_chainRatio" jdbcType="VARCHAR" property="pcLeadChainratio"/>
        <result column="pc_lead_chainRatioDiff" jdbcType="VARCHAR" property="pcLeadChainratiodiff"/>
        <result column="pc_lead_yearOnYear" jdbcType="VARCHAR" property="pcLeadYearonyear"/>
        <result column="pc_lead_yearOnYearDiff" jdbcType="VARCHAR" property="pcLeadYearonyeardiff"/>
        <result column="pc_lead_rate" jdbcType="VARCHAR" property="pcLeadRate"/>
        <result column="pc_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="pcLeadrateChainratiodiff"/>
        <result column="pc_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="pcLeadrateYearonyeardiff"/>
        <result column="pc_lead_monthAvg" jdbcType="VARCHAR" property="pcLeadMonthavg"/>
        <result column="pc_leadRate_monthAvg" jdbcType="VARCHAR" property="pcLeadrateMonthavg"/>
        <result column="m_lead_sum" jdbcType="VARCHAR" property="mLeadSum"/>
        <result column="m_lead_chainRatio" jdbcType="VARCHAR" property="mLeadChainratio"/>
        <result column="m_lead_chainRatioDiff" jdbcType="VARCHAR" property="mLeadChainratiodiff"/>
        <result column="m_lead_yearOnYear" jdbcType="VARCHAR" property="mLeadYearonyear"/>
        <result column="m_lead_yearOnYearDiff" jdbcType="VARCHAR" property="mLeadYearonyeardiff"/>
        <result column="m_lead_rate" jdbcType="VARCHAR" property="mLeadRate"/>
        <result column="m_leadRate_chainRatioDiff" jdbcType="VARCHAR" property="mLeadrateChainratiodiff"/>
        <result column="m_leadRate_yearOnYearDiff" jdbcType="VARCHAR" property="mLeadrateYearonyeardiff"/>
        <result column="m_lead_monthAvg" jdbcType="VARCHAR" property="mLeadMonthavg"/>
        <result column="m_leadRate_monthAvg" jdbcType="VARCHAR" property="mLeadrateMonthavg"/>
        <result column="app_leadRate_monthAvg" jdbcType="VARCHAR" property="appLeadrateMonthavg"/>
        <result column="miniprogram_leadRate_monthAvg" jdbcType="VARCHAR" property="miniprogramLeadrateMonthavg"/>
        <result column="live_leadRate_monthAvg" jdbcType="VARCHAR" property="liveLeadrateMonthavg"/>
        <result column="community_leadRate_monthAvg" jdbcType="VARCHAR" property="communityLeadrateMonthavg"/>
        <result column="hotchat_leadRate_monthAvg" jdbcType="VARCHAR" property="hotchatLeadrateMonthavg"/>
        <result column="total_phone_sum" jdbcType="VARCHAR" property="totalPhoneSum"/>
        <result column="total_phone_chainRatio" jdbcType="VARCHAR" property="totalPhoneChainRatio"/>
        <result column="total_phone_chainRatioDiff" jdbcType="VARCHAR" property="totalPhoneChainRatioDiff"/>
        <result column="total_phone_yearOnYear" jdbcType="VARCHAR" property="totalPhoneYearOnYear"/>
        <result column="total_phone_yearOnYearDiff" jdbcType="VARCHAR" property="totalPhoneYearOnYearDiff"/>
        <result column="total_phone_monthAvg" jdbcType="VARCHAR" property="totalPhoneMonthAvg"/>
        <result column="app_phone_sum" jdbcType="VARCHAR" property="appPhoneSum"/>
        <result column="app_phone_rate" jdbcType="VARCHAR" property="appPhoneRate"/>
        <result column="app_phone_chainRatio" jdbcType="VARCHAR" property="appPhoneChainRatio"/>
        <result column="app_phone_chainRatioDiff" jdbcType="VARCHAR" property="appPhoneChainRatioDiff"/>
        <result column="app_phone_yearOnYear" jdbcType="VARCHAR" property="appPhoneYearOnYear"/>
        <result column="app_phone_yearOnYearDiff" jdbcType="VARCHAR" property="appPhoneYearOnYearDiff"/>
        <result column="app_phone_monthAvg" jdbcType="VARCHAR" property="appPhoneMonthAvg"/>
        <result column="app_phoneRate_chainRatioDiff" jdbcType="VARCHAR" property="appPhoneRateChainRatioDiff"/>
        <result column="app_phoneRate_yearOnYearDiff" jdbcType="VARCHAR" property="appPhoneRateYearOnYearDiff"/>
        <result column="app_phoneRate_monthAvg" jdbcType="VARCHAR" property="appPhoneRateMonthAvg"/>
        <result column="miniprogram_phone_sum" jdbcType="VARCHAR" property="miniprogramPhoneSum"/>
        <result column="miniprogram_phone_rate" jdbcType="VARCHAR" property="miniprogramPhoneRate"/>
        <result column="miniprogram_phone_chainRatio" jdbcType="VARCHAR" property="miniprogramPhoneChainRatio"/>
        <result column="miniprogram_phone_chainRatioDiff" jdbcType="VARCHAR" property="miniprogramPhoneChainRatioDiff"/>
        <result column="miniprogram_phone_yearOnYear" jdbcType="VARCHAR" property="miniprogramPhoneYearOnYear"/>
        <result column="miniprogram_phone_yearOnYearDiff" jdbcType="VARCHAR" property="miniprogramPhoneYearOnYearDiff"/>
        <result column="miniprogram_phone_monthAvg" jdbcType="VARCHAR" property="miniprogramPhoneMonthAvg"/>
        <result column="miniprogram_phoneRate_chainRatioDiff" jdbcType="VARCHAR"
                property="miniprogramPhoneRateChainRatioDiff"/>
        <result column="miniprogram_phoneRate_yearOnYearDiff" jdbcType="VARCHAR"
                property="miniprogramPhoneRateYearOnYearDiff"/>
        <result column="miniprogram_phoneRate_monthAvg" jdbcType="VARCHAR" property="miniprogramPhoneRateMonthAvg"/>
        <result column="pc_phone_sum" jdbcType="VARCHAR" property="pcPhoneSum"/>
        <result column="pc_phone_rate" jdbcType="VARCHAR" property="pcPhoneRate"/>
        <result column="pc_phone_chainRatio" jdbcType="VARCHAR" property="pcPhoneChainRatio"/>
        <result column="pc_phone_chainRatioDiff" jdbcType="VARCHAR" property="pcPhoneChainRatioDiff"/>
        <result column="pc_phone_yearOnYear" jdbcType="VARCHAR" property="pcPhoneYearOnYear"/>
        <result column="pc_phone_yearOnYearDiff" jdbcType="VARCHAR" property="pcPhoneYearOnYearDiff"/>
        <result column="pc_phone_monthAvg" jdbcType="VARCHAR" property="pcPhoneMonthAvg"/>
        <result column="pc_phoneRate_chainRatioDiff" jdbcType="VARCHAR" property="pcPhoneRateChainRatioDiff"/>
        <result column="pc_phoneRate_yearOnYearDiff" jdbcType="VARCHAR" property="pcPhoneRateYearOnYearDiff"/>
        <result column="pc_phoneRate_monthAvg" jdbcType="VARCHAR" property="pcPhoneRateMonthAvg"/>
        <result column="m_phone_sum" jdbcType="VARCHAR" property="mPhoneSum"/>
        <result column="m_phone_rate" jdbcType="VARCHAR" property="mPhoneRate"/>
        <result column="m_phone_chainRatio" jdbcType="VARCHAR" property="mPhoneChainRatio"/>
        <result column="m_phone_chainRatioDiff" jdbcType="VARCHAR" property="mPhoneChainRatioDiff"/>
        <result column="m_phone_yearOnYear" jdbcType="VARCHAR" property="mPhoneYearOnYear"/>
        <result column="m_phone_yearOnYearDiff" jdbcType="VARCHAR" property="mPhoneYearOnYearDiff"/>
        <result column="m_phone_monthAvg" jdbcType="VARCHAR" property="mPhoneMonthAvg"/>
        <result column="m_phoneRate_chainRatioDiff" jdbcType="VARCHAR" property="mPhoneRateChainRatioDiff"/>
        <result column="m_phoneRate_yearOnYearDiff" jdbcType="VARCHAR" property="mPhoneRateYearOnYearDiff"/>
        <result column="m_phoneRate_monthAvg" jdbcType="VARCHAR" property="mPhoneRateMonthAvg"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , dt, total_lead_sum, total_lead_chainRatio, total_lead_chainRatioDiff, total_lead_yearOnYear,
    total_lead_yearOnYearDiff, total_lead_monthAvg, app_lead_sum, app_lead_rate, app_lead_chainRatio,
    app_lead_chainRatioDiff, app_lead_yearOnYear, app_lead_yearOnYearDiff, app_lead_monthAvg,
    miniprogram_lead_sum, miniprogram_lead_rate, miniprogram_lead_chainRatio, miniprogram_lead_chainRatioDiff,
    miniprogram_lead_yearOnYear, miniprogram_lead_yearOnYearDiff, miniprogram_lead_monthAvg,
    live_lead_sum, live_lead_rate, live_lead_chainRatio, live_lead_chainRatioDiff, live_lead_yearOnYear,
    live_lead_yearOnYearDiff, live_lead_monthAvg, community_lead_sum, community_lead_rate,
    community_lead_chainRatio, community_lead_chainRatioDiff, community_lead_yearOnYear,
    community_lead_yearOnYearDiff, community_lead_monthAvg, hotchat_lead_sum, hotchat_lead_rate,
    hotchat_lead_chainRatio, hotchat_lead_chainRatioDiff, hotchat_lead_yearOnYear, hotchat_lead_yearOnYearDiff,
    hotchat_lead_monthAvg, default_0, default_1, default_2, default_3, default_4, default_5,
    default_6, default_7, default_8, default_9, is_del, Created_STime, Modified_STime,
    app_leadRate_chainRatioDiff, app_leadRate_yearOnYearDiff, miniprogram_leadRate_chainRatioDiff,
    miniprogram_leadRate_yearOnYearDiff, live_leadRate_chainRatioDiff, live_leadRate_yearOnYearDiff,
    community_leadRate_chainRatioDiff, community_leadRate_yearOnYearDiff, hotchat_leadRate_chainRatioDiff,
    hotchat_leadRate_yearOnYearDiff, pc_lead_sum, pc_lead_chainRatio, pc_lead_chainRatioDiff,
    pc_lead_yearOnYear, pc_lead_yearOnYearDiff, pc_lead_rate, pc_leadRate_chainRatioDiff,
    pc_leadRate_yearOnYearDiff, pc_lead_monthAvg, pc_leadRate_monthAvg, m_lead_sum, m_lead_chainRatio,
    m_lead_chainRatioDiff, m_lead_yearOnYear, m_lead_yearOnYearDiff, m_lead_rate, m_leadRate_chainRatioDiff,
    m_leadRate_yearOnYearDiff, m_lead_monthAvg, m_leadRate_monthAvg, app_leadRate_monthAvg,
    miniprogram_leadRate_monthAvg, live_leadRate_monthAvg, community_leadRate_monthAvg,
    hotchat_leadRate_monthAvg,
    total_phone_sum		
	,total_phone_chainRatio
	,total_phone_chainRatioDiff
	,total_phone_yearOnYear
	,total_phone_yearOnYearDiff
  ,total_phone_monthAvg
  ,app_phone_sum
  ,app_phone_rate
	,app_phone_chainRatio
  ,app_phone_chainRatioDiff
	,app_phone_yearOnYear	
	,app_phone_yearOnYearDiff 
	,app_phone_monthAvg
	,app_phoneRate_chainRatioDiff
	,app_phoneRate_yearOnYearDiff
  ,app_phoneRate_monthAvg 
   ,miniprogram_phone_sum
  ,miniprogram_phone_rate
	,miniprogram_phone_chainRatio
  ,miniprogram_phone_chainRatioDiff
	,miniprogram_phone_yearOnYear	
	,miniprogram_phone_yearOnYearDiff 
	,miniprogram_phone_monthAvg
	,miniprogram_phoneRate_chainRatioDiff
	,miniprogram_phoneRate_yearOnYearDiff
  ,miniprogram_phoneRate_monthAvg
  ,pc_phone_sum
  ,pc_phone_rate
	,pc_phone_chainRatio
  ,pc_phone_chainRatioDiff
	,pc_phone_yearOnYear	
	,pc_phone_yearOnYearDiff 
	,pc_phone_monthAvg
	,pc_phoneRate_chainRatioDiff
	,pc_phoneRate_yearOnYearDiff
  ,pc_phoneRate_monthAvg 
  ,m_phone_sum
  ,m_phone_rate
	,m_phone_chainRatio
  ,m_phone_chainRatioDiff
	,m_phone_yearOnYear	
	,m_phone_yearOnYearDiff 
	,m_phone_monthAvg
	,m_phoneRate_chainRatioDiff
	,m_phoneRate_yearOnYearDiff
  ,m_phoneRate_monthAvg
    </sql>

    <select id="getReport" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from [Replication].[dbo].lead_buyUseCar_dailyRepoter_dingding
        where dt = #{dt}
    </select>


</mapper>