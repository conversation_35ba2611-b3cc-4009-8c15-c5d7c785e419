<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.AppletOpenAutohomeRelationMapper">

    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.AppletOpenAutohomeRelation">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="openid" property="openid" jdbcType="VARCHAR" />
        <result column="unionid" property="unionid" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="platform" property="platform" jdbcType="INTEGER" />
        <result column="first_login_date" property="firstLoginDate" jdbcType="TIMESTAMP" />
        <result column="first_login_ip" property="firstLoginIp" jdbcType="VARCHAR" />
        <result column="first_login_position" property="firstLoginPosition" jdbcType="VARCHAR" />
        <result column="last_login_date" property="lastLoginDate" jdbcType="TIMESTAMP" />
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR" />
        <result column="last_login_position" property="lastLoginPosition" jdbcType="VARCHAR" />
        <result column="Created_STime" property="createdSTime" jdbcType="TIMESTAMP" />
        <result column="Modified_STime" property="modifiedSTime" jdbcType="TIMESTAMP" />
        <result column="is_del" property="isDel" jdbcType="INTEGER" />
    </resultMap>

    <select id="getUserIdByOpenIds" resultMap="BaseResultMap">
        select openid,user_id
        from applet_open_autohome_relation with(nolock)
        where openid in
        <foreach collection="openIdList" item="openId" open="(" separator="," close=")">
            #{openId}
        </foreach>
        and platform = #{platformType}
        and is_del = 0
        order by id desc
    </select>
</mapper>