<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SweetClientRealtionMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.SweetClientRealtion">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="Cid" jdbcType="VARCHAR" property="cid" />
    <result column="Sid" jdbcType="INTEGER" property="sid" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="RV" jdbcType="BINARY" property="rv" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from SweetClientRealtion
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.SweetClientRealtion">
    insert into SweetClientRealtion (Cid, Sid,
      Modified_STime, Created_STime, is_del)
    values (#{cid,jdbcType=VARCHAR}, #{sid,jdbcType=INTEGER}, getdate(), getdate(), 0)
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.SweetClientRealtion">
    update SweetClientRealtion
    set Cid = #{cid,jdbcType=VARCHAR},
      Sid = #{sid,jdbcType=INTEGER},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=INTEGER},
      RV = #{rv,jdbcType=BINARY}
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select Id, Cid, Sid, Modified_STime, Created_STime, is_del, RV
    from SweetClientRealtion
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select Id, Cid, Sid, Modified_STime, Created_STime, is_del, RV
    from SweetClientRealtion
  </select>
</mapper>