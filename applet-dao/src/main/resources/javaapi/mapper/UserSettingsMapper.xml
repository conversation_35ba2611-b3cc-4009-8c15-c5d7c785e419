<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.UserSettingsMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.UserSettings">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="OpenId" jdbcType="VARCHAR" property="openid"/>
        <result column="PersonalizedRec" jdbcType="INTEGER" property="personalizedrec"/>
        <result column="is_del" jdbcType="TINYINT" property="isDel"/>
        <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime"/>
        <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, OpenId, PersonalizedRec, is_del, Created_STime, Modified_STime
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from UserSettings
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.UserSettings">
        insert into UserSettings (id, OpenId, PersonalizedRec,
                                  is_del, Created_STime, Modified_STime)
        values (#{id,jdbcType=INTEGER}, #{openid,jdbcType=VARCHAR}, #{personalizedrec,jdbcType=INTEGER},
                #{isDel,jdbcType=TINYINT}, #{createdStime,jdbcType=TIMESTAMP}, #{modifiedStime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertNew">
        insert into UserSettings (OpenId, PersonalizedRec)
        values (#{openid,jdbcType=VARCHAR}, #{personalizedrec,jdbcType=INTEGER})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.UserSettings">
        update UserSettings
        set OpenId          = #{openid,jdbcType=VARCHAR},
            PersonalizedRec = #{personalizedrec,jdbcType=INTEGER},
            is_del          = #{isDel,jdbcType=TINYINT},
            Created_STime   = #{createdStime,jdbcType=TIMESTAMP},
            Modified_STime  = #{modifiedStime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select id, OpenId, PersonalizedRec, is_del, Created_STime, Modified_STime
        from UserSettings
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select id, OpenId, PersonalizedRec, is_del, Created_STime, Modified_STime
        from UserSettings
    </select>
    <select id="getPersonalizedRec" resultType="java.lang.Integer">
        select PersonalizedRec
        from UserSettings
        where OpenId = #{openId}
          and is_del = 0
    </select>
    <select id="getUserSettings" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UserSettings
        where OpenId = #{openId}
        and is_del = 0
    </select>
</mapper>