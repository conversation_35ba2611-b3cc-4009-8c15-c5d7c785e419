<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.AppletDoTaskLogMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.AppletDoTaskLog">
        <id column="id" property="id" javaType="java.lang.Long"/>
        <result column="openid" property="openid" javaType="java.lang.String"/>
        <result column="code" property="code" javaType="java.lang.String"/>
        <result column="unionid" property="unionid" javaType="java.lang.String"/>
        <result column="user_id" property="userId" javaType="java.lang.String"/>
        <result column="type" property="type" javaType="java.lang.Integer"/>
        <result column="status" property="status" javaType="java.lang.Integer"/>
        <result column="request_url" property="requestUrl" javaType="java.lang.String"/>
        <result column="request_data" property="requestData" javaType="java.lang.String"/>
        <result column="first_request_time" property="firstRequestTime" javaType="java.util.Date"/>
        <result column="response_code" property="responseCode" javaType="java.lang.String"/>
        <result column="response_status" property="responseStatus" javaType="java.lang.Integer"/>
        <result column="response_data" property="responseData" javaType="java.lang.String"/>
        <result column="Created_STime" property="createdSTime" javaType="java.util.Date"/>
        <result column="Modified_STime" property="modifiedSTime" javaType="java.util.Date"/>
        <result column="is_del" property="isDel" javaType="java.lang.Integer"/>
    </resultMap>

    <select id="getUnexecutedList" resultMap="BaseResultMap">
        select top ${pageSize}
        * from applet_do_task_log with(nolock)
        where status = 0
            and Created_STime between  #{startDate} and  #{endDate}
            and is_del = 0
            <if test="lastId > 0">
                and id > #{lastId}
            </if>
            order by id asc
    </select>

    <update id="updateByPrimaryKey">
        update applet_do_task_log
        set
        openid = #{openid,jdbcType=VARCHAR},
        code = #{code,jdbcType=VARCHAR},
        unionid = #{unionid,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        type = #{type,jdbcType=INTEGER},
        status = #{status,jdbcType=INTEGER},
        request_url = #{requestUrl,jdbcType=VARCHAR},
        request_data = #{requestData,jdbcType=VARCHAR},
        first_request_time = #{firstRequestTime,jdbcType=TIMESTAMP},
        response_code = #{responseCode,jdbcType=VARCHAR},
        response_status = #{responseStatus,jdbcType=INTEGER},
        response_data = #{responseData,jdbcType=VARCHAR},
        Modified_STime = #{modifiedSTime,jdbcType=TIMESTAMP},
        is_del = #{isDel,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>