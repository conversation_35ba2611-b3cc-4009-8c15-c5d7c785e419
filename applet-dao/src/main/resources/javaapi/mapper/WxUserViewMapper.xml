<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.WxUserViewMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.wxapp.UserView">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="uid" jdbcType="INTEGER" property="uid"/>
        <result column="ex_id" jdbcType="INTEGER" property="exId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , uid, ex_id, type, create_time, modify_time
    </sql>
    <insert id="insertSelective" parameterType="com.autohome.applet.dao.javaapi.model.wxapp.UserView">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into user_view
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">
                uid,
            </if>
            <if test="exId != null">
                ex_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">
                #{uid,jdbcType=INTEGER},
            </if>
            <if test="exId != null">
                #{exId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>