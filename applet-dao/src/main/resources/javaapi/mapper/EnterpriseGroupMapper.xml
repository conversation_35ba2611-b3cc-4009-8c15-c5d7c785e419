<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.EnterpriseGroupMapper">
  <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.EnterpriseGroup">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="BrandId" jdbcType="INTEGER" property="brandId" />
    <result column="BrandName" jdbcType="VARCHAR" property="brandName" />
    <result column="SeriesId" jdbcType="INTEGER" property="seriesId" />
    <result column="SeriesName" jdbcType="VARCHAR" property="seriesName" />
    <result column="CityId" jdbcType="VARCHAR" property="cityId" />
    <result column="CityName" jdbcType="VARCHAR" property="cityName" />
    <result column="GroupLogo" jdbcType="VARCHAR" property="groupLogo" />
    <result column="GroupName" jdbcType="VARCHAR" property="groupName" />
    <result column="GroupQRCode" jdbcType="VARCHAR" property="groupQrCode" />
    <result column="GroupQRCode3" jdbcType="VARCHAR" property="groupQrCode3" />
    <result column="Status" jdbcType="INTEGER" property="status" />
    <result column="Type" jdbcType="INTEGER" property="type" />
    <result column="SortId" jdbcType="INTEGER" property="sortId" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime" />
    <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from EnterpriseGroup
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.autohome.applet.dao.javaapi.model.EnterpriseGroup">
    insert into EnterpriseGroup (Id, BrandId, BrandName, 
      SeriesId, SeriesName, CityId, 
      CityName, GroupLogo, GroupName, 
      GroupQRCode, GroupQRCode3, Status, 
      Type, SortId, is_del, Modified_STime, 
      Created_STime, RV)
    values (#{id,jdbcType=INTEGER}, #{brandid,jdbcType=INTEGER}, #{brandname,jdbcType=VARCHAR}, 
      #{seriesid,jdbcType=INTEGER}, #{seriesname,jdbcType=VARCHAR}, #{cityid,jdbcType=VARCHAR}, 
      #{cityname,jdbcType=VARCHAR}, #{grouplogo,jdbcType=VARCHAR}, #{groupname,jdbcType=VARCHAR}, 
      #{groupqrcode,jdbcType=VARCHAR}, #{groupqrcode3,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{sortid,jdbcType=INTEGER}, #{isDel,jdbcType=BIT}, #{modifiedStime,jdbcType=TIMESTAMP}, 
      #{createdStime,jdbcType=TIMESTAMP}, #{rv,jdbcType=BINARY})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.autohome.applet.dao.javaapi.model.EnterpriseGroup">
    update EnterpriseGroup
    set BrandId = #{brandid,jdbcType=INTEGER},
      BrandName = #{brandname,jdbcType=VARCHAR},
      SeriesId = #{seriesid,jdbcType=INTEGER},
      SeriesName = #{seriesname,jdbcType=VARCHAR},
      CityId = #{cityid,jdbcType=VARCHAR},
      CityName = #{cityname,jdbcType=VARCHAR},
      GroupLogo = #{grouplogo,jdbcType=VARCHAR},
      GroupName = #{groupname,jdbcType=VARCHAR},
      GroupQRCode = #{groupqrcode,jdbcType=VARCHAR},
      GroupQRCode3 = #{groupqrcode3,jdbcType=VARCHAR},
      Status = #{status,jdbcType=INTEGER},
      Type = #{type,jdbcType=INTEGER},
      SortId = #{sortid,jdbcType=INTEGER},
      is_del = #{isDel,jdbcType=BIT},
      Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP},
      Created_STime = #{createdStime,jdbcType=TIMESTAMP},
      RV = #{rv,jdbcType=BINARY}
    where Id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select Id, BrandId, BrandName, SeriesId, SeriesName, CityId, CityName, GroupLogo, 
    GroupName, GroupQRCode, GroupQRCode3, Status, Type, SortId, is_del, Modified_STime, 
    Created_STime, RV
    from EnterpriseGroup
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select Id, BrandId, BrandName, SeriesId, SeriesName, CityId, CityName, GroupLogo, 
    GroupName, GroupQRCode, GroupQRCode3, Status, Type, SortId, is_del, Modified_STime, 
    Created_STime, RV
    from EnterpriseGroup
  </select>
  <select id="selectBySeriesId" resultMap="BaseResultMap">
    select * from EnterpriseGroup where SeriesId = #{seriesId} and Status = 1 and is_del = 0
    order by SortId desc
  </select>
</mapper>