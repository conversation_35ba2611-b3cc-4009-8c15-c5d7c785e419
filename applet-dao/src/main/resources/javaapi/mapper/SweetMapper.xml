<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.autohome.applet.dao.javaapi.mapper.SweetMapper">
    <resultMap id="BaseResultMap" type="com.autohome.applet.dao.javaapi.model.Sweet">
        <id column="SweetId" jdbcType="INTEGER" property="sweetid"/>
        <result column="SweetName" jdbcType="NVARCHAR" property="sweetname"/>
        <result column="ClientType" jdbcType="INTEGER" property="clienttype"/>
        <result column="JumpTo" jdbcType="VARCHAR" property="jumpto"/>
        <result column="Img" jdbcType="VARCHAR" property="img"/>
        <result column="StartTime" jdbcType="TIMESTAMP" property="starttime"/>
        <result column="EndTime" jdbcType="TIMESTAMP" property="endtime"/>
        <result column="SortNum" jdbcType="INTEGER" property="sortnum"/>
        <result column="Status" jdbcType="INTEGER" property="status"/>
        <result column="Recycled" jdbcType="BIT" property="recycled"/>
        <result column="Modified_STime" jdbcType="TIMESTAMP" property="modifiedStime"/>
        <result column="Created_STime" jdbcType="TIMESTAMP" property="createdStime"/>
        <result column="JumpType" jdbcType="INTEGER" property="jumptype"/>
        <result column="OtherAppid" jdbcType="VARCHAR" property="otherappid"/>
        <result column="ReportPoint" jdbcType="VARCHAR" property="reportpoint"/>
        <result column="ShareDesc" jdbcType="VARCHAR" property="sharedesc"/>
        <result column="ShareImg" jdbcType="VARCHAR" property="shareimg"/>
        <result column="ContentType" jdbcType="INTEGER" property="contenttype"/>
        <result column="ShowPoint" jdbcType="VARCHAR" property="showpoint"/>
        <result column="Ext" jdbcType="NVARCHAR" property="ext"/>
        <result column="NeedLogin" jdbcType="INTEGER" property="needlogin"/>
        <result column="CategoryPart" jdbcType="INTEGER" property="categorypart"/>
        <result column="ForceSort" jdbcType="INTEGER" property="forcesort"/>
    </resultMap>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select SweetId,
               SweetName,
               ClientType,
               JumpTo,
               Img,
               StartTime,
               EndTime,
               SortNum,
               Status,
               Recycled,
               Modified_STime,
               Created_STime,
               JumpType,
               OtherAppid,
               ReportPoint,
               ShareDesc,
               ShareImg,
               ContentType,
               ShowPoint,
               Ext,
               NeedLogin,
               CategoryPart,
               RV,
               ForceSort
        from Sweet
        where SweetId = #{sweetid,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select SweetId,
               SweetName,
               ClientType,
               JumpTo,
               Img,
               StartTime,
               EndTime,
               SortNum,
               Status,
               Recycled,
               Modified_STime,
               Created_STime,
               JumpType,
               OtherAppid,
               ReportPoint,
               ShareDesc,
               ShareImg,
               ContentType,
               ShowPoint,
               Ext,
               NeedLogin,
               CategoryPart,
               RV,
               ForceSort
        from Sweet
    </select>
    <select id="getLastForClientType" resultMap="BaseResultMap">
        select top 1 * from Sweet where ClientType = #{clientType,jdbcType=INTEGER}
        order by SortNum desc, EndTime
    </select>
    <select id="getHomeFeed7" resultMap="BaseResultMap">
        select * from Sweet with(nolock)
        where ClientType = 107 and SortNum = 7 and ForceSort = 1 and Recycled = 0 and status = 0
    </select>

    <update id="resetSweet7All">
        update Sweet set status = 1
        where ClientType = 107 and SortNum = 7 and ForceSort = 1 and Recycled = 0 and status = 0
    </update>

    <select id="getHomeFocus2" resultMap="BaseResultMap">
        select * from Sweet with(nolock)
        where ClientType = 11 and SortNum = 2 and ForceSort = 1 and Recycled = 0 and status = 0
    </select>

    <update id="resetSweet2All">
        update Sweet set status = 1
        where ClientType = 11 and SortNum = 2 and ForceSort = 1 and Recycled = 0 and status = 0
    </update>

    <select id="getDetailAd" resultMap="BaseResultMap">
        select * from Sweet where ClientType = #{clientType} and Recycled = 0
    </select>
    <update id="del">
        update Sweet set Recycled = 1 where SweetId = #{sweetId}
    </update>
    <update id="resetSweet">
        update Sweet set SweetName      = #{sweetname,jdbcType=NVARCHAR},
                         Img            = #{img,jdbcType=VARCHAR},
                         JumpType       = #{jumptype,jdbcType=INTEGER},
                         JumpTo         = #{jumpto,jdbcType=VARCHAR},
                         ReportPoint    = #{reportpoint,jdbcType=VARCHAR},
                         ShowPoint      = #{showpoint,jdbcType=VARCHAR},
                         StartTime      = #{starttime,jdbcType=TIMESTAMP},
                         EndTime        = #{endtime,jdbcType=TIMESTAMP},
                         Created_STime = #{createdStime,jdbcType=TIMESTAMP},
                         Modified_STime = #{modifiedStime,jdbcType=TIMESTAMP}
        where SweetId = #{sweetid,jdbcType=INTEGER}
    </update>

    <update id="updateById">
        update Sweet set status = 0 where SweetId = #{sweetId}
    </update>
</mapper>