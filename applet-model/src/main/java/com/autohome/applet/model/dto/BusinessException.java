package com.autohome.applet.model.dto;

public class BusinessException extends RuntimeException{

	private static final long serialVersionUID = 4102168831099201160L;
	private int code;
    private String message;

    public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public BusinessException(String message, int code) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(String message) {
        this(message, 0);
    }
}
