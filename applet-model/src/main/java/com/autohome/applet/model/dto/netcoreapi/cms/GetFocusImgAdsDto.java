package com.autohome.applet.model.dto.netcoreapi.cms;

import lombok.Data;

import java.util.List;

@Data
public class GetFocusImgAdsDto {
    private List<LinkData> data;
    private Meta meta;
    private String type;
    private String version;
    //private Object view;

    @Data
    public static class LinkData {
        private Link link;
        private Material material;

        @Data
        public static class Link{
            private String src;
            private String desc;
            private List<String> reports;
        }

        @Data
        public static class Material{
            private List<MaterialItem> img;
        }

        @Data
        public static class MaterialItem{
            private String src;
        }
    }

    // Meta class
    @Data
    public static class Meta {
        private EngineInfo engineInfo;
        //private Object extInfo;
        private PageInfo pageInfo;
        private PosInfo posInfo;
        private ReportInfo reportInfo;
        private TplInfo tplInfo;

        // EngineInfo class
        @Data
        public static class EngineInfo {
            private String creativeId;
            private String dspname;
            private String endTime;
            private String engine;
            private String ishavead;
            private String pubTime;
        }

        // PageInfo class
        @Data
        public static class PageInfo {
            private String keywords;
            private String wd;
            private String wdc;
            private String wdt;
            private String deviceid;
        }

        // PosInfo class
        @Data
        public static class PosInfo {
            private String height;
            private String iconflag;
            private String psid;
            private String tag;
            private String width;
        }

        @Data
        // ReportInfo class
        public static class ReportInfo {
            private Object closeurl;
            private String pvid;
            private Object pvurls;
            private List<String> rdpvurls;
            private List<String> rdrqurls;
        }

        // TplInfo class
        @Data
        public static class TplInfo {
            private String height;
            private String templateId;
            private String width;
        }
    }
}
