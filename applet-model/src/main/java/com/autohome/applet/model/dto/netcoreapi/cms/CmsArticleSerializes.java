package com.autohome.applet.model.dto.netcoreapi.cms;

public class CmsArticleSerializes {


    private int endPage;
    private long Id;
    private String img;
    private boolean ispublish;
    private boolean isSerializOver;
    private String MobileMemo;
    private int orders;
    private String publishTime;
    private int startPage;
    private String sTitle;
    private String title;

    public void setEndPage(int endPage) {
        this.endPage = endPage;
    }

    public int getEndPage() {
        return endPage;
    }

    public void setId(long Id) {
        this.Id = Id;
    }

    public long getId() {
        return Id;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getImg() {
        return img;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setIsSerializOver(boolean isSerializOver) {
        this.isSerializOver = isSerializOver;
    }

    public boolean getIsSerializOver() {
        return isSerializOver;
    }

    public void setMobileMemo(String MobileMemo) {
        this.MobileMemo = MobileMemo;
    }

    public String getMobileMemo() {
        return MobileMemo;
    }

    public void setOrders(int orders) {
        this.orders = orders;
    }

    public int getOrders() {
        return orders;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setStartPage(int startPage) {
        this.startPage = startPage;
    }

    public int getStartPage() {
        return startPage;
    }

    public void setSTitle(String sTitle) {
        this.sTitle = sTitle;
    }

    public String getSTitle() {
        return sTitle;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

}