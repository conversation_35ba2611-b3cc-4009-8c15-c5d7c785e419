package com.autohome.applet.model.dto.carlibrary;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ElectricSpecDto {

    @JsonProperty("seriesid")
    private int seriesId;

    @JsonProperty("seriesname")
    private String seriesName;

    @JsonProperty("specitems")
    private List<ElectricSpecItem> specItems;

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public List<ElectricSpecItem> getSpecItems() {
        return specItems;
    }

    public void setSpecItems(List<ElectricSpecItem> specItems) {
        this.specItems = specItems;
    }
}
