package com.autohome.applet.model.dto.qqupload;

/**
 * 同步腾讯品牌卡
 * */
import lombok.Data;

@Data
public class BrandInfoUploadDtoV3 {

    // 具体车系，必填标签。最多出现1次。最少1字节，最多512字节。
    private String key;
    private Display display;
    // 城市名称，必填，测试前几天我们每个品牌和车系全量城市输出，有测试数据后，会根据城市调整输出的品牌或车系
    private String location;


    @Data
    public static class Display {
        // PC端显示标题（品牌名称或厂商名称），必填标签。最多出现1次。最少1字节，最多1000字节。
        private String title;
        // 必要标签，必填标签。最多出现1次。最少0字节，最多1000字节。
        private String url;
        // 品牌名称，必填标签。最多出现1次。最少1字节，最多1000字节。
        private String brand;
        // 品牌国别，选填标签。最多出现1次。最少0字节，最多1000字节。
        private String nation;
//        // 品牌成立年份，选填标签。最多出现1次。最少0字节，最多1000字节。
//        private String buildyear;
//        // 品牌总部地址，与国别不是一个概念，选填标签。最多出现1次。最少0字节，最多1000字节。
//        private String zongbuadd;
        // 品牌在售车系，必填标签。最多出现1次。最少1字节，最多1000字节。 指定品牌下所有在售车系车系ID（在产在售、停产在售），加密处理
        private String onsaleseries;
        // 品牌车系，必填标签。最多出现1次。最少1字节，最多1000字节。 指定品牌下所有在售车系（在产在售、停产在售）
        private String carSeries;
        // 品牌车系热度值，值越大排名越靠前，必填标签。最多出现1次。最少1字节，最多1000字节。 所有输出车系按照之家热度值排序，热度最高此处热度值为1，依此类推
        private String carSerieshot;
        // 品牌厂商，必填标签。最多出现1次。最少1字节，最多1000字节。 每个输出车系对应的厂商名称
        private String subBrand;
        // 车系的主体，必填标签。最多出现1次。最少1字节，最多1000字节。 每个输出车系对应的级别
        private String carLevel;
        // 最多7位数 用于分组，必填标签。最多出现1次。最少1字节，最多1000字节。 每个输出车系对应的级别ID（可自定义）
        private String carLevelID;
        // 车系所属品牌的carlevellist,英文分号分隔，必填标签。最多出现1次。最少1字节，最多1000字节。 每个品牌下，所有在售车系所属车系级别list
        private String carlevellist;
        // 厂商指导价，必填标签。最多出现1次。最少1字节，最多1000字节。 每个输出车系对应的厂商指导价
        private String guidedPrice;
        // 无线端品牌或厂商logo，必填标签。最多出现1次。最少0字节，最多1000字节。 品牌logo  150*150
        private String wx_logo;
        // 无线端车系图片，显示用图，必填标签。最多出现1次。最少0字节，最多1000字节。 车系实拍图第一张  400*300
        private String wx_carPic;
        // 无线端车系落地页，必填标签。最多出现1次。最少0字节，最多1000字节。 H5车系综述页
        private String wx_carurl;
        // 车系是否可以VR看车，用于判断，0是不可以，1是可以，必填标签。最多出现1次。最少1字节，最多1000字节。 车系是否可以VR看车 0是不可以，1是可以
        private String vrviewID;
        // 无线端vr看车链接，必填标签。最多出现1次。最少0字节，最多1000字节。 如当前车系支持VR看车，则输出对应VR看车H5链接
        private String wx_vrviewurl;
        // 询底价url，必填标签。最多出现1次。最少0字节，最多1000字节。 当前车系对应H5查成交价页面
        private String minpriceurl;
        // 除车系所属厂商之外的同品牌其余厂商，选填标签。最多出现5次。
//        private Changshanglist changshanglist;
        // 品牌概况页面url（综合页），选填标签。最多出现1次。最少0字节，最多2048字节。 品牌车系列表页URL
//        private String summaryUrl;
//        // 品牌文章页，选填标签。最多出现1次。最少0字节，最多2048字节。
//        private String articleListUrl;
    }

    @Data
    public static class Changshanglist {
        // 除车系所属厂商之外的同品牌其余厂商名字，选填标签。最多出现1次。最少0字节，最多1000字节。
        private String changshangname;
        // 除车系所属厂商之外的同品牌其余厂商图片，选填标签。最多出现1次。最少0字节，最多1000字节。
        private String changshangpic;
    }
}
