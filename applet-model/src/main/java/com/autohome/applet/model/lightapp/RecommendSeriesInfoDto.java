package com.autohome.applet.model.lightapp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecommendSeriesInfoDto {
    private int seriesId;
    private int specId;
    private String seriesName;
    private String peopleCount;
    /**
     * 该城市上月最低售价
     * */
    private String dealPrice;
    private String minPrice;
    private String downPrice;
    private String imgUrl;
    private Integer belongId;
    private Long leadsTaskId;
    private Integer dealerType;
    /**
     * 线索来源
     * 枚举 LeadFromEnum
     * */
    private Integer from;
}
