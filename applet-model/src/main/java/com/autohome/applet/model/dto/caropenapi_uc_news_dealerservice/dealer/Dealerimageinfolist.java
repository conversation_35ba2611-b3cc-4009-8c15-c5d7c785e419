package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

public class Dealerimageinfolist {

    @JsonProperty("ImageId")
    private int imageid;
    @JsonProperty("ImageUrl")
    private String imageurl;

    public void setImageid(int imageid) {
        this.imageid = imageid;
    }

    public int getImageid() {
        return imageid;
    }

    public void setImageurl(String imageurl) {
        this.imageurl = imageurl;
    }

    public String getImageurl() {
        if (StringUtil.isNotNullAndWhiteSpace(imageurl))
            return imageurl.replace("http://", "https://");
        else
            return imageurl;

    }

}
