package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;

import java.util.Date;
import java.util.List;

/**
 * Created by hanshanfeng on 2019/4/29.
 */
public class FctContents {
    private int took;
    private int allcount;
    private List<ContentList> list;

    public int getTook() {
        return took;
    }

    public void setTook(int took) {
        this.took = took;
    }

    public int getAllcount() {
        return allcount;
    }

    public void setAllcount(int allcount) {
        this.allcount = allcount;
    }

    public List<ContentList> getList() {
        return list;
    }

    public void setList(List<ContentList> list) {
        this.list = list;
    }

    public static class ContentList {

        //        @JsonProperty("contentOriginal")
//        private String contentoriginal;

        private int biztype;//liu
        //        @JsonProperty("cms_subjectId")
//        private int cmsSubjectid;
//        @JsonProperty("recommendTime")
//        private String recommendtime;
//        private List<Integer> levelids;
//        @JsonProperty("isCloseComment")
//        private boolean isclosecomment;
//        @JsonProperty("dbUpdateTime")
//        private String dbupdatetime;
//        @JsonProperty("authorImg")
//        private String authorimg;
//        @JsonProperty("cms_serializeStart")
//        private int cmsSerializestart;

        private String title;//liu
        //        @JsonProperty("yearIds")
//        private List<Integer> yearids;
//        @JsonProperty("brandIds")
//        private List<Integer> brandids;
//        @JsonProperty("seriesColumnId")
//        private int seriescolumnid;
//        @JsonProperty("seriesIds")
//        private List<Integer> seriesids;
//        @JsonProperty("tagNames")
//        private List<String> tagnames;
//        @JsonProperty("factoryIds")
//        private List<Integer> factoryids;
//        @JsonProperty("cms_kind")
//        private int cmsKind;
//        @JsonProperty("cms_className1")
//        private String cmsClassname1;
//        @JsonProperty("cms_className2")
//        private String cmsClassname2;
//        private int ispublish;

        private int bizid;//liu
        //        @JsonProperty("dbCreateTime")
//        private String dbcreatetime;
//        @JsonProperty("imgUrl_16x9")
//        private String imgurl16x9;
//        @JsonProperty("viewCount")
//        private int viewcount;
//        private boolean energy;
//        @JsonProperty("memberId")
//        private String memberid;
//        private String summary;

        private String publishtime;//liu
        //        @JsonProperty("smallTitle")
//        private String smalltitle;
//        private String images;
//        @JsonProperty("pcUrl")
//        private String pcurl;
//        @JsonProperty("isDelete")
//        private int isdelete;
//        @JsonProperty("cms_isPhone")
//        private boolean cmsIsphone;
//        @JsonProperty("authorId")
//        private int authorid;
//        @JsonProperty("cms_classId2")
//        private int cmsClassid2;

        private String imgurl4x3;
        //        @JsonProperty("cms_classId1")
//        private int cmsClassid1;

        private int replycount;//liu

        private List<String> cmsAppimages;//liu
        private String img;
        //        @JsonProperty("createTime")
//        private String createtime;
//        @JsonProperty("specIds")
//        private List<Integer> specids;

        private String authorname;//liu
        //        @JsonProperty("showImageList")
//        private boolean showimagelist;
//        @JsonProperty("cms_serializeOrders")
//        private int cmsSerializeorders;
//        @JsonProperty("lastUpdateTime")
//        private String lastupdatetime;
        //蓝V
        private String lanVicon;

        public String getImg() {
            if (StringUtil.isBlank(this.imgurl4x3)) {
                return this.img;
            } else {
                return this.imgurl4x3;
            }
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getLanVicon() {
            if (biztype != 101) {
                return "https://chejiahao.autohome.com.cn/images/v/<EMAIL>";
            }
            return lanVicon;
        }

        public String getImgurl4x3() {
            return imgurl4x3;
        }

        public void setImgurl4x3(String imgurl4x3) {
            this.imgurl4x3 = imgurl4x3;
        }

        public void setLanVicon(String lanVicon) {
            this.lanVicon = lanVicon;
        }

        public int getBiztype() {
            return biztype;
        }

        public void setBiztype(int biztype) {
            this.biztype = biztype;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getBizid() {
            return bizid;
        }

        public void setBizid(int bizid) {
            this.bizid = bizid;
        }

        public String getPublishtime() {
            if (publishtime.indexOf("Date") > 0) {
                return FormatDate.dateToStr(new Date(Long.parseLong(publishtime.replace("/Date(", "").replace(")/", ""))), "yyyy-MM-dd HH:mm:ss");
            }
            return publishtime;
        }

        public void setPublishtime(String publishtime) {
            this.publishtime = publishtime;
        }

        public int getReplycount() {
            return replycount;
        }

        public void setReplycount(int replycount) {
            this.replycount = replycount;
        }

        public List<String> getCmsAppimages() {
            return cmsAppimages;
        }

        public void setCmsAppimages(List<String> cmsAppimages) {
            this.cmsAppimages = cmsAppimages;
        }

        public String getAuthorname() {
            return authorname;
        }

        public void setAuthorname(String authorname) {
            this.authorname = authorname;
        }
    }
}
