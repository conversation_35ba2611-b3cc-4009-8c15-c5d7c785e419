package com.autohome.applet.model.dto.honor;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AnalyzeCarResult {

    private List<Cars> cars;

    @JsonProperty("img_url")
    private String imgUrl;

    public List<Cars> getCars() {
        return cars;
    }

    public void setCars(List<Cars> cars) {
        this.cars = cars;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public static class Cars {

        // 车在图像中的位置，如果请求的字段中position存在且不为"",则不返回该字段
        private List<Object> position;

        // 检测此位置为车的置信度，如果请求的字段中position存在且不为""，则不返回该字段
        @JsonProperty("detect_prob")
        private float detectProb;

        // 检测所用时间，如果请求的字段中position存在且不为""，则不返回该字段
        @JsonProperty("detect_time")
        private float detectTime;

        // 识别所用时间
        @JsonProperty("recog_time")
        private float recogTime;

        // 唯一的 PVID
        @JsonProperty("pvid")
        private String pvId;

        // 一个位置的识别结果top-n(0 < n < 6)
        private List<Car> car;

        public List<Object> getPosition() {
            return position;
        }

        public void setPosition(List<Object> position) {
            this.position = position;
        }

        public float getDetectProb() {
            return detectProb;
        }

        public void setDetectProb(float detectProb) {
            this.detectProb = detectProb;
        }

        public float getDetectTime() {
            return detectTime;
        }

        public void setDetectTime(float detectTime) {
            this.detectTime = detectTime;
        }

        public float getRecogTime() {
            return recogTime;
        }

        public void setRecogTime(float recogTime) {
            this.recogTime = recogTime;
        }

        public String getPvId() {
            return pvId;
        }

        public void setPvId(String pvId) {
            this.pvId = pvId;
        }

        public List<Car> getCar() {
            return car;
        }

        public void setCar(List<Car> car) {
            this.car = car;
        }
    }

    public static class Car {

        public static final int PUBLIC_STATE_UN_PUBLIC = 0;
        public static final int PUBLIC_STATE_ON_SALE = 1;
        public static final int PUBLIC_STATE_STOP_SALE = 2;

        // 识别结果(车系)置信度
        private float confidence;

        @JsonProperty("factory_id")
        private int factoryId;

        @JsonProperty("factory_name")
        private String factoryName;

        @JsonProperty("brand_id")
        private int brandId;

        @JsonProperty("brand_name")
        private String brandName;

        @JsonProperty("series_id")
        private int seriesId;

        @JsonProperty("series_name")
        private String seriesName;

        // 0 未上市 1 在售 2 停售
        @JsonProperty("is_public")
        private int isPublic;

        public String getSaleState() {
            if (isPublic == PUBLIC_STATE_UN_PUBLIC) {
                return "即将上市";
            } else if (isPublic == PUBLIC_STATE_ON_SALE) {
                return "售卖中";
            } else if (isPublic == PUBLIC_STATE_STOP_SALE) {
                return "已停售";
            } else {
                return "未知";
            }
        }

        public float getConfidence() {
            return confidence;
        }

        public void setConfidence(float confidence) {
            this.confidence = confidence;
        }

        public int getFactoryId() {
            return factoryId;
        }

        public void setFactoryId(int factoryId) {
            this.factoryId = factoryId;
        }

        public String getFactoryName() {
            return factoryName;
        }

        public void setFactoryName(String factoryName) {
            this.factoryName = factoryName;
        }

        public int getBrandId() {
            return brandId;
        }

        public void setBrandId(int brandId) {
            this.brandId = brandId;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public int getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(int seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesName() {
            return seriesName;
        }

        public void setSeriesName(String seriesName) {
            this.seriesName = seriesName;
        }

        public int getIsPublic() {
            return isPublic;
        }

        public void setIsPublic(int isPublic) {
            this.isPublic = isPublic;
        }
    }
}
