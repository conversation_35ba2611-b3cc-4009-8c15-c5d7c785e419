package com.autohome.applet.model.dto.cms;

public class ChejiahaoDetailResultTagsnew {
    private String created_stime;
    private int id;
    private boolean is_del;
    private boolean ishot;
    private boolean ismultiplechoice;
    private String modified_stime;
    private String newkeywordids;
    private String newkeywords;
    private int oldtagid;
    private String oldtagname;
    private int parentid;
    private String parentname;
    private int rows;
    private boolean status;
    private String tagname;
    private int tagsort;

    public void setCreated_stime(String created_stime) {
        this.created_stime = created_stime;
    }

    public String getCreated_stime() {
        return created_stime;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setIs_del(boolean is_del) {
        this.is_del = is_del;
    }

    public boolean getIs_del() {
        return is_del;
    }

    public void setIshot(boolean ishot) {
        this.ishot = ishot;
    }

    public boolean getIshot() {
        return ishot;
    }

    public void setIsmultiplechoice(boolean ismultiplechoice) {
        this.ismultiplechoice = ismultiplechoice;
    }

    public boolean getIsmultiplechoice() {
        return ismultiplechoice;
    }

    public void setModified_stime(String modified_stime) {
        this.modified_stime = modified_stime;
    }

    public String getModified_stime() {
        return modified_stime;
    }

    public void setNewkeywordids(String newkeywordids) {
        this.newkeywordids = newkeywordids;
    }

    public String getNewkeywordids() {
        return newkeywordids;
    }

    public void setNewkeywords(String newkeywords) {
        this.newkeywords = newkeywords;
    }

    public String getNewkeywords() {
        return newkeywords;
    }

    public void setOldtagid(int oldtagid) {
        this.oldtagid = oldtagid;
    }

    public int getOldtagid() {
        return oldtagid;
    }

    public void setOldtagname(String oldtagname) {
        this.oldtagname = oldtagname;
    }

    public String getOldtagname() {
        return oldtagname;
    }

    public void setParentid(int parentid) {
        this.parentid = parentid;
    }

    public int getParentid() {
        return parentid;
    }

    public void setParentname(String parentname) {
        this.parentname = parentname;
    }

    public String getParentname() {
        return parentname;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public int getRows() {
        return rows;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public boolean getStatus() {
        return status;
    }

    public void setTagname(String tagname) {
        this.tagname = tagname;
    }

    public String getTagname() {
        return tagname;
    }

    public void setTagsort(int tagsort) {
        this.tagsort = tagsort;
    }

    public int getTagsort() {
        return tagsort;
    }

}