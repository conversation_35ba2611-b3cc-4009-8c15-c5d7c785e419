package com.autohome.applet.model.dto.seriesbasecardinfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 车系综述页 - tab栏目（包含一级和二级栏目）
 * 只显示由数据的栏目
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SeriesTabInfoDto {
    private String tabkey;
    private String tabtitle;
//    private String taburl;
    private boolean show;
    private String pvName;
    private String pvEvent;
    private String clickEvent;
    private List<SecondSeriesTab> secondSeriesTabList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SecondSeriesTab{
        private String tabkey;
        private String tabtitle;
        private int ext;
        private boolean show;
    }

    public static List<SeriesTabInfoDto> getSeriesTabInfoDtos() {
        List<SeriesTabInfoDto> seriesTabInfoDtoList = new ArrayList<>();
        seriesTabInfoDtoList.add(new SeriesTabInfoDto("newcar","车型",true,"view/series/index/model","auto_mini_series_channel_news_pv","autowx_selection_series_information_click", new ArrayList<>()));

        List<SecondSeriesTab> secondSeriesTabs1 = new ArrayList<>();
        secondSeriesTabs1.add(new SecondSeriesTab("recommend", "全部", 0, true));
        secondSeriesTabs1.add(new SecondSeriesTab("news", "车闻", 10001, true));
        secondSeriesTabs1.add(new SecondSeriesTab("review", "评测", 10002, true));
        secondSeriesTabs1.add(new SecondSeriesTab("original", "精选", 100001, true));
        secondSeriesTabs1.add(new SecondSeriesTab("video", "视频", 100002, true));
        seriesTabInfoDtoList.add(new SeriesTabInfoDto("article","资讯",false,"view/series/index/information","auto_mini_series_channel_littlevideo_pv","chexi_video_tab_click", secondSeriesTabs1));

        List<SecondSeriesTab> secondSeriesTabs2 = new ArrayList<>();
        secondSeriesTabs2.add(new SecondSeriesTab("recommend", "推荐", 1, false));
        secondSeriesTabs2.add(new SecondSeriesTab("original", "原创评测", 2, false));
        seriesTabInfoDtoList.add(new SeriesTabInfoDto("video","视频",false,"view/series/index/video","auto_mini_series_channel_newcar_pv","autowx_selection_series_model_click", secondSeriesTabs2));

        return seriesTabInfoDtoList;
    }

    public static List<SeriesTabInfoDto> getSeriesTabInfoDtosForCity() {
        List<SeriesTabInfoDto> seriesTabInfoDtoList = new ArrayList<>();

        /**
         * 二手车没有内网访问的接口，暂时先不处理
         * */
//        List<SecondSeriesTab> secondSeriesTabs3 = new ArrayList<>();
//        secondSeriesTabs3.add(new SecondSeriesTab("zonghe", "综合排序", 0, true));
//        secondSeriesTabs3.add(new SecondSeriesTab("pricelow", "价格低", 2, true));
//        secondSeriesTabs3.add(new SecondSeriesTab("pricehigh", "价格高", 1, true));
//        secondSeriesTabs3.add(new SecondSeriesTab("zuixinfabu", "最新发布", 4, true));
//        seriesTabInfoDtoList.add(new SeriesTabInfoDto("ershou","二手车",false,"view/series/index/ershou","auto_mini_series_channel_usedcar_pv","chexi_ershouche_tab_click", secondSeriesTabs3));

        seriesTabInfoDtoList.add(new SeriesTabInfoDto("tongjiche","同级车",false,"view/series/index/tongjiche","auto_series_tj_list_pv","auto_series_tj_tab_click", new ArrayList<>()));
        return seriesTabInfoDtoList;
    }
}
