package com.autohome.applet.model.dto.car;

import java.util.List;

/**
 * 上市车管理v2.0查询接口
 * WIKI:https://doc.autohome.com.cn/docapi/page/share/share_zuODDurHY8
 * */
public class MarketCarDTO {
    /**
     * 总条数
     * */
    private int rowCount;
    /**
     * 页数
     * */
    private int pageCount;
    /**
     * 当前页
     * */
    private int pageIndex;
    /**
     * 结果集
     */
    List<MarketCarItem> items;

    public int getRowCount() {
        return rowCount;
    }

    public void setRowCount(int rowCount) {
        this.rowCount = rowCount;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public List<MarketCarItem> getItems() {
        return items;
    }

    public void setItems(List<MarketCarItem> items) {
        this.items = items;
    }

    public static class MarketCarItem {
        // 参数名：id，类型：Integer，说明：主键id
        private Integer id;

        // 参数名：marketType，类型：Integer，说明：上市类型，1上市，2首发
        private Integer marketType;

        // 参数名：seriesId，类型：Integer，说明：车系id
        private Integer seriesId;

        // 参数名：seriesName，类型：String，说明：车系名称
        private String seriesName;

        // 参数名：seriesImage，类型：String，说明：车系图
        private String seriesImage;

        // 参数名：brandId，类型：String，说明：品牌id
        private Integer brandId;

        // 参数名：factoryId，类型：Integer，说明：厂商id
        private Integer factoryId;

        // 参数名：factory，类型：String，说明：厂商名称
        private String factory;

        // 参数名：marketName，类型：String，说明：标题
        private String marketName;

        // 参数名：newCarType，类型：Integer，说明：新车类型，枚举参考：1:全新车系，2:车系换代，4:新加车型，5:中期改款，6:小改款
        private Integer newCarType;

        // 参数名：isMarket，类型：Integer，说明：是否上市，0否，1是
        private Integer isMarket;

        // 参数名：isEnergy，类型：Integer，说明：是否新能源，0否，1是
        private Integer isEnergy;

        // 参数名：seriesLevelId，类型：Integer，说明：车系级别id
        private Integer seriesLevelId;

        // 参数名：seriesLevelName，类型：String，说明：车系级别名称
        private String seriesLevelName;

        // 参数名：eventLevel，类型：Integer，说明：评级，枚举参考：0: 未评级，1：S，2：A，3：B，4：C
        private Integer eventLevel;

        // 参数名：activeTimeType，类型：String，说明：上市时间类型
        private String activeTimeType;

        // 参数名：activeBeginTime，类型：String，说明：上市或者首发时间，格式：yyyy-MM-dd HH:mm:ss
        private String activeBeginTime;

        // 参数名：activeTimeDesc，类型：String，说明：时间描述
        private String activeTimeDesc;

        // 参数名：planMarket，类型：Integer，说明：计划上市区间，枚举如下：1月内，2上旬，3中旬，4下旬
        private Integer planMarket;

        // 参数名：remark，类型：String，说明：备注
        private String remark;

        // 参数名：lightpoint，类型：String，说明：新车卖点
        private String lightpoint;

        // 参数名：contentList，类型：list，说明：关联内容，具体格式参考如下
        private List<MarketCarContent> contentList;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public Integer getMarketType() {
            return marketType;
        }

        public void setMarketType(Integer marketType) {
            this.marketType = marketType;
        }

        public Integer getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(Integer seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesName() {
            return seriesName;
        }

        public void setSeriesName(String seriesName) {
            this.seriesName = seriesName;
        }

        public String getSeriesImage() {
            return seriesImage;
        }

        public void setSeriesImage(String seriesImage) {
            this.seriesImage = seriesImage;
        }

        public Integer getBrandId() {
            return brandId;
        }

        public void setBrandId(Integer brandId) {
            this.brandId = brandId;
        }

        public Integer getFactoryId() {
            return factoryId;
        }

        public void setFactoryId(Integer factoryId) {
            this.factoryId = factoryId;
        }

        public String getFactory() {
            return factory;
        }

        public void setFactory(String factory) {
            this.factory = factory;
        }

        public String getMarketName() {
            return marketName;
        }

        public void setMarketName(String marketName) {
            this.marketName = marketName;
        }

        public Integer getNewCarType() {
            return newCarType;
        }

        public void setNewCarType(Integer newCarType) {
            this.newCarType = newCarType;
        }

        public Integer getIsMarket() {
            return isMarket;
        }

        public void setIsMarket(Integer isMarket) {
            this.isMarket = isMarket;
        }

        public Integer getIsEnergy() {
            return isEnergy;
        }

        public void setIsEnergy(Integer isEnergy) {
            this.isEnergy = isEnergy;
        }

        public Integer getSeriesLevelId() {
            return seriesLevelId;
        }

        public void setSeriesLevelId(Integer seriesLevelId) {
            this.seriesLevelId = seriesLevelId;
        }

        public String getSeriesLevelName() {
            return seriesLevelName;
        }

        public void setSeriesLevelName(String seriesLevelName) {
            this.seriesLevelName = seriesLevelName;
        }

        public Integer getEventLevel() {
            return eventLevel == null ? 0 : eventLevel;
        }

        public void setEventLevel(Integer eventLevel) {
            this.eventLevel = eventLevel;
        }

        public String getActiveTimeType() {
            return activeTimeType;
        }

        public void setActiveTimeType(String activeTimeType) {
            this.activeTimeType = activeTimeType;
        }

        public String getActiveBeginTime() {
            return activeBeginTime;
        }

        public void setActiveBeginTime(String activeBeginTime) {
            this.activeBeginTime = activeBeginTime;
        }

        public String getActiveTimeDesc() {
            return activeTimeDesc;
        }

        public void setActiveTimeDesc(String activeTimeDesc) {
            this.activeTimeDesc = activeTimeDesc;
        }

        public Integer getPlanMarket() {
            return planMarket == null ? 0 : planMarket;
        }

        public void setPlanMarket(Integer planMarket) {
            this.planMarket = planMarket;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getLightpoint() {
            return lightpoint;
        }

        public void setLightpoint(String lightpoint) {
            this.lightpoint = lightpoint;
        }

        public List<MarketCarContent> getContentList() {
            return contentList;
        }

        public void setContentList(List<MarketCarContent> contentList) {
            this.contentList = contentList;
        }
    }

    public static class MarketCarContent {
        // 参数名：contentId，类型：Integer，说明：内容id
        private Integer contentId;

        // 参数名：contentType，类型：String，说明：内容类型，枚举参考：文章、视频
        private String contentType;

        // 参数名：title，类型：String，说明：标题
        private String title;

        // 参数名：publishTime，类型：String，说明：发布时间
        private String publishTime;

        // 参数名：publishUser，类型：String，说明：发布人
        private String publishUser;

        // 参数名：jumpUrl，类型：String，说明：跳转地址
        private String jumpUrl;

        // 参数名：coverImg，类型：String，说明：代表图
        private String coverImg;

        public Integer getContentId() {
            return contentId;
        }

        public void setContentId(Integer contentId) {
            this.contentId = contentId;
        }

        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getPublishTime() {
            return publishTime;
        }

        public void setPublishTime(String publishTime) {
            this.publishTime = publishTime;
        }

        public String getPublishUser() {
            return publishUser;
        }

        public void setPublishUser(String publishUser) {
            this.publishUser = publishUser;
        }

        public String getJumpUrl() {
            return jumpUrl;
        }

        public void setJumpUrl(String jumpUrl) {
            this.jumpUrl = jumpUrl;
        }

        public String getCoverImg() {
            return coverImg;
        }

        public void setCoverImg(String coverImg) {
            this.coverImg = coverImg;
        }
    }

}
