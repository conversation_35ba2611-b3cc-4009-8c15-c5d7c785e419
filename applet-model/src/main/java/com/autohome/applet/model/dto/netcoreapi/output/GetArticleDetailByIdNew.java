package com.autohome.applet.model.dto.netcoreapi.output;

public class GetArticleDetailByIdNew {
    private int start;

    private int titletype;

    private int id;

    private String title;

    private String img;

    private int authorid;

    private String authorname;

    private String avatar;

    private String publishtime;

    private String content;

    private int class1;

    private String class1name;

    private int lookcount;

    private int replycount;

    private int likecount;

    private boolean ispublish;

    private String newkeywords;

    private String description;

    private String articleNavigation;

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getTitletype() {
        return titletype;
    }

    public void setTitletype(int titletype) {
        this.titletype = titletype;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getAuthorid() {
        return authorid;
    }

    public void setAuthorid(int authorid) {
        this.authorid = authorid;
    }

    public String getAuthorname() {
        return authorname;
    }

    public void setAuthorname(String authorname) {
        this.authorname = authorname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getClass1() {
        return class1;
    }

    public void setClass1(int class1) {
        this.class1 = class1;
    }

    public String getClass1name() {
        return class1name;
    }

    public void setClass1name(String class1name) {
        this.class1name = class1name;
    }

    public int getLookcount() {
        return lookcount;
    }

    public void setLookcount(int lookcount) {
        this.lookcount = lookcount;
    }

    public int getReplycount() {
        return replycount;
    }

    public void setReplycount(int replycount) {
        this.replycount = replycount;
    }

    public int getLikecount() {
        return likecount;
    }

    public void setLikecount(int likecount) {
        this.likecount = likecount;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public String getNewkeywords() {
        return newkeywords;
    }

    public void setNewkeywords(String newkeywords) {
        this.newkeywords = newkeywords;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setArticleNavigation(String articleNavigation) {
        this.articleNavigation = articleNavigation;
    }

    public String getArticleNavigation() {
        return articleNavigation;

    }
}