package com.autohome.applet.model.dto;

import lombok.Data;

@Data
public class CodeMsg<T> {
    private int code = 0;
    private String msg;
    private T data;
    private T result;

    public CodeMsg(T data) {
        this(0, null, data);
    }

    public CodeMsg(int code, String msg) {
        this(code, msg, null);
    }

    public CodeMsg(int code, String msg, T result) {
        this.code = code;
        this.msg = msg;
        this.data = result;
    }

    public CodeMsg() {
        this(0,null,null);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public static <T> CodeMsg<T> buildSuccessResult(T data) {
        CodeMsg<T> rt = new CodeMsg<>();
        rt.setCode(0);
        rt.setData(data);
        rt.setMsg("success");
        return rt;
    }

    public static <T> CodeMsg<T> buildErrorResult(int code, String msg) {
        CodeMsg<T> rt = new CodeMsg<>();
        rt.setCode(code);
        rt.setMsg(msg);
        return rt;
    }

}

