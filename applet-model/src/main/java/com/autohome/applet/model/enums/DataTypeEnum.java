package com.autohome.applet.model.enums;

/**
 * 资讯数据类型枚举
 * <AUTHOR>
 * @date 2017年7月31日 下午3:47:38
 * @since
 */
public enum DataTypeEnum {
	
	//文章
	Article(1),
	
	//说客
	ShuoKe(2),
	
	//视频
    Video(3),
    
    //焦点图
    FocusImg(4),
    
    //帖子
    Topic(5),
    
    //图库
    Pic(6),
    
    //快报
    FastNews(7),
    
    //电台（目前客户端占用）
    Radio(8),
    
    //二手车文章
    SecondHandCarArticle(9),
    
    //数据库中DataType=10 在此已申明被占用(虽然库中不存在此类数据)
    ArticleTuWen(10),
    
    //口碑  
    KouBei(11),

    //资讯平台长文 
    NewsPlatformArticle(12),
    
    //资讯平台短文
    NewsPlatformTextImage(13),

    //资讯平台短文视频
    NewsPlatformVideo(14),

    //资讯平台音频
    NewsPlatformAudio(15),

    //汽车之家电台
    NewsRadio(16),

    //直播-预告和直播中
    NewsLive(20),

    //直播-重播
    NewsReLive(21),

    //优创话题
    YCTopic(24),

    //资讯话题
    NewsTopic(26),

    //注：推荐列表的类型排序还是按上面来，99只是针对原创频道用的值
    //资讯类型-快报（全部快报数据）
    //fastnews=7 推荐到首页的，=96时，全部的快报数据-包括推荐到首页的
    FastNews_News(96),

    //资讯类型-直播-预告和直播中
    //newsLive=20 包含很多业务方的直播，但都从资讯接口提供，=97时，为资讯业务线自己的直播数据
    Live_News(97),

    //资讯类型-直播-重播
    //newsReLive=21 包含很多业务方的重播，但都从资讯接口提供，=98时，为资讯业务线自己的重播数据
    ReLive_News(98),

    //原创视频（此类型值针对原创频道应用，推荐列表的id定义还按上面排序）
    AutoVideo(99),

    //焦点图（新版）100
    //由于老版本焦点图没有区分获取的业务，所以新增焦点图支持类型时，老版本会有问题
    FocusImg_New(100),

    //焦点图（新版）100
    //由于老版本焦点图没有区分获取的业务，所以新增焦点图支持类型时，老版本会有问题
    FocusImg_Ori(101),
	
    //焦点图（840后 新版）102
    //由于老版本焦点图没有区分获取的业务，所以新增焦点图支持类型时，老版本会有问题
	FocusImg_Ori_New(102),

    //置顶 原创频道置顶
    TopNews_Ori(105),

    //快讯（10.21.0版本新增）对应其他项目中MediaTypeEnums中的700112的快讯
	FASTNEWS(106);
	
	private int value;
	
	public int getValue() {
		return value;
	}
	
    DataTypeEnum(int value) {
		this.value = value;
	}

    public static DataTypeEnum getDataType(int value){
        DataTypeEnum[] types = DataTypeEnum.values();
        for (int i = 0; i < types.length; i++){
            if(value == types[i].getValue()){
                return types[i];
            }
        }
        return null;
    }

}
