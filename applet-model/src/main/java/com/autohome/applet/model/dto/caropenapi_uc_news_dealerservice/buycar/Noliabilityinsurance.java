package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by hanshanfeng on 2018/8/27.
 */
public class Noliabilityinsurance {
    @JsonProperty("noLiabilityInsuranceKey")
    private String noliabilityinsurancekey;
    @JsonProperty("noLiabilityInsuranceValue")
    private int noliabilityinsurancevalue;

    public void setNoliabilityinsurancekey(String noliabilityinsurancekey) {
        this.noliabilityinsurancekey = noliabilityinsurancekey;
    }

    public String getNoliabilityinsurancekey() {
        return noliabilityinsurancekey;
    }

    public void setNoliabilityinsurancevalue(int noliabilityinsurancevalue) {
        this.noliabilityinsurancevalue = noliabilityinsurancevalue;
    }

    public int getNoliabilityinsurancevalue() {
        return noliabilityinsurancevalue;
    }
}
