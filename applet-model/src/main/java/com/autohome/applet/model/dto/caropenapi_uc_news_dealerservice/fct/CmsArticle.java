package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

/**
 * Created by hans<PERSON>feng on 2019/5/7.
 */
public class CmsArticle {
    private int id;
    private String title;
    private String title2;
    private boolean ispublish;
    private String publishtime;
    private String img;
    private String appimg;
    private int serializestartpage;
    private boolean isserializeover;
    private int serializeendpage;
    private String dir;
    private String content;
    private int brandid;
    private int replycount;
    private int viewcount;
    private int class1;
    private int class2;
    private String class1name;
    private String class2name;
    private String lastupdatetime;
    private String summary;
    private int isclosecomment;
    private int kind;
    private int seriesid;
    private String seriesids;
    private int specid;
    private String specids;
    private String brands;
    private String fctids;
    private String cwordids;
    private int state;
    private int serializeorders;
    private int editor;
    private int memberid;
    private String editorname;
    private int articletype;
    private String pageid;
    @JsonProperty("graphic_img_list")
    private String graphicImgList;
    @JsonProperty("graphic_img_list2")
    private String graphicImgList2;
    @JsonProperty("graphic_img_list3")
    private String graphicImgList3;
    private String firstcoverimg;
    private String secondcoverimg;
    private String thirdcoverimg;
    private String firstappcoverimg;
    private String secondappcoverimg;
    private String thirdappcoverimg;
    private int evalgoodcount;
    private String murl;
    private String pcurl;
    private String focusimg;

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle2(String title2) {
        this.title2 = title2;
    }

    public String getTitle2() {
        return title2;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        if (publishtime.indexOf("Date") > 0) {
            return FormatDate.dateToStr(new Date(Long.parseLong(publishtime.replace("/Date(", "").replace(")/", ""))), "yyyy-MM-dd HH:mm:ss");
        }
        return publishtime;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getImg() {
        if (StringUtil.isNotNullAndWhiteSpace(img))
            return img.replace("http://", "https://");
        return img;
    }

    public void setAppimg(String appimg) {
        this.appimg = appimg;
    }

    public String getAppimg() {
        return appimg;
    }

    public void setSerializestartpage(int serializestartpage) {
        this.serializestartpage = serializestartpage;
    }

    public int getSerializestartpage() {
        return serializestartpage;
    }

    public void setIsserializeover(boolean isserializeover) {
        this.isserializeover = isserializeover;
    }

    public boolean getIsserializeover() {
        return isserializeover;
    }

    public void setSerializeendpage(int serializeendpage) {
        this.serializeendpage = serializeendpage;
    }

    public int getSerializeendpage() {
        return serializeendpage;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getDir() {
        return dir;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setReplycount(int replycount) {
        this.replycount = replycount;
    }

    public int getReplycount() {
        return replycount;
    }

    public void setViewcount(int viewcount) {
        this.viewcount = viewcount;
    }

    public int getViewcount() {
        return viewcount;
    }

    public void setClass1(int class1) {
        this.class1 = class1;
    }

    public int getClass1() {
        return class1;
    }

    public void setClass2(int class2) {
        this.class2 = class2;
    }

    public int getClass2() {
        return class2;
    }

    public void setClass1name(String class1name) {
        this.class1name = class1name;
    }

    public String getClass1name() {
        return class1name;
    }

    public void setClass2name(String class2name) {
        this.class2name = class2name;
    }

    public String getClass2name() {
        return class2name;
    }

    public void setLastupdatetime(String lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public String getLastupdatetime() {
        return lastupdatetime;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getSummary() {
        return summary;
    }

    public void setIsclosecomment(int isclosecomment) {
        this.isclosecomment = isclosecomment;
    }

    public int getIsclosecomment() {
        return isclosecomment;
    }

    public void setKind(int kind) {
        this.kind = kind;
    }

    public int getKind() {
        return kind;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesids(String seriesids) {
        this.seriesids = seriesids;
    }

    public String getSeriesids() {
        return seriesids;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecids(String specids) {
        this.specids = specids;
    }

    public String getSpecids() {
        return specids;
    }

    public void setBrands(String brands) {
        this.brands = brands;
    }

    public String getBrands() {
        return brands;
    }

    public void setFctids(String fctids) {
        this.fctids = fctids;
    }

    public String getFctids() {
        return fctids;
    }

    public void setCwordids(String cwordids) {
        this.cwordids = cwordids;
    }

    public String getCwordids() {
        return cwordids;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setSerializeorders(int serializeorders) {
        this.serializeorders = serializeorders;
    }

    public int getSerializeorders() {
        return serializeorders;
    }

    public void setEditor(int editor) {
        this.editor = editor;
    }

    public int getEditor() {
        return editor;
    }

    public void setMemberid(int memberid) {
        this.memberid = memberid;
    }

    public int getMemberid() {
        return memberid;
    }

    public void setEditorname(String editorname) {
        this.editorname = editorname;
    }

    public String getEditorname() {
        return editorname;
    }

    public void setArticletype(int articletype) {
        this.articletype = articletype;
    }

    public int getArticletype() {
        return articletype;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setGraphicImgList(String graphicImgList) {
        this.graphicImgList = graphicImgList;
    }

    public String getGraphicImgList() {
        return graphicImgList;
    }

    public void setGraphicImgList2(String graphicImgList2) {
        this.graphicImgList2 = graphicImgList2;
    }

    public String getGraphicImgList2() {
        return graphicImgList2;
    }

    public void setGraphicImgList3(String graphicImgList3) {
        this.graphicImgList3 = graphicImgList3;
    }

    public String getGraphicImgList3() {
        return graphicImgList3;
    }

    public void setFirstcoverimg(String firstcoverimg) {
        this.firstcoverimg = firstcoverimg;
    }

    public String getFirstcoverimg() {
        return firstcoverimg;
    }

    public void setSecondcoverimg(String secondcoverimg) {
        this.secondcoverimg = secondcoverimg;
    }

    public String getSecondcoverimg() {
        return secondcoverimg;
    }

    public void setThirdcoverimg(String thirdcoverimg) {
        this.thirdcoverimg = thirdcoverimg;
    }

    public String getThirdcoverimg() {
        return thirdcoverimg;
    }

    public void setFirstappcoverimg(String firstappcoverimg) {
        this.firstappcoverimg = firstappcoverimg;
    }

    public String getFirstappcoverimg() {
        if (StringUtil.isNotNullAndWhiteSpace(firstappcoverimg))
            return firstappcoverimg.replace("http://", "https://");
        return firstappcoverimg;
    }

    public void setSecondappcoverimg(String secondappcoverimg) {
        this.secondappcoverimg = secondappcoverimg;
    }

    public String getSecondappcoverimg() {
        if (StringUtil.isNotNullAndWhiteSpace(secondappcoverimg))
            return secondappcoverimg.replace("http://", "https://");
        return secondappcoverimg;
    }

    public void setThirdappcoverimg(String thirdappcoverimg) {
        this.thirdappcoverimg = thirdappcoverimg;
    }

    public String getThirdappcoverimg() {
        if (StringUtil.isNotNullAndWhiteSpace(thirdappcoverimg))
            return thirdappcoverimg.replace("http://", "https://");
        return thirdappcoverimg;
    }

    public void setEvalgoodcount(int evalgoodcount) {
        this.evalgoodcount = evalgoodcount;
    }

    public int getEvalgoodcount() {
        return evalgoodcount;
    }

    public void setMurl(String murl) {
        this.murl = murl;
    }

    public String getMurl() {
        return murl;
    }

    public void setPcurl(String pcurl) {
        this.pcurl = pcurl;
    }

    public String getPcurl() {
        return pcurl;
    }

    public void setFocusimg(String focusimg) {
        this.focusimg = focusimg;
    }

    public String getFocusimg() {
        return focusimg;
    }

}
