package com.autohome.applet.model.dto.netcoreapi.output;

public class GetAttentionBySeries {
    private String seriesname;

    private int id;

    private String name;

    private String logo;

    private long minprice;

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public long getMinprice() {
        return minprice;
    }

    public void setMinprice(long minprice) {
        this.minprice = minprice;
    }
}
