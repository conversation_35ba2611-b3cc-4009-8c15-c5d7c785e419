package com.autohome.applet.model.dto.carlibrary.carapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CarApiFctList {

    @JsonProperty("brandid")
    private int brandId;

    @JsonProperty("brandname")
    private String brandName;

    @JsonProperty("brandlogo")
    private String brandLogo;

    private int total;

    @JsonProperty("fctlist")
    private List<CarApiFct> fctList;

}
