package com.autohome.applet.model.dto.car;

/**
 * <AUTHOR>
 */
public class EnterpriseGroupDto {

    private Integer brandId;

    private String brandName;

    private Integer seriesId;

    private String seriesName;

    private String cityId;

    private String cityName;

    private String groupLogo;

    private String groupName;

    // 渠道码
    private String groupQrCode;

    // 群活码
    private String groupQrCode3;


    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getGroupLogo() {
        return groupLogo;
    }

    public void setGroupLogo(String groupLogo) {
        this.groupLogo = groupLogo;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupQrCode() {
        return groupQrCode;
    }

    public void setGroupQrCode(String groupQrCode) {
        this.groupQrCode = groupQrCode;
    }

    public String getGroupQrCode3() {
        return groupQrCode3;
    }

    public void setGroupQrCode3(String groupQrCode3) {
        this.groupQrCode3 = groupQrCode3;
    }
}
