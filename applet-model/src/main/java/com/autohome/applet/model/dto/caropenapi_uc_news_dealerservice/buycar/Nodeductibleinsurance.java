package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by hanshanfeng on 2018/8/27.
 */
public class Nodeductibleinsurance {
    @JsonProperty("noDeductibleInsuranceKey")
    private String nodeductibleinsurancekey;
    @JsonProperty("noDeductibleInsuranceValue")
    private int nodeductibleinsurancevalue;

    public void setNodeductibleinsurancekey(String nodeductibleinsurancekey) {
        this.nodeductibleinsurancekey = nodeductibleinsurancekey;
    }

    public String getNodeductibleinsurancekey() {
        return nodeductibleinsurancekey;
    }

    public void setNodeductibleinsurancevalue(int nodeductibleinsurancevalue) {
        this.nodeductibleinsurancevalue = nodeductibleinsurancevalue;
    }

    public int getNodeductibleinsurancevalue() {
        return nodeductibleinsurancevalue;
    }
}
