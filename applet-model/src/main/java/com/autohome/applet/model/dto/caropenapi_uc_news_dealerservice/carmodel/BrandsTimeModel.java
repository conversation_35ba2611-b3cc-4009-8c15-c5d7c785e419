package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandsTimeModel {
    private String date;
    private List<SeriesInfo> serieslist;

    public static class SeriesInfo{
        private String factory;
        private String ontime;
        private String ontimenote;
        private String img;
        private String smalltitle;
        private String jumpCarUrl;
        private int minprice;
        private int maxprice;
        private int tagid;
        private String tagname;
        private boolean onmarket;
        private int seriesid;
        private String seriesname;
        private String title;
        private String url;
        private int newsid;
        private String jumpPicUrl;
        private String jumpConfigUrl;
        private int levelid;

        private int isInquiry;
        private String showUrl;
        private int state;

        private String serieswhitelogo;

        public String getOntime() {
            return ontime;
        }

        public void setOntime(String ontime) {
            this.ontime = ontime;
        }

        public String getOntimenote() {
            return ontimenote;
        }

        public void setOntimenote(String ontimenote) {
            this.ontimenote = ontimenote;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getSmalltitle() {
            return smalltitle;
        }

        public void setSmalltitle(String smalltitle) {
            this.smalltitle = smalltitle;
        }

        public String getJumpCarUrl() {
            return jumpCarUrl;
        }

        public void setJumpCarUrl(String jumpCarUrl) {
            this.jumpCarUrl = jumpCarUrl;
        }

        public int getMinprice() {
            return minprice;
        }

        public void setMinprice(int minprice) {
            this.minprice = minprice;
        }

        public int getMaxprice() {
            return maxprice;
        }

        public void setMaxprice(int maxprice) {
            this.maxprice = maxprice;
        }

        public int getTagid() {
            return tagid;
        }

        public void setTagid(int tagid) {
            this.tagid = tagid;
        }

        public String getTagname() {
            return tagname;
        }

        public void setTagname(String tagname) {
            this.tagname = tagname;
        }

        public boolean isOnmarket() {
            return onmarket;
        }

        public void setOnmarket(boolean onmarket) {
            this.onmarket = onmarket;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public int getNewsid() {
            return newsid;
        }

        public void setNewsid(int newsid) {
            this.newsid = newsid;
        }

        public String getJumpPicUrl() {
            return jumpPicUrl;
        }

        public void setJumpPicUrl(String jumpPicUrl) {
            this.jumpPicUrl = jumpPicUrl;
        }

        public String getJumpConfigUrl() {
            return jumpConfigUrl;
        }

        public void setJumpConfigUrl(String jumpConfigUrl) {
            this.jumpConfigUrl = jumpConfigUrl;
        }

        public int getLevelid() {
            return levelid;
        }

        public void setLevelid(int levelid) {
            this.levelid = levelid;
        }

        public int getIsInquiry() {
            return isInquiry;
        }

        public void setIsInquiry(int isInquiry) {
            this.isInquiry = isInquiry;
        }

        public String getShowUrl() {
            return showUrl;
        }

        public void setShowUrl(String showUrl) {
            this.showUrl = showUrl;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }

        public String getSerieswhitelogo() {
            return serieswhitelogo;
        }

        public String getFactory() {
            return factory;
        }

        public void setFactory(String factory) {
            this.factory = factory;
        }

        public void setSerieswhitelogo(String serieswhitelogo) {
            this.serieswhitelogo = serieswhitelogo;
        }
    }
}