package com.autohome.applet.model.dto.original;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserChejiahaoBean {
    private int userid;
    private String nickname;
    private String relationname;
    private String avatar;
    private String introduction;
    private String mobile;
    private String wechatid;
    private String coverimage;
    private String email;
    private boolean allowbigfile;
    private String jointime;
    private String createtime;
    private int status;
    private String statusdes;
    private UserChejiahaoStatistics statistics;
    private String pageid;
    private boolean isaloneplay;
    private String vcrid;
    private int activedegree;
    private String subscribetime;
    private boolean isshielded;
    private int infolevel;
    private int userlevel;
    private int creditscore;
    private boolean enabled;
    private String auditor;
    private int inforecommendlevel;
    private String inforecommendleveltime;
    private UserChejiahaoExtend2 userextend2;
    private int recommendtype;
    private String recommendtime;
    private boolean isautowrite;
    private String vtype;
    private String vtypedes;
    private String vdescription;
    private String vremark;
    private String vaudittime;
    private boolean isjoinadplan;
    private String joinadplanString;
    private boolean isjointask;
    private String jointaskdate;
    private boolean iswhitelist;
    private String businesstag;
    private String usertitle;
    private String gca;
    private boolean islocaltion;
    private int localprovinceid;
    private String localprovincename;
    private int localcityid;
    private String localcityname;
    private boolean isgoodauthor;
    private boolean issmallvideoauthor;
    private int fastestfirstid;
    private String fastestfirstname;
    private String gloryname;
    private int businesstype;
    private int isimportant;
    private int forbiddentype;
    private int forbiddenreason;
    private String forbiddentime;
    private List<String> userglory;

}
