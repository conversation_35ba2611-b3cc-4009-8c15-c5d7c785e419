package com.autohome.applet.model.lightapp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class Dealer4SpecActivity {
    private long id;
    private int type;
    private String name;
    private String image;
    private String beginTime;
    private String endTime;
    private String appId;
    private String tagText;
    private String buttonText;
    private int dealerId;

    @JsonProperty("id")
    public void setId(long id) {
        this.id = id;
    }

    public long getId() {
        return id;
    }

    @JsonProperty("type")
    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @JsonProperty("image")
    public void setImage(String image) {
        this.image = image;
    }

    public String getImage() {
        return image;
    }

    @JsonProperty("beginTime")
    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getBeginTime() {
        return beginTime;
    }

    @JsonProperty("endTime")
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getEndTime() {
        return endTime;
    }

    @JsonProperty("appId")
    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppId() {
        return appId;
    }

    @JsonProperty("tagText")
    public void setTagText(String tagText) {
        this.tagText = tagText;
    }

    public String getTagText() {
        return tagText;
    }

    @JsonProperty("buttonText")
    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }

    public String getButtonText() {
        return buttonText;
    }

    @JsonProperty("dealerId")
    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public int getDealerId() {
        return dealerId;
    }
}
