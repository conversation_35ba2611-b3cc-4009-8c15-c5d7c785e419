package com.autohome.applet.model.dto.newenergy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KouBeiShareContentDto  extends  KouBeiInfoDto{

    /**
     * 平均分
     */
    private Float average;
    /**
     * 春秋满电续航
     */

    private Integer springrange;

    /**
     * 夏季满电续航
     */
    private Integer summerrange;

    /**
     * 冬季满电续航
     */
    private Integer winterCruisingRange;

    private List<ImageData> multiImages;

    private List<TabInfo> tabInfoList = new ArrayList<>();

    @NoArgsConstructor
    @Data
    public static class ImageData {

        private int appendingId;
        private int coverType;
        private int during;
        private String duringString;
        private int id;
        private String img;
        private int is_del;
        private int koubeiId;
        private int photoId;
        private int userSelected;
        private int videoId;
    }

    @NoArgsConstructor
    @Data
    public static class TabInfo{
        private String name;
        private String value;
    }



}
