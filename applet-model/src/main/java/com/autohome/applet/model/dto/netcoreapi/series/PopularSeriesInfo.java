package com.autohome.applet.model.dto.netcoreapi.series;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PopularSeriesInfo {

    // 车系id
    public int id;

    // 车系名称
    public String name;

    // 价格区间
    public String price;

    // 车系状态
    public int state;

    //透明图
    public String transparentphone;

    //区间
    @JsonProperty("JXSprice")
    public String jxSprice;

    //最大降幅
    @JsonProperty("MaxCPrice")
    public String maxCPrice;





}
