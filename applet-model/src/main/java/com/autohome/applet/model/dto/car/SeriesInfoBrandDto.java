package com.autohome.applet.model.dto.car;

import com.autohome.applet.model.dto.carlibrary.carapi.out.RankListpageResult;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class SeriesInfoBrandDto  {
    //所有车系销量排行信息
    public RankListpageResult rank;
    //该品牌下所有支持vr的车系
    public Set<Integer> seriesIdsSet   ;

    //一次性查出该品牌下所有车系，并将该车系详情查出来，避免内部循环多次调用
    public Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap;


}
