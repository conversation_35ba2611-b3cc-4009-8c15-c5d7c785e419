package com.autohome.applet.model.dto.car.portrayal;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class Upb2Result {

    @JsonProperty("UserViewcarBrandList")
    private List<Upb2Item> userViewCarBrandList;

    @JsonProperty("UserViewcarSeriList")
    private List<Upb2Item> userViewCarSeriesList;

    @JsonProperty("UserViewcarNewenergySeriList")
    private List<Upb2Item> userViewcarNewenergySeriList;

    @JsonProperty("UserViewcarNewenergyBrandList")
    private List<Upb2Item> userViewcarNewenergyBrandList;

    public List<Upb2Item> getUserViewCarBrandList() {
        return userViewCarBrandList;
    }

    public void setUserViewCarBrandList(List<Upb2Item> userViewCarBrandList) {
        this.userViewCarBrandList = userViewCarBrandList;
    }

    public List<Upb2Item> getUserViewCarSeriesList() {
        return userViewCarSeriesList;
    }

    public void setUserViewCarSeriesList(List<Upb2Item> userViewCarSeriesList) {
        this.userViewCarSeriesList = userViewCarSeriesList;
    }

    public List<Upb2Item> getUserViewcarNewenergySeriList() {
        return userViewcarNewenergySeriList;
    }

    public void setUserViewcarNewenergySeriList(List<Upb2Item> userViewcarNewenergySeriList) {
        this.userViewcarNewenergySeriList = userViewcarNewenergySeriList;
    }

    public List<Upb2Item> getUserViewcarNewenergyBrandList() {
        return userViewcarNewenergyBrandList;
    }

    public void setUserViewcarNewenergyBrandList(List<Upb2Item> userViewcarNewenergyBrandList) {
        this.userViewcarNewenergyBrandList = userViewcarNewenergyBrandList;
    }
}
