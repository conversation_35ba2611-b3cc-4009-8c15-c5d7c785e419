package com.autohome.applet.model.dto.netcoreapi.series;

import java.util.List;

public class DimSeriesPRCType {

    private int isSemantic;
    private List<DimSeriesPRCTypeSummary> summary;
    private int typeKey;
    private String typeName;

    public void setIsSemantic(int isSemantic) {
        this.isSemantic = isSemantic;
    }

    public int getIsSemantic() {
        return isSemantic;
    }

    public void setSummary(List<DimSeriesPRCTypeSummary> summary) {
        this.summary = summary;
    }

    public List<DimSeriesPRCTypeSummary> getSummary() {
        return summary;
    }

    public void setTypeKey(int typeKey) {
        this.typeKey = typeKey;
    }

    public int getTypeKey() {
        return typeKey;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeName() {
        return typeName;
    }

}
