package com.autohome.applet.model.dto.carlibrary;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class ElectricSpecItem {
    @JsonProperty("specid")
    private int specId;

    @JsonProperty("specname")
    private String specName;

    @JsonProperty("specstate")
    private int specState;

    @JsonProperty("minprice")
    private double minPrice;

    @JsonProperty("maxprice")
    private double maxPrice;

    @JsonProperty("horsepower")
    private int horsepower;

    @JsonProperty("mileage")
    private int mileage;

    @JsonProperty("officialfastchargetime")
    private double officialFastChargeTime;

    @JsonProperty("officialslowchargetime")
    private int officialSlowChargeTime;

    @JsonProperty("batterycapacity")
    private double batteryCapacity;

    @JsonProperty("paramisshow")
    private int paramIsShow;

    @JsonProperty("fueltypedetail")
    private int fuelTypeDetail;

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public int getSpecState() {
        return specState;
    }

    public void setSpecState(int specState) {
        this.specState = specState;
    }

    public double getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(double minPrice) {
        this.minPrice = minPrice;
    }

    public double getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(double maxPrice) {
        this.maxPrice = maxPrice;
    }

    public int getHorsepower() {
        return horsepower;
    }

    public void setHorsepower(int horsepower) {
        this.horsepower = horsepower;
    }

    public int getMileage() {
        return mileage;
    }

    public void setMileage(int mileage) {
        this.mileage = mileage;
    }

    public double getOfficialFastChargeTime() {
        return officialFastChargeTime;
    }

    public void setOfficialFastChargeTime(double officialFastChargeTime) {
        this.officialFastChargeTime = officialFastChargeTime;
    }

    public int getOfficialSlowChargeTime() {
        return officialSlowChargeTime;
    }

    public void setOfficialSlowChargeTime(int officialSlowChargeTime) {
        this.officialSlowChargeTime = officialSlowChargeTime;
    }

    public double getBatteryCapacity() {
        return batteryCapacity;
    }

    public void setBatteryCapacity(double batteryCapacity) {
        this.batteryCapacity = batteryCapacity;
    }

    public int getParamIsShow() {
        return paramIsShow;
    }

    public void setParamIsShow(int paramIsShow) {
        this.paramIsShow = paramIsShow;
    }

    public int getFuelTypeDetail() {
        return fuelTypeDetail;
    }

    public void setFuelTypeDetail(int fuelTypeDetail) {
        this.fuelTypeDetail = fuelTypeDetail;
    }
}
