package com.autohome.applet.model.dto.newcar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LaOriginItemProtocol implements Serializable {
    private List<LaOriginItemProtocolItem> items;

    @Data
    public static class LaOriginItemProtocolItem {
        private static final long serialVersionUID = -3776213850982322023L;

        @JsonProperty(value = "ext_json")
        private String extJson;
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty(value = "pool_create_time")
        private Date poolCreateTime;
        @JsonProperty(value = "pool_biz_types")
        private List<Long> poolBizTypes;
        @JsonProperty(value = "series_names")
        private List<String> seriesNames;
        @JsonProperty(value = "factory_names")
        private List<String> factoryNames;
        @JsonProperty(value = "brand_names")
        private List<String> brandNames;
        @JsonProperty(value = "city_names")
        private List<String> cityNames;
        @JsonProperty(value = "province_names")
        private List<String> provinceNames;
        @JsonProperty(value = "global_id")
        private Long globalId;
        @JsonProperty(value = "main_data_type")
        private String mainDataType;
        @JsonProperty(value = "biz_id")
        private Long bizId;
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty(value = "biz_create_time")
        private Date bizCreateTime;
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty(value = "biz_update_time")
        private Date bizUpdateTime;
        @JsonProperty(value = "pc_url")
        private String pcUrl;
        @JsonProperty(value = "m_url")
        private String mUrl;
        @JsonProperty(value = "app_url")
        private String appUrl;
        @JsonProperty(value = "video_height")
        private Integer videoHeight;
        @JsonProperty(value = "video_width")
        private Integer videoWidth;
        @JsonProperty(value = "series_ids")
        private List<Integer> seriesIds;
        @JsonProperty(value = "factory_ids")
        private List<Integer> factoryIds;
        @JsonProperty(value = "brand_ids")
        private List<Integer> brandIds;
        @JsonProperty(value = "city_ids")
        private List<Integer> cityIds;
        @JsonProperty(value = "province_ids")
        private List<Integer> provinceIds;
        @JsonProperty(value = "multi_images")
        private List<String> multiImages;
        @JsonProperty(value = "has_multi_images")
        private Integer hasMultiImages;
        @JsonProperty(value = "author_img")
        private String authorImg;
        @JsonProperty(value = "author_name")
        private String authorName;
        @JsonProperty(value = "author_id")
        private Long authorId;
        @JsonProperty(value = "is_delete")
        private Integer isDelete;
        @JsonProperty(value = "is_publish")
        private Integer isPublish;
        @JsonProperty(value = "img_url")
        private String imgUrl;
        @JsonProperty(value = "img_url_4x3")
        private String imgUrl43;
        @JsonProperty(value = "img_url_16x9")
        private String imgUrl69;
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty(value = "publish_time")
        private Date publishTime;
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty(value = "last_reply_date")
        private Date lastReplyDate;
        @JsonProperty(value = "title")
        private String title;
        @JsonProperty(value = "small_title")
        private String smallTitle;
        @JsonProperty(value = "summary")
        private String summary;
        @JsonProperty(value = "is_close_comment")
        private Integer isCloseComment;
        @JsonProperty(value = "parent_biz_id")
        private Integer parentBizId;
        @JsonProperty(value = "cms_createsource")
        private Integer cmsCreatesource;
        @JsonProperty(value = "cms_class3")
        private Integer cmsClass3;
        @JsonProperty(value = "cms_class2")
        private Integer cmsClass2;
        @JsonProperty(value = "cms_class1")
        private Integer cmsClass1;
        @JsonProperty(value = "cms_content_class2")
        private String cmsContentClass2;
        @JsonProperty(value = "cms_content_class")
        private String cmsContentClass;
        @JsonProperty(value = "cms_images")
        private String cmsImages;
        @JsonProperty(value = "cms_refine")
        private Long cmsRefine;
        @JsonProperty(value = "duration")
        private Long duration;
        @JsonProperty(value = "video_direction")
        private Integer videoDirection;
        @JsonProperty(value = "video_source")
        private String videoSource;
        @JsonProperty(value = "video_original_parent_tag_names")
        private List<String> videoOriginalParentTagNames;
        @JsonProperty(value = "video_original_tag_names")
        private List<String> videoOriginalTagNames;
        @JsonProperty(value = "video_original_parent_tag_ids")
        private List<Integer> videoOriginalParentTagIds;
        @JsonProperty(value = "video_original_tag_ids")
        private List<Integer> videoOriginalTagIds;
        @JsonProperty(value = "video_verify")
        private Integer videoVerify;
        @JsonProperty(value = "video_video_status")
        private Integer videoVideoStatus;
        @JsonProperty(value = "club_bbs_name")
        private String clubBbsName;
        @JsonProperty(value = "club_bbs_id")
        private Integer clubBbsId;
        @JsonProperty(value = "club_topic_tag_ids")
        private List<Integer> clubTopicTagIds;
        @JsonProperty(value = "club_topic_tag_names")
        private List<String> clubTopicTagNames;
        @JsonProperty(value = "club_is_jinghua")
        private Integer clubIsJinghua;
        @JsonProperty(value = "club_is_pool")
        private Integer clubIsPool;
        @JsonProperty(value = "imgList")
        private String imgList;
        @JsonProperty(value = "unlike_count")
        private Long unlikeCount;
        @JsonProperty(value = "topic_reply_count")
        private Long topicReplyCount;
        @JsonProperty(value = "like_count")
        private Long likeCount;
        @JsonProperty(value = "reply_count")
        private Long replyCount;
        @JsonProperty(value = "vv")
        private Long vv;
        @JsonProperty(value = "pv")
        private Long pv;

        @JsonProperty(value = "usecar_category_ids")
        private List<Integer> usecarCategoryIds;
        @JsonProperty(value = "usecar_category_names")
        private List<String> usecarCategoryNames;

        @JsonProperty(value = "video_app_title")
        private String videoAppTitle;

        @JsonProperty(value = "chejiahao_tag_ids")
        private List<Long> chejiahaoTagIds;
        @JsonProperty(value = "chejiahao_tag_names")
        private List<String> chejiahaoTagNames;
        @JsonProperty(value = "chejiahao_IsWaiTui")
        private Integer chejiahaoIsWaiTui;

        @JsonProperty(value = "small_video_recommend_tag")
        private List<String> smallVideoRecommendTag;
        @JsonProperty(value = "small_video_recommend_tag_names")
        private List<String> smallVideoRecommendTagNames;
        @JsonProperty(value = "small_video_is_recommend")
        private Integer smallVideoIsRecommend;
        @JsonProperty(value = "small_video_isprivate")
        private Integer smallVideoIsprivate;
        @JsonProperty(value = "small_video_verify_status")
        private Integer smallVideoVerifyStatus;
        @JsonProperty(value = "small_video_height")
        private Integer smallVideoHeight;
        @JsonProperty(value = "small_video_width")
        private Integer smallVideoWidth;
        @JsonProperty(value = "small_video_bid")
        private Long smallVideoBid;
        @JsonProperty(value = "small_video_bname")
        private String smallVideoBname;
        @JsonProperty(value = "small_video_topicid")
        private Long smallVideoTopicid;
        @JsonProperty(value = "small_video_play_url")
        private String smallVideoPlayUrl;

        @JsonProperty(value = "qcby_carcase_categoryId")
        private Long qcbyCarcaseCategoryId;
        @JsonProperty(value = "qcby_carcase_categoryName")
        private String qcbyCarcaseCategoryName;
        @JsonProperty(value = "qcby_carcase_jh_imgs")
        private List<String> qcbyCarcaseJhImgs;
        @JsonProperty(value = "qcby_carcase_userId")
        private Long qcbyCarcaseUserId;
        @JsonProperty(value = "qcby_carcase_userName")
        private String qcbyCarcaseUserName;
        @JsonProperty(value = "qcby_carcase_userPic")
        private String qcbyCarcaseUserPic;

        @JsonProperty(value = "car_refit_show_to_user")
        private Integer carRefitShowToUser;
        @JsonProperty(value = "car_refit_topic")
        private String carRefitTopic;
        @JsonProperty(value = "car_refit_dealer_id")
        private Integer carRefitDealerId;
        @JsonProperty(value = "car_refit_dealer_name")
        private String carRefitDealerName;
        @JsonProperty(value = "car_refit_stars")
        private Long carRefitStars;
        @JsonProperty(value = "car_refit_audit_status")
        private Integer carRefitAuditStatus;
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty(value = "car_refit_refit_date")
        private Date carRefitRefitDate;
        @JsonProperty(value = "car_refit_total_cost")
        private String carRefitTotalCost;
        @JsonProperty(value = "car_refit_file_id")
        private Long carRefitFileId;

        @JsonProperty(value = "second_biz_id")
        private Long secondBizId;
        @JsonProperty(value = "follow_topic_id")
        private List<Long> followTopicId;

        @JsonProperty(value = "club_topiccards")
        private List<ClubTopiccardsItemDto> clubTopiccards;//正文内容的结构化数据

        @JsonProperty(value = "club_delete_flag")
        private Integer clubDeleteFlag;//帖子是否被删除；0：没有删除

        @JsonProperty(value = "jiage_client_type")
        private Long jiageClientType;

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ClubTopiccardsItemDto implements Serializable {
            private static final long serialVersionUID = -6871138362395793029L;

            @JsonProperty(value = "club_topiccards_ctype")
            private Integer clubTopiccardsCtype;// 卡片类型  1:图片
            @JsonProperty(value = "club_topiccards_cardid")
            private Integer clubTopiccardsCardid;
            @JsonProperty(value = "club_topiccards_url")
            private String clubTopiccardsUrl;
        }
    }
}


