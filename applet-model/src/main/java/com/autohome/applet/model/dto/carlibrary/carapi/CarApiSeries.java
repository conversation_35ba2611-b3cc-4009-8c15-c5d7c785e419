package com.autohome.applet.model.dto.carlibrary.carapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarApiSeries {

    @JsonProperty("seriesid")
    private int seriesId;
    private String seriesName;
    private String seriesImg;
    private int seriesState;
    private int seriesPriceMin;
    private int seriesPriceMax;
    @JsonProperty("fctid")
    private int fctId;
    @JsonProperty("fctname")
    private String fctName;
    @JsonProperty("seriesplace")
    private String seriesPlace;
    private int rank;
    @JsonProperty("pnglogo")
    private String pngLogo;
    @JsonProperty("newEnergySeriesId")
    private int newenergySeriesId;
    @JsonProperty("containbookedspec")
    private int containBookedSpec;
    @JsonProperty("newnergy")
    private int newEnergy;
    private int levelId;
    private String levelName;
    private String fctPy;
    private int isForeignCar;
    private int uvRank;
    private int hotRank;
}
