package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by ha<PERSON><PERSON>feng on 2019/5/30.
 */
public class DealerSeries {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<FctList> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<FctList> getList() {
        return list;
    }

    public void setList(List<FctList> list) {
        this.list = list;
    }

    public static class FctList {

        @JsonProperty("FactoryId")
        private int factoryid;
        @JsonProperty("FactoryName")
        private String factoryname;
        @JsonProperty("SeriesList")
        private List<Serieslist> serieslist;

        public void setFactoryid(int factoryid) {
            this.factoryid = factoryid;
        }

        public int getFactoryid() {
            return factoryid;
        }

        public void setFactoryname(String factoryname) {
            this.factoryname = factoryname;
        }

        public String getFactoryname() {
            return factoryname;
        }

        public void setSerieslist(List<Serieslist> serieslist) {
            this.serieslist = serieslist;
        }

        public List<Serieslist> getSerieslist() {
            return serieslist;
        }

    }

    public static class Serieslist {

        @JsonProperty("SeriesId")
        private int seriesid;
        @JsonProperty("SeriesName")
        private String seriesname;
        @JsonProperty("ImageUrl")
        private String imageurl;
        @JsonProperty("MinPrice")
        private int minprice;
        @JsonProperty("MaxPrice")
        private int maxprice;
        @JsonProperty("MinOriginalPrice")
        private int minoriginalprice;
        @JsonProperty("MaxOriginalPrice")
        private int maxoriginalprice;
        @JsonProperty("PriceOff")
        private int priceoff;
        @JsonProperty("SpecCount")
        private int speccount;
        @JsonProperty("IsNewCar")
        private int isnewcar;
        @JsonProperty("NewCarSpecId")
        private int newcarspecid;
        @JsonProperty("SeriesVRUrl")
        private String seriesvrurl;

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setImageurl(String imageurl) {
            this.imageurl = imageurl;
        }

        public String getImageurl() {
            return imageurl;
        }

        public void setMinprice(int minprice) {
            this.minprice = minprice;
        }

        public int getMinprice() {
            return minprice;
        }

        public void setMaxprice(int maxprice) {
            this.maxprice = maxprice;
        }

        public int getMaxprice() {
            return maxprice;
        }

        public void setMinoriginalprice(int minoriginalprice) {
            this.minoriginalprice = minoriginalprice;
        }

        public int getMinoriginalprice() {
            return minoriginalprice;
        }

        public void setMaxoriginalprice(int maxoriginalprice) {
            this.maxoriginalprice = maxoriginalprice;
        }

        public int getMaxoriginalprice() {
            return maxoriginalprice;
        }

        public void setPriceoff(int priceoff) {
            this.priceoff = priceoff;
        }

        public int getPriceoff() {
            return priceoff;
        }

        public void setSpeccount(int speccount) {
            this.speccount = speccount;
        }

        public int getSpeccount() {
            return speccount;
        }

        public void setIsnewcar(int isnewcar) {
            this.isnewcar = isnewcar;
        }

        public int getIsnewcar() {
            return isnewcar;
        }

        public void setNewcarspecid(int newcarspecid) {
            this.newcarspecid = newcarspecid;
        }

        public int getNewcarspecid() {
            return newcarspecid;
        }

        public void setSeriesvrurl(String seriesvrurl) {
            this.seriesvrurl = seriesvrurl;
        }

        public String getSeriesvrurl() {
            return seriesvrurl;
        }

    }
}
