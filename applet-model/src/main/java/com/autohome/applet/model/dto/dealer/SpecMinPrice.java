package com.autohome.applet.model.dto.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class SpecMinPrice {
    @JsonProperty("seriesId")
    private int seriesId;

    @JsonProperty("specId")
    private int specId;

    @JsonProperty("newsId")
    private int newsId;

    @JsonProperty("dealerId")
    private int dealerId;

    @JsonProperty("newsPrice")
    private int newsPrice;

    @JsonProperty("minOriginalPrice")
    private int minOriginalPrice;

    @JsonProperty("maxOriginalPrice")
    private int maxOriginalPrice;

    @JsonProperty("payTypeSequenceOne")
    private int payTypeSequenceOne;

    @JsonProperty("seriesName")
    private String seriesName;

    @JsonProperty("specName")
    private String specName;

    @JsonProperty("dealerSimpleName")
    private String dealerSimpleName;

    @JsonProperty("specImageUrl")
    private String specImageUrl;

    @JsonProperty("starLevel")
    private int starLevel;

    public SpecMinPrice() {
    }

    public SpecMinPrice(int seriesId, int specId, int newsId, int dealerId, int newsPrice, int minOriginalPrice, int maxOriginalPrice, int payTypeSequenceOne, String seriesName, String specName, String dealerSimpleName, String specImageUrl, int starLevel) {
        this.seriesId = seriesId;
        this.specId = specId;
        this.newsId = newsId;
        this.dealerId = dealerId;
        this.newsPrice = newsPrice;
        this.minOriginalPrice = minOriginalPrice;
        this.maxOriginalPrice = maxOriginalPrice;
        this.payTypeSequenceOne = payTypeSequenceOne;
        this.seriesName = seriesName;
        this.specName = specName;
        this.dealerSimpleName = dealerSimpleName;
        this.specImageUrl = specImageUrl;
        this.starLevel = starLevel;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public int getNewsId() {
        return newsId;
    }

    public void setNewsId(int newsId) {
        this.newsId = newsId;
    }

    public int getDealerId() {
        return dealerId;
    }

    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public int getNewsPrice() {
        return newsPrice;
    }

    public void setNewsPrice(int newsPrice) {
        this.newsPrice = newsPrice;
    }

    public int getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(int minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public int getMaxOriginalPrice() {
        return maxOriginalPrice;
    }

    public void setMaxOriginalPrice(int maxOriginalPrice) {
        this.maxOriginalPrice = maxOriginalPrice;
    }

    public int getPayTypeSequenceOne() {
        return payTypeSequenceOne;
    }

    public void setPayTypeSequenceOne(int payTypeSequenceOne) {
        this.payTypeSequenceOne = payTypeSequenceOne;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getDealerSimpleName() {
        return dealerSimpleName;
    }

    public void setDealerSimpleName(String dealerSimpleName) {
        this.dealerSimpleName = dealerSimpleName;
    }

    public String getSpecImageUrl() {
        return specImageUrl;
    }

    public void setSpecImageUrl(String specImageUrl) {
        this.specImageUrl = specImageUrl;
    }

    public int getStarLevel() {
        return starLevel;
    }

    public void setStarLevel(int starLevel) {
        this.starLevel = starLevel;
    }
}
