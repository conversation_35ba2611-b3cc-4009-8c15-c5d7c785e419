package com.autohome.applet.model.dto.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChejiahaoDetailResult {

    private boolean allowdelete;
    private ChejiahaoDetailResultArticle article;
    private String audio;
    private String auditnotes;
    private int auditor;
    private int auditrecommend;
    private ChejiahaoDetailResultAuthor author;
    private long authorid;
    private String autohomeua;
    private List<String> carmonads;
    private String cityid;
    private int clienttype;
    private String conceptkeywords;
    private int contentlevel;
    private int contenttype;
    private String correctiondescription;
    private String correctiontitle;
    private Date createtime;
    private int creatorinfoid;
    private String description;
    private String description2;
    private String expiredate;
    private String hailiangreason;
    private int hailiangstatus;
    private boolean hasreview;
    private String hotinfopv;
    private int hotspottag;
    private int ident;
    private String image;
    private List<String> images;
    private String imgcross;
    private String imgvertical;
    private int infocityid;
    private String infocityname;
    private int infoid;
    private int infolevel;
    private ChejiahaoDetailResultInfostatistics infostatistics;
    private String infostatus;
    private int infotype;
    private boolean innovate;
    private String ip;
    private String ipprovince;
    private boolean is_title_edit;
    private boolean isartificial;
    private boolean isautowrite;
    private boolean isbusiness;
    private boolean isconverttext;
    private boolean isdeduction;
    private boolean isexpose;
    private boolean isfastestfirst;
    private boolean isfirst;
    private boolean ishot;
    private boolean isimportant;
    private boolean islowcar;
    private boolean ismanualcontentlevel;
    private boolean ismanualorai;
    private boolean ismodify;
    private int ismultipletitle;
    private boolean isoriginal;
    private boolean ispublish;
    private int isrecommend;
    private boolean istiming;
    private boolean istop;
    private boolean iswaitui;
    private boolean isyouxuan;
    private String keywords;
    private Date lastupdatetime;
    private String live;
    private Date localrecommendtime;
    private int localrecommendtype;
    private List<String> motorcyclerelationcar;
    private String murl;
    private String newkeywordids;
    private String newkeywords;
    private String operateremarks;
    private String originaldescription;
    private String pageid;
    private long pageticks;
    private String pcurl;
    private int plusorminusone;
    private String publishtime;
    private Date publishtiming;
    private String pushdescription;
    private String pushtitle;
    private String qrcode;
    private boolean recommedntoxny;
    private boolean recommendshowbigimg;
    private boolean recommendtomoji;
    private boolean recommendtoseries;
    private boolean recycled;
    private List<ChejiahaoDetailResultRelationCar> relationcar;
    private boolean showbigimg;
    private boolean showimagelist;
    private int showlevel;
    private int sorttype;
    private int status;
    private String statusnote;
    private String statusnoteid;
    private List<ChejiahaoDetailResultTagdata> tagdata;
    private List<ChejiahaoDetailResultTags> tags;
    private List<ChejiahaoDetailResultTagsnew> tagsnew;
    private String taskid;
    private HashMap<String, Object> textimage;
    private String title;
    private String title2;
    private String title3;
    private Date title_edit_time;
    private ChejiahaoDetailResultVideo video;
    private int videotype;
    private Date waituitime;
    private Date youxuantime;

    public void setAllowdelete(boolean allowdelete) {
        this.allowdelete = allowdelete;
    }

    public boolean getAllowdelete() {
        return allowdelete;
    }

    public void setArticle(ChejiahaoDetailResultArticle article) {
        this.article = article;
    }

    public ChejiahaoDetailResultArticle getArticle() {
        return article;
    }

    public void setAudio(String audio) {
        this.audio = audio;
    }

    public String getAudio() {
        return audio;
    }

    public void setAuditnotes(String auditnotes) {
        this.auditnotes = auditnotes;
    }

    public String getAuditnotes() {
        return auditnotes;
    }

    public void setAuditor(int auditor) {
        this.auditor = auditor;
    }

    public int getAuditor() {
        return auditor;
    }

    public void setAuditrecommend(int auditrecommend) {
        this.auditrecommend = auditrecommend;
    }

    public int getAuditrecommend() {
        return auditrecommend;
    }

    public void setAuthor(ChejiahaoDetailResultAuthor author) {
        this.author = author;
    }

    public ChejiahaoDetailResultAuthor getAuthor() {
        return author;
    }

    public void setAuthorid(long authorid) {
        this.authorid = authorid;
    }

    public long getAuthorid() {
        return authorid;
    }

    public void setAutohomeua(String autohomeua) {
        this.autohomeua = autohomeua;
    }

    public String getAutohomeua() {
        return autohomeua;
    }

    public void setCarmonads(List<String> carmonads) {
        this.carmonads = carmonads;
    }

    public List<String> getCarmonads() {
        return carmonads;
    }

    public void setCityid(String cityid) {
        this.cityid = cityid;
    }

    public String getCityid() {
        return cityid;
    }

    public void setClienttype(int clienttype) {
        this.clienttype = clienttype;
    }

    public int getClienttype() {
        return clienttype;
    }

    public void setConceptkeywords(String conceptkeywords) {
        this.conceptkeywords = conceptkeywords;
    }

    public String getConceptkeywords() {
        return conceptkeywords;
    }

    public void setContentlevel(int contentlevel) {
        this.contentlevel = contentlevel;
    }

    public int getContentlevel() {
        return contentlevel;
    }

    public void setContenttype(int contenttype) {
        this.contenttype = contenttype;
    }

    public int getContenttype() {
        return contenttype;
    }

    public void setCorrectiondescription(String correctiondescription) {
        this.correctiondescription = correctiondescription;
    }

    public String getCorrectiondescription() {
        return correctiondescription;
    }

    public void setCorrectiontitle(String correctiontitle) {
        this.correctiontitle = correctiontitle;
    }

    public String getCorrectiontitle() {
        return correctiontitle;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatorinfoid(int creatorinfoid) {
        this.creatorinfoid = creatorinfoid;
    }

    public int getCreatorinfoid() {
        return creatorinfoid;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription2(String description2) {
        this.description2 = description2;
    }

    public String getDescription2() {
        return description2;
    }

    public void setExpiredate(String expiredate) {
        this.expiredate = expiredate;
    }

    public String getExpiredate() {
        return expiredate;
    }

    public void setHailiangreason(String hailiangreason) {
        this.hailiangreason = hailiangreason;
    }

    public String getHailiangreason() {
        return hailiangreason;
    }

    public void setHailiangstatus(int hailiangstatus) {
        this.hailiangstatus = hailiangstatus;
    }

    public int getHailiangstatus() {
        return hailiangstatus;
    }

    public void setHasreview(boolean hasreview) {
        this.hasreview = hasreview;
    }

    public boolean getHasreview() {
        return hasreview;
    }

    public void setHotinfopv(String hotinfopv) {
        this.hotinfopv = hotinfopv;
    }

    public String getHotinfopv() {
        return hotinfopv;
    }

    public void setHotspottag(int hotspottag) {
        this.hotspottag = hotspottag;
    }

    public int getHotspottag() {
        return hotspottag;
    }

    public void setIdent(int ident) {
        this.ident = ident;
    }

    public int getIdent() {
        return ident;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getImage() {
        return image;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImgcross(String imgcross) {
        this.imgcross = imgcross;
    }

    public String getImgcross() {
        return imgcross;
    }

    public void setImgvertical(String imgvertical) {
        this.imgvertical = imgvertical;
    }

    public String getImgvertical() {
        return imgvertical;
    }

    public void setInfocityid(int infocityid) {
        this.infocityid = infocityid;
    }

    public int getInfocityid() {
        return infocityid;
    }

    public void setInfocityname(String infocityname) {
        this.infocityname = infocityname;
    }

    public String getInfocityname() {
        return infocityname;
    }

    public void setInfoid(int infoid) {
        this.infoid = infoid;
    }

    public int getInfoid() {
        return infoid;
    }

    public void setInfolevel(int infolevel) {
        this.infolevel = infolevel;
    }

    public int getInfolevel() {
        return infolevel;
    }

    public void setInfostatistics(ChejiahaoDetailResultInfostatistics infostatistics) {
        this.infostatistics = infostatistics;
    }

    public ChejiahaoDetailResultInfostatistics getInfostatistics() {
        return infostatistics;
    }

    public void setInfostatus(String infostatus) {
        this.infostatus = infostatus;
    }

    public String getInfostatus() {
        return infostatus;
    }

    public void setInfotype(int infotype) {
        this.infotype = infotype;
    }

    public int getInfotype() {
        return infotype;
    }

    public void setInnovate(boolean innovate) {
        this.innovate = innovate;
    }

    public boolean getInnovate() {
        return innovate;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIp() {
        return ip;
    }

    public void setIpprovince(String ipprovince) {
        this.ipprovince = ipprovince;
    }

    public String getIpprovince() {
        return ipprovince;
    }

    public void setIs_title_edit(boolean is_title_edit) {
        this.is_title_edit = is_title_edit;
    }

    public boolean getIs_title_edit() {
        return is_title_edit;
    }

    public void setIsartificial(boolean isartificial) {
        this.isartificial = isartificial;
    }

    public boolean getIsartificial() {
        return isartificial;
    }

    public void setIsautowrite(boolean isautowrite) {
        this.isautowrite = isautowrite;
    }

    public boolean getIsautowrite() {
        return isautowrite;
    }

    public void setIsbusiness(boolean isbusiness) {
        this.isbusiness = isbusiness;
    }

    public boolean getIsbusiness() {
        return isbusiness;
    }

    public void setIsconverttext(boolean isconverttext) {
        this.isconverttext = isconverttext;
    }

    public boolean getIsconverttext() {
        return isconverttext;
    }

    public void setIsdeduction(boolean isdeduction) {
        this.isdeduction = isdeduction;
    }

    public boolean getIsdeduction() {
        return isdeduction;
    }

    public void setIsexpose(boolean isexpose) {
        this.isexpose = isexpose;
    }

    public boolean getIsexpose() {
        return isexpose;
    }

    public void setIsfastestfirst(boolean isfastestfirst) {
        this.isfastestfirst = isfastestfirst;
    }

    public boolean getIsfastestfirst() {
        return isfastestfirst;
    }

    public void setIsfirst(boolean isfirst) {
        this.isfirst = isfirst;
    }

    public boolean getIsfirst() {
        return isfirst;
    }

    public void setIshot(boolean ishot) {
        this.ishot = ishot;
    }

    public boolean getIshot() {
        return ishot;
    }

    public void setIsimportant(boolean isimportant) {
        this.isimportant = isimportant;
    }

    public boolean getIsimportant() {
        return isimportant;
    }

    public void setIslowcar(boolean islowcar) {
        this.islowcar = islowcar;
    }

    public boolean getIslowcar() {
        return islowcar;
    }

    public void setIsmanualcontentlevel(boolean ismanualcontentlevel) {
        this.ismanualcontentlevel = ismanualcontentlevel;
    }

    public boolean getIsmanualcontentlevel() {
        return ismanualcontentlevel;
    }

    public void setIsmanualorai(boolean ismanualorai) {
        this.ismanualorai = ismanualorai;
    }

    public boolean getIsmanualorai() {
        return ismanualorai;
    }

    public void setIsmodify(boolean ismodify) {
        this.ismodify = ismodify;
    }

    public boolean getIsmodify() {
        return ismodify;
    }

    public void setIsmultipletitle(int ismultipletitle) {
        this.ismultipletitle = ismultipletitle;
    }

    public int getIsmultipletitle() {
        return ismultipletitle;
    }

    public void setIsoriginal(boolean isoriginal) {
        this.isoriginal = isoriginal;
    }

    public boolean getIsoriginal() {
        return isoriginal;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setIsrecommend(int isrecommend) {
        this.isrecommend = isrecommend;
    }

    public int getIsrecommend() {
        return isrecommend;
    }

    public void setIstiming(boolean istiming) {
        this.istiming = istiming;
    }

    public boolean getIstiming() {
        return istiming;
    }

    public void setIstop(boolean istop) {
        this.istop = istop;
    }

    public boolean getIstop() {
        return istop;
    }

    public void setIswaitui(boolean iswaitui) {
        this.iswaitui = iswaitui;
    }

    public boolean getIswaitui() {
        return iswaitui;
    }

    public void setIsyouxuan(boolean isyouxuan) {
        this.isyouxuan = isyouxuan;
    }

    public boolean getIsyouxuan() {
        return isyouxuan;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    public void setLive(String live) {
        this.live = live;
    }

    public String getLive() {
        return live;
    }

    public void setLocalrecommendtime(Date localrecommendtime) {
        this.localrecommendtime = localrecommendtime;
    }

    public Date getLocalrecommendtime() {
        return localrecommendtime;
    }

    public void setLocalrecommendtype(int localrecommendtype) {
        this.localrecommendtype = localrecommendtype;
    }

    public int getLocalrecommendtype() {
        return localrecommendtype;
    }

    public void setMotorcyclerelationcar(List<String> motorcyclerelationcar) {
        this.motorcyclerelationcar = motorcyclerelationcar;
    }

    public List<String> getMotorcyclerelationcar() {
        return motorcyclerelationcar;
    }

    public void setMurl(String murl) {
        this.murl = murl;
    }

    public String getMurl() {
        return murl;
    }

    public void setNewkeywordids(String newkeywordids) {
        this.newkeywordids = newkeywordids;
    }

    public String getNewkeywordids() {
        return newkeywordids;
    }

    public void setNewkeywords(String newkeywords) {
        this.newkeywords = newkeywords;
    }

    public String getNewkeywords() {
        return newkeywords;
    }

    public void setOperateremarks(String operateremarks) {
        this.operateremarks = operateremarks;
    }

    public String getOperateremarks() {
        return operateremarks;
    }

    public void setOriginaldescription(String originaldescription) {
        this.originaldescription = originaldescription;
    }

    public String getOriginaldescription() {
        return originaldescription;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setPageticks(long pageticks) {
        this.pageticks = pageticks;
    }

    public long getPageticks() {
        return pageticks;
    }

    public void setPcurl(String pcurl) {
        this.pcurl = pcurl;
    }

    public String getPcurl() {
        return pcurl;
    }

    public void setPlusorminusone(int plusorminusone) {
        this.plusorminusone = plusorminusone;
    }

    public int getPlusorminusone() {
        return plusorminusone;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setPublishtiming(Date publishtiming) {
        this.publishtiming = publishtiming;
    }

    public Date getPublishtiming() {
        return publishtiming;
    }

    public void setPushdescription(String pushdescription) {
        this.pushdescription = pushdescription;
    }

    public String getPushdescription() {
        return pushdescription;
    }

    public void setPushtitle(String pushtitle) {
        this.pushtitle = pushtitle;
    }

    public String getPushtitle() {
        return pushtitle;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setRecommedntoxny(boolean recommedntoxny) {
        this.recommedntoxny = recommedntoxny;
    }

    public boolean getRecommedntoxny() {
        return recommedntoxny;
    }

    public void setRecommendshowbigimg(boolean recommendshowbigimg) {
        this.recommendshowbigimg = recommendshowbigimg;
    }

    public boolean getRecommendshowbigimg() {
        return recommendshowbigimg;
    }

    public void setRecommendtomoji(boolean recommendtomoji) {
        this.recommendtomoji = recommendtomoji;
    }

    public boolean getRecommendtomoji() {
        return recommendtomoji;
    }

    public void setRecommendtoseries(boolean recommendtoseries) {
        this.recommendtoseries = recommendtoseries;
    }

    public boolean getRecommendtoseries() {
        return recommendtoseries;
    }

    public void setRecycled(boolean recycled) {
        this.recycled = recycled;
    }

    public boolean getRecycled() {
        return recycled;
    }

    public void setRelationcar(List<ChejiahaoDetailResultRelationCar> relationcar) {
        this.relationcar = relationcar;
    }

    public List<ChejiahaoDetailResultRelationCar> getRelationcar() {
        return relationcar;
    }

    public void setShowbigimg(boolean showbigimg) {
        this.showbigimg = showbigimg;
    }

    public boolean getShowbigimg() {
        return showbigimg;
    }

    public void setShowimagelist(boolean showimagelist) {
        this.showimagelist = showimagelist;
    }

    public boolean getShowimagelist() {
        return showimagelist;
    }

    public void setShowlevel(int showlevel) {
        this.showlevel = showlevel;
    }

    public int getShowlevel() {
        return showlevel;
    }

    public void setSorttype(int sorttype) {
        this.sorttype = sorttype;
    }

    public int getSorttype() {
        return sorttype;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setStatusnote(String statusnote) {
        this.statusnote = statusnote;
    }

    public String getStatusnote() {
        return statusnote;
    }

    public void setStatusnoteid(String statusnoteid) {
        this.statusnoteid = statusnoteid;
    }

    public String getStatusnoteid() {
        return statusnoteid;
    }

    public void setTagdata(List<ChejiahaoDetailResultTagdata> tagdata) {
        this.tagdata = tagdata;
    }

    public List<ChejiahaoDetailResultTagdata> getTagdata() {
        return tagdata;
    }

    public void setTags(List<ChejiahaoDetailResultTags> tags) {
        this.tags = tags;
    }

    public List<ChejiahaoDetailResultTags> getTags() {
        return tags;
    }

    public void setTagsnew(List<ChejiahaoDetailResultTagsnew> tagsnew) {
        this.tagsnew = tagsnew;
    }

    public List<ChejiahaoDetailResultTagsnew> getTagsnew() {
        return tagsnew;
    }

    public void setTaskid(String taskid) {
        this.taskid = taskid;
    }

    public String getTaskid() {
        return taskid;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle2(String title2) {
        this.title2 = title2;
    }

    public String getTitle2() {
        return title2;
    }

    public void setTitle3(String title3) {
        this.title3 = title3;
    }

    public String getTitle3() {
        return title3;
    }

    public void setTitle_edit_time(Date title_edit_time) {
        this.title_edit_time = title_edit_time;
    }

    public Date getTitle_edit_time() {
        return title_edit_time;
    }

    public void setVideo(ChejiahaoDetailResultVideo video) {
        this.video = video;
    }

    public ChejiahaoDetailResultVideo getVideo() {
        return video;
    }

    public void setVideotype(int videotype) {
        this.videotype = videotype;
    }

    public int getVideotype() {
        return videotype;
    }

    public void setWaituitime(Date waituitime) {
        this.waituitime = waituitime;
    }

    public Date getWaituitime() {
        return waituitime;
    }

    public void setYouxuantime(Date youxuantime) {
        this.youxuantime = youxuantime;
    }

    public Date getYouxuantime() {
        return youxuantime;
    }

}