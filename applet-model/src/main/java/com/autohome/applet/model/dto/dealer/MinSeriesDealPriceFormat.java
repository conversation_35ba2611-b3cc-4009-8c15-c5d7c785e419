package com.autohome.applet.model.dto.dealer;


import java.io.Serializable;

public class MinSeriesDealPriceFormat  implements Serializable {
    private static final long serialVersionUID = -18859510098669884L;

    private int seriesId;

    private int specId;

    private String mainTitle;

    private String subTitle;

    private String buttonTitle;

    private Integer bargainUV;

    private Integer lastMonthDealPrice;

    private String lastMonthDealPriceFormat;

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getMainTitle() {
        return mainTitle;
    }

    public void setMainTitle(String mainTitle) {
        this.mainTitle = mainTitle;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getButtonTitle() {
        return buttonTitle;
    }

    public void setButtonTitle(String buttonTitle) {
        this.buttonTitle = buttonTitle;
    }

    public Integer getBargainUV() {
        return bargainUV;
    }

    public void setBargainUV(Integer bargainUV) {
        this.bargainUV = bargainUV;
    }

    public Integer getLastMonthDealPrice() {
        return lastMonthDealPrice;
    }

    public void setLastMonthDealPrice(Integer lastMonthDealPrice) {
        this.lastMonthDealPrice = lastMonthDealPrice;
    }

    public String getLastMonthDealPriceFormat() {
        return lastMonthDealPriceFormat;
    }

    public void setLastMonthDealPriceFormat(String lastMonthDealPriceFormat) {
        this.lastMonthDealPriceFormat = lastMonthDealPriceFormat;
    }
}
