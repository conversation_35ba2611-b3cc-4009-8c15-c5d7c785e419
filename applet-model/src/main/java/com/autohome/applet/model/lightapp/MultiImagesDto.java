package com.autohome.applet.model.lightapp;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MultiImagesDto {
    private Integer appendingId;
    private Integer coverType;
    private Integer during;
    private String duringString;
    private Integer id;
    private String img;
    private String imgKey;
    @JsonProperty(value = "is_del")
    private Integer isDel;
    private Integer koubeiId;
    private Integer photoId;
    private Integer userSelected;
    private Integer videoId;

    public Integer getAppendingId() {
        return appendingId;
    }

    public void setAppendingId(Integer appendingId) {
        this.appendingId = appendingId;
    }

    public Integer getCoverType() {
        return coverType;
    }

    public void setCoverType(Integer coverType) {
        this.coverType = coverType;
    }

    public Integer getDuring() {
        return during;
    }

    public void setDuring(Integer during) {
        this.during = during;
    }

    public String getDuringString() {
        return duringString;
    }

    public void setDuringString(String duringString) {
        this.duringString = duringString;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getImgKey() {
        return imgKey;
    }

    public void setImgKey(String imgKey) {
        this.imgKey = imgKey;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Integer getKoubeiId() {
        return koubeiId;
    }

    public void setKoubeiId(Integer koubeiId) {
        this.koubeiId = koubeiId;
    }

    public Integer getPhotoId() {
        return photoId;
    }

    public void setPhotoId(Integer photoId) {
        this.photoId = photoId;
    }

    public Integer getUserSelected() {
        return userSelected;
    }

    public void setUserSelected(Integer userSelected) {
        this.userSelected = userSelected;
    }

    public Integer getVideoId() {
        return videoId;
    }

    public void setVideoId(Integer videoId) {
        this.videoId = videoId;
    }
}
