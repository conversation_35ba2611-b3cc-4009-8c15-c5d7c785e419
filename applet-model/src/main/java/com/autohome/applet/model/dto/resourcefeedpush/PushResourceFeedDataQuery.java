package com.autohome.applet.model.dto.resourcefeedpush;

import com.autohome.applet.util.DateHelper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

@Data
public class PushResourceFeedDataQuery {
    private String daily;
    private String starttime;
    private String endtime;
    private int offset;
    private Integer count;
    /**
     * 1 原创文章
     * 帖子	5
     * 论坛视频	66（本期暂不推送）
     * 精选论坛帖	33
     * 问答	74
     * 车家号长文	12
     * 车家号轻文	13
     *
     * 新增	原创轻文	700112
     * 新增	口碑	11
     * 新增	车主价格	36
     * */
    private List<Integer> bizTypeList;

    public Integer getCount(){
        if(count == null){
            return 500;
        }
        return count;
    }

    public List<Integer> getBizTypeList() {
        if(CollectionUtils.isEmpty(bizTypeList)){
            return Arrays.asList(1,5,12,13,33,74,700112); //本次暂不推送视频
        }
        return bizTypeList;
    }

    public void setBizTypeList(List<Integer> bizTypeList) {
        this.bizTypeList = bizTypeList;
    }

    public String getStarttime() {
        if(StringUtils.isNoneEmpty(starttime)){
            return starttime;
        }
        return DateHelper.serialize(DateHelper.beforeNDaysDate(2), DateHelper.DATEFORMAT_ONLY_DATE);
    }

    public String getEndtime() {
        if(StringUtils.isNoneEmpty(endtime)){
            return endtime;
        }
        return DateHelper.serialize(DateHelper.beforeNDaysDate(1), DateHelper.DATEFORMAT_ONLY_DATE);
    }

    public String getDaily() {
        String startTime = this.getStarttime();
        String tmpStartDate = startTime.split(" ")[0];
        return tmpStartDate;
    }
}
