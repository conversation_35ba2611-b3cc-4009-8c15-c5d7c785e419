package com.autohome.applet.model.dto.car;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AllBrandInfoDto {
    private int total;
    private List<BrandInfoDto> branditems;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BrandInfoDto{
        /**
         * 品牌id
         * */
        private Integer id;
        /**
         * 品牌名称
         * */
        private String name;
        /**
         * 品牌连接
         * */
        private String url;
        /**
         * 所属国家
         * */
        private String country;
        /**
         * 首字母
         * */
        private String firstletter;
        /**
         * 创建时间
         * */
        private String createtime;
        /**
         * 编辑时间
         * */
        private String edittime;
        /**
         * 品牌logo,最大尺寸100x100; 替换"brand/50/" 为 'brand/100/"
         * */
        private String logo;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getFirstletter() {
            return firstletter;
        }

        public void setFirstletter(String firstletter) {
            this.firstletter = firstletter;
        }

        public String getCreatetime() {
            return createtime;
        }

        public void setCreatetime(String createtime) {
            this.createtime = createtime;
        }

        public String getEdittime() {
            return edittime;
        }

        public void setEdittime(String edittime) {
            this.edittime = edittime;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }
    }
}
