package com.autohome.applet.model.dto.donate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonateHuaweiModel {
    private List<DonateHuaweiItem> list;
    /**
     * 是否还有更多
     * 0:否(没有), 1:是(有)
     * */
    private int hasMore;

    public static class DonateHuaweiItem {
        /**
         * 暂时定application
         */
        private String contentType;
        /**
         * 服务标题
         */
        private String displayname;
        /**
         * 服务描述
         */
        private String description;
        /**
         * 服务数据更新时间,用于排序,格式2023-05-24 16:35:20
        */
        private String metadataModificationDate;
        private List<String> keyWords;
        /**
         * 缩略图地址（尺寸：48*48)
         */
        private String logoURL;
        /**
         * 排序权重比 （0-100的浮点数值型）
         */
        private BigDecimal rankingHint;
        /**
         * 数据唯一键
         */
        private String uniqueldentifier;
        /**
         * app scheme
         */
        private String dataURI;
//        /**
//         * 数据源类型
//         */
//        private String dataSource;
//        /**
//         * 内容类型
//         */
//        private String viewMark;

        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        public String getDisplayname() {
            return displayname;
        }

        public void setDisplayname(String displayname) {
            this.displayname = displayname;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getMetadataModificationDate() {
            return metadataModificationDate;
        }

        public void setMetadataModificationDate(String metadataModificationDate) {
            this.metadataModificationDate = metadataModificationDate;
        }

        public List<String> getKeyWords() {
            return keyWords;
        }

        public void setKeyWords(List<String> keyWords) {
            this.keyWords = keyWords;
        }

        public String getLogoURL() {
            return logoURL;
        }

        public void setLogoURL(String logoURL) {
            this.logoURL = logoURL;
        }

        public BigDecimal getRankingHint() {
            return rankingHint;
        }

        public void setRankingHint(BigDecimal rankingHint) {
            this.rankingHint = rankingHint;
        }

        public String getUniqueldentifier() {
            return uniqueldentifier;
        }

        public void setUniqueldentifier(String uniqueldentifier) {
            this.uniqueldentifier = uniqueldentifier;
        }

        public String getDataURI() {
            return dataURI;
        }

        public void setDataURI(String dataURI) {
            this.dataURI = dataURI;
        }

//        public String getDataSource() {
//            return dataSource;
//        }
//
//        public void setDataSource(String dataSource) {
//            this.dataSource = dataSource;
//        }
//
//        public String getViewMark() {
//            return viewMark;
//        }
//
//        public void setViewMark(String viewMark) {
//            this.viewMark = viewMark;
//        }
    }
}
