package com.autohome.applet.model.dto.car;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * zhangxiaonan
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpecsMinPriceExtendDTO {

    private Integer rowcount;
    private Integer pagecount;
    private Integer pageindex;

    private List<SpecsMinPriceList> list;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SpecsMinPriceList {
        private Integer specId;
        private Integer minOriginalPrice;
    }

}
