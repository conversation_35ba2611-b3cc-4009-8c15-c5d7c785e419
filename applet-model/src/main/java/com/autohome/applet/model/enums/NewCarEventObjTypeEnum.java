package com.autohome.applet.model.enums;

import java.util.Arrays;

public enum NewCarEventObjTypeEnum {
    NULL(-1, "null"),
    原创文章(1, "cms"),
    原创快讯(2, "cms&cms_createsource=2"),//特殊类型 mainDataType=cms&cms_createsource=2是原创快讯
    原创视频(3, "video");

    private int objType;
    private String mainDataType;

    NewCarEventObjTypeEnum(int objType, String mainDataType) {
        this.objType = objType;
        this.mainDataType = mainDataType;
    }

    public int getObjType() {
        return objType;
    }

    public String getMainDataType() {
        return mainDataType;
    }

    public static NewCarEventObjTypeEnum getByMainDataType(String mainDataType) {
        return Arrays.stream(NewCarEventObjTypeEnum.values())
                .filter(e -> e.getMainDataType().equalsIgnoreCase(mainDataType))
                .findFirst()
                .orElse(NULL);
    }
}
