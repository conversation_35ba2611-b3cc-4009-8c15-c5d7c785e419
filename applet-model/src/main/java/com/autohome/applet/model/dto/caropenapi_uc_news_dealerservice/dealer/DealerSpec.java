package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/30.
 */
public class DealerSpec {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<SpecGroupList> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<SpecGroupList> getList() {
        return list;
    }

    public void setList(List<SpecGroupList> list) {
        this.list = list;
    }

    public static class SpecGroupList {

        @JsonProperty("IsIM")
        private int isim;
        @JsonProperty("IsYXL")
        private int isyxl;
        @JsonProperty("VolumeGroupName")
        private String volumegroupname;
        @JsonProperty("SpecList")
        private List<Speclist> speclist;

        public void setIsim(int isim) {
            this.isim = isim;
        }

        public int getIsim() {
            return isim;
        }

        public void setIsyxl(int isyxl) {
            this.isyxl = isyxl;
        }

        public int getIsyxl() {
            return isyxl;
        }

        public void setVolumegroupname(String volumegroupname) {
            this.volumegroupname = volumegroupname;
        }

        public String getVolumegroupname() {
            return volumegroupname;
        }

        public void setSpeclist(List<Speclist> speclist) {
            this.speclist = speclist;
        }

        public List<Speclist> getSpeclist() {
            return speclist;
        }

    }

    public static class Speclist {

        @JsonProperty("SpecId")
        private int specid;
        @JsonProperty("SpecName")
        private String specname;
        @JsonProperty("SeriesName")
        private String seriesname;
        @JsonProperty("MinOriginalPrice")
        private int minoriginalprice;
        @JsonProperty("MaxOriginalPrice")
        private int maxoriginalprice;
        @JsonProperty("MinPrice")
        private int minprice;
        @JsonProperty("MaxPrice")
        private int maxprice;
        @JsonProperty("ImageUrl")
        private String imageurl;
        @JsonProperty("HasPromotion")
        private int haspromotion;

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setMinoriginalprice(int minoriginalprice) {
            this.minoriginalprice = minoriginalprice;
        }

        public int getMinoriginalprice() {
            return minoriginalprice;
        }

        public void setMaxoriginalprice(int maxoriginalprice) {
            this.maxoriginalprice = maxoriginalprice;
        }

        public int getMaxoriginalprice() {
            return maxoriginalprice;
        }

        public void setMinprice(int minprice) {
            this.minprice = minprice;
        }

        public int getMinprice() {
            return minprice;
        }

        public void setMaxprice(int maxprice) {
            this.maxprice = maxprice;
        }

        public int getMaxprice() {
            return maxprice;
        }

        public void setImageurl(String imageurl) {
            this.imageurl = imageurl;
        }

        public String getImageurl() {
            return imageurl;
        }

        public void setHaspromotion(int haspromotion) {
            this.haspromotion = haspromotion;
        }

        public int getHaspromotion() {
            return haspromotion;
        }

    }
}
