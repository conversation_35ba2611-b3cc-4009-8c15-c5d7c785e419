package com.autohome.applet.model.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum SeasonEnum {
    SPRING("spring",1,"春"),
    SUMMER("summer",2,"夏"),
    AUTUMN("autumn",3,"秋"),
    WINTER("winter",4,"冬");
    private final String name;
    private final String cnName;
    private final int order;

    SeasonEnum(String name, int order, String cnName) {
        this.name = name;
        this.cnName=cnName;
        this.order = order;
    }

    public static String getCnName(String name){
        SeasonEnum seasonEnum = Arrays.stream(SeasonEnum.values()).filter(e -> e.getName().equals(name)).findFirst().orElse(null);
        if(null!=seasonEnum){
            return seasonEnum.getCnName();
        }
        return "";
    }
	public static int getOrderByCnName(String name){
		SeasonEnum seasonEnum = Arrays.stream(SeasonEnum.values()).filter(e -> e.getCnName().equals(name)).findFirst().orElse(null);
		if(null!=seasonEnum){
			return seasonEnum.getOrder();
		}
		return 0;
	}
}