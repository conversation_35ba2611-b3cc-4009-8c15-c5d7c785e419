package com.autohome.applet.model.dto.netcoreapi.output;

public class SeriesSpecInfoOut {
    /// <summary>
    /// 车型id
    ///</summary>
    private int id;
    /// <summary>
    /// 车型名称
    ///</summary>
    private String name;
    /// <summary>
    ///  车型代表图
    ///</summary>
    private String logo;

    private int yearid;
    /// <summary>
    ///  年贷款
    ///</summary>
    private int yearname;
    /// <summary>
    /// 车型指导价(低价)
    ///</summary>
    private int minprice;
    /// <summary>
    ///车型指导价(高价)
    ///</summary>
    private int maxprice;

    /// <summary>
    /// 车型状态
    ///</summary>
    private int state;

    private String seriesid;

    private String seriesname;

    private String serieslogo;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public int getYearid() {
        return yearid;
    }

    public void setYearid(int yearid) {
        this.yearid = yearid;
    }

    public int getYearname() {
        return yearname;
    }

    public void setYearname(int yearname) {
        this.yearname = yearname;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(int maxprice) {
        this.maxprice = maxprice;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(String seriesid) {
        this.seriesid = seriesid;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSerieslogo() {
        return serieslogo;
    }

    public void setSerieslogo(String serieslogo) {
        this.serieslogo = serieslogo;
    }
}
