package com.autohome.applet.model.dto.douyin;

import lombok.Data;
import java.util.List;

@Data
public class HangQingDto {

    private Tag tag;
    private Seriesinfo seriesinfo;
    private List<Items> items;
    private String pageid;
    private Boolean hasmore;

    @Data
    public static class Tag {
        private int id;
        private String name;
        private int isdel;
        private int num;
        private String createdstime;
        private String modifiedstime;
    }

    @Data
    public static class Seriesinfo {
        private int seriesid;
        private String seriesname;
        private int maxprice;
        private int minprice;
        private String brandname;
        private String brand_logo;
        private double koubei_average;
        private int brandid;
        private String pnglogo;
        private int type;
        private List<Btnlist> btnlist;

        @Data
        public static class Btnlist {
            private String linkurl;
            private String mlinkurl;
            private String name;
        }
    }

    @Data
    public static class Items {
        private int id;
        private String title;
        private int hangqingtype;
        private String img;
        private int authorid;
        private String authorhead;
        private String authorname;
        private int ispublish;
        private String publishtime;
        private String publishprovince;
        private String description;
        private int isdel;
        private String pushid;
        private int maxpriceoff;
        private int manualstate;
        private int cityid;
        private String dealerjson;
    }
}
