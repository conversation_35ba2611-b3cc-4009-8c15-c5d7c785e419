package com.autohome.applet.model.dto.donate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonateIosModel {
    private List<DonateIosItem> list;
    /**
     * 是否还有更多
     * 0:否(没有), 1:是(有)
     * */
    private int hasMore;

    public static class DonateIosItem {
        /**
         * 服务标题
         */
        private String title;
        /**
         * 服务描述
         */
        private String description;
        private List<String> keyword;
//        /**
//         * 缩略图地址（尺寸：48*48)
//         */
//        private String imageid;
//        /**
//         * 数据唯一键
//         */
//        private String uniqueldentifier;
        /**
         * app schema
         */
        private String url;


//        /**
//         * 数据源类型
//         */
//        private String dataSource;
//        /**
//         * 内容类型
//         */
//        private String viewMark;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<String> getKeyword() {
            return keyword;
        }

        public void setKeyword(List<String> keyword) {
            this.keyword = keyword;
        }

//        public String getUniqueldentifier() {
//            return uniqueldentifier;
//        }
//
//        public void setUniqueldentifier(String uniqueldentifier) {
//            this.uniqueldentifier = uniqueldentifier;
//        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

//        public String getDataSource() {
//            return dataSource;
//        }
//
//        public void setDataSource(String dataSource) {
//            this.dataSource = dataSource;
//        }
//
//        public String getViewMark() {
//            return viewMark;
//        }
//
//        public void setViewMark(String viewMark) {
//            this.viewMark = viewMark;
//        }
    }
}
