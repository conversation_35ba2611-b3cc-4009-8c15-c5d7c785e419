package com.autohome.applet.model.dto.netcoreapi.series;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MiniPriceDealPriceDetail {

    @JsonProperty("AllowancePrice")
    private int allowancePrice;

    @JsonProperty("ClassId")
    private int classId;

    @JsonProperty("CommercialInsurance")
    private int commercialInsurance;

    @JsonProperty("CommercialInsurancePrice")
    private int commercialInsurancePrice;

    @JsonProperty("CompulsoryInsurance")
    private int compulsoryInsurance;

    @JsonProperty("CompulsoryInsurancePrice")
    private int compulsoryInsurancePrice;

    @JsonProperty("CreateTime")
    private String createTime;

    @JsonProperty("DealerID")
    private long dealerID;

    @JsonProperty("EquipCarId")
    private int equipCarId;

    @JsonProperty("ExchangeSubsidyPrice")
    private int exchangeSubsidyPrice;

    @JsonProperty("FactorySubsidyPrice")
    private int factorySubsidyPrice;

    @JsonProperty("GovSubsidyPrice")
    private int govSubsidyPrice;

    @JsonProperty("InsuranceDiscount")
    private int insuranceDiscount;

    public int getAllowancePrice() {
        return allowancePrice;
    }

    public void setAllowancePrice(int allowancePrice) {
        this.allowancePrice = allowancePrice;
    }

    public int getClassId() {
        return classId;
    }

    public void setClassId(int classId) {
        this.classId = classId;
    }

    public int getCommercialInsurance() {
        return commercialInsurance;
    }

    public void setCommercialInsurance(int commercialInsurance) {
        this.commercialInsurance = commercialInsurance;
    }

    public int getCommercialInsurancePrice() {
        return commercialInsurancePrice;
    }

    public void setCommercialInsurancePrice(int commercialInsurancePrice) {
        this.commercialInsurancePrice = commercialInsurancePrice;
    }

    public int getCompulsoryInsurance() {
        return compulsoryInsurance;
    }

    public void setCompulsoryInsurance(int compulsoryInsurance) {
        this.compulsoryInsurance = compulsoryInsurance;
    }

    public int getCompulsoryInsurancePrice() {
        return compulsoryInsurancePrice;
    }

    public void setCompulsoryInsurancePrice(int compulsoryInsurancePrice) {
        this.compulsoryInsurancePrice = compulsoryInsurancePrice;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public long getDealerID() {
        return dealerID;
    }

    public void setDealerID(long dealerID) {
        this.dealerID = dealerID;
    }

    public int getEquipCarId() {
        return equipCarId;
    }

    public void setEquipCarId(int equipCarId) {
        this.equipCarId = equipCarId;
    }

    public int getExchangeSubsidyPrice() {
        return exchangeSubsidyPrice;
    }

    public void setExchangeSubsidyPrice(int exchangeSubsidyPrice) {
        this.exchangeSubsidyPrice = exchangeSubsidyPrice;
    }

    public int getFactorySubsidyPrice() {
        return factorySubsidyPrice;
    }

    public void setFactorySubsidyPrice(int factorySubsidyPrice) {
        this.factorySubsidyPrice = factorySubsidyPrice;
    }

    public int getGovSubsidyPrice() {
        return govSubsidyPrice;
    }

    public void setGovSubsidyPrice(int govSubsidyPrice) {
        this.govSubsidyPrice = govSubsidyPrice;
    }

    public int getInsuranceDiscount() {
        return insuranceDiscount;
    }

    public void setInsuranceDiscount(int insuranceDiscount) {
        this.insuranceDiscount = insuranceDiscount;
    }

    public int getInventoryState() {
        return inventoryState;
    }

    public void setInventoryState(int inventoryState) {
        this.inventoryState = inventoryState;
    }

    public boolean getIsAuthenticPrice() {
        return isAuthenticPrice;
    }

    public void setIsAuthenticPrice(boolean isAuthenticPrice) {
        isAuthenticPrice = isAuthenticPrice;
    }

    public int getIsPriceOff() {
        return isPriceOff;
    }

    public void setIsPriceOff(int isPriceOff) {
        this.isPriceOff = isPriceOff;
    }

    public String getLastTime() {
        return lastTime;
    }

    public void setLastTime(String lastTime) {
        this.lastTime = lastTime;
    }

    public int getLicenseTaxPrice() {
        return licenseTaxPrice;
    }

    public void setLicenseTaxPrice(int licenseTaxPrice) {
        this.licenseTaxPrice = licenseTaxPrice;
    }

    public long getNewsTemplateId() {
        return newsTemplateId;
    }

    public void setNewsTemplateId(long newsTemplateId) {
        this.newsTemplateId = newsTemplateId;
    }

    public int getOtherPrice() {
        return otherPrice;
    }

    public void setOtherPrice(int otherPrice) {
        this.otherPrice = otherPrice;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getPriceOff() {
        return priceOff;
    }

    public void setPriceOff(int priceOff) {
        this.priceOff = priceOff;
    }

    public int getPurchaseTax() {
        return purchaseTax;
    }

    public void setPurchaseTax(int purchaseTax) {
        this.purchaseTax = purchaseTax;
    }

    public int getPurchaseTaxPrice() {
        return purchaseTaxPrice;
    }

    public void setPurchaseTaxPrice(int purchaseTaxPrice) {
        this.purchaseTaxPrice = purchaseTaxPrice;
    }

    public int getSeriesID() {
        return seriesID;
    }

    public void setSeriesID(int seriesID) {
        this.seriesID = seriesID;
    }

    public int getSpecID() {
        return specID;
    }

    public void setSpecID(int specID) {
        this.specID = specID;
    }

    public int getStrikePrice() {
        return strikePrice;
    }

    public void setStrikePrice(int strikePrice) {
        this.strikePrice = strikePrice;
    }

    public int getVehicleTaxPrice() {
        return vehicleTaxPrice;
    }

    public void setVehicleTaxPrice(int vehicleTaxPrice) {
        this.vehicleTaxPrice = vehicleTaxPrice;
    }

    @JsonProperty("InventoryState")
    private int inventoryState;

    @JsonProperty("IsAuthenticPrice")
    private boolean isAuthenticPrice;

    @JsonProperty("IsPriceOff")
    private int isPriceOff;

    @JsonProperty("LastTime")
    private String lastTime;

    @JsonProperty("LicenseTaxPrice")
    private int licenseTaxPrice;

    @JsonProperty("NewsTemplateId")
    private long newsTemplateId;

    @JsonProperty("OtherPrice")
    private int otherPrice;

    @JsonProperty("Price")
    private int price;

    @JsonProperty("PriceOff")
    private int priceOff;

    @JsonProperty("PurchaseTax")
    private int purchaseTax;

    @JsonProperty("PurchaseTaxPrice")
    private int purchaseTaxPrice;

    @JsonProperty("SeriesID")
    private int seriesID;

    @JsonProperty("SpecID")
    private int specID;

    @JsonProperty("StrikePrice")
    private int strikePrice;

    @JsonProperty("VehicleTaxPrice")
    private int vehicleTaxPrice;


}