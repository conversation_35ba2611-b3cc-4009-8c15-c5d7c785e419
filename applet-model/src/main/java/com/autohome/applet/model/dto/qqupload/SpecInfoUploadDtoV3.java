package com.autohome.applet.model.dto.qqupload;
import lombok.Data;

@Data
public class SpecInfoUploadDtoV3 {

    /**
     * 数据的唯一标识，请保证唯一性
     */
    private String key;

    /**
     * display，必填标签。最多出现1次。
     */
    private Display display;


    private String location;

    @Data
    public static class Display {

        private String title;
        private String url;
        private String name;
        private String byname;
        private String brand;
        private String subBrand;
//        private String simplename;
        private String quotedPrice;
        private String guidedPrice;
        private String carspailiang;
        private String oilWear;
        private String peizhiLink;
        private String baojiaLink;
        private String luntanurl;
        private String jibie;
//        private String leixing;
        private CarInfo carInfo;
        private String hot;
//        private String chandi;
        private String jiegou;
        private String place;
        private String showurl;
        private String more;
        private String morelink;
//        private String enterprise;
//        private String en_place;
//        private String articleListUrl;
//        private String xundijiaLink;
//        private String video_miaodong;
//        private String video_instruction;
        private String picLink;

        @Data
        public static class CarInfo {

            private String carName;
            private String carUrl;
            private String saleState;
            private String carmguidedPrice;
            private String carPailiang;
            private String niankuan;
            private String banxing;
            private String carJiegou;
            private String chekuanxinghao;
//            private String color;
//            private String carHot;
            private String carMinPriceURL;
//            private String seat;
//            private String oil;
//            private String door;

        }
    }
}
