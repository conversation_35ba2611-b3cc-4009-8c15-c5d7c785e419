package com.autohome.applet.model.dto.car;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FctItemListDto {
    private int total;
    private List<FctItem> fctitems;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<FctItem> getFctitems() {
        return fctitems;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FctItem {
        private int id;
        private String name;
        private String url;
        private String isimport;
        private String createtime;
        private String edittime;
        private String firstletter;
        private String logo;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getIsimport() {
            return isimport;
        }

        public void setIsimport(String isimport) {
            this.isimport = isimport;
        }

        public String getCreatetime() {
            return createtime;
        }

        public void setCreatetime(String createtime) {
            this.createtime = createtime;
        }

        public String getEdittime() {
            return edittime;
        }

        public void setEdittime(String edittime) {
            this.edittime = edittime;
        }

        public String getFirstletter() {
            return firstletter;
        }

        public void setFirstletter(String firstletter) {
            this.firstletter = firstletter;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }
    }
}
