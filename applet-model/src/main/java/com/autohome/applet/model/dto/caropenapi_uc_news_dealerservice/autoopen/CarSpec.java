package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.autoopen;

import com.autohome.applet.model.dto.netcoreapi.jiage.CarSpecChart;
import com.autohome.applet.model.dto.netcoreapi.jiage.CarSpecList;

import java.util.List;

public class CarSpec {


    private long averageprice;
    private List<CarSpecChart> chartprice;
    private long fctprice;
    private List<CarSpecList> list;
    private int ownercount;
    private int pagecount;
    private int pagesize;
    private int rowcont;
    private int seriesid;
    private String seriesname;
    private int specid;
    private String specname;

    public void setAverageprice(long averageprice) {
        this.averageprice = averageprice;
    }

    public long getAverageprice() {
        return averageprice;
    }

    public void setChartprice(List<CarSpecChart> chartprice) {
        this.chartprice = chartprice;
    }

    public List<CarSpecChart> getChartprice() {
        return chartprice;
    }

    public void setFctprice(long fctprice) {
        this.fctprice = fctprice;
    }

    public long getFctprice() {
        return fctprice;
    }

    public void setList(List<CarSpecList> list) {
        this.list = list;
    }

    public List<CarSpecList> getList() {
        return list;
    }

    public void setOwnercount(int ownercount) {
        this.ownercount = ownercount;
    }

    public int getOwnercount() {
        return ownercount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagesize(int pagesize) {
        this.pagesize = pagesize;
    }

    public int getPagesize() {
        return pagesize;
    }

    public void setRowcont(int rowcont) {
        this.rowcont = rowcont;
    }

    public int getRowcont() {
        return rowcont;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getSpecname() {
        return specname;
    }

}