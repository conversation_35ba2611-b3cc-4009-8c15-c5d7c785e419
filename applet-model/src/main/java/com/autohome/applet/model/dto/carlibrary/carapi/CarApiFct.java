package com.autohome.applet.model.dto.carlibrary.carapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CarApiFct {

    @JsonProperty("fctid")
    private int fctId;

    @JsonProperty("fctname")
    private String fctName;

    @JsonProperty("fctPy")
    private String fctPy;

    @JsonProperty("seriesplace")
    private String seriesPlace;

    @JsonProperty("seriesPlaceNum")
    private int seriesplacenum;

    @JsonProperty("serieslist")
    private List<CarApiSeries> seriesList;
}
