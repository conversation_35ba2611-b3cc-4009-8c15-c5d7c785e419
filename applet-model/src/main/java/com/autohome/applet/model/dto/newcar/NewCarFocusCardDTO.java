package com.autohome.applet.model.dto.newcar;

public class NewCarFocusCardDTO {
    private int seriesId;
    private String seriesName;
    private String seriesImg;
    /**
     * 配置的广告位
     * */
    private int positionId;
    /**
     * 排名
     * */
    private int rank;
    /**
     * 上市日期 08/28上市
     * */
    private String ontime;
    /**
     * 新车两年
     * */
    private String lightpoint;
    /**
     * 是否开通消息
     * 0 否， 1 开通
     * */
    private int isNotise;

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSeriesImg() {
        return seriesImg;
    }

    public void setSeriesImg(String seriesImg) {
        this.seriesImg = seriesImg;
    }

    public int getPositionId() {
        return positionId;
    }

    public void setPositionId(int positionId) {
        this.positionId = positionId;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public String getOntime() {
        return ontime == null ? "" : ontime;
    }

    public void setOntime(String ontime) {
        this.ontime = ontime;
    }

    public String getLightpoint() {
        return lightpoint == null ? "" : lightpoint;
    }

    public void setLightpoint(String lightpoint) {
        this.lightpoint = lightpoint;
    }

    public int getIsNotise() {
        return isNotise;
    }

    public void setIsNotise(int isNotise) {
        this.isNotise = isNotise;
    }
}
