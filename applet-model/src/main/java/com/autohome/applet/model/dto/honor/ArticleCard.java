package com.autohome.applet.model.dto.honor;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ArticleCard {

    private String cardStyle = "resultPageFeeds"; // 卡片类型 {resultPageFeeds:图文，02:纯文本}
    private String accessCode; // 接入编码
    private String contentId; // 内容ID。唯一内容标识，会基于此内容ID更新数据。须与生成sign所用contentId一致。
    private int op; // 操作符。{ 1:添加, 2:删除 }，1表示数据推送，2 表示bad case删除
    private String title; // 展示用标题。纯文本类型，可以没有标题。
    private String description; // 正式内容。图文类型不用于展示，强烈建议cp提供，有助于内容露出；纯文本类型必须提供。
    private String keywords; // 辅助搜索用关键词。多个关键词以英文逗号，分割
    private String language; // 语言。{“zh_cn”:简体中文 }，出海可能会用这个语言做过滤。
    private double contentScore; // 内容的分数。受用户欢迎程度。可以是点赞数或分数，可作排序评分。
//    private int isIsv; // 是否蓝标。{ 1:是, 0:否 }。 ISV最早是Independent Software Vendors 的英文缩写，如果是ISV会有蓝V展示在卡片右下角，默认为0
    private String cpName; // 内容提供商名称。展示在卡片左下角
    private String cpIconUrl; // 内容提供商图标链接。展示在卡片左下角，限制2M
    private String videoUrl; // 封面视频链接
    private int videoType; // 封面视频类型。 { 1:mp4, 2:avi, 3:wmv, 4:mpeg, 5:rmvb }，默认为1
    private String videoSpecs; // 封面视频宽高比，展示时等比例做缩放或裁剪。如果竖版视频，宽高比是 9:16，则此字段值为"9:16"，其他常见比例如 9:16; 16:9; 4:3; 3:4。
    private String imgUrl; // 封面图链接，限制2M
    private int imgType; // 封面图类型。{ 1:jpg, 2:png, 3:gif }，默认为1
    private String imgSpecs; // 封面图片宽高比，展示时等比例做缩放或裁剪。如果竖版图片，宽高比是 9:16，则此字段值为"9:16"，其他常见比例如 9:16; 16:9; 4:3; 3:4
    private String webLink; // 【跳转内容】跳转网页端的链接，保底
    private String nativeLink; // 【跳转内容】跳转应用的链接
    private String nativeAppName; // 【跳转内容】跳转应用使用的应用名
    private String nativePackageName; // 【跳转内容】跳转应用使用的应用包名(若填写了nativeLink，该参数必填)
    private int nativeMinVersionCode; // 【跳转内容】跳转应用使用的应用最小支持版本。端侧会获取当前应用版本与此最小支持版本对比，判断是否跳转(若填写了nativeLink，该参数必填)
    private int minAndroidApiLevel; // 【跳转内容】跳转应用使用的安卓系统最小支持版本。(若填写了nativeLink，该参数必填)
    private String quickAppLink; // 【跳转内容】跳转快应用的链接，需保证快应用在荣耀上架
    private String quickAppAppName; // 【跳转内容】跳转快应用使用的快应用名
    private String quickAppPackageName; // 【跳转内容】跳转快应用使用的快应用包名(若填写了quickAppLink，该参数必填)
    private int quickAppMinVersionCode; // 【跳转内容】跳转快应用最小支持版本(若填写了quickAppLink，该参数必填)
    private String minPlatformVersion; // 【跳转内容】跳转快应用使用的快应用平台最小支持版本。端侧会获取当前快应用平台版本与此最小支持版本对比，判断是否跳转(若填写了quickAppLink，该参数必填)
    private String mpAppName; // 【跳转内容】跳转微信小程序使用的微信应用名，默认为微信
    private String mpPackageName; // 【跳转内容】跳转微信小程序使用的微信应用包名，默认为com.tencent.mm
    private int mpMinVersionCode; // 【跳转内容】跳转微信小程序最小支持版本(若填写了mpAppId，该参数必填)
    private String mpType; // 【跳转内容】跳转微信小程序类型
    private String mpAppId; // 【跳转内容】跳转微信小程序appId
    private String mpPath; // 【跳转内容】跳转微信小程序path(若填写了mpAppId，该参数必填)
    private String mpKey; // 【跳转内容】跳转微信小程序key，默认为空
    private String cpWebLink; // 【跳转CP品牌】跳转网页端的链接，保底。
    private String cpNativeLink; // 【跳转CP品牌】跳转应用的链接
    private String cpNativeAppName; // 【跳转CP品牌】跳转应用使用的应用名
    private String cpNativePackageName; // 【跳转CP品牌】跳转应用使用的应用包名(若填写了cpNativeLink，该参数必填)
    private int cpNativeMinVersionCode; // 【跳转CP品牌】跳转应用使用的应用最小支持版本。端侧会获取当前应用版本与此最小支持版本对比，判断是否跳转。(若填写了cpNativeLink，该参数必填)
    private int cpMinAndroidApiLevel; // 【跳转CP品牌】跳转应用使用的安卓系统最小支持版本(若填写了cpNativeLink，该参数必填)
    private String cpQuickAppLink; // 【跳转CP品牌】跳转快应用的链接，需保证快应用在荣耀上架。
    private String cpQuickAppAppName; // 【跳转CP品牌】跳转快应用使用的快应用名
    private String cpQuickAppPackageName; // 【跳转CP品牌】 跳转快应用使用的快应用包名(若填写了cpQuickAppLink，该参数必填)
    private int cpQuickAppMinVersionCode; // 【跳转CP品牌】跳转快应用最小支持版本(若填写了cpQuickAppLink，该参数必填)
    private int cpMinPlatformVersion; // 【跳转CP品牌】跳转快应用使用的快应用平台最小支持版本。端侧会获取当前快应用平台版本与此最小支持版本对比，判断是否跳转。(若填写了cpQuickAppLink，该参数必填)
    private String cpMpAppName; // 【跳转CP品牌】跳转微信小程序使用的微信应用名，默认为微信
    private String cpMpPackageName; // 【跳转CP品牌】跳转微信小程序使用的微信应用包名，默认为com.tencent.mm
    private int cpMpMinVersionCode; // 【跳转CP品牌】跳转微信小程序最小支持版本(若填写了cpMpAppId，该参数必填)
    private String cpMpType; // 【跳转CP品牌】跳转微信小程序类型
    private String cpMpAppId; // 【跳转CP品牌】跳转微信小程序appId
    private String cpMpPath; // 【跳转CP品牌】跳转微信小程序path(若填写了cpMpAppId，该参数必填)
    private String cpMpKey; //【跳转CP品牌】跳转微信小程序key，默认为空
}