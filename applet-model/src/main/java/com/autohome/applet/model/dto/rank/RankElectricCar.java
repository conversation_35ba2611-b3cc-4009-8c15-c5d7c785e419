package com.autohome.applet.model.dto.rank;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class RankElectricCar {
    @JsonProperty("seriesid")
    private String seriesId;

    @JsonProperty("rank")
    private String rank;

    @JsonProperty("rankchange")
    private int rankChange;

    @JsonProperty("seriesimage")
    private String seriesImage;

    @JsonProperty("seriesname")
    private String seriesName;

    @JsonProperty("scorevalue")
    private String scoreValue;

    @JsonProperty("scoretip")
    private String scoreTip;

    @JsonProperty("priceinfo")
    private String priceInfo;

    @JsonProperty("linkurl")
    private String linkUrl;

    @JsonProperty("salecount")
    private int saleCount;

    @JsonProperty("energetype")
    private int energyType;

    @JsonProperty("historylinkname")
    private String historyLinkName;

    @JsonProperty("historylinkurl")
    private String historyLinkUrl;

    @JsonProperty("rightpricetitle")
    private String rightPriceTitle;

    @JsonProperty("pricelinkurl")
    private String priceLinkUrl;

    @JsonProperty("rcmtext")
    private String rcmText;

    @JsonProperty("rcmdesc")
    private String rcmDesc;

    @JsonProperty("specname")
    private String specName;

    @JsonProperty("righttextone")
    private String rightTextOne;

    @JsonProperty("righttexttwo")
    private String rightTextTwo;

    @JsonProperty("righttexttwolinkurl")
    private String rightTextTwoLinkUrl;

    public String getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(String seriesId) {
        this.seriesId = seriesId;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public int getRankChange() {
        return rankChange;
    }

    public void setRankChange(int rankChange) {
        this.rankChange = rankChange;
    }

    public String getSeriesImage() {
        return seriesImage;
    }

    public void setSeriesImage(String seriesImage) {
        this.seriesImage = seriesImage;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getScoreValue() {
        return scoreValue;
    }

    public void setScoreValue(String scoreValue) {
        this.scoreValue = scoreValue;
    }

    public String getScoreTip() {
        return scoreTip;
    }

    public void setScoreTip(String scoreTip) {
        this.scoreTip = scoreTip;
    }

    public String getPriceInfo() {
        return priceInfo;
    }

    public void setPriceInfo(String priceInfo) {
        this.priceInfo = priceInfo;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public int getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(int saleCount) {
        this.saleCount = saleCount;
    }

    public int getEnergyType() {
        return energyType;
    }

    public void setEnergyType(int energyType) {
        this.energyType = energyType;
    }

    public String getHistoryLinkName() {
        return historyLinkName;
    }

    public void setHistoryLinkName(String historyLinkName) {
        this.historyLinkName = historyLinkName;
    }

    public String getHistoryLinkUrl() {
        return historyLinkUrl;
    }

    public void setHistoryLinkUrl(String historyLinkUrl) {
        this.historyLinkUrl = historyLinkUrl;
    }

    public String getRightPriceTitle() {
        return rightPriceTitle;
    }

    public void setRightPriceTitle(String rightPriceTitle) {
        this.rightPriceTitle = rightPriceTitle;
    }

    public String getPriceLinkUrl() {
        return priceLinkUrl;
    }

    public void setPriceLinkUrl(String priceLinkUrl) {
        this.priceLinkUrl = priceLinkUrl;
    }

    public String getRcmText() {
        return rcmText;
    }

    public void setRcmText(String rcmText) {
        this.rcmText = rcmText;
    }

    public String getRcmDesc() {
        return rcmDesc;
    }

    public void setRcmDesc(String rcmDesc) {
        this.rcmDesc = rcmDesc;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getRightTextOne() {
        return rightTextOne;
    }

    public void setRightTextOne(String rightTextOne) {
        this.rightTextOne = rightTextOne;
    }

    public String getRightTextTwo() {
        return rightTextTwo;
    }

    public void setRightTextTwo(String rightTextTwo) {
        this.rightTextTwo = rightTextTwo;
    }

    public String getRightTextTwoLinkUrl() {
        return rightTextTwoLinkUrl;
    }

    public void setRightTextTwoLinkUrl(String rightTextTwoLinkUrl) {
        this.rightTextTwoLinkUrl = rightTextTwoLinkUrl;
    }
}
