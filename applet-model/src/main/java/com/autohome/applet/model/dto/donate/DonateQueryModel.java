package com.autohome.applet.model.dto.donate;

import com.autohome.applet.model.enums.DonateDataSourceEnum;
import com.autohome.applet.model.enums.DonateTerminalEnum;
import com.autohome.applet.model.enums.DonateViewMarkEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DonateQueryModel {
    private String beginDate;
    private String endDate;
    private String daily;
    private int userId;
    private String deviceId;
    private int deviceHash;
    private int page;
    private int pageSize;
    private String bizIds;

    /**
     * 定时任务插入日期(将多日的数据存储到当前日期中)
     * */
    private String dailyBeginDate;
    /**
     * 定时任务插入日期(将多日的数据存储到当前日期中)
     * */
    private String dailyEndDate;

    private DonateTerminalEnum donateTerminalEnum;
    private DonateViewMarkEnum donateViewMarkEnum;

    /**
     * 当前数据源
     * */
    private DonateDataSourceEnum donateDataSourceEnum;

//    /**
//     * 剩余数量
//     * */
//    private int remainingAmount;



    /**
     * 捐赠数量
     * */
    private int total;
}
