package com.autohome.applet.model.dto.newenergy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * @description: 车系落地页DTO
 * @author: WangBoWen
 * @date: 2024-01-02
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CarOwnerRealEnduranceOfBaseInfoDTO {


    private SeriesInfo seriesinfo;

    @Data
    public static class SeriesInfo{
        private Integer seriesid;

        private String seriesname;

        private String serieslogo;

        private String fcttext;

        private String fctprice;

        private String imgrighttitle;

        private List<String> imglist;

        private String imglinkurl;

        private String mileagetitle;

        private List<SpecInfo> tablist;

    }

    @Data
    public static class SpecInfo{

        private Integer specid;

        private String tabname;

        private Integer officialdriverange;

        private Integer datatype;

    }
}
