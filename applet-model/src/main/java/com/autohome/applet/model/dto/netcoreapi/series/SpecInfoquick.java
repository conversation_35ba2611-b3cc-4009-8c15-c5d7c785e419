package com.autohome.applet.model.dto.netcoreapi.series;

public class SpecInfoquick {

    private String minNewsPrice;

    public String butiePrice;

    private String differenceprice;

    private String spceprice;

    private String lastMonthDealPriceFormat;

    private int downpayAmount;

    private int monthPay;

    public String getMinNewsPrice() {
        return minNewsPrice;
    }

    public void setMinNewsPrice(String minNewsPrice) {
        this.minNewsPrice = minNewsPrice;
    }

    public String getButiePrice() {
        return butiePrice;
    }

    public void setButiePrice(String butiePrice) {
        this.butiePrice = butiePrice;
    }

    public String getDifferenceprice() {
        return differenceprice;
    }

    public void setDifferenceprice(String differenceprice) {
        this.differenceprice = differenceprice;
    }

    public String getSpceprice() {
        return spceprice;
    }

    public void setSpceprice(String spceprice) {
        this.spceprice = spceprice;
    }

    public String getLastMonthDealPriceFormat() {
        return lastMonthDealPriceFormat;
    }

    public void setLastMonthDealPriceFormat(String lastMonthDealPriceFormat) {
        this.lastMonthDealPriceFormat = lastMonthDealPriceFormat;
    }

    public int getDownpayAmount() {
        return downpayAmount;
    }

    public void setDownpayAmount(int downpayAmount) {
        this.downpayAmount = downpayAmount;
    }

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }
}
