package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashSet;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/17.
 */
//@JSONType(ignores = {"seriesids", "seriesStateGroup"})
public class FctSeries {
    private int pageindex;

    private int size;

    private int total;

    //@JSONField(serialize = false)
    @JsonIgnore
    private String seriesids;

    //    @JSONField(serialize = false)
//    private List<Integer> seriesList;

    private List<Seriesitems> seriesitems;

//    public List<Integer> getSeriesList() {
//        if (seriesitems == null || seriesitems.size() == 0) {
//            return null;
//        }
//        List<Integer> sereiesList = new ArrayList<>();
//        seriesitems.forEach(s -> {
//            sereiesList.add(s.getId());
//        });
//        if (sereiesList == null || sereiesList.size() == 0) {
//            return null;
//        }
//        return sereiesList;
//    }
//
//    public void setSeriesList(List<Integer> seriesList) {
//        this.seriesList = seriesList;
//    }

    public String getSeriesids() {
        if (seriesitems == null || seriesitems.size() == 0) {
            return null;
        }
        HashSet<String> sereiesList = new HashSet<>();
        seriesitems.forEach(s -> {
            sereiesList.add(Integer.toString(s.getId()));
        });
        if (sereiesList == null || sereiesList.size() == 0) {
            return null;
        }
        return String.join(",", sereiesList);
    }

    public void setSeriesids(String seriesids) {
        this.seriesids = seriesids;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<Seriesitems> getSeriesitems() {
        return seriesitems;
    }

    public void setSeriesitems(List<Seriesitems> seriesitems) {
        this.seriesitems = seriesitems;
    }


}
