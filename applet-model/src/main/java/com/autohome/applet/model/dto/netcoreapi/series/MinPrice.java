package com.autohome.applet.model.dto.netcoreapi.series;

import java.util.List;

public class MinPrice {


    private List<MinPriceItem> list;
    private int pagecount;
    private int pageindex;
    private int rowcount;

    public void setList(List<MinPriceItem> list) {
        this.list = list;
    }

    public List<MinPriceItem> getList() {
        return list;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getRowcount() {
        return rowcount;
    }

}