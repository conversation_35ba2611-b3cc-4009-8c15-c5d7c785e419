package com.autohome.applet.model.dto.car;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * zhangxiaonan
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BrandSeariesDTO {

    private Integer brandid;
    private String brandname;
    private String brandimg;
    private String firstletter;

    private List<bbslist> bbslist;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class bbslist {
        private String bbs;
        private Integer bbsid;
        private String bbsname;
        private Integer sort;
    }

}
