package com.autohome.applet.model.dto.car;

import com.autohome.applet.model.dto.carlibrary.SpecDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.SeriesClassPictureDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.Api8Dto;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsResult;
import com.autohome.applet.model.dto.netcoreapi.series.SeriesRate;
import com.autohome.applet.model.dto.newenergy.SeriesConfigInfoResult;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SpecParamDto {
//    //所有车系销量排行信息
//    public RankListpageResult rank;
//    //该品牌下所有支持vr的车系
//    public Set<Integer> seriesIdsSet;

    //一次性查出该品牌下所有车系，并将该车系详情查出来，避免内部循环多次调用
    public Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap;

    // 车型状态，在售还是停产在售
    public Map<Integer, Integer> specStatusMap;

    //在售所有车系和停产在售所有车系
    List<SeriesInfoDto> seriesInfoDtoList;

    //车系下所有车型配置信息
    SeriesConfigInfoResult seriesConfigInfoResult;

    //热门车型
    Integer hotSpecid;

    //车系常规信息
    SeriesInfoDto seriesInfoDto;

//    CarApiBrandOut carApiBrandOut;

    SeriesMinPriceExtendsResult seriesMinPriceExtendsResult;


    int specId = 0;

    //图片卡使用参数    车系实拍首图
    List<Api8Dto.Picitems> api8;

    //根据车系id获取各个类型前五张图片 外观、中空、座椅、其他
    List<SeriesClassPictureDto.Typeitems>  typeitems;

    //获取该车系下所有车型信息
    SpecDto specsBySeriesId;

    //车系下热门车型列表
    List<SeriesRate> hotSpecs;
}
