package com.autohome.applet.model.dto.carlibrary.carapi.out;

import lombok.Data;

import java.util.List;

@Data
public class AllBrandOut {
    private int returncode;
    private String message;
    private Result result;

    @Data
    public static class Result {
        private int total;
        private List<BrandList> brandlist;
    }

    @Data
    public static class BrandList {
        private int id;
        private String name;
        private String logo;
        private String firstletter;
        private String country;
        private int state;
        private int rank;
        private int uvrank;
        private int luxury;
        private int havenewenergy;
        private int orderrank;
    }
}
