package com.autohome.applet.model.dto;


import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

public class FaultLight {

    private int id;

    private String name;

    private String img;

    private String type;

    private String keyword;

    private String illustrate;

    private int common;

    private int videoid;

    private String videosource;

    public FaultLight(int id, String name, String img, String type, String keyword, String illustrate, int common, int videoid, String videosource) {
        this.id = id;
        this.name = name;
        this.img = img;
        this.type = type;
        this.keyword = keyword;
        this.illustrate = illustrate;
        this.common = common;
        this.videoid = videoid;
        this.videosource = videosource;
    }

    public FaultLight() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getIllustrate() {
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public int getCommon() {
        return common;
    }

    public void setCommon(int common) {
        this.common = common;
    }

    public int getVideoid() {
        return videoid;
    }

    public void setVideoid(int videoid) {
        this.videoid = videoid;
    }

    public String getVideosource() {
        return videosource;
    }

    public void setVideosource(String videosource) {
        this.videosource = videosource;
    }
}
