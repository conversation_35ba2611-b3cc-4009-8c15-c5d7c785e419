package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Comparator;
import java.util.List;

/**
 * Created by ha<PERSON><PERSON>feng on 2019/4/19.
 */
public class BusinessInfo {

    @JsonProperty("chatId")
    private int chatid;

    @JsonProperty("targetId")
    private int targetid;

    @JsonProperty("targetType")
    private int targettype;

    @JsonProperty("memberCount")
    private int membercount;

    private String slogan;

    private String title;

    @JsonProperty("coverImg")
    private String coverimg;

    private String backgroundImg;

    private Xiaomi xiaomi;

    private String label;

    public String getBackgroundImg() {
        if (StringUtil.isNotNullAndWhiteSpace(backgroundImg)) {
            return backgroundImg.replace("http://", "https://");
        }
        return backgroundImg;
    }

    public void setBackgroundImg(String backgroundImg) {
        this.backgroundImg = backgroundImg;
    }

    @JsonProperty("historySpeakList")
    private List<Historyspeaklist> historyspeaklist;

    private int speakcount;
//    private Historyspeaklist lasthistoryspeak;

//    public int getSpeakcount() {
//        return speakcount;
//    }
//
//    public void setSpeakcount(int speakcount) {
//        this.speakcount = speakcount;
//    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }


    public void setChatid(int chatid) {
        this.chatid = chatid;
    }

    public int getChatid() {
        return chatid;
    }

    public void setTargetid(int targetid) {
        this.targetid = targetid;
    }

    public int getTargetid() {
        return targetid;
    }

    public void setTargettype(int targettype) {
        this.targettype = targettype;
    }

    public int getTargettype() {
        return targettype;
    }

    public void setMembercount(int membercount) {
        this.membercount = membercount;
    }

    public int getMembercount() {
        return membercount;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setCoverimg(String coverimg) {
        this.coverimg = coverimg;
    }

    public String getCoverimg() {
        return coverimg;
    }

    public void setXiaomi(Xiaomi xiaomi) {
        this.xiaomi = xiaomi;
    }

    public Xiaomi getXiaomi() {
        return xiaomi;
    }

    public void setHistoryspeaklist(List<Historyspeaklist> historyspeaklist) {
        this.historyspeaklist = historyspeaklist;
    }

    public List<Historyspeaklist> getHistoryspeaklist() {
        if (historyspeaklist != null) {
            historyspeaklist.sort(new Comparator<Historyspeaklist>() {
                @Override
                public int compare(Historyspeaklist o1, Historyspeaklist o2) {
                    try {
                        if (FormatDate.dateCompare(FormatDate.strToTime(o1.getSpeaktime()), FormatDate.strToTime(o2.getSpeaktime()))) {
                            return 1;
                        } else {
                            return -1;
                        }
                    } catch (Exception e) {
                        return 0;
                    }
                }
            });
            if (historyspeaklist.size() > 6) {
                historyspeaklist = historyspeaklist.subList(0, 6);
            }
        }
        return historyspeaklist;
    }


    public static class Xiaomi {

        @JsonProperty("memberName")
        private String membername;

        @JsonProperty("headImage")
        private String headimage;

        @JsonProperty("speakContent")
        private String speakcontent;

        public void setMembername(String membername) {
            this.membername = membername;
        }

        public String getMembername() {
            return membername;
        }

        public void setHeadimage(String headimage) {
            this.headimage = headimage;
        }

        public String getHeadimage() {
            if (StringUtil.isNotNullAndWhiteSpace(headimage)) {
                return headimage.replace("http://", "http://");
            }
            return headimage;
        }

        public void setSpeakcontent(String speakcontent) {
            this.speakcontent = speakcontent;
        }

        public String getSpeakcontent() {
            return speakcontent;
        }

    }

    public static class Historyspeaklist {

        @JsonProperty("memberId")
        private int memberid;

        private int sex;

        @JsonProperty("memberName")
        private String membername;

        @JsonProperty("headImage")
        private String headimage;

        @JsonProperty("speakContent")
        private String speakcontent;

        @JsonProperty("speakTime")
        private String speaktime;

        public void setMemberid(int memberid) {
            this.memberid = memberid;
        }

        public int getMemberid() {
            return memberid;
        }

        public void setSex(int sex) {
            this.sex = sex;
        }

        public int getSex() {
            return sex;
        }

        public void setMembername(String membername) {
            this.membername = membername;
        }

        public String getMembername() {
            return membername;
        }

        public void setHeadimage(String headimage) {
            this.headimage = headimage;
        }

        public String getHeadimage() {
            if (StringUtil.isNotNullAndWhiteSpace(headimage)) {
                return headimage.replace("http://", "http://");
            }
            return headimage;
        }

        public void setSpeakcontent(String speakcontent) {
            this.speakcontent = speakcontent;
        }

        public String getSpeakcontent() {
            return speakcontent;
        }

        public void setSpeaktime(String speaktime) {
            this.speaktime = speaktime;
        }

        public String getSpeaktime() {
            return speaktime;
        }

    }
}
