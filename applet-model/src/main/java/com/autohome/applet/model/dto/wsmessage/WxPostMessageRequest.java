package com.autohome.applet.model.dto.wsmessage;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class WxPostMessageRequest {

    @JsonAlias({"userid"})
    private Integer userId;
    @NotBlank(message = "缺少openid")
    @JsonAlias({"openid"})
    private String openId;
    @Min(value = 1,message = "请输入正确的订阅类型")
    @JsonAlias({"biztype"})
    private Integer bizType;
    @Min(value = 1,message = "请输入正确的车系id")
    @JsonAlias({"seriesid"})
    private Integer seriesId;
    @NotBlank(message = "缺少车系名称")
    @JsonAlias({"seriesname"})
    private String seriesName;
    @Min(value = 0,message = "请输入正确的渠道")
    @JsonAlias({"sourceid"})
    private Integer sourceId;
    @Min(value = 1,message = "请输入正确的平台类型")
    @JsonAlias({"pm"})
    private Integer pm;
    @JsonAlias({"specid"})
    private Integer specId;
    @JsonAlias({"cityid"})
    private Integer cityId;
}