package com.autohome.applet.model.dto.user;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * for https://user.api.autohome.com.cn/api/go_userInfo/getuserinfolist
 * <AUTHOR>
 */
public class UserInfo {

    @JsonProperty("headimage")
    private String headImage;

    @JsonProperty("sex")
    private int sex;

    @JsonProperty("newnickname")
    private String newNickname;

    @JsonProperty("userid")
    private int userId;

    public String getHeadImage() {
        return headImage;
    }

    public void setHeadImage(String headImage) {
        this.headImage = headImage;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getNewNickname() {
        return newNickname;
    }

    public void setNewNickname(String newNickname) {
        this.newNickname = newNickname;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }
}
