package com.autohome.applet.model.dto.weather;

public class Day {

    private String date; //"date": "2023-01-05",
    private int week;        //"week": "4",
    private String dayweather;//       "dayweather": "多云",
    private String dayweathericon; //  "dayweathericon": "",
    private String nightweather; // "nightweather": "多云",
    private String nightweathericon; //  "nightweathericon": "",
    private int daytemp;//  "daytemp": "5",
    private int nighttemp;//  "nighttemp": "-6",
    private String daywind; //   "daywind": "西南",
    private String nightwind; //    "nightwind": "西南",
    private String daypower; //     "daypower": "≤3",
    private String nightpower; //       "nightpower": "≤3"

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getWeek() {
        return week;
    }

    public void setWeek(int week) {
        this.week = week;
    }

    public String getDayweather() {
        return dayweather;
    }

    public void setDayweather(String dayweather) {
        this.dayweather = dayweather;
    }

    public String getDayweathericon() {
        return dayweathericon;
    }

    public void setDayweathericon(String dayweathericon) {
        this.dayweathericon = dayweathericon;
    }

    public String getNightweather() {
        return nightweather;
    }

    public void setNightweather(String nightweather) {
        this.nightweather = nightweather;
    }

    public String getNightweathericon() {
        return nightweathericon;
    }

    public void setNightweathericon(String nightweathericon) {
        this.nightweathericon = nightweathericon;
    }

    public int getDaytemp() {
        return daytemp;
    }

    public void setDaytemp(int daytemp) {
        this.daytemp = daytemp;
    }

    public int getNighttemp() {
        return nighttemp;
    }

    public void setNighttemp(int nighttemp) {
        this.nighttemp = nighttemp;
    }

    public String getDaywind() {
        return daywind;
    }

    public void setDaywind(String daywind) {
        this.daywind = daywind;
    }

    public String getNightwind() {
        return nightwind;
    }

    public void setNightwind(String nightwind) {
        this.nightwind = nightwind;
    }

    public String getDaypower() {
        return daypower;
    }

    public void setDaypower(String daypower) {
        this.daypower = daypower;
    }

    public String getNightpower() {
        return nightpower;
    }

    public void setNightpower(String nightpower) {
        this.nightpower = nightpower;
    }
}
