package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chejiahao;

import java.util.List;

/**
 * Created by ha<PERSON><PERSON><PERSON> on 2019/3/29.
 */
public class PageListByTagList {

    private int seriesid;
    private PageListByTag pageListByTag;

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public PageListByTag getPageListByTag() {
        return pageListByTag;
    }

    public void setPageListByTag(PageListByTag pageListByTag) {
        this.pageListByTag = pageListByTag;
    }

    public static class PageListByTag {
        private int totalcount;
        private List<Items> items;

        public void setTotalcount(int totalcount) {
            this.totalcount = totalcount;
        }

        public int getTotalcount() {
            return totalcount;
        }

        public void setItems(List<Items> items) {
            this.items = items;
        }

        public List<Items> getItems() {
            return items;
        }
    }

    public static class Items {

        //        private boolean isyouxuan;
//        private String keywords;
//        private int ident;
//        private boolean recommendshowbigimg;
//        private boolean isautowrite;
//        private List<Relationcar> relationcar;
//        private int hailiangstatus;
//        private boolean isconverttext;
//        private int authorid;
//        private int infolevel;
//        private String youxuantime;
//        private boolean iswaitui;
//        private boolean ispublish;
//        private boolean recommedntoxny;
//        private boolean istop;
//        private int pageticks;
//        private int showlevel;
//        private boolean showimagelist;
//        private int auditrecommend;
//        private boolean ishot;
        private String image;
        //        private List<String> images;
//        private int infoid;
//        private String lastupdatetime;
//        private boolean showbigimg;
//        private Author author;
//        private String autohomeua;
//        private boolean hasreview;
//        private String title2;
//        private String title3;
//        private String cityid;
//        private String pushtitle;
//        private String article;
//        private List<Tags> tags;
//        private String statusnote;
//        private String publishtime;
//        private String pushdescription;
//        private boolean isfirst;
//        private int status;
//        private String originaldescription;
//        private int infotype;
//        private int clienttype;
//        private Infostatistics infostatistics;
//        private int sorttype;
//        private String pcurl;
//        private String description;
        private Video video;
        private String title;
        private String pageid;
//        private String murl;
//        private boolean recycled;
//        private String audio;
//        private String live;
//        private String createtime;
//        private String carmonads;
//        private boolean recommendtomoji;
//        private int auditor;
//        private boolean isbusiness;
//        private boolean recommendtoseries;
//        private String waituitime;
//        private boolean isoriginal;
//        private String newkeywords;
//        private String newkeywordids;
//        private String hailiangreason;
//        private String textimage;


        public void setVideo(Video video) {
            this.video = video;
        }

        public Video getVideo() {
            return video;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitle() {
            return title;
        }

        public void setPageid(String pageid) {
            this.pageid = pageid;
        }

        public String getPageid() {
            return pageid;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }
    }

    public static class Video {

        private int duration;
        private int infoid;
        private int smallvideoid;
        private boolean isvr;
        private String videoid;
        private int status;

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public int getDuration() {
            return duration;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setSmallvideoid(int smallvideoid) {
            this.smallvideoid = smallvideoid;
        }

        public int getSmallvideoid() {
            return smallvideoid;
        }

        public void setIsvr(boolean isvr) {
            this.isvr = isvr;
        }

        public boolean getIsvr() {
            return isvr;
        }

        public void setVideoid(String videoid) {
            this.videoid = videoid;
        }

        public String getVideoid() {
            return videoid;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }

    }
//    public static class Infostatistics {
//
//        private int vv;
//        private int uv;
//        private int infoid;
//        private int pv;
//        private int epv;
//        private int praisecount;
//        private int imageclickcount;
//        private String pageid;
//        private int playduration;
//        private int detailpv;
//        private int sharecount;
//        private int playcount;
//        private int favoritecount;
//        private int replycount;
//        public void setVv(int vv) {
//            this.vv = vv;
//        }
//        public int getVv() {
//            return vv;
//        }
//
//        public void setUv(int uv) {
//            this.uv = uv;
//        }
//        public int getUv() {
//            return uv;
//        }
//
//        public void setInfoid(int infoid) {
//            this.infoid = infoid;
//        }
//        public int getInfoid() {
//            return infoid;
//        }
//
//        public void setPv(int pv) {
//            this.pv = pv;
//        }
//        public int getPv() {
//            return pv;
//        }
//
//        public void setEpv(int epv) {
//            this.epv = epv;
//        }
//        public int getEpv() {
//            return epv;
//        }
//
//        public void setPraisecount(int praisecount) {
//            this.praisecount = praisecount;
//        }
//        public int getPraisecount() {
//            return praisecount;
//        }
//
//        public void setImageclickcount(int imageclickcount) {
//            this.imageclickcount = imageclickcount;
//        }
//        public int getImageclickcount() {
//            return imageclickcount;
//        }
//
//        public void setPageid(String pageid) {
//            this.pageid = pageid;
//        }
//        public String getPageid() {
//            return pageid;
//        }
//
//        public void setPlayduration(int playduration) {
//            this.playduration = playduration;
//        }
//        public int getPlayduration() {
//            return playduration;
//        }
//
//        public void setDetailpv(int detailpv) {
//            this.detailpv = detailpv;
//        }
//        public int getDetailpv() {
//            return detailpv;
//        }
//
//        public void setSharecount(int sharecount) {
//            this.sharecount = sharecount;
//        }
//        public int getSharecount() {
//            return sharecount;
//        }
//
//        public void setPlaycount(int playcount) {
//            this.playcount = playcount;
//        }
//        public int getPlaycount() {
//            return playcount;
//        }
//
//        public void setFavoritecount(int favoritecount) {
//            this.favoritecount = favoritecount;
//        }
//        public int getFavoritecount() {
//            return favoritecount;
//        }
//
//        public void setReplycount(int replycount) {
//            this.replycount = replycount;
//        }
//        public int getReplycount() {
//            return replycount;
//        }
//
//    }
//    public static class Tags {
//
//        private boolean isshow;
//        private int navorder;
//        private String tagname;
//        private boolean isnav;
//        private int tagid;
//        private int order;
//        public void setIsshow(boolean isshow) {
//            this.isshow = isshow;
//        }
//        public boolean getIsshow() {
//            return isshow;
//        }
//
//        public void setNavorder(int navorder) {
//            this.navorder = navorder;
//        }
//        public int getNavorder() {
//            return navorder;
//        }
//
//        public void setTagname(String tagname) {
//            this.tagname = tagname;
//        }
//        public String getTagname() {
//            return tagname;
//        }
//
//        public void setIsnav(boolean isnav) {
//            this.isnav = isnav;
//        }
//        public boolean getIsnav() {
//            return isnav;
//        }
//
//        public void setTagid(int tagid) {
//            this.tagid = tagid;
//        }
//        public int getTagid() {
//            return tagid;
//        }
//
//        public void setOrder(int order) {
//            this.order = order;
//        }
//        public int getOrder() {
//            return order;
//        }
//
//    }
//    public static class Author {
//
//        private String gca;
//        private String vtype;
//        private boolean isautowrite;
//        private boolean isjointask;
//        private boolean isshielded;
//        private int recommendtype;
//        private int userid;
//        private String jointaskdate;
//        private int localprovinceid;
//        private int infolevel;
//        private boolean isjoinadplan;
//        private String vtypedes;
//        private boolean islocaltion;
//        private String wechatid;
//        private String joinadplandate;
//        private String vremark;
//        private boolean isaloneplay;
//        private boolean isgoodauthor;
//        private String localcityname;
//        private String localprovincename;
//        private int inforecommendlevel;
//        private String coverimage;
//        private String usertitle;
//        private int status;
//        private int creditscore;
//        private String pageid;
//        private int activedegree;
//        private boolean enabled;
//        private String vdescription;
//        private String inforecommendleveltime;
//        private String subscribetime;
//        private String nickname;
//        private boolean allowbigfile;
//        private int localcityid;
//        private String userextend2;
//        private String businesstag;
//        private String introduction;
//        private String email;
//        private String createtime;
//        private String relationname;
//        private String mobile;
//        private String auditor;
//        private boolean iswhitelist;
//        private String avatar;
//        private String vcrid;
//        private int userlevel;
//        private String recommendtime;
//        private String jointime;
//        private String statusdes;
//        private String statistics;
//        public void setGca(String gca) {
//            this.gca = gca;
//        }
//        public String getGca() {
//            return gca;
//        }
//
//        public void setVtype(String vtype) {
//            this.vtype = vtype;
//        }
//        public String getVtype() {
//            return vtype;
//        }
//
//        public void setIsautowrite(boolean isautowrite) {
//            this.isautowrite = isautowrite;
//        }
//        public boolean getIsautowrite() {
//            return isautowrite;
//        }
//
//        public void setIsjointask(boolean isjointask) {
//            this.isjointask = isjointask;
//        }
//        public boolean getIsjointask() {
//            return isjointask;
//        }
//
//        public void setIsshielded(boolean isshielded) {
//            this.isshielded = isshielded;
//        }
//        public boolean getIsshielded() {
//            return isshielded;
//        }
//
//        public void setRecommendtype(int recommendtype) {
//            this.recommendtype = recommendtype;
//        }
//        public int getRecommendtype() {
//            return recommendtype;
//        }
//
//        public void setUserid(int userid) {
//            this.userid = userid;
//        }
//        public int getUserid() {
//            return userid;
//        }
//
//        public void setJointaskdate(String jointaskdate) {
//            this.jointaskdate = jointaskdate;
//        }
//        public String getJointaskdate() {
//            return jointaskdate;
//        }
//
//        public void setLocalprovinceid(int localprovinceid) {
//            this.localprovinceid = localprovinceid;
//        }
//        public int getLocalprovinceid() {
//            return localprovinceid;
//        }
//
//        public void setInfolevel(int infolevel) {
//            this.infolevel = infolevel;
//        }
//        public int getInfolevel() {
//            return infolevel;
//        }
//
//        public void setIsjoinadplan(boolean isjoinadplan) {
//            this.isjoinadplan = isjoinadplan;
//        }
//        public boolean getIsjoinadplan() {
//            return isjoinadplan;
//        }
//
//        public void setVtypedes(String vtypedes) {
//            this.vtypedes = vtypedes;
//        }
//        public String getVtypedes() {
//            return vtypedes;
//        }
//
//        public void setIslocaltion(boolean islocaltion) {
//            this.islocaltion = islocaltion;
//        }
//        public boolean getIslocaltion() {
//            return islocaltion;
//        }
//
//        public void setWechatid(String wechatid) {
//            this.wechatid = wechatid;
//        }
//        public String getWechatid() {
//            return wechatid;
//        }
//
//        public void setJoinadplandate(String joinadplandate) {
//            this.joinadplandate = joinadplandate;
//        }
//        public String getJoinadplandate() {
//            return joinadplandate;
//        }
//
//        public void setVremark(String vremark) {
//            this.vremark = vremark;
//        }
//        public String getVremark() {
//            return vremark;
//        }
//
//        public void setIsaloneplay(boolean isaloneplay) {
//            this.isaloneplay = isaloneplay;
//        }
//        public boolean getIsaloneplay() {
//            return isaloneplay;
//        }
//
//        public void setIsgoodauthor(boolean isgoodauthor) {
//            this.isgoodauthor = isgoodauthor;
//        }
//        public boolean getIsgoodauthor() {
//            return isgoodauthor;
//        }
//
//        public void setLocalcityname(String localcityname) {
//            this.localcityname = localcityname;
//        }
//        public String getLocalcityname() {
//            return localcityname;
//        }
//
//        public void setLocalprovincename(String localprovincename) {
//            this.localprovincename = localprovincename;
//        }
//        public String getLocalprovincename() {
//            return localprovincename;
//        }
//
//        public void setInforecommendlevel(int inforecommendlevel) {
//            this.inforecommendlevel = inforecommendlevel;
//        }
//        public int getInforecommendlevel() {
//            return inforecommendlevel;
//        }
//
//        public void setCoverimage(String coverimage) {
//            this.coverimage = coverimage;
//        }
//        public String getCoverimage() {
//            return coverimage;
//        }
//
//        public void setUsertitle(String usertitle) {
//            this.usertitle = usertitle;
//        }
//        public String getUsertitle() {
//            return usertitle;
//        }
//
//        public void setStatus(int status) {
//            this.status = status;
//        }
//        public int getStatus() {
//            return status;
//        }
//
//        public void setCreditscore(int creditscore) {
//            this.creditscore = creditscore;
//        }
//        public int getCreditscore() {
//            return creditscore;
//        }
//
//        public void setPageid(String pageid) {
//            this.pageid = pageid;
//        }
//        public String getPageid() {
//            return pageid;
//        }
//
//        public void setActivedegree(int activedegree) {
//            this.activedegree = activedegree;
//        }
//        public int getActivedegree() {
//            return activedegree;
//        }
//
//        public void setEnabled(boolean enabled) {
//            this.enabled = enabled;
//        }
//        public boolean getEnabled() {
//            return enabled;
//        }
//
//        public void setVdescription(String vdescription) {
//            this.vdescription = vdescription;
//        }
//        public String getVdescription() {
//            return vdescription;
//        }
//
//        public void setInforecommendleveltime(String inforecommendleveltime) {
//            this.inforecommendleveltime = inforecommendleveltime;
//        }
//        public String getInforecommendleveltime() {
//            return inforecommendleveltime;
//        }
//
//        public void setSubscribetime(String subscribetime) {
//            this.subscribetime = subscribetime;
//        }
//        public String getSubscribetime() {
//            return subscribetime;
//        }
//
//        public void setNickname(String nickname) {
//            this.nickname = nickname;
//        }
//        public String getNickname() {
//            return nickname;
//        }
//
//        public void setAllowbigfile(boolean allowbigfile) {
//            this.allowbigfile = allowbigfile;
//        }
//        public boolean getAllowbigfile() {
//            return allowbigfile;
//        }
//
//        public void setLocalcityid(int localcityid) {
//            this.localcityid = localcityid;
//        }
//        public int getLocalcityid() {
//            return localcityid;
//        }
//
//        public void setUserextend2(String userextend2) {
//            this.userextend2 = userextend2;
//        }
//        public String getUserextend2() {
//            return userextend2;
//        }
//
//        public void setBusinesstag(String businesstag) {
//            this.businesstag = businesstag;
//        }
//        public String getBusinesstag() {
//            return businesstag;
//        }
//
//        public void setIntroduction(String introduction) {
//            this.introduction = introduction;
//        }
//        public String getIntroduction() {
//            return introduction;
//        }
//
//        public void setEmail(String email) {
//            this.email = email;
//        }
//        public String getEmail() {
//            return email;
//        }
//
//        public void setCreatetime(String createtime) {
//            this.createtime = createtime;
//        }
//        public String getCreatetime() {
//            return createtime;
//        }
//
//        public void setRelationname(String relationname) {
//            this.relationname = relationname;
//        }
//        public String getRelationname() {
//            return relationname;
//        }
//
//        public void setMobile(String mobile) {
//            this.mobile = mobile;
//        }
//        public String getMobile() {
//            return mobile;
//        }
//
//        public void setAuditor(String auditor) {
//            this.auditor = auditor;
//        }
//        public String getAuditor() {
//            return auditor;
//        }
//
//        public void setIswhitelist(boolean iswhitelist) {
//            this.iswhitelist = iswhitelist;
//        }
//        public boolean getIswhitelist() {
//            return iswhitelist;
//        }
//
//        public void setAvatar(String avatar) {
//            this.avatar = avatar;
//        }
//        public String getAvatar() {
//            return avatar;
//        }
//
//        public void setVcrid(String vcrid) {
//            this.vcrid = vcrid;
//        }
//        public String getVcrid() {
//            return vcrid;
//        }
//
//        public void setUserlevel(int userlevel) {
//            this.userlevel = userlevel;
//        }
//        public int getUserlevel() {
//            return userlevel;
//        }
//
//        public void setRecommendtime(String recommendtime) {
//            this.recommendtime = recommendtime;
//        }
//        public String getRecommendtime() {
//            return recommendtime;
//        }
//
//        public void setJointime(String jointime) {
//            this.jointime = jointime;
//        }
//        public String getJointime() {
//            return jointime;
//        }
//
//        public void setStatusdes(String statusdes) {
//            this.statusdes = statusdes;
//        }
//        public String getStatusdes() {
//            return statusdes;
//        }
//
//        public void setStatistics(String statistics) {
//            this.statistics = statistics;
//        }
//        public String getStatistics() {
//            return statistics;
//        }
//
//    }
//    public static class Relationcar {
//
//        private int specid;
//        private String createtime;
//        private int infoid;
//        private String lastupdatetime;
//        private int factoryid;
//        private int appearcount;
//        private String specname;
//        private String yearname;
//        private String brandname;
//        private int seriesid;
//        private int yearid;
//        private boolean isfrommachine;
//        private String seriesname;
//        private int brandid;
//        private int infocarrelationid;
//        private String factoryname;
//        private int status;
//        public void setSpecid(int specid) {
//            this.specid = specid;
//        }
//        public int getSpecid() {
//            return specid;
//        }
//
//        public void setCreatetime(String createtime) {
//            this.createtime = createtime;
//        }
//        public String getCreatetime() {
//            return createtime;
//        }
//
//        public void setInfoid(int infoid) {
//            this.infoid = infoid;
//        }
//        public int getInfoid() {
//            return infoid;
//        }
//
//        public void setLastupdatetime(String lastupdatetime) {
//            this.lastupdatetime = lastupdatetime;
//        }
//        public String getLastupdatetime() {
//            return lastupdatetime;
//        }
//
//        public void setFactoryid(int factoryid) {
//            this.factoryid = factoryid;
//        }
//        public int getFactoryid() {
//            return factoryid;
//        }
//
//        public void setAppearcount(int appearcount) {
//            this.appearcount = appearcount;
//        }
//        public int getAppearcount() {
//            return appearcount;
//        }
//
//        public void setSpecname(String specname) {
//            this.specname = specname;
//        }
//        public String getSpecname() {
//            return specname;
//        }
//
//        public void setYearname(String yearname) {
//            this.yearname = yearname;
//        }
//        public String getYearname() {
//            return yearname;
//        }
//
//        public void setBrandname(String brandname) {
//            this.brandname = brandname;
//        }
//        public String getBrandname() {
//            return brandname;
//        }
//
//        public void setSeriesid(int seriesid) {
//            this.seriesid = seriesid;
//        }
//        public int getSeriesid() {
//            return seriesid;
//        }
//
//        public void setYearid(int yearid) {
//            this.yearid = yearid;
//        }
//        public int getYearid() {
//            return yearid;
//        }
//
//        public void setIsfrommachine(boolean isfrommachine) {
//            this.isfrommachine = isfrommachine;
//        }
//        public boolean getIsfrommachine() {
//            return isfrommachine;
//        }
//
//        public void setSeriesname(String seriesname) {
//            this.seriesname = seriesname;
//        }
//        public String getSeriesname() {
//            return seriesname;
//        }
//
//        public void setBrandid(int brandid) {
//            this.brandid = brandid;
//        }
//        public int getBrandid() {
//            return brandid;
//        }
//
//        public void setInfocarrelationid(int infocarrelationid) {
//            this.infocarrelationid = infocarrelationid;
//        }
//        public int getInfocarrelationid() {
//            return infocarrelationid;
//        }
//
//        public void setFactoryname(String factoryname) {
//            this.factoryname = factoryname;
//        }
//        public String getFactoryname() {
//            return factoryname;
//        }
//
//        public void setStatus(int status) {
//            this.status = status;
//        }
//        public int getStatus() {
//            return status;
//        }
//
//    }
}

