package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * crated by hanshanfeng 2021/8/13 16:43
 */
public class SeriesClhSaleRankParam {
    /**
     * maxPrice : 90000000
     * minPrice : 0
     * manutypes : ["合资"]
     * month : 2021-06
     * levels : [3]
     * isNewenergy :
     * flag : true
     */

    private String maxPrice;
    private String minPrice;
    private String beginMonth;
    private String endMonth;
    private String isNewenergy;
    private String flag;
    private List<String> manutypes;
    private List<String> levels;

    public List<String> getLevels() {
        return levels;
    }

    public void setLevels(List<String> levels) {
        this.levels = levels;
    }

    public String getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(String maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(String minPrice) {
        this.minPrice = minPrice;
    }

    public String getBeginMonth() {
        return beginMonth;
    }

    public void setBeginMonth(String beginMonth) {
        this.beginMonth = beginMonth;
    }

    public String getEndMonth() {
        return endMonth;
    }

    public void setEndMonth(String endMonth) {
        this.endMonth = endMonth;
    }

    public String getIsNewenergy() {
        return isNewenergy;
    }

    public void setIsNewenergy(String isNewenergy) {
        this.isNewenergy = isNewenergy;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public List<String> getManutypes() {
        return manutypes;
    }

    public void setManutypes(List<String> manutypes) {
        this.manutypes = manutypes;
    }

}
