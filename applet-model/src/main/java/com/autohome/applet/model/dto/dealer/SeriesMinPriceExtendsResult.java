package com.autohome.applet.model.dto.dealer;

public class SeriesMinPriceExtendsResult {
    private int dealerId;
    private int isLowCity;
    private int seriesId;
    private int specId;
    private int cityId;
    /**
     * 最低价 单位：元
     * */
    private int newsPrice;
    private int newsId;
    private String endTime;
    private int minOriginalPrice;
    private int priceOffRatio;
    private int saleType;
    private int openType;

    public int getDealerId() {
        return dealerId;
    }

    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public int getIsLowCity() {
        return isLowCity;
    }

    public void setIsLowCity(int isLowCity) {
        this.isLowCity = isLowCity;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public int getNewsPrice() {
        return newsPrice;
    }

    public void setNewsPrice(int newsPrice) {
        this.newsPrice = newsPrice;
    }

    public int getNewsId() {
        return newsId;
    }

    public void setNewsId(int newsId) {
        this.newsId = newsId;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(int minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public int getPriceOffRatio() {
        return priceOffRatio;
    }

    public void setPriceOffRatio(int priceOffRatio) {
        this.priceOffRatio = priceOffRatio;
    }

    public int getSaleType() {
        return saleType;
    }

    public void setSaleType(int saleType) {
        this.saleType = saleType;
    }

    public int getOpenType() {
        return openType;
    }

    public void setOpenType(int openType) {
        this.openType = openType;
    }
}
