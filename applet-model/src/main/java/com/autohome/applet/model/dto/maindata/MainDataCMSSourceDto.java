package com.autohome.applet.model.dto.maindata;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class MainDataCMSSourceDto {

    @JsonProperty(value = "biz_id")
    private Integer bizId;

    @JsonProperty(value = "parent_biz_id")
    private Integer parentBizId;

    @JsonProperty(value = "biz_update_time")
    private String bizUpdateTime;

    @JsonProperty(value = "pc_url")
    private String pcUrl;
    @JsonProperty(value = "app_url")
    private String appUrl;

    @JsonProperty(value = "m_url")
    private String mUrl;

    @JsonProperty(value = "reply_count")
    private String replyCount;

    private Integer vv;

    private Integer pv;

    @JsonProperty(value = "author_img")
    private String authorImg;

    @JsonProperty(value = "author_name")
    private String authorName;

    private String summary;

    @JsonProperty(value = "chejiahao_Status")
    private Integer chejiahaoStatus;

    @JsonProperty(value = "main_data_type")
    private String mainDataType;

    private String title;

    @JsonProperty(value = "is_delete")
    private Integer isDelete;

    @JsonProperty(value = "pool_biz_types")
    private List<Integer> poolBizTypes;

    @JsonProperty(value = "img_url")
    private String imgUrl;

    @JsonProperty(value = "author_id")
    private Integer authorId;

    @JsonProperty(value = "publish_time")
    private String publishTime;

    @JsonProperty(value = "chejiahao_IsRecommend")
    private Integer chejiahaoIsRecommend;

    @JsonProperty(value = "series_ids")
    private List<Integer> seriesIds;

    @JsonProperty(value = "is_publish")
    private Integer isPublish;

    @JsonProperty(value = "img_url_16x9")
    private String imgUrl16x9;

    @JsonProperty(value = "img_url_4x3")
    private String imgUrl4x3;

    @JsonProperty(value = "small_title")
    private String smallTitle;

    @JsonProperty(value = "cms_tags")
    private String cmsTags;

    public Integer getBizId() {
        return bizId;
    }

    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }

    public Integer getParentBizId() {
        return parentBizId;
    }

    public void setParentBizId(Integer parentBizId) {
        this.parentBizId = parentBizId;
    }

    public String getBizUpdateTime() {
        return bizUpdateTime;
    }

    public void setBizUpdateTime(String bizUpdateTime) {
        this.bizUpdateTime = bizUpdateTime;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public String getmUrl() {
        return mUrl;
    }

    public void setmUrl(String mUrl) {
        this.mUrl = mUrl;
    }

    public String getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(String replyCount) {
        this.replyCount = replyCount;
    }

    public Integer getVv() {
        return vv;
    }

    public void setVv(Integer vv) {
        this.vv = vv;
    }

    public Integer getPv() {
        return pv == null ? 0 : pv;
    }

    public void setPv(Integer pv) {
        this.pv = pv;
    }

    public String getAuthorImg() {
        return authorImg;
    }

    public void setAuthorImg(String authorImg) {
        this.authorImg = authorImg;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getChejiahaoStatus() {
        return chejiahaoStatus;
    }

    public void setChejiahaoStatus(Integer chejiahaoStatus) {
        this.chejiahaoStatus = chejiahaoStatus;
    }

    public String getMainDataType() {
        return mainDataType;
    }

    public void setMainDataType(String mainDataType) {
        this.mainDataType = mainDataType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public List<Integer> getPoolBizTypes() {
        return poolBizTypes;
    }

    public void setPoolBizTypes(List<Integer> poolBizTypes) {
        this.poolBizTypes = poolBizTypes;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Integer authorId) {
        this.authorId = authorId;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public Integer getChejiahaoIsRecommend() {
        return chejiahaoIsRecommend;
    }

    public void setChejiahaoIsRecommend(Integer chejiahaoIsRecommend) {
        this.chejiahaoIsRecommend = chejiahaoIsRecommend;
    }

    public List<Integer> getSeriesIds() {
        return seriesIds;
    }

    public void setSeriesIds(List<Integer> seriesIds) {
        this.seriesIds = seriesIds;
    }

    public Integer getIsPublish() {
        return isPublish;
    }

    public void setIsPublish(Integer isPublish) {
        this.isPublish = isPublish;
    }

    public String getImgUrl16x9() {
        return imgUrl16x9;
    }

    public void setImgUrl16x9(String imgUrl16x9) {
        this.imgUrl16x9 = imgUrl16x9;
    }

    public String getImgUrl4x3() {
        return imgUrl4x3;
    }

    public void setImgUrl4x3(String imgUrl4x3) {
        this.imgUrl4x3 = imgUrl4x3;
    }

    public String getSmallTitle() {
        return smallTitle;
    }

    public void setSmallTitle(String smallTitle) {
        this.smallTitle = smallTitle;
    }

    public String getCmsTags() {
        return cmsTags;
    }

    public void setCmsTags(String cmsTags) {
        this.cmsTags = cmsTags;
    }

    public String getAppUrl() {
        return appUrl == null ? "" : appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl == null ? "" : appUrl;
    }
}
