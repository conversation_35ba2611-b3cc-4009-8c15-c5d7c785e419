package com.autohome.applet.model.dto.honor;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AnalyzeSeriesCardInfo {

    private String seriesName;

    private int seriesId;

    private String saleStatus;

    private String brandLogo;

    private String brandName;

    private String seriesLevel;

    private String seriesImg;

//    private ExtendInfo extendInfo;

    private DeepLink homePage;

    private List<NameValue> extraContent;

    private List<OperateIcon> operateIconInfo;

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public DeepLink getHomePage() {
        return homePage;
    }

    public void setHomePage(DeepLink homePage) {
        this.homePage = homePage;
    }

    public List<NameValue> getExtraContent() {
        return extraContent;
    }

    public void setExtraContent(List<NameValue> extraContent) {
        this.extraContent = extraContent;
    }

    public List<OperateIcon> getOperateIconInfo() {
        return operateIconInfo;
    }

    public void setOperateIconInfo(List<OperateIcon> operateIconInfo) {
        this.operateIconInfo = operateIconInfo;
    }

    public static class OperateIcon {
        private String buttonText;
        private DeepLink deeplink;

        public OperateIcon() {
        }

        public OperateIcon(String buttonText, DeepLink deeplink) {
            this.buttonText = buttonText;
            this.deeplink = deeplink;
        }

        public String getButtonText() {
            return buttonText;
        }

        public void setButtonText(String buttonText) {
            this.buttonText = buttonText;
        }

        public DeepLink getDeeplink() {
            return deeplink;
        }

        public void setDeeplink(DeepLink deeplink) {
            this.deeplink = deeplink;
        }
    }

    public static class NameValue {

        private String name;
        private String value;

        public NameValue() {
        }

        public NameValue(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static class DeepLink {
        private Web web;
        private Native nativeApp;
        private Quick quickApp;

        public Web getWeb() {
            return web;
        }

        public void setWeb(Web web) {
            this.web = web;
        }

        public Native getNativeApp() {
            return nativeApp;
        }

        public void setNativeApp(Native nativeApp) {
            this.nativeApp = nativeApp;
        }

        public Quick getQuickApp() {
            return quickApp;
        }

        public void setQuickApp(Quick quickApp) {
            this.quickApp = quickApp;
        }
    }

    public static class Web {
        private String appName;
        private String url;

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public static class Native {
        private String appName;
        private String url;
        private String pkgName;
        private int minVersion;
        private int minAndroidApiLevel;

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getPkgName() {
            return pkgName;
        }

        public void setPkgName(String pkgName) {
            this.pkgName = pkgName;
        }

        public int getMinVersion() {
            return minVersion;
        }

        public void setMinVersion(int minVersion) {
            this.minVersion = minVersion;
        }

        public int getMinAndroidApiLevel() {
            return minAndroidApiLevel;
        }

        public void setMinAndroidApiLevel(int minAndroidApiLevel) {
            this.minAndroidApiLevel = minAndroidApiLevel;
        }
    }

    public static class Quick {
        private String appName;
        private String url;
        private String pkgName;
        private int minPlatformVersion;
        private int minVersion;

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getPkgName() {
            return pkgName;
        }

        public void setPkgName(String pkgName) {
            this.pkgName = pkgName;
        }

        public int getMinPlatformVersion() {
            return minPlatformVersion;
        }

        public void setMinPlatformVersion(int minPlatformVersion) {
            this.minPlatformVersion = minPlatformVersion;
        }

        public int getMinVersion() {
            return minVersion;
        }

        public void setMinVersion(int minVersion) {
            this.minVersion = minVersion;
        }
    }

    public static class ExtendInfo {

        @JsonProperty("card_deeplink")
        private String cardDeeplink;

        @JsonProperty("dynamic_fields")
        private List<AtomItem> dynamicFields;

        @JsonProperty("recommend_service_list")
        private List<ServiceLink> recommendServiceList;

        public String getCardDeeplink() {
            return cardDeeplink;
        }

        public void setCardDeeplink(String cardDeeplink) {
            this.cardDeeplink = cardDeeplink;
        }

        public List<AtomItem> getDynamicFields() {
            return dynamicFields;
        }

        public void setDynamicFields(List<AtomItem> dynamicFields) {
            this.dynamicFields = dynamicFields;
        }

        public List<ServiceLink> getRecommendServiceList() {
            return recommendServiceList;
        }

        public void setRecommendServiceList(List<ServiceLink> recommendServiceList) {
            this.recommendServiceList = recommendServiceList;
        }
    }

    public static class AtomItem {
        private String key;
        private String value;

        public AtomItem() {
        }

        public AtomItem(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static class ServiceLink {

        @JsonProperty("service_name")
        private String serviceName;

        @JsonProperty("deeplink")
        private String deeplink;

        public ServiceLink() {
        }

        public ServiceLink(String serviceName, String deeplink) {
            this.serviceName = serviceName;
            this.deeplink = deeplink;
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getDeeplink() {
            return deeplink;
        }

        public void setDeeplink(String deeplink) {
            this.deeplink = deeplink;
        }
    }

//    private String line1Name;
//
//    private String line1Value;
//
//    private String line2Name;
//
//    private String line2Value;
//
//    private String line3Name;
//
//    private String line3Value;
//
//    private String cardDeepLink;
//
//    private String imgDeepLink;
//
//    private String askDeepLink;

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSaleStatus() {
        return saleStatus;
    }

    public void setSaleStatus(String saleStatus) {
        this.saleStatus = saleStatus;
    }

    public String getBrandLogo() {
        return brandLogo;
    }

    public void setBrandLogo(String brandLogo) {
        this.brandLogo = brandLogo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSeriesLevel() {
        return seriesLevel;
    }

    public void setSeriesLevel(String seriesLevel) {
        this.seriesLevel = seriesLevel;
    }

    public String getSeriesImg() {
        return seriesImg;
    }

    public void setSeriesImg(String seriesImg) {
        this.seriesImg = seriesImg;
    }

//    public ExtendInfo getExtendInfo() {
//        return extendInfo;
//    }
//
//    public void setExtendInfo(ExtendInfo extendInfo) {
//        this.extendInfo = extendInfo;
//    }
    //
//    public String getLine1Name() {
//        return line1Name;
//    }
//
//    public void setLine1Name(String line1Name) {
//        this.line1Name = line1Name;
//    }
//
//    public String getLine1Value() {
//        return line1Value;
//    }
//
//    public void setLine1Value(String line1Value) {
//        this.line1Value = line1Value;
//    }
//
//    public String getLine2Name() {
//        return line2Name;
//    }
//
//    public void setLine2Name(String line2Name) {
//        this.line2Name = line2Name;
//    }
//
//    public String getLine2Value() {
//        return line2Value;
//    }
//
//    public void setLine2Value(String line2Value) {
//        this.line2Value = line2Value;
//    }
//
//    public String getLine3Name() {
//        return line3Name;
//    }
//
//    public void setLine3Name(String line3Name) {
//        this.line3Name = line3Name;
//    }
//
//    public String getLine3Value() {
//        return line3Value;
//    }
//
//    public void setLine3Value(String line3Value) {
//        this.line3Value = line3Value;
//    }
//
//    public String getCardDeepLink() {
//        return cardDeepLink;
//    }
//
//    public void setCardDeepLink(String cardDeepLink) {
//        this.cardDeepLink = cardDeepLink;
//    }
//
//    public String getImgDeepLink() {
//        return imgDeepLink;
//    }
//
//    public void setImgDeepLink(String imgDeepLink) {
//        this.imgDeepLink = imgDeepLink;
//    }
//
//    public String getAskDeepLink() {
//        return askDeepLink;
//    }
//
//    public void setAskDeepLink(String askDeepLink) {
//        this.askDeepLink = askDeepLink;
//    }
}
