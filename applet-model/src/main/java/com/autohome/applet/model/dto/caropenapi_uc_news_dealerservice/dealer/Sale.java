package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;

import java.math.BigDecimal;

public class Sale {

    private int uid;

    private int userid;

    private String nickname;

    private String nicknameletter;

    private int nicknamelettercode;

    private String photoimgurl;

    private String brandname;

    private int dealerid;

    private String dealername;

    private String fansnum;

    private String thumbupnum;


    private int islike;

    private int salesid;


    private String usermainurl;

    private int certificationtype;

    private String imid;

    private int usertype;

    private int ishavehomepage;

    private int userreplyrate;

    private String userreplydesc;

    private int brandid;

    private int saleslevel;

    private int cityid;

    private String cityname;

    private int serviceusertotalcount;

    private int serviceusercount;

    private String usersalescore;
    //星级
    private int usersalestar;

    private String distancedesc;

    public int getUsersalestar() {
        try {
            if (StringUtil.isBlank(this.usersalescore) || this.usersalescore.equals("0.0") || this.usersalescore.equals("0")) {
                return 3;
            }
            BigDecimal b = new BigDecimal(this.usersalescore);
            int salescore = b.setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
            return salescore;
        } catch (Exception e) {
            //默认3星
            return 3;
        }
    }

    public void setUsersalestar(int usersalestar) {
        this.usersalestar = usersalestar;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getUid() {
        return uid;
    }

    public void setUserid(int userid) {
        this.userid = userid;
    }

    public int getUserid() {
        return userid;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNicknameletter(String nicknameletter) {
        this.nicknameletter = nicknameletter;
    }

    public String getNicknameletter() {
        return nicknameletter;
    }

    public void setNicknamelettercode(int nicknamelettercode) {
        this.nicknamelettercode = nicknamelettercode;
    }

    public int getNicknamelettercode() {
        return nicknamelettercode;
    }

    public void setPhotoimgurl(String photoimgurl) {
        this.photoimgurl = photoimgurl;
    }

    public String getPhotoimgurl() {
        return photoimgurl;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setDealerid(int dealerid) {
        this.dealerid = dealerid;
    }

    public int getDealerid() {
        return dealerid;
    }

    public void setDealername(String dealername) {
        this.dealername = dealername;
    }

    public String getDealername() {
        return dealername;
    }

    public void setFansnum(String fansnum) {
        this.fansnum = fansnum;
    }

    public String getFansnum() {
        return fansnum;
    }

    public void setThumbupnum(String thumbupnum) {
        this.thumbupnum = thumbupnum;
    }

    public String getThumbupnum() {
        return thumbupnum;
    }

    public void setIslike(int islike) {
        this.islike = islike;
    }

    public int getIslike() {
        return islike;
    }

    public void setSalesid(int salesid) {
        this.salesid = salesid;
    }

    public int getSalesid() {
        return salesid;
    }

    public void setUsermainurl(String usermainurl) {
        this.usermainurl = usermainurl;
    }

    public String getUsermainurl() {
        return usermainurl;
    }

    public void setCertificationtype(int certificationtype) {
        this.certificationtype = certificationtype;
    }

    public int getCertificationtype() {
        return certificationtype;
    }

    public void setImid(String imid) {
        this.imid = imid;
    }

    public String getImid() {
        return imid;
    }

    public void setUsertype(int usertype) {
        this.usertype = usertype;
    }

    public int getUsertype() {
        return usertype;
    }

    public void setIshavehomepage(int ishavehomepage) {
        this.ishavehomepage = ishavehomepage;
    }

    public int getIshavehomepage() {
        return ishavehomepage;
    }

    public void setUserreplyrate(int userreplyrate) {
        this.userreplyrate = userreplyrate;
    }

    public int getUserreplyrate() {
        return userreplyrate;
    }

    public void setUserreplydesc(String userreplydesc) {
        this.userreplydesc = userreplydesc;
    }

    public String getUserreplydesc() {
        return userreplydesc;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setSaleslevel(int saleslevel) {
        this.saleslevel = saleslevel;
    }

    public int getSaleslevel() {
        return saleslevel;
    }

    public void setCityid(int cityid) {
        this.cityid = cityid;
    }

    public int getCityid() {
        return cityid;
    }

    public void setCityname(String cityname) {
        this.cityname = cityname;
    }

    public String getCityname() {
        return cityname;
    }

    public void setServiceusertotalcount(int serviceusertotalcount) {
        this.serviceusertotalcount = serviceusertotalcount;
    }

    public int getServiceusertotalcount() {
        return serviceusertotalcount;
    }

    public void setServiceusercount(int serviceusercount) {
        this.serviceusercount = serviceusercount;
    }

    public int getServiceusercount() {
        return serviceusercount;
    }

    public void setUsersalescore(String usersalescore) {
        this.usersalescore = usersalescore;
    }

    public String getUsersalescore() {
        return usersalescore;
    }

    public void setDistancedesc(String distancedesc) {
        this.distancedesc = distancedesc;
    }

    public String getDistancedesc() {
        return distancedesc;
    }

}
