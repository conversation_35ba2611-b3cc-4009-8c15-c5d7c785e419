package com.autohome.applet.model.dto.netcoreapi.series;

public class DimSeriesPRCTypeSummary {


    private int angleIndicatorLevel2Key;
    private int angleIndicatorLevel3Key;
    private int angleIndicatorLevel4Key;
    private String combination;
    private int groupIndicator;
    private int indicatorClassify;
    private int orderNum;
    private int originalTypeKey;
    private int salesTypeKey;
    private int sentimentKey;
    private int seriesKey;
    private long seriesSummaryKey;
    private int typeKey;
    private int volume;

    public void setAngleIndicatorLevel2Key(int angleIndicatorLevel2Key) {
        this.angleIndicatorLevel2Key = angleIndicatorLevel2Key;
    }

    public int getAngleIndicatorLevel2Key() {
        return angleIndicatorLevel2Key;
    }

    public void setAngleIndicatorLevel3Key(int angleIndicatorLevel3Key) {
        this.angleIndicatorLevel3Key = angleIndicatorLevel3Key;
    }

    public int getAngleIndicatorLevel3Key() {
        return angleIndicatorLevel3Key;
    }

    public void setAngleIndicatorLevel4Key(int angleIndicatorLevel4Key) {
        this.angleIndicatorLevel4Key = angleIndicatorLevel4Key;
    }

    public int getAngleIndicatorLevel4Key() {
        return angleIndicatorLevel4Key;
    }

    public void setCombination(String combination) {
        this.combination = combination;
    }

    public String getCombination() {
        return combination;
    }

    public void setGroupIndicator(int groupIndicator) {
        this.groupIndicator = groupIndicator;
    }

    public int getGroupIndicator() {
        return groupIndicator;
    }

    public void setIndicatorClassify(int indicatorClassify) {
        this.indicatorClassify = indicatorClassify;
    }

    public int getIndicatorClassify() {
        return indicatorClassify;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOriginalTypeKey(int originalTypeKey) {
        this.originalTypeKey = originalTypeKey;
    }

    public int getOriginalTypeKey() {
        return originalTypeKey;
    }

    public void setSalesTypeKey(int salesTypeKey) {
        this.salesTypeKey = salesTypeKey;
    }

    public int getSalesTypeKey() {
        return salesTypeKey;
    }

    public void setSentimentKey(int sentimentKey) {
        this.sentimentKey = sentimentKey;
    }

    public int getSentimentKey() {
        return sentimentKey;
    }

    public void setSeriesKey(int seriesKey) {
        this.seriesKey = seriesKey;
    }

    public int getSeriesKey() {
        return seriesKey;
    }

    public void setSeriesSummaryKey(long seriesSummaryKey) {
        this.seriesSummaryKey = seriesSummaryKey;
    }

    public long getSeriesSummaryKey() {
        return seriesSummaryKey;
    }

    public void setTypeKey(int typeKey) {
        this.typeKey = typeKey;
    }

    public int getTypeKey() {
        return typeKey;
    }

    public void setVolume(int volume) {
        this.volume = volume;
    }

    public int getVolume() {
        return volume;
    }

}