/**
 * ResponseContent.java
 * com.jfly.core.httpclient
 * Copyright (c) 2014.
 */

package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class ResponseContent<T> {

    public ResponseContent() {
    }

    public ResponseContent(T result) {
        this.returncode = 0;
        this.message = "成功";
        this.result = result;
    }

    public ResponseContent(int returncode, String message) {
        this.returncode = returncode;
        this.message = message;
        this.result = null;
    }

    public ResponseContent(int returncode, String message, T result) {
        this.returncode = returncode;
        this.message = message;
        this.result = result;
    }

    private int returncode;
    private String message = StringUtils.EMPTY;
    private T result;
    private List<T> resultList;

    public int getReturncode() {
        return returncode;
    }

    public void setReturncode(int returncode) {
        this.returncode = returncode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public void setResultList(List<T> resultList) {
        this.resultList = resultList;
    }
}
