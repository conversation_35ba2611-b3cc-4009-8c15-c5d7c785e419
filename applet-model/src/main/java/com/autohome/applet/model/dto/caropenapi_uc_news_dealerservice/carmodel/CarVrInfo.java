package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/17.
 */
public class CarVrInfo {
    @JsonProperty("HasInterior")
    private boolean hasinterior;
    @JsonProperty("HasExterior")
    private boolean hasexterior;
    @JsonProperty("IntInfo")
    private List<Intinfo> intinfo;
    @JsonProperty("ExtInfo")
    private List<Extinfo> extinfo;
    @JsonProperty("QueryTime")
    private String querytime;

    public void setHasinterior(boolean hasinterior) {
        this.hasinterior = hasinterior;
    }

    public boolean getHasinterior() {
        return hasinterior;
    }

    public void setHasexterior(boolean hasexterior) {
        this.hasexterior = hasexterior;
    }

    public boolean getHasexterior() {
        return hasexterior;
    }

    public void setIntinfo(List<Intinfo> intinfo) {
        this.intinfo = intinfo;
    }

    public List<Intinfo> getIntinfo() {
        return intinfo;
    }

    public void setExtinfo(List<Extinfo> extinfo) {
        this.extinfo = extinfo;
    }

    public List<Extinfo> getExtinfo() {
        return extinfo;
    }

    public void setQuerytime(String querytime) {
        this.querytime = querytime;
    }

    public String getQuerytime() {
        return querytime;
    }


    public static class Intinfo {

        @JsonProperty("PanoId")
        private int panoid;
        @JsonProperty("ShowUrl")
        private String showurl;
        @JsonProperty("CoverUrl")
        private String coverurl;
        @JsonProperty("VisitCount")
        private int visitcount;
        @JsonProperty("LikeCount")
        private int likecount;
        @JsonProperty("SceneCount")
        private int scenecount;
        @JsonProperty("HotspotCount")
        private int hotspotcount;
        @JsonProperty("SeriesId")
        private int seriesid;
        @JsonProperty("SeriesName")
        private String seriesname;
        @JsonProperty("SpecId")
        private int specid;
        @JsonProperty("SpecName")
        private String specname;
        @JsonProperty("brand_id")
        private int brandId;
        @JsonProperty("brand_name")
        private String brandName;
        @JsonProperty("min_price")
        private int minPrice;
        @JsonProperty("max_price")
        private int maxPrice;
        @JsonProperty("iframe_url")
        private String iframeUrl;
        @JsonProperty("is_show")
        private boolean isShow;

        public void setPanoid(int panoid) {
            this.panoid = panoid;
        }

        public int getPanoid() {
            return panoid;
        }

        public void setShowurl(String showurl) {
            this.showurl = showurl;
        }

        public String getShowurl() {
            if (StringUtil.isBlank(showurl)) {
                return showurl;
            }
            if (showurl.indexOf("?") >= 0) {
                return showurl + "&pure=1";
            }
            return showurl + "?pure=1";
        }

        public void setCoverurl(String coverurl) {
            this.coverurl = coverurl;
        }

        public String getCoverurl() {
            return coverurl;
        }

        public void setVisitcount(int visitcount) {
            this.visitcount = visitcount;
        }

        public int getVisitcount() {
            return visitcount;
        }

        public void setLikecount(int likecount) {
            this.likecount = likecount;
        }

        public int getLikecount() {
            return likecount;
        }

        public void setScenecount(int scenecount) {
            this.scenecount = scenecount;
        }

        public int getScenecount() {
            return scenecount;
        }

        public void setHotspotcount(int hotspotcount) {
            this.hotspotcount = hotspotcount;
        }

        public int getHotspotcount() {
            return hotspotcount;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setBrandId(int brandId) {
            this.brandId = brandId;
        }

        public int getBrandId() {
            return brandId;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setMinPrice(int minPrice) {
            this.minPrice = minPrice;
        }

        public int getMinPrice() {
            return minPrice;
        }

        public void setMaxPrice(int maxPrice) {
            this.maxPrice = maxPrice;
        }

        public int getMaxPrice() {
            return maxPrice;
        }

        public void setIframeUrl(String iframeUrl) {
            this.iframeUrl = iframeUrl;
        }

        public String getIframeUrl() {
            return iframeUrl;
        }

        public void setIsShow(boolean isShow) {
            this.isShow = isShow;
        }

        public boolean getIsShow() {
            return isShow;
        }

    }

    public static class Extinfo {

        @JsonProperty("ExtId")
        private int extid;
        @JsonProperty("ShowUrl")
        private String showurl;
        @JsonProperty("CoverUrl")
        private String coverurl;
        @JsonProperty("VisitCount")
        private int visitcount;
        @JsonProperty("LikeCount")
        private int likecount;
        @JsonProperty("ColorCount")
        private int colorcount;
        @JsonProperty("FrameCount")
        private int framecount;
        @JsonProperty("HotspotCount")
        private int hotspotcount;
        @JsonProperty("SeriesId")
        private int seriesid;
        @JsonProperty("SeriesName")
        private String seriesname;
        @JsonProperty("SpecId")
        private int specid;
        @JsonProperty("SpecName")
        private String specname;
        @JsonProperty("BaseColorList")
        private List<String> basecolorlist;
        @JsonProperty("ColorList")
        private List<String> colorlist;
        @JsonProperty("brand_id")
        private int brandId;
        @JsonProperty("brand_name")
        private String brandName;
        @JsonProperty("min_price")
        private int minPrice;
        @JsonProperty("max_price")
        private int maxPrice;
        @JsonProperty("iframe_url")
        private String iframeUrl;
        @JsonProperty("is_show")
        private boolean isShow;

        public void setExtid(int extid) {
            this.extid = extid;
        }

        public int getExtid() {
            return extid;
        }

        public void setShowurl(String showurl) {
            this.showurl = showurl;
        }

        public String getShowurl() {
            if (StringUtil.isBlank(showurl)) {
                return showurl;
            }
            if (showurl.indexOf("?") >= 0) {
                return showurl + "&pure=1";
            }
            return showurl + "?pure=1";
        }

        public void setCoverurl(String coverurl) {
            this.coverurl = coverurl;
        }

        public String getCoverurl() {
            return coverurl;
        }

        public void setVisitcount(int visitcount) {
            this.visitcount = visitcount;
        }

        public int getVisitcount() {
            return visitcount;
        }

        public void setLikecount(int likecount) {
            this.likecount = likecount;
        }

        public int getLikecount() {
            return likecount;
        }

        public void setColorcount(int colorcount) {
            this.colorcount = colorcount;
        }

        public int getColorcount() {
            return colorcount;
        }

        public void setFramecount(int framecount) {
            this.framecount = framecount;
        }

        public int getFramecount() {
            return framecount;
        }

        public void setHotspotcount(int hotspotcount) {
            this.hotspotcount = hotspotcount;
        }

        public int getHotspotcount() {
            return hotspotcount;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setBasecolorlist(List<String> basecolorlist) {
            this.basecolorlist = basecolorlist;
        }

        public List<String> getBasecolorlist() {
            return basecolorlist;
        }

        public void setColorlist(List<String> colorlist) {
            this.colorlist = colorlist;
        }

        public List<String> getColorlist() {
            return colorlist;
        }

        public void setBrandId(int brandId) {
            this.brandId = brandId;
        }

        public int getBrandId() {
            return brandId;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setMinPrice(int minPrice) {
            this.minPrice = minPrice;
        }

        public int getMinPrice() {
            return minPrice;
        }

        public void setMaxPrice(int maxPrice) {
            this.maxPrice = maxPrice;
        }

        public int getMaxPrice() {
            return maxPrice;
        }

        public void setIframeUrl(String iframeUrl) {
            this.iframeUrl = iframeUrl;
        }

        public String getIframeUrl() {
            return iframeUrl;
        }

        public void setIsShow(boolean isShow) {
            this.isShow = isShow;
        }

        public boolean getIsShow() {
            return isShow;
        }

    }
}
