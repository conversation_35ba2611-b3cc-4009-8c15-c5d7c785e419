package com.autohome.applet.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 捐赠终端 1-华为 2-苹果
 * @createTime 2023-09-18 16:21:00
 */
public enum DonateTerminalEnum {
    HUAWEI(1, "华为"),//华为
    APPLE(2, "苹果"), //苹果
    ;

    private final static Map<Integer, DonateTerminalEnum> _CODES_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));


    private Integer code;
    private String info;

    private DonateTerminalEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static DonateTerminalEnum fromCode(int code) {
        return _CODES_MAP.getOrDefault(code, null);
    }
}
