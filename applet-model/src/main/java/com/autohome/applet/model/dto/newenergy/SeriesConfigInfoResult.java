package com.autohome.applet.model.dto.newenergy;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: WangBoWen
 * @date: 2024-01-11
 **/
@Data
public class SeriesConfigInfoResult {

    private Integer seriesid;

    private List<?> specList;

    private List<ParamtypeitemsDTO> paramtypeitems;

    @Data
    public static class ParamtypeitemsDTO{

        private String groupname;

        private String name;

        private List<ParamitemsDTO> paramitems;

        @Data
        public static class ParamitemsDTO{

            private Integer id;

            private String name;

            private List<ValueitemsDTO> valueitems;

            @Data
            public static class ValueitemsDTO{

                private Integer specid;

                private String value;

                private List<?> sublist;

            }

        }
    }
}
