package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecBaseInfo {

    @JsonProperty("id")
    private int id;

    @JsonProperty("brandid")
    private int brandId;

    @JsonProperty("brandname")
    private String brandName;

    @JsonProperty("fctid")
    private int fctId;

    @JsonProperty("fctname")
    private String fctName;

    @JsonProperty("seriesid")
    private int seriesId;

    @JsonProperty("seriesname")
    private String seriesName;

    @JsonProperty("levelid")
    private int levelId;

    @JsonProperty("levelname")
    private String levelName;

    @JsonProperty("specid")
    private int specId;

    @JsonProperty("specname")
    private String specName;

    @JsonProperty("specisstop")
    private int specIsTop;

    @JsonProperty("specimg")
    private String specImg;

    @JsonProperty("minprice")
    private String minPrice;

    @JsonProperty("maxprice")
    private String maxPrice;

    @JsonProperty("url")
    private String url;

    @JsonProperty("ispevcar")
    private int isPEVCar;

    @JsonProperty("imglist")
    private String[] imgList;

    @JsonProperty("classpicnum")
    private int classPicNum;

    public static class SpecItemList {

        @JsonProperty("specitems")
        private List<SpecBaseInfo> specItems;

        public List<SpecBaseInfo> getSpecItems() {
            return specItems;
        }

        public void setSpecItems(List<SpecBaseInfo> specItems) {
            this.specItems = specItems;
        }
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getBrandId() {
        return brandId;
    }

    public void setBrandId(int brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public int getFctId() {
        return fctId;
    }

    public void setFctId(int fctId) {
        this.fctId = fctId;
    }

    public String getFctName() {
        return fctName;
    }

    public void setFctName(String fctName) {
        this.fctName = fctName;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public int getLevelId() {
        return levelId;
    }

    public void setLevelId(int levelId) {
        this.levelId = levelId;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public int getSpecIsTop() {
        return specIsTop;
    }

    public void setSpecIsTop(int specIsTop) {
        this.specIsTop = specIsTop;
    }

    public String getSpecImg() {
        return specImg;
    }

    public void setSpecImg(String specImg) {
        this.specImg = specImg;
    }

    public String getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(String minPrice) {
        this.minPrice = minPrice;
    }

    public String getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(String maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getIsPEVCar() {
        return isPEVCar;
    }

    public void setIsPEVCar(int isPEVCar) {
        this.isPEVCar = isPEVCar;
    }

    public String[] getImgList() {
        return imgList;
    }

    public void setImgList(String[] imgList) {
        this.imgList = imgList;
    }

    public int getClassPicNum() {
        return classPicNum;
    }

    public void setClassPicNum(int classPicNum) {
        this.classPicNum = classPicNum;
    }
}
