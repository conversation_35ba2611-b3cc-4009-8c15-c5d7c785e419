package com.autohome.applet.model.dto.donate;

import com.autohome.applet.model.enums.DonateDataSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonateCacheModel {
    private int deviceHashCode;
    private DonateDataSourceEnum donateDataSourceEnum;
    private long searchAfter;

    public static DonateCacheModel buildDefaultResult(int deviceHashCode) {
        return DonateCacheModel.builder()
                .deviceHashCode(deviceHashCode)
                .donateDataSourceEnum(DonateDataSourceEnum.BEHAVIORHISTORY)
                .searchAfter(0)
                .build();
    }
}
