package com.autohome.applet.model.dto.netcoreapi.output;

public class GetArticleDetailById {
    private int start;

    private int titletype;

    private int id;

    private String title;

    private String img;

    private int authorid;

    private String authorname;

    private String avatar;

    private String publishtime;

    private String content;

    private String class1;

    private String class1name;

    private int lookcount;

    private int replycount;

    private int likecount;

    private int views;

    private String seriesids;

    private boolean ispublish;

    private boolean recycled;

    private int infotype;

    private String thumbnailimg;

    private String newkeywords;

    private String description;

    private String pcurl;

    private String showKeywords;

    private String ipprovince;

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getTitletype() {
        return titletype;
    }

    public void setTitletype(int titletype) {
        this.titletype = titletype;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getAuthorid() {
        return authorid;
    }

    public void setAuthorid(int authorid) {
        this.authorid = authorid;
    }

    public String getAuthorname() {
        return authorname;
    }

    public void setAuthorname(String authorname) {
        this.authorname = authorname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getClass1() {
        return class1;
    }

    public void setClass1(String class1) {
        this.class1 = class1;
    }

    public String getClass1name() {
        return class1name;
    }

    public void setClass1name(String class1name) {
        this.class1name = class1name;
    }

    public int getLookcount() {
        return lookcount;
    }

    public void setLookcount(int lookcount) {
        this.lookcount = lookcount;
    }

    public int getReplycount() {
        return replycount;
    }

    public void setReplycount(int replycount) {
        this.replycount = replycount;
    }

    public int getLikecount() {
        return likecount;
    }

    public void setLikecount(int likecount) {
        this.likecount = likecount;
    }

    public int getViews() {
        return views;
    }

    public void setViews(int views) {
        this.views = views;
    }

    public String getSeriesids() {
        return seriesids;
    }

    public void setSeriesids(String seriesids) {
        this.seriesids = seriesids;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public boolean getRecycled() {
        return recycled;
    }

    public void setRecycled(boolean recycled) {
        this.recycled = recycled;
    }

    public int getInfotype() {
        return infotype;
    }

    public void setInfotype(int infotype) {
        this.infotype = infotype;
    }

    public String getThumbnailimg() {
        return thumbnailimg;
    }

    public void setThumbnailimg(String thumbnailimg) {
        this.thumbnailimg = thumbnailimg;
    }

    public String getNewkeywords() {
        return newkeywords;
    }

    public void setNewkeywords(String newkeywords) {
        this.newkeywords = newkeywords;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPcurl() {
        return pcurl;
    }

    public void setPcurl(String pcurl) {
        this.pcurl = pcurl;
    }

    public String getShowKeywords() {
        return showKeywords;
    }

    public void setShowKeywords(String showKeywords) {
        this.showKeywords = showKeywords;
    }

    public String getIpprovince() {
        return ipprovince;
    }

    public void setIpprovince(String ipprovince) {
        this.ipprovince = ipprovince;
    }
}
