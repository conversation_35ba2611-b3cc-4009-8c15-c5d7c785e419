package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

import com.autohome.applet.model.dto.car.MarketCarDTO;
import com.autohome.applet.util.DateHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AllNewBrands {
    private String appurl;
    private int brandid;
    private int createsource;
    private String factory;
    private int id;
    private String img;
    private String imgurl;
    private int ismanage;
    private String launchtime;
    private int levelid;
    private String levelname;
    private String lightpoint;
    private String manageendtime;
    private String managetime;
    private int newsid;
    private boolean onmarket;
    private String ontime;
    private String ontimenote;
    private int seriesid;
    private String seriesname;
    private String smalltitle;
    private int tagid;
    /**
     * 枚举参考：
     * 1:全新车系
     * 2:车系换代
     * 4:新加车型
     * 5:中期改款
     * 6:小改款
     * */
    private String tagname;
    private String url;

    private int isInquiry;
    private String showUrl;
    private int state;

    private boolean isenergy;
    private String remark;
    private int planmarket;
    /**
     * 计划上市区间
     * 枚举如下：
     * 1  月内
     * 2  上旬
     * 3 中旬
     * 4  下旬
     * */
    private String planmarketdec;
    /**
     * 0：明确时间
     * 1：未明确时间
     * */
    private String activetimetype;
    private int eventlevel;
    /**
     * 运营编辑的新车上市话术
     * */
    private String activetimedesc;
    /**
     * 枚举参考：
     * 0: 未评级
     * 1：S
     * 2：A
     * 3： B
     * 4： C
     * */
    private String eventleveldec;

    private List<MarketCarDTO.MarketCarContent> contentList;

    private Long ontimeDate;

    private Long ontimeDatev2;

    public Long getOntimeDate() {
        if(StringUtils.isEmpty(this.ontime)){
            return 0L;
        }
        Date date = DateHelper.deserializeDateT(this.ontime);
        return date == null ? 0 : date.getTime();
    }

    public String getOntimeDateStr(String format) {
        if(StringUtils.isEmpty(this.ontime)){
            return "";
        }
        Date date = DateHelper.deserializeDateT(this.ontime);
        return date == null ? "" : DateHelper.serialize(date, format);
    }

    public Long getOntimeDateV2(){
        if(StringUtils.isEmpty(this.ontimenote)){
            return 0l;
        }
        Date date = DateHelper.deserialize(this.ontimenote, DateHelper.DATEFORMAT_STANDARD);
        return date == null ? 0 : date.getTime();
    }

    public String getOntimeDateV2Str() {
        if(StringUtils.isEmpty(this.ontimenote)){
            return "";
        }
        Date date = DateHelper.deserialize(this.ontimenote, DateHelper.DATEFORMAT_STANDARD);
        if (date == null) {
            return "";
        }
        /**
         * 当activeTimeType=0（明确时间）返回activeBeginTime：yyyy-MM-dd HH:mm:ss中MM-dd
         * 当activeTimeType=1（未明确时间）返回activeBeginTime：yyyy-MM-dd HH:mm:ss中MM+planMarket中文案
         * */
        String tmpStr;
        if ("1".equals(this.activetimetype)) {
            tmpStr = DateHelper.serialize(date, DateHelper.DATEFORMAT_MONTH) + "-" + this.getPlanmarketdec();
        } else {
            tmpStr = DateHelper.serialize(date, DateHelper.DATEFORMAT_MONTH_DAY);
        }
        return tmpStr;
    }

    public String getOntimeOnlyDateStr() {
        if(StringUtils.isEmpty(this.ontimenote)){
            return "";
        }
        Date date = DateHelper.deserialize(this.ontimenote, DateHelper.DATEFORMAT_STANDARD);
        if (date == null) {
            return "";
        }
        /**
         * 只取年月日， 排序分组使用
         * */
        String tmpStr = DateHelper.serialize(date, DateHelper.DATEFORMAT_ONLY_DATE);
        return tmpStr;
    }

    /**
     * 返回 08-06,08-上旬
     * */
    public String getOntimeDateV3Str() {
        if(StringUtils.isEmpty(this.ontimenote)){
            return "";
        }
        Date date = DateHelper.deserialize(this.ontimenote, DateHelper.DATEFORMAT_STANDARD);
        if (date == null) {
            return "";
        }
        /**
         * 当activeTimeType=0（明确时间）返回activeBeginTime：yyyy-MM-dd HH:mm:ss中MM-dd
         * 当activeTimeType=1（未明确时间）返回activeBeginTime：yyyy-MM-dd HH:mm:ss中MM+planMarket中文案
         * */
        String tmpStr;
        if ("1".equals(this.activetimetype)) {
            tmpStr = DateHelper.serialize(date, DateHelper.DATEFORMAT_MONTH_2) + "-" + this.getPlanmarketdec();
        } else {
            tmpStr = DateHelper.serialize(date, DateHelper.DATEFORMAT_MONTH_DAY_2);
        }
        return tmpStr;
    }

    public String getTagname() {
        /**
         * 枚举参考：
         * 1:全新车系
         * 2:车系换代
         * 4:新加车型
         * 5:中期改款
         * 6:小改款
         * */
        if(tagid == 1){
            return "全新车系";
        }
        else if(tagid == 1){
            return "全新车系";
        }
        else if(tagid == 2){
            return "车系换代";
        }
        else if(tagid == 4){
            return "新加车型";
        }
        else if(tagid == 5){
            return "中期改款";
        }
        else if(tagid == 6){
            return "小改款";
        }
        return String.valueOf(tagid);
    }

    public String getPlanmarketdec() {
        /**
         * 计划上市区间
         * 枚举如下：
         * 1  月内
         * 2  上旬
         * 3 中旬
         * 4  下旬
         * */
        switch (this.planmarket){
            case 1:{
                return "月内";
            }
            case 2:{
                return "上旬";
            }
            case 3:{
                return "中旬";
            }
            case 4:{
                return "下旬";
            }
            default: {
                //源数据有出现planmarket是空的情况，此处做一下兼容
                return "月内";
            }
        }
    }

    public String getEventleveldec() {
        /**
         * 枚举参考：
         * 0: 未评级
         * 1：S
         * 2：A
         * 3： B
         * 4： C
         * */
        switch (this.eventlevel){
            case 1:{
                return "S";
            }
            case 2:{
                return "A";
            }
            case 3:{
                return "B";
            }
            case 4:{
                return "C";
            }
            default: {
                return "未评级";
            }
        }
    }

    @Override
    public String toString() {
        return "AllNewBrands{" +
                "appurl='" + appurl + '\'' +
                ", brandid=" + brandid +
                ", createsource=" + createsource +
                ", factory='" + factory + '\'' +
                ", id=" + id +
                ", img='" + img + '\'' +
                ", imgurl='" + imgurl + '\'' +
                ", ismanage=" + ismanage +
                ", launchtime='" + launchtime + '\'' +
                ", levelid=" + levelid +
                ", levelname='" + levelname + '\'' +
                ", lightpoint='" + lightpoint + '\'' +
                ", manageendtime='" + manageendtime + '\'' +
                ", managetime='" + managetime + '\'' +
                ", newsid=" + newsid +
                ", onmarket=" + onmarket +
                ", ontime='" + ontime + '\'' +
                ", ontimenote='" + ontimenote + '\'' +
                ", seriesid=" + seriesid +
                ", seriesname='" + seriesname + '\'' +
                ", smalltitle='" + smalltitle + '\'' +
                ", tagid=" + tagid +
                ", tagname='" + tagname + '\'' +
                ", url='" + url + '\'' +
                ", isInquiry=" + isInquiry +
                ", showUrl='" + showUrl + '\'' +
                ", state=" + state +
                ", isenergy=" + isenergy +
                ", remark='" + remark + '\'' +
                ", planmarket=" + planmarket +
                ", planmarketdec='" + planmarketdec + '\'' +
                ", activetimetype='" + activetimetype + '\'' +
                ", eventlevel=" + eventlevel +
                ", activetimedesc='" + activetimedesc + '\'' +
                ", eventleveldec='" + eventleveldec + '\'' +
                ", contentList=" + contentList +
                ", ontimeDate=" + ontimeDate +
                ", ontimeDatev2=" + ontimeDatev2 +
                '}';
    }
}
