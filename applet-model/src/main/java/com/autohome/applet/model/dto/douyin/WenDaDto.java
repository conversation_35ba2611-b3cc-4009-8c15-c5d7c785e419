package com.autohome.applet.model.dto.douyin;

import lombok.Data;

import java.util.List;

@Data
public class WenDaDto {

    private TagData tag;
    private List<ItemData> items;
    private int totalcount;
    private String pageid;
    private boolean hasmore;

    @Data
    public static class TagData {
        private int id;
        private String name;
        private int isdel;
        private int aigcarticletagid;
        private int num;
    }

    @Data
    public static class ItemData {
        private int id;
        private String title;
        private int articletype;
        private String img;
        private int authorid;
        private String authorhead;
        private String authorname;
        private int auditorid;
        private int ispublish;
        private String publishtime;
        private String publishprovince;
        private String description;
        private int isdel;
        private String pushid;
        private int manualstate;
        private int auditcontrol;
    }
}
