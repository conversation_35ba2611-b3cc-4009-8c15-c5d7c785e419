package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.LatMapLonBaidu;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by hanshanfeng on 2018/5/11.
 */
public class Dealer {

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String OrderRange;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String ProvinceId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String DealerId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String CompanySimple;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String Address;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String MinPrice;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String SellPhone;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String KindId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String MapLatBaidu;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String MapLonBaidu;

    //region 输出字段
    private String id;

    private String dealername;

    private String price;

    private String phone;

    private String titleflag;

    private String type;

    private String latitude;

    private String longitude;

    private String distance;

    private String distanceitem;

    public String getLatitude() {
        return MapLatBaidu;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return MapLonBaidu;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getDistance() {
        try {
//            String mile=Double.toString(LatMapLonBaidu.DistanceOfTwoPoints(Double.parseDouble(latitude), Double.parseDouble(longitude), Double.parseDouble(MapLatBaidu), Double.parseDouble(MapLonBaidu)));
//            if(Double.parseDouble(mile)==0){
//                return "";
//            }
            if (StringUtil.isNotNullAndWhiteSpace(distance)) {
                distance = LatMapLonBaidu.format(distance);
            }
            return distance;
        } catch (NumberFormatException e) {
            return "";
        }
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    public String getDistanceitem() {
        try {
            if (StringUtil.isNotNullAndWhiteSpace(distance)) {
                distanceitem = Double.parseDouble(distance) > 999 ? "距离999+公里" : "距离" + LatMapLonBaidu.format(distance) + "公里";
            }
            return distanceitem;
        } catch (NumberFormatException e) {
            return "";
        }
    }

    public void setDistanceitem(String distanceitem) {
        this.distanceitem = distanceitem;
    }

    public String getTitleflag() {
        try {
            int orderRange = StringUtil.isNotNullAndWhiteSpace(this.OrderRange) ? Integer.parseInt(this.OrderRange) : 0;

            if (orderRange == 0) {
                titleflag = "售全国";
            } else if (orderRange == 1) {
                if (ProvinceId.equals("110000") || ProvinceId.equals("120000") || ProvinceId.equals("310000") || ProvinceId.equals("500000")) {
                    titleflag = "售本市";
                } else {
                    titleflag = "售本省";
                }
            } else if (orderRange == 2) {
                titleflag = "售本市";
            } else if (orderRange == 3) {
                titleflag = "售多市";
            }
            return titleflag;
        } catch (NumberFormatException e) {
            return "售全国";
        }
    }

    public void setTitleflag(String titleflag) {
        this.titleflag = titleflag;
    }

    public String getType() {
        if (!StringUtil.isNotNullAndWhiteSpace(KindId)) {
            return "";
        }
        return KindId.equals("1") ? "4S店" : (KindId.equals("2") ? "综合" : "展厅");
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPhone() {
        return SellPhone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPrice() {
        try {
            if (!StringUtil.isNotNullAndWhiteSpace(MinPrice)) {
                return "0.00万";
            }

            return CarPriceUtils.getStrPrice(MinPrice, MinPrice);
        } catch (Exception e) {
            return "0.00万";
        }
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getDealername() {
        if (StringUtil.isNotNullAndWhiteSpace(dealername)) {
            return dealername;
        }
        return CompanySimple;
    }

    public void setDealername(String dealername) {
        this.dealername = dealername;
    }

    public String getId() {
        if (StringUtil.isNotNullAndWhiteSpace(id)) {
            return id;
        }
        return DealerId;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setOrderRange(String OrderRange) {
        this.OrderRange = OrderRange;
    }

    public void setProvinceId(String provinceId) {
        this.ProvinceId = provinceId;
    }

    public void setDealerId(String dealerId) {
        this.DealerId = dealerId;
    }

    public void setCompanySimple(String companySimple) {
        CompanySimple = companySimple;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public void setMinPrice(String minPrice) {
        MinPrice = minPrice;
    }

    public void setSellPhone(String sellPhone) {
        SellPhone = sellPhone;
    }

    public void setKindId(String kindId) {
        KindId = kindId;
    }

    public void setMapLatBaidu(String mapLatBaidu) {
        MapLatBaidu = mapLatBaidu;
    }

    public void setMapLonBaidu(String mapLonBaidu) {
        MapLonBaidu = mapLonBaidu;
    }
}
