package com.autohome.applet.model.dto.car;

import com.autohome.applet.model.dto.netcoreapi.series.CarSpecInfoquick;

/**
 * <AUTHOR>
 */
public class OnSaleSeriesDto {

    private int seriesId;
    private String seriesName;
    private String logo;
    private int minPrice;
    private int maxPrice;
    private CarSpecInfoquick specInfoquick;

    public static OnSaleSeriesDto by(ParamBySeries series) {
        OnSaleSeriesDto dto = new OnSaleSeriesDto();
        dto.setSeriesId(series.getId());
        dto.setSeriesName(series.getName());
        dto.setLogo(series.getPnglogo());
        dto.setMinPrice(series.getMinprice());
        dto.setMaxPrice(series.getMaxprice());
        dto.setSpecInfoquick(series.getSpecInfoquick());
        return dto;
    }

    public CarSpecInfoquick getSpecInfoquick() {
        return specInfoquick;
    }

    public void setSpecInfoquick(CarSpecInfoquick specInfoquick) {
        this.specInfoquick = specInfoquick;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public int getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(int minPrice) {
        this.minPrice = minPrice;
    }

    public int getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(int maxPrice) {
        this.maxPrice = maxPrice;
    }
}
