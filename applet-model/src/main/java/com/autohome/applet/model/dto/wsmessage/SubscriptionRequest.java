package com.autohome.applet.model.dto.wsmessage;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SubscriptionRequest {

//    @NotNull(message = "缺少type")
//    @Min(value = 0,message = "请输入正确的订阅类型")
//    @Max(value = 1,message = "请输入正确的订阅类型")
//    private Integer type;

    @NotBlank(message = "缺少fwhOpenId")
    private String fwhOpenId;

    @NotBlank(message = "缺少unionId")
    private String unionId;

    @NotNull(message = "缺少订阅类型")
    @Min(value = 1,message = "请输入正确的订阅类型")
    private Integer biztype;


}