package com.autohome.applet.model.constants;

public class RedisKeys {

    // 产品库所有车系id集合
    public static String seriesIds = "applet:car:series:ids";

    public static final String CUSTOM_DEALER_SAVE_FOR_PUSH_CLUES = "applet:api:custom_dealer_save_for_push_clues:%s-%d";

    /**
     * 挽留对话框按日记录是否弹窗-车型车系，openId-visitId
     */
    public static final String DIALOG_DAILY_LOG_SERIES_SPEC = "applet:api:dialog_daily_log-series_spec:%s-%s";

    /**
     * 挽留对话框按日记录是否弹窗-高优，openId-visitId
     */
    public static final String DIALOG_DAILY_LOG_MAIN = "applet:api:dialog_daily_log-main:%s-%s";

    /**
     * 用户个人设置，是否开启个性化推荐（默认开启）
     */
    public static final String USER_SETTINGS_PERSONALIZED_REC = "applet:api:usersettings:personalizedrec:%s";

    /**
     * 新能源车系最好车型用户真实续航
     */
    public static final String CAR_OWNER_REAL_ENDURANCE_BEST_KEY = "applet:api:car:owner:real:endurance:data:series:%d:%d";
    /**
     * 新能源车系指定车型用户真实续航
     */
    public static final String CAR_OWNER_REAL_ENDURANCE_KEY = "applet:api:car:owner:real:endurance:data:series:%d:%d:%d";

    public static final String CAR_OWNER_REAL_ENDURANCE_BASE_INFO_KEY = "applet:api:car:owner:real:endurance:base:info:data:series:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_SPEC_INFO_V2_KEY = "applet:api:car:owner:real:endurance:spec:info:v2:data:series:%d:%d:%d:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_SPEC_INFO_KEY = "applet:api:car:owner:real:endurance:spec:info:data:series:%d:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_ENERGY_CONFIG_BEANS = "applet:api:car:owner:real:endurance:energy:config:bean:data:series:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_BEI_LI_DATA = "applet:api:car:owner:real:endurance:beili:data:series:%d:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_KOU_BEI_DATA = "applet:api:car:owner:real:endurance:koubei:data:series:%d:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_SPEC_PARAM_DATA = "applet:api:car:owner:real:endurance:spec:param:data:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_MAX_RANGE_SPEC_DATA = "applet:api:car:owner:real:endurance:max:range:spec:data:%d:%d";
    public static final String CAR_OWNER_REAL_ENDURANCE_FACT_DRIVE_PROCESS_DATA = "applet:api:car:owner:real:endurance:fact:drive:process:data:%d:%d";

    /**
     * 平安对接接口缓存
     */

    public static final String CAR_SALES_RANK = "car:sales:rank";
    public static final String VR_BRAND_LIST_NEW = "vr:brand:list:new";
    public static final String VR_SERIES_LIST = "vr:series:list:%d";
    public static final String CAR_VR_INFO = "car:vr:info:%s:%d";

}
