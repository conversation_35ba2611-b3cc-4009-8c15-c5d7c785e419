package com.autohome.applet.model.dto.car.portrayal;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CompetingSeries {

    private SectInfo sectInfo;
    private LevelInfo levelInfo;
    private String seriName;
    private int minPrice;
    private int seriState;
    private PlaceInfo placeInfo;
    private BrandInfo brandInfo;
    private int maxPrice;
    private ManuInfo manuInfo;
    private long hotNess;
    private List<CompetingSeri> competingSeriList;


    public static class CompetingSeri {
        private double score;
        private int seriId;
        private String seriName;
        private String img;
        private int minPrice;
        private int maxPrice;

        public double getScore() {
            return score;
        }

        public void setScore(double score) {
            this.score = score;
        }

        public int getSeriId() {
            return seriId;
        }

        public void setSeriId(int seriId) {
            this.seriId = seriId;
        }

        public String getSeriName() {
            return seriName;
        }

        public void setSeriName(String seriName) {
            this.seriName = seriName;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public int getMinPrice() {
            return minPrice;
        }

        public void setMinPrice(int minPrice) {
            this.minPrice = minPrice;
        }

        public int getMaxPrice() {
            return maxPrice;
        }

        public void setMaxPrice(int maxPrice) {
            this.maxPrice = maxPrice;
        }
    }

    public static class SectInfo {
        private String name;
        private int id;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public static class LevelInfo {
        private String name;
        private int id;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public static class PlaceInfo {
        private String name;
        private int id;

        // getter和setter方法省略

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public static class BrandInfo {
        private String name;
        private int id;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public static class ManuInfo {
        private String name;
        private int id;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public SectInfo getSectInfo() {
        return sectInfo;
    }

    public void setSectInfo(SectInfo sectInfo) {
        this.sectInfo = sectInfo;
    }

    public LevelInfo getLevelInfo() {
        return levelInfo;
    }

    public void setLevelInfo(LevelInfo levelInfo) {
        this.levelInfo = levelInfo;
    }

    public String getSeriName() {
        return seriName;
    }

    public void setSeriName(String seriName) {
        this.seriName = seriName;
    }

    public int getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(int minPrice) {
        this.minPrice = minPrice;
    }

    public int getSeriState() {
        return seriState;
    }

    public void setSeriState(int seriState) {
        this.seriState = seriState;
    }

    public PlaceInfo getPlaceInfo() {
        return placeInfo;
    }

    public void setPlaceInfo(PlaceInfo placeInfo) {
        this.placeInfo = placeInfo;
    }

    public BrandInfo getBrandInfo() {
        return brandInfo;
    }

    public void setBrandInfo(BrandInfo brandInfo) {
        this.brandInfo = brandInfo;
    }

    public int getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(int maxPrice) {
        this.maxPrice = maxPrice;
    }

    public ManuInfo getManuInfo() {
        return manuInfo;
    }

    public void setManuInfo(ManuInfo manuInfo) {
        this.manuInfo = manuInfo;
    }

    public long getHotNess() {
        return hotNess;
    }

    public void setHotNess(long hotNess) {
        this.hotNess = hotNess;
    }

    public List<CompetingSeri> getCompetingSeriList() {
        return competingSeriList;
    }

    public void setCompetingSeriList(List<CompetingSeri> competingSeriList) {
        this.competingSeriList = competingSeriList;
    }
}
