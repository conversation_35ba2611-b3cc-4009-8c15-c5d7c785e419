package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by hanshanfeng on 2018/8/27.
 */
public class Usagetaxcustom {
    @JsonProperty("usageTaxCustomValue")
    private int usagetaxcustomvalue;
    @JsonProperty("usageTaxCustomKey")
    private String usagetaxcustomkey;

    public int getUsagetaxcustomvalue() {
        return usagetaxcustomvalue;
    }

    public void setUsagetaxcustomvalue(int usagetaxcustomvalue) {
        this.usagetaxcustomvalue = usagetaxcustomvalue;
    }

    public String getUsagetaxcustomkey() {
        return usagetaxcustomkey;
    }

    public void setUsagetaxcustomkey(String usagetaxcustomkey) {
        this.usagetaxcustomkey = usagetaxcustomkey;
    }
}
