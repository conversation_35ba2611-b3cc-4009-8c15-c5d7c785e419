package com.autohome.applet.model.dto.car;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * zhangxiaonan
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BrandmenulistDTO {

    private Integer total;

    private List<brandlist> brandlist;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class brandlist {
        private Integer id;
        private String name;
        private String logo;
        private String firstletter;
        private String country;
        private Integer state;
        private Integer rank;
        private Integer uvrank;
        private Integer luxury;
        private Integer havenewenergy;
        private Integer orderrank;
    }

}
