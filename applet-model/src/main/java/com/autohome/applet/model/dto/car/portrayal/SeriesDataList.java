package com.autohome.applet.model.dto.car.portrayal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SeriesDataList {

    private Object debug;

    @JsonProperty("datalist")
    private List<DataItem> dataList;

    @Data
    public static class DataItem {
        private String img;
        private String ui;
        private String seriesName;
        @JsonProperty("carprice")
        private List<CarPrice> carPrice;
        private int seriesId;
    }

    @Data
    public static class CarPrice {
        private int specId;
        private int isPriceOff;
        private int minOriginalPrice;
        private int lastMonthDealPrice;
        private int seriesId;
    }
}
