package com.autohome.applet.model.dto.original;

import java.util.List;

/**
 * 重点ip源接口实体
 */
public class SOpsDataResult {

    private int returncode = 0;
    private String message = "";
    private Result result;

    public int getReturncode() {
        return returncode;
    }

    public void setReturncode(int returncode) {
        this.returncode = returncode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Result getResult() {
        return result;
    }
    
    public void setResult(Result result) {
        this.result = result;
    }
    
    public static class Result{
        private List<OpsDatItem> list;
    
        public List<OpsDatItem> getList() {
            return list;
        }
    
        public void setList(List<OpsDatItem> list) {
            this.list = list;
        }
    }

    public static class OpsDatItem{
        /**
         * 主体颜色
         */
        private Integer id;
        /**
         * 素材ID
         */
        private String objId;
        /**
         * 素材标题
         */
        private String objTitle;
        private Integer isShow;
        private Integer isTop;
        private Integer isDel;
        private String mainDataType;
        //2024-08-06 00:00:00.0
        private String showTimeEnd;
        /**
         * 排序值
         */
        private Integer orderNo;
        private String objImage1;
        private String objImage2;
        private String objImage3;
        /**
         * 跳转地址
         */
        private String objUrl;
        private Integer addType;
        //2024-08-06 00:00:00.0
        private String showTimeStart;
        private Integer isAllCity;
        private Integer objState;
        private Integer customType;
    
        public Integer getId() {
            return id;
        }
    
        public void setId(Integer id) {
            this.id = id;
        }
    
        public String getObjId() {
            return objId;
        }
    
        public void setObjId(String objId) {
            this.objId = objId;
        }
    
        public String getObjTitle() {
            return objTitle;
        }
    
        public void setObjTitle(String objTitle) {
            this.objTitle = objTitle;
        }
    
        public Integer getIsShow() {
            return isShow;
        }
    
        public void setIsShow(Integer isShow) {
            this.isShow = isShow;
        }
    
        public Integer getIsTop() {
            return isTop;
        }
    
        public void setIsTop(Integer isTop) {
            this.isTop = isTop;
        }
    
        public Integer getIsDel() {
            return isDel;
        }
    
        public void setIsDel(Integer isDel) {
            this.isDel = isDel;
        }
    
        public String getMainDataType() {
            return mainDataType;
        }
    
        public void setMainDataType(String mainDataType) {
            this.mainDataType = mainDataType;
        }
    
        public String getShowTimeEnd() {
            return showTimeEnd;
        }
    
        public void setShowTimeEnd(String showTimeEnd) {
            this.showTimeEnd = showTimeEnd;
        }
    
        public Integer getOrderNo() {
            return orderNo;
        }
    
        public void setOrderNo(Integer orderNo) {
            this.orderNo = orderNo;
        }
    
        public String getObjImage1() {
            return objImage1;
        }
    
        public void setObjImage1(String objImage1) {
            this.objImage1 = objImage1;
        }
    
        public String getObjImage2() {
            return objImage2;
        }
    
        public void setObjImage2(String objImage2) {
            this.objImage2 = objImage2;
        }
    
        public String getObjImage3() {
            return objImage3;
        }
    
        public void setObjImage3(String objImage3) {
            this.objImage3 = objImage3;
        }
    
        public String getObjUrl() {
            return objUrl;
        }
    
        public void setObjUrl(String objUrl) {
            this.objUrl = objUrl;
        }
    
        public Integer getAddType() {
            return addType;
        }
    
        public void setAddType(Integer addType) {
            this.addType = addType;
        }
    
        public String getShowTimeStart() {
            return showTimeStart;
        }
    
        public void setShowTimeStart(String showTimeStart) {
            this.showTimeStart = showTimeStart;
        }
    
        public Integer getIsAllCity() {
            return isAllCity;
        }
    
        public void setIsAllCity(Integer isAllCity) {
            this.isAllCity = isAllCity;
        }
    
        public Integer getObjState() {
            return objState;
        }
    
        public void setObjState(Integer objState) {
            this.objState = objState;
        }
    
        public Integer getCustomType() {
            return customType;
        }
    
        public void setCustomType(Integer customType) {
            this.customType = customType;
        }
    }
}
