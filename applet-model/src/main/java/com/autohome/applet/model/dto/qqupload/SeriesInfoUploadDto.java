package com.autohome.applet.model.dto.qqupload;

import java.util.List;

/**
 * 同步腾讯车系卡
 * */
public class SeriesInfoUploadDto {
    /**
     * 该条数据的唯一标识	后续更新删除强依赖
     * */
    private String id;
    /**
     * 卡片类型，品牌卡使用1000
     * */
    private Integer type;
    /**
     * 表示仅在该query下展示该数据，多个query使用竖线分割	query需要保证在词包范围内，否则将该条数据不会生效	示例：奔驰|梅赛德斯奔驰
     * */
    private String querys;
    /**
     * 城市码，表示该数据仅在该城市下展示，不填则默认不限制城市	6位城市码
     * */
    private String city_code;
    /**
     * 标题区
     * */
    private SeriesInfoUploadDto.Header header;
    /**
     * 中部商品信息
     * */
    private Item item;
    /**
     * 底部链接	数组长度范围[4,4]  链接中文汉字字符长度范围[2-4]
     * */
    private List<Link> links;


    public static class Header {
        /**
         * 标题	中文汉字字符长度<=16，超长将截断	【品牌名称】汽车车系-汽车之家
         * */
        private String title;
        /**
         * 点击标题区跳转链接，支持 小程序链接/H5链接		微信之家小程序品牌车系列表落地页路径
         * */
        private String url;

        private List<UrlEntity> urls;

        /**
         * 品牌描述文案	中文汉字字符长度<=20，超长将截断	为您提供及时、全面、准确的价格信息
         * */
        private String description;

        public Header(String title, String url, String description, List<UrlEntity> urls) {
            this.title = title;
            this.url = url;
            this.description = description;
            this.urls = urls;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<UrlEntity> getUrls() {
            return urls;
        }

        public void setUrls(List<UrlEntity> urls) {
            this.urls = urls;
        }
    }

    public static class Item {
        /**
         * 商品图片链接	素材比例可选：  l  3:2 -> 300*200px  l  1:1 -> 300*300px	该品牌下所有车系白底图
         * */
        private String image_url;
        /**
         * 商品主文案	中文汉字字符长度>= 2	该品牌下所有车系名称
         * */
        private String main_text;
        /**
         * 商品图片比例类型 1表示1:1 , 2表示3:2
         * */
        private int scale_type;
        /**
         * 商品副文案	中文汉字字符长度范围[2-7]	该品牌下所有车系当地经销商该车系报价起步价  格式：xx.xx万起
         * */
        private String sub_text;
        /**
         * 橙色价格方案		该车系当前城市经销商最低报价  格式：xx.xx万起
         * */
        private String accent_text;
        /**
         * 链接信息
         * */
        private SeriesInfoUploadDto.Link link;
        /**
         * 点击商品的跳转链接		微信之家当前车系落地页路径
         * */
        private String url;

        private List<UrlEntity> urls;

        public String getImage_url() {
            return image_url;
        }

        public void setImage_url(String image_url) {
            this.image_url = image_url;
        }

        public String getMain_text() {
            return main_text;
        }

        public void setMain_text(String main_text) {
            this.main_text = main_text;
        }

        public String getSub_text() {
            return sub_text;
        }

        public void setSub_text(String sub_text) {
            this.sub_text = sub_text;
        }

        public SeriesInfoUploadDto.Link getLink() {
            return link;
        }

        public void setLink(SeriesInfoUploadDto.Link link) {
            this.link = link;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getAccent_text() {
            return accent_text;
        }

        public void setAccent_text(String accent_text) {
            this.accent_text = accent_text;
        }

        public int getScale_type() {
            return scale_type;
        }

        public void setScale_type(int scale_type) {
            this.scale_type = scale_type;
        }

        public List<UrlEntity> getUrls() {
            return urls;
        }

        public void setUrls(List<UrlEntity> urls) {
            this.urls = urls;
        }
    }

    public static class Link {
        /**
         * 链接文字	中文汉字字符长度范围[2-3]	查成交价
         * */
        private String text;
        /**
         * 点击跳转的URL		微信之家查成交价落地页路径
         * */
        private String url;

        private List<UrlEntity> urls;

        public Link(String text, String url, List<UrlEntity> urls) {
            this.text = text;
            this.url = url;
            this.urls = urls;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public List<UrlEntity> getUrls() {
            return urls;
        }

        public void setUrls(List<UrlEntity> urls) {
            this.urls = urls;
        }
    }

    /**
     * 支持 应用deeplink/小程序链接/H5链接
     * 2：应用链接
     * 3：小程序链接
     * 4：H5链接
     * */
    public static class UrlEntity {
        private String url;
        /**
         * 2：应用链接
         * 3：小程序链接
         * 4：H5链接
         * */
        private Integer type;

        public UrlEntity(String url, Integer type) {
            this.url = url;
            this.type = type;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getQuerys() {
        return querys;
    }

    public void setQuerys(String querys) {
        this.querys = querys;
    }

    public String getCity_code() {
        return city_code;
    }

    public void setCity_code(String city_code) {
        this.city_code = city_code;
    }

    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    public List<Link> getLinks() {
        return links;
    }

    public void setLinks(List<Link> links) {
        this.links = links;
    }
}
