package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.netcoreapi.StringUtil;

/**
 * Created by ha<PERSON><PERSON><PERSON> on 2018/7/27.
 */
public class SpecPicture {
    private int id;
    private int rownum;
    private String bigimg;
    private int specid;
    private String smallimg;
    private String specname;
    private String minprice;
    private String maxprice;
    private String pricedescribe;
    private String specState;
    private String filepath;

    public String getSpecState() {
        return specState;
    }

    public void setSpecState(String specState) {
        this.specState = specState;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getRownum() {
        return rownum;
    }

    public void setRownum(int rownum) {
        this.rownum = rownum;
    }

    public String getBigimg() {
        return bigimg;
    }

    public void setBigimg(String bigimg) {
        this.bigimg = bigimg;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public String getSmallimg() {
        return smallimg;
    }

    public void setSmallimg(String smallimg) {
        this.smallimg = smallimg;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getMinprice() {
        return minprice;
    }

    public void setMinprice(String minprice) {
        this.minprice = minprice;
    }

    public String getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(String maxprice) {
        this.maxprice = maxprice;
    }

    public String getPricedescribe() {
        if (!StringUtil.isNotNullAndWhiteSpace(this.minprice)) {
            return "暂无报价";
        }
        return CarPriceUtils.getStrPrice(minprice, minprice);

    }

    public void setPricedescribe(String pricedescribe) {
        this.pricedescribe = pricedescribe;
    }

    public String getFilepath() {
        return filepath;
    }

    public void setFilepath(String filepath) {
        this.filepath = filepath;
    }
}
