package com.autohome.applet.model.dto.carlibrary.carapi.out;

import com.autohome.applet.model.dto.carlibrary.carapi.CarApiSpec;
import com.autohome.applet.util.SecurityKit;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarApiSpecOut {

    private String specId;
    private String specname;
//    private int rankhot;
    private int specstate;
//    private String selltime;
    private int displacement;
    private String gearbox;
    private int energytype;
//    private String mah;
    private int mileage;
    private int year;

    public static CarApiSpecOut by(CarApiSpec spec) {
        CarApiSpecOut carApiSpecOut = new CarApiSpecOut();
        carApiSpecOut.setSpecId(SecurityKit.encrypt(spec.getId()));
        carApiSpecOut.setSpecname(spec.getName());
//        carApiSpecOut.setRankhot(0);
        carApiSpecOut.setSpecstate(spec.getState()); // 状态
//        carApiSpecOut.setSelltime("");
        carApiSpecOut.setDisplacement(spec.getDisplacement()); // 排气量
        carApiSpecOut.setGearbox(spec.getGearbox()); // 变速箱
        carApiSpecOut.setEnergytype(spec.getElectrictype()); // 电动机类型：1纯电动、2增程式
//        carApiSpecOut.setMah("");
        carApiSpecOut.setMileage(spec.getEndurancemileage()); // 续航里程
        carApiSpecOut.setYear(spec.getYear());
        return carApiSpecOut;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CarApiSpecOut that = (CarApiSpecOut) o;

        return specId.equals(that.specId);
    }

    @Override
    public int hashCode() {
        return specId.hashCode();
    }
}
