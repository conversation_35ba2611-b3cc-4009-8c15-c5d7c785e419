package com.autohome.applet.model.dto.original;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class VideoUserBean implements Serializable {
    private static final long serialVersionUID = 1997845188117292347L;
    private boolean isfollowuser;
    private int jbid;
    private String homevideopv;
    private String homemorepv;
    private String hometabeid;
    private String channeltagpv;
    private long userid;
    private String nickname;
    private String avatar;
    private String description;
    private int usertype;
    private int order;
    private long playcount;
    private int fanscount;
    private int videocount;
    private int replycount;
    private String pageid;
    private String jbimg1;
    private String jbimg2;
    private boolean isshow;
    private boolean coreip;
    private int teamid;
    private int categoryid;
    private String categoryname;
    private String lastvideoupdatetime;
    private String pic_url;
    private String pic_url2;
    private String pic_url3;
    private String videList;
}
