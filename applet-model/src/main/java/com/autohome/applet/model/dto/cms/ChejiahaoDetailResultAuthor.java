package com.autohome.applet.model.dto.cms;

import java.util.Date;
import java.util.List;

public class ChejiahaoDetailResultAuthor {
    private int activedegree;
    private boolean allowbigfile;
    private String auditor;
    private String avatar;
    private String businesstag;
    private int businesstype;
    private String coverimage;
    private Date createtime;
    private int creditscore;
    private String email;
    private boolean enabled;
    private int fastestfirstid;
    private String fastestfirstname;
    private int forbiddenreason;
    private Date forbiddentime;
    private int forbiddentype;
    private String gca;
    private String gloryname;
    private int infolevel;
    private int inforecommendlevel;
    private Date inforecommendleveltime;
    private String introduction;
    private boolean isaloneplay;
    private boolean isautowrite;
    private boolean isgoodauthor;
    private int isimportant;
    private boolean isjoinadplan;
    private boolean isjointask;
    private boolean islocaltion;
    private boolean isshielded;
    private boolean issmallvideoauthor;
    private boolean iswhitelist;
    private Date joinadplandate;
    private Date jointaskdate;
    private Date jointime;
    private int localcityid;
    private String localcityname;
    private int localprovinceid;
    private String localprovincename;
    private String mobile;
    private String nickname;
    private String pageid;
    private Date recommendtime;
    private int recommendtype;
    private String relationname;
    private ChejiahaoDetailResultAuthorStatistics statistics;
    private int status;
    private String statusdes;
    private Date subscribetime;
    private ChejiahaoDetailResultAuthorUserextend2 userextend2;
    private List<ChejiahaoDetailResultAuthorUserglory> userglory;
    private int userid;
    private int userlevel;
    private String usertitle;
    private Date vaudittime;
    private String vcrid;
    private String vdescription;
    private String vremark;
    private String vtype;
    private String vtypedes;
    private String wechatid;

    public void setActivedegree(int activedegree) {
        this.activedegree = activedegree;
    }

    public int getActivedegree() {
        return activedegree;
    }

    public void setAllowbigfile(boolean allowbigfile) {
        this.allowbigfile = allowbigfile;
    }

    public boolean getAllowbigfile() {
        return allowbigfile;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setBusinesstag(String businesstag) {
        this.businesstag = businesstag;
    }

    public String getBusinesstag() {
        return businesstag;
    }

    public void setBusinesstype(int businesstype) {
        this.businesstype = businesstype;
    }

    public int getBusinesstype() {
        return businesstype;
    }

    public void setCoverimage(String coverimage) {
        this.coverimage = coverimage;
    }

    public String getCoverimage() {
        return coverimage;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreditscore(int creditscore) {
        this.creditscore = creditscore;
    }

    public int getCreditscore() {
        return creditscore;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean getEnabled() {
        return enabled;
    }

    public void setFastestfirstid(int fastestfirstid) {
        this.fastestfirstid = fastestfirstid;
    }

    public int getFastestfirstid() {
        return fastestfirstid;
    }

    public void setFastestfirstname(String fastestfirstname) {
        this.fastestfirstname = fastestfirstname;
    }

    public String getFastestfirstname() {
        return fastestfirstname;
    }

    public void setForbiddenreason(int forbiddenreason) {
        this.forbiddenreason = forbiddenreason;
    }

    public int getForbiddenreason() {
        return forbiddenreason;
    }

    public void setForbiddentime(Date forbiddentime) {
        this.forbiddentime = forbiddentime;
    }

    public Date getForbiddentime() {
        return forbiddentime;
    }

    public void setForbiddentype(int forbiddentype) {
        this.forbiddentype = forbiddentype;
    }

    public int getForbiddentype() {
        return forbiddentype;
    }

    public void setGca(String gca) {
        this.gca = gca;
    }

    public String getGca() {
        return gca;
    }

    public void setGloryname(String gloryname) {
        this.gloryname = gloryname;
    }

    public String getGloryname() {
        return gloryname;
    }

    public void setInfolevel(int infolevel) {
        this.infolevel = infolevel;
    }

    public int getInfolevel() {
        return infolevel;
    }

    public void setInforecommendlevel(int inforecommendlevel) {
        this.inforecommendlevel = inforecommendlevel;
    }

    public int getInforecommendlevel() {
        return inforecommendlevel;
    }

    public void setInforecommendleveltime(Date inforecommendleveltime) {
        this.inforecommendleveltime = inforecommendleveltime;
    }

    public Date getInforecommendleveltime() {
        return inforecommendleveltime;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIsaloneplay(boolean isaloneplay) {
        this.isaloneplay = isaloneplay;
    }

    public boolean getIsaloneplay() {
        return isaloneplay;
    }

    public void setIsautowrite(boolean isautowrite) {
        this.isautowrite = isautowrite;
    }

    public boolean getIsautowrite() {
        return isautowrite;
    }

    public void setIsgoodauthor(boolean isgoodauthor) {
        this.isgoodauthor = isgoodauthor;
    }

    public boolean getIsgoodauthor() {
        return isgoodauthor;
    }

    public void setIsimportant(int isimportant) {
        this.isimportant = isimportant;
    }

    public int getIsimportant() {
        return isimportant;
    }

    public void setIsjoinadplan(boolean isjoinadplan) {
        this.isjoinadplan = isjoinadplan;
    }

    public boolean getIsjoinadplan() {
        return isjoinadplan;
    }

    public void setIsjointask(boolean isjointask) {
        this.isjointask = isjointask;
    }

    public boolean getIsjointask() {
        return isjointask;
    }

    public void setIslocaltion(boolean islocaltion) {
        this.islocaltion = islocaltion;
    }

    public boolean getIslocaltion() {
        return islocaltion;
    }

    public void setIsshielded(boolean isshielded) {
        this.isshielded = isshielded;
    }

    public boolean getIsshielded() {
        return isshielded;
    }

    public void setIssmallvideoauthor(boolean issmallvideoauthor) {
        this.issmallvideoauthor = issmallvideoauthor;
    }

    public boolean getIssmallvideoauthor() {
        return issmallvideoauthor;
    }

    public void setIswhitelist(boolean iswhitelist) {
        this.iswhitelist = iswhitelist;
    }

    public boolean getIswhitelist() {
        return iswhitelist;
    }

    public void setJoinadplandate(Date joinadplandate) {
        this.joinadplandate = joinadplandate;
    }

    public Date getJoinadplandate() {
        return joinadplandate;
    }

    public void setJointaskdate(Date jointaskdate) {
        this.jointaskdate = jointaskdate;
    }

    public Date getJointaskdate() {
        return jointaskdate;
    }

    public void setJointime(Date jointime) {
        this.jointime = jointime;
    }

    public Date getJointime() {
        return jointime;
    }

    public void setLocalcityid(int localcityid) {
        this.localcityid = localcityid;
    }

    public int getLocalcityid() {
        return localcityid;
    }

    public void setLocalcityname(String localcityname) {
        this.localcityname = localcityname;
    }

    public String getLocalcityname() {
        return localcityname;
    }

    public void setLocalprovinceid(int localprovinceid) {
        this.localprovinceid = localprovinceid;
    }

    public int getLocalprovinceid() {
        return localprovinceid;
    }

    public void setLocalprovincename(String localprovincename) {
        this.localprovincename = localprovincename;
    }

    public String getLocalprovincename() {
        return localprovincename;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setRecommendtime(Date recommendtime) {
        this.recommendtime = recommendtime;
    }

    public Date getRecommendtime() {
        return recommendtime;
    }

    public void setRecommendtype(int recommendtype) {
        this.recommendtype = recommendtype;
    }

    public int getRecommendtype() {
        return recommendtype;
    }

    public void setRelationname(String relationname) {
        this.relationname = relationname;
    }

    public String getRelationname() {
        return relationname;
    }

    public void setStatistics(ChejiahaoDetailResultAuthorStatistics statistics) {
        this.statistics = statistics;
    }

    public ChejiahaoDetailResultAuthorStatistics getStatistics() {
        return statistics;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setStatusdes(String statusdes) {
        this.statusdes = statusdes;
    }

    public String getStatusdes() {
        return statusdes;
    }

    public void setSubscribetime(Date subscribetime) {
        this.subscribetime = subscribetime;
    }

    public Date getSubscribetime() {
        return subscribetime;
    }

    public void setUserextend2(ChejiahaoDetailResultAuthorUserextend2 userextend2) {
        this.userextend2 = userextend2;
    }

    public ChejiahaoDetailResultAuthorUserextend2 getUserextend2() {
        return userextend2;
    }

    public void setUserglory(List<ChejiahaoDetailResultAuthorUserglory> userglory) {
        this.userglory = userglory;
    }

    public List<ChejiahaoDetailResultAuthorUserglory> getUserglory() {
        return userglory;
    }

    public void setUserid(int userid) {
        this.userid = userid;
    }

    public int getUserid() {
        return userid;
    }

    public void setUserlevel(int userlevel) {
        this.userlevel = userlevel;
    }

    public int getUserlevel() {
        return userlevel;
    }

    public void setUsertitle(String usertitle) {
        this.usertitle = usertitle;
    }

    public String getUsertitle() {
        return usertitle;
    }

    public void setVaudittime(Date vaudittime) {
        this.vaudittime = vaudittime;
    }

    public Date getVaudittime() {
        return vaudittime;
    }

    public void setVcrid(String vcrid) {
        this.vcrid = vcrid;
    }

    public String getVcrid() {
        return vcrid;
    }

    public void setVdescription(String vdescription) {
        this.vdescription = vdescription;
    }

    public String getVdescription() {
        return vdescription;
    }

    public void setVremark(String vremark) {
        this.vremark = vremark;
    }

    public String getVremark() {
        return vremark;
    }

    public void setVtype(String vtype) {
        this.vtype = vtype;
    }

    public String getVtype() {
        return vtype;
    }

    public void setVtypedes(String vtypedes) {
        this.vtypedes = vtypedes;
    }

    public String getVtypedes() {
        return vtypedes;
    }

    public void setWechatid(String wechatid) {
        this.wechatid = wechatid;
    }

    public String getWechatid() {
        return wechatid;
    }

}