package com.autohome.applet.model.dto.carlibrary.carapi.out;

import com.autohome.applet.model.dto.carlibrary.carapi.CarApiSpecPicDetail;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class CarApiSpecDetailOut {
    private String specId;
    private String specName;
//    private int seriesId;
//    private String seriesName;
//    private int brandId;
//    private String brandName;
//    private int fctId;
//    private String fctName;
//    private int levelId;
    private String levelName; // 车型级别
//    private int specPiCount;
//    private String specLogo;
//    private int specMinPrice;
//    private int specMaxPrice;
//    private int specEngineId;
//    private String specEngineName;
    private int specStructureDoor; // 车门数
    private String specStructureSeat; // 座位数
    private String specStructureTypeName; // 车身结构
    private String specTransmission; // 档位
//    private int specState;
    private int specOilOfficial; // 综合油耗(L/100km)
    private int specLength; // 车辆长宽高（mm）
    private int specWidth;
    private int specHeight;
    private int specWeight;
    private String specDrivingModeName; // 驱动方式
    private int specFlowModeId;
    private String specFlowModeName;
    private double specDisplacement; // 排量
    private String specDispWithFlowMode; // 排量L or T
    private int specEnginePower; // 最大马力（Ps）
//    private int specParamIsShow;
//    private int specIsPreferential;
//    private int specIsTaxRelief;
//    private int specIsTaxExemption;
//    private String specQuality;
//    private String specIsImport;
//    private boolean specIsBooked;
//    private String dynamicPrice;
    private int oilBoxVolume; // 油箱容积(L)
    private int fuelType; // 能源类型 ? 1.燃油车  2.新能源
    private String fastChargeTime; // 快充时间（小时）
    private String slowChargeTime; // 慢充时间（小时）
//    private String fastChargePercent;
//    private String batteryCapacity;
    private String mile; // 纯电续航里程(km)
//    private int fuelTypeDetail;
//    private String fuelTypeName;
//    private String greenStandards;
//    private String engineTorque;
    private String engingKw; // 发动机最大功率（kW）
//    private String qrCode;
//    private String priceDescription;
//    private String electricMotorGrossPower;
//    private String electricMotorGrossTorque;
    private String oilLabel; // 燃油标号

    private List<PicItemOut> picGroups; // 附加-单独接口：车型图片

    @Data
    public static class PicItemOut {
        private String typeName;
        private List<ItemOut> picItems;

        public static PicItemOut by(CarApiSpecPicDetail.PicItem picItem) {
            PicItemOut picItemOut = new PicItemOut();
            picItemOut.setTypeName(picItem.getTypeName());
            picItemOut.setPicItems(picItem.getPicItems().stream().map(ItemOut::by)
                    .collect(Collectors.toList()));
            return picItemOut;
        }
    }

    @Data
    public static class ItemOut {
        private String itemName;
        private String picPath;

        public static ItemOut by(CarApiSpecPicDetail.Item item) {
            ItemOut itemOut = new ItemOut();
            itemOut.setItemName(item.getItemName());
            itemOut.setPicPath(item.getPicPath());
            return itemOut;
        }
    }
}
