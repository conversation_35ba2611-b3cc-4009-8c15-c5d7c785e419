package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by hanshanfeng on 2018/8/27.
 */
public class CarCalculator {
    @JsonProperty("carPrice")
    private int carprice;
    private int specistaxexemption;
    @JsonProperty("licenseTaxCustom")
    private int licensetaxcustom;
    @JsonProperty("usageTaxCustom")
    private List<Usagetaxcustom> usagetaxcustom;
    @JsonProperty("seatCount")
    private int seatcount;
    @JsonProperty("trafficInsurance")
    private List<Trafficinsurance> trafficinsurance;
    @JsonProperty("thirdInsurance")
    private List<Thirdinsurance> thirdinsurance;
    @JsonProperty("passengerInsureCustom")
    private int passengerinsurecustom;
    @JsonProperty("carBodyInsurance")
    private List<Carbodyinsurance> carbodyinsurance;
    private int excise;
    @JsonProperty("purchaseTax")
    private int purchasetax;
    @JsonProperty("stolenInsurance")
    private int stoleninsurance;
    @JsonProperty("damageInsurance")
    private int damageinsurance;
    @JsonProperty("glassInsurance")
    private List<Glassinsurance> glassinsurance;
    @JsonProperty("combustInsurance")
    private int combustinsurance;
    @JsonProperty("noDeductibleInsurance")
    private List<Nodeductibleinsurance> nodeductibleinsurance;
    @JsonProperty("noLiabilityInsurance")
    private List<Noliabilityinsurance> noliabilityinsurance;

    public void setCarprice(int carprice) {
        this.carprice = carprice;
    }

    public int getCarprice() {
        return carprice;
    }

    public void setSpecistaxexemption(int specistaxexemption) {
        this.specistaxexemption = specistaxexemption;
    }

    public int getSpecistaxexemption() {
        return specistaxexemption;
    }

    public void setLicensetaxcustom(int licensetaxcustom) {
        this.licensetaxcustom = licensetaxcustom;
    }

    public int getLicensetaxcustom() {
        return licensetaxcustom;
    }

    public void setUsagetaxcustom(List<Usagetaxcustom> usagetaxcustom) {
        this.usagetaxcustom = usagetaxcustom;
    }

    public List<Usagetaxcustom> getUsagetaxcustom() {
        return usagetaxcustom;
    }

    public void setSeatcount(int seatcount) {
        this.seatcount = seatcount;
    }

    public int getSeatcount() {
        return seatcount;
    }

    public void setTrafficinsurance(List<Trafficinsurance> trafficinsurance) {
        this.trafficinsurance = trafficinsurance;
    }

    public List<Trafficinsurance> getTrafficinsurance() {
        return trafficinsurance;
    }

    public void setThirdinsurance(List<Thirdinsurance> thirdinsurance) {
        this.thirdinsurance = thirdinsurance;
    }

    public List<Thirdinsurance> getThirdinsurance() {
        return thirdinsurance;
    }

    public void setPassengerinsurecustom(int passengerinsurecustom) {
        this.passengerinsurecustom = passengerinsurecustom;
    }

    public int getPassengerinsurecustom() {
        return passengerinsurecustom;
    }

    public void setCarbodyinsurance(List<Carbodyinsurance> carbodyinsurance) {
        this.carbodyinsurance = carbodyinsurance;
    }

    public List<Carbodyinsurance> getCarbodyinsurance() {
        return carbodyinsurance;
    }

    public void setExcise(int excise) {
        this.excise = excise;
    }

    public int getExcise() {
        return excise;
    }

    public void setPurchasetax(int purchasetax) {
        this.purchasetax = purchasetax;
    }

    public int getPurchasetax() {
        return purchasetax;
    }

    public void setStoleninsurance(int stoleninsurance) {
        this.stoleninsurance = stoleninsurance;
    }

    public int getStoleninsurance() {
        return stoleninsurance;
    }

    public void setDamageinsurance(int damageinsurance) {
        this.damageinsurance = damageinsurance;
    }

    public int getDamageinsurance() {
        return damageinsurance;
    }

    public void setGlassinsurance(List<Glassinsurance> glassinsurance) {
        this.glassinsurance = glassinsurance;
    }

    public List<Glassinsurance> getGlassinsurance() {
        return glassinsurance;
    }

    public void setCombustinsurance(int combustinsurance) {
        this.combustinsurance = combustinsurance;
    }

    public int getCombustinsurance() {
        return combustinsurance;
    }

    public void setNodeductibleinsurance(List<Nodeductibleinsurance> nodeductibleinsurance) {
        this.nodeductibleinsurance = nodeductibleinsurance;
    }

    public List<Nodeductibleinsurance> getNodeductibleinsurance() {
        return nodeductibleinsurance;
    }

    public void setNoliabilityinsurance(List<Noliabilityinsurance> noliabilityinsurance) {
        this.noliabilityinsurance = noliabilityinsurance;
    }

    public List<Noliabilityinsurance> getNoliabilityinsurance() {
        return noliabilityinsurance;
    }
}
