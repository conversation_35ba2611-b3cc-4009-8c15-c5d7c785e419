package com.autohome.applet.model.dto.dealer;

import lombok.Data;

import java.util.List;

/**
 * @description: 弹窗程序化包装接口DTO
 * @author: WangBoWen
 * @date: 2023-11-22
 **/
@Data
public class SmartAreaButtonResultDTO {

    private String tag;

    private String group;

    private Integer specId;

    private List<Button> buttonList;


    @Data
    public static class Button{

        private Integer btnType;

        private String mainText;

        private String subText;

        private String cornerText;

        private Integer windowType;

        private String telNO;

        private String imSchema;

        private String sourceId;

        private Integer specId;

        private String ext;

        private String url;

        private Integer isSupply;
    }


}
