package com.autohome.applet.model.dto.newenergy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

/**
 * @description: 新能源车主真实续航口碑接口数据
 * @author: WangBoWen
 * @date: 2024-01-02
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CarOwnerRealEnduranceOfKoubeiDTO {

    private List<SameCar> newSameCarList;

    private TreeMap<String, SeasonInfo> seasonData;

    private List<SpecInfo> specInfoList;

    private Integer currentSpecId;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SameCar {

        private String brandName;

        private Integer officialDriveRange;

        private List<SameCarSeason> sameCarSeasonList;

        private Integer seriesId;

        private String seriesName;

        private Integer specId;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SameCarSeason {

        private Double driveRate;

        private Integer factChaSpeed;

        private Integer factDriveRange;

        private Double  factEnergy100;

        private String season;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SeasonInfo {

        private Double driveRate;

        private Double factChaSpeed;

        private Integer factDriveRange;

        private Double factDriveRangeDouble;

        private Double factEnergy100;

        private Integer fastChaMoney;

        private Integer slowChaMoney;

        private Integer volumeScore;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SpecInfo{

        private Integer factDriveRange;

        private Integer factEnergy100;

        private Integer maxprice;

        private Integer minprice;

        private Integer officialDriveRange;

        private Integer seriesId;

        private String seriesName;

        private String seriesPngLogo;

        private Integer specId;

        private String specName;
    }

    public List<StatisSpecInfo> getStatisSpecInfo(){

        List<StatisSpecInfo> list = new ArrayList<>();

        for (SpecInfo specInfo : this.specInfoList) {

            StatisSpecInfo statisSpecInfo = new StatisSpecInfo();

            statisSpecInfo.setSeriesId(specInfo.getSeriesId());
            statisSpecInfo.setSeriesName(specInfo.getSeriesName());
            statisSpecInfo.setSeriesPngLogo(specInfo.getSeriesPngLogo());
            statisSpecInfo.setMinprice(specInfo.getMinprice());
            statisSpecInfo.setMaxprice(specInfo.getMaxprice());
            statisSpecInfo.setOfficialDriveRange(specInfo.getOfficialDriveRange());
            statisSpecInfo.setFactDriveRange(specInfo.getFactDriveRange());
            statisSpecInfo.setSpecId(specInfo.getSpecId());

            list.add(statisSpecInfo);
        }

        return list;
    }


}
