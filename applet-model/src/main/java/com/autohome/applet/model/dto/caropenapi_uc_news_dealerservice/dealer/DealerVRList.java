package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarSettings;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

public class DealerVRList {

    @JsonProperty("seriesImg")
    private String seriesimg;
    @JsonProperty("seriesVRUrl")
    private String seriesvrurl;
    @JsonProperty("seriesName")
    private String seriesname;
    @JsonProperty("seriesId")
    private int seriesid;
    private String shareimg;

    public String getShareimg() {
        if (StringUtil.isNotNullAndWhiteSpace(seriesimg)) {
            return CarSettings.ConvertImgNew(getSeriesimg(), "400x300_");
        }
        return shareimg;
    }

    public void setShareimg(String shareimg) {
        this.shareimg = shareimg;
    }

    public void setSeriesimg(String seriesimg) {
        this.seriesimg = seriesimg;
    }

    public String getSeriesimg() {
        if (StringUtil.isNotNullAndWhiteSpace(seriesimg)) {
            seriesimg = CarSettings.ConvertImgNew(seriesimg, "524x0_0_");//seriesimg.replace("/autohomecar","/524x0_0_autohomecar");//CarSettings.getInstance().GetFullImagePathByPrefix(seriesimg, "524x0_0_", 0);
        }
        if (seriesimg.indexOf("http://") >= 0) {
            return seriesimg.replace("http://", "https://");
        }
        if (seriesimg.indexOf("https://") >= 0) {
            return seriesimg;
        }
        seriesimg = "https:" + seriesimg;
        return seriesimg;

    }

    public void setSeriesvrurl(String seriesvrurl) {
        this.seriesvrurl = seriesvrurl;
    }

    public String getSeriesvrurl() {
        if (StringUtil.isBlank(seriesvrurl)) {
            return seriesvrurl;
        }
        if (seriesvrurl.indexOf("?") >= 0) {
            return seriesvrurl + "&pure=1&quickapp=0";
        }
        return seriesvrurl + "?pure=1&quickapp=0";
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

}
