package com.autohome.applet.model.dto.honor;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AnalyzeCar {
    private String data;
    @JsonProperty("traceid")
    private String traceId;

    @JsonProperty("content_data")
    private NameValue contentData;

    @JsonProperty("image_type")
    private NameValue imageType;

    private int testSeriesId;
    private String testSeriesName;
    private int testBrandId;
    private String testBrandName;

    public static class NameValue{
        private String name;
        private List<Real> values;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Real> getValues() {
            return values;
        }

        public void setValues(List<Real> values) {
            this.values = values;
        }
    }

    public static class Real {
        private String real;

        public String getReal() {
            return real;
        }

        public void setReal(String real) {
            this.real = real;
        }
    }

    public String getData() {
        if (contentData != null) {
            List<Real> values = contentData.getValues();
            if (!CollectionUtils.isEmpty(values)) {
                Real real = values.get(0);
                if (real != null) {
                    return real.getReal();
                }
            }
        }
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public NameValue getContentData() {
        return contentData;
    }

    public void setContentData(NameValue contentData) {
        this.contentData = contentData;
    }

    public NameValue getImageType() {
        return imageType;
    }

    public void setImageType(NameValue imageType) {
        this.imageType = imageType;
    }

    public int getTestSeriesId() {
        return testSeriesId;
    }

    public void setTestSeriesId(int testSeriesId) {
        this.testSeriesId = testSeriesId;
    }

    public String getTestSeriesName() {
        return testSeriesName;
    }

    public void setTestSeriesName(String testSeriesName) {
        this.testSeriesName = testSeriesName;
    }

    public int getTestBrandId() {
        return testBrandId;
    }

    public void setTestBrandId(int testBrandId) {
        this.testBrandId = testBrandId;
    }

    public String getTestBrandName() {
        return testBrandName;
    }

    public void setTestBrandName(String testBrandName) {
        this.testBrandName = testBrandName;
    }

    public static void main(String[] args) {
        String imagePath = "/Users/<USER>/Downloads/WechatIMG615262.jpeg"; // 替换成实际的图片路径

        try {
            File imageFile = new File(imagePath);
            byte[] imageBytes = readImageBytes(imageFile);
            String base64String = encodeToBase64(imageBytes);


            String body = "{\n" +
                    "\t\"endpoint\": {\n" +
                    "\t\t\"device\": {\n" +
                    "\t\t\t\"base\": {\n" +
                    "\t\t\t\t\"deviceId\": \"string\",\n" +
                    "\t\t\t\t\"deviceType\": \"phone\"\n" +
                    "\t\t\t},\n" +
                    "\t\t\t\"presentation\": {\n" +
                    "\t\t\t\t\"screenOrientation\": \"horizontal\",\n" +
                    "\t\t\t\t\"net\": \"wifi\",\n" +
                    "\t\t\t\t\"location\": {\n" +
                    "\t\t\t\t\t\"longitude\": \"string\",\n" +
                    "\t\t\t\t\t\"latitude\": \"string\",\n" +
                    "\t\t\t\t\t\"locationSystem\": \"WGS84\"\n" +
                    "\t\t\t\t}\n" +
                    "\t\t\t}\n" +
                    "\t\t},\n" +
                    "                \"auth\": {\n" +
                    "                     \"apiAccessToken\": \"string\"\n" +
                    "                },\n" +
                    "               \"locale\": \"zh-CN\",\n" +
                    "\t\t\"countryCode\": \"zh\",\n" +
                    "\t\t\"timeZone\": \"+0800\",\n" +
                    "\t\t\"localTime\": \"string\"\n" +
                    "\t},\n" +
                    "\t\"inquire\": {\n" +
                    "\t\t\"inquireId\": \"string\",\n" +
                    "\t\t\"intent\": {\n" +
                    "\t\t\t\"serviceId\": \"string\",\n" +
                    "\t\t\t\"intentName\": \"string\",\n" +
                    "\t\t\t\"status\": \"online\",\n" +
                    "\t\t\t\"slots\": {\n" +
                    "                \"imgBase64\":\" "+base64String+"\",\n" +
                    "                \"deviceId\": \"deviceId123\"\n" +
                    "            } \n" +
                    "\t\t}\n" +
                    "\t},\n" +
                    "         \"session\": {\n" +
                    "              \"attributes\": \"string\",\n" +
                    "              \"selectedItem\": \"string\"\n" +
                    "       }\n" +
                    "}";
            System.out.println(body);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static byte[] readImageBytes(File file) throws IOException {
        byte[] buffer = new byte[(int) file.length()];

        try (FileInputStream fis = new FileInputStream(file)) {
            fis.read(buffer);
        }

        return buffer;
    }

    private static String encodeToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }
}
