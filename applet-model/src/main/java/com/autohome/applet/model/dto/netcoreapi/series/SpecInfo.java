package com.autohome.applet.model.dto.netcoreapi.series;

public class SpecInfo {

    private int booked;
    private String brandfirstletter;
    private int brandid;
    private String brandlogo;
    private String brandname;
    private String brandofficialurl;
    private String displacement;
    private String fctfirstletter;
    private int fctid;
    private String fctlogo;
    private String fctname;
    private String fctofficialurl;
    private int fueltype;
    private int id;
    private int levelid;
    private String levelname;
    private String logo;
    private long maxprice;
    private int minprice;
    private String name;
    private int paramisshow;
    
    private String pricedescription;
    private int ranliaoxingshi;
    private String seriesfirstletter;
    private int seriesid;
    private String serieslogo;
    private String seriesname;
    private String seriesofficialurl;
    private String specquality;
    private int state;
    private String timemarket;
    private int yearid;
    private String yearname;

    public void setBooked(int booked) {
        this.booked = booked;
    }

    public int getBooked() {
        return booked;
    }

    public void setBrandfirstletter(String brandfirstletter) {
        this.brandfirstletter = brandfirstletter;
    }

    public String getBrandfirstletter() {
        return brandfirstletter;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandlogo(String brandlogo) {
        this.brandlogo = brandlogo;
    }

    public String getBrandlogo() {
        return brandlogo;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandofficialurl(String brandofficialurl) {
        this.brandofficialurl = brandofficialurl;
    }

    public String getBrandofficialurl() {
        return brandofficialurl;
    }

    public void setDisplacement(String displacement) {
        this.displacement = displacement;
    }

    public String getDisplacement() {
        return displacement;
    }

    public void setFctfirstletter(String fctfirstletter) {
        this.fctfirstletter = fctfirstletter;
    }

    public String getFctfirstletter() {
        return fctfirstletter;
    }

    public void setFctid(int fctid) {
        this.fctid = fctid;
    }

    public int getFctid() {
        return fctid;
    }

    public void setFctlogo(String fctlogo) {
        this.fctlogo = fctlogo;
    }

    public String getFctlogo() {
        return fctlogo;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public String getFctname() {
        return fctname;
    }

    public void setFctofficialurl(String fctofficialurl) {
        this.fctofficialurl = fctofficialurl;
    }

    public String getFctofficialurl() {
        return fctofficialurl;
    }

    public void setFueltype(int fueltype) {
        this.fueltype = fueltype;
    }

    public int getFueltype() {
        return fueltype;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setLevelid(int levelid) {
        this.levelid = levelid;
    }

    public int getLevelid() {
        return levelid;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public void setMaxprice(long maxprice) {
        this.maxprice = maxprice;
    }

    public long getMaxprice() {
        return maxprice;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setParamisshow(int paramisshow) {
        this.paramisshow = paramisshow;
    }

    public int getParamisshow() {
        return paramisshow;
    }

    public void setPricedescription(String pricedescription) {
        this.pricedescription = pricedescription;
    }

    public String getPricedescription() {
        return pricedescription;
    }

    public void setRanliaoxingshi(int ranliaoxingshi) {
        this.ranliaoxingshi = ranliaoxingshi;
    }

    public int getRanliaoxingshi() {
        return ranliaoxingshi;
    }

    public void setSeriesfirstletter(String seriesfirstletter) {
        this.seriesfirstletter = seriesfirstletter;
    }

    public String getSeriesfirstletter() {
        return seriesfirstletter;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSerieslogo(String serieslogo) {
        this.serieslogo = serieslogo;
    }

    public String getSerieslogo() {
        return serieslogo;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesofficialurl(String seriesofficialurl) {
        this.seriesofficialurl = seriesofficialurl;
    }

    public String getSeriesofficialurl() {
        return seriesofficialurl;
    }

    public void setSpecquality(String specquality) {
        this.specquality = specquality;
    }

    public String getSpecquality() {
        return specquality;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setTimemarket(String timemarket) {
        this.timemarket = timemarket;
    }

    public String getTimemarket() {
        return timemarket;
    }

    public void setYearid(int yearid) {
        this.yearid = yearid;
    }

    public int getYearid() {
        return yearid;
    }

    public void setYearname(String yearname) {
        this.yearname = yearname;
    }

    public String getYearname() {
        return yearname;
    }

}