package com.autohome.applet.model.dto;

import lombok.Data;

@Data
public class OemMsg<T> {
    private int returnCode = 0;
    private String message;
    private T result;

    public OemMsg(T data) {
        this(0, null, data);
    }

    public OemMsg(int code, String msg) {
        this(code, msg, null);
    }

    public OemMsg(int returnCode, String message, T result) {
        this.returnCode = returnCode;
        this.message = message;
        this.result = result;
    }

    public OemMsg() {
        this(0,null,null);
    }

    public int getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(int returnCode) {
        this.returnCode = returnCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public static <T> OemMsg<T> buildSuccessResult(T data) {
        OemMsg<T> rt = new OemMsg<>();
        rt.setReturnCode(0);
        rt.setResult(data);
        rt.setMessage("success");
        return rt;
    }

    public static <T> OemMsg<T> buildErrorResult(int returnCode, String message) {
        OemMsg<T> rt = new OemMsg<>();
        rt.setReturnCode(returnCode);
        rt.setMessage(message);
        return rt;
    }

}

