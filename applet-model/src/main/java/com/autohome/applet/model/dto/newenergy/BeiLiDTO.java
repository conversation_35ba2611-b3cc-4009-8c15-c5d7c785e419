package com.autohome.applet.model.dto.newenergy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 北里DTO
 * @author: WangBoWen
 * @date: 2024-01-11
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BeiLiDTO {

    private List<SpecInfo> specInfoList;

    private Map<String,?> seasonData;

    private List<?> newSameCarList;

    private List<?> temperatureDriveRangeRate;

    private List<?> totalSlowChaMoneyList;

    private List<?> totalFastChaMoneyList;

    private String winterLabScheme;

    private Integer currentSpecId;

    @Data
    public static class SpecInfo{

        private int officialDriveRange;

        private int factDriveRange;

        private double driveRate;

        private int factEnergy100;

        private double factChaSpeed;

        private int seriesId;

        private String seriesName;

        private int specId;

        private String specName;

        private Integer minprice;

        private Integer maxprice;

        private String seriesPngLogo;

        private String specLogo;

    }

    public List<StatisSpecInfo> getStatisSpecInfo(){

        List<StatisSpecInfo> list = new ArrayList<>();

        for (SpecInfo specInfo : this.specInfoList) {

            StatisSpecInfo statisSpecInfo = new StatisSpecInfo();
            statisSpecInfo.setSeriesId(specInfo.getSeriesId());
            statisSpecInfo.setSeriesName(specInfo.getSeriesName());
            statisSpecInfo.setSeriesPngLogo(specInfo.getSeriesPngLogo());
            statisSpecInfo.setMinprice(specInfo.getMinprice());
            statisSpecInfo.setMaxprice(specInfo.getMaxprice());
            statisSpecInfo.setOfficialDriveRange(specInfo.getOfficialDriveRange());
            statisSpecInfo.setFactDriveRange(specInfo.getFactDriveRange());
            statisSpecInfo.setSpecId(specInfo.getSpecId());

            list.add(statisSpecInfo);
        }

        return list;
    }


}
