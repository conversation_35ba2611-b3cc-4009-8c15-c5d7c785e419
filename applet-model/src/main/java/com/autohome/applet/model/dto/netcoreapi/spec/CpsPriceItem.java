package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class CpsPriceItem {
    @JsonProperty("specid")
    private int specId;
    private String value;
    private String businessUrl; // cpsType=1
    private RecActivityInfo.ExtendInfo extendInfo; // cpsType=1
    private int cpsType;

    private int skuId;
    private String skuDetailUrl; // cpsType=2

    public CpsPriceItem() {
    }

    public CpsPriceItem(int specId, String value, String businessUrl, RecActivityInfo.ExtendInfo extendInfo, int cpsType) {
        this.specId = specId;
        this.value = value;
        this.businessUrl = businessUrl;
        this.extendInfo = extendInfo;
        this.cpsType = cpsType;
    }

    public CpsPriceItem(int specId, String value, int skuId, String skuDetailUrl, int cpsType) {
        this.specId = specId;
        this.value = value;
        this.skuId = skuId;
        this.skuDetailUrl = skuDetailUrl;
        this.cpsType = cpsType;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getBusinessUrl() {
        return businessUrl;
    }

    public void setBusinessUrl(String businessUrl) {
        this.businessUrl = businessUrl;
    }

    public RecActivityInfo.ExtendInfo getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(RecActivityInfo.ExtendInfo extendInfo) {
        this.extendInfo = extendInfo;
    }

    public int getCpsType() {
        return cpsType;
    }

    public void setCpsType(int cpsType) {
        this.cpsType = cpsType;
    }

    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    public String getSkuDetailUrl() {
        return skuDetailUrl;
    }

    public void setSkuDetailUrl(String skuDetailUrl) {
        this.skuDetailUrl = skuDetailUrl;
    }
}
