package com.autohome.applet.model.dto.serachcars;

import lombok.Data;

import java.util.HashMap;
import java.util.List;

@Data
public class SerachCarsInfo {

    private int returncode;

    private String message;

    public Result result;


    @Data
    public static class Result {
        public int seriescount;

        public int speccount;
        public List<SubSeries> seriesGroupList;
    }

    @Data
    public static class SubSeries {
        public int brandid;
        public String brandName;
        public int fctId;
        public String fcName;
        public int seriesId;
        public String seriesName;
        public String seriesImg;
        public int levelid;
        public String levelName;
        public int seriesFctMinPrice;
        public int seriesFctMaxPrice;
        public int seriesSaleCount;

        public int attNum = 0;

        public  int seriesState;

        public  int seriesIsTaxRelief;

        public  int maxPriceOff;

        public  int maxPriceOffPercent;

        public  int clhSeriesSaleCount;

        public List<HashMap<String, Object>> specitems ;
    }
}
