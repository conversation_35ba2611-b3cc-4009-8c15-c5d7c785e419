package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/7.
 */
public class ChejiahaoArticle {
    private int infoid;

    private int authorid;

    private int infotype;

    private String title;

    private String title2;

    private String title3;

    private String image;

    private List<String> images;

    private boolean showimagelist;

    private String description;

    private int ident;

    private boolean ispublish;

    private int status;

    private String publishtime;

    private boolean istop;

    private boolean recycled;

    private boolean showbigimg;

    private Author author;

    private String article;

    private String audio;

    @JsonIgnore
    private String textimage;

    private Video video;

    private String live;

    private List<Tags> tags;

    private Infostatistics infostatistics;

    @JsonIgnore
    private List<String> relationcar;

    private List<String> carmonads;

    public void setInfoid(int infoid) {
        this.infoid = infoid;
    }

    public int getInfoid() {
        return infoid;
    }

    public void setAuthorid(int authorid) {
        this.authorid = authorid;
    }

    public int getAuthorid() {
        return authorid;
    }

    public void setInfotype(int infotype) {
        this.infotype = infotype;
    }

    public int getInfotype() {
        return infotype;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle2(String title2) {
        this.title2 = title2;
    }

    public String getTitle2() {
        return title2;
    }

    public void setTitle3(String title3) {
        this.title3 = title3;
    }

    public String getTitle3() {
        return title3;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getImage() {
        if (StringUtil.isNotNullAndWhiteSpace(image))
            return image.replace("http://", "//").replace("https://", "//");
        return image;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<String> getImages() {
        if (images != null && images.size() > 0) {
            images.forEach(s -> {
                if (StringUtil.isNotNullAndWhiteSpace(s))
                    s = s.replace("http://", "//").replace("https://", "//");
            });
        }
        return images;
    }

    public void setShowimagelist(boolean showimagelist) {
        this.showimagelist = showimagelist;
    }

    public boolean getShowimagelist() {
        return showimagelist;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setIdent(int ident) {
        this.ident = ident;
    }

    public int getIdent() {
        return ident;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        if (publishtime.indexOf("Date") >= 0) {
            return FormatDate.dateToStr(new Date(Long.parseLong(publishtime.replace("/Date(", "").replace(")/", ""))), "yyyy-MM-dd HH:mm:ss");
        } else if (publishtime.indexOf("T") >= 0) {
            return FormatDate.netToJavaTime(publishtime);
        }
        return publishtime;
    }

    public void setIstop(boolean istop) {
        this.istop = istop;
    }

    public boolean getIstop() {
        return istop;
    }

    public void setRecycled(boolean recycled) {
        this.recycled = recycled;
    }

    public boolean getRecycled() {
        return recycled;
    }

    public void setShowbigimg(boolean showbigimg) {
        this.showbigimg = showbigimg;
    }

    public boolean getShowbigimg() {
        return showbigimg;
    }

    public void setAuthor(Author author) {
        this.author = author;
    }

    public Author getAuthor() {
        return author;
    }

    public void setArticle(String article) {
        this.article = article;
    }

    public String getArticle() {
        return article;
    }

    public void setAudio(String audio) {
        this.audio = audio;
    }

    public String getAudio() {
        return audio;
    }

    public void setTextimage(String textimage) {
        this.textimage = textimage;
    }

    public String getTextimage() {
        return textimage;
    }

    public void setVideo(Video video) {
        this.video = video;
    }

    public Video getVideo() {
        return video;
    }

    public void setLive(String live) {
        this.live = live;
    }

    public String getLive() {
        return live;
    }

    public void setTags(List<Tags> tags) {
        this.tags = tags;
    }

    public List<Tags> getTags() {
        return tags;
    }

    public void setInfostatistics(Infostatistics infostatistics) {
        this.infostatistics = infostatistics;
    }

    public Infostatistics getInfostatistics() {
        return infostatistics;
    }

    public void setRelationcar(List<String> relationcar) {
        this.relationcar = relationcar;
    }

    public List<String> getRelationcar() {
        return relationcar;
    }

    public void setCarmonads(List<String> carmonads) {
        this.carmonads = carmonads;
    }

    public List<String> getCarmonads() {
        return carmonads;
    }

    public static class Statistics {

        private int userid;

        private int fanscount;

        private int followcount;

        private int articlecount;

        private int articlepv;

        private int articlereplycount;

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public int getUserid() {
            return userid;
        }

        public void setFanscount(int fanscount) {
            this.fanscount = fanscount;
        }

        public int getFanscount() {
            return fanscount;
        }

        public void setFollowcount(int followcount) {
            this.followcount = followcount;
        }

        public int getFollowcount() {
            return followcount;
        }

        public void setArticlecount(int articlecount) {
            this.articlecount = articlecount;
        }

        public int getArticlecount() {
            return articlecount;
        }

        public void setArticlepv(int articlepv) {
            this.articlepv = articlepv;
        }

        public int getArticlepv() {
            return articlepv;
        }

        public void setArticlereplycount(int articlereplycount) {
            this.articlereplycount = articlereplycount;
        }

        public int getArticlereplycount() {
            return articlereplycount;
        }

    }

    public static class Author {

        private int userid;

        private String nickname;

        private String avatar;

        private String introduction;

        private String coverimage;

        private boolean allowbigfile;

        private String jointime;

        private String createtime;

        private String vcrid;

        private int status;

        private String pageid;

        private int userlevel;

        private String vtype;

        private String vtypedes;

        private String vdescription;

        private String vremark;

        private boolean isshielded;

        private boolean isaloneplay;

        private boolean enabled;

        private boolean iswhitelist;

        private String usertitle;

        private Statistics statistics;

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public int getUserid() {
            return userid;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getNickname() {
            return nickname;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setIntroduction(String introduction) {
            this.introduction = introduction;
        }

        public String getIntroduction() {
            return introduction;
        }

        public void setCoverimage(String coverimage) {
            this.coverimage = coverimage;
        }

        public String getCoverimage() {
            return coverimage;
        }

        public void setAllowbigfile(boolean allowbigfile) {
            this.allowbigfile = allowbigfile;
        }

        public boolean getAllowbigfile() {
            return allowbigfile;
        }

        public void setVcrid(String vcrid) {
            this.vcrid = vcrid;
        }

        public String getVcrid() {
            return vcrid;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }

        public void setPageid(String pageid) {
            this.pageid = pageid;
        }

        public String getPageid() {
            return pageid;
        }

        public void setUserlevel(int userlevel) {
            this.userlevel = userlevel;
        }

        public int getUserlevel() {
            return userlevel;
        }

        public void setVtype(String vtype) {
            this.vtype = vtype;
        }

        public String getVtype() {
            return vtype;
        }

        public void setVtypedes(String vtypedes) {
            this.vtypedes = vtypedes;
        }

        public String getVtypedes() {
            return vtypedes;
        }

        public void setVdescription(String vdescription) {
            this.vdescription = vdescription;
        }

        public String getVdescription() {
            return vdescription;
        }

        public void setVremark(String vremark) {
            this.vremark = vremark;
        }

        public String getVremark() {
            return vremark;
        }

        public void setIsshielded(boolean isshielded) {
            this.isshielded = isshielded;
        }

        public boolean getIsshielded() {
            return isshielded;
        }

        public void setIsaloneplay(boolean isaloneplay) {
            this.isaloneplay = isaloneplay;
        }

        public boolean getIsaloneplay() {
            return isaloneplay;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean getEnabled() {
            return enabled;
        }

        public void setIswhitelist(boolean iswhitelist) {
            this.iswhitelist = iswhitelist;
        }

        public boolean getIswhitelist() {
            return iswhitelist;
        }

        public void setUsertitle(String usertitle) {
            this.usertitle = usertitle;
        }

        public String getUsertitle() {
            return usertitle;
        }

        public void setStatistics(Statistics statistics) {
            this.statistics = statistics;
        }

        public Statistics getStatistics() {
            return statistics;
        }

    }

    public static class Video {

        private int infoid;
        private String videoid;
        private int duration;
        private int status;
        private boolean isvr;

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setVideoid(String videoid) {
            this.videoid = videoid;
        }

        public String getVideoid() {
            return videoid;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public int getDuration() {
            return duration;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }

        public void setIsvr(boolean isvr) {
            this.isvr = isvr;
        }

        public boolean getIsvr() {
            return isvr;
        }

    }

    public static class Tags {

        private int infotagid;
        private int infoid;
        private int tagid;
        private String tagname;
        private boolean recycled;

        public void setInfotagid(int infotagid) {
            this.infotagid = infotagid;
        }

        public int getInfotagid() {
            return infotagid;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setTagid(int tagid) {
            this.tagid = tagid;
        }

        public int getTagid() {
            return tagid;
        }

        public void setTagname(String tagname) {
            this.tagname = tagname;
        }

        public String getTagname() {
            return tagname;
        }

        public void setRecycled(boolean recycled) {
            this.recycled = recycled;
        }

        public boolean getRecycled() {
            return recycled;
        }

    }

    public static class Infostatistics {

        private int infoid;
        private int pv;
        private int replycount;
        private int praisecount;
        private int sharecount;
        private int favoritecount;

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setPv(int pv) {
            this.pv = pv;
        }

        public int getPv() {
            return pv;
        }

        public void setReplycount(int replycount) {
            this.replycount = replycount;
        }

        public int getReplycount() {
            return replycount;
        }

        public void setPraisecount(int praisecount) {
            this.praisecount = praisecount;
        }

        public int getPraisecount() {
            return praisecount;
        }

        public void setSharecount(int sharecount) {
            this.sharecount = sharecount;
        }

        public int getSharecount() {
            return sharecount;
        }

        public void setFavoritecount(int favoritecount) {
            this.favoritecount = favoritecount;
        }

        public int getFavoritecount() {
            return favoritecount;
        }

    }
}
