package com.autohome.applet.model.dto.car;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpecBaseInfoBySpecListDTO {
    private int specid;
    private String specname;
    private String seriesname;
    private String pic;
    private String minprice;
    private String maxprice;

    public int getMinPriceInt(){
        try{
            return Integer.valueOf(minprice);
        }
        catch (Exception e){
            return 0;
        }
    }
}
