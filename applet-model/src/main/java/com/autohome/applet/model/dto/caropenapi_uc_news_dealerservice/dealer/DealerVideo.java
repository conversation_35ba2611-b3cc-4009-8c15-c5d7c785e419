package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/25.
 */
public class DealerVideo {
    private int height;
    @JsonProperty("likeCount")
    private int likecount;
    @JsonProperty("nickName")
    private String nickname;
    @JsonProperty("playCount")
    private int playcount;
    @JsonProperty("playUrl")
    private String playurl;
    private String portrait;
    @JsonProperty("publishTime")
    private String publishtime;
    @JsonProperty("shareUrl")
    private String shareurl;
    @JsonProperty("userId")
    private int userid;
    @JsonProperty("videoId")
    private int videoid;
    @JsonProperty("videoImg")
    private String videoimg;
    @JsonProperty("videoImg34")
    private String videoimg34;
    @JsonProperty("videoTitle")
    private String videotitle;
    private int width;

    public void setHeight(int height) {
        this.height = height;
    }

    public int getHeight() {
        return height;
    }

    public void setLikecount(int likecount) {
        this.likecount = likecount;
    }

    public int getLikecount() {
        return likecount;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setPlaycount(int playcount) {
        this.playcount = playcount;
    }

    public int getPlaycount() {
        return playcount;
    }

    public void setPlayurl(String playurl) {
        this.playurl = playurl;
    }

    public String getPlayurl() {
        return playurl;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getPortrait() {
        if (StringUtil.isNotNullAndWhiteSpace(portrait))
            return portrait.replace("http://", "https://");
        return portrait;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setShareurl(String shareurl) {
        this.shareurl = shareurl;
    }

    public String getShareurl() {
        return shareurl;
    }

    public void setUserid(int userid) {
        this.userid = userid;
    }

    public int getUserid() {
        return userid;
    }

    public void setVideoid(int videoid) {
        this.videoid = videoid;
    }

    public int getVideoid() {
        return videoid;
    }

    public void setVideoimg(String videoimg) {
        this.videoimg = videoimg;
    }

    public String getVideoimg() {
        return videoimg;
    }

    public void setVideoimg34(String videoimg34) {
        this.videoimg34 = videoimg34;
    }

    public String getVideoimg34() {
        return videoimg34;
    }

    public void setVideotitle(String videotitle) {
        this.videotitle = videotitle;
    }

    public String getVideotitle() {
        return videotitle;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getWidth() {
        return width;
    }


}
