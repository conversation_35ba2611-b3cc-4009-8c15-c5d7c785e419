package com.autohome.applet.model.dto.tt;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AntidirtResult {

    @JsonProperty("log_id")
    private String logId;
    private List<Item> data;

    @Data
    public static class Item {
        private int code;
        private String msg;
        @JsonProperty("data_id")
        private String dataId;
        @JsonProperty("task_id")
        private String taskId;
        private List<Predict> predicts;

    }

    @Data
    public static class Predict {
        private String target;
        @JsonProperty("model_name")
        private String modelName;
        private double prob;
        private boolean hit;
    }
}
