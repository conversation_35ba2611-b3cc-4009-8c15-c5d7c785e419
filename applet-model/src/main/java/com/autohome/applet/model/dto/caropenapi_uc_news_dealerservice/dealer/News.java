package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

public class News {

    public void setNewsId(long newsId) {
        this.newsId = newsId;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public void setClassId(int classId) {
        this.classId = classId;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private long newsId;

    private String title;

    private String publishTime;

    private String summary;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String imageUrl;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int dealerId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int classId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String className;

    //-----------------
    public int getClassid() {
        return classId;
    }

    public String getClassname() {
        return className;
    }

    public int getDealerid() {
        return dealerId;
    }

    public long getNewsid() {
        return newsId;
    }

    public String getPublishtime() {
        if (StringUtil.isBlank(publishTime)) {
            return publishTime;
        }
        return publishTime.substring(0, publishTime.indexOf("T"));
    }

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private int classid;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String classname;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private int dealerid;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private long newsid;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String publishtime;


    public String getTitle() {
        return title;
    }

    public String getSummary() {
        return summary;
    }

    public String getImageurl() {
        return this.imageUrl;
    }
}