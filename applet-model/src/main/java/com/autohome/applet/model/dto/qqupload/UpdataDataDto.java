package com.autohome.applet.model.dto.qqupload;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdataDataDto<T> {
    private int count;
    private DataList data_list;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataList<T> {
        private List<T> add_data_list;
        private List<T> delete_data_list;

        public List<T> getAdd_data_list() {
            return add_data_list == null ? new ArrayList<>() : add_data_list;
        }

        public List<T> getDelete_data_list() {
            return delete_data_list == null ? new ArrayList<>() : delete_data_list;
        }
    }
}
