package com.autohome.applet.model.dto.dealer;

import java.util.List;

public class DealerFilterUrlResult {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<ItemInfo> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<ItemInfo> getList() {
        return list;
    }

    public void setList(List<ItemInfo> list) {
        this.list = list;
    }

    public static class ItemInfo {
        private List<Manufactory> ManufactoryList;

        public List<Manufactory> getManufactoryList() {
            return ManufactoryList;
        }

        public void setManufactoryList(List<Manufactory> manufactoryList) {
            ManufactoryList = manufactoryList;
        }
    }

    public static class Manufactory{
        private int ManufactoryId;
        private String Name;
        private List<Serie> Series;

        public int getManufactoryId() {
            return ManufactoryId;
        }

        public void setManufactoryId(int manufactoryId) {
            ManufactoryId = manufactoryId;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public List<Serie> getSeries() {
            return Series;
        }

        public void setSeries(List<Serie> series) {
            Series = series;
        }
    }

    public static class Serie{
        private int SeriesId;
        private String Name;

        public int getSeriesId() {
            return SeriesId;
        }

        public void setSeriesId(int seriesId) {
            SeriesId = seriesId;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }
    }
}
