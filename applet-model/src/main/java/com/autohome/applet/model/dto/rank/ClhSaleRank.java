package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * crated by hanshanfeng 2021/12/14 15:35
 */
public class ClhSaleRank {


    private int code;
    private String message;
    private String dataType;
    private List<SeriesSaleRank.DataBean> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public List<SeriesSaleRank.DataBean> getData() {
        return data;
    }

    public void setData(List<SeriesSaleRank.DataBean> data) {
        this.data = data;
    }
//    public List<SaleData> getData() {
//        return data;
//    }
//
//    public void setData(List<SaleData> data) {
//        this.data = data;
//    }

//    public static class DataBean {
//        /**
//         * id : 1367
//         * seriesid : 5714
//         * min_guidance_price : 28800
//         * max_guidance_price : 43600
//         * month : 2021-10
//         * salecnt : 47834
//         * manu_type : 自主
//         * level : 1
//         * is_newenergy : 1
//         * created_stime : 1637059551000
//         * modified_stime : 1639150562000
//         * rnnum : 1
//         * rn : 1
//         * scorecnt : 47834
//         * pre_rn : 0
//         */
//
//        private int id;
//        private String seriesid;
//        private int min_guidance_price;
//        private int max_guidance_price;
//        private String month;
//        private int salecnt;
//        private String manu_type;
//        private String level;
//        private int is_newenergy;
//        private long created_stime;
//        private long modified_stime;
//        private int rnnum;
//        private int rn;
//        private int scorecnt;
//        private int pre_rn;
//
//        public int getId() {
//            return id;
//        }
//
//        public void setId(int id) {
//            this.id = id;
//        }
//
//        public String getSeriesid() {
//            return seriesid;
//        }
//
//        public void setSeriesid(String seriesid) {
//            this.seriesid = seriesid;
//        }
//
//        public int getMin_guidance_price() {
//            return min_guidance_price;
//        }
//
//        public void setMin_guidance_price(int min_guidance_price) {
//            this.min_guidance_price = min_guidance_price;
//        }
//
//        public int getMax_guidance_price() {
//            return max_guidance_price;
//        }
//
//        public void setMax_guidance_price(int max_guidance_price) {
//            this.max_guidance_price = max_guidance_price;
//        }
//
//        public String getMonth() {
//            return month;
//        }
//
//        public void setMonth(String month) {
//            this.month = month;
//        }
//
//        public int getSalecnt() {
//            return salecnt;
//        }
//
//        public void setSalecnt(int salecnt) {
//            this.salecnt = salecnt;
//        }
//
//        public String getManu_type() {
//            return manu_type;
//        }
//
//        public void setManu_type(String manu_type) {
//            this.manu_type = manu_type;
//        }
//
//        public String getLevel() {
//            return level;
//        }
//
//        public void setLevel(String level) {
//            this.level = level;
//        }
//
//        public int getIs_newenergy() {
//            return is_newenergy;
//        }
//
//        public void setIs_newenergy(int is_newenergy) {
//            this.is_newenergy = is_newenergy;
//        }
//
//        public long getCreated_stime() {
//            return created_stime;
//        }
//
//        public void setCreated_stime(long created_stime) {
//            this.created_stime = created_stime;
//        }
//
//        public long getModified_stime() {
//            return modified_stime;
//        }
//
//        public void setModified_stime(long modified_stime) {
//            this.modified_stime = modified_stime;
//        }
//
//        public int getRnnum() {
//            return rnnum;
//        }
//
//        public void setRnnum(int rnnum) {
//            this.rnnum = rnnum;
//        }
//
//        public int getRn() {
//            return rn;
//        }
//
//        public void setRn(int rn) {
//            this.rn = rn;
//        }
//
//        public int getScorecnt() {
//            return scorecnt;
//        }
//
//        public void setScorecnt(int scorecnt) {
//            this.scorecnt = scorecnt;
//        }
//
//        public int getPre_rn() {
//            return pre_rn;
//        }
//
//        public void setPre_rn(int pre_rn) {
//            this.pre_rn = pre_rn;
//        }
//    }
}
