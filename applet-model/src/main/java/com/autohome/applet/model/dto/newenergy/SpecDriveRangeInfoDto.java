package com.autohome.applet.model.dto.newenergy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>BoWen
 * @date: 2024-01-21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpecDriveRangeInfoDto {

    private int seriesId;

    private int lastLevelFactRange;

    private List<SpecSeasonData> seasonDataList;

    private int officialDriveRange;

    private int factDriveRange;

    //private StatisSpecInfo specInfo;

    public static SpecDriveRangeInfoDto getInstance(StatisSpecInfo specInfo) {
        SpecDriveRangeInfoDto instance = new SpecDriveRangeInfoDto();
        instance.setSeriesId(specInfo.getSeriesId());
        instance.setFactDriveRange(specInfo.getFactDriveRange());
        instance.setOfficialDriveRange(specInfo.getOfficialDriveRange());
        instance.setLastLevelFactRange(0);
        return instance;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SpecSeasonData{

        private String name;

        private Integer factDriveRange;

    }
}
