package com.autohome.applet.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 经销商类型 1-定制经销商 2-标准经销商
 * @createTime 2023-09-18 16:21:00
 */
public enum DealerTypeEnum {
    SPECIFIC(1, "定制经销商"),//定制经销商
    NORMAL(2, "标准经销商"), //标准经销商
    ;

    private final static Map<Integer, DealerTypeEnum> _CODES_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));


    private Integer code;
    private String info;

    private DealerTypeEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static DealerTypeEnum fromCode(int code) {
        return _CODES_MAP.getOrDefault(code, DealerTypeEnum.NORMAL);
    }
}
