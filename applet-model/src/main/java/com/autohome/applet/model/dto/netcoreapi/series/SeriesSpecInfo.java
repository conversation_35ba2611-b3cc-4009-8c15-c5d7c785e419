package com.autohome.applet.model.dto.netcoreapi.series;

import java.math.BigDecimal;

public class SeriesSpecInfo {
    /// <summary>
    /// 车型id
    ///</summary>
    private int id;
    /// <summary>
    /// 车型名称
    ///</summary>
    private String name;
    /// <summary>
    ///  车型代表图
    ///</summary>
    private String logo;
    /// <summary>
    ///  年贷款
    ///</summary>
    private int year;
    /// <summary>
    /// 车型指导价(低价)
    ///</summary>
    private int minprice;
    /// <summary>
    ///车型指导价(高价)
    ///</summary>
    private int maxprice;
    /// <summary>
    /// 变速箱
    ///</summary>
    private String transmission;
    /// <summary>
    /// 车型状态
    ///</summary>
    private int state;
    /// <summary>
    /// 驱动类型
    ///</summary>
    private String drivingmodename;
    /// <summary>
    /// 进气形式id
    ///</summary>
    private int flowmodeid;
    /// <summary>
    /// 进气形式名称
    ///</summary>
    private String flowmodename;
    /// <summary>
    /// 排气量
    ///</summary>
    private BigDecimal displacement;
    /// <summary>
    ///  马力
    ///</summary>
    private int enginepower;
    /// <summary>
    /// 是否优税
    ///</summary>
    private int ispreferential;
    /// <summary>
    ///  是否惠民
    ///</summary>
    private int istaxrelief;
    /// <summary>
    /// 车型排序
    ///</summary>
    private int order;
    /// <summary>
    /// 是否参数外显1：外显1：不外显
    ///</summary>
    private int specisimage;
    /// <summary>
    /// 是否为图片车型1：是0：否 
    ///</summary>
    private int paramisshow;
    /// <summary>
    /// 是否为经典车
    /// </summary>
    private int isclassic;
    /// <summary>
    /// 燃料形式 ： 汽油、柴油、油电混合、 电动 
    /// </summary>

    private String fueltype;
    /// <summary>
    /// 燃料形式id:  1 汽油、2柴油、3油电混合、4 电动 
    /// </summary>

    private int fueltypeid;

    private int syearid;

    private String emissionstandards;

    private String dynamicprice;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(int maxprice) {
        this.maxprice = maxprice;
    }

    public String getTransmission() {
        return transmission;
    }

    public void setTransmission(String transmission) {
        this.transmission = transmission;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getDrivingmodename() {
        return drivingmodename;
    }

    public void setDrivingmodename(String drivingmodename) {
        this.drivingmodename = drivingmodename;
    }

    public int getFlowmodeid() {
        return flowmodeid;
    }

    public void setFlowmodeid(int flowmodeid) {
        this.flowmodeid = flowmodeid;
    }

    public String getFlowmodename() {
        return flowmodename;
    }

    public void setFlowmodename(String flowmodename) {
        this.flowmodename = flowmodename;
    }

    public BigDecimal getDisplacement() {
        return displacement;
    }

    public void setDisplacement(BigDecimal displacement) {
        this.displacement = displacement;
    }

    public int getEnginepower() {
        return enginepower;
    }

    public void setEnginepower(int enginepower) {
        this.enginepower = enginepower;
    }

    public int getIspreferential() {
        return ispreferential;
    }

    public void setIspreferential(int ispreferential) {
        this.ispreferential = ispreferential;
    }

    public int getIstaxrelief() {
        return istaxrelief;
    }

    public void setIstaxrelief(int istaxrelief) {
        this.istaxrelief = istaxrelief;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getSpecisimage() {
        return specisimage;
    }

    public void setSpecisimage(int specisimage) {
        this.specisimage = specisimage;
    }

    public int getParamisshow() {
        return paramisshow;
    }

    public void setParamisshow(int paramisshow) {
        this.paramisshow = paramisshow;
    }

    public int getIsclassic() {
        return isclassic;
    }

    public void setIsclassic(int isclassic) {
        this.isclassic = isclassic;
    }

    public String getFueltype() {
        return fueltype;
    }

    public void setFueltype(String fueltype) {
        this.fueltype = fueltype;
    }

    public int getFueltypeid() {
        return fueltypeid;
    }

    public void setFueltypeid(int fueltypeid) {
        this.fueltypeid = fueltypeid;
    }

    public int getSyearid() {
        return syearid;
    }

    public void setSyearid(int syearid) {
        this.syearid = syearid;
    }

    public String getEmissionstandards() {
        return emissionstandards;
    }

    public void setEmissionstandards(String emissionstandards) {
        this.emissionstandards = emissionstandards;
    }

    public String getDynamicprice() {
        return dynamicprice;
    }

    public void setDynamicprice(String dynamicprice) {
        this.dynamicprice = dynamicprice;
    }
}
