package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SmartDataDetail {
    private String index5Id;
    private String indexShow;
    private int loudIndex;
    private int isBusiness;
    private int seriesNum;
    private String indexDesc;
    private List<SeriesInfo> seriesInfos;

    public String getIndex5Id() {
        return index5Id;
    }

    public void setIndex5Id(String index5Id) {
        this.index5Id = index5Id;
    }

    public String getIndexShow() {
        return indexShow;
    }

    public void setIndexShow(String indexShow) {
        this.indexShow = indexShow;
    }

    public int getLoudIndex() {
        return loudIndex;
    }

    public void setLoudIndex(int loudIndex) {
        this.loudIndex = loudIndex;
    }

    public int getIsBusiness() {
        return isBusiness;
    }

    public void setIsBusiness(int isBusiness) {
        this.isBusiness = isBusiness;
    }

    public int getSeriesNum() {
        return seriesNum;
    }

    public void setSeriesNum(int seriesNum) {
        this.seriesNum = seriesNum;
    }

    public String getIndexDesc() {
        return indexDesc;
    }

    public void setIndexDesc(String indexDesc) {
        this.indexDesc = indexDesc;
    }

    public List<SeriesInfo> getSeriesInfos() {
        return seriesInfos;
    }

    public void setSeriesInfos(List<SeriesInfo> seriesInfos) {
        this.seriesInfos = seriesInfos;
    }

    public static class SeriesInfo implements SeriesInfoSetable{
        private int seriesId;
        private String seriesName;
        private int seriesState;
        private int isBusiness;
        private String brandId;
        private String brandName;
        private String levelName;
        private String onTime;
        private List<String> top3;
        private String img;
        private String spec;
        private List<SpecList> specList;

        public int getSeriesId() {
            return seriesId;
        }

        @Override
        public void setBrandId(int brandId) {
            this.brandId = brandId + "";
        }

        @Override
        public void setSeriespnglogo(String logo) {
            this.img = logo;
        }

        @Override
        public void setMinPrice(int minPrice) {

        }

        @Override
        public void setMaxPrice(int maxPrice) {

        }

        public void setSeriesId(int seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesName() {
            return seriesName;
        }

        @Override
        public void setSeriesName(String seriesName) {
            this.seriesName = seriesName;
        }

        public int getSeriesState() {
            return seriesState;
        }

        public void setSeriesState(int seriesState) {
            this.seriesState = seriesState;
        }

        public int getIsBusiness() {
            return isBusiness;
        }

        public void setIsBusiness(int isBusiness) {
            this.isBusiness = isBusiness;
        }

        public String getBrandId() {
            return brandId;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public String getLevelName() {
            return levelName;
        }

        public void setLevelName(String levelName) {
            this.levelName = levelName;
        }

        public String getOnTime() {
            return onTime;
        }

        public void setOnTime(String onTime) {
            this.onTime = onTime;
        }

        public List<String> getTop3() {
            return top3;
        }

        public void setTop3(List<String> top3) {
            this.top3 = top3;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getSpec() {
            return spec;
        }

        public void setSpec(String spec) {
            this.spec = spec;
        }

        public List<SpecList> getSpecList() {
            return specList;
        }

        public void setSpecList(List<SpecList> specList) {
            this.specList = specList;
        }
    }

    public static class SpecList {
        private String seriesId;
        private String specId;

        public String getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(String seriesId) {
            this.seriesId = seriesId;
        }

        public String getSpecId() {
            return specId;
        }

        public void setSpecId(String specId) {
            this.specId = specId;
        }
    }
}
