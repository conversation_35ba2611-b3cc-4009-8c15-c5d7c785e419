package com.autohome.applet.model.dto.newenergy;



import java.util.List;

public class KoubeiInfoRes extends BaseEntity {
    private Result result;

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    public static class Result{
        private List<KouBeiInfoDto> list;
        private Integer pageIndex;
        private Integer pagecount;
        private Integer rowcount;

        public List<KouBeiInfoDto> getList() {
            return list;
        }

        public void setList(List<KouBeiInfoDto> list) {
            this.list = list;
        }

        public Integer getPageIndex() {
            return pageIndex;
        }

        public void setPageIndex(Integer pageIndex) {
            this.pageIndex = pageIndex;
        }

        public Integer getPagecount() {
            return pagecount;
        }

        public void setPagecount(Integer pagecount) {
            this.pagecount = pagecount;
        }

        public Integer getRowcount() {
            return rowcount;
        }

        public void setRowcount(Integer rowcount) {
            this.rowcount = rowcount;
        }
    }

}
