package com.autohome.applet.model.dto.netcoreapi.dealer;

import java.util.List;

public class DealerInfoBaseOut {

    private int kindId;
    private int is24h;
    private String dealerName;
    private String companySimple;
    private String address;
    private int starLevel;
    private String sellPhone;
    private String appSellPhone;
    private String showPhone;
    private String businessArea;
    private int orderRange;
    private String orderRangeTitle;
    private long provinceId;
    private long cityId;
    private long countyId;
    private double longitude;
    private double latitude;
    private String provinceName;
    private String cityName;
    private String countyName;
    private List<DealerInfoBrandList> brandList;
    private int payType;
    private int callRate400;
    private int leadsRatingScore;
    private int tradeReduce;
    private int openIM;
    private int contractType;
    private String urlH5;
    private String urlPC;
    private String urlApp;
    private int salesLevel;
    private int isZhiXiao;

    public void setKindId(int kindId) {
        this.kindId = kindId;
    }

    public int getKindId() {
        return kindId;
    }

    public void setIs24h(int is24h) {
        this.is24h = is24h;
    }

    public int getIs24h() {
        return is24h;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setCompanySimple(String companySimple) {
        this.companySimple = companySimple;
    }

    public String getCompanySimple() {
        return companySimple;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setStarLevel(int starLevel) {
        this.starLevel = starLevel;
    }

    public int getStarLevel() {
        return starLevel;
    }

    public void setSellPhone(String sellPhone) {
        this.sellPhone = sellPhone;
    }

    public String getSellPhone() {
        return sellPhone;
    }

    public void setAppSellPhone(String appSellPhone) {
        this.appSellPhone = appSellPhone;
    }

    public String getAppSellPhone() {
        return appSellPhone;
    }

    public void setShowPhone(String showPhone) {
        this.showPhone = showPhone;
    }

    public String getShowPhone() {
        return showPhone;
    }

    public void setBusinessArea(String businessArea) {
        this.businessArea = businessArea;
    }

    public String getBusinessArea() {
        return businessArea;
    }

    public void setOrderRange(int orderRange) {
        this.orderRange = orderRange;
    }

    public int getOrderRange() {
        return orderRange;
    }

    public void setOrderRangeTitle(String orderRangeTitle) {
        this.orderRangeTitle = orderRangeTitle;
    }

    public String getOrderRangeTitle() {
        return orderRangeTitle;
    }

    public void setProvinceId(long provinceId) {
        this.provinceId = provinceId;
    }

    public long getProvinceId() {
        return provinceId;
    }

    public void setCityId(long cityId) {
        this.cityId = cityId;
    }

    public long getCityId() {
        return cityId;
    }

    public void setCountyId(long countyId) {
        this.countyId = countyId;
    }

    public long getCountyId() {
        return countyId;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setBrandList(List<DealerInfoBrandList> brandList) {
        this.brandList = brandList;
    }

    public List<DealerInfoBrandList> getBrandList() {
        return brandList;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public int getPayType() {
        return payType;
    }

    public void setCallRate400(int callRate400) {
        this.callRate400 = callRate400;
    }

    public int getCallRate400() {
        return callRate400;
    }

    public void setLeadsRatingScore(int leadsRatingScore) {
        this.leadsRatingScore = leadsRatingScore;
    }

    public int getLeadsRatingScore() {
        return leadsRatingScore;
    }

    public void setTradeReduce(int tradeReduce) {
        this.tradeReduce = tradeReduce;
    }

    public int getTradeReduce() {
        return tradeReduce;
    }

    public void setOpenIM(int openIM) {
        this.openIM = openIM;
    }

    public int getOpenIM() {
        return openIM;
    }

    public void setContractType(int contractType) {
        this.contractType = contractType;
    }

    public int getContractType() {
        return contractType;
    }

    public void setUrlH5(String urlH5) {
        this.urlH5 = urlH5;
    }

    public String getUrlH5() {
        return urlH5;
    }

    public void setUrlPC(String urlPC) {
        this.urlPC = urlPC;
    }

    public String getUrlPC() {
        return urlPC;
    }

    public void setUrlApp(String urlApp) {
        this.urlApp = urlApp;
    }

    public String getUrlApp() {
        return urlApp;
    }

    public void setSalesLevel(int salesLevel) {
        this.salesLevel = salesLevel;
    }

    public int getSalesLevel() {
        return salesLevel;
    }

    public void setIsZhiXiao(int isZhiXiao) {
        this.isZhiXiao = isZhiXiao;
    }

    public int getIsZhiXiao() {
        return isZhiXiao;
    }

}