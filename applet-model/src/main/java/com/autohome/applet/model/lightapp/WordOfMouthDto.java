package com.autohome.applet.model.lightapp;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class WordOfMouthDto {

    @JsonProperty(value = "actual_battery_consumption")
    private Double actualBatteryConsumption;
    @JsonProperty(value = "actual_oil_consumption")
    private Double actualOilConsumption;
    private Integer adminStatus;
    @JsonProperty(value = "append_count")
    private Integer appendCount;
    @JsonProperty(value = "append_list")
    private List<String> appendList;
    private Double apperance;
    private Double average;
    private String best;
    private Integer bigV;
    private String bigVCoverUrl;
    private Integer bigVPlayCount;
    private Integer bigVPlayId;
    private String bigVVideoId;
    private String boughtCityName;
    @JsonProperty(value = "bought_city")
    private Double boughtCity;
    @JsonProperty(value = "bought_date")
    private String boughtDate;
    @JsonProperty(value = "bought_place")
    private Integer boughtPlace;
    @JsonProperty(value = "bought_province")
    private Integer boughtProvince;
    private Integer carOwnerLevels;
    private String carOwnershipPeriod;
    private Double comfortableness;
    private Integer commentCount;
    private Double consumption;
    @JsonProperty(value = "cost_efficient")
    private Double costEfficient;
    private String created;
    private String dealerName;
    private Boolean deleteShow;
    private Integer deliverycycle;
    @JsonProperty(value = "driven_kilometers")
    private Integer drivenKilometers;
    private Integer drivingSkills;
    private Integer factoryId;
    private String feeling;
    @JsonProperty(value = "feeling_summary")
    private String feelingSummary;
    private String headImage;
    private Integer helpLV;
    private Integer helpfulCount;
    private Integer id;
    private Double interior;
    @JsonProperty(value = "ip_location")
    private String ipLocation;
    private Integer isAuthSeries;
    private Integer isAuthenticated;
    private Integer koubeiStatus;
    private Integer koubeiType;
    private List<String> koubeiVideoList;
    private String lastAppendTime;
    @JsonProperty(value = "last_append_driven_kilometers")
    private Double lastAppendDrivenKilometers;
    @JsonProperty(value = "last_edit")
    private String lastEdit;
    private Double maneuverability;
    private Integer medal;
    private List<MultiImagesDto> multiImages;
    private Integer newEnergyEval;
    private String newEnergyImgBack;
    private Integer newEnergyUser;
    private String nickName;
    private Integer objId;
    private List<String> photos;
    private Double power;
    private Integer powerType;
    @JsonProperty(value = "power_driven_type")
    private Integer powerDrivenType;
    private Double price;
    private List<PurposesDto> purposes;
    private Integer recommend;
    private Integer regionId;
    private String regionProvince;
    private Integer routineCruisingRange;
    @JsonProperty(value = "row_status")
    private Integer rowStatus;
    private Integer seriesId;
    //平均得分
    private Double kbscore;
    private String seriesName;
    private String showId;
    private Integer showPublishEval;
    private Integer showSignUp;
    private Double smartConfig;
    private Double space;
    private String specName;
    private Integer specid;
    private Double springbattery;
    private Integer springrange;
    private Double summerbattery;
    private Integer summerrange;
    private List<TabInfoListDto> tabInfoList;
    private List<String> topics;
    private Integer userBigVLevel;
    private Integer userid;
    private Integer visitCount;
    private Integer winterCruisingRange;
    private Integer winterbattery;
    private String worst;
    private Integer writeseason;
    //口碑获取用户购车价
    private BoughtpriceDto boughtprice;

    public BoughtpriceDto getBoughtprice() {
        return boughtprice;
    }

    public void setBoughtprice(BoughtpriceDto boughtprice) {
        this.boughtprice = boughtprice;
    }

    public Double getKbscore() {
        return kbscore;
    }

    public void setKbscore(Double kbscore) {
        this.kbscore = kbscore;
    }

    public Double getActualBatteryConsumption() {
        return actualBatteryConsumption;
    }

    public void setActualBatteryConsumption(Double actualBatteryConsumption) {
        this.actualBatteryConsumption = actualBatteryConsumption;
    }

    public Double getActualOilConsumption() {
        return actualOilConsumption;
    }

    public void setActualOilConsumption(Double actualOilConsumption) {
        this.actualOilConsumption = actualOilConsumption;
    }

    public Integer getAdminStatus() {
        return adminStatus;
    }

    public void setAdminStatus(Integer adminStatus) {
        this.adminStatus = adminStatus;
    }

    public Integer getAppendCount() {
        return appendCount;
    }

    public void setAppendCount(Integer appendCount) {
        this.appendCount = appendCount;
    }

    public List<String> getAppendList() {
        return appendList;
    }

    public void setAppendList(List<String> appendList) {
        this.appendList = appendList;
    }

    public Double getApperance() {
        return apperance;
    }

    public void setApperance(Double apperance) {
        this.apperance = apperance;
    }

    public Double getAverage() {
        return average;
    }

    public void setAverage(Double average) {
        this.average = average;
    }

    public String getBest() {
        return best;
    }

    public void setBest(String best) {
        this.best = best;
    }

    public Integer getBigV() {
        return bigV;
    }

    public void setBigV(Integer bigV) {
        this.bigV = bigV;
    }

    public String getBigVCoverUrl() {
        return bigVCoverUrl;
    }

    public void setBigVCoverUrl(String bigVCoverUrl) {
        this.bigVCoverUrl = bigVCoverUrl;
    }

    public Integer getBigVPlayCount() {
        return bigVPlayCount;
    }

    public void setBigVPlayCount(Integer bigVPlayCount) {
        this.bigVPlayCount = bigVPlayCount;
    }

    public Integer getBigVPlayId() {
        return bigVPlayId;
    }

    public void setBigVPlayId(Integer bigVPlayId) {
        this.bigVPlayId = bigVPlayId;
    }

    public String getBigVVideoId() {
        return bigVVideoId;
    }

    public void setBigVVideoId(String bigVVideoId) {
        this.bigVVideoId = bigVVideoId;
    }

    public String getBoughtCityName() {
        return boughtCityName;
    }

    public void setBoughtCityName(String boughtCityName) {
        this.boughtCityName = boughtCityName;
    }

    public Double getBoughtCity() {
        return boughtCity;
    }

    public void setBoughtCity(Double boughtCity) {
        this.boughtCity = boughtCity;
    }

    public String getBoughtDate() {
        return boughtDate;
    }

    public void setBoughtDate(String boughtDate) {
        this.boughtDate = boughtDate;
    }

    public Integer getBoughtPlace() {
        return boughtPlace;
    }

    public void setBoughtPlace(Integer boughtPlace) {
        this.boughtPlace = boughtPlace;
    }

    public Integer getBoughtProvince() {
        return boughtProvince;
    }

    public void setBoughtProvince(Integer boughtProvince) {
        this.boughtProvince = boughtProvince;
    }

    public Integer getCarOwnerLevels() {
        return carOwnerLevels;
    }

    public void setCarOwnerLevels(Integer carOwnerLevels) {
        this.carOwnerLevels = carOwnerLevels;
    }

    public String getCarOwnershipPeriod() {
        return carOwnershipPeriod;
    }

    public void setCarOwnershipPeriod(String carOwnershipPeriod) {
        this.carOwnershipPeriod = carOwnershipPeriod;
    }

    public Double getComfortableness() {
        return comfortableness;
    }

    public void setComfortableness(Double comfortableness) {
        this.comfortableness = comfortableness;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Double getConsumption() {
        return consumption;
    }

    public void setConsumption(Double consumption) {
        this.consumption = consumption;
    }

    public Double getCostEfficient() {
        return costEfficient;
    }

    public void setCostEfficient(Double costEfficient) {
        this.costEfficient = costEfficient;
    }

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public Boolean getDeleteShow() {
        return deleteShow;
    }

    public void setDeleteShow(Boolean deleteShow) {
        this.deleteShow = deleteShow;
    }

    public Integer getDeliverycycle() {
        return deliverycycle;
    }

    public void setDeliverycycle(Integer deliverycycle) {
        this.deliverycycle = deliverycycle;
    }

    public Integer getDrivenKilometers() {
        return drivenKilometers;
    }

    public void setDrivenKilometers(Integer drivenKilometers) {
        this.drivenKilometers = drivenKilometers;
    }

    public Integer getDrivingSkills() {
        return drivingSkills;
    }

    public void setDrivingSkills(Integer drivingSkills) {
        this.drivingSkills = drivingSkills;
    }

    public Integer getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Integer factoryId) {
        this.factoryId = factoryId;
    }

    public String getFeeling() {
        return feeling;
    }

    public void setFeeling(String feeling) {
        this.feeling = feeling;
    }

    public String getFeelingSummary() {
        return feelingSummary;
    }

    public void setFeelingSummary(String feelingSummary) {
        this.feelingSummary = feelingSummary;
    }

    public String getHeadImage() {
        return headImage;
    }

    public void setHeadImage(String headImage) {
        this.headImage = headImage;
    }

    public Integer getHelpLV() {
        return helpLV;
    }

    public void setHelpLV(Integer helpLV) {
        this.helpLV = helpLV;
    }

    public Integer getHelpfulCount() {
        return helpfulCount;
    }

    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getInterior() {
        return interior;
    }

    public void setInterior(Double interior) {
        this.interior = interior;
    }

    public String getIpLocation() {
        return ipLocation;
    }

    public void setIpLocation(String ipLocation) {
        this.ipLocation = ipLocation;
    }

    public Integer getIsAuthSeries() {
        return isAuthSeries;
    }

    public void setIsAuthSeries(Integer isAuthSeries) {
        this.isAuthSeries = isAuthSeries;
    }

    public Integer getIsAuthenticated() {
        return isAuthenticated;
    }

    public void setIsAuthenticated(Integer isAuthenticated) {
        this.isAuthenticated = isAuthenticated;
    }

    public Integer getKoubeiStatus() {
        return koubeiStatus;
    }

    public void setKoubeiStatus(Integer koubeiStatus) {
        this.koubeiStatus = koubeiStatus;
    }

    public Integer getKoubeiType() {
        return koubeiType;
    }

    public void setKoubeiType(Integer koubeiType) {
        this.koubeiType = koubeiType;
    }

    public List<String> getKoubeiVideoList() {
        return koubeiVideoList;
    }

    public void setKoubeiVideoList(List<String> koubeiVideoList) {
        this.koubeiVideoList = koubeiVideoList;
    }

    public String getLastAppendTime() {
        return lastAppendTime;
    }

    public void setLastAppendTime(String lastAppendTime) {
        this.lastAppendTime = lastAppendTime;
    }

    public Double getLastAppendDrivenKilometers() {
        return lastAppendDrivenKilometers;
    }

    public void setLastAppendDrivenKilometers(Double lastAppendDrivenKilometers) {
        this.lastAppendDrivenKilometers = lastAppendDrivenKilometers;
    }

    public String getLastEdit() {
        return lastEdit;
    }

    public void setLastEdit(String lastEdit) {
        this.lastEdit = lastEdit;
    }

    public Double getManeuverability() {
        return maneuverability;
    }

    public void setManeuverability(Double maneuverability) {
        this.maneuverability = maneuverability;
    }

    public Integer getMedal() {
        return medal;
    }

    public void setMedal(Integer medal) {
        this.medal = medal;
    }

    public List<MultiImagesDto> getMultiImages() {
        return multiImages;
    }

    public void setMultiImages(List<MultiImagesDto> multiImages) {
        this.multiImages = multiImages;
    }

    public Integer getNewEnergyEval() {
        return newEnergyEval;
    }

    public void setNewEnergyEval(Integer newEnergyEval) {
        this.newEnergyEval = newEnergyEval;
    }

    public String getNewEnergyImgBack() {
        return newEnergyImgBack;
    }

    public void setNewEnergyImgBack(String newEnergyImgBack) {
        this.newEnergyImgBack = newEnergyImgBack;
    }

    public Integer getNewEnergyUser() {
        return newEnergyUser;
    }

    public void setNewEnergyUser(Integer newEnergyUser) {
        this.newEnergyUser = newEnergyUser;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getObjId() {
        return objId;
    }

    public void setObjId(Integer objId) {
        this.objId = objId;
    }

    public List<String> getPhotos() {
        return photos;
    }

    public void setPhotos(List<String> photos) {
        this.photos = photos;
    }

    public Double getPower() {
        return power;
    }

    public void setPower(Double power) {
        this.power = power;
    }

    public Integer getPowerType() {
        return powerType;
    }

    public void setPowerType(Integer powerType) {
        this.powerType = powerType;
    }

    public Integer getPowerDrivenType() {
        return powerDrivenType;
    }

    public void setPowerDrivenType(Integer powerDrivenType) {
        this.powerDrivenType = powerDrivenType;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public List<PurposesDto> getPurposes() {
        return purposes;
    }

    public void setPurposes(List<PurposesDto> purposes) {
        this.purposes = purposes;
    }

    public Integer getRecommend() {
        return recommend;
    }

    public void setRecommend(Integer recommend) {
        this.recommend = recommend;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getRegionProvince() {
        return regionProvince;
    }

    public void setRegionProvince(String regionProvince) {
        this.regionProvince = regionProvince;
    }

    public Integer getRoutineCruisingRange() {
        return routineCruisingRange;
    }

    public void setRoutineCruisingRange(Integer routineCruisingRange) {
        this.routineCruisingRange = routineCruisingRange;
    }

    public Integer getRowStatus() {
        return rowStatus;
    }

    public void setRowStatus(Integer rowStatus) {
        this.rowStatus = rowStatus;
    }

    public Integer getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getShowId() {
        return showId;
    }

    public void setShowId(String showId) {
        this.showId = showId;
    }

    public Integer getShowPublishEval() {
        return showPublishEval;
    }

    public void setShowPublishEval(Integer showPublishEval) {
        this.showPublishEval = showPublishEval;
    }

    public Integer getShowSignUp() {
        return showSignUp;
    }

    public void setShowSignUp(Integer showSignUp) {
        this.showSignUp = showSignUp;
    }

    public Double getSmartConfig() {
        return smartConfig;
    }

    public void setSmartConfig(Double smartConfig) {
        this.smartConfig = smartConfig;
    }

    public Double getSpace() {
        return space;
    }

    public void setSpace(Double space) {
        this.space = space;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public Integer getSpecid() {
        return specid;
    }

    public void setSpecid(Integer specid) {
        this.specid = specid;
    }

    public Double getSpringbattery() {
        return springbattery;
    }

    public void setSpringbattery(Double springbattery) {
        this.springbattery = springbattery;
    }

    public Integer getSpringrange() {
        return springrange;
    }

    public void setSpringrange(Integer springrange) {
        this.springrange = springrange;
    }

    public Double getSummerbattery() {
        return summerbattery;
    }

    public void setSummerbattery(Double summerbattery) {
        this.summerbattery = summerbattery;
    }

    public Integer getSummerrange() {
        return summerrange;
    }

    public void setSummerrange(Integer summerrange) {
        this.summerrange = summerrange;
    }

    public List<TabInfoListDto> getTabInfoList() {
        return tabInfoList;
    }

    public void setTabInfoList(List<TabInfoListDto> tabInfoList) {
        this.tabInfoList = tabInfoList;
    }

    public List<String> getTopics() {
        return topics;
    }

    public void setTopics(List<String> topics) {
        this.topics = topics;
    }

    public Integer getUserBigVLevel() {
        return userBigVLevel;
    }

    public void setUserBigVLevel(Integer userBigVLevel) {
        this.userBigVLevel = userBigVLevel;
    }

    public Integer getUserid() {
        return userid;
    }

    public void setUserid(Integer userid) {
        this.userid = userid;
    }

    public Integer getVisitCount() {
        return visitCount;
    }

    public void setVisitCount(Integer visitCount) {
        this.visitCount = visitCount;
    }

    public Integer getWinterCruisingRange() {
        return winterCruisingRange;
    }

    public void setWinterCruisingRange(Integer winterCruisingRange) {
        this.winterCruisingRange = winterCruisingRange;
    }

    public Integer getWinterbattery() {
        return winterbattery;
    }

    public void setWinterbattery(Integer winterbattery) {
        this.winterbattery = winterbattery;
    }

    public String getWorst() {
        return worst;
    }

    public void setWorst(String worst) {
        this.worst = worst;
    }

    public Integer getWriteseason() {
        return writeseason;
    }

    public void setWriteseason(Integer writeseason) {
        this.writeseason = writeseason;
    }
}
