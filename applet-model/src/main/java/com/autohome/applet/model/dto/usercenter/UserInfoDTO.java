package com.autohome.applet.model.dto.usercenter;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by liliang on 2021/10/29.
 */
@Data
public class UserInfoDTO {
    private String headimage;
    private String nickname;
    private String newnickname;
    private Integer userid;
    private String mobilephone;
    private String realMobliePhone;
    private Integer sex;
    private Integer cityid;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date addDate;
}
