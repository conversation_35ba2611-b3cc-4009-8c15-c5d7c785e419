package com.autohome.applet.model.dto.cms;

import java.util.Date;
import java.util.List;

public class JiageDetail {

    private int brandid;
    private long carpriceid;
    private long cityid;
    private int countyid;
    private Date createtime;
    private long dealerid;
    private List<JiageDetailfee> detailfee;
    private long fctprice;
    private String feelcontent;
    private int feellevel;
    private double fullprice;
    private String invoiceurl;
    private long iscarowner;
    private long memberid;
    private String membername;
    private long nakedprice;
    private long provinceid;
    private String salespack;
    private int seriesid;
    private String seriesName;
    private Date shoppingtime;
    private int specid;
    private String specname;
    private int specstate;
    private String uuid;

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setCarpriceid(long carpriceid) {
        this.carpriceid = carpriceid;
    }

    public long getCarpriceid() {
        return carpriceid;
    }

    public void setCityid(long cityid) {
        this.cityid = cityid;
    }

    public long getCityid() {
        return cityid;
    }

    public void setCountyid(int countyid) {
        this.countyid = countyid;
    }

    public int getCountyid() {
        return countyid;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setDealerid(long dealerid) {
        this.dealerid = dealerid;
    }

    public long getDealerid() {
        return dealerid;
    }

    public void setDetailfee(List<JiageDetailfee> detailfee) {
        this.detailfee = detailfee;
    }

    public List<JiageDetailfee> getDetailfee() {
        return detailfee;
    }

    public void setFctprice(long fctprice) {
        this.fctprice = fctprice;
    }

    public long getFctprice() {
        return fctprice;
    }

    public void setFeelcontent(String feelcontent) {
        this.feelcontent = feelcontent;
    }

    public String getFeelcontent() {
        return feelcontent;
    }

    public void setFeellevel(int feellevel) {
        this.feellevel = feellevel;
    }

    public int getFeellevel() {
        return feellevel;
    }

    public void setFullprice(double fullprice) {
        this.fullprice = fullprice;
    }

    public double getFullprice() {
        return fullprice;
    }

    public void setInvoiceurl(String invoiceurl) {
        this.invoiceurl = invoiceurl;
    }

    public String getInvoiceurl() {
        return invoiceurl;
    }

    public void setIscarowner(long iscarowner) {
        this.iscarowner = iscarowner;
    }

    public long getIscarowner() {
        return iscarowner;
    }

    public void setMemberid(long memberid) {
        this.memberid = memberid;
    }

    public long getMemberid() {
        return memberid;
    }

    public void setMembername(String membername) {
        this.membername = membername;
    }

    public String getMembername() {
        return membername;
    }

    public void setNakedprice(long nakedprice) {
        this.nakedprice = nakedprice;
    }

    public long getNakedprice() {
        return nakedprice;
    }

    public void setProvinceid(long provinceid) {
        this.provinceid = provinceid;
    }

    public long getProvinceid() {
        return provinceid;
    }

    public void setSalespack(String salespack) {
        this.salespack = salespack;
    }

    public String getSalespack() {
        return salespack;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setShoppingtime(Date shoppingtime) {
        this.shoppingtime = shoppingtime;
    }

    public Date getShoppingtime() {
        return shoppingtime;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecstate(int specstate) {
        this.specstate = specstate;
    }

    public int getSpecstate() {
        return specstate;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUuid() {
        return uuid;
    }

}