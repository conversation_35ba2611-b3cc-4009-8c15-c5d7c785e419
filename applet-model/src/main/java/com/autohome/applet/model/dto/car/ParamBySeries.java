package com.autohome.applet.model.dto.car;

import com.autohome.applet.model.dto.netcoreapi.series.CarSpecInfoquick;

import java.util.List;

public class ParamBySeries {
    private int brandid;
    private String brandname;
    private int containstopspec;
    private int containtelectriccar;
    private int currentstatemaxoilwear;
    private int currentstateminoilwear;
    private List<String> displacementitems;
    private int existmaintain;
    private int fctid;
    private String fctname;
    private int id;
    private int isshow;
    private int levelid;
    private String levelname;
    private int maxfuelconsumption;
    private int maxoilwear;
    private int maxprice;
    private int minfuelconsumption;
    private int minoilwear;
    private int minprice;
    private String name;
    private int newenergy;
    private int paramisshow;
    private List<ParamBySeriesPicinfoitems> picinfoitems;
    private List<String> picitems;
    private int picnum;
    private String pnglogo;
    private int sellspecnum;
    private int specnum;
    private int state;
    private int stopspecnum;
    private List<String> structitems;
    private List<String> transmissionitems;
    private int waitspecnum;

    private CarSpecInfoquick specInfoquick;

    public CarSpecInfoquick getSpecInfoquick() {
        return specInfoquick;
    }

    public void setSpecInfoquick(CarSpecInfoquick specInfoquick) {
        this.specInfoquick = specInfoquick;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setContainstopspec(int containstopspec) {
        this.containstopspec = containstopspec;
    }

    public int getContainstopspec() {
        return containstopspec;
    }

    public void setContaintelectriccar(int containtelectriccar) {
        this.containtelectriccar = containtelectriccar;
    }

    public int getContaintelectriccar() {
        return containtelectriccar;
    }

    public void setCurrentstatemaxoilwear(int currentstatemaxoilwear) {
        this.currentstatemaxoilwear = currentstatemaxoilwear;
    }

    public int getCurrentstatemaxoilwear() {
        return currentstatemaxoilwear;
    }

    public void setCurrentstateminoilwear(int currentstateminoilwear) {
        this.currentstateminoilwear = currentstateminoilwear;
    }

    public int getCurrentstateminoilwear() {
        return currentstateminoilwear;
    }

    public void setDisplacementitems(List<String> displacementitems) {
        this.displacementitems = displacementitems;
    }

    public List<String> getDisplacementitems() {
        return displacementitems;
    }

    public void setExistmaintain(int existmaintain) {
        this.existmaintain = existmaintain;
    }

    public int getExistmaintain() {
        return existmaintain;
    }

    public void setFctid(int fctid) {
        this.fctid = fctid;
    }

    public int getFctid() {
        return fctid;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public String getFctname() {
        return fctname;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setIsshow(int isshow) {
        this.isshow = isshow;
    }

    public int getIsshow() {
        return isshow;
    }

    public void setLevelid(int levelid) {
        this.levelid = levelid;
    }

    public int getLevelid() {
        return levelid;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setMaxfuelconsumption(int maxfuelconsumption) {
        this.maxfuelconsumption = maxfuelconsumption;
    }

    public int getMaxfuelconsumption() {
        return maxfuelconsumption;
    }

    public void setMaxoilwear(int maxoilwear) {
        this.maxoilwear = maxoilwear;
    }

    public int getMaxoilwear() {
        return maxoilwear;
    }

    public void setMaxprice(int maxprice) {
        this.maxprice = maxprice;
    }

    public int getMaxprice() {
        return maxprice;
    }

    public void setMinfuelconsumption(int minfuelconsumption) {
        this.minfuelconsumption = minfuelconsumption;
    }

    public int getMinfuelconsumption() {
        return minfuelconsumption;
    }

    public void setMinoilwear(int minoilwear) {
        this.minoilwear = minoilwear;
    }

    public int getMinoilwear() {
        return minoilwear;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setNewenergy(int newenergy) {
        this.newenergy = newenergy;
    }

    public int getNewenergy() {
        return newenergy;
    }

    public void setParamisshow(int paramisshow) {
        this.paramisshow = paramisshow;
    }

    public int getParamisshow() {
        return paramisshow;
    }

    public void setPicinfoitems(List<ParamBySeriesPicinfoitems> picinfoitems) {
        this.picinfoitems = picinfoitems;
    }

    public List<ParamBySeriesPicinfoitems> getPicinfoitems() {
        return picinfoitems;
    }

    public void setPicitems(List<String> picitems) {
        this.picitems = picitems;
    }

    public List<String> getPicitems() {
        return picitems;
    }

    public void setPicnum(int picnum) {
        this.picnum = picnum;
    }

    public int getPicnum() {
        return picnum;
    }

    public void setPnglogo(String pnglogo) {
        this.pnglogo = pnglogo;
    }

    public String getPnglogo() {
        return pnglogo;
    }

    public void setSellspecnum(int sellspecnum) {
        this.sellspecnum = sellspecnum;
    }

    public int getSellspecnum() {
        return sellspecnum;
    }

    public void setSpecnum(int specnum) {
        this.specnum = specnum;
    }

    public int getSpecnum() {
        return specnum;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setStopspecnum(int stopspecnum) {
        this.stopspecnum = stopspecnum;
    }

    public int getStopspecnum() {
        return stopspecnum;
    }

    public void setStructitems(List<String> structitems) {
        this.structitems = structitems;
    }

    public List<String> getStructitems() {
        return structitems;
    }

    public void setTransmissionitems(List<String> transmissionitems) {
        this.transmissionitems = transmissionitems;
    }

    public List<String> getTransmissionitems() {
        return transmissionitems;
    }

    public void setWaitspecnum(int waitspecnum) {
        this.waitspecnum = waitspecnum;
    }

    public int getWaitspecnum() {
        return waitspecnum;
    }

}