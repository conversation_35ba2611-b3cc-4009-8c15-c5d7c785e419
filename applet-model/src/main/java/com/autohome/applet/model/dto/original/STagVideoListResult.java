package com.autohome.applet.model.dto.original;

import java.util.List;

/**
 * 视频列表
 * wiki： http://wiki.corpautohome.com/pages/viewpage.action?pageId=124981738
 */
public class STagVideoListResult {

    private int returncode = 0;

    private String message = "";

    private Result result;

    public int getReturncode() {
        return returncode;
    }

    public void setReturncode(int returncode) {
        this.returncode = returncode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    public static class Result {

        private boolean has_more;

        private String search_after;

        private List<VideoInfo> items;


        public boolean isHas_more() {
            return has_more;
        }

        public void setHas_more(boolean has_more) {
            this.has_more = has_more;
        }

        public String getSearch_after() {
            return search_after;
        }

        public void setSearch_after(String search_after) {
            this.search_after = search_after;
        }

        public List<VideoInfo> getItems() {
            return items;
        }

        public void setItems(List<VideoInfo> items) {
            this.items = items;
        }
    }

    public static class VideoInfo {

        private String author_name;

        private long vv;

        private long pv;

        private List<String> video_tag_names;

        private String author_img;

        private String link;

        private String img_url_16x9;

        private String title;

        private long duration;

        private String video_app_title;

        private String publish_time;

        private String video_source;

        private String summary;

        private String video_qrcode;

        private String img_url_4x3;

        private int reply_count;

        private int biz_id;

        private int author_id;

        private List<String> series_ids;

        private Integer video_width;
        private Integer video_height;
        private Integer video_direction;
    
        public Integer getVideo_width() {
            return video_width;
        }
    
        public void setVideo_width(Integer video_width) {
            this.video_width = video_width;
        }
    
        public Integer getVideo_height() {
            return video_height;
        }
    
        public void setVideo_height(Integer video_height) {
            this.video_height = video_height;
        }
    
        public Integer getVideo_direction() {
            return video_direction;
        }
    
        public void setVideo_direction(Integer video_direction) {
            this.video_direction = video_direction;
        }
    
        public String getAuthor_name() {
            return author_name;
        }

        public void setAuthor_name(String author_name) {
            this.author_name = author_name;
        }

        public long getVv() {
            return vv;
        }

        public void setVv(long vv) {
            this.vv = vv;
        }

        public long getPv() {
            return pv;
        }

        public void setPv(long pv) {
            this.pv = pv;
        }

        public List<String> getVideo_tag_names() {
            return video_tag_names;
        }

        public void setVideo_tag_names(List<String> video_tag_names) {
            this.video_tag_names = video_tag_names;
        }

        public String getAuthor_img() {
            return author_img;
        }

        public void setAuthor_img(String author_img) {
            this.author_img = author_img;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public String getImg_url_16x9() {
            return img_url_16x9;
        }

        public void setImg_url_16x9(String img_url_16x9) {
            this.img_url_16x9 = img_url_16x9;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }

        public String getVideo_app_title() {
            return video_app_title;
        }

        public void setVideo_app_title(String video_app_title) {
            this.video_app_title = video_app_title;
        }

        public String getPublish_time() {
            return publish_time;
        }

        public void setPublish_time(String publish_time) {
            this.publish_time = publish_time;
        }

        public String getVideo_source() {
            return video_source;
        }

        public void setVideo_source(String video_source) {
            this.video_source = video_source;
        }

        public String getSummary() {
            return summary;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }

        public String getVideo_qrcode() {
            return video_qrcode;
        }

        public void setVideo_qrcode(String video_qrcode) {
            this.video_qrcode = video_qrcode;
        }

        public String getImg_url_4x3() {
            return img_url_4x3;
        }

        public void setImg_url_4x3(String img_url_4x3) {
            this.img_url_4x3 = img_url_4x3;
        }

        public int getReply_count() {
            return reply_count;
        }

        public void setReply_count(int reply_count) {
            this.reply_count = reply_count;
        }

        public int getBiz_id() {
            return biz_id;
        }

        public void setBiz_id(int biz_id) {
            this.biz_id = biz_id;
        }

        public int getAuthor_id() {
            return author_id;
        }

        public void setAuthor_id(int author_id) {
            this.author_id = author_id;
        }

        public List<String> getSeries_ids() {
            return series_ids;
        }

        public void setSeries_ids(List<String> series_ids) {
            this.series_ids = series_ids;
        }
    }
}
