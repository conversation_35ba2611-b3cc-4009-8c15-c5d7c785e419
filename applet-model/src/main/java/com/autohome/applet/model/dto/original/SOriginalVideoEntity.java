package com.autohome.applet.model.dto.original;

import java.util.List;

/**
 * 原创视频实体
 * <AUTHOR>
 * @date 2021/05/13 12:30
 **/
public class SOriginalVideoEntity {

    /**
     * 视频id
     */
    private int id;

    /**
     * 标题
     */
    private String title;

    /**
     *  短标题
     */
    private String shorttitle;

    /**
     * App标题
     */
    private String apptitle;

    /**
     * 代表图 4:3 带按钮
     */
    private String coverimage;

    /**
     * 代表图 16:9 不带按钮
     */
    private String coverimage2;

    /**
     * 代表图 16:9 带按钮
     */
    private String coverimage3;

    /**
     * 大图
     */
    private String coverimage4;

    /**
     * 作者信息
     */
    private Author author;

    /**
     * 视频时长，单位秒
     */
    private String duration;

    /**
     * 简介
     */
    private String description;

    /**
     * 播放数
     */
    private int playcount;

    /**
     * 评论数
     */
    private int commentcount;

    /**
     * 是否禁止评论
     */
    private int disablecomment;

    /**
     * 发布时间
     */
    private String publishtime;

    /**
     * 自建视频编号，当此属性不为空时为自建视频，否则为优酷视频
     */
    private String videoid;

    /**
     * 播放页分享二维码图片URL
     */
    private String qrcode;

    /**
     * 分页标识
     */
    private String pageid;

    /**
     * 关联车系编号
     */
    private List<Integer> relationseriesids;

    /**
     * 相关标签
     */
    private List<VideoTag> tags;

    /**
     * 期数
     */
    private Integer episode;

    /**
     * 重点ip视频类型（1-正片；2-预告；3-花絮）
     */
    private Integer coreiptype;

    /**
     * 视频类别
     */
    private Integer video_category;

    private Integer verify;
    
    public Integer getVerify() {
        return verify;
    }
    
    public void setVerify(Integer verify) {
        this.verify = verify;
    }
    
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getShorttitle() {
        return shorttitle;
    }

    public void setShorttitle(String shorttitle) {
        this.shorttitle = shorttitle;
    }

    public String getApptitle() {
        return apptitle;
    }

    public void setApptitle(String apptitle) {
        this.apptitle = apptitle;
    }

    public String getCoverimage() {
        return coverimage;
    }

    public void setCoverimage(String coverimage) {
        this.coverimage = coverimage;
    }

    public String getCoverimage2() {
        return coverimage2;
    }

    public void setCoverimage2(String coverimage2) {
        this.coverimage2 = coverimage2;
    }

    public String getCoverimage3() {
        return coverimage3;
    }

    public void setCoverimage3(String coverimage3) {
        this.coverimage3 = coverimage3;
    }

    public String getCoverimage4() {
        return coverimage4;
    }

    public void setCoverimage4(String coverimage4) {
        this.coverimage4 = coverimage4;
    }

    public Author getAuthor() {
        return author;
    }

    public void setAuthor(Author author) {
        this.author = author;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getPlaycount() {
        return playcount;
    }

    public void setPlaycount(int playcount) {
        this.playcount = playcount;
    }

    public int getCommentcount() {
        return commentcount;
    }

    public void setCommentcount(int commentcount) {
        this.commentcount = commentcount;
    }

    public int getDisablecomment() {
        return disablecomment;
    }

    public void setDisablecomment(int disablecomment) {
        this.disablecomment = disablecomment;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getVideoid() {
        return videoid;
    }

    public void setVideoid(String videoid) {
        this.videoid = videoid;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public String getPageid() {
        return pageid;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public List<Integer> getRelationseriesids() {
        return relationseriesids;
    }

    public void setRelationseriesids(List<Integer> relationseriesids) {
        this.relationseriesids = relationseriesids;
    }

    public List<VideoTag> getTags() {
        return tags;
    }

    public void setTags(List<VideoTag> tags) {
        this.tags = tags;
    }

    public Integer getEpisode() {
        return episode;
    }

    public void setEpisode(Integer episode) {
        this.episode = episode;
    }

    public Integer getCoreiptype() {
        return coreiptype;
    }

    public void setCoreiptype(Integer coreiptype) {
        this.coreiptype = coreiptype;
    }

    public Integer getVideo_category() {
        return video_category;
    }

    public void setVideo_category(Integer video_category) {
        this.video_category = video_category;
    }

    /**
     * 作者信息
     */
    public static class Author {

        private int userid;

        private String nickname;

        private String avatar;

        private String description;

        private int usertype;
        
        private Integer videocount;
    
        public Integer getVideocount() {
            return videocount;
        }
    
        public void setVideocount(Integer videocount) {
            this.videocount = videocount;
        }
    
        public int getUserid() {
            return userid;
        }

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public int getUsertype() {
            return usertype;
        }

        public void setUsertype(int usertype) {
            this.usertype = usertype;
        }
    }

    /**
     * 视频标签
     */
    public static class VideoTag {

        private int id;

        private String name;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
