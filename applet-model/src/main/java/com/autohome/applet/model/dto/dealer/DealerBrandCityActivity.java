package com.autohome.applet.model.dto.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DealerBrandCityActivity {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 活动图片
     */
    private String image;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 品牌id列表
     */
    @JsonProperty("brandIds")
    private List<Integer> brandIdList;

    /**
     * item
     */
    private Double item;

    /**
     * 活动类型
     */
    private Integer type;

    /**
     * 小程序app_id
     */
    private String appId;

    /**
     * 标签文案
     */
    private String labelCopy;

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public List<Integer> getBrandIdList() {
        return brandIdList;
    }

    public void setBrandIdList(List<Integer> brandIdList) {
        this.brandIdList = brandIdList;
    }

    public Double getItem() {
        return item;
    }

    public void setItem(Double item) {
        this.item = item;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getLabelCopy() {
        return labelCopy;
    }

    public void setLabelCopy(String labelCopy) {
        this.labelCopy = labelCopy;
    }
}
