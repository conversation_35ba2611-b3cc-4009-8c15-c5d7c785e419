package com.autohome.applet.model.dto.cms;

import java.util.Date;

public class ChejiahaoDetailResultAuthorUserglory {

    private String appurl;
    private Date awarddate;
    private Date created_stime;
    private String glorycity;
    private String gloryicon;
    private int gloryid;
    private String gloryname;
    private String introduction;
    private int is_del;
    private int isshowcity;
    private Date modified_stime;
    private String murl;
    private String nickname;
    private int ordernum;
    private int parentid;
    private String pcurl;
    private long userid;

    public void setAppurl(String appurl) {
        this.appurl = appurl;
    }

    public String getAppurl() {
        return appurl;
    }

    public void setAwarddate(Date awarddate) {
        this.awarddate = awarddate;
    }

    public Date getAwarddate() {
        return awarddate;
    }

    public void setCreated_stime(Date created_stime) {
        this.created_stime = created_stime;
    }

    public Date getCreated_stime() {
        return created_stime;
    }

    public void setGlorycity(String glorycity) {
        this.glorycity = glorycity;
    }

    public String getGlorycity() {
        return glorycity;
    }

    public void setGloryicon(String gloryicon) {
        this.gloryicon = gloryicon;
    }

    public String getGloryicon() {
        return gloryicon;
    }

    public void setGloryid(int gloryid) {
        this.gloryid = gloryid;
    }

    public int getGloryid() {
        return gloryid;
    }

    public void setGloryname(String gloryname) {
        this.gloryname = gloryname;
    }

    public String getGloryname() {
        return gloryname;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIs_del(int is_del) {
        this.is_del = is_del;
    }

    public int getIs_del() {
        return is_del;
    }

    public void setIsshowcity(int isshowcity) {
        this.isshowcity = isshowcity;
    }

    public int getIsshowcity() {
        return isshowcity;
    }

    public void setModified_stime(Date modified_stime) {
        this.modified_stime = modified_stime;
    }

    public Date getModified_stime() {
        return modified_stime;
    }

    public void setMurl(String murl) {
        this.murl = murl;
    }

    public String getMurl() {
        return murl;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setOrdernum(int ordernum) {
        this.ordernum = ordernum;
    }

    public int getOrdernum() {
        return ordernum;
    }

    public void setParentid(int parentid) {
        this.parentid = parentid;
    }

    public int getParentid() {
        return parentid;
    }

    public void setPcurl(String pcurl) {
        this.pcurl = pcurl;
    }

    public String getPcurl() {
        return pcurl;
    }

    public void setUserid(long userid) {
        this.userid = userid;
    }

    public long getUserid() {
        return userid;
    }

}