package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BusinessTag {

    private int id;

    private int type;

    private String name;

    private String value;

    private int sort;

    private int state;

    @JsonProperty("modifyTime")
    private String modifytime;

    @JsonProperty("createTime")
    private String createtime;


    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getSort() {
        return sort;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setModifytime(String modifytime) {
        this.modifytime = modifytime;
    }

    public String getModifytime() {
        return modifytime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getCreatetime() {
        return createtime;
    }

}
