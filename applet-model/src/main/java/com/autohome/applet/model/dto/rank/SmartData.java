package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SmartData implements SeriesInfoSetable{
    private String index5Id;
    private String index5;
    private String indexShow;
    private int loudIndex;
    private String weekStart;
    private String weekEnd;
    private int isBusiness;
    private int seriesNum;
    private String showSeriesId;
    private String showSeriesName;
    private String showUrl;
    private String showTitle;
    private int seriesState;
    private String onTime;
    private String img;

    @Override
    public int getSeriesId() {
        return showSeriesId != null ? Integer.parseInt(showSeriesId) : 0;
    }

    @Override
    public void setBrandId(int brandId) {

    }

    @Override
    public void setSeriespnglogo(String logo) {
        this.img = logo;
    }

    @Override
    public void setMinPrice(int minPrice) {

    }

    @Override
    public void setMaxPrice(int maxPrice) {

    }

    @Override
    public void setSeriesName(String seriesName) {
        this.showSeriesName = seriesName;
    }

    public static class Result {

        private String dt;
        private List<SmartData> data;

        public String getDt() {
            return dt;
        }

        public void setDt(String dt) {
            this.dt = dt;
        }

        public List<SmartData> getData() {
            return data;
        }

        public void setData(List<SmartData> data) {
            this.data = data;
        }
    }

    public String getIndex5Id() {
        return index5Id;
    }

    public void setIndex5Id(String index5Id) {
        this.index5Id = index5Id;
    }

    public String getIndex5() {
        return index5;
    }

    public void setIndex5(String index5) {
        this.index5 = index5;
    }

    public String getIndexShow() {
        return indexShow;
    }

    public void setIndexShow(String indexShow) {
        this.indexShow = indexShow;
    }

    public int getLoudIndex() {
        return loudIndex;
    }

    public void setLoudIndex(int loudIndex) {
        this.loudIndex = loudIndex;
    }

    public String getWeekStart() {
        return weekStart;
    }

    public void setWeekStart(String weekStart) {
        this.weekStart = weekStart;
    }

    public String getWeekEnd() {
        return weekEnd;
    }

    public void setWeekEnd(String weekEnd) {
        this.weekEnd = weekEnd;
    }

    public int getIsBusiness() {
        return isBusiness;
    }

    public void setIsBusiness(int isBusiness) {
        this.isBusiness = isBusiness;
    }

    public int getSeriesNum() {
        return seriesNum;
    }

    public void setSeriesNum(int seriesNum) {
        this.seriesNum = seriesNum;
    }

    public String getShowSeriesId() {
        return showSeriesId;
    }

    public void setShowSeriesId(String showSeriesId) {
        this.showSeriesId = showSeriesId;
    }

    public String getShowSeriesName() {
        return showSeriesName;
    }

    public void setShowSeriesName(String showSeriesName) {
        this.showSeriesName = showSeriesName;
    }

    public String getShowUrl() {
        return showUrl;
    }

    public void setShowUrl(String showUrl) {
        this.showUrl = showUrl;
    }

    public String getShowTitle() {
        return showTitle;
    }

    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle;
    }

    public int getSeriesState() {
        return seriesState;
    }

    public void setSeriesState(int seriesState) {
        this.seriesState = seriesState;
    }

    public String getOnTime() {
        return onTime;
    }

    public void setOnTime(String onTime) {
        this.onTime = onTime;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }
}
