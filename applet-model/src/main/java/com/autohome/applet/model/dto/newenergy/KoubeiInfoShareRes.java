package com.autohome.applet.model.dto.newenergy;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
public class KoubeiInfoShareRes extends BaseEntity {
    private Result result;

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result{
        private List<KouBeiShareContentDto> list;
        private Integer pageIndex;
        private Integer pagecount;
        private Integer rowcount;

        public List<KouBeiShareContentDto> getList() {
            return list;
        }

        public void setList(List<KouBeiShareContentDto> list) {
            this.list = list;
        }

        public Integer getPageIndex() {
            return pageIndex;
        }

        public void setPageIndex(Integer pageIndex) {
            this.pageIndex = pageIndex;
        }

        public Integer getPagecount() {
            return pagecount;
        }

        public void setPagecount(Integer pagecount) {
            this.pagecount = pagecount;
        }

        public Integer getRowcount() {
            return rowcount;
        }

        public void setRowcount(Integer rowcount) {
            this.rowcount = rowcount;
        }
    }

}
