package com.autohome.applet.model.dto.wsmessage;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class NewCarSeriesSubscriptionRequest {

    @NotNull(message = "缺少type")
    @Min(value = 1,message = "请输入正确的订阅类型")
    @Max(value = 2,message = "请输入正确的订阅类型")
    private Integer type;

    @NotNull(message = "缺少订阅类型")
    @Min(value = 1,message = "请输入正确的订阅类型")
    private Integer biztype;

    private String auth;

    @NotNull(message = "缺少车系id")
    @Min(value = 1,message = "请输入正确的车系id")
    private Integer seriesid;

    @NotBlank(message = "缺少openid")
    private String openid;

    @NotNull(message = "缺少渠道")
    @Min(value = 0,message = "请输入正确的渠道")
    private Integer sourceid;

    //渠道
    private Integer pm=5;
    //城市ID
    private Integer cityid;

   //用户小程序中唯一标识
    private String unionid;
}