package com.autohome.applet.model.dto.cms;

public class ChejiahaoDetailResultInfostatistics {

    private int detailpv;
    private int epv;
    private int favoritecount;
    private int imageclickcount;
    private long infoid;
    private String pageid;
    private int playcount;
    private int playduration;
    private int praisecount;
    private int pv;
    private int replycount;
    private int sharecount;
    private int uv;
    private int vv;

    public void setDetailpv(int detailpv) {
        this.detailpv = detailpv;
    }

    public int getDetailpv() {
        return detailpv;
    }

    public void setEpv(int epv) {
        this.epv = epv;
    }

    public int getEpv() {
        return epv;
    }

    public void setFavoritecount(int favoritecount) {
        this.favoritecount = favoritecount;
    }

    public int getFavoritecount() {
        return favoritecount;
    }

    public void setImageclickcount(int imageclickcount) {
        this.imageclickcount = imageclickcount;
    }

    public int getImageclickcount() {
        return imageclickcount;
    }

    public void setInfoid(long infoid) {
        this.infoid = infoid;
    }

    public long getInfoid() {
        return infoid;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setPlaycount(int playcount) {
        this.playcount = playcount;
    }

    public int getPlaycount() {
        return playcount;
    }

    public void setPlayduration(int playduration) {
        this.playduration = playduration;
    }

    public int getPlayduration() {
        return playduration;
    }

    public void setPraisecount(int praisecount) {
        this.praisecount = praisecount;
    }

    public int getPraisecount() {
        return praisecount;
    }

    public void setPv(int pv) {
        this.pv = pv;
    }

    public int getPv() {
        return pv;
    }

    public void setReplycount(int replycount) {
        this.replycount = replycount;
    }

    public int getReplycount() {
        return replycount;
    }

    public void setSharecount(int sharecount) {
        this.sharecount = sharecount;
    }

    public int getSharecount() {
        return sharecount;
    }

    public void setUv(int uv) {
        this.uv = uv;
    }

    public int getUv() {
        return uv;
    }

    public void setVv(int vv) {
        this.vv = vv;
    }

    public int getVv() {
        return vv;
    }

}