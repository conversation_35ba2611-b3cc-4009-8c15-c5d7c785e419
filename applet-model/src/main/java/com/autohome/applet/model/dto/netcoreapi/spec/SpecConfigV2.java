package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecConfigV2 {

    @JsonProperty("titlelist")
    private List<SpecParams.ValueItem> titleList;

    @JsonProperty("namelist")
    private List<OneTabName> nameList;

    @JsonProperty("clist")
    private List<CList> cList;

    public SpecConfigV2() {
    }

    public SpecConfigV2(List<SpecParams.ValueItem> titleList, List<OneTabName> nameList, List<CList> cList) {
        this.titleList = titleList;
        this.nameList = nameList;
        this.cList = cList;
    }

    public List<SpecParams.ValueItem> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<SpecParams.ValueItem> titleList) {
        this.titleList = titleList;
    }

    public List<OneTabName> getNameList() {
        return nameList;
    }

    public void setNameList(List<OneTabName> nameList) {
        this.nameList = nameList;
    }

    public List<CList> getcList() {
        return cList;
    }

    public void setcList(List<CList> cList) {
        this.cList = cList;
    }
}
