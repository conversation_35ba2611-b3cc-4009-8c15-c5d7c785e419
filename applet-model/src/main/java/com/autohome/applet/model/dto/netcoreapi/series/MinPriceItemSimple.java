package com.autohome.applet.model.dto.netcoreapi.series;

import java.util.List;

public class MinPriceItemSimple {

    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<MinPriceItemSimpleItem> list;

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getRowcount() {
        return rowcount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setList(List<MinPriceItemSimpleItem> list) {
        this.list = list;
    }

    public List<MinPriceItemSimpleItem> getList() {
        return list;
    }

}