package com.autohome.applet.model.dto.netcoreapi.series;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MinPriceItemSimpleItem {

    public long getCityId() {
        return cityId;
    }

    public void setCityId(long cityId) {
        this.cityId = cityId;
    }

    public long getMaxOriginalPrice() {
        return maxOriginalPrice;
    }

    public void setMaxOriginalPrice(long maxOriginalPrice) {
        this.maxOriginalPrice = maxOriginalPrice;
    }

    public long getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(long minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public int getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(int minPrice) {
        this.minPrice = minPrice;
    }

    public int getPriceOff() {
        return priceOff;
    }

    public void setPriceOff(int priceOff) {
        this.priceOff = priceOff;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    @JsonProperty("CityId")
    private long cityId;

    @JsonProperty("MaxOriginalPrice")
    private long maxOriginalPrice;

    @JsonProperty("MinOriginalPrice")
    private long minOriginalPrice;

    @JsonProperty("MinPrice")
    private int minPrice;

    @JsonProperty("PriceOff")
    private int priceOff;

    @JsonProperty("SeriesId")
    private int seriesId;

    @JsonProperty("SpecId")
    private int specId;


}