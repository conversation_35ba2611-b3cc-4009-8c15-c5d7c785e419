package com.autohome.applet.model.dto.netcoreapi.dealer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DealerInfo {
    private long dealerId;
    private long dealerInfoId;
    private int seriesId;
    private int specId;
    private int minNewsPrice;
    private long minOriginalPrice;
    private long maxOriginalPrice;
    private int promotionType;
    private int isOpenJiaSuDai;
    private boolean isAuthenticPrice;
    private double distance;
    private DealerInfoBaseOut dealerInfoBaseOut;
    private DealerInfoShowImageOut dealerShowImageOut;
    private DealerInfoNewsInfoOut newsInfoOut;
    private List<String> promotionConditions;
    private DealerInfoMaintainNewsOut maintainNewsOut;
    private String yphone;
    private int isSupplyDealer;
    private int hasVideo;
    private int isVirtualShop;
    private String virtualShowTips;
    private String kindStr;
    private long mainDealerId;
    private String mainDealerSimpleName;
    private int isFactory;
    private int isShow;
    private double serviceFeedbackScore;
    private int isMixed;
    private int isSupplyMinPrice;

    public void setDealerId(long dealerId) {
        this.dealerId = dealerId;
    }

    public long getDealerId() {
        return dealerId;
    }

    public void setDealerInfoId(long dealerInfoId) {
        this.dealerInfoId = dealerInfoId;
    }

    public long getDealerInfoId() {
        return dealerInfoId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setMinNewsPrice(int minNewsPrice) {
        this.minNewsPrice = minNewsPrice;
    }

    public int getMinNewsPrice() {
        return minNewsPrice;
    }

    public void setMinOriginalPrice(long minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public long getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMaxOriginalPrice(long maxOriginalPrice) {
        this.maxOriginalPrice = maxOriginalPrice;
    }

    public long getMaxOriginalPrice() {
        return maxOriginalPrice;
    }

    public void setPromotionType(int promotionType) {
        this.promotionType = promotionType;
    }

    public int getPromotionType() {
        return promotionType;
    }

    public void setIsOpenJiaSuDai(int isOpenJiaSuDai) {
        this.isOpenJiaSuDai = isOpenJiaSuDai;
    }

    public int getIsOpenJiaSuDai() {
        return isOpenJiaSuDai;
    }

    public void setIsAuthenticPrice(boolean isAuthenticPrice) {
        this.isAuthenticPrice = isAuthenticPrice;
    }

    public boolean getIsAuthenticPrice() {
        return isAuthenticPrice;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }

    public double getDistance() {
        return distance;
    }

    public void setDealerInfoBaseOut(DealerInfoBaseOut dealerInfoBaseOut) {
        this.dealerInfoBaseOut = dealerInfoBaseOut;
    }

    public DealerInfoBaseOut getDealerInfoBaseOut() {
        return dealerInfoBaseOut;
    }

    public void setDealerShowImageOut(DealerInfoShowImageOut dealerShowImageOut) {
        this.dealerShowImageOut = dealerShowImageOut;
    }

    public DealerInfoShowImageOut getDealerShowImageOut() {
        return dealerShowImageOut;
    }

    public void setNewsInfoOut(DealerInfoNewsInfoOut newsInfoOut) {
        this.newsInfoOut = newsInfoOut;
    }

    public DealerInfoNewsInfoOut getNewsInfoOut() {
        return newsInfoOut;
    }

    public void setPromotionConditions(List<String> promotionConditions) {
        this.promotionConditions = promotionConditions;
    }

    public List<String> getPromotionConditions() {
        return promotionConditions;
    }

    public void setMaintainNewsOut(DealerInfoMaintainNewsOut maintainNewsOut) {
        this.maintainNewsOut = maintainNewsOut;
    }

    public DealerInfoMaintainNewsOut getMaintainNewsOut() {
        return maintainNewsOut;
    }

    public void setYphone(String yphone) {
        this.yphone = yphone;
    }

    public String getYphone() {
        return yphone;
    }

    public void setIsSupplyDealer(int isSupplyDealer) {
        this.isSupplyDealer = isSupplyDealer;
    }

    public int getIsSupplyDealer() {
        return isSupplyDealer;
    }

    public void setHasVideo(int hasVideo) {
        this.hasVideo = hasVideo;
    }

    public int getHasVideo() {
        return hasVideo;
    }

    public void setIsVirtualShop(int isVirtualShop) {
        this.isVirtualShop = isVirtualShop;
    }

    public int getIsVirtualShop() {
        return isVirtualShop;
    }

    public void setVirtualShowTips(String virtualShowTips) {
        this.virtualShowTips = virtualShowTips;
    }

    public String getVirtualShowTips() {
        return virtualShowTips;
    }

    public void setKindStr(String kindStr) {
        this.kindStr = kindStr;
    }

    public String getKindStr() {
        return kindStr;
    }

    public void setMainDealerId(long mainDealerId) {
        this.mainDealerId = mainDealerId;
    }

    public long getMainDealerId() {
        return mainDealerId;
    }

    public void setMainDealerSimpleName(String mainDealerSimpleName) {
        this.mainDealerSimpleName = mainDealerSimpleName;
    }

    public String getMainDealerSimpleName() {
        return mainDealerSimpleName;
    }

    public void setIsFactory(int isFactory) {
        this.isFactory = isFactory;
    }

    public int getIsFactory() {
        return isFactory;
    }

    public void setIsShow(int isShow) {
        this.isShow = isShow;
    }

    public int getIsShow() {
        return isShow;
    }

    public void setServiceFeedbackScore(double serviceFeedbackScore) {
        this.serviceFeedbackScore = serviceFeedbackScore;
    }

    public double getServiceFeedbackScore() {
        return serviceFeedbackScore;
    }

    public void setIsMixed(int isMixed) {
        this.isMixed = isMixed;
    }

    public int getIsMixed() {
        return isMixed;
    }

    public void setIsSupplyMinPrice(int isSupplyMinPrice) {
        this.isSupplyMinPrice = isSupplyMinPrice;
    }

    public int getIsSupplyMinPrice() {
        return isSupplyMinPrice;
    }

}