package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DealerPromotion {

    private int newsid;

    private int newstemplateid;

    private String title;

    private String createtime;

    private String enddate;

    private String publishtime;

    private String imgurl;

    private String focusimgurl;

    private List<String> imglist;

    private int seriesid;
    private String activitytime;

    public String getActivitytime() {
        return FormatDate.diffTimeCompare(FormatDate.getCurrentDate(), this.enddate);
    }

    public void setActivitytime(String activitytime) {
        this.activitytime = activitytime;
    }

    public void setNewsid(int newsid) {
        this.newsid = newsid;
    }

    public int getNewsid() {
        return newsid;
    }

    public void setNewstemplateid(int newstemplateid) {
        this.newstemplateid = newstemplateid;
    }

    public int getNewstemplateid() {
        return newstemplateid;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public String getEnddate() {
        return enddate;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        if (publishtime.indexOf("Date") > 0) {
            return FormatDate.dateToStr(new Date(Long.parseLong(publishtime.replace("/Date(", "").replace("+0800)/", ""))), "yyyy-MM-dd HH:mm:ss");
        }
        return publishtime;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl;
    }

    public String getImgurl() {
        if (StringUtil.isNotNullAndWhiteSpace(imgurl)) {
            return imgurl.replace("http://", "https://");
        }
        return imgurl;
    }

    public void setFocusimgurl(String focusimgurl) {
        this.focusimgurl = focusimgurl;
    }

    public String getFocusimgurl() {
        if (StringUtil.isNotNullAndWhiteSpace(focusimgurl)) {
            return focusimgurl.replace("http://", "https://");
        }
        return focusimgurl;
    }

    public void setImglist(List<String> imglist) {
        this.imglist = imglist;
    }

    public List<String> getImglist() {
        List<String> newImgList = new ArrayList<>();
        if (imglist != null && imglist.size() > 0) {
            for (String img : imglist) {
                if (StringUtil.isNotNullAndWhiteSpace(img)) {
                    newImgList.add(img.replace("http://", "https://"));
                }
            }
        }
        return newImgList;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

}
