package com.autohome.applet.model.dto;

public class Plate {
    private int id;

    private String letter;
    private String shortname;

    private String numberplate;

    private String province;

    private String city;

    private int provinceId;

    private int cityId;

    public int getCapital() {
        return capital;
    }

    public void setCapital(int capital) {
        this.capital = capital;
    }

    private int capital;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getShortname() {
        return shortname;
    }

    public void setShortname(String shortname) {
        this.shortname = shortname;
    }

    public String getLetter() {
        return letter;
    }

    public void setLetter(String letter) {
        this.letter = letter;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getNumberplate() {
        return numberplate;
    }

    public void setNumberplate(String numberplate) {
        this.numberplate = numberplate;
    }

    public int getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(int provinceId) {
        this.provinceId = provinceId;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }


    public Plate() {}

    public Plate(int id, String shortname, String letter, String province, String city, String numberplate, int provinceId, int cityId, int capital) {
        this.id = id;
        this.shortname = shortname;
        this.letter = letter;
        this.province = province;
        this.city = city;
        this.numberplate = numberplate;
        this.provinceId = provinceId;
        this.cityId = cityId;
        this.capital = capital;
    }

    @Override
    public String toString() {
        return "Plate{" +
                "id=" + id +
                ", letter='" + letter + '\'' +
                ", shortname='" + shortname + '\'' +
                ", numberplate='" + numberplate + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", provinceId=" + provinceId +
                ", cityId=" + cityId +
                ", capital=" + capital +
                '}';
    }
}
