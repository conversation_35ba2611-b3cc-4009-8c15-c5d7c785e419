package com.autohome.applet.model.dto.honor;

/**
 * <AUTHOR>
 */
public class PlatformPostRequest<T> {
    private Endpoint endpoint;
    private Inquire<T> inquire;
    private Session session;

    public Endpoint getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Endpoint endpoint) {
        this.endpoint = endpoint;
    }

    public Inquire<T> getInquire() {
        return inquire;
    }

    public void setInquire(Inquire<T> inquire) {
        this.inquire = inquire;
    }

    public Session getSession() {
        return session;
    }

    public void setSession(Session session) {
        this.session = session;
    }

    public static class Endpoint {
        private Device device;
        private Auth auth;
        private String locale;
        private String countryCode;
        private String timeZone;
        private String localTime;

        public Device getDevice() {
            return device;
        }

        public void setDevice(Device device) {
            this.device = device;
        }

        public Auth getAuth() {
            return auth;
        }

        public void setAuth(Auth auth) {
            this.auth = auth;
        }

        public String getLocale() {
            return locale;
        }

        public void setLocale(String locale) {
            this.locale = locale;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getTimeZone() {
            return timeZone;
        }

        public void setTimeZone(String timeZone) {
            this.timeZone = timeZone;
        }

        public String getLocalTime() {
            return localTime;
        }

        public void setLocalTime(String localTime) {
            this.localTime = localTime;
        }
    }

    public static class Auth {
        private String apiAccessToken;

        public String getApiAccessToken() {
            return apiAccessToken;
        }

        public void setApiAccessToken(String apiAccessToken) {
            this.apiAccessToken = apiAccessToken;
        }
    }

    public static class Device {
        private BaseDevice base;
        private PresentationDevice presentation;

        public BaseDevice getBase() {
            return base;
        }

        public void setBase(BaseDevice base) {
            this.base = base;
        }

        public PresentationDevice getPresentation() {
            return presentation;
        }

        public void setPresentation(PresentationDevice presentation) {
            this.presentation = presentation;
        }
    }

    public static class BaseDevice {
        private String deviceId;
        private String deviceType;

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }
    }

    public static class PresentationDevice {
        private String screenOrientation;
        private String net;
        private Location location;

        public String getScreenOrientation() {
            return screenOrientation;
        }

        public void setScreenOrientation(String screenOrientation) {
            this.screenOrientation = screenOrientation;
        }

        public String getNet() {
            return net;
        }

        public void setNet(String net) {
            this.net = net;
        }

        public Location getLocation() {
            return location;
        }

        public void setLocation(Location location) {
            this.location = location;
        }
    }

    public static class Location {
        private String longitude;
        private String latitude;
        private String locationSystem;

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getLocationSystem() {
            return locationSystem;
        }

        public void setLocationSystem(String locationSystem) {
            this.locationSystem = locationSystem;
        }
    }

    public static class Inquire<T> {
        private String inquireId;
        private Intent<T> intent;

        public String getInquireId() {
            return inquireId;
        }

        public void setInquireId(String inquireId) {
            this.inquireId = inquireId;
        }

        public Intent<T> getIntent() {
            return intent;
        }

        public void setIntent(Intent<T> intent) {
            this.intent = intent;
        }
    }

    public static class Intent<T> {
        private String serviceId;
        private String intentName;
        private String status;
        private T slots;

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        public String getIntentName() {
            return intentName;
        }

        public void setIntentName(String intentName) {
            this.intentName = intentName;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public T getSlots() {
            return slots;
        }

        public void setSlots(T slots) {
            this.slots = slots;
        }
    }

    public static class Session {
        private String attributes;
        private String selectedItem;

        public String getAttributes() {
            return attributes;
        }

        public void setAttributes(String attributes) {
            this.attributes = attributes;
        }

        public String getSelectedItem() {
            return selectedItem;
        }

        public void setSelectedItem(String selectedItem) {
            this.selectedItem = selectedItem;
        }
    }
}
