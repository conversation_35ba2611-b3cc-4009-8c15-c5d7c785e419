package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 暂无报价
 * <AUTHOR>
 */
public class SpecMinPriceDto2 {
    @JsonProperty("seriesId")
    private int seriesId;

    @JsonProperty("specId")
    private int specId;

    @JsonProperty("seriesName")
    private String seriesName;

    @JsonProperty("specName")
    private String specName;

    @JsonProperty("value")
    private String value;


    public SpecMinPriceDto2() {
    }

    public SpecMinPriceDto2(int seriesId, int specId, String seriesName, String specName, String value) {
        this.seriesId = seriesId;
        this.specId = specId;
        this.seriesName = seriesName;
        this.specName = specName;
        this.value = value;
    }

    public static SpecMinPriceDto2 by(SpecBaseInfo specInfo, String value) {
        return new SpecMinPriceDto2(
                specInfo.getSeriesId(),
                specInfo.getSpecId(),
                specInfo.getSeriesName(),
                specInfo.getSpecName(),
                value
        );
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
