package com.autohome.applet.model.dto.netcoreapi.ics;

import java.util.Date;

public class Article {

    private int id;
    private String title;
    private String shorttitle;
    private String img;
    private String publishtime;
    private boolean isSerializeOver;
    private int serializeNum;
    private String source;
    private int editor;
    private int class1;
    private String contentKeywords;
    private String class1name;
    private int class2;
    private String class2name;
    private String dir;
    private String content;
    private int pagesCount;
    private int replyCount;
    private int clickCount;
    private String articleNavigation;
    private String type;
    private long provinceid;
    private long cityid;
    private int countyid;
    private int serializeStartPage;
    private Date lastUpdateTime;
    private boolean isCloseComment;
    private int autoShowId;
    private boolean isPublish;
    private String locationProvinceName;
    private String locationCityName;

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setShorttitle(String shorttitle) {
        this.shorttitle = shorttitle;
    }

    public String getShorttitle() {
        return shorttitle;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getImg() {
        return img;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setIsSerializeOver(boolean isSerializeOver) {
        this.isSerializeOver = isSerializeOver;
    }

    public boolean getIsSerializeOver() {
        return isSerializeOver;
    }

    public void setSerializeNum(int serializeNum) {
        this.serializeNum = serializeNum;
    }

    public int getSerializeNum() {
        return serializeNum;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setEditor(int editor) {
        this.editor = editor;
    }

    public int getEditor() {
        return editor;
    }

    public void setClass1(int class1) {
        this.class1 = class1;
    }

    public int getClass1() {
        return class1;
    }

    public void setContentKeywords(String contentKeywords) {
        this.contentKeywords = contentKeywords;
    }

    public String getContentKeywords() {
        return contentKeywords;
    }

    public void setClass1name(String class1name) {
        this.class1name = class1name;
    }

    public String getClass1name() {
        return class1name;
    }

    public void setClass2(int class2) {
        this.class2 = class2;
    }

    public int getClass2() {
        return class2;
    }

    public void setClass2name(String class2name) {
        this.class2name = class2name;
    }

    public String getClass2name() {
        return class2name;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getDir() {
        return dir;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setPagesCount(int pagesCount) {
        this.pagesCount = pagesCount;
    }

    public int getPagesCount() {
        return pagesCount;
    }

    public void setReplyCount(int replyCount) {
        this.replyCount = replyCount;
    }

    public int getReplyCount() {
        return replyCount;
    }

    public void setClickCount(int clickCount) {
        this.clickCount = clickCount;
    }

    public int getClickCount() {
        return clickCount;
    }

    public void setArticleNavigation(String articleNavigation) {
        this.articleNavigation = articleNavigation;
    }

    public String getArticleNavigation() {
        return articleNavigation;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setProvinceid(long provinceid) {
        this.provinceid = provinceid;
    }

    public long getProvinceid() {
        return provinceid;
    }

    public void setCityid(long cityid) {
        this.cityid = cityid;
    }

    public long getCityid() {
        return cityid;
    }

    public void setCountyid(int countyid) {
        this.countyid = countyid;
    }

    public int getCountyid() {
        return countyid;
    }

    public void setSerializeStartPage(int serializeStartPage) {
        this.serializeStartPage = serializeStartPage;
    }

    public int getSerializeStartPage() {
        return serializeStartPage;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setIsCloseComment(boolean isCloseComment) {
        this.isCloseComment = isCloseComment;
    }

    public boolean getIsCloseComment() {
        return isCloseComment;
    }

    public void setAutoShowId(int autoShowId) {
        this.autoShowId = autoShowId;
    }

    public int getAutoShowId() {
        return autoShowId;
    }

    public void setIsPublish(boolean isPublish) {
        this.isPublish = isPublish;
    }

    public boolean getIsPublish() {
        return isPublish;
    }

    public void setLocationProvinceName(String locationProvinceName) {
        this.locationProvinceName = locationProvinceName;
    }

    public String getLocationProvinceName() {
        return locationProvinceName;
    }

    public void setLocationCityName(String locationCityName) {
        this.locationCityName = locationCityName;
    }

    public String getLocationCityName() {
        return locationCityName;
    }

}