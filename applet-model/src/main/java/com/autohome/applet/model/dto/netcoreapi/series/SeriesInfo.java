package com.autohome.applet.model.dto.netcoreapi.series;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class SeriesInfo {

    public int picnum;

    //@JsonSerialize(nullsUsing = NullObjectToEmptyObjectSerializer.class)
    //public VRData vrWG;
    public Object vrWG;

    //@JsonSerialize(nullsUsing = NullObjectToEmptyObjectSerializer.class)
    public Object vrNS;
    /// <summary>
    /// 车系id
    /// </summary>
    public int id;

    /// <summary>
    /// 车系名称
    /// </summary>
    public String name;

    /// <summary>
    /// 车系logo
    /// </summary>
    public String logo;

    /// <summary>
    /// 级别分类
    /// </summary>
    public String type;

    /// <summary>
    /// 价格区间
    /// </summary>
    public String price;


    /// <summary>
    /// 在售车型数量
    /// </summary>
    public int speccount;

    /// <summary>
    /// 排序值
    /// </summary>
    public int ordervalue;

    /// <summary>
    /// 上市时间
    /// </summary>
    public String publictime;

    /// <summary>
    /// 品牌id
    /// </summary>
    public int brandid;

    /// <summary>
    /// 品牌名称
    /// </summary>
    public String brandname;

    /// <summary>
    /// 厂商id
    /// </summary>
    public int fctid;

    /// <summary>
    /// 厂商名称
    /// </summary>
    public String fctname;

    /// <summary>
    /// 级别id
    /// </summary>
    public int levelid;

    /// <summary>
    /// 级别名称
    /// </summary>
    public String levelname;

    /// <summary>
    /// 油耗
    /// </summary>
    public String fuelconsumption;
    /// <summary>
    /// 车系状态
    /// </summary>
    public int state;

    /// <summary>
    /// 车主价格数量
    /// </summary>
    public int chezhuCount;

    public int cartype;

    public String whitelog;

    @JsonProperty("EvalScore")
    public String evalScore;

    @JsonProperty("LoadSeriesPRCTypeStr")
    public List<String> loadSeriesPRCTypeStr;

    public int newenergy;

    public String butiePrice;

    public String brandlog;

    //透明图
    public String transparentphone;

    //最大价格
    public int fprice;

    //最小价格
    public int lprice;

    //区间
    @JsonProperty("JXSprice")
    public String jxSprice;

    //最大降幅
    @JsonProperty("MaxCPrice")
    public String maxCPrice;

    public String kuaichong;

    public String manchong;

    public String rongliang;

    public String xuhang;

    public int getPicnum() {
        return picnum;
    }

    public void setPicnum(int picnum) {
        this.picnum = picnum;
    }

    public Object getVrWG() {
        return vrWG;
    }

    public void setVrWG(Object vrWG) {
        this.vrWG = vrWG;
    }

    public Object getVrNS() {
        return vrNS;
    }

    public void setVrNS(Object vrNS) {
        this.vrNS = vrNS;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getSpeccount() {
        return speccount;
    }

    public void setSpeccount(int speccount) {
        this.speccount = speccount;
    }

    public int getOrdervalue() {
        return ordervalue;
    }

    public void setOrdervalue(int ordervalue) {
        this.ordervalue = ordervalue;
    }

    public String getPublictime() {
        return publictime;
    }

    public void setPublictime(String publictime) {
        this.publictime = publictime;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public int getFctid() {
        return fctid;
    }

    public void setFctid(int fctid) {
        this.fctid = fctid;
    }

    public String getFctname() {
        return fctname;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public int getLevelid() {
        return levelid;
    }

    public void setLevelid(int levelid) {
        this.levelid = levelid;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public String getFuelconsumption() {
        return fuelconsumption;
    }

    public void setFuelconsumption(String fuelconsumption) {
        this.fuelconsumption = fuelconsumption;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getChezhuCount() {
        return chezhuCount;
    }

    public void setChezhuCount(int chezhuCount) {
        this.chezhuCount = chezhuCount;
    }

    public int getCartype() {
        return cartype;
    }

    public void setCartype(int cartype) {
        this.cartype = cartype;
    }

    public String getWhitelog() {
        return whitelog;
    }

    public void setWhitelog(String whitelog) {
        this.whitelog = whitelog;
    }

    public String getEvalScore() {
        return evalScore;
    }

    public void setEvalScore(String evalScore) {
        this.evalScore = evalScore;
    }

    public List<String> getLoadSeriesPRCTypeStr() {
        return loadSeriesPRCTypeStr;
    }

    public void setLoadSeriesPRCTypeStr(List<String> loadSeriesPRCTypeStr) {
        this.loadSeriesPRCTypeStr = loadSeriesPRCTypeStr;
    }

    public int getNewenergy() {
        return newenergy;
    }

    public void setNewenergy(int newenergy) {
        this.newenergy = newenergy;
    }

    public String getButiePrice() {
        return butiePrice;
    }

    public void setButiePrice(String butiePrice) {
        this.butiePrice = butiePrice;
    }

    public String getBrandlog() {
        return brandlog;
    }

    public void setBrandlog(String brandlog) {
        this.brandlog = brandlog;
    }

    public String getTransparentphone() {
        return transparentphone;
    }

    public void setTransparentphone(String transparentphone) {
        this.transparentphone = transparentphone;
    }

    public int getFprice() {
        return fprice;
    }

    public void setFprice(int fprice) {
        this.fprice = fprice;
    }

    public int getLprice() {
        return lprice;
    }

    public void setLprice(int lprice) {
        this.lprice = lprice;
    }

    public String getJxSprice() {
        return jxSprice;
    }

    public void setJXSprice(String jXSprice) {
        this.jxSprice = jXSprice;
    }

    public String getMaxCPrice() {
        return maxCPrice;
    }

    public void setMaxCPrice(String maxCPrice) {
        this.maxCPrice = maxCPrice;
    }

    public String getKuaichong() {
        return kuaichong;
    }

    public void setKuaichong(String kuaichong) {
        this.kuaichong = kuaichong;
    }

    public String getManchong() {
        return manchong;
    }

    public void setManchong(String manchong) {
        this.manchong = manchong;
    }

    public String getRongliang() {
        return rongliang;
    }

    public void setRongliang(String rongliang) {
        this.rongliang = rongliang;
    }

    public String getXuhang() {
        return xuhang;
    }

    public void setXuhang(String xuhang) {
        this.xuhang = xuhang;
    }


}
