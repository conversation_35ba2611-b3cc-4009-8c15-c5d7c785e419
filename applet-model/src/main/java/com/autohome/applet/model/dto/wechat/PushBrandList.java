package com.autohome.applet.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 微信搜一搜-汽车品牌推送
 */
@Builder
@Data
public class PushBrandList {

    @JsonProperty("car_brand_list")
    private List<CarBrandList> carBrandListList;

    @JsonProperty("is_test")
    private int isTest;

    @Builder
    @Data
    public static class CarBrandList {
        @JsonProperty("brand_id")
        private String brandId;

        @JsonProperty("brand_name")
        private String brandName;

        @JsonProperty("more_jump_info")
        private JumpInfo moreJumpInfo;

        @JsonProperty("hot_jump_info")
        private JumpInfo hotJumpInfo;

        @JsonProperty("chexi_ids")
        private List<String> chexiIds;

        @JsonProperty("status")
        private int status;
    }

    @Builder
    @Data
    public static class JumpInfo {
        @JsonProperty("jump_type")
        private int jumpType;

        @JsonProperty("appid")
        private String appid;

        @JsonProperty("service_url")
        private String serviceUrl;
    }

}
