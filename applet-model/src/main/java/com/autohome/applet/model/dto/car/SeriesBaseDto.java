package com.autohome.applet.model.dto.car;

/**
 * <AUTHOR>
 */
public class SeriesBaseDto {
    // 车系编号
    private Integer seriesid;
    // 车系名称
    private String name;
    // 厂商指导价 最低
    private Integer minprice;
    // 厂商指导价 最高
    private Integer maxprice;
    // 白底图
    private String seriespnglogo;

    // 实拍图
    private String pic;

    // 品牌编号
    private Integer brandid;
    // 品牌名称
    private String brandname;
    // 厂商编号
    private Integer fctid;
    // 厂商名称
    private String fctname;

    private String level;

    public Integer getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(Integer seriesid) {
        this.seriesid = seriesid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getMinprice() {
        return minprice;
    }

    public void setMinprice(Integer minprice) {
        this.minprice = minprice;
    }

    public Integer getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(Integer maxprice) {
        this.maxprice = maxprice;
    }

    public String getSeriespnglogo() {
        return seriespnglogo;
    }

    public void setSeriespnglogo(String seriespnglogo) {
        this.seriespnglogo = seriespnglogo;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getBrandid() {
        return brandid;
    }

    public void setBrandid(Integer brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public Integer getFctid() {
        return fctid;
    }

    public void setFctid(Integer fctid) {
        this.fctid = fctid;
    }

    public String getFctname() {
        return fctname;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }
}
