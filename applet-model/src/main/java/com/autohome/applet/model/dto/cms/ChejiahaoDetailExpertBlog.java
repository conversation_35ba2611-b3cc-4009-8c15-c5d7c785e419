package com.autohome.applet.model.dto.cms;

public class ChejiahaoDetailExpertBlog {
    public int eBlogId;
    /// <summary>
    /// 博客名称
    /// </summary>
    public String eBlogName;
    /// <summary>
    /// 部门
    /// </summary>
    public String eDepartment;
    public int eMemberId;
    public String eMemberPic;
    public String eNickName;
    public int eEditorId;
    public String eChannelName;
    public String ePosition;
    public int eSex;
    public String eBirthday;
    public String eHobby;
    public String eJoinDate;
    public int eJbType;

    public int eArticleCount;

    public String ePictureSquare;

    /// <summary>
    /// 编辑个性签名
    /// </summary>
    public String eSignature;

    public int geteBlogId() {
        return eBlogId;
    }

    public void seteBlogId(int eBlogId) {
        this.eBlogId = eBlogId;
    }

    public String geteBlogName() {
        return eBlogName;
    }

    public void seteBlogName(String eBlogName) {
        this.eBlogName = eBlogName;
    }

    public String geteDepartment() {
        return eDepartment;
    }

    public void seteDepartment(String eDepartment) {
        this.eDepartment = eDepartment;
    }

    public int geteMemberId() {
        return eMemberId;
    }

    public void seteMemberId(int eMemberId) {
        this.eMemberId = eMemberId;
    }

    public String geteMemberPic() {
        return eMemberPic;
    }

    public void seteMemberPic(String eMemberPic) {
        this.eMemberPic = eMemberPic;
    }

    public String geteNickName() {
        return eNickName;
    }

    public void seteNickName(String eNickName) {
        this.eNickName = eNickName;
    }

    public int geteEditorId() {
        return eEditorId;
    }

    public void seteEditorId(int eEditorId) {
        this.eEditorId = eEditorId;
    }

    public String geteChannelName() {
        return eChannelName;
    }

    public void seteChannelName(String eChannelName) {
        this.eChannelName = eChannelName;
    }

    public String getePosition() {
        return ePosition;
    }

    public void setePosition(String ePosition) {
        this.ePosition = ePosition;
    }

    public int geteSex() {
        return eSex;
    }

    public void seteSex(int eSex) {
        this.eSex = eSex;
    }

    public String geteBirthday() {
        return eBirthday;
    }

    public void seteBirthday(String eBirthday) {
        this.eBirthday = eBirthday;
    }

    public String geteHobby() {
        return eHobby;
    }

    public void seteHobby(String eHobby) {
        this.eHobby = eHobby;
    }

    public String geteJoinDate() {
        return eJoinDate;
    }

    public void seteJoinDate(String eJoinDate) {
        this.eJoinDate = eJoinDate;
    }

    public int geteJbType() {
        return eJbType;
    }

    public void seteJbType(int eJbType) {
        this.eJbType = eJbType;
    }

    public int geteArticleCount() {
        return eArticleCount;
    }

    public void seteArticleCount(int eArticleCount) {
        this.eArticleCount = eArticleCount;
    }

    public String getePictureSquare() {
        return ePictureSquare;
    }

    public void setePictureSquare(String ePictureSquare) {
        this.ePictureSquare = ePictureSquare;
    }

    public String geteSignature() {
        return eSignature;
    }

    public void seteSignature(String eSignature) {
        this.eSignature = eSignature;
    }
}
