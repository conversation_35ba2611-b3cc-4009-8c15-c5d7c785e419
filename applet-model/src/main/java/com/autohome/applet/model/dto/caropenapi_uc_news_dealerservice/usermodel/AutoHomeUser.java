package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.usermodel;

import java.util.Date;

/**
 * Created by shenjm on 2018/4/21.
 * 汽车之家用户
 */
public class AutoHomeUser {
    //汽车之家用户id
    private int UserId;
    //用户状态
    private int UserState;
    //添加日期
    private Date AddDate;
    //昵称
    private String Nickname;
    //性别
    private int Sex;
    //所在省份id
    private int ProvinceId;
    //所在城市id
    private int CityId;
    //所在城镇id
    private int CountyId;
    //头像
    private String HeadImage;

    public int getUserId() {
        return UserId;
    }

    public void setUserId(int userId) {
        UserId = userId;
    }

    public int getUserState() {
        return UserState;
    }

    public void setUserState(int userState) {
        UserState = userState;
    }

    public Date getAddDate() {
        return AddDate;
    }

    public void setAddDate(Date addDate) {
        AddDate = addDate;
    }

    public String getNickname() {
        return Nickname;
    }

    public void setNickname(String nickname) {
        Nickname = nickname;
    }

    public int getSex() {
        return Sex;
    }

    public void setSex(int sex) {
        Sex = sex;
    }

    public int getProvinceId() {
        return ProvinceId;
    }

    public void setProvinceId(int provinceId) {
        ProvinceId = provinceId;
    }

    public int getCityId() {
        return CityId;
    }

    public void setCityId(int cityId) {
        CityId = cityId;
    }

    public int getCountyId() {
        return CountyId;
    }

    public void setCountyId(int countyId) {
        CountyId = countyId;
    }

    public String getHeadImage() {
        return HeadImage;
    }

    public void setHeadImage(String headImage) {
        HeadImage = headImage;
    }
}
