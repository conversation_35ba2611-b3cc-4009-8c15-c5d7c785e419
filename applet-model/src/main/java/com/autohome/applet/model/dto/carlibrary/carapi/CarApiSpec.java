package com.autohome.applet.model.dto.carlibrary.carapi;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarApiSpec {
private int id;
        private String name;
        private String logo;
        private int seriesid;
        private int fctid;
        private int brandid;
        private int syearid;
        private int structId;
        private int year;
        private int driveForm;
        private int minprice;
        private int maxprice;
        private String transmission;
        private int state;
        private String drivingmodename;
        private int flowmodeid;
        private String flowmodename;
        private int displacement;
        private int enginepower;
        private int ispreferential;
        private int istaxrelief;
        private int istaxexemption;
        private int order;
        private int specisimage;
        private int paramisshow;
        private String structtype;
        private int isclassic;
        private String gearbox;
        private String fueltype;
        private int fueltypeid;
        private int fueltypedetailid;
        private int electrictype;
        private int electrickw;
        private int isnewcar;
        private String emissionstandards;
        private String spectitle;
        private int endurancemileage;
//        private String diffconfiginfo;
        private String pricedescription;
        private int seatcount;
        private int electrickwfloat;
        private int integrativekw;
}
