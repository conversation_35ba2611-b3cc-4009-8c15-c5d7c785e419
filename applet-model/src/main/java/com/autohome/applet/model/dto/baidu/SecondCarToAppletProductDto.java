package com.autohome.applet.model.dto.baidu;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecondCarToAppletProductDto {
    @JsonAlias({"orgId"})
    private String org_id;
    @JsonAlias({"serviceId"})
    private String service_id;
    private String name;
    private int type;
    private String pic;
    private String tabs;
    private String comment;
    private String description;
    private String originalprice;
    private String finalprice;
    private String category;
    @JsonAlias({"infoUrl"})
    private String info_url;
    /**
     * status: 状态，1/0，1-状态正常，0-状态不正常（如商产品下线/下架/售罄/过期/失效等）
     * */
    private int status;
    @JsonAlias({"carName"})
    private String car_name;
    @JsonAlias({"carBrand"})
    private String car_brand;
    @JsonAlias({"carGear"})
    private String car_gear;
    @JsonAlias({"carVolume"})
    private String car_volume;
    @JsonAlias({"carType"})
    private String car_type;
    @JsonAlias({"carSeat"})
    private String car_seat;
    @JsonAlias({"carYear"})
    private String car_year;
    @JsonAlias({"carPlate"})
    private String car_plate;
    private String version;

    //由于二手车门店没有小程序版本,所以此字段先注释掉
//    private SecondCarToAppletXcxParamsDto xcx_params;

    public String getOrg_id() {
        return StringUtils.isEmpty(org_id) ? "" : org_id;
    }

    public String getService_id() {
        return StringUtils.isEmpty(service_id) ? "" : service_id;
    }

    public String getName() {
        return StringUtils.isEmpty(name) ? "" : name;
    }

    public int getType() {
        return type;
    }

    public String getPic() {
        return StringUtils.isEmpty(pic) ? "" : pic;
    }

    public String getTabs() {
        return StringUtils.isEmpty(tabs) ? "" : tabs;
    }

    public String getComment() {
        return StringUtils.isEmpty(comment) ? "" : comment;
    }

    public String getDescription() {
        return StringUtils.isEmpty(description) ? "" : description;
    }

    public String getOriginalprice() {
        return StringUtils.isEmpty(originalprice) ? "" : originalprice;
    }

    public String getFinalprice() {
        return StringUtils.isEmpty(finalprice) ? "" : finalprice;
    }

    public String getCategory() {
        return StringUtils.isEmpty(category) ? "" : category;
    }

    public String getInfo_url() {
        return StringUtils.isEmpty(info_url) ? "" : info_url;
    }

    public int getStatus() {
        return status;
    }

    public String getCar_name() {
        return StringUtils.isEmpty(car_name) ? "" : car_name;
    }

    public String getCar_brand() {
        return StringUtils.isEmpty(car_brand) ? "" : car_brand;
    }

    public String getCar_gear() {
        return StringUtils.isEmpty(car_gear) ? "" : car_gear;
    }

    public String getCar_volume() {
        return StringUtils.isEmpty(car_volume) ? "" : car_volume;
    }

    public String getCar_type() {
        return StringUtils.isEmpty(car_type) ? "" : car_type;
    }

    public String getCar_seat() {
        return StringUtils.isEmpty(car_seat) ? "" : car_seat;
    }

    public String getCar_year() {
        return StringUtils.isEmpty(car_year) ? "" : car_year;
    }

    public String getCar_plate() {
        return StringUtils.isEmpty(car_plate) ? "" : car_plate;
    }

    public String getVersion() {
        return version;
    }

    public static String getTestSecondCarToAppletProductDto(){
        return "{\n" +
                "                \"name\": \"精洗汽车 | 含内饰\",\n" +
                "                \"originalprice\": \"132\",\n" +
                "                \"finalprice\": \"111\",\n" +
                "                \"category\": \"摄影婚庆_婚礼策划\",\n" +
                "                \"tabs\": \"随时退 需预约\",\n" +
                "                \"info_url\": \"http://www.baidu.com/xx/xx/xx\",\n" +
                "                \"status\": 1,\n" +
                "                \"type\": 0,\n" +
                "                \"service_id\": \"26874826863\",\n" +
                "                \"pic\": \"https://img2.baidu.com/it/u=2537370952\",\n" +
                "                \"comment\": \"租的这个车子很干净、动力足挺省油的，店家也很准时送车...\",\n" +
                "                \"car_name\": \"朗逸\",\n" +
                "                \"car_brand\": \"奥迪\",\n" +
                "                \"car_gear\": \"自动\",\n" +
                "                \"car_volume\": \"2.0L\",\n" +
                "                \"car_type\": \"suv\",\n" +
                "                \"car_seat\": \"5\",\n" +
                "                \"car_year\": \"2017\",\n" +
                "                \"car_plate\": \"京牌\",\n" +
                "                \"org_id\": \"26874826864\"\n" +
                "            }";
    }
}
