package com.autohome.applet.model.dto.baiduex;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class WarningLightsResult {

    @JsonProperty("classifytime")
    private double classifyTime;

    @JsonProperty("img_url")
    private String imgUrl;

    @JsonProperty("label")
    private int label;

    @JsonProperty("labelconfidence")
    private int labelConfidence;

    @JsonProperty("reqid")
    private String reqId;

    @JsonProperty("runtime")
    private double runtime;

    @JsonProperty("warninglights")
    private List<WarningLight> warningLights;

    public double getClassifyTime() {
        return classifyTime;
    }

    public void setClassifyTime(double classifyTime) {
        this.classifyTime = classifyTime;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public int getLabel() {
        return label;
    }

    public void setLabel(int label) {
        this.label = label;
    }

    public int getLabelConfidence() {
        return labelConfidence;
    }

    public void setLabelConfidence(int labelConfidence) {
        this.labelConfidence = labelConfidence;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public double getRuntime() {
        return runtime;
    }

    public void setRuntime(double runtime) {
        this.runtime = runtime;
    }

    public List<WarningLight> getWarningLights() {
        return warningLights;
    }

    public void setWarningLights(List<WarningLight> warningLights) {
        this.warningLights = warningLights;
    }

    public static class WarningLight {
        @JsonProperty("pvid")
        private String pvid;

        @JsonProperty("recog_time")
        private int recogTime;

        @JsonProperty("detect_time")
        private int detectTime;

        @JsonProperty("resultpage_url")
        private String resultPageUrl;

        @JsonProperty("warningcode")
        private List<WarningCode> warningCodes;

        public String getPvid() {
            return pvid;
        }

        public void setPvid(String pvid) {
            this.pvid = pvid;
        }

        public int getRecogTime() {
            return recogTime;
        }

        public void setRecogTime(int recogTime) {
            this.recogTime = recogTime;
        }

        public int getDetectTime() {
            return detectTime;
        }

        public void setDetectTime(int detectTime) {
            this.detectTime = detectTime;
        }

        public String getResultPageUrl() {
            return resultPageUrl;
        }

        public void setResultPageUrl(String resultPageUrl) {
            this.resultPageUrl = resultPageUrl;
        }

        public List<WarningCode> getWarningCodes() {
            return warningCodes;
        }

        public void setWarningCodes(List<WarningCode> warningCodes) {
            this.warningCodes = warningCodes;
        }
    }

    public static class WarningCode {
        @JsonProperty("confidence")
        private String confidence;

        @JsonProperty("detectconfidence")
        private String detectConfidence;

        @JsonProperty("positionid")
        private int positionId;

        @JsonProperty("warningid")
        private String warningId;

        @JsonProperty("warningname")
        private String warningName;

        @JsonProperty("explain")
        private String explain;

        @JsonProperty("sample_url")
        private String sampleUrl;

        @JsonProperty("position")
        private List<Double> position;

        public String getConfidence() {
            return confidence;
        }

        public void setConfidence(String confidence) {
            this.confidence = confidence;
        }

        public String getDetectConfidence() {
            return detectConfidence;
        }

        public void setDetectConfidence(String detectConfidence) {
            this.detectConfidence = detectConfidence;
        }

        public int getPositionId() {
            return positionId;
        }

        public void setPositionId(int positionId) {
            this.positionId = positionId;
        }

        public String getWarningId() {
            return warningId;
        }

        public void setWarningId(String warningId) {
            this.warningId = warningId;
        }

        public String getWarningName() {
            return warningName;
        }

        public void setWarningName(String warningName) {
            this.warningName = warningName;
        }

        public String getExplain() {
            return explain;
        }

        public void setExplain(String explain) {
            this.explain = explain;
        }

        public String getSampleUrl() {
            return sampleUrl;
        }

        public void setSampleUrl(String sampleUrl) {
            this.sampleUrl = sampleUrl;
        }

        public List<Double> getPosition() {
            return position;
        }

        public void setPosition(List<Double> position) {
            this.position = position;
        }
    }
}
