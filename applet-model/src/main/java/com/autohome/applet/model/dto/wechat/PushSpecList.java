package com.autohome.applet.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 微信搜一搜-汽车车型推送
 */
@Builder
@Data
public class PushSpecList {

    @JsonProperty("chexing_list")
    private List<CarSpecList> chexingList;

    @JsonProperty("is_test")
    private int isTest;

    @Builder
    @Data
    public static class CarSpecList {
        @JsonProperty("chexing_id")
        private String chexingId;

        @JsonProperty("chexing_name")
        private String chexingName;

        @JsonProperty("chexi_id")
        private String chexiId;

        @JsonProperty("market_time")
        private String marketTime;

        @JsonProperty("year")
        private String year;

        @JsonProperty("price")
        private int price;

        @JsonProperty("chexing_jump_info")
        private JumpInfo chexingJumpInfo;

        @JsonProperty("engine_type")
        private Integer engineType;

        @JsonProperty("oil_cost")
        private String oilCost;

        @JsonProperty("engine")
        private String engine;

        @JsonProperty("battery_life")
        private String  batteryLife;

        @JsonProperty("charge_time")
        private String chargeTime;

        @JsonProperty("sale_status")
        private int saleStatus;

        @JsonProperty("status")
        private int status;

//        @JsonProperty("aliases")
//        private List<String> aliases;

    }

    @Builder
    @Data
    public static class JumpInfo {
        @JsonProperty("jump_type")
        private int jumpType;

        @JsonProperty("appid")
        private String appid;

        @JsonProperty("service_url")
        private String serviceUrl;
    }

}
