package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BusinessList {

    @JsonProperty("DealerId")
    private int dealerid;
    @JsonProperty("VrState")
    private int vrstate;
    @JsonProperty("VrTitle")
    private String vrtitle;
    @JsonProperty("LinkUrl")
    private String linkurl;

    public void setDealerid(int dealerid) {
        this.dealerid = dealerid;
    }

    public int getDealerid() {
        return dealerid;
    }

    public void setVrstate(int vrstate) {
        this.vrstate = vrstate;
    }

    public int getVrstate() {
        return vrstate;
    }

    public void setVrtitle(String vrtitle) {
        this.vrtitle = vrtitle;
    }

    public String getVrtitle() {
        return vrtitle;
    }

    public void setLinkurl(String linkurl) {
        this.linkurl = linkurl;
    }

    public String getLinkurl() {
        return linkurl;
    }

}
