package com.autohome.applet.model.dto.dealer;

import java.math.BigDecimal;
import java.util.List;

public class DealerListSeriesResult {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<ItemInfo> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<ItemInfo> getList() {
        return list;
    }

    public void setList(List<ItemInfo> list) {
        this.list = list;
    }

    public static class ItemInfo {
        private int dealerId;
        private int seriesId;
        /**
         * 车系最低促销价
         * */
        private int minNewsPrice;
        /**
         * 车系最高促销价
         * */
        private int maxNewsPrice;
        /**
         * 厂商指导价
         * */
        private int minOriginalPrice;
        /**
         * 厂商指导价
         * */
        private int maxOriginalPrice;
        /**
         * 车系最高降幅
         * */
        private int maxPriceOff;
        /**
         * 服务评价分数
         * */
        private BigDecimal serviceFeedbackScore;

        public int getDealerId() {
            return dealerId;
        }

        public void setDealerId(int dealerId) {
            this.dealerId = dealerId;
        }

        public int getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(int seriesId) {
            this.seriesId = seriesId;
        }

        public int getMinNewsPrice() {
            return minNewsPrice;
        }

        public void setMinNewsPrice(int minNewsPrice) {
            this.minNewsPrice = minNewsPrice;
        }

        public int getMaxNewsPrice() {
            return maxNewsPrice;
        }

        public void setMaxNewsPrice(int maxNewsPrice) {
            this.maxNewsPrice = maxNewsPrice;
        }

        public int getMinOriginalPrice() {
            return minOriginalPrice;
        }

        public void setMinOriginalPrice(int minOriginalPrice) {
            this.minOriginalPrice = minOriginalPrice;
        }

        public int getMaxOriginalPrice() {
            return maxOriginalPrice;
        }

        public void setMaxOriginalPrice(int maxOriginalPrice) {
            this.maxOriginalPrice = maxOriginalPrice;
        }

        public int getMaxPriceOff() {
            return maxPriceOff;
        }

        public void setMaxPriceOff(int maxPriceOff) {
            this.maxPriceOff = maxPriceOff;
        }

        public BigDecimal getServiceFeedbackScore() {
            return serviceFeedbackScore;
        }

        public void setServiceFeedbackScore(BigDecimal serviceFeedbackScore) {
            this.serviceFeedbackScore = serviceFeedbackScore;
        }
    }
}
