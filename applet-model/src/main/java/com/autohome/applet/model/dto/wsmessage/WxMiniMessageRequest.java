package com.autohome.applet.model.dto.wsmessage;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class WxMiniMessageRequest {

    @JsonAlias({"userId"})
    private Integer userId;
    @NotBlank(message = "缺少xcxopenId")
    @JsonAlias({"xcxopenId"})
    private String xcxopenId;
    @NotBlank(message = "缺少unionId")
    @JsonAlias({"unionId"})
    private String unionId;
    @Min(value = 1, message = "请输入正确的订阅类型")
    @JsonAlias({"bizType"})
    private Integer bizType;
    @Min(value = 0, message = "请输入正确的渠道")
    @JsonAlias({"sourceId"})
    private Integer sourceId;
    @Min(value = 1, message = "请输入正确的平台类型")
    @JsonAlias({"pm"})
    private Integer pm;
    @NotBlank(message = "缺少deviceId")
    @JsonAlias({"deviceId"})
    private String deviceId;
    @NotBlank(message = "缺少seriesId")
    @JsonAlias({"seriesId"})
    private String seriesId;
    @NotBlank(message = "缺少pluginversion")
    @JsonAlias({"pluginversion"})
    private String pluginversion;
    
    @JsonAlias({"cityId"})
    private Integer cityId;
}