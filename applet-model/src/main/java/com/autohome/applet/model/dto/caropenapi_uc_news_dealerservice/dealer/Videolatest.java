package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/23.
 */
public class Videolatest {
    private int rowcount;
    private int nextoffset;
    private List<Videolist> videolist;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getNextoffset() {
        return nextoffset;
    }

    public void setNextoffset(int nextoffset) {
        this.nextoffset = nextoffset;
    }

    public List<Videolist> getVideolist() {
        return videolist;
    }

    public void setVideolist(List<Videolist> videolist) {
        this.videolist = videolist;
    }

    public static class Videolist {

        private int videoid;
        private String videotitle;
        private String videoimg;
        private String videoimg34;
        private String videoimg43;
        private String videoimg169;
        private String publishtime;
        private String verifytime;
        private String recommendtime;
        private Author author;
        private int commentcount;
        private int likecount;
        private int playcount;
        private int expiredlikecount;
        private int expiredplaycount;
        private String mediaid;
        private String description;
        private String location;
        private String playurl;
        private int businessid;
        private int ispass;
        private int islike;
        private int topicid;
        private String topictitle;
        private String shareurl;
        private int width;
        private int height;
        private int isprivate;
        private int weight;
        private int duration;
        private int usertype;
        private String businessname;
        private String thumbnails;
        //@JsonProperty("speechContent")
        private String speechcontent;
        //@JsonProperty("seriesIds")
        private String seriesids;
        private int brandid;
        private int demo;
        private int offset;
        //@JsonProperty("cityId")
        private int cityid;
        private String extjson;
        private String animwebp;
        private String musicid;
        private String musictitle;
        private int ranknum;
        //@JsonProperty("countHot")
        private int counthot;
        private int flag;
        //@JsonProperty("is_yingxiao")
        private int isYingxiao;
        private int showmodel;
        private String recommendtag;
        private String recommendtagpath;
        private String tagframe;
        private String tagframepath;

        public void setVideoid(int videoid) {
            this.videoid = videoid;
        }

        public int getVideoid() {
            return videoid;
        }

        public void setVideotitle(String videotitle) {
            this.videotitle = videotitle;
        }

        public String getVideotitle() {
            return videotitle;
        }

        public void setVideoimg(String videoimg) {
            this.videoimg = videoimg;
        }

        public String getVideoimg() {
            if (StringUtil.isNotNullAndWhiteSpace(videoimg)) {
                return videoimg.replace("https://", "//").replace("http://", "//");
            }
            return videoimg;
        }

        public void setVideoimg34(String videoimg34) {
            this.videoimg34 = videoimg34;
        }

        public String getVideoimg34() {
            if (StringUtil.isNotNullAndWhiteSpace(videoimg34)) {
                return videoimg34.replace("https://", "//").replace("http://", "//");
            }
            return videoimg34;
        }

        public void setVideoimg43(String videoimg43) {
            this.videoimg43 = videoimg43;
        }

        public String getVideoimg43() {
            if (StringUtil.isNotNullAndWhiteSpace(videoimg43)) {
                return videoimg43.replace("https://", "//").replace("http://", "//");
            }
            return videoimg43;
        }

        public void setVideoimg169(String videoimg169) {
            this.videoimg169 = videoimg169;
        }

        public String getVideoimg169() {
            if (StringUtil.isNotNullAndWhiteSpace(videoimg169)) {
                return videoimg169.replace("https://", "//").replace("http://", "//");
            }
            return videoimg169;
        }

        public void setPublishtime(String publishtime) {
            this.publishtime = publishtime;
        }

        public String getPublishtime() {
            return publishtime;
        }

        public void setVerifytime(String verifytime) {
            this.verifytime = verifytime;
        }

        public String getVerifytime() {
            return verifytime;
        }

        public void setRecommendtime(String recommendtime) {
            this.recommendtime = recommendtime;
        }

        public String getRecommendtime() {
            return recommendtime;
        }

        public void setAuthor(Author author) {
            this.author = author;
        }

        public Author getAuthor() {
            return author;
        }

        public void setCommentcount(int commentcount) {
            this.commentcount = commentcount;
        }

        public int getCommentcount() {
            return commentcount;
        }

        public void setLikecount(int likecount) {
            this.likecount = likecount;
        }

        public int getLikecount() {
            return likecount;
        }

        public void setPlaycount(int playcount) {
            this.playcount = playcount;
        }

        public int getPlaycount() {
            return playcount;
        }

        public void setExpiredlikecount(int expiredlikecount) {
            this.expiredlikecount = expiredlikecount;
        }

        public int getExpiredlikecount() {
            return expiredlikecount;
        }

        public void setExpiredplaycount(int expiredplaycount) {
            this.expiredplaycount = expiredplaycount;
        }

        public int getExpiredplaycount() {
            return expiredplaycount;
        }

        public void setMediaid(String mediaid) {
            this.mediaid = mediaid;
        }

        public String getMediaid() {
            return mediaid;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getLocation() {
            return location;
        }

        public void setPlayurl(String playurl) {
            this.playurl = playurl;
        }

        public String getPlayurl() {
            if (StringUtil.isNotNullAndWhiteSpace(playurl)) {
                return playurl.replace("https://", "//").replace("http://", "//");
            }
            return playurl;
        }

        public void setBusinessid(int businessid) {
            this.businessid = businessid;
        }

        public int getBusinessid() {
            return businessid;
        }

        public void setIspass(int ispass) {
            this.ispass = ispass;
        }

        public int getIspass() {
            return ispass;
        }

        public void setIslike(int islike) {
            this.islike = islike;
        }

        public int getIslike() {
            return islike;
        }

        public void setTopicid(int topicid) {
            this.topicid = topicid;
        }

        public int getTopicid() {
            return topicid;
        }

        public void setTopictitle(String topictitle) {
            this.topictitle = topictitle;
        }

        public String getTopictitle() {
            return topictitle;
        }

        public void setShareurl(String shareurl) {
            this.shareurl = shareurl;
        }

        public String getShareurl() {
            if (StringUtil.isNotNullAndWhiteSpace(shareurl)) {
                return shareurl.replace("https://", "//").replace("http://", "//");
            }
            return shareurl;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getWidth() {
            return width;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public int getHeight() {
            return height;
        }

        public void setIsprivate(int isprivate) {
            this.isprivate = isprivate;
        }

        public int getIsprivate() {
            return isprivate;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }

        public int getWeight() {
            return weight;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public int getDuration() {
            return duration;
        }

        public void setUsertype(int usertype) {
            this.usertype = usertype;
        }

        public int getUsertype() {
            return usertype;
        }

        public void setBusinessname(String businessname) {
            this.businessname = businessname;
        }

        public String getBusinessname() {
            return businessname;
        }

        public void setThumbnails(String thumbnails) {
            this.thumbnails = thumbnails;
        }

        public String getThumbnails() {
            return thumbnails;
        }

        public void setSpeechcontent(String speechcontent) {
            this.speechcontent = speechcontent;
        }

        public String getSpeechcontent() {
            return speechcontent;
        }

        public void setSeriesids(String seriesids) {
            this.seriesids = seriesids;
        }

        public String getSeriesids() {
            return seriesids;
        }

        public void setBrandid(int brandid) {
            this.brandid = brandid;
        }

        public int getBrandid() {
            return brandid;
        }

        public void setDemo(int demo) {
            this.demo = demo;
        }

        public int getDemo() {
            return demo;
        }

        public void setOffset(int offset) {
            this.offset = offset;
        }

        public int getOffset() {
            return offset;
        }

        public void setCityid(int cityid) {
            this.cityid = cityid;
        }

        public int getCityid() {
            return cityid;
        }

        public void setExtjson(String extjson) {
            this.extjson = extjson;
        }

        public String getExtjson() {
            return extjson;
        }

        public void setAnimwebp(String animwebp) {
            this.animwebp = animwebp;
        }

        public String getAnimwebp() {
            return animwebp;
        }

        public void setMusicid(String musicid) {
            this.musicid = musicid;
        }

        public String getMusicid() {
            return musicid;
        }

        public void setMusictitle(String musictitle) {
            this.musictitle = musictitle;
        }

        public String getMusictitle() {
            return musictitle;
        }

        public void setRanknum(int ranknum) {
            this.ranknum = ranknum;
        }

        public int getRanknum() {
            return ranknum;
        }

        public void setCounthot(int counthot) {
            this.counthot = counthot;
        }

        public int getCounthot() {
            return counthot;
        }

        public void setFlag(int flag) {
            this.flag = flag;
        }

        public int getFlag() {
            return flag;
        }

        public void setIsYingxiao(int isYingxiao) {
            this.isYingxiao = isYingxiao;
        }

        public int getIsYingxiao() {
            return isYingxiao;
        }

        public void setShowmodel(int showmodel) {
            this.showmodel = showmodel;
        }

        public int getShowmodel() {
            return showmodel;
        }

        public void setRecommendtag(String recommendtag) {
            this.recommendtag = recommendtag;
        }

        public String getRecommendtag() {
            return recommendtag;
        }

        public void setRecommendtagpath(String recommendtagpath) {
            this.recommendtagpath = recommendtagpath;
        }

        public String getRecommendtagpath() {
            return recommendtagpath;
        }

        public void setTagframe(String tagframe) {
            this.tagframe = tagframe;
        }

        public String getTagframe() {
            return tagframe;
        }

        public void setTagframepath(String tagframepath) {
            this.tagframepath = tagframepath;
        }

        public String getTagframepath() {
            return tagframepath;
        }

    }

    public static class Author {

        private int userid;
        private String portrait;
        private String nickname;
        private String seriesid;
        private String seriesname;
        private int level;

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public int getUserid() {
            return userid;
        }

        public void setPortrait(String portrait) {
            this.portrait = portrait;
        }

        public String getPortrait() {
            return portrait;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getNickname() {
            return nickname;
        }

        public void setSeriesid(String seriesid) {
            this.seriesid = seriesid;
        }

        public String getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getLevel() {
            return level;
        }

    }
}
