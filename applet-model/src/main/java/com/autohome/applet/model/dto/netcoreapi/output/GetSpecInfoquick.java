package com.autohome.applet.model.dto.netcoreapi.output;

import com.autohome.applet.model.dto.netcoreapi.series.SeriesInfo;
import com.fasterxml.jackson.annotation.JsonProperty;

public class GetSpecInfoquick {
    private int specid;

    private String specname;

    private String speclog;

    private String spceprice;

    private String seriesname;

    private int state;

    private String brand;

    private String series;

    private String year;

    private int jiagecount;

    private SeriesInfo seriesinfo;

    private String whiteimg;

    private int seriesid;

    private int specstate;

    private int ranliaoxingshi;

    private String butiePrice;

    private String brandfirstletter;

    private int colorCount;

    private String minNewsPrice;

    @JsonProperty("Differenceprice")
    private String differenceprice;

    private int fueltype;

    public int getSpecid() {
        return specid;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getSpeclog() {
        return speclog;
    }

    public void setSpeclog(String speclog) {
        this.speclog = speclog;
    }

    public String getSpceprice() {
        return spceprice;
    }

    public void setSpceprice(String spceprice) {
        this.spceprice = spceprice;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public int getJiagecount() {
        return jiagecount;
    }

    public void setJiagecount(int jiagecount) {
        this.jiagecount = jiagecount;
    }

    public SeriesInfo getSeriesinfo() {
        return seriesinfo;
    }

    public void setSeriesinfo(SeriesInfo seriesinfo) {
        this.seriesinfo = seriesinfo;
    }

    public String getWhiteimg() {
        return whiteimg;
    }

    public void setWhiteimg(String whiteimg) {
        this.whiteimg = whiteimg;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSpecstate() {
        return specstate;
    }

    public void setSpecstate(int specstate) {
        this.specstate = specstate;
    }

    public int getRanliaoxingshi() {
        return ranliaoxingshi;
    }

    public void setRanliaoxingshi(int ranliaoxingshi) {
        this.ranliaoxingshi = ranliaoxingshi;
    }

    public String getButiePrice() {
        return butiePrice;
    }

    public void setButiePrice(String butiePrice) {
        this.butiePrice = butiePrice;
    }

    public String getBrandfirstletter() {
        return brandfirstletter;
    }

    public void setBrandfirstletter(String brandfirstletter) {
        this.brandfirstletter = brandfirstletter;
    }

    public int getColorCount() {
        return colorCount;
    }

    public void setColorCount(int colorCount) {
        this.colorCount = colorCount;
    }

    public String getMinNewsPrice() {
        return minNewsPrice;
    }

    public void setMinNewsPrice(String minNewsPrice) {
        this.minNewsPrice = minNewsPrice;
    }

    public String getDifferenceprice() {
        return differenceprice;
    }

    public void setDifferenceprice(String differenceprice) {
        this.differenceprice = differenceprice;
    }

    public int getFueltype() {
        return fueltype;
    }

    public void setFueltype(int fueltype) {
        this.fueltype = fueltype;
    }
}
