package com.autohome.applet.model.dto.carlibrary.carapi.out;

import lombok.Data;

import java.util.List;

@Data
public class RankListpageResult {
    private int cacheable;
    private int cdncachesecond;
    private String message;
    public Result result;
    private int returncode;


    @Data
    public static class Result {
        public List<ListItem> list;
        private String morescheme;
        private int pagecount;
        private int pageindex;
        private int pagesize;
        private String saleranktip;
        private String scenesubtitle;
        private String scenetitle;
        private Shareinfo shareinfo;
    }

    @Data
    public static class ListItem {
        private int cardtype;
        private String followcount;
        private String followlink;
        private String followtext;
        private int isshowscorevalue;
        private String linkurl;
        private String priceinfo;
        private Pvitem pvitem;
        private String rank;
        private int rankNum;
        private int rankchange;
        private String rcmdesc;
        private List<Object> rcmlist;
        private String rcmtext;
        private int rcmtype;
        private Rightinfo rightinfo;
        private String righttextone;
        private String righttexttwo;
        private String righttexttwolinkurl;
        private List<Object> safeitemlist;
        private String scoretip;
        private String scorevalue;
        private String seriesid;
        private String seriesimage;
        private String seriesname;
        private Shareinfo shareinfo;
        private int showenergyicon;
        private int showrankchange;
        private String specname;
    }

    @Data
    public static class Shareinfo {
        private int canlongshare;
        private String shareext;
        private List<Sharelist> sharelist;
    }

    @Data
    public static class Sharelist {
        private String icon;
        private String id;
        private Pvitem pvitem;
        private int sharetypeid;
        private String title;
    }

    @Data
    public static class Pvitem {
        private Argvs argvs;
        private Click click;
        private Show show;
    }

    @Data
    public static class Argvs {
        private String subranktypeid;
        private String rank;
        private String typeid;
        private String seriesid;
        private String sharetypeid;
    }

    @Data
    public static class Click {
        private Object argvs;
        private String eventid;
    }

    @Data
    public static class Show {
        private Object argvs;
        private String eventid;
    }

    @Data
    public static class Rightinfo {
        private String ext;
        private Priceinfo priceinfo;
        private Pvitem pvitem;
        private String rightpriceeid;
        private String rightpricetitle;
        private String rightpriceurl;
        private String righttextone;
        private String righttexttwo;
        private String righttexttwolinkurl;
    }

    @Data
    public static class Priceinfo {
        private String eid;
        private String ext;
        private String linkurl;
        private String title;
    }

}