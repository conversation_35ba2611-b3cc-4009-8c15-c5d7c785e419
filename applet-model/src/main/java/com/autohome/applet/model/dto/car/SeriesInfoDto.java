package com.autohome.applet.model.dto.car;

public class SeriesInfoDto {
    /**
     * 车系Id
     * */
    private Integer id;
    /**
     * 车系名称
     * */
    private String name;
    /**
     * 厂商id
     * */
    private Integer fctid;
    /**
     * 车系链接
     * */
    private String url;
    /**
     * 品牌id
     * */
    private Integer brandid;
    /**
     * 级别id
     * */
    private Integer levelid;
    /**
     * 级别
     * */
    private String levelname;
    /**
     * 车系性质
     * */
    private String place;
    /**
     * 编辑时间
     * */
    private String edittime;
    /**
     * 首字母
     * */
    private String firstletter;
    /**
     * 品牌logo
     * */
    private String logo;
    /**
     * 车系png代表图
     * */
    private String seriespnglogo;
    /**
     * 0=未售
     * 10=待售
     * 20=在产在售
     * 30=停产在售
     * 40=停售
     * */
    private Integer state;

    private Integer salespecnum;
    private Integer minprice;
    private Integer maxprice;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getFctid() {
        return fctid;
    }

    public void setFctid(Integer fctid) {
        this.fctid = fctid;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getBrandid() {
        return brandid;
    }

    public void setBrandid(Integer brandid) {
        this.brandid = brandid;
    }

    public Integer getLevelid() {
        return levelid;
    }

    public void setLevelid(Integer levelid) {
        this.levelid = levelid;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getEdittime() {
        return edittime;
    }

    public void setEdittime(String edittime) {
        this.edittime = edittime;
    }

    public String getFirstletter() {
        return firstletter;
    }

    public void setFirstletter(String firstletter) {
        this.firstletter = firstletter;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getSeriespnglogo() {
        return seriespnglogo;
    }

    public void setSeriespnglogo(String seriespnglogo) {
        this.seriespnglogo = seriespnglogo;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getSalespecnum() {
        return salespecnum;
    }

    public void setSalespecnum(Integer salespecnum) {
        this.salespecnum = salespecnum;
    }

    public Integer getMinprice() {
        return minprice;
    }

    public void setMinprice(Integer minprice) {
        this.minprice = minprice;
    }

    public Integer getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(Integer maxprice) {
        this.maxprice = maxprice;
    }
}
