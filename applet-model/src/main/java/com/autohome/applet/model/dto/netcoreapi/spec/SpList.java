package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class SpList {

    @JsonProperty("specid")
    private int specId;

    @JsonProperty("value")
    private String value;

    public static SpList by(SpecParams.ValueItem valueItem) {
        return new SpList(valueItem.getSpecId(), valueItem.getValue());
    }

    public SpList() {
    }

    public SpList(int specId, String value) {
        this.specId = specId;
        if (value != null) {
            value = value.replace("&nbsp;", " ");
        }
        this.value = value;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
