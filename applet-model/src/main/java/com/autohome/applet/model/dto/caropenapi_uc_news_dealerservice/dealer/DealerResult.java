package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/5/11.
 */
public class DealerResult {
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int rowcount;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int pagecount;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int pageindex;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String latitude;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String longitude;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<Dealer> list;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<Dealer> items;

    private int totalcount;

    private String pageid;

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = StringUtil.isNotEmpty(latitude) ? latitude : "0";
        if (list == null || list.size() == 0) {
            return;
        }
        for (Dealer item : list) {
            item.setLatitude(this.latitude);
        }
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = StringUtil.isNotEmpty(longitude) ? longitude : "0";
        if (list == null || list.size() == 0) {
            return;
        }
        for (Dealer item : list) {
            item.setLongitude(this.longitude);
        }
        this.longitude = longitude;
    }

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<Dealer> getList() {
        return list;
    }

    public void setList(List<Dealer> list) {
        this.list = list;
    }

    public int getTotalcount() {
        return rowcount;
    }

    public void setTotalcount(int totalcount) {
        this.totalcount = totalcount;
    }

    public String getPageid() {
        return StringUtil.isNotNullAndWhiteSpace(pageid) ? pageid : "";
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public List<Dealer> getItems() {
        return list;
    }
}
