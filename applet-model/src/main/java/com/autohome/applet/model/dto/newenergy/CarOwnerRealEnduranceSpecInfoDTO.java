package com.autohome.applet.model.dto.newenergy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 新能源车主真实续航北里车系续航接口数据
 * @author: WangBoWen
 * @date: 2024-01-02
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CarOwnerRealEnduranceSpecInfoDTO {

    private SpecInfo specinfo;

    private List<Nav> navlist;
    @Data
    public static class SpecInfo{

        private Integer specid;

        private String subname;

        private String tabtips;

        private List<SubInfo> sublist;

        private String tabtipssuffix;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubInfo {
        private String title;

        private String suffix;

        private String subtitle;

        private String titlecolor;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Nav {

        private String navkey;

        private String navtitle;

        private SameSpecBase samespecinfo;

        private CompareInfo compareinfo;

        private DiscussInfo discussinfo;

        private ChargeInfo chargeinfo;

        private MoreInfo moreinfo;

        private Integer navid;

    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CompareTableCol{

        private String title;

        private String titlecolor;

        private String subtitle;

        private Integer specid;

        private Integer seriesid;

        private List<TableColValue> list;

        private String linkurl;
    }

    @Data
    public static class DiscussItem {

        private String userpic;

        private String username;

        private String userdesc;

        private String title;

        private List<String> imglist;

        private Integer replycount;

        private Integer praisecount;

        private String viewcount;

        private String linkurl;

        private String score;

        private List<DiscussShareTitle> testlist;



    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TableColValue {

        private String name;

        private String value;

        private String titlecolor;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DiscussShareTitle {

        private String title;

        private String subtitle;


    }

    @Data
    public static class SameSpecBase {

        private String bigtitle;


    }

    @Data
    public static class CompareInfo {

        private String bigtitle;

        private String specids;

        private List<CompareTableCol> tablelist;

    }

    @Data
    public static class DiscussInfo {

        private String bigtitle;

        private List<DiscussItem> list;
    }

    @Data
    public static class MoreInfo {

        private String moretitle;

        private String morelinkurl;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ChargeInfo {

        private String bigtitlelinkurl;

        private String bigtitle;

        private String poptitle;

        private List<Charge> chargetimes;

        private CalculatorInfo calculatorinfo;

    }

    @Data
    public static class SeasonItem {

        private String seasonname;

        private List<Charge> chargeList;
    }

    @Data
    public static class EnergyCost {

        private String seasonname;

        private Double nenghao100;

        private Integer normalmileage;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Charge{

        private String time;

        private String suffix;

        private String way;

        private String range;
    }

    @Data
    public static class CalculatorInfo {

        private String bigtitle;

        private List<EnergyCost> seasonlist;

        private Integer defmileage;

        private Double defchargeprice;

        private Double oilprice;

        private Double avgfuel;

        private Integer issuv;

        private String tipstitle;

        private List<Tip> tipslist;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Tip {

        private String title;

        private String desc;
    }

    @Data
    public static class SameSpecInfo extends SameSpecBase {

        private String winterSubTitle;

        private String winterSuffix;

        private String winterTitle;

        private Integer winterValue;

        private String summerTitle;

        private String summerSubtitle;

        private String summerSuffix;

        private Integer summerValue;

    }

    @Data
    public static class SameSpecInfoNew extends SameSpecBase{

        private String bigtitlelinkurl;

        private List<SeasonDto> list;

        @Data
        public static class SeasonDto{

            private String title;

            private String subtitle;

            private Integer value;

            private String suffix;

            private String startcolor;

            private String endcolor;
        }

    }

}
