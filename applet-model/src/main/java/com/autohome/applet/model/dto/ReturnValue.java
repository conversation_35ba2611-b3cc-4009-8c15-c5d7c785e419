package com.autohome.applet.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ReturnValue<T> {
    /**
     * 返回code
     * */
    @JsonAlias(value = {"returncode", "returnCode"})
    private int returncode = 0;
    /**
     * 错误信息
     * */
    private String message;
    /**
     * 返回结果
     * */
    private T result;

    public ReturnValue(T result) {
        this(0, null, result);
    }

    public ReturnValue(int returnCode, String message) {
        this(returnCode, message, null);
    }

    public ReturnValue(int returnCode, String message, T result) {
        this.returncode = returnCode;
        this.message = message;
        this.result = result;
    }

    public ReturnValue() {
        this(0,null,null);
    }

    public int getReturncode() {
        return returncode;
    }

    public void setReturncode(int returncode) {
        this.returncode = returncode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public static <T> ReturnValue<T> buildSuccessResult(T obj) {
        ReturnValue<T> rt = new ReturnValue<>();
        rt.setReturncode(0);
        rt.setResult(obj);
        rt.setMessage("success");
        return rt;
    }

    public static <T> ReturnValue<T> buildErrorResult(int returnCode, String message) {
        ReturnValue<T> rt = new ReturnValue<>();
        rt.setReturncode(returnCode);
        rt.setMessage(message);
        return rt;
    }

}

