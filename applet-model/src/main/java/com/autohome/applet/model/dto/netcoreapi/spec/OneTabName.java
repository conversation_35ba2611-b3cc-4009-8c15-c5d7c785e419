package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OneTabName {

    @JsonProperty("onetabname")
    private String oneTabName;

    @JsonProperty("twoname")
    private List<TwoTabName> twoName;

    public OneTabName() {
    }

    public OneTabName(String oneTabName, List<TwoTabName> twoName) {
        this.oneTabName = oneTabName;
        this.twoName = twoName;
    }

    public String getOneTabName() {
        return oneTabName;
    }

    public void setOneTabName(String oneTabName) {
        this.oneTabName = oneTabName;
    }

    public List<TwoTabName> getTwoName() {
        return twoName;
    }

    public void setTwoName(List<TwoTabName> twoName) {
        this.twoName = twoName;
    }
}
