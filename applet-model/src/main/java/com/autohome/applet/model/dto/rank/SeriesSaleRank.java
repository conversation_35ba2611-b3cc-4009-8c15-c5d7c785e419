package com.autohome.applet.model.dto.rank;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SeriesSaleRank {

    private List<DataBean> data;
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean implements KoubeiScoreListable, SeriesInfoSetable{
        /**
         * max_guidance_price : 143000
         * manu_type : 合资
         * level : 3
         * is_newenergy : 0
         * pre_rn : 1
         * created_stime : 1626404051000
         * seriesid : 448
         * month : 2021-06
         * salecnt : 47283
         * modified_stime : 1627034929000
         * min_guidance_price : 99800
         * id : 297785
         * rn : 1
         */

//        private int max_guidance_price;
        private String manu_type;
        private String level;
        //        private int is_newenergy;
        private int pre_rn;
        //        private long created_stime;
        private String seriesid;
        private String seriesname;
        private String logo;
        private String month;
        private int salecnt;
        //        private long modified_stime;
//        private int min_guidance_price;
//        private int id;
        private int rn;
        private String minprice;
        private String maxprice;
        private String seriesprice;
        private List<SeriesScoreList.Score> scoreList;
        private int brandid;

        public int getBrandid() {
            return brandid;
        }

        public void setBrandid(int brandid) {
            this.brandid = brandid;
        }

        @Override
        public void setBrandId(int brandId) {
            this.brandid = brandId;
        }

        public List<SeriesScoreList.Score> getScoreList() {
            return scoreList;
        }

        @JsonIgnore
        @Override
        public int getSeriesId() {
            return Integer.parseInt(seriesid);
        }

        @Override
        public void setSeriespnglogo(String logo) {
            this.logo = logo;
        }

        @Override
        public void setMinPrice(int minPrice) {
            this.minprice = String.valueOf(minPrice);
        }

        @Override
        public void setMaxPrice(int maxPrice) {
            this.maxprice = String.valueOf(maxPrice);
        }

        @Override
        public void setSeriesName(String seriesName) {
            this.seriesname = seriesName;
        }

        @Override
        public void setScoreList(List<SeriesScoreList.Score> scoreList) {
            this.scoreList = scoreList;
        }

        public String getSeriesprice() {
            return seriesprice;
        }

        public void setSeriesprice(String seriesprice) {
            this.seriesprice = seriesprice;
        }

        public String getMinprice() {
            return minprice;
        }

        public void setMinprice(String minprice) {
            this.minprice = minprice;
        }

        public String getMaxprice() {
            return maxprice;
        }

        public void setMaxprice(String maxprice) {
            this.maxprice = maxprice;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }


        public String getManu_type() {
            return manu_type;
        }

        public void setManu_type(String manu_type) {
            this.manu_type = manu_type;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public int getPre_rn() {
            return pre_rn;
        }

        public void setPre_rn(int pre_rn) {
            this.pre_rn = pre_rn;
        }

        public String getSeriesid() {
            return seriesid;
        }

        public void setSeriesid(String seriesid) {
            this.seriesid = seriesid;
        }

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public int getSalecnt() {
            return salecnt;
        }

        public void setSalecnt(int salecnt) {
            this.salecnt = salecnt;
        }


        public int getRn() {
            return rn;
        }

        public void setRn(int rn) {
            this.rn = rn;
        }
    }
}
