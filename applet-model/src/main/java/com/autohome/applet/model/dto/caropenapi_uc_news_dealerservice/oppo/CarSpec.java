package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.oppo;

/**
 * Created by ha<PERSON><PERSON><PERSON> on 2018/8/27.
 */
public class CarSpec {
    //    @JsonIgnore
    private int id;
    private String name;
    private int minprice;
    //    private int maxprice;
    private String logo;
    //    private int yearid;
//    private String yearname;
//    private int seriesid;
    private String seriesname;
    //    private String serieslogo;
//    private String seriesofficialurl;
//    private String seriesfirstletter;
//    private int brandid;
    private String brandname;
//    private String brandlogo;
//    private String brandofficialurl;
//    private String brandfirstletter;
//    private int fctid;
//    private String fctname;
//    private String fctlogo;
//    private String fctofficialurl;
//    private String fctfirstletter;
//    private int levelid;
//    private String levelname;
//    private int state;
//    private int paramisshow;
//    private String specquality;
//    private int fueltype;
//    private String displacement;
//    private String timemarket;
//    private int ranliaoxingshi;

//    private String specHapUrl;
//    private String specMUrl;


//    public String getSpecHapUrl() {
//        return "hap://app/com.autohome.quickapp/PageSpec?specid="+id+"&auto_open_from=Oppo_search";
//    }
//
//    public String getSpecMUrl() {
//        return "https://m.autohome.com.cn/spec/"+id;
//    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }
}
