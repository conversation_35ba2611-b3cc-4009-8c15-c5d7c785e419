package com.autohome.applet.model.dto.usercenter;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;

public class AutoUserInfo {
    @JsonProperty("UserId")
    private int userId;
    @JsonProperty("RealName")
    private String realName;
    @JsonProperty("MobilePhone")
    private String mobilePhone;
    @JsonProperty("NickName")
    private String nickName;
    @JsonProperty("HeadImage")
    private String headImage;
    @JsonProperty("AddDate")
    private String addDate;

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getHeadImage() {
//        StringUtils.isNotBlank(headImage)? "//i2.autoimg.cn/userscenter" + headImage : "";
        if(StringUtils.isNotBlank(headImage)){
            if(!headImage.contains("http")){
                if(!headImage.contains("//i2.autoimg")){
                    headImage = "https://i2.autoimg.cn/userscenter" + headImage;
                }
            }
        }else{
            headImage="";
        }

        return headImage;
    }

    public void setHeadImage(String headImage) {
        this.headImage = headImage;
    }

    public String getAddDate() {
        return addDate;
    }

    public void setAddDate(String addDate) {
        this.addDate = addDate;
    }
}
