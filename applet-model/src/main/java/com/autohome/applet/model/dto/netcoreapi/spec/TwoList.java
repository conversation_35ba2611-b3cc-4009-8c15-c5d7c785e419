package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TwoList {

    @JsonProperty("twoname")
    private String twoName;

    @JsonProperty("twolist")
    private List<?> twoList;

    private boolean isSame;

    private boolean isEmpty;

    public TwoList() {
    }

    public TwoList(String twoName, List<?> twoList, boolean isSame, boolean isEmpty) {
        this.twoName = twoName;
        this.twoList = twoList;
        this.isSame = isSame;
        this.isEmpty = isEmpty;
    }

    public String getTwoName() {
        return twoName;
    }

    public void setTwoName(String twoName) {
        this.twoName = twoName;
    }

    public List<?> getTwoList() {
        return twoList;
    }

    public void setTwoList(List<?> twoList) {
        this.twoList = twoList;
    }

    @JsonProperty("isSame")
    public boolean isSame() {
        return isSame;
    }

    public void setSame(boolean same) {
        isSame = same;
    }

    @JsonProperty("isEmpty")
    public boolean isEmpty() {
        return isEmpty;
    }

    public void setEmpty(boolean empty) {
        isEmpty = empty;
    }
}
