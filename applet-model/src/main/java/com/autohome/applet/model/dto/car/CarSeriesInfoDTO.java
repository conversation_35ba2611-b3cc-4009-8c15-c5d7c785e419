package com.autohome.applet.model.dto.car;

import lombok.Data;

/**
 * @description: 车系信息DTO
 * @author: WangBoWen
 * @date: 2023-11-28
 **/
@Data
public class CarSeriesInfoDTO {

    /**
     * 车系id
     */
    private Integer seriesid;

    /**
     * 车系名称
     */
    private String name;

    /**
     * 品牌id
     */
    private Integer brandid;

    /**
     * 品牌名
     */
    private String brandname;

    /**
     * 品牌logo
     */
    private String brandlogo;

    /**
     * 厂商id
     */
    private Integer fctid;

    /**
     * 厂商名
     */
    private String fctname;

    /**
     * 级别名称
     */
    private String level;

    /**
     * 车系代表图
     */
    private String pic;

    /**
     * 车系最低价
     */
    private String minprice;

    /**
     * 车系最高价
     */
    private String maxprice;

    /**
     * png图片
     */
    private String seriespnglogo;

}
