package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SeriesScoreRank implements KoubeiScoreListable, SeriesInfoSetable{
    // 
    private Integer number;
    // 排名
    private Integer rank;
    // 得分
    private Double score;
    // 车系编号
    private Integer seriesId;
    // 车系名称
    private String seriesName;
    // 车系logo
    private String seriespnglogo;
    // 最低价
    private int minPrice;
    // 最高价
    private int maxPrice;
    // 口碑评分
    private List<SeriesScoreList.Score> scoreList;

    @Override
    public void setScoreList(List<SeriesScoreList.Score> scoreList) {
        this.scoreList = scoreList;
    }

    public List<SeriesScoreList.Score> getScoreList() {
        return scoreList;
    }

    @Override
    public void setBrandId(int brandId) {

    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    @Override
    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSeriespnglogo() {
        return seriespnglogo;
    }

    @Override
    public void setSeriespnglogo(String seriespnglogo) {
        this.seriespnglogo = seriespnglogo;
    }

    public Integer getMinPrice() {
        return minPrice;
    }

    @Override
    public void setMinPrice(int minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getMaxPrice() {
        return maxPrice;
    }

    @Override
    public void setMaxPrice(int maxPrice) {
        this.maxPrice = maxPrice;
    }
}
