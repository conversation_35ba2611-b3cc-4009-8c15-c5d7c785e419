package com.autohome.applet.model.dto.newenergy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Random;
@JsonIgnoreProperties(ignoreUnknown = true)
public class KouBeiInfoDto {
    private Integer id;
    private Integer userId;
    private String nickName;
    private String headImage;
    private String feeling;
    private Integer seriesId;
    private String seriesName;
    private Integer specid;
    private String specName;
    private Integer commentCount;
    private Integer helpfulCount;
    private Integer visitCount;
    private List<String> photos;
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getHeadImage() {
        return headImage;
    }

    public void setHeadImage(String headImage) {
        this.headImage = headImage;
    }

    public String getFeeling() {
        return feeling;
    }

    public void setFeeling(String feeling) {
        this.feeling = feeling;
    }

    public Integer getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public Integer getSpecid() {
        return specid;
    }

    public void setSpecid(Integer specid) {
        this.specid = specid;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getHelpfulCount() {
        return helpfulCount;
    }

    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }

    public Integer getVisitCount() {
        return visitCount;
    }

    public void setVisitCount(Integer visitCount) {
        this.visitCount = visitCount;
    }

    public List<String> getPhotos() {
        return photos;
    }

    public void setPhotos(List<String> photos) {
        this.photos = photos;
    }
    public static String getHeadImageUrl(String headImageUrl) {
        //头像为空返回默认头像
        //http://x.autoimg.cn/space/images/head_120X120.gif App不返回默认头像，有客户端添加默认头像
        if (StringUtils.isBlank(headImageUrl)) {
            return "";
        }
        //完整路径头像直接返回
        headImageUrl = headImageUrl.replace("https://", "http://");
        if (headImageUrl.startsWith("https://") || headImageUrl.startsWith("http://")) {
            return headImageUrl;
        }
        if (headImageUrl.startsWith("//x.autoimg.cn")) {
            return "http:".concat(headImageUrl);
        }
        //论坛头像数据带有 "userheaders" ，需要把"userheaders"处理掉
        headImageUrl = headImageUrl.replace("userheaders", "");
        // int randNumber =rand.nextInt(MAX - MIN + 1) + MIN; // randNumber 将被赋值为一个 MIN 和 MAX 范围内的随机数
        int randNumber = new Random().nextInt(3 - 2 + 1) + 2;
        String domain = String.format("http://i%d.autoimg.cn/userscenter", randNumber);
        return domain.concat(headImageUrl);
    }

}
