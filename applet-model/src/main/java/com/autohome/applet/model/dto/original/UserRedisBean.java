package com.autohome.applet.model.dto.original;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserRedisBean {
    private static final long serialVersionUID = 1997845188117292347L;
    
    private long userId;
    private String nickName;
    private String headImg;
    /*
    用户简介
     */
    private String desc;

    private int userId_sec;
    private String nickName_sec;
    private String headImg_sec;

    private int userType;
    private int fansCount;

    private int certifiedSeriesId;
    private String certifiedSeriesName;
    private int certifiedLevel;
    
    /** 用户勋章列表 */
    private List<UserBadge> userBadges;

    /**
     * 勋章列表
     */
    private List<Badge> badges;
    
    /** 用户榜单信息 */
    private RankItem rankinfo;
    
    /** 用户入住汽车之家的时间 */
    private String regtime;
    
    /** 用户认证车主的时间 */
    private String carregtime;
    
    private Integer workscount;
    
    @Data
    public static class UserBadge implements Serializable {
        private static final long serialVersionUID = 2737874752980995290L;

        /** 领取序号 */
        private Integer sortNumber;

        /** 成就ID */
        private Integer achievementId;

        /** 成就名称 */
        private String badgeName;

        /** 成就图标 （获得） */
        private String badgeIcon;

        /** 0：没有领取序号  1:有领取序号 */
        private Integer issort;
    
        ///** 1-佩戴；2-最优价值；
        // * OGC、UGC 只返回佩戴勋章；PGC返回佩戴勋章和最优价值勋章
        // * */
        //private Integer badgetype;
    }

    /**
     * 勋章信息
     */
    @Data
    public static class Badge {

        /**
         * 勋章id
         */
        private Integer id;

        /**
         * 勋章标题
         */
        private String title;

        /**
         * 勋章图标
         */
        private String icon;

        /**
         * 勋章类型(用于跳转传参)
         */
        private Integer type;

        /**
         * 自定义勋章类型（1-佩戴；2-最优价值；
         * OGC、UGC 只返回佩戴勋章；PGC返回佩戴勋章和最优价值勋章）
         */
        private Integer badgeType;
    }
    
    @Data
    public static class RankItem implements Serializable {
        private static final long serialVersionUID = 2834079959659351157L;

        /** 榜单类型，详情参见：PgcRankingRankTypeEnum */
        private Integer rankType;

        /** 榜单分类，详情参见：PgcRankingRankCategoryEnum */
        private Integer rankCategory;

        /** 榜单排名 */
        private Integer ranking;

        /** 榜单名称 */
        private String rankName;

        /** 作者外显榜单文案 */
        private String showTag; // 没想好外显文案规则应该放后台还是前台，暂定先放后台
    }

    public String toString() {
        String nickname2nd = nickName_sec != null ? nickName_sec : "-";
        String headImg2nd = headImg_sec != null ? headImg_sec : "-";
        String result = "userId:" + userId + ",nickname:" + nickName + ",headImg:" + headImg + ",desc:" + desc + ",userType:" + userType + ",fansCount:" + fansCount + ",";
        result += "nickName_sec:" + nickname2nd + ",headImg_sec:" + headImg2nd + ",certifiedSeriesId:" + certifiedSeriesId + ",certifiedLevel:" + certifiedLevel;
        if (certifiedSeriesName != null) {
            result = result + ",certifiedSeriesName:" + certifiedSeriesName;
        }
        return result;
    }
}
