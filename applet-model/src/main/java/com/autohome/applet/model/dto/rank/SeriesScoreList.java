package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SeriesScoreList {
    private int seriesId;
    private List<Score> scoreList;

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public List<Score> getScoreList() {
        return scoreList;
    }

    public void setScoreList(List<Score> scoreList) {
        this.scoreList = scoreList;
    }

    public static class SeriesListScoreList {
        private List<SeriesScoreList> list;

        public List<SeriesScoreList> getList() {
            return list;
        }

        public void setList(List<SeriesScoreList> list) {
            this.list = list;
        }
    }

    public static class Score {
        private int rank;
        private float score;
        private int typeKey;
        private String typeName;

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getTypeKey() {
            return typeKey;
        }

        public void setTypeKey(int typeKey) {
            this.typeKey = typeKey;
        }

        public String getTypeName() {
            return typeName;
        }

        public void setTypeName(String typeName) {
            this.typeName = typeName;
        }
    }
}
