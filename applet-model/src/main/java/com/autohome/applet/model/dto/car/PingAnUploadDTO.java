package com.autohome.applet.model.dto.car;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PingAnUploadDTO<T> {
    private String reqId;
    private String msg;
    private int code;
    private T result;

    public static <T> PingAnUploadDTO<T> buildSuccessResult(int code, String msg) {
        PingAnUploadDTO<T> rt = new PingAnUploadDTO<>();
        rt.setCode(code);
        rt.setMsg(msg);
        return rt;
    }

    public static <T> PingAnUploadDTO<T> buildErrorResult(int code, String msg) {
        PingAnUploadDTO<T> rt = new PingAnUploadDTO<>();
        rt.setCode(code);
        rt.setMsg(msg);
        return rt;
    }
}
