package com.autohome.applet.model.dto.club;

/**
 * <AUTHOR>
 */
import com.fasterxml.jackson.annotation.JsonProperty;

public class BBSForum {
    @JsonProperty("bbsid")
    private int bbsId;

    @JsonProperty("bbs")
    private String bbs;

    @JsonProperty("bbsname")
    private String bbsName;

    @JsonProperty("topicCount")
    private int topicCount;

    @JsonProperty("bbsgroupid")
    private int bbsGroupId;

    @JsonProperty("bbsgroup")
    private String bbsGroup;

    @JsonProperty("firstletter")
    private String firstLetter;

    @JsonProperty("mergername")
    private String mergerName;

    @JsonProperty("bbsimgurl")
    private String bbsImgUrl;


    // 添加getter和setter方法
    public int getBbsId() {
        return bbsId;
    }

    public void setBbsId(int bbsId) {
        this.bbsId = bbsId;
    }

    public String getBbs() {
        return bbs;
    }

    public void setBbs(String bbs) {
        this.bbs = bbs;
    }

    public String getBbsName() {
        return bbsName;
    }

    public void setBbsName(String bbsName) {
        this.bbsName = bbsName;
    }

    public int getTopicCount() {
        return topicCount;
    }

    public void setTopicCount(int topicCount) {
        this.topicCount = topicCount;
    }

    public int getBbsGroupId() {
        return bbsGroupId;
    }

    public void setBbsGroupId(int bbsGroupId) {
        this.bbsGroupId = bbsGroupId;
    }

    public String getBbsGroup() {
        return bbsGroup;
    }

    public void setBbsGroup(String bbsGroup) {
        this.bbsGroup = bbsGroup;
    }

    public String getFirstLetter() {
        return firstLetter;
    }

    public void setFirstLetter(String firstLetter) {
        this.firstLetter = firstLetter;
    }

    public String getMergerName() {
        return mergerName;
    }

    public void setMergerName(String mergerName) {
        this.mergerName = mergerName;
    }

    public String getBbsImgUrl() {
        return bbsImgUrl;
    }

    public void setBbsImgUrl(String bbsImgUrl) {
        this.bbsImgUrl = bbsImgUrl;
    }
}

