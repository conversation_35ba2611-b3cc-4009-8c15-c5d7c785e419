package com.autohome.applet.model.dto.netcoreapi.series;

public class VRData {


    private int brandid;
    private String brandname;
    private String coverurl;
    private int id;
    private int likecount;
    private String publishtime;
    private int seriesid;
    private String seriesname;
    private String showurl;
    private int specid;
    private String specname;
    private String subtitle;
    private int type;
    private long visitcount;

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setCoverurl(String coverurl) {
        this.coverurl = coverurl;
    }

    public String getCoverurl() {
        return coverurl;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setLikecount(int likecount) {
        this.likecount = likecount;
    }

    public int getLikecount() {
        return likecount;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setShowurl(String showurl) {
        this.showurl = showurl;
    }

    public String getShowurl() {
        return showurl;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public void setVisitcount(long visitcount) {
        this.visitcount = visitcount;
    }

    public long getVisitcount() {
        return visitcount;
    }

}