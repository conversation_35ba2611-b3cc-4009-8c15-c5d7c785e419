package com.autohome.applet.model.dto.car;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AskFlow4User {

    @JsonProperty("recommedList")
    private List<RecommendList> recommendList;
    private String recommendTitle;
    private Object ext;
    private Integer specId;
    private Object favoriteDealer;
    private Object favoriteSaler;
    private Object onShop;

    public List<RecommendList> getRecommendList() {
        return recommendList;
    }

    public void setRecommendList(List<RecommendList> recommendList) {
        this.recommendList = recommendList;
    }

    public String getRecommendTitle() {
        return recommendTitle;
    }

    public void setRecommendTitle(String recommendTitle) {
        this.recommendTitle = recommendTitle;
    }

    public Object getExt() {
        return ext;
    }

    public void setExt(Object ext) {
        this.ext = ext;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Object getFavoriteDealer() {
        return favoriteDealer;
    }

    public void setFavoriteDealer(Object favoriteDealer) {
        this.favoriteDealer = favoriteDealer;
    }

    public Object getFavoriteSaler() {
        return favoriteSaler;
    }

    public void setFavoriteSaler(Object favoriteSaler) {
        this.favoriteSaler = favoriteSaler;
    }

    public Object getOnShop() {
        return onShop;
    }

    public void setOnShop(Object onShop) {
        this.onShop = onShop;
    }

    public static class RecommendList {
        private Integer seriesId;
        private String seriesName;
        private String seriesLogo;
        private Integer newsPrice;
        private String priceFormat;
        private Object officialKM;
        private Integer specId;
        private Object tips;

        public Integer getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(Integer seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesName() {
            return seriesName;
        }

        public void setSeriesName(String seriesName) {
            this.seriesName = seriesName;
        }

        public String getSeriesLogo() {
            return seriesLogo;
        }

        public void setSeriesLogo(String seriesLogo) {
            this.seriesLogo = seriesLogo;
        }

        public Integer getNewsPrice() {
            return newsPrice;
        }

        public void setNewsPrice(Integer newsPrice) {
            this.newsPrice = newsPrice;
        }

        public String getPriceFormat() {
            return priceFormat;
        }

        public void setPriceFormat(String priceFormat) {
            this.priceFormat = priceFormat;
        }

        public Object getOfficialKM() {
            return officialKM;
        }

        public void setOfficialKM(Object officialKM) {
            this.officialKM = officialKM;
        }

        public Integer getSpecId() {
            return specId;
        }

        public void setSpecId(Integer specId) {
            this.specId = specId;
        }

        public Object getTips() {
            return tips;
        }

        public void setTips(Object tips) {
            this.tips = tips;
        }
    }
}
