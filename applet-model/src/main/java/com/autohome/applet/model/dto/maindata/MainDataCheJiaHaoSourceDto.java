package com.autohome.applet.model.dto.maindata;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class MainDataCheJiaHaoSourceDto {
    @JsonProperty(value = "author_name")
    private String authorName;

    private Integer vv;

    private Integer pv;

    private Integer duration;

    private String summary;

    @JsonProperty(value = "chejiahao_Status")
    private Integer chejiahaoStatus;

    @JsonProperty(value = "main_data_type")
    private String mainDataType;

    @JsonProperty(value = "author_img")
    private String authorImg;

    private String title;

    @JsonProperty(value = "is_delete")
    private Integer isDelete;

    @JsonProperty(value = "m_url")
    private String mUrl;

    @JsonProperty(value = "biz_update_time")
    private String bizUpdateTime;

    @JsonProperty(value = "pool_biz_types")
    private List<Integer> poolBizTypes;

    @JsonProperty(value = "parent_biz_id")
    private Integer parentBizId;

    @JsonProperty(value = "img_url")
    private String imgUrl;

    @JsonProperty(value = "publish_time")
    private String publishTime;

    @JsonProperty(value = "chejiahao_IsRecommend")
    private Integer chejiahaoIsRecommend;

    @JsonProperty(value = "series_ids")
    private List<Integer> seriesIds;

    @JsonProperty(value = "author_id")
    private Integer authorId;

    @JsonProperty(value = "biz_id")
    private Integer bizId;

    @JsonProperty(value = "pc_url")
    private String pcUrl;
    @JsonProperty(value = "app_url")
    private String appUrl;

    @JsonProperty(value = "is_publish")
    private Integer isPublish;

    @JsonProperty(value = "small_title")
    private String smallTitle;

    @JsonProperty(value = "content_class1_names")
    private List<String> contentClass1Names;

    @JsonProperty(value = "content_class2_names")
    private List<String> contentClass2Names;

    @JsonProperty(value = "content_class3_names")
    private List<String> contentClass3Names;

    @JsonProperty(value = "chejiahao_tag_names")
    private List<String> chejiahaoTagNames;


    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Integer getVv() {
        return vv;
    }

    public void setVv(Integer vv) {
        this.vv = vv;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getPv() {
        return pv;
    }

    public void setPv(Integer pv) {
        this.pv = pv;
    }

    public Integer getChejiahaoStatus() {
        return chejiahaoStatus;
    }

    public void setChejiahaoStatus(Integer chejiahaoStatus) {
        this.chejiahaoStatus = chejiahaoStatus;
    }

    public String getMainDataType() {
        return mainDataType;
    }

    public void setMainDataType(String mainDataType) {
        this.mainDataType = mainDataType;
    }

    public String getAuthorImg() {
        return authorImg;
    }

    public void setAuthorImg(String authorImg) {
        this.authorImg = authorImg;
    }

    public List<String> getContentClass1Names() {
        return contentClass1Names;
    }

    public void setContentClass1Names(List<String> contentClass1Names) {
        this.contentClass1Names = contentClass1Names;
    }

    public List<String> getContentClass2Names() {
        return contentClass2Names;
    }

    public void setContentClass2Names(List<String> contentClass2Names) {
        this.contentClass2Names = contentClass2Names;
    }

    public List<String> getContentClass3Names() {
        return contentClass3Names;
    }

    public void setContentClass3Names(List<String> contentClass3Names) {
        this.contentClass3Names = contentClass3Names;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getmUrl() {
        return mUrl;
    }

    public void setmUrl(String mUrl) {
        this.mUrl = mUrl;
    }

    public String getBizUpdateTime() {
        return bizUpdateTime;
    }

    public void setBizUpdateTime(String bizUpdateTime) {
        this.bizUpdateTime = bizUpdateTime;
    }

    public List<Integer> getPoolBizTypes() {
        return poolBizTypes;
    }

    public void setPoolBizTypes(List<Integer> poolBizTypes) {
        this.poolBizTypes = poolBizTypes;
    }

    public Integer getParentBizId() {
        return parentBizId;
    }

    public void setParentBizId(Integer parentBizId) {
        this.parentBizId = parentBizId;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public Integer getChejiahaoIsRecommend() {
        return chejiahaoIsRecommend;
    }

    public void setChejiahaoIsRecommend(Integer chejiahaoIsRecommend) {
        this.chejiahaoIsRecommend = chejiahaoIsRecommend;
    }

    public List<Integer> getSeriesIds() {
        return seriesIds;
    }

    public void setSeriesIds(List<Integer> seriesIds) {
        this.seriesIds = seriesIds;
    }

    public Integer getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Integer authorId) {
        this.authorId = authorId;
    }

    public Integer getBizId() {
        return bizId;
    }

    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public Integer getIsPublish() {
        return isPublish;
    }

    public void setIsPublish(Integer isPublish) {
        this.isPublish = isPublish;
    }

    public String getSmallTitle() {
        return smallTitle;
    }

    public void setSmallTitle(String smallTitle) {
        this.smallTitle = smallTitle;
    }

    public List<String> getChejiahaoTagNames() {
        return chejiahaoTagNames;
    }

    public void setChejiahaoTagNames(List<String> chejiahaoTagNames) {
        this.chejiahaoTagNames = chejiahaoTagNames;
    }

    public String getAppUrl() {
        return appUrl == null ? "" : appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl == null ? "" : appUrl;
    }
}
