package com.autohome.applet.model.dto.netcoreapi.series;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MinPriceItem {

    @JsonProperty("AppSellPhone")
    private String appSellPhone;

    @JsonProperty("CityId")
    private long cityId;

    @JsonProperty("CityName")
    private String cityName;
    private String copa;

    @JsonProperty("DealerId")
    private long dealerId;

    @JsonProperty("DealerName")
    private String dealerName;

    @JsonProperty("DealerPackage")
    private String dealerPackage;

    @JsonProperty("DealerPhone")
    private String dealerPhone;

    @JsonProperty("DealPrice")
    private long dealPrice;

    @JsonProperty("DealPriceDetails")
    private MiniPriceDealPriceDetail dealPriceDetails;

    @JsonProperty("HasIMSaler")
    private boolean hasIMSaler;

    @JsonProperty("InventoryState")
    private int inventoryState;

    @<PERSON>sonProperty("MapLatBaidu")
    private double mapLatBaidu;

    @JsonProperty("MapLonBaidu")
    private double mapLonBaidu;

    @JsonProperty("MinPrice")
    private long minPrice;

    @JsonProperty("OriginalPrice")
    private long originalPrice;

    @JsonProperty("PackageId")
    private int packageId;

    @JsonProperty("Price")
    private long price;

    @JsonProperty("ProvinceId")
    private long provinceId;

    @JsonProperty("ProvinceName")
    private String provinceName;

    @JsonProperty("SpecId")
    private int specId;

    @JsonProperty("Url")
    private String url;

    public String getAppSellPhone() {
        return appSellPhone;
    }

    public void setAppSellPhone(String appSellPhone) {
        this.appSellPhone = appSellPhone;
    }

    public long getCityId() {
        return cityId;
    }

    public void setCityId(long cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCopa() {
        return copa;
    }

    public void setCopa(String copa) {
        this.copa = copa;
    }

    public long getDealerId() {
        return dealerId;
    }

    public void setDealerId(long dealerId) {
        this.dealerId = dealerId;
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerPackage() {
        return dealerPackage;
    }

    public void setDealerPackage(String dealerPackage) {
        this.dealerPackage = dealerPackage;
    }

    public String getDealerPhone() {
        return dealerPhone;
    }

    public void setDealerPhone(String dealerPhone) {
        this.dealerPhone = dealerPhone;
    }

    public long getDealPrice() {
        return dealPrice;
    }

    public void setDealPrice(long dealPrice) {
        this.dealPrice = dealPrice;
    }

    public MiniPriceDealPriceDetail getDealPriceDetails() {
        return dealPriceDetails;
    }

    public void setDealPriceDetails(MiniPriceDealPriceDetail dealPriceDetails) {
        this.dealPriceDetails = dealPriceDetails;
    }

    public boolean isHasIMSaler() {
        return hasIMSaler;
    }

    public void setHasIMSaler(boolean hasIMSaler) {
        this.hasIMSaler = hasIMSaler;
    }

    public int getInventoryState() {
        return inventoryState;
    }

    public void setInventoryState(int inventoryState) {
        this.inventoryState = inventoryState;
    }

    public double getMapLatBaidu() {
        return mapLatBaidu;
    }

    public void setMapLatBaidu(double mapLatBaidu) {
        this.mapLatBaidu = mapLatBaidu;
    }

    public double getMapLonBaidu() {
        return mapLonBaidu;
    }

    public void setMapLonBaidu(double mapLonBaidu) {
        this.mapLonBaidu = mapLonBaidu;
    }

    public long getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(long minPrice) {
        this.minPrice = minPrice;
    }

    public long getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(long originalPrice) {
        this.originalPrice = originalPrice;
    }

    public int getPackageId() {
        return packageId;
    }

    public void setPackageId(int packageId) {
        this.packageId = packageId;
    }

    public long getPrice() {
        return price;
    }

    public void setPrice(long price) {
        this.price = price;
    }

    public long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(long provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}