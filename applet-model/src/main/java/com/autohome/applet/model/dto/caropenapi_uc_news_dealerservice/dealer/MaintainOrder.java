package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.netcoreapi.StringUtil;

import java.util.List;

/**
 * Created by ha<PERSON><PERSON><PERSON> on 2019/5/20.
 */
public class MaintainOrder {
    private List<Orderlist> list;
    private int rowcount;
    private int pagecount;
    private int pageindex;

    public List<Orderlist> getList() {
        return list;
    }

    public void setList(List<Orderlist> list) {
        this.list = list;
    }

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public static class Orderlist {

        private int orderid;
        private int ordertype;
        private int orderstate;
        private String customername;
        private String customerphone;
        private String appointstarttime;
        private String appointendtime;
        private String ordertime;
        private int brandid;
        private String brandname;
        private int seriesid;
        private String seriesname;
        private int specid;
        private String specname;
        private int dealerid;
        private String dealername;
        private int salesid;
        private String salesname;
        private String salesphone;
        private String salesheadpic;
        private int maintainmode;
        private String drivingcartime;
        private int drivingmaintainstate;
        private String platenumber;
        private String speclogo;
        private String seriespicurl;

        public String getSeriespicurl() {
            return seriespicurl;
        }

        public void setSeriespicurl(String seriespicurl) {
            this.seriespicurl = seriespicurl;
        }

        public String getSpeclogo() {
            if (StringUtil.isBlank(speclogo)) {
                return seriespicurl;
            }
            return speclogo;
        }

        public void setSpeclogo(String speclogo) {
            this.speclogo = speclogo;
        }

        public void setOrderid(int orderid) {
            this.orderid = orderid;
        }

        public int getOrderid() {
            return orderid;
        }

        public void setOrdertype(int ordertype) {
            this.ordertype = ordertype;
        }

        public int getOrdertype() {
            return ordertype;
        }

        public void setOrderstate(int orderstate) {
            this.orderstate = orderstate;
        }

        public int getOrderstate() {
            return orderstate;
        }

        public void setCustomername(String customername) {
            this.customername = customername;
        }

        public String getCustomername() {
            return customername;
        }

        public void setCustomerphone(String customerphone) {
            this.customerphone = customerphone;
        }

        public String getCustomerphone() {
            return customerphone;
        }

        public void setAppointstarttime(String appointstarttime) {
            this.appointstarttime = appointstarttime;
        }

        public String getAppointstarttime() {
            return appointstarttime;
        }

        public void setAppointendtime(String appointendtime) {
            this.appointendtime = appointendtime;
        }

        public String getAppointendtime() {
            return appointendtime;
        }

        public void setOrdertime(String ordertime) {
            this.ordertime = ordertime;
        }

        public String getOrdertime() {
            return ordertime;
        }

        public void setBrandid(int brandid) {
            this.brandid = brandid;
        }

        public int getBrandid() {
            return brandid;
        }

        public void setBrandname(String brandname) {
            this.brandname = brandname;
        }

        public String getBrandname() {
            return brandname;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setDealerid(int dealerid) {
            this.dealerid = dealerid;
        }

        public int getDealerid() {
            return dealerid;
        }

        public void setDealername(String dealername) {
            this.dealername = dealername;
        }

        public String getDealername() {
            return dealername;
        }

        public void setSalesid(int salesid) {
            this.salesid = salesid;
        }

        public int getSalesid() {
            return salesid;
        }

        public void setSalesname(String salesname) {
            this.salesname = salesname;
        }

        public String getSalesname() {
            return salesname;
        }

        public void setSalesphone(String salesphone) {
            this.salesphone = salesphone;
        }

        public String getSalesphone() {
            return salesphone;
        }

        public void setSalesheadpic(String salesheadpic) {
            this.salesheadpic = salesheadpic;
        }

        public String getSalesheadpic() {
            return salesheadpic;
        }

        public void setMaintainmode(int maintainmode) {
            this.maintainmode = maintainmode;
        }

        public int getMaintainmode() {
            return maintainmode;
        }

        public void setDrivingcartime(String drivingcartime) {
            this.drivingcartime = drivingcartime;
        }

        public String getDrivingcartime() {
            return drivingcartime;
        }

        public void setDrivingmaintainstate(int drivingmaintainstate) {
            this.drivingmaintainstate = drivingmaintainstate;
        }

        public int getDrivingmaintainstate() {
            return drivingmaintainstate;
        }

        public void setPlatenumber(String platenumber) {
            this.platenumber = platenumber;
        }

        public String getPlatenumber() {
            return platenumber;
        }

    }
}
