package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by ha<PERSON><PERSON>feng on 2019/5/9.
 */
public class AutoSmallVideo {

    private String pvid;
    private List<SmallVideoList> list;

    public String getPvid() {
        return pvid;
    }

    public void setPvid(String pvid) {
        this.pvid = pvid;
    }

    public List<SmallVideoList> getList() {
        return list;
    }

    public void setList(List<SmallVideoList> list) {
        this.list = list;
    }

    public static class Feednag {

        @JsonProperty("FN_tagdimid")
        private int fnTagdimid;
        @JsonProperty("FN_tagid")
        private int fnTagid;
        @JsonProperty("FN_tagtitle")
        private String fnTagtitle;

        public void setFnTagdimid(int fnTagdimid) {
            this.fnTagdimid = fnTagdimid;
        }

        public int getFnTagdimid() {
            return fnTagdimid;
        }

        public void setFnTagid(int fnTagid) {
            this.fnTagid = fnTagid;
        }

        public int getFnTagid() {
            return fnTagid;
        }

        public void setFnTagtitle(String fnTagtitle) {
            this.fnTagtitle = fnTagtitle;
        }

        public String getFnTagtitle() {
            return fnTagtitle;
        }

    }

    public static class Bigimgmode {

        private int cardtype;
        private String cardextendattr;
        private String cardextendimg;

        public void setCardtype(int cardtype) {
            this.cardtype = cardtype;
        }

        public int getCardtype() {
            return cardtype;
        }

        public void setCardextendattr(String cardextendattr) {
            this.cardextendattr = cardextendattr;
        }

        public String getCardextendattr() {
            return cardextendattr;
        }

        public void setCardextendimg(String cardextendimg) {
            this.cardextendimg = cardextendimg;
        }

        public String getCardextendimg() {
            return cardextendimg;
        }

    }

    public static class SmallVideoList {

        private String publishtime;
        private int objid;
        private int datatype;
        private List<Feednag> feednag;
        private Bigimgmode bigimgmode;
        private int mdbid;
        private String title;
        private String imgurl;
        private int viewcount;
        private int replycount;
        private String updatetime;
        private int subjectid;
        private String imgurl2;
        private String vedioduration;
        private String authorname;
        //        @JsonProperty("AuthorName")
//        private String authorname;
        @JsonProperty("jump_url")
        private String jumpUrl;
        @JsonProperty("v_id")
        private String vId;
        @JsonProperty("video_id")
        private String videoId;
        //        @JsonProperty("Author_id")
//        private int authorId;
        @JsonProperty("author_id")
        private int authorId;
        @JsonProperty("series_id")
        private String seriesId;
        private String datatypename;
        private String imgurl3;
        private String imgurl4;
        private int popularity;
        @JsonProperty("author_icon")
        private String authorIcon;
        private int liveline;
        private String summary;
        @JsonProperty("share_url")
        private String shareUrl;
        @JsonProperty("geo_point")
        private String geoPoint;
        @JsonProperty("video_width")
        private int videoWidth;
        @JsonProperty("video_height")
        private int videoHeight;
        @JsonProperty("live_albumname")
        private String liveAlbumname;
        @JsonProperty("author_roles")
        private List<Integer> authorRoles;
        private String shoufu;
        @JsonProperty("video_img_url")
        private String videoImgUrl;
        @JsonProperty("is_yingxiao")
        private int isYingxiao;
        @JsonProperty("info_obj")
        private String infoObj;
        private int modetype;

        public String getAuthorname() {
            return authorname;
        }

        public void setAuthorname(String authorname) {
            this.authorname = authorname;
        }

        public String getvId() {
            return vId;
        }

        public void setvId(String vId) {
            this.vId = vId;
        }

        public int getAuthorId() {
            return authorId;
        }

        public void setAuthorId(int authorId) {
            this.authorId = authorId;
        }

        public void setPublishtime(String publishtime) {
            this.publishtime = publishtime;
        }

        public String getPublishtime() {
            return publishtime;
        }

        public void setObjid(int objid) {
            this.objid = objid;
        }

        public int getObjid() {
            return objid;
        }

        public void setDatatype(int datatype) {
            this.datatype = datatype;
        }

        public int getDatatype() {
            return datatype;
        }

        public void setFeednag(List<Feednag> feednag) {
            this.feednag = feednag;
        }

        public List<Feednag> getFeednag() {
            return feednag;
        }

        public void setBigimgmode(Bigimgmode bigimgmode) {
            this.bigimgmode = bigimgmode;
        }

        public Bigimgmode getBigimgmode() {
            return bigimgmode;
        }

        public void setMdbid(int mdbid) {
            this.mdbid = mdbid;
        }

        public int getMdbid() {
            return mdbid;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitle() {
            return title;
        }

        public void setImgurl(String imgurl) {
            this.imgurl = imgurl;
        }

        public String getImgurl() {
//            if (StringUtils.isNotNullAndWhiteSpace(imgurl)) {
//                return imgurl.replace("https://", "//");
//            }
            return imgurl;
        }

        public void setViewcount(int viewcount) {
            this.viewcount = viewcount;
        }

        public int getViewcount() {
            return viewcount;
        }

        public void setReplycount(int replycount) {
            this.replycount = replycount;
        }

        public int getReplycount() {
            return replycount;
        }

        public void setUpdatetime(String updatetime) {
            this.updatetime = updatetime;
        }

        public String getUpdatetime() {
            return updatetime;
        }

        public void setSubjectid(int subjectid) {
            this.subjectid = subjectid;
        }

        public int getSubjectid() {
            return subjectid;
        }

        public void setImgurl2(String imgurl2) {
            this.imgurl2 = imgurl2;
        }

        public String getImgurl2() {
//            if (StringUtils.isNotNullAndWhiteSpace(imgurl2)) {
//                return imgurl2.replace("https://", "//");
//            }
            return imgurl2;
        }

        public void setVedioduration(String vedioduration) {
            this.vedioduration = vedioduration;
        }

        public String getVedioduration() {
            return vedioduration;
        }


        public void setJumpUrl(String jumpUrl) {
            this.jumpUrl = jumpUrl;
        }

        public String getJumpUrl() {
//            if (StringUtils.isNotNullAndWhiteSpace(jumpUrl)) {
//                return jumpUrl.replace("https://", "//");
//            }
            return jumpUrl;
        }

        public void setVId(String vId) {
            this.vId = vId;
        }

        public String getVId() {
            return vId;
        }

        public void setVideoId(String videoId) {
            this.videoId = videoId;
        }

        public String getVideoId() {
            return videoId;
        }


        public void setSeriesId(String seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesId() {
            return seriesId;
        }

        public void setDatatypename(String datatypename) {
            this.datatypename = datatypename;
        }

        public String getDatatypename() {
            return datatypename;
        }

        public void setImgurl3(String imgurl3) {
            this.imgurl3 = imgurl3;
        }

        public String getImgurl3() {
//            if (StringUtils.isNotNullAndWhiteSpace(imgurl3)) {
//                return imgurl3.replace("https://", "//");
//            }
            return imgurl3;
        }

        public void setImgurl4(String imgurl4) {
            this.imgurl4 = imgurl4;
        }

        public String getImgurl4() {
//            if (StringUtils.isNotNullAndWhiteSpace(imgurl4)) {
//                return imgurl4.replace("https://", "//");
//            }
            return imgurl4;
        }

        public void setPopularity(int popularity) {
            this.popularity = popularity;
        }

        public int getPopularity() {
            return popularity;
        }

        public void setAuthorIcon(String authorIcon) {
            this.authorIcon = authorIcon;
        }

        public String getAuthorIcon() {
//            if(StringUtils.isNotNullAndWhiteSpace(authorIcon)){
//                return authorIcon.replace("https://","//");
//            }
            return authorIcon;
        }

        public void setLiveline(int liveline) {
            this.liveline = liveline;
        }

        public int getLiveline() {
            return liveline;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }

        public String getSummary() {
            return summary;
        }

        public void setShareUrl(String shareUrl) {
            this.shareUrl = shareUrl;
        }

        public String getShareUrl() {
//            if (StringUtils.isNotNullAndWhiteSpace(shareUrl)) {
//                return shareUrl.replace("https://", "//");
//            }
            return shareUrl;
        }

        public void setGeoPoint(String geoPoint) {
            this.geoPoint = geoPoint;
        }

        public String getGeoPoint() {
            return geoPoint;
        }

        public void setVideoWidth(int videoWidth) {
            this.videoWidth = videoWidth;
        }

        public int getVideoWidth() {
            return videoWidth;
        }

        public void setVideoHeight(int videoHeight) {
            this.videoHeight = videoHeight;
        }

        public int getVideoHeight() {
            return videoHeight;
        }

        public void setLiveAlbumname(String liveAlbumname) {
            this.liveAlbumname = liveAlbumname;
        }

        public String getLiveAlbumname() {
            return liveAlbumname;
        }

        public void setAuthorRoles(List<Integer> authorRoles) {
            this.authorRoles = authorRoles;
        }

        public List<Integer> getAuthorRoles() {
            return authorRoles;
        }

        public void setShoufu(String shoufu) {
            this.shoufu = shoufu;
        }

        public String getShoufu() {
            return shoufu;
        }

        public void setVideoImgUrl(String videoImgUrl) {
            this.videoImgUrl = videoImgUrl;
        }

        public String getVideoImgUrl() {
//            if (StringUtils.isNotNullAndWhiteSpace(videoImgUrl)) {
//                return videoImgUrl.replace("https://", "//");
//            }
            return videoImgUrl;
        }

        public void setIsYingxiao(int isYingxiao) {
            this.isYingxiao = isYingxiao;
        }

        public int getIsYingxiao() {
            return isYingxiao;
        }

        public void setInfoObj(String infoObj) {
            this.infoObj = infoObj;
        }

        public String getInfoObj() {
            return infoObj;
        }

        public void setModetype(int modetype) {
            this.modetype = modetype;
        }

        public int getModetype() {
            return modetype;
        }

    }
}
