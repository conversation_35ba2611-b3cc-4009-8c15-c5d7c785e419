package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.HtmlRegexpUtil;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.ProjTransform;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by hans<PERSON>feng on 2019/4/22.
 */
public class CompleteDealerInfo {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<DealerList> list;
    private DealerImages dealerImages;
    private DealerBusiness dealerBusiness;

    public DealerBusiness getDealerBusiness() {
        return dealerBusiness;
    }

    public void setDealerBusiness(DealerBusiness dealerBusiness) {
        this.dealerBusiness = dealerBusiness;
    }

    public DealerImages getDealerImages() {
        if (dealerImages == null || dealerImages.getList() == null || dealerImages.getList().size() == 0) {
            dealerImages = new DealerImages();
            DealerImages.ImageList imge = new DealerImages.ImageList();
            List<DealerImages.ImageList> imageLists = new ArrayList<>();
            imageLists.add(imge);
            dealerImages.setList(imageLists);
            return dealerImages;
        }
        return dealerImages;
    }

    public void setDealerImages(DealerImages dealerImages) {
        this.dealerImages = dealerImages;
    }

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<DealerList> getList() {
        return list;
    }

    public void setList(List<DealerList> list) {
        this.list = list;
    }

    public static class DealerList {
        private String LicenseUrl;
        @JsonProperty("ProvinceID")
        private int provinceid;
        @JsonProperty("CityID")
        private int cityid;
        @JsonProperty("CountyID")
        private int countyid;
        @JsonProperty("AppSellPhone")
        private String appsellphone;
        @JsonProperty("HasIMSaler")
        private boolean hasimsaler;
        @JsonProperty("LowPrice")
        private int lowprice;
        @JsonProperty("MapLatBaidu")
        private String maplatbaidu;
        @JsonProperty("MapLonBaidu")
        private String maplonbaidu;
        @JsonProperty("LeadsRangeType")
        private int leadsrangetype;
        @JsonProperty("CustomRangeProvinces")
        private String customrangeprovinces;
        @JsonProperty("CustomRangeCities")
        private String customrangecities;
        @JsonProperty("DealerInfoId")
        private int dealerinfoid;
        @JsonProperty("CityName")
        private String cityname;
        @JsonProperty("DealerHeaderImage")
        private String dealerheaderimage;
        @JsonProperty("AsSellPhone")
        private String assellphone;
        @JsonProperty("IsAuth")
        private boolean isauth;
        @JsonProperty("PayType")
        private int paytype;
        @JsonProperty("ServicePhone")
        private String servicephone;
        @JsonProperty("IsGreatQuality")
        private boolean isgreatquality;
        @JsonProperty("LeadsRatingScore")
        private int leadsratingscore;
        @JsonProperty("CallRate400")
        private int callrate400;
        @JsonProperty("StarLevel")
        private int starlevel;
        @JsonProperty("MainBrandImage")
        private String mainbrandimage;
        @JsonProperty("BusinessArea")
        private String businessarea;
        @JsonProperty("OrderRangeTitle")
        private String orderrangetitle;
        @JsonProperty("Brands")
        private List<String> brands;
        @JsonProperty("DealerId")
        private int dealerid;
        @JsonProperty("CompanySimple")
        private String companysimple;
        @JsonProperty("Company")
        private String company;
        @JsonProperty("KindId")
        private int kindid;
        @JsonProperty("Address")
        private String Address;
        @JsonProperty("PID")
        private int pid;
        @JsonProperty("CID")
        private int cid;
        @JsonProperty("SID")
        private int sid;
        @JsonProperty("SellPhone")
        private String sellphone;
        @JsonProperty("StyledSellPhone")
        private String styledsellphone;
        @JsonProperty("StyledSellPhoneV2")
        private String styledsellphonev2;
        @JsonProperty("Description")
        private String description;
        @JsonProperty("StyledServicePhone")
        private String styledservicephone;
        @JsonProperty("Fax")
        private String fax;
        @JsonProperty("Is24h")
        private boolean is24h;
        @JsonProperty("Website")
        private String website;
        @JsonProperty("LinkMan")
        private String linkman;
        @JsonProperty("Route")
        private String route;
        @JsonProperty("CloseOrder")
        private int closeorder;
        @JsonProperty("MapbarLatitude")
        private String mapbarlatitude;
        @JsonProperty("MapbarLongitude")
        private String mapbarlongitude;
        //是否虚拟店铺
        private int isVirtualShop;
//        @JsonProperty("GoogleLatitude")
//        private String googlelatitude;
//        @JsonProperty("GoogleLongitude")
//        private String googlelongitude;

        private String wxLatitude;
        private String wxLongitude;

        public int getIsVirtualShop() {
            return isVirtualShop;
        }

        public void setIsVirtualShop(int isVirtualShop) {
            this.isVirtualShop = isVirtualShop;
        }

        public String getWxLatitude() {
            if (StringUtil.isBlank(this.getMaplonbaidu())) {
                return wxLatitude;
            }
            try {
                ProjTransform proj = new ProjTransform();
                double[] resultLat = proj.bMapTransqqMap(Double.parseDouble(this.getMaplonbaidu()), Double.parseDouble(this.getMaplatbaidu()));
                if (resultLat != null && resultLat.length > 1) {
                    this.wxLatitude = Double.toString(resultLat[1]);
                    this.wxLongitude = Double.toString(resultLat[0]);
                }
            } catch (NumberFormatException e) {
                return wxLatitude;
            }
            return wxLatitude;
        }

        public void setWxLatitude(String wxLatitude) {
            this.wxLatitude = wxLatitude;
        }

        public String getWxLongitude() {
            return wxLongitude;
        }

        public void setWxLongitude(String wxLongitude) {
            this.wxLongitude = wxLongitude;
        }

        public String getLicenseUrl() {
            if (StringUtil.isNotNullAndWhiteSpace(LicenseUrl) && LicenseUrl.indexOf("https:////") >= 0)
                return LicenseUrl.replace("https:////", "https://");
            return LicenseUrl;
        }

        public void setLicenseUrl(String licenseUrl) {
            LicenseUrl = licenseUrl;
        }

        public void setProvinceid(int provinceid) {
            this.provinceid = provinceid;
        }

        public int getProvinceid() {
            return provinceid;
        }

        public void setCityid(int cityid) {
            this.cityid = cityid;
        }

        public int getCityid() {
            return cityid;
        }

        public void setCountyid(int countyid) {
            this.countyid = countyid;
        }

        public int getCountyid() {
            return countyid;
        }

        public void setAppsellphone(String appsellphone) {
            this.appsellphone = appsellphone;
        }

        public String getAppsellphone() {
            return appsellphone;
        }

        public void setHasimsaler(boolean hasimsaler) {
            this.hasimsaler = hasimsaler;
        }

        public boolean getHasimsaler() {
            return hasimsaler;
        }

        public void setLowprice(int lowprice) {
            this.lowprice = lowprice;
        }

        public int getLowprice() {
            return lowprice;
        }

        public void setMaplatbaidu(String maplatbaidu) {
            this.maplatbaidu = maplatbaidu;
        }

        public String getMaplatbaidu() {
            return maplatbaidu;
        }

        public void setMaplonbaidu(String maplonbaidu) {
            this.maplonbaidu = maplonbaidu;
        }

        public String getMaplonbaidu() {
            return maplonbaidu;
        }

        public void setLeadsrangetype(int leadsrangetype) {
            this.leadsrangetype = leadsrangetype;
        }

        public int getLeadsrangetype() {
            return leadsrangetype;
        }

        public void setCustomrangeprovinces(String customrangeprovinces) {
            this.customrangeprovinces = customrangeprovinces;
        }

        public String getCustomrangeprovinces() {
            return customrangeprovinces;
        }

        public void setCustomrangecities(String customrangecities) {
            this.customrangecities = customrangecities;
        }

        public String getCustomrangecities() {
            return customrangecities;
        }

        public void setDealerinfoid(int dealerinfoid) {
            this.dealerinfoid = dealerinfoid;
        }

        public int getDealerinfoid() {
            return dealerinfoid;
        }

        public void setCityname(String cityname) {
            this.cityname = cityname;
        }

        public String getCityname() {
            return cityname;
        }

        public void setDealerheaderimage(String dealerheaderimage) {
            this.dealerheaderimage = dealerheaderimage;
        }

        public String getDealerheaderimage() {
            if (StringUtil.isNotNullAndWhiteSpace(dealerheaderimage))
                return dealerheaderimage.replace("http://", "https://");
            return dealerheaderimage;
        }

        public void setAssellphone(String assellphone) {
            this.assellphone = assellphone;
        }

        public String getAssellphone() {
            return assellphone;
        }

        public void setIsauth(boolean isauth) {
            this.isauth = isauth;
        }

        public boolean getIsauth() {
            return isauth;
        }

        public void setPaytype(int paytype) {
            this.paytype = paytype;
        }

        public int getPaytype() {
            return paytype;
        }

        public void setServicephone(String servicephone) {
            this.servicephone = servicephone;
        }

        public String getServicephone() {
            return servicephone;
        }

        public void setIsgreatquality(boolean isgreatquality) {
            this.isgreatquality = isgreatquality;
        }

        public boolean getIsgreatquality() {
            return isgreatquality;
        }

        public void setLeadsratingscore(int leadsratingscore) {
            this.leadsratingscore = leadsratingscore;
        }

        public int getLeadsratingscore() {
            return leadsratingscore;
        }

        public void setCallrate400(int callrate400) {
            this.callrate400 = callrate400;
        }

        public int getCallrate400() {
            return callrate400;
        }

        public void setStarlevel(int starlevel) {
            this.starlevel = starlevel;
        }

        public int getStarlevel() {
            return starlevel;
        }

        public void setMainbrandimage(String mainbrandimage) {
            this.mainbrandimage = mainbrandimage;
        }

        public String getMainbrandimage() {
            if (StringUtil.isNotNullAndWhiteSpace(mainbrandimage))
                return mainbrandimage.replace("http://", "https://");
            else
                return mainbrandimage;
        }

        public void setBusinessarea(String businessarea) {
            this.businessarea = businessarea;
        }

        public String getBusinessarea() {
            return businessarea;
        }

        public void setOrderrangetitle(String orderrangetitle) {
            this.orderrangetitle = orderrangetitle;
        }

        public String getOrderrangetitle() {
            return orderrangetitle;
        }

        public void setBrands(List<String> brands) {
            this.brands = brands;
        }

        public List<String> getBrands() {
            return brands;
        }

        public void setDealerid(int dealerid) {
            this.dealerid = dealerid;
        }

        public int getDealerid() {
            return dealerid;
        }

        public void setCompanysimple(String companysimple) {
            this.companysimple = companysimple;
        }

        public String getCompanysimple() {
            return companysimple;
        }

        public void setCompany(String company) {
            this.company = company;
        }

        public String getCompany() {
            return company;
        }

        public void setKindid(int kindid) {
            this.kindid = kindid;
        }

        public int getKindid() {
            return kindid;
        }

        public void setAddress(String address) {
            this.Address = address;
        }

        public String getAddress() {
            return Address;
        }

        public void setPid(int pid) {
            this.pid = pid;
        }

        public int getPid() {
            return pid;
        }

        public void setCid(int cid) {
            this.cid = cid;
        }

        public int getCid() {
            return cid;
        }

        public void setSid(int sid) {
            this.sid = sid;
        }

        public int getSid() {
            return sid;
        }

        public void setSellphone(String sellphone) {
            this.sellphone = sellphone;
        }

        public String getSellphone() {
            return sellphone;
        }

        public void setStyledsellphone(String styledsellphone) {
            this.styledsellphone = styledsellphone;
        }

        public String getStyledsellphone() {
            return styledsellphone;
        }

        public void setStyledsellphonev2(String styledsellphonev2) {
            this.styledsellphonev2 = styledsellphonev2;
        }

        public String getStyledsellphonev2() {
            return styledsellphonev2;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getDescription() {
            if (StringUtil.isNotNullAndWhiteSpace(description)) {
                return HtmlRegexpUtil.filterHtmlAttr(description, "style=\\\"(.*?)\\\"");
            }
            return description;
        }

        public void setStyledservicephone(String styledservicephone) {
            this.styledservicephone = styledservicephone;
        }

        public String getStyledservicephone() {
            return styledservicephone;
        }

        public void setFax(String fax) {
            this.fax = fax;
        }

        public String getFax() {
            return fax;
        }

        public void setIs24h(boolean is24h) {
            this.is24h = is24h;
        }

        public boolean getIs24h() {
            return is24h;
        }

        public void setWebsite(String website) {
            this.website = website;
        }

        public String getWebsite() {
            return website;
        }

        public void setLinkman(String linkman) {
            this.linkman = linkman;
        }

        public String getLinkman() {
            return linkman;
        }

        public void setRoute(String route) {
            this.route = route;
        }

        public String getRoute() {
            return route;
        }

        public void setCloseorder(int closeorder) {
            this.closeorder = closeorder;
        }

        public int getCloseorder() {
            return closeorder;
        }

        public void setMapbarlatitude(String mapbarlatitude) {
            this.mapbarlatitude = mapbarlatitude;
        }

        public String getMapbarlatitude() {
            if (StringUtil.isBlank(mapbarlatitude) || mapbarlatitude.equals("0")) {
                return maplatbaidu;
            }
            return mapbarlatitude;
        }

        public void setMapbarlongitude(String mapbarlongitude) {
            this.mapbarlongitude = mapbarlongitude;
        }

        public String getMapbarlongitude() {
            if (StringUtil.isBlank(mapbarlongitude) || mapbarlongitude.equals("0")) {
                return maplonbaidu;
            }
            return mapbarlongitude;
        }
    }
}
