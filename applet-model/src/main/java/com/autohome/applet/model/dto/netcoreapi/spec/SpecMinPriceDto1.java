package com.autohome.applet.model.dto.netcoreapi.spec;

import com.autohome.applet.model.dto.dealer.SpecMinPrice;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 经销商报价
 * <AUTHOR>
 */
public class SpecMinPriceDto1 {
    @JsonProperty("seriesId")
    private int seriesId;

    @JsonProperty("specId")
    private int specId;

    @JsonProperty("newsId")
    private int newsId;

    @JsonProperty("dealerId")
    private int dealerId;

    @JsonProperty("newsPrice")
    private String newsPrice;

    @JsonProperty("minOriginalPrice")
    private String minOriginalPrice;

    @JsonProperty("maxOriginalPrice")
    private String maxOriginalPrice;

    @JsonProperty("payTypeSequenceOne")
    private int payTypeSequenceOne;

    @JsonProperty("seriesName")
    private String seriesName;

    @JsonProperty("specName")
    private String specName;

    @JsonProperty("dealerSimpleName")
    private String dealerSimpleName;

    @JsonProperty("specImageUrl")
    private String specImageUrl;

    @JsonProperty("starLevel")
    private int starLevel;

    public SpecMinPriceDto1() {
    }

    public SpecMinPriceDto1(int seriesId, int specId, int newsId, int dealerId, String newsPrice, String minOriginalPrice, String maxOriginalPrice, int payTypeSequenceOne, String seriesName, String specName, String dealerSimpleName, String specImageUrl, int starLevel) {
        this.seriesId = seriesId;
        this.specId = specId;
        this.newsId = newsId;
        this.dealerId = dealerId;
        this.newsPrice = newsPrice;
        this.minOriginalPrice = minOriginalPrice;
        this.maxOriginalPrice = maxOriginalPrice;
        this.payTypeSequenceOne = payTypeSequenceOne;
        this.seriesName = seriesName;
        this.specName = specName;
        this.dealerSimpleName = dealerSimpleName;
        this.specImageUrl = specImageUrl;
        this.starLevel = starLevel;
    }

    public static SpecMinPriceDto1 by(SpecMinPrice priceInfo) {
        return new SpecMinPriceDto1(
        priceInfo.getSeriesId(),
                priceInfo.getSpecId(),
                priceInfo.getNewsId(),
                priceInfo.getDealerId(),
                String.format("%.2f", priceInfo.getNewsPrice() / 10000.0) + "万",
                String.format("%.2f", priceInfo.getMinOriginalPrice() / 10000.0) + "万",
                String.format("%.2f", priceInfo.getMaxOriginalPrice() / 10000.0) + "万",
                priceInfo.getPayTypeSequenceOne(),
                priceInfo.getSeriesName(),
                priceInfo.getSpecName(),
                priceInfo.getDealerSimpleName(),
                priceInfo.getSpecImageUrl(),
                priceInfo.getStarLevel()
        );
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public int getNewsId() {
        return newsId;
    }

    public void setNewsId(int newsId) {
        this.newsId = newsId;
    }

    public int getDealerId() {
        return dealerId;
    }

    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public String getNewsPrice() {
        return newsPrice;
    }

    public void setNewsPrice(String newsPrice) {
        this.newsPrice = newsPrice;
    }

    public String getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(String minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public String getMaxOriginalPrice() {
        return maxOriginalPrice;
    }

    public void setMaxOriginalPrice(String maxOriginalPrice) {
        this.maxOriginalPrice = maxOriginalPrice;
    }

    public int getPayTypeSequenceOne() {
        return payTypeSequenceOne;
    }

    public void setPayTypeSequenceOne(int payTypeSequenceOne) {
        this.payTypeSequenceOne = payTypeSequenceOne;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getDealerSimpleName() {
        return dealerSimpleName;
    }

    public void setDealerSimpleName(String dealerSimpleName) {
        this.dealerSimpleName = dealerSimpleName;
    }

    public String getSpecImageUrl() {
        return specImageUrl;
    }

    public void setSpecImageUrl(String specImageUrl) {
        this.specImageUrl = specImageUrl;
    }

    public int getStarLevel() {
        return starLevel;
    }

    public void setStarLevel(int starLevel) {
        this.starLevel = starLevel;
    }
}
