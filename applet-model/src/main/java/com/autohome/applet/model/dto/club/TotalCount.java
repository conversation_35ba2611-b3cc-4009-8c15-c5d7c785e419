package com.autohome.applet.model.dto.club;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class TotalCount {

    @JsonProperty("bbsId")
    private int bbsId;

    @JsonProperty("topicCount")
    private int topicCount;

    @JsonProperty("bbsName")
    private String bbsName;

    @JsonProperty("bbsimgurl")
    private String bbsImgUrl;

    // 添加getter和setter方法
    public int getBbsId() {
        return bbsId;
    }

    public void setBbsId(int bbsId) {
        this.bbsId = bbsId;
    }

    public int getTopicCount() {
        return topicCount;
    }

    public void setTopicCount(int topicCount) {
        this.topicCount = topicCount;
    }

    public String getBbsName() {
        return bbsName;
    }

    public void setBbsName(String bbsName) {
        this.bbsName = bbsName;
    }

    public String getBbsImgUrl() {
        return bbsImgUrl;
    }

    public void setBbsImgUrl(String bbsImgUrl) {
        this.bbsImgUrl = bbsImgUrl;
    }

}
