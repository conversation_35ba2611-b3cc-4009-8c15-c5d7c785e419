package com.autohome.applet.model.dto.club;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class BBSRec {
    private String bbs;

    private String bbsImage;

    private int recommendType;

    @JsonProperty("bbsname")
    private String bbsName;

    private int bbsId;

    private int totalCount;

    public String getBbs() {
        return bbs;
    }
    public void setBbs(String bbs) {
        this.bbs = bbs;
    }

    public String getBbsImage() {
        return bbsImage;
    }
    public void setBbsImage(String bbsImage) {
        this.bbsImage = bbsImage;
    }

    public int getRecommendType() {
        return recommendType;
    }
    public void setRecommendType(int recommendType) {
        this.recommendType = recommendType;
    }

    public String getBbsName() {
        return bbsName;
    }
    public void setBbsName(String bbsName) {
        this.bbsName = bbsName;
    }

    public int getBbsId() {
        return bbsId;
    }
    public void setBbsId(int bbsId) {
        this.bbsId = bbsId;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
}
