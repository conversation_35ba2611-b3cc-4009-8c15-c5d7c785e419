package com.autohome.applet.model.dto.usercenter;

import com.fasterxml.jackson.annotation.JsonProperty;

public class User {

    private int Code;
    private int ApiResultCode;
    private String Message;
    private int ResultCode;
    private int MemberId;
    @JsonProperty("userId")
    private long UserId;
    @JsonProperty("userState")
    private int UserState;
    @JsonProperty("sex")
    private int Sex;
    private String AddDate;
    private String EMail;
    private int RegisterSiteId;
    private String UserPwd;
    private int UserTypeId;
    private String LastEditDate;
    @JsonProperty("nickName")
    private String NickName;
    @JsonProperty("mobilePhone")
    private String MobilePhone;
    @JsonProperty("mobilePhoneEncrypted")
    private String MobilePhoneEncrypted;
    private int IpLoginErrorCount;
    @JsonProperty("pcpopClub")
    private String PcpopClub;
    @JsonProperty("sessionLogin")
    private String SessionLogin;
    private int UserStatus;
    private String Minpic;
    private int MobileValidRemainCount;
    private int DubiousLevel;
    private String dubiouslogin;
    private String headImage;
    private String ct;

    public String getHeadImage() {
        if (headImage != null && headImage.length() > 0) {
            if (!headImage.contains("http")) {
                if (!headImage.contains("//i2.autoimg")) {
                    headImage = "https://i2.autoimg.cn/userscenter" + headImage;
                }
            }
        } else {
            headImage = "";
        }

        return headImage;
    }


    public String getMobilePhoneEncrypted() {
        return MobilePhoneEncrypted;
    }

    public void setMobilePhoneEncrypted(String mobilePhoneEncrypted) {
        MobilePhoneEncrypted = mobilePhoneEncrypted;
    }

    public int getCode() {
        return Code;
    }

    public void setCode(int code) {
        Code = code;
    }

    public int getApiResultCode() {
        return ApiResultCode;
    }

    public void setApiResultCode(int apiResultCode) {
        ApiResultCode = apiResultCode;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String message) {
        Message = message;
    }

    public int getResultCode() {
        return ResultCode;
    }

    public void setResultCode(int resultCode) {
        ResultCode = resultCode;
    }

    public int getMemberId() {
        return MemberId;
    }

    public void setMemberId(int memberId) {
        MemberId = memberId;
    }

    public long getUserId() {
        return UserId;
    }

    public void setUserId(long userId) {
        UserId = userId;
    }

    public int getUserState() {
        return UserState;
    }

    public void setUserState(int userState) {
        UserState = userState;
    }

    public int getSex() {
        return Sex;
    }

    public void setSex(int sex) {
        Sex = sex;
    }

    public String getAddDate() {
        return AddDate;
    }

    public void setAddDate(String addDate) {
        AddDate = addDate;
    }

    public String getEMail() {
        return EMail;
    }

    public void setEMail(String EMail) {
        this.EMail = EMail;
    }

    public int getRegisterSiteId() {
        return RegisterSiteId;
    }

    public void setRegisterSiteId(int registerSiteId) {
        RegisterSiteId = registerSiteId;
    }

    public String getUserPwd() {
        return UserPwd;
    }

    public void setUserPwd(String userPwd) {
        UserPwd = userPwd;
    }

    public int getUserTypeId() {
        return UserTypeId;
    }

    public void setUserTypeId(int userTypeId) {
        UserTypeId = userTypeId;
    }

    public String getLastEditDate() {
        return LastEditDate;
    }

    public void setLastEditDate(String lastEditDate) {
        LastEditDate = lastEditDate;
    }

    public String getNickName() {
        return NickName;
    }

    public void setNickName(String nickName) {
        NickName = nickName;
    }

    public String getMobilePhone() {
        return MobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        MobilePhone = mobilePhone;
    }

    public int getIpLoginErrorCount() {
        return IpLoginErrorCount;
    }

    public void setIpLoginErrorCount(int ipLoginErrorCount) {
        IpLoginErrorCount = ipLoginErrorCount;
    }

    public String getPcpopClub() {
        return PcpopClub;
    }

    public void setPcpopClub(String pcpopClub) {
        PcpopClub = pcpopClub;
    }

    public String getSessionLogin() {
        return SessionLogin;
    }

    public void setSessionLogin(String sessionLogin) {
        SessionLogin = sessionLogin;
    }

    public int getUserStatus() {
        return UserStatus;
    }

    public void setUserStatus(int userStatus) {
        UserStatus = userStatus;
    }

    public String getMinpic() {
        return Minpic;
    }

    public void setMinpic(String minpic) {
        Minpic = minpic;
    }

    public int getMobileValidRemainCount() {
        return MobileValidRemainCount;
    }

    public void setMobileValidRemainCount(int mobileValidRemainCount) {
        MobileValidRemainCount = mobileValidRemainCount;
    }

    public int getDubiousLevel() {
        return DubiousLevel;
    }

    public void setDubiousLevel(int dubiousLevel) {
        DubiousLevel = dubiousLevel;
    }

    public String getDubiouslogin() {
        return dubiouslogin;
    }

    public void setDubiouslogin(String dubiouslogin) {
        this.dubiouslogin = dubiouslogin;
    }

    public void setHeadImage(String headImage) {
        this.headImage = headImage;
    }

    public String getCt() {
        return ct;
    }

    public void setCt(String ct) {
        this.ct = ct;
    }
}
