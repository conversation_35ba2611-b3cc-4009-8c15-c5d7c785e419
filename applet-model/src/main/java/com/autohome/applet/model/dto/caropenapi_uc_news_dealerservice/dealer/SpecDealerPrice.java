package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by ha<PERSON>hanfeng on 2019/3/15.
 */
public class SpecDealerPrice {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<SpecPriceList> list;

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<SpecPriceList> getList() {
        return list;
    }

    public void setList(List<SpecPriceList> list) {
        this.list = list;
    }

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public static class SpecPriceList {

        @JsonProperty("specId")
        private int specid;
        @JsonProperty("seriesId")
        private int seriesid;
        @JsonProperty("newsPrice")
        private int newsprice;
        @JsonProperty("minOriginalPrice")
        private int minoriginalprice;
        @JsonProperty("maxOriginalPrice")
        private int maxoriginalprice;
        @JsonProperty("cityId")
        private int cityid;
        @JsonProperty("lastMonthDealPrice")
        private int lastMonthDealPrice;

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setNewsprice(int newsprice) {
            this.newsprice = newsprice;
        }

        public int getNewsprice() {
            return newsprice;
        }

        public void setMinoriginalprice(int minoriginalprice) {
            this.minoriginalprice = minoriginalprice;
        }

        public int getMinoriginalprice() {
            return minoriginalprice;
        }

        public void setMaxoriginalprice(int maxoriginalprice) {
            this.maxoriginalprice = maxoriginalprice;
        }

        public int getMaxoriginalprice() {
            return maxoriginalprice;
        }

        public void setCityid(int cityid) {
            this.cityid = cityid;
        }

        public int getCityid() {
            return cityid;
        }

        public int getLastMonthDealPrice() {
            return lastMonthDealPrice;
        }

        public void setLastMonthDealPrice(int lastMonthDealPrice) {
            this.lastMonthDealPrice = lastMonthDealPrice;
        }
    }
}
