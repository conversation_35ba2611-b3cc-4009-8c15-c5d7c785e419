package com.autohome.applet.model.dto.maindata;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class Article {
    private String author_name; // 作者名字
    private int vv;
    private String video_height; // null,
    private int pv; // 210,
    private String author_img; // http://www2.autoimg.cn/newsdfs/g24/M04/C4/24/autohomecar__Chtk3WC4lVeAE1g3AAEgy7kJgsk207.jpg",
    private String video_width; // null,
    private String img_url_16x9; // http://www2.autoimg.cn/newsdfs/g27/M05/52/A8/400x300_0_autohomecar__CjIFVWQcIseAcOv_AAFcjOjpWuM599.jpg",
    private List <CmsPassageList> cms_passage_list; // null,
    private int cms_refine; // 10,
    private String title; // 或上海车展上市 新款捷豹XFL现已到店",
    private String video_vertical_img_url; // null,
//    private String __sort__value__; // [
//            1679594874000,
//            1281424
//            ],
    private String video_app_title; // "",
    private int duration; // 0,
    private String cms_ThirdAppCoverImg; // "",
    private String video_direction; // null,
    private String cms_aFirstCoverImg; // http://www3.autoimg.cn/newsdfs/g27/M01/A5/C3/400x300_0_autohomecar__ChxkmWQcIsqADbfZAAJEHEzQWQU207.jpg",
    private String publish_time; // "2023/03/23 18:07:54",
    private String cms_aFirstAppCoverImg; // "http://www3.autoimg.cn/newsdfs/g27/M01/A5/C3/400x300_0_autohomecar__ChxkmWQcIsqADbfZAAJEHEzQWQU207.jpg",
    private String cms_thirdappcoverimg; // "",
    private String cms_aSecondCoverImg; // "http://www3.autoimg.cn/newsdfs/g27/M02/A5/C4/400x300_0_autohomecar__ChxkmWQcIs2AUqbRAAQeUnmOxfs594.jpg",
    private String video_source; // "",
    private String small_title; // "英伦风依旧 新款捷豹XFL现已到店 有望上海车展上市",
    private String summary; // "&nbsp; &nbsp; [汽车之家&nbsp;资讯]&nbsp;&nbsp;日前，汽车之家从经销商处拍摄到了新款捷豹XFL，新车作为年度改款车型，主要针对配置进行了升级，有望于上海车展正式上市。&nbsp; &nbsp;&nbsp;捷豹XFL在2021年完成中期改款，因此外观一点不过时，此次改款依旧将会提供豪华版和R-Dynamic运动版两种格栅样式，分别为点阵式或蜂窝式风格。&nbsp; &",
    private String cms_firstappcoverimg; // "",
    private String like_count; // null,
    private String cms_aThirdAppCoverImg; // "http://www3.autoimg.cn/newsdfs/g27/M00/52/AA/400x300_0_autohomecar__CjIFVWQcIs6AUqCBAAGOhPBV0_M273.jpg",
    private String cms_secondcoverimg; // "",
    private String main_data_type; // "cms",
    private String cms_thirdcoverimg; // "",
    private long global_id; // 2229299348284379100,
    private String img_url_4x3; // "http://www2.autoimg.cn/newsdfs/g27/M05/52/A8/400x300_0_autohomecar__CjIFVWQcIseAcOv_AAFcjOjpWuM599.jpg",
    private String cms_aSecondAppCoverImg; // "http://www3.autoimg.cn/newsdfs/g27/M02/A5/C4/400x300_0_autohomecar__ChxkmWQcIs2AUqbRAAQeUnmOxfs594.jpg",
    private String cms_aThirdCoverImg; // "http://www3.autoimg.cn/newsdfs/g27/M00/52/AA/400x300_0_autohomecar__CjIFVWQcIs6AUqCBAAGOhPBV0_M273.jpg",
    private String reply_count; // 0,
    private String cms_secondappcoverimg; // "",
    private String is_close_comment; // 0,
    private String biz_update_time; // "2023/03/23 18:07:55",
    private String multi_images; // null,
    private String express_title; // "英伦风依旧 新款捷豹XFL现已到店 有望上海车展上市",
    private String cms_SecondAppCoverImg; // "",
    private String img_url; // "http://www2.autoimg.cn/newsdfs/g27/M05/52/A8/400x300_0_autohomecar__CjIFVWQcIseAcOv_AAFcjOjpWuM599.jpg",
    private String cms_firstcoverimg; // "",
    private String cms_createsource; // 0,
    private String video_vertical_jb_img_url; // null,
    private int biz_id; // 1281424,
    private int author_id; // 241434478,
    private List<Integer> series_ids;

    public static class CmsPassageList{

        private int is_del;
        private int pType;
        private int pOrderNum;
        private String modified_STime;
        private String pImage;
        private int simpleType;
        private String pText;
        private String pHtml;
        private int articleId;
        private int height;
        private int id;
        private String created_STime;
        private String objHref;
        private int width;

        public int getIs_del() {
            return is_del;
        }

        public void setIs_del(int is_del) {
            this.is_del = is_del;
        }

        public int getpType() {
            return pType;
        }

        public void setpType(int pType) {
            this.pType = pType;
        }

        public int getpOrderNum() {
            return pOrderNum;
        }

        public void setpOrderNum(int pOrderNum) {
            this.pOrderNum = pOrderNum;
        }

        public String getModified_STime() {
            return modified_STime;
        }

        public void setModified_STime(String modified_STime) {
            this.modified_STime = modified_STime;
        }

        public String getpImage() {
            return pImage;
        }

        public void setpImage(String pImage) {
            this.pImage = pImage;
        }

        public int getSimpleType() {
            return simpleType;
        }

        public void setSimpleType(int simpleType) {
            this.simpleType = simpleType;
        }

        public String getpText() {
            return pText;
        }

        public void setpText(String pText) {
            this.pText = pText;
        }

        public String getpHtml() {
            return pHtml;
        }

        public void setpHtml(String pHtml) {
            this.pHtml = pHtml;
        }

        public int getArticleId() {
            return articleId;
        }

        public void setArticleId(int articleId) {
            this.articleId = articleId;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCreated_STime() {
            return created_STime;
        }

        public void setCreated_STime(String created_STime) {
            this.created_STime = created_STime;
        }

        public String getObjHref() {
            return objHref;
        }

        public void setObjHref(String objHref) {
            this.objHref = objHref;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }
    }

}
