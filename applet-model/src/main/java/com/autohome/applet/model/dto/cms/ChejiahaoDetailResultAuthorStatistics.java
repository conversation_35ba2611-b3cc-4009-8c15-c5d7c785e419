package com.autohome.applet.model.dto.cms;

public class ChejiahaoDetailResultAuthorStatistics {


    private int articlecount;
    private long articlepv;
    private int articlereplycount;
    private int articleuv;
    private int fanscount;
    private int followcount;
    private int questioncount;
    private long userid;

    public void setArticlecount(int articlecount) {
        this.articlecount = articlecount;
    }

    public int getArticlecount() {
        return articlecount;
    }

    public void setArticlepv(long articlepv) {
        this.articlepv = articlepv;
    }

    public long getArticlepv() {
        return articlepv;
    }

    public void setArticlereplycount(int articlereplycount) {
        this.articlereplycount = articlereplycount;
    }

    public int getArticlereplycount() {
        return articlereplycount;
    }

    public void setArticleuv(int articleuv) {
        this.articleuv = articleuv;
    }

    public int getArticleuv() {
        return articleuv;
    }

    public void setFanscount(int fanscount) {
        this.fanscount = fanscount;
    }

    public int getFanscount() {
        return fanscount;
    }

    public void setFollowcount(int followcount) {
        this.followcount = followcount;
    }

    public int getFollowcount() {
        return followcount;
    }

    public void setQuestioncount(int questioncount) {
        this.questioncount = questioncount;
    }

    public int getQuestioncount() {
        return questioncount;
    }

    public void setUserid(long userid) {
        this.userid = userid;
    }

    public long getUserid() {
        return userid;
    }

}