package com.autohome.applet.model.dto.vrinfodata;

import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Data
public class BrandListNewResult {

    private String firstletter;

    private List<BrandlistNew> branditems;

    public List<BrandlistNew> getBranditems() {
        if (branditems == null || branditems.size() == 0) {
            return new ArrayList<>();
        }
        branditems.sort(new Comparator<BrandlistNew>() {
            @Override
            public int compare(BrandlistNew o1, BrandlistNew o2) {
                try {
                    if (o1.getRank() > o2.getRank()) {
                        return 1;
                    }
                } catch (NumberFormatException e) {
                    return 0;
                }
                return -1;
            }
        });
        return branditems;
    }

    public void setBrandlist(List<BrandlistNew> brandlist) {
        this.branditems = brandlist;
    }
}