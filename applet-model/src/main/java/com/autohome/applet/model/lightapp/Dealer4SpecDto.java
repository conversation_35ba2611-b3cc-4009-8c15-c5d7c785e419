package com.autohome.applet.model.lightapp;



import java.util.List;

public class Dealer4SpecDto {

    // 经销商编号
    private Integer dealerId;

    // 最低促销价
    private Integer minNewsPrice;
    // 厂商指导价
    private Integer minOriginalPrice;
    // 厂商指导价
    private Integer maxOriginalPrice;
    // 1：现金优惠，2：综合优惠
    private Integer promotionType;
    // 优惠额度？？？？？？？？？？？？？？？？？
    private Integer priceOff;

    // 是否虚拟店铺
    private Integer isVirtualShop;

    // 距离
    private Double distance;

    // 0:本地经销商，1:异地经销商
    private Integer isSupplyDealer;

    // 优惠条件model
    private List<promotionConditionDto> promotionConditions;

    private dealerInfoBaseOutDto dealerInfoBaseOut;

    private Dealer4SpecActivity activity;

    public dealerInfoBaseOutDto getDealerInfoBaseOut() {
        return dealerInfoBaseOut;
    }

    public void setDealerInfoBaseOut(dealerInfoBaseOutDto dealerInfoBaseOut) {
        this.dealerInfoBaseOut = dealerInfoBaseOut;
    }

    public Integer getDealerId() {
        return dealerId;
    }

    public void setDealerId(Integer dealerId) {
        this.dealerId = dealerId;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Integer getMinNewsPrice() {
        return minNewsPrice;
    }

    public void setMinNewsPrice(Integer minNewsPrice) {
        this.minNewsPrice = minNewsPrice;
    }

    public Integer getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(Integer minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public Integer getMaxOriginalPrice() {
        return maxOriginalPrice;
    }

    public void setMaxOriginalPrice(Integer maxOriginalPrice) {
        this.maxOriginalPrice = maxOriginalPrice;
    }

    public Integer getPromotionType() {
        return promotionType;
    }

    public void setPromotionType(Integer promotionType) {
        this.promotionType = promotionType;
    }

    public Integer getPriceOff() {
        return priceOff;
    }

    public void setPriceOff(Integer priceOff) {
        this.priceOff = priceOff;
    }

    public List<promotionConditionDto> getPromotionConditions() {
        return promotionConditions;
    }

    public void setPromotionConditions(List<promotionConditionDto> promotionConditions) {
        this.promotionConditions = promotionConditions;
    }
    private String LinkUrl;
    private String landUrl;
    private boolean havePermission;

    public boolean isHavePermission() {
        return havePermission;
    }

    public void setHavePermission(boolean havePermission) {
        this.havePermission = havePermission;
    }

    public String getLandUrl() {
        return landUrl;
    }

    public void setLandUrl(String landUrl) {
        this.landUrl = landUrl;
    }

    public String getLinkUrl() {
        return LinkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        LinkUrl = linkUrl;
    }

//    public String getVrTitle() {
//        return VrTitle;
//    }
//
//    public void setVrTitle(String vrTitle) {
//        VrTitle = vrTitle;
//    }

    public int getVrState() {
        return VrState;
    }

    public void setVrState(int vrState) {
        VrState = vrState;
    }

   // private String VrTitle;
    private int VrState;


    public Integer getIsVirtualShop() {
        return isVirtualShop;
    }

    public void setIsVirtualShop(Integer isVirtualShop) {
        this.isVirtualShop = isVirtualShop;
    }

    public Integer getIsSupplyDealer() {
        return isSupplyDealer;
    }

    public void setIsSupplyDealer(Integer isSupplyDealer) {
        this.isSupplyDealer = isSupplyDealer;
    }

    public Dealer4SpecActivity getActivity() {
        return activity;
    }

    public void setActivity(Dealer4SpecActivity activity) {
        this.activity = activity;
    }
}
