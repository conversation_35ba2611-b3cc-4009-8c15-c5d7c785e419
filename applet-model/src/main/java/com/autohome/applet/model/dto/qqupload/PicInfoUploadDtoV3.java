package com.autohome.applet.model.dto.qqupload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
public class PicInfoUploadDtoV3 {
        // 车系名称
        private String key;
        // Display对象，对应XML文档中的<display>节点
        private Display display;


    @Data
    public static class Display {
        // 图片标题
        private String title;
        // 车系图片落地页
        private String url;
        // 品牌名称
        private String brand;
        // 品牌厂商
        private String subBrand;
        // 品牌车系
        private String carSeries;
        // 简称与别名
//        private String simplename;
        // Pic对象，对应XML文档中的<pic>节点
        private Pic pic;
        // 同类热销车型列表
//        private List<Car> samelist;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Samelist samelist;
    }

    @Data
    public static class Pic {
        // PicList对象列表，对应XML文档中的<piclist>节点列表
        private List<PicList> piclist;
    }


    @Data
    public static class Samelist {
        // PicList对象列表，对应XML文档中的<piclist>节点列表
        private List<Car> car;
    }

    @Data
    public static class PicList {
        // 图片展示的类别名
        private String picname;
        // 该类别下图片的总张数
        private int picnum;
        // 该类别的无线落地页
        private String wapurl;
        // 该类别的PC落地页
        private String pcurl;
        // ValueList对象列表，对应XML文档中的<valuelist>节点列表
        private List<ValueList> valuelist;
    }

    @Data
    public static class ValueList {
        // 该类别下的一张图片地址
        private String value;
        // 单张图片的跳转链接
//        private String valuelink;
    }

    @Data
    public static class Car {
        // 车型名
        private String carname;
        // 车型图片
        private String carpic;
        // 车型售价
        private String carprice;
        // 无线落地页
        private String carurl;
        // PC落地页
        private String carpcurl;
    }
}
