package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

public class Seriesitems {

    private int id;

    private String name;

    private String logo;

    //@JSONField(serialize = false)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    //@JsonIgnore
    private String pnglogo;

    private String brandname;

    private String fctname;

    private String levelname;

    private int minprice;

    private int maxprice;

    private String seriesprice;

    private boolean haveVr;

    ////0-未售  10-待售  20-在产在售  30-停产在售  40-停售
    private int seriesstate;

    //@JSONField(serialize = false)
    @JsonIgnore
    private String seriesStateGroup;

    //@JSONField(serialize = false)
    @JsonIgnore
    private String seriesStateGroupForFct;

    //@JSONField(serialize = false)
    @JsonIgnore
    private long seriesInsightCount;

    @JsonIgnore
    private String vrShowUrl;

    //@JSONField(serialize = false)
    @JsonIgnore
    private int recommendStatus;

    //@JSONField(serialize = false)
    @JsonIgnore
    private int topStatus;

    //@JSONField(serialize = false)
    @JsonIgnore
    private int status;

    //@JSONField(serialize = false)
    @JsonIgnore
    private int sortNum;

    //@JSONField(serialize = false)
    @JsonIgnore
    private int haveStore;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String recommendTime;

    public int getHaveStore() {
        return haveStore;
    }

    public void setHaveStore(int haveStore) {
        this.haveStore = haveStore;
    }

    public int getStatus() {

        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getTopStatus() {
        return topStatus;
    }

    public void setTopStatus(int topStatus) {
        this.topStatus = topStatus;
    }

    public String getRecommendTime() {
        return recommendTime;
    }

    public void setRecommendTime(String recommendTime) {
        this.recommendTime = recommendTime;
    }

    public void setSeriesStateGroupForFct(String seriesStateGroupForFct) {
        this.seriesStateGroupForFct = seriesStateGroupForFct;
    }

    public int getRecommendStatus() {
        return recommendStatus;
    }

    public void setRecommendStatus(int recommendStatus) {
        this.recommendStatus = recommendStatus;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getSeriesStateGroupForFct() {
        if (seriesstate == 20 || seriesstate == 10 || seriesstate == 30)
            seriesStateGroupForFct = "在售";
        else {
            seriesStateGroupForFct = "停售";
        }
        return seriesStateGroupForFct;
    }

    public String getPnglogo() {
        return pnglogo;
    }

    public void setPnglogo(String pnglogo) {
        this.pnglogo = pnglogo;
    }

    public String getVrShowUrl() {
        return vrShowUrl;
    }

    public void setVrShowUrl(String vrShowUrl) {
        this.vrShowUrl = vrShowUrl;
    }

    public long getSeriesInsightCount() {
        return seriesInsightCount;
    }

    public void setSeriesInsightCount(long seriesInsightCount) {
        this.seriesInsightCount = seriesInsightCount;
    }

    public String getSeriesStateGroup() {
        if (seriesstate == 20 || seriesstate == 30)
            seriesStateGroup = "在售";
        else if (seriesstate == 10)
            seriesStateGroup = "预售";
        else {
            seriesStateGroup = "停售";
        }
        return seriesStateGroup;
    }

    public void setSeriesStateGroup(String seriesStateGroup) {
        this.seriesStateGroup = seriesStateGroup;
    }

    public int getSeriesstate() {
        return seriesstate;
    }

    public void setSeriesstate(int seriesstate) {
        this.seriesstate = seriesstate;
    }

    public boolean isHaveVr() {
        return haveVr;
    }

    public void setHaveVr(boolean haveVr) {
        this.haveVr = haveVr;
    }
    //        private List<String> gearbox;
//        private List<String> displacement;
//        private List<String> structure;

    public String getSeriesprice() {
        return CarPriceUtils.GetSpecPrice(minprice, maxprice);
    }

    public void setSeriesprice(String seriesprice) {
        this.seriesprice = seriesprice;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        if (StringUtil.isNotNullAndWhiteSpace(this.pnglogo)) {
            return this.pnglogo;
        }
        return logo;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public String getFctname() {
        return fctname;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setMaxprice(int maxprice) {
        this.maxprice = maxprice;
    }

    public int getMaxprice() {
        return maxprice;
    }

//        public void setGearbox(List<String> gearbox) {
//            this.gearbox = gearbox;
//        }
//        public List<String> getGearbox() {
//            return gearbox;
//        }
//
//        public void setDisplacement(List<String> displacement) {
//            this.displacement = displacement;
//        }
//        public List<String> getDisplacement() {
//            return displacement;
//        }
//
//        public void setStructure(List<String> structure) {
//            this.structure = structure;
//        }
//        public List<String> getStructure() {
//            return structure;
//        }

}