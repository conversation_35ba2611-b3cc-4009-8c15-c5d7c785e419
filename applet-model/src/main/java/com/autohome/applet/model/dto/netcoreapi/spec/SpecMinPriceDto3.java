package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 二手车均价
 * <AUTHOR>
 */
public class SpecMinPriceDto3 {
    @JsonProperty("seriesId")
    private int seriesId;

    @JsonProperty("specId")
    private int specId;

    @JsonProperty("seriesName")
    private String seriesName;

    @JsonProperty("specName")
    private String specName;

    @JsonProperty("title")
    private String title;

    @JsonProperty("subTitle")
    private String subTitle;

    @JsonIgnore
    @JsonProperty("addr")
    private String addr;

    @JsonProperty("minPrice")
    private float minPrice;



    public SpecMinPriceDto3() {
    }

    public SpecMinPriceDto3(int seriesId, int specId, String seriesName, String specName, String title, String subTitle, String addr, float minPrice) {
        this.seriesId = seriesId;
        this.specId = specId;
        this.seriesName = seriesName;
        this.specName = specName;
        this.title = title;
        this.subTitle = subTitle;
        this.addr = addr;
        this.minPrice = minPrice;
    }

    public static SpecMinPriceDto3 by(SpecBaseInfo specInfo, SpecUsedPrice usedPrice) {
        return new SpecMinPriceDto3(
                specInfo.getSeriesId(),
                specInfo.getSpecId(),
                specInfo.getSeriesName(),
                specInfo.getSpecName(),
                usedPrice.getTitle(),
                usedPrice.getSubtitle(),
                usedPrice.getAddr(),
                usedPrice.getMinPrice()
        );
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public float getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(float minPrice) {
        this.minPrice = minPrice;
    }
}
