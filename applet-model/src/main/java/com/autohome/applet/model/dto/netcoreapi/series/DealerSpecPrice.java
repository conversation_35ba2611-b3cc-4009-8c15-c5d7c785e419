package com.autohome.applet.model.dto.netcoreapi.series;

public class DealerSpecPrice {
    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public long getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(long minPrice) {
        this.minPrice = minPrice;
    }

    public long getDealPriceQ() {
        return dealPriceQ;
    }

    public void setDealPriceQ(long dealPriceQ) {
        this.dealPriceQ = dealPriceQ;
    }

    public String getDealPriceTest() {
        return dealPriceTest;
    }

    public void setDealPriceTest(String dealPriceTest) {
        this.dealPriceTest = dealPriceTest;
    }

    public long getPrice() {
        return price;
    }

    public void setPrice(long price) {
        this.price = price;
    }

    public long getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(long originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(int minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public int specId;

    public long minPrice;

    public long dealPriceQ;

    public String dealPriceTest;

    public long price;

    public long originalPrice;

    public String url;

    public int minOriginalPrice;
}
