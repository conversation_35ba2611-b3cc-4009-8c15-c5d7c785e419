package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarSettings;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/23.
 */
public class DealerImages {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<ImageList> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<ImageList> getList() {
        return list;
    }

    public void setList(List<ImageList> list) {
        this.list = list;
    }

    public static class ImageList {

        @JsonProperty("ClassId")
        private int classid;
        @JsonProperty("ClassName")
        private String classname;
        @JsonProperty("DealerImageInfoList")
        private List<Dealerimageinfolist> dealerimageinfolist;
        private String shareimg;

        public String getShareimg() {
            if (dealerimageinfolist != null && dealerimageinfolist.size() > 0) {
                String img = dealerimageinfolist.get(0).getImageurl();
                if (StringUtil.isNotNullAndWhiteSpace(img)) {
                    try {
                        if (img.indexOf("autohomecar") >= 0)
                            return CarSettings.ConvertImgNew(img, "400x320_").replace("http://", "https://");
                        else {
                            img = img.substring(0, img.lastIndexOf("/")) + "/640x480_0_q87_c42_" + img.substring(img.lastIndexOf("/") + 1, img.length());
                            return img.replace("http://", "https://");
                        }
                    } catch (Exception e) {
                        return img;
                    }

                }
            }
            return shareimg;
        }

        public void setShareimg(String shareimg) {
            this.shareimg = shareimg;
        }

        public void setClassid(int classid) {
            this.classid = classid;
        }

        public int getClassid() {
            return classid;
        }

        public void setClassname(String classname) {
            this.classname = classname;
        }

        public String getClassname() {
            return classname;
        }

        public void setDealerimageinfolist(List<Dealerimageinfolist> dealerimageinfolist) {
            this.dealerimageinfolist = dealerimageinfolist;
        }

        public List<Dealerimageinfolist> getDealerimageinfolist() {
            if (dealerimageinfolist == null) {
                List<Dealerimageinfolist> nullDealerImage = new ArrayList<>();
                Dealerimageinfolist dealerimageinfo = new Dealerimageinfolist();
                nullDealerImage.add(dealerimageinfo);
                return nullDealerImage;
            }
            return dealerimageinfolist;
        }

    }

}
