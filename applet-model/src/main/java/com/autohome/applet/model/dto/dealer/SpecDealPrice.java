package com.autohome.applet.model.dto.dealer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecDealPrice {
    private int specId;
    private String mainTitle;
    private String subTitle;
    private String buttonTitle;
    private int lastMonthDealPrice;
    private String lastMonthDealPriceFormat;
    private int bargainUV;
    private int cityId;
    private int seriesId;
    private String isLuoDiPrice;

    /**
     * 需要调用另一个接口补齐车型名称
     * */
    private String specName;
    /**
     * 需要调用另一个接口补齐车系名称
     * */
    private String seriesName;

    public static class SpecDealPriceList {
        private int rowCount;
        private int pageCount;
        private int pageIndex;
        private List<SpecDealPrice> list;

        public int getRowCount() {
            return rowCount;
        }

        public void setRowCount(int rowCount) {
            this.rowCount = rowCount;
        }

        public int getPageCount() {
            return pageCount;
        }

        public void setPageCount(int pageCount) {
            this.pageCount = pageCount;
        }

        public int getPageIndex() {
            return pageIndex;
        }

        public void setPageIndex(int pageIndex) {
            this.pageIndex = pageIndex;
        }

        public List<SpecDealPrice> getList() {
            return list;
        }

        public void setList(List<SpecDealPrice> list) {
            this.list = list;
        }
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getMainTitle() {
        return mainTitle;
    }

    public void setMainTitle(String mainTitle) {
        this.mainTitle = mainTitle;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getButtonTitle() {
        return buttonTitle;
    }

    public void setButtonTitle(String buttonTitle) {
        this.buttonTitle = buttonTitle;
    }

    public int getLastMonthDealPrice() {
        return lastMonthDealPrice;
    }

    public void setLastMonthDealPrice(int lastMonthDealPrice) {
        this.lastMonthDealPrice = lastMonthDealPrice;
    }

    public String getLastMonthDealPriceFormat() {
        return lastMonthDealPriceFormat;
    }

    public void setLastMonthDealPriceFormat(String lastMonthDealPriceFormat) {
        this.lastMonthDealPriceFormat = lastMonthDealPriceFormat;
    }

    public int getBargainUV() {
        return bargainUV;
    }

    public void setBargainUV(int bargainUV) {
        this.bargainUV = bargainUV;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public String getIsLuoDiPrice() {
        return isLuoDiPrice;
    }

    public void setIsLuoDiPrice(String isLuoDiPrice) {
        this.isLuoDiPrice = isLuoDiPrice;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }
}
