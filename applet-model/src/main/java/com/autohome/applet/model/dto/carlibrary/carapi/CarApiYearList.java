package com.autohome.applet.model.dto.carlibrary.carapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CarApiYearList {

    @JsonProperty("seriesid")
    private int seriesId;
    @JsonProperty("fromcache")
    private int fromCache;
    @JsonProperty("minprice")
    private int minPrice;
    @JsonProperty("maxprice")
    private int maxPrice;
    @JsonProperty("yearlist")
    private List<CarApiYearBean> yearList;
}
