package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PromotionSeriesInfo implements KoubeiScoreListable, SeriesInfoSetable{
    // 城市编号
    private Integer cityId;
    // 经销商编号
    private Integer dealerId;
    // 厂商编号
    private Integer factoryId;
    // 燃油方式
    private Integer fuelTypeDetailId;
    // 降价
    private Integer maxPriceOff;
    // 降价百分比
    private Integer maxPriceOffPercent;
    // 原件
    private Integer minOriginalPrice;
    // 
    private Integer newsId;
    // 售价
    private Integer price;
    // 车系编号
    private Integer seriesId;
    // 车系级别
    private Integer seriesLevel;
    // 车系名称
    private String seriesName;
    // 车型编号
    private Integer specId;
    // 车系白底图
    private String seriespnglogo;
    // 厂商指导价
    private Integer minPrice;
    // 厂商指导价
    private Integer maxPrice;
    // 排名
    private Integer orderNum;
    // 口碑评分
    private List<SeriesScoreList.Score> scoreList;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getDealerId() {
        return dealerId;
    }

    public void setDealerId(Integer dealerId) {
        this.dealerId = dealerId;
    }

    public Integer getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Integer factoryId) {
        this.factoryId = factoryId;
    }

    public Integer getFuelTypeDetailId() {
        return fuelTypeDetailId;
    }

    public void setFuelTypeDetailId(Integer fuelTypeDetailId) {
        this.fuelTypeDetailId = fuelTypeDetailId;
    }

    public Integer getMaxPriceOff() {
        return maxPriceOff;
    }

    public void setMaxPriceOff(Integer maxPriceOff) {
        this.maxPriceOff = maxPriceOff;
    }

    public Integer getMaxPriceOffPercent() {
        return maxPriceOffPercent;
    }

    public void setMaxPriceOffPercent(Integer maxPriceOffPercent) {
        this.maxPriceOffPercent = maxPriceOffPercent;
    }

    public Integer getMinOriginalPrice() {
        return minOriginalPrice;
    }

    public void setMinOriginalPrice(Integer minOriginalPrice) {
        this.minOriginalPrice = minOriginalPrice;
    }

    public Integer getNewsId() {
        return newsId;
    }

    public void setNewsId(Integer newsId) {
        this.newsId = newsId;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    @Override
    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(Integer seriesId) {
        this.seriesId = seriesId;
    }

    public Integer getSeriesLevel() {
        return seriesLevel;
    }

    public void setSeriesLevel(Integer seriesLevel) {
        this.seriesLevel = seriesLevel;
    }

    public String getSeriesName() {
        return seriesName;
    }

    @Override
    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSeriespnglogo() {
        return seriespnglogo;
    }

    @Override
    public void setSeriespnglogo(String seriespnglogo) {
        this.seriespnglogo = seriespnglogo;
    }


    public Integer getMinPrice() {
        return minPrice;
    }

    @Override
    public void setMinPrice(int minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getMaxPrice() {
        return maxPrice;
    }

    @Override
    public void setMaxPrice(int maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public List<SeriesScoreList.Score> getScoreList() {
        return scoreList;
    }

    @Override
    public void setScoreList(List<SeriesScoreList.Score> scoreList) {
        this.scoreList = scoreList;
    }

    @Override
    public void setBrandId(int brandId) {

    }
}
