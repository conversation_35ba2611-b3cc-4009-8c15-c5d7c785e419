package com.autohome.applet.model.dto.netcoreapi.user;

/**
 * <AUTHOR>
 */
public class WeixinPostObj {

    private String _logincode;
    private String _userPwd;
    private String mobile;
    private String _sessionKey;
    private String validCode;
    private String _iv;
    private String _rawData;
    private String _signature;
    private String _encryptedData;
    private String _openid;
    private String _deviceId;
    private String clienttype;

    public String get_logincode() {
        return _logincode;
    }

    public void set_logincode(String _logincode) {
        this._logincode = _logincode;
    }

    public String get_userPwd() {
        return _userPwd;
    }

    public void set_userPwd(String _userPwd) {
        this._userPwd = _userPwd;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String get_sessionKey() {
        return _sessionKey;
    }

    public void set_sessionKey(String _sessionKey) {
        this._sessionKey = _sessionKey;
    }

    public String getValidCode() {
        return validCode;
    }

    public void setValidCode(String validCode) {
        this.validCode = validCode;
    }

    public String get_iv() {
        return _iv;
    }

    public void set_iv(String _iv) {
        this._iv = _iv;
    }

    public String get_rawData() {
        return _rawData;
    }

    public void set_rawData(String _rawData) {
        this._rawData = _rawData;
    }

    public String get_signature() {
        return _signature;
    }

    public void set_signature(String _signature) {
        this._signature = _signature;
    }

    public String get_encryptedData() {
        return _encryptedData;
    }

    public void set_encryptedData(String _encryptedData) {
        this._encryptedData = _encryptedData;
    }

    public String get_openid() {
        return _openid;
    }

    public void set_openid(String _openid) {
        this._openid = _openid;
    }

    public String get_deviceId() {
        return _deviceId;
    }

    public void set_deviceId(String _deviceId) {
        this._deviceId = _deviceId;
    }

    public String getClienttype() {
        return clienttype;
    }

    public void setClienttype(String clienttype) {
        this.clienttype = clienttype;
    }
}
