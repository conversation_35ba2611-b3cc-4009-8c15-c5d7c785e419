package com.autohome.applet.model.dto.dealer;

import lombok.Data;

import java.util.List;

@Data
public class SeriesMinPriceExtendsDto {

    private Long cityId;
    private Long dealerId;
    private Long endTime;
    private Long isLowCity;
    private String jsExtend;
    private String key;
    private List<String> mallEntrance;
    private Long minOriginalPrice;
    private Long newsId;
    private Long newsPrice;
    private Long openType;
    private Long priceOffRatio;
    private Long saleType;
    private Long seriesId;
    private SeriesMainButton seriesMainButton;
    private List seriesMainButtonList;
    private Long specId;


    @Data
    public class SeriesMainButton {
        private int abTag;
        private String amount;
        private int biztype;
        private String cornerText;
        private String mainText;
        private String rightsType;
        private String rightsUrl;
        private String subText;
        private int type;
        private String url;
    }
}
