package com.autohome.applet.model.dto.alipay;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;

@Data
public class OrderUnFreezeRequest {

    @NotBlank(message = "请输入auth_no")
    private String authNo;
    @NotBlank(message = "请输入out_request_no")
    private String outRequestNo;
    // 需要冻结的金额，单位为：元（人民币），精确到小数点后两位
    @Digits(integer = 7, fraction = 2, message = "请输入正确金额")
    @DecimalMin(value = "0.01",message = "金额不得小于0元")
    private Double amount;

}