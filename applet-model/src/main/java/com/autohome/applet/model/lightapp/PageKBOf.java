package com.autohome.applet.model.lightapp;

import java.util.List;

public class PageKBOf<T,K> {

    public PageKBOf(Long totalCount, List<T> items , List<K> dimSeriesPRCTypes) {
        setTotalCount(totalCount);
        setItems(items);
        setDimSeriesPRCTypes(dimSeriesPRCTypes);
    }

    public PageKBOf(){}

    /**
     * 总数
     */
    private Long totalCount;

    /**
     * 列表
     */
    private List<T> items;
    private List<K> dimSeriesPRCTypes;


    /**
     * 获取 总数
     *
     * @return totalCount 总数
     */
    public Long getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 总数
     *
     * @param totalCount 总数
     */
    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 列表
     *
     * @return items 列表
     */
    public List<T> getItems() {
        return this.items;
    }

    /**
     * 设置 列表
     *
     * @param items 列表
     */
    public void setItems(List<T> items) {
        this.items = items;
    }

    public List<K> getDimSeriesPRCTypes() {
        return dimSeriesPRCTypes;
    }

    public void setDimSeriesPRCTypes(List<K> dimSeriesPRCTypes) {
        this.dimSeriesPRCTypes = dimSeriesPRCTypes;
    }
}
