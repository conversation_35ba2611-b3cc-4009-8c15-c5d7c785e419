package com.autohome.applet.model.dto.netcoreapi.jiage;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

public class CarPriceInfo {

    private long carpriceid;
    private long cityId;
    private int clientType;

    @JsonProperty("CountyId")
    private int countyId;

    @JsonProperty("CreateTime")
    private Date createTime;
    private long fctprice;
    private double fullPrice;
    private String invoiceUrl;
    private int iscarowner;
    private int ishaveinvoice;
    private long memberId;
    private String memberName;
    private long nakedPrice;

    @JsonProperty("Name")
    private String name;
    private long provinceId;

    @JsonProperty("ShoppingTime")
    private String shoppingTime;
    private int specId;

    public void setCarpriceid(long carpriceid) {
        this.carpriceid = carpriceid;
    }

    public long getCarpriceid() {
        return carpriceid;
    }

    public void setCityId(long cityId) {
        this.cityId = cityId;
    }

    public long getCityId() {
        return cityId;
    }

    public void setClientType(int clientType) {
        this.clientType = clientType;
    }

    public int getClientType() {
        return clientType;
    }

    public void setCountyId(int countyId) {
        this.countyId = countyId;
    }

    public int getCountyId() {
        return countyId;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setFctprice(long fctprice) {
        this.fctprice = fctprice;
    }

    public long getFctprice() {
        return fctprice;
    }

    public void setFullPrice(double fullPrice) {
        this.fullPrice = fullPrice;
    }

    public double getFullPrice() {
        return fullPrice;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public String getInvoiceUrl() {
        return invoiceUrl;
    }

    public void setIscarowner(int iscarowner) {
        this.iscarowner = iscarowner;
    }

    public int getIscarowner() {
        return iscarowner;
    }

    public void setIshaveinvoice(int ishaveinvoice) {
        this.ishaveinvoice = ishaveinvoice;
    }

    public int getIshaveinvoice() {
        return ishaveinvoice;
    }

    public void setMemberId(long memberId) {
        this.memberId = memberId;
    }

    public long getMemberId() {
        return memberId;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setNakedPrice(long nakedPrice) {
        this.nakedPrice = nakedPrice;
    }

    public long getNakedPrice() {
        return nakedPrice;
    }

    public void setName(String Name) {
        this.name = Name;
    }

    public String getName() {
        return name;
    }

    public void setProvinceId(long provinceId) {
        this.provinceId = provinceId;
    }

    public long getProvinceId() {
        return provinceId;
    }

    public void setShoppingTime(String shoppingTime) {
        this.shoppingTime = shoppingTime;
    }

    public String getShoppingTime() {
        return shoppingTime;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public int getSpecId() {
        return specId;
    }

}