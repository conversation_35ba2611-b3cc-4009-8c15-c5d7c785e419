package com.autohome.applet.model.dto.carlibrary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeriesDetailDto {

        private int id;
        private int seriesRelationSeriesId;
        private String name;
        private int maxprice;
        private int minprice;
        private int state;
        private String seriesOfficialUrl;
        private int fctid;
        private String fctname;
        private int brandid;
        private String brandname;
        private List<String> structitems;
        private List<String> transmissionitems;
        private List<String> displacementitems;
        private int levelid;
        private String levelname;
        private List<String> picitems;
        private List<Picinfoitems> picinfoitems;
        private int specnum;
        private int sellspecnum;
        private int stopspecnum;
        private int waitspecnum;
        private int picnum;
        private int minfuelconsumption;
        private int maxfuelconsumption;
        private int isshow;
        private int paramisshow;
        private int existmaintain;
        private int showelectricparam;
        private List<String> electricmotormileage;
        private List<String> electricmotorkw;
        private String electricchargetime;
        private List<String> electricrongliang;
//        private Date createtime;
        private int containbookedspec;
        private float minoilwear;
        private float maxoilwear;
        private int containstopspec;
        private int newenergy;
        private String pnglogo;
        private String pricedescription;
        private String seriesplace;

    @Data
    public static class Picinfoitems {
        private long picid;
        private int specid;
        private int specstate;
        private String picpath;

    }

}
