package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecParams {

    @JsonProperty("paramtypeitems")
    private List<ParamTypeItem> paramTypeItems;

    @JsonProperty("configtypeitems")
    private List<ConfigTypeItem> configTypeItems;

    public void setParamTypeItems(List<ParamTypeItem> paramTypeItems) {
        this.paramTypeItems = paramTypeItems;
    }

    public List<ParamTypeItem> getParamTypeItems() {
        return paramTypeItems;
    }

    public List<ConfigTypeItem> getConfigTypeItems() {
        return configTypeItems;
    }

    public void setConfigTypeItems(List<ConfigTypeItem> configTypeItems) {
        this.configTypeItems = configTypeItems;
    }

    public static class ParamTypeItem {

        @JsonProperty("name")
        private String name;

        @JsonProperty("paramitems")
        private List<ParamItem> paramItems;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<ParamItem> getParamItems() {
            return paramItems;
        }

        public void setParamItems(List<ParamItem> paramItems) {
            this.paramItems = paramItems;
        }
    }

    public static class ConfigTypeItem {

        @JsonProperty("name")
        private String name;
        @JsonProperty("configitems")
        private List<ParamItem> configItems;

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public List<ParamItem> getConfigItems() { return configItems; }
        public void setConfigItems(List<ParamItem> configItems) { this.configItems = configItems; }
    }

    public static class ParamItem {

        @JsonProperty("id")
        private int id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("valueitems")
        private List<ValueItem> valueItems;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<ValueItem> getValueItems() {
            return valueItems;
        }

        public void setValueItems(List<ValueItem> valueItems) {
            this.valueItems = valueItems;
        }
    }

    public static class ValueItem {

        @JsonProperty("specid")
        private int specId;

        @JsonProperty("value")
        private String value;

        @JsonProperty("url")
        private String url;

        public int getSpecId() {
            return specId;
        }

        public void setSpecId(int specId) {
            this.specId = specId;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
