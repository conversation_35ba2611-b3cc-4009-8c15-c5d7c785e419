package com.autohome.applet.model.dto.newenergy;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: WangBoW<PERSON>
 * @date: 2024-01-16
 **/
@Data
public class SeriesBaseInfoV2 {

    @Data
    public static class Energyconfigbeans{

        private String title;

        private String subtitle;

        private List<String> imglist;

        private List<Configbean> list;

        private PvItem pvitem;

        private String linkurl;

    }

    @Data
    public static class Configbean {

        private String title;

        private String subtitle;

        private String pointcolor;
    }

    @Data
    public static class PvItem{

        private Map<String,String> argvs;

        private Click click;

        private Show show;

        @Data
        public static class Click{
            private String eventid;
        }

        @Data
        public static class Show{
            private String eventid;
        }
    }
}
