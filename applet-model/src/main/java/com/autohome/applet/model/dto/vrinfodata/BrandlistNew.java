package com.autohome.applet.model.dto.vrinfodata;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @description:
 * @author: <PERSON>Bo<PERSON><PERSON>
 * @date: 2024-03-15
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BrandlistNew {

    private int id;

    private String name;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String firstletter;

    private String logo;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private int rank;

    private int state;


}
