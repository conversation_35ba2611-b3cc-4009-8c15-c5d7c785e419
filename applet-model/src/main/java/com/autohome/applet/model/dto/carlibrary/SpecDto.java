package com.autohome.applet.model.dto.carlibrary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpecDto {

    private int seriesid;
    private int total;
    private List<Specitems> specitems;

    @Data
    public static class Specitems {
        private int id;
        private String name;
        private String logo;
        private int syearid;
        private int year;
        private int minprice;
        private int maxprice;
        private String transmission;
        private String gearbox;
        private int state;
        private String drivingmodename;
        private int flowmodeid;
        private String flowmodename;
        private BigDecimal displacement;
        private int enginepower;
        private int ispreferential;
        private int istaxrelief;
        private int istaxexemption;
        private int order;
        private int specisimage;
        private int paramisshow;
        private int isclassic;
        private String structtype;
        private String fueltype;
        private int fueltypeid;
        private int electrictype;
        private int electrickw;
        private int booked;
        private String dynamicprice;
        private int isnewcar;
        private int endurancemileage;
        private String emissionstandards;
        private String seat;
    }
}
