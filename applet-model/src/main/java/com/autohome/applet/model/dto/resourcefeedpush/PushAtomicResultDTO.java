package com.autohome.applet.model.dto.resourcefeedpush;

import lombok.Data;

import java.util.concurrent.atomic.AtomicInteger;

@Data
public class PushAtomicResultDTO {
    AtomicInteger success = new AtomicInteger();
    AtomicInteger error = new AtomicInteger();
    AtomicInteger pushError = new AtomicInteger();
    AtomicInteger ignore = new AtomicInteger();
    AtomicInteger tagIgnore = new AtomicInteger();
    AtomicInteger pushDone = new AtomicInteger();
    AtomicInteger delPushDone = new AtomicInteger();
    AtomicInteger delSuccess = new AtomicInteger();

    private int total;

    public PushAtomicResultDTO(int total) {
        this.total = total;
        this.success = new AtomicInteger(0);
        this.error = new AtomicInteger(0);
        this.pushError = new AtomicInteger(0);
        this.ignore = new AtomicInteger(0);
        this.tagIgnore = new AtomicInteger(0);
        this.pushDone = new AtomicInteger(0);
        this.delPushDone = new AtomicInteger(0);
        this.delSuccess = new AtomicInteger(0);
    }
}
