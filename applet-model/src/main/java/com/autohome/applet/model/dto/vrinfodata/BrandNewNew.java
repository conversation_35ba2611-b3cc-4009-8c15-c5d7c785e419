package com.autohome.applet.model.dto.vrinfodata;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class BrandNewNew {


    private  int total;

    private List<Fctlist> fctlist;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<Fctlist> getFctlist() {
        return fctlist;
    }

    public void setFctlist(List<Fctlist> fctlist) {
        this.fctlist = fctlist;
    }

    public static class Serieslist {

        private  String seriesplace;
        private  int rank;
        @JsonProperty("seriesid")
        private int id;
        @JsonProperty("seriesname")
        private String name;
        @JsonProperty("seriesimg")
        private String logo;
        @JsonProperty("seriesPriceMin")
        private int minprice;
        @JsonProperty("seriesPriceMax")
        private int maxprice;
        private int seriesState;
        private String levelName;
        private Integer levelId;
        private Integer newenergy;

        public Integer getLevelId() {
            return levelId;
        }

        public void setLevelId(Integer levelId) {
            this.levelId = levelId;
        }

        public String getLevelName() {
            return levelName;
        }

        public void setLevelName(String levelName) {
            this.levelName = levelName;
        }

        public Integer getNewenergy() {
            return newenergy;
        }

        public void setNewenergy(Integer newenergy) {
            this.newenergy = newenergy;
        }

        private String seriesprice;

        private String pnglogo;
        private String pngurl;

        public void setPngurl(String pngurl) {
            this.pngurl = pngurl;
        }

        public String getPngurl() {
            return pnglogo == null ? "" : pnglogo.replace("/autohomecar", "/192x144_0_autohomecar")+".png";
        }
        public String getSeriesprice() {
            return CarPriceUtils.getStrPrice(Long.toString(minprice), Long.toString(maxprice));
        }

        public void setSeriesprice(String seriesprice) {
            this.seriesprice = seriesprice;
        }


        public String getSeriesplace() {
            return seriesplace;
        }

        public void setSeriesplace(String seriesplace) {
            this.seriesplace = seriesplace;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public int getMinprice() {
            return minprice;
        }

        public void setMinprice(int minprice) {
            this.minprice = minprice;
        }

        public int getMaxprice() {
            return maxprice;
        }

        public void setMaxprice(int maxprice) {
            this.maxprice = maxprice;
        }

        public int getSeriesState() {
            return seriesState;
        }

        public void setSeriesState(int seriesState) {
            this.seriesState = seriesState;
        }

        public void setPnglogo(String pnglogo) {
            this.pnglogo = pnglogo;
        }
        public String getPnglogo() {
             return pnglogo == null ? "" : pnglogo.replace("/autohomecar", "/192x144_0_f40_autohomecar");
        }

    }
    public static class Fctlist {

        private int fctid;
        private String fctname;

        @JsonIgnore
        private String seriesplace;
        private  int seriesplacenum;

        private List<Serieslist> serieslist;

        public int getFctid() {
            return fctid;
        }

        public void setFctid(int fctid) {
            this.fctid = fctid;
        }

        public String getFctname() {
            return fctname;
        }

        public void setFctname(String fctname) {
            this.fctname = fctname;
        }

        public String getSeriesplace() {
            return seriesplace;
        }

        public void setSeriesplace(String seriesplace) {
            this.seriesplace = seriesplace;
        }

        public int getSeriesplacenum() {
            return seriesplacenum;
        }

        public void setSeriesplacenum(int seriesplacenum) {
            this.seriesplacenum = seriesplacenum;
        }

        public List<Serieslist> getSerieslist() {
            return serieslist;
        }

        public void setSerieslist(List<Serieslist> serieslist) {
            this.serieslist = serieslist;
        }
    }
}