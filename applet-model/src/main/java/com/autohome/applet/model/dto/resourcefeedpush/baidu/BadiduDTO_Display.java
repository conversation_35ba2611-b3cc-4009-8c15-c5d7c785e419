package com.autohome.applet.model.dto.resourcefeedpush.baidu;

public class BadiduDTO_Display {

    private String url;
    private String title;
    private String content;
    private String thumbnail_img_list;
    private String video_url;
    private String video_thumbnail_url;
    private String author_name;
    private String author_url;
    private String author_tag;
    private String author_img;
    private String author_level;
    private int author_fans;
    private String author_fields;
    private int author_docs;
    private String date_published;
    private double quality_score;
    private String content_quality_label;
    private String content_tag;
    private int like_count;
    private int unlike_count;
    private int comment_count;
    private String comment_content;
    private int collect_count;
    private int repost_count;
    private int image_count;
    private String video_list;
    private int is_video;
    private int status;
    private String src_type;
    private String sub_src;
    private String third_id;
    private int is_abroad;
    private String country;
    private int poi_type;
    private BadiduDTO_Poi_list poi_list;

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setThumbnail_img_list(String thumbnail_img_list) {
        this.thumbnail_img_list = thumbnail_img_list;
    }

    public String getThumbnail_img_list() {
        return thumbnail_img_list;
    }

    public void setVideo_url(String video_url) {
        this.video_url = video_url;
    }

    public String getVideo_url() {
        return video_url;
    }

    public void setVideo_thumbnail_url(String video_thumbnail_url) {
        this.video_thumbnail_url = video_thumbnail_url;
    }

    public String getVideo_thumbnail_url() {
        return video_thumbnail_url;
    }

    public void setAuthor_name(String author_name) {
        this.author_name = author_name;
    }

    public String getAuthor_name() {
        return author_name;
    }

    public void setAuthor_url(String author_url) {
        this.author_url = author_url;
    }

    public String getAuthor_url() {
        return author_url;
    }

    public void setAuthor_tag(String author_tag) {
        this.author_tag = author_tag;
    }

    public String getAuthor_tag() {
        return author_tag;
    }

    public void setAuthor_img(String author_img) {
        this.author_img = author_img;
    }

    public String getAuthor_img() {
        return author_img;
    }

    public void setAuthor_level(String author_level) {
        this.author_level = author_level;
    }

    public String getAuthor_level() {
        return author_level;
    }

    public void setAuthor_fans(int author_fans) {
        this.author_fans = author_fans;
    }

    public int getAuthor_fans() {
        return author_fans;
    }

    public void setAuthor_fields(String author_fields) {
        this.author_fields = author_fields;
    }

    public String getAuthor_fields() {
        return author_fields;
    }

    public void setAuthor_docs(int author_docs) {
        this.author_docs = author_docs;
    }

    public int getAuthor_docs() {
        return author_docs;
    }

    public void setDate_published(String date_published) {
        this.date_published = date_published;
    }

    public String getDate_published() {
        return date_published;
    }

    public void setQuality_score(double quality_score) {
        this.quality_score = quality_score;
    }

    public double getQuality_score() {
        return quality_score;
    }

    public void setContent_quality_label(String content_quality_label) {
        this.content_quality_label = content_quality_label;
    }

    public String getContent_quality_label() {
        return content_quality_label;
    }

    public void setContent_tag(String content_tag) {
        this.content_tag = content_tag;
    }

    public String getContent_tag() {
        return content_tag;
    }

    public void setLike_count(int like_count) {
        this.like_count = like_count;
    }

    public int getLike_count() {
        return like_count;
    }

    public void setUnlike_count(int unlike_count) {
        this.unlike_count = unlike_count;
    }

    public int getUnlike_count() {
        return unlike_count;
    }

    public void setComment_count(int comment_count) {
        this.comment_count = comment_count;
    }

    public int getComment_count() {
        return comment_count;
    }

    public void setComment_content(String comment_content) {
        this.comment_content = comment_content;
    }

    public String getComment_content() {
        return comment_content;
    }

    public void setCollect_count(int collect_count) {
        this.collect_count = collect_count;
    }

    public int getCollect_count() {
        return collect_count;
    }

    public void setRepost_count(int repost_count) {
        this.repost_count = repost_count;
    }

    public int getRepost_count() {
        return repost_count;
    }

    public void setImage_count(int image_count) {
        this.image_count = image_count;
    }

    public int getImage_count() {
        return image_count;
    }

    public void setVideo_list(String video_list) {
        this.video_list = video_list;
    }

    public String getVideo_list() {
        return video_list;
    }

    public void setIs_video(int is_video) {
        this.is_video = is_video;
    }

    public int getIs_video() {
        return is_video;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setSrc_type(String src_type) {
        this.src_type = src_type;
    }

    public String getSrc_type() {
        return src_type;
    }

    public void setSub_src(String sub_src) {
        this.sub_src = sub_src;
    }

    public String getSub_src() {
        return sub_src;
    }

    public void setThird_id(String third_id) {
        this.third_id = third_id;
    }

    public String getThird_id() {
        return third_id;
    }

    public void setIs_abroad(int is_abroad) {
        this.is_abroad = is_abroad;
    }

    public int getIs_abroad() {
        return is_abroad;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setPoi_type(int poi_type) {
        this.poi_type = poi_type;
    }

    public int getPoi_type() {
        return poi_type;
    }

    public void setPoi_list(BadiduDTO_Poi_list poi_list) {
        this.poi_list = poi_list;
    }

    public BadiduDTO_Poi_list getPoi_list() {
        return poi_list;
    }

}
