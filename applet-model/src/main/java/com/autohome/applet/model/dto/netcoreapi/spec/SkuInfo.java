package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class SkuInfo {

    @JsonProperty("seriesId")
    private int seriesId;

    @JsonProperty("skuId")
    private int skuId;

    @JsonProperty("editionId")
    private int editionId;

    @JsonProperty("imageUrl")
    private String imageUrl;

    @JsonProperty("jumpUrl")
    private String jumpUrl;

    @JsonProperty("isCpsSku")
    private int isCpsSku;

    @JsonProperty("clickaction")
    private String clickAction;

    @JsonProperty("showaction")
    private String showAction;

    @JsonProperty("extvalue")
    private String extValue;

    @JsonProperty("adTxtList")
    private String[] adTxtList;

    @JsonProperty("endDate")
    private long endDate;

    @JsonProperty("orderNum")
    private int orderNum;

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    public int getEditionId() {
        return editionId;
    }

    public void setEditionId(int editionId) {
        this.editionId = editionId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public int getIsCpsSku() {
        return isCpsSku;
    }

    public void setIsCpsSku(int isCpsSku) {
        this.isCpsSku = isCpsSku;
    }

    public String getClickAction() {
        return clickAction;
    }

    public void setClickAction(String clickAction) {
        this.clickAction = clickAction;
    }

    public String getShowAction() {
        return showAction;
    }

    public void setShowAction(String showAction) {
        this.showAction = showAction;
    }

    public String getExtValue() {
        return extValue;
    }

    public void setExtValue(String extValue) {
        this.extValue = extValue;
    }

    public String[] getAdTxtList() {
        return adTxtList;
    }

    public void setAdTxtList(String[] adTxtList) {
        this.adTxtList = adTxtList;
    }

    public long getEndDate() {
        return endDate;
    }

    public void setEndDate(long endDate) {
        this.endDate = endDate;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }
}
