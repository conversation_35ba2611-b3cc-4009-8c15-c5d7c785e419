package com.autohome.applet.model.dto.original;

import java.util.Date;

/**
 * 视频实体
 * @since
 */
public class ObjectInfoModel {
	
    //数据库唯一标识
    private int id;
    
    //标题
    private String title;
    
    //媒体类型
    private int mediatype;
    
    //发布时间
    private Date publishtime;
    
    //图片地址
    private String imgUrl;
    
    //页面详情
    private String summary;
    
    //评论数
    private int replycount;
	
	private int playcount;
	
	private int viewcount;
    
    //页码索引
    private String videoid;
    
    private int direction;
    
    //更新时间
    private Date updateTime;

    //视频时长
    private Integer duration;
    
    private Long userid;

    private String username;

    private String headimg;

    private String seriesids;
	
	private int cms_kind;
	
	public int getCms_kind() {
		return cms_kind;
	}
	
	public void setCms_kind(int cms_kind) {
		this.cms_kind = cms_kind;
	}
	
	public int getViewcount() {
		return viewcount;
	}
	
	public void setViewcount(int viewcount) {
		this.viewcount = viewcount;
	}
	
	public int getPlaycount() {
		return playcount;
	}
	
	public void setPlaycount(int playcount) {
		this.playcount = playcount;
	}
	
	public int getId() {
		return id;
	}
	
	public void setId(int id) {
		this.id = id;
	}
	
	public String getTitle() {
		return title;
	}
	
	public void setTitle(String title) {
		this.title = title;
	}
	
	public int getMediatype() {
		return mediatype;
	}
	
	public void setMediatype(int mediatype) {
		this.mediatype = mediatype;
	}
	
	public Date getPublishtime() {
		return publishtime;
	}
	
	public void setPublishtime(Date publishtime) {
		this.publishtime = publishtime;
	}
	
	public String getImgUrl() {
		return imgUrl;
	}
	
	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	
	public String getSummary() {
		return summary;
	}
	
	public void setSummary(String summary) {
		this.summary = summary;
	}
	
	public int getReplycount() {
		return replycount;
	}
	
	public void setReplycount(int replycount) {
		this.replycount = replycount;
	}
	
	public String getVideoid() {
		return videoid;
	}
	
	public void setVideoid(String videoid) {
		this.videoid = videoid;
	}
	
	public int getDirection() {
		return direction;
	}
	
	public void setDirection(int direction) {
		this.direction = direction;
	}
	
	public Date getUpdateTime() {
		return updateTime;
	}
	
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Integer getDuration() {
		return duration;
	}
	
	public void setDuration(Integer duration) {
		this.duration = duration;
	}
	
	public Long getUserid() {
		return userid;
	}
	
	public void setUserid(Long userid) {
		this.userid = userid;
	}
	
	public String getUsername() {
		return username;
	}
	
	public void setUsername(String username) {
		this.username = username;
	}
	
	public String getHeadimg() {
		return headimg;
	}
	
	public void setHeadimg(String headimg) {
		this.headimg = headimg;
	}
	
	public String getSeriesids() {
		return seriesids;
	}
	
	public void setSeriesids(String seriesids) {
		this.seriesids = seriesids;
	}
}
