package com.autohome.applet.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum ConfigInfoEnum {
    SPEC_NAME(113,"车型名称"),
    LEVEL(116, "级别"),
    CLTC(101, "CLTC纯电续航里程(km)"),
    WLTC(135, "WLTC纯电续航里程(km)"),
    NEDC(75, "NEDC纯电续航里程(km)"),
    FAST_CHARGE_TIME(79,"电池快充时间(小时)"),
    SLOW_CHARGE_TIME(81, "电池慢充时间(小时)"),
    HUNDRED_KM_POWER_CONSUMPTION(77, "百公里耗电量(kWh/100km)");

    private final int id;
    private final String name;
}