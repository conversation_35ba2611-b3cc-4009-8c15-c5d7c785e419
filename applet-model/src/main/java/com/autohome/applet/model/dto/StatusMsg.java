package com.autohome.applet.model.dto;

import lombok.Data;

@Data
public class StatusMsg<T> {
    private int status = 0;
    private String msg;
    private T data;
    private T result;

    public StatusMsg(T data) {
        this(0, null, data);
    }

    public StatusMsg(int status, String msg) {
        this(status, msg, null);
    }

    public StatusMsg(int status, String msg, T result) {
        this.status = status;
        this.msg = msg;
        this.data = result;
    }

    public StatusMsg() {
        this(0,null,null);
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public static <T> StatusMsg<T> buildSuccessResult(T data) {
        StatusMsg<T> rt = new StatusMsg<>();
        rt.setStatus(0);
        rt.setData(data);
        rt.setMsg("success");
        return rt;
    }

    public static <T> StatusMsg<T> buildErrorResult(int code, String msg) {
        StatusMsg<T> rt = new StatusMsg<>();
        rt.setStatus(code);
        rt.setMsg(msg);
        return rt;
    }

}

