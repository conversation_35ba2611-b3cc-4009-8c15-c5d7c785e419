package com.autohome.applet.model.dto.netcoreapi.dealer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Dealer {

    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<DealerInfo> list;

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getRowcount() {
        return rowcount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setList(List<DealerInfo> list) {
        this.list = list;
    }

    public List<DealerInfo> getList() {
        return list;
    }

}