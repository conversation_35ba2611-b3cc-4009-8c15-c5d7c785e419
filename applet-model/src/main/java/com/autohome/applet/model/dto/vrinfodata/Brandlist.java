package com.autohome.applet.model.dto.vrinfodata;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringEscapeUtils;

/**
 * @description:
 * @author: WangBoWen
 * @date: 2024-03-15
 **/

public class Brandlist {

    private String brandid;

    private String name;

    @JsonIgnore
    private String firstletter;

    private String logo;

    @JsonIgnore
    private String ordernum;

    public String getBrandid() {
        return brandid;
    }

    public void setBrandid(String brandid) {
        this.brandid = brandid;
    }

    public String getName() {
        if(name!=null && name.trim().length() > 0){
            return StringEscapeUtils.unescapeHtml4(name);
        }
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    //    @JsonIgnore
    public String getFirstletter() {
        return firstletter;
    }

    public void setFirstletter(String firstletter) {
        this.firstletter = firstletter;
    }
    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getOrdernum() {
        return ordernum;
    }

    public void setOrdernum(String ordernum) {
        this.ordernum = ordernum;
    }
}
