package com.autohome.applet.model.dto;

import lombok.Data;

@Data
public class BdCodeMsg<T> {
    private int errno = 0;
    private String errmsg;
    private String request_id;
    private long timestamp;
    private T data;

    public BdCodeMsg(T data) {
        this(0, null, data);
    }

    public BdCodeMsg(int code, String msg) {
        this(code, msg, null);
    }

    public BdCodeMsg(int code, String msg, T result) {
        this.errno = code;
        this.errmsg = msg;
        this.data = data;
    }

    public BdCodeMsg() {
        this(0,null,null);
    }

    public int getErrno() {
        return errno;
    }

    public void setErrno(int errno) {
        this.errno = errno;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public String getRequest_id() {
        return request_id;
    }

    public void setRequest_id(String request_id) {
        this.request_id = request_id;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> BdCodeMsg<T> buildSuccessResult(T data) {
        BdCodeMsg<T> rt = new BdCodeMsg<>();
        rt.setErrno(0);
        rt.setData(data);
        rt.setErrmsg("succ");
        return rt;
    }

    public static <T> BdCodeMsg<T> buildErrorResult(int code, String msg) {
        BdCodeMsg<T> rt = new BdCodeMsg<>();
        rt.setErrno(code);
        rt.setErrmsg(msg);
        return rt;
    }

}

