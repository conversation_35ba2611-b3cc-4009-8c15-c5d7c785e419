package com.autohome.applet.model.dto.ShortVideo;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BaseVideoAppletDto {

    @JsonProperty(value = "_index")
    private String index;

    @JsonProperty(value = "_type")
    private String type;

    @JsonProperty(value = "_id")
    private String id;

//    @JsonProperty(value = "_score")
//    private String score;
//
//    private List<Integer> sort;

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
