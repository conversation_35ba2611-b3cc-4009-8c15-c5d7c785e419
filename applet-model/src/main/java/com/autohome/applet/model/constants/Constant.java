package com.autohome.applet.model.constants;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class Constant {

    public static final String CLUB_APP = "user";

    public static final String APP_ID = "app";
    public static final String ACTI_BLACK_LIST = "acti_black_list";

    public static final String ACTI_WHITE_LIST = "acti_white_list";

    public static final String JSON_TYPE = "application/json;charset=UTF-8";
    public static final String FORM_TYPE = "application/x-www-form-urlencoded;charset=UTF-8";

    public static final int RETCODE_EXIST = 1, RETCODE_SUCCESS = 0, RETCODE_ERROR = 102, RETCODE_NOTFOUND = 404,
            RETCODE_IMGFILEILLEAGUE = 135, RETCODE_PARAMSLOST = 101, RETCODE_CERTERROR = 103, RETCODE_SIGNERROR = 104,
            RETCODE_SIGNFAIL = 105, RETCODE_CAREXIST = 106, RETCODE_APPLYERROR = 107, RETCODE_USERNOTEXIST = 108;

    public static final int SYSTEM_ERROR = 101;
    public static final String SYSTEM_ERROR_MESSAGE = "系统繁忙，请稍后再试";

    public static final int PARAM_ERROR = 102;
    public static final String PARAM_ERROR_MESSAGE = "缺少必要参数，请检查再试";

    public static final int AUTH_ERROR = 103;
    public static final String AUTH_ERROR_MESSAGE = "AUTH错误，请检查再试";

    public static final int SIGN_ERROR = 104;
    public static final String SIGN_ERROR_MESSAGE = "签名校验失败";

    public static final int SIGN_PARAM_ERROR = 105;
    public static final String SIGN_PARAM_ERROR_MESSAGE = "签名参数缺失";

    public static final int USER_INFO_ERROR = 106;
    public static final String USER_INFO_ERROR_MESSAGE = "获取用户信息错误，请检查再试";

    public static final int REQUEST_TIMES_ERROR = 107;
    public static final String REQUEST_TIMES_ERROR_MESSAGE = "请求过于频繁，请稍后再试";

    public static final int REQUEST_EXPIRE_ERROR = 108;
    public static final String REQUEST_EXPIRE_ERROR_MESSAGE = "请求已过期，请检查再试";

    public static final int REQUEST_HOST_PERMISSION_ERROR = 109;
    public static final String REQUEST_HOST_PERMISSION_ERROR_MESSAGE = "该Host禁止访问";

    public static final int REQUEST_LAN_API_PERMISSION_ERROR = 110;
    public static final String REQUEST_LAN_API_PERMISSION_ERROR_MESSAGE = "禁止外网访问";

    public static final int PARAM_VAILD_ERROR = 111;
    public static final String PARAM_VAILD_ERROR_MESSAGE = "必要参数校验失败，请检查再试";

    public static final int SYSTEM_USER_ERROR = 10009;
    public static final String SYSTEM_USER_ERROR_MESSAGE = "账号异常!";

    public static final int INVITE_GET_USER_INFO_ERROR = 110001;
    public static final String INVITE_GET_USER_INFO_MESSAGE = "获取用户信息错误";

    public static final int USERS_SUBMIT_INFO_ERROR = 110002;
    public static final String USERS_SUBMIT_INFO_ERROR_MESSAGE = "用户信息不合格信息";

    public static final int PARAM_FORMAT_ERROR = 110003;
    public static final String PARAM_FORMAT_ERROR_MESSAGE = "参数格式错误";

    public static final int INVITE_NETWORK_ERROR = 110004;
    public static final String INVITE_NETWORK_ERROR_MESSAGE = "网络错误，请重试";

    public static final String DEFAULT_USER_HEADIMG = "https://x.autoimg.cn/space/images/head_120X120.gif";

    public static final String DEFAULT_USER_HEADIMG_818 = "http://app2.autoimg"
                                                          + ".cn/appdfs/g30/M09/E6/8E"
                                                          + "/autohomecar__ChsEoFzibkyAKEbUAABBTxI6AoM862.png";

    public static final String ACTI_SEND_WALLET_FAIL_LIST = "ACTI_SEND_WALLET_FAIL_LIST";

    public static final String ACTI_INTERFACE_LINK_URL = "ACTI_INTERFACE_LINK_URL_%s";

    public static final int ORDER_NO_REPEAT = 110005;
    public static final String ORDER_NO_REPEAT_MESSAGE = "订单号重复";

    public static final int DECREASE_ORDER_PARAM_ERROR = 110006;
    public static final String DECREASE_ORDER_PARAM_ERROR_MESSAGE = "消费参数错误";

    public static final int REWARD_ORDER_PARAM_ERROR = 110007;
    public static final String REWARD_ORDER_PARAM_ERROR_MESSAGE = "发放参数错误";

    public static final int ORDER_PARAM_ERROR = 110008;
    public static final String ORDER_PARAM_ERROR_MESSAGE = "发放参数错误";

    public static final int ORDER_BALANCE_INSUFFICIENT_ERROR = 110009;
    public static final String ORDER_BALANCE_INSUFFICIENT_ERROR_MESSAGE = "用户余额不足";

    public static final int INVITE_WITHDRAY_ENCRYPTEN_ERROR = 110010;
    public static final String INVITE_WITHDRAY_ENCRYPTEN_ERROR_MESSAGE = "今日限额已提完,请明日再来！";

    public static final int REWARD_ORDER_SEND_ERROR = 110011;
    public static final String REWARD_ORDER_SEND_ERROR_MESSAGE = "发放失败";

    /**
     * 用户短信验证码发送次数
     */
    public static final String ACTI_COMMON_MSG_CODE_SEND_COUNT = "ACTI_COMMON_MSG_CODE_SEND_COUNT_%s";

    /**
     * 用户短信验证码发送次数
     */
    public static final String ACTI_COMMON_MSG_VERIFY_CODE = "ACTI_COMMON_MSG_VERIFY_CODE_%s";

    /**
     * 主app签名key
     */
    public static final String APP_SIGN_KEY = "@7U$aPOE@$";


    public static final String SIGN_NEWUSER_CARD = "acti_sign_newcard";
    public static final String SIGN_EXCHANGEREREWARD_CARD = "acti_sign_rewardlist";
    public static final String SIGN_BANNER_CARD = "acti_sign_banner";
    public static final String SIGN_TASK_CARD = "acti_sign_task";
    public static final String SIGN_DUIBA_CARD = "acti_sign_duiba";
    public static final String SIGN_NEWUSER_PRIZE = "acti_sign_newuser";
    public static final String SIGN_LOTTERY_PRIZE = "acti_sign_lottery";
    public static final String SIGN_LOTTERY_TIPS = "acti_sign_tips";
    public static final String SIGN_TASK_YOUXI = "acti_sign_youxi";
    public static final String PUSH_CONTENT = "sign_pushcontent";

    /**
     * 任务不可参与
     */
    public static final int NOT_PARTICIPATE = 1;

    /**
     * 任务可参与
     */
    public static final int CAN_PARTICIPATE = 2;

    /**
     * 任务参与完成
     */
    public static final int FINISH_PARTICIPATE = 3;

    /**
     * 任务领奖状态
     */
    public static final int REWARD_STATUS = 4;

    public static final int TASK_FINISH = 1;

    public static final int TASK_NOT_BEGIN = 0;

    public static final int TASK_UN_FINISH = 2;

    public static final int TASK_EXPIRE = -1;
    public static final Map<String, String> MALLCODE_KEY = new HashMap<>();
    public static Set<String> filterSet = new HashSet<>();

    static {
        filterSet.add("finish_type");
        filterSet.add("position");
        filterSet.add("showcontent");
        filterSet.add("caculate");
        filterSet.add("jumpurl");
        filterSet.add("subscripts_isshow");
        filterSet.add("subscripts_content");
        filterSet.add("verify_isshow");
        filterSet.add("title");
        filterSet.add("sub_title");
        filterSet.add("title_img_url");
        filterSet.add("button_content");
        filterSet.add("finish_content");
        filterSet.add("subscripts_img_url");

        MALLCODE_KEY.put("201691575_autoappid", "F82527BD4D54420891656504C8F60D22");
        MALLCODE_KEY.put("201691575_secret", "FF2BB9AA81A74374A8317F8E2C109B5A");
    }
}
