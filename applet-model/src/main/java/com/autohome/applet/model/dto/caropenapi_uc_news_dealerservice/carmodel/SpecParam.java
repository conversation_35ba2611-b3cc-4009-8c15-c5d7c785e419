package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by ha<PERSON><PERSON>feng on 2018/8/28.
 */
public class SpecParam {
    private int specid;
    private String specname;
    private int seriesid;
    private String seriesname;
    private int brandid;
    private String brandname;
    private int fctid;
    private String fctname;
    private int levelid;
    private String levelname;
    private int specpicount;
    private String speclogo;
    private int specminprice;
    private int specmaxprice;
    private int specengineid;
    private String specenginename;
    private int specstructuredoor;
    private String specstructureseat;
    private String specstructuretypename;
    private String spectransmission;
    private int specstate;
    private int specoiloffical;
    private int speclength;
    private int specwidth;
    private int specheight;
    private int specweight;
    private String specdrivingmodename;
    private int specflowmodeid;
    private String specflowmodename;
    private double specdisplacement;
    private int specenginepower;
    private int specparamisshow;
    private int specispreferential;
    private int specistaxrelief;
    private int specistaxexemption;
    private String specquality;
    private String specisimport;
    private boolean specisbooked;
    private String dynamicprice;
    private int oilboxvolume;
    private int fueltype;
    private String fastchargetime;
    private String slowchargetime;
    @JsonProperty("fastchargePercent")
    private String fastchargepercent;
    private String batterycapacity;
    private String mile;
    private int fueltypedetail;
    private String fueltypename;
    private String greenstandards;
    private String enginetorque;
    private String engingkw;

    public int getSpecid() {
        return specid;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public int getFctid() {
        return fctid;
    }

    public void setFctid(int fctid) {
        this.fctid = fctid;
    }

    public String getFctname() {
        return fctname;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public int getLevelid() {
        return levelid;
    }

    public void setLevelid(int levelid) {
        this.levelid = levelid;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public int getSpecpicount() {
        return specpicount;
    }

    public void setSpecpicount(int specpicount) {
        this.specpicount = specpicount;
    }

    public String getSpeclogo() {
        return speclogo;
    }

    public void setSpeclogo(String speclogo) {
        this.speclogo = speclogo;
    }

    public int getSpecminprice() {
        return specminprice;
    }

    public void setSpecminprice(int specminprice) {
        this.specminprice = specminprice;
    }

    public int getSpecmaxprice() {
        return specmaxprice;
    }

    public void setSpecmaxprice(int specmaxprice) {
        this.specmaxprice = specmaxprice;
    }

    public int getSpecengineid() {
        return specengineid;
    }

    public void setSpecengineid(int specengineid) {
        this.specengineid = specengineid;
    }

    public String getSpecenginename() {
        return specenginename;
    }

    public void setSpecenginename(String specenginename) {
        this.specenginename = specenginename;
    }

    public int getSpecstructuredoor() {
        return specstructuredoor;
    }

    public void setSpecstructuredoor(int specstructuredoor) {
        this.specstructuredoor = specstructuredoor;
    }

    public String getSpecstructureseat() {
        return specstructureseat;
    }

    public void setSpecstructureseat(String specstructureseat) {
        this.specstructureseat = specstructureseat;
    }

    public boolean isSpecisbooked() {
        return specisbooked;
    }

    public String getSpecstructuretypename() {
        return specstructuretypename;
    }

    public void setSpecstructuretypename(String specstructuretypename) {
        this.specstructuretypename = specstructuretypename;
    }

    public String getSpectransmission() {
        return spectransmission;
    }

    public void setSpectransmission(String spectransmission) {
        this.spectransmission = spectransmission;
    }

    public int getSpecstate() {
        return specstate;
    }

    public void setSpecstate(int specstate) {
        this.specstate = specstate;
    }

    public int getSpecoiloffical() {
        return specoiloffical;
    }

    public void setSpecoiloffical(int specoiloffical) {
        this.specoiloffical = specoiloffical;
    }

    public int getSpeclength() {
        return speclength;
    }

    public void setSpeclength(int speclength) {
        this.speclength = speclength;
    }

    public int getSpecwidth() {
        return specwidth;
    }

    public void setSpecwidth(int specwidth) {
        this.specwidth = specwidth;
    }

    public int getSpecheight() {
        return specheight;
    }

    public void setSpecheight(int specheight) {
        this.specheight = specheight;
    }

    public int getSpecweight() {
        return specweight;
    }

    public void setSpecweight(int specweight) {
        this.specweight = specweight;
    }

    public String getSpecdrivingmodename() {
        return specdrivingmodename;
    }

    public void setSpecdrivingmodename(String specdrivingmodename) {
        this.specdrivingmodename = specdrivingmodename;
    }

    public int getSpecflowmodeid() {
        return specflowmodeid;
    }

    public void setSpecflowmodeid(int specflowmodeid) {
        this.specflowmodeid = specflowmodeid;
    }

    public String getSpecflowmodename() {
        return specflowmodename;
    }

    public void setSpecflowmodename(String specflowmodename) {
        this.specflowmodename = specflowmodename;
    }

    public double getSpecdisplacement() {
        return specdisplacement;
    }

    public void setSpecdisplacement(double specdisplacement) {
        this.specdisplacement = specdisplacement;
    }

    public int getSpecenginepower() {
        return specenginepower;
    }

    public void setSpecenginepower(int specenginepower) {
        this.specenginepower = specenginepower;
    }

    public int getSpecparamisshow() {
        return specparamisshow;
    }

    public void setSpecparamisshow(int specparamisshow) {
        this.specparamisshow = specparamisshow;
    }

    public int getSpecispreferential() {
        return specispreferential;
    }

    public void setSpecispreferential(int specispreferential) {
        this.specispreferential = specispreferential;
    }

    public int getSpecistaxrelief() {
        return specistaxrelief;
    }

    public void setSpecistaxrelief(int specistaxrelief) {
        this.specistaxrelief = specistaxrelief;
    }

    public int getSpecistaxexemption() {
        return specistaxexemption;
    }

    public void setSpecistaxexemption(int specistaxexemption) {
        this.specistaxexemption = specistaxexemption;
    }

    public String getSpecquality() {
        return specquality;
    }

    public void setSpecquality(String specquality) {
        this.specquality = specquality;
    }

    public String getSpecisimport() {
        return specisimport;
    }

    public void setSpecisimport(String specisimport) {
        this.specisimport = specisimport;
    }

    public boolean getSpecisbooked() {
        return specisbooked;
    }

    public void setSpecisbooked(boolean specisbooked) {
        this.specisbooked = specisbooked;
    }

    public String getDynamicprice() {
        return dynamicprice;
    }

    public void setDynamicprice(String dynamicprice) {
        this.dynamicprice = dynamicprice;
    }

    public int getOilboxvolume() {
        return oilboxvolume;
    }

    public void setOilboxvolume(int oilboxvolume) {
        this.oilboxvolume = oilboxvolume;
    }

    public int getFueltype() {
        return fueltype;
    }

    public void setFueltype(int fueltype) {
        this.fueltype = fueltype;
    }

    public String getFastchargetime() {
        return fastchargetime;
    }

    public void setFastchargetime(String fastchargetime) {
        this.fastchargetime = fastchargetime;
    }

    public String getSlowchargetime() {
        return slowchargetime;
    }

    public void setSlowchargetime(String slowchargetime) {
        this.slowchargetime = slowchargetime;
    }

    public String getFastchargepercent() {
        return fastchargepercent;
    }

    public void setFastchargepercent(String fastchargepercent) {
        this.fastchargepercent = fastchargepercent;
    }

    public String getBatterycapacity() {
        return batterycapacity;
    }

    public void setBatterycapacity(String batterycapacity) {
        this.batterycapacity = batterycapacity;
    }

    public String getMile() {
        return mile;
    }

    public void setMile(String mile) {
        this.mile = mile;
    }

    public int getFueltypedetail() {
        return fueltypedetail;
    }

    public void setFueltypedetail(int fueltypedetail) {
        this.fueltypedetail = fueltypedetail;
    }

    public String getFueltypename() {
        return fueltypename;
    }

    public void setFueltypename(String fueltypename) {
        this.fueltypename = fueltypename;
    }

    public String getGreenstandards() {
        return greenstandards;
    }

    public void setGreenstandards(String greenstandards) {
        this.greenstandards = greenstandards;
    }

    public String getEnginetorque() {
        return enginetorque;
    }

    public void setEnginetorque(String enginetorque) {
        this.enginetorque = enginetorque;
    }

    public String getEngingkw() {
        return engingkw;
    }

    public void setEngingkw(String engingkw) {
        this.engingkw = engingkw;
    }

    //计算车门数
    public int getSpecstructureseatnum() {
        if (StringUtil.isBlank(this.getSpecstructureseat())) {
            return 5;
        }
        if (this.getSpecstructureseat().contains("-") || this.getSpecstructureseat().contains("/")) {
            String[] numArr = this.getSpecstructureseat().split("[\\-/]");
            return Integer.parseInt(numArr[numArr.length - 1]);
        }
        return Integer.parseInt(this.getSpecstructureseat());
    }
}
