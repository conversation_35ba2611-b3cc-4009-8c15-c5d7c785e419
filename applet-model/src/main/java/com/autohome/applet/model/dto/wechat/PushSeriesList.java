package com.autohome.applet.model.dto.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 微信搜一搜-汽车车系推送
 */
@Builder
@Data
public class PushSeriesList {

    @JsonProperty("chexi_list")
    private List<CarSeriesList> chexiList;

    @JsonProperty("is_test")
    private int isTest;

    @Builder
    @Data
    public static class CarSeriesList {
        @JsonProperty("chexi_id")
        private String chexiId;

        @JsonProperty("chexi_name")
        private String chexiName;

        @JsonProperty("brand_ids")
        private List<String> brandIds;

        @JsonProperty("chexi_type")
        private int chexiType;

        @JsonProperty("hot_num")
        private double hotNum;

        @JsonProperty("img_url")
        private String imgUrl;

        @JsonProperty("market_time")
        private String marketTime;

        @JsonProperty("low_price")
        private int lowPrice;

        @JsonProperty("high_price")
        private int highPrice;

        @JsonProperty("sale_num")
        private int saleNum;

        @JsonProperty("jump_wording")
        private String jumpWording;

        @JsonProperty("car_colors")
        private List<CarColor> carColors;

        @JsonProperty("engine_types")
        private List<Integer> engineTypes;

        @JsonProperty("oil_cost")
        private String oilCost;

        @JsonProperty("engine")
        private String engine;

        @JsonProperty("battery_life")
        private String  batteryLife;

        @JsonProperty("charge_time")
        private String chargeTime;

        @JsonProperty("status")
        private int status;

        @JsonProperty("chexi_jump_info")
        private JumpInfo chexiJumpInfo;

        @JsonProperty("params_jump_info")
        private JumpInfo paramsJumpInfo;

//        @JsonProperty("aliases")
//        private List<String> aliases;

        @JsonProperty("sale_status")
        private int saleStatus;

        @JsonProperty("search_img_dir_jump_info")
        private List<SearchJumpInfo> searchImgDirJumpInfo;

    }

    @Builder
    @Data
    public static class JumpInfo {
        @JsonProperty("jump_type")
        private int jumpType;

        @JsonProperty("appid")
        private String appid;

        @JsonProperty("service_url")
        private String serviceUrl;
    }

    @Builder
    @Data
    public static class SearchJumpInfo {
        @JsonProperty("jump_type")
        private int jumpType;

        @JsonProperty("appid")
        private String appid;

        @JsonProperty("service_url")
        private String serviceUrl;

        @JsonProperty("search_img_url")
        private String searchImgUrl;
    }

    @Builder
    @Data
    public static class CarColor {

        @JsonProperty("desc")
        private String desc;

        @JsonProperty("value")
        private String value;

    }


}
