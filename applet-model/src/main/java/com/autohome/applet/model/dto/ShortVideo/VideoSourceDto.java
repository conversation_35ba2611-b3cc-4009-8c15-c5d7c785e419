package com.autohome.applet.model.dto.ShortVideo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class VideoSourceDto {

    @JsonProperty(value = "author_name")
    private String authorName;

    private Integer vv;

    private String summary;

    private Integer pv;

    @JsonProperty(value = "main_data_type")
    private String mainDataType;

    @JsonProperty(value = "author_img")
    private String authorImg;

    @JsonProperty(value = "img_url_16x9")
    private String imgUrl16x9;

    @JsonProperty(value = "img_url_4x3")
    private String imgUrl4x3;

    @JsonProperty(value = "reply_count")
    private Integer replyCount;

    private String title;

    @JsonProperty(value = "is_delete")
    private Integer isDelete;

    @JsonProperty(value = "video_app_title")
    private String videoAppTitle;

    @JsonProperty(value = "m_url")
    private String mUrl;

    @JsonProperty(value = "biz_update_time")
    private String bizUpdateTime;

    @JsonProperty(value = "pool_biz_types")
    private List<Integer> poolBizTypes;

    @JsonProperty(value = "parent_biz_id")
    private Integer parentBizId;

    @JsonProperty(value = "img_url")
    private String imgUrl;

    @JsonProperty(value = "publish_time")
    private String publishTime;

    @JsonProperty(value = "author_id")
    private Integer authorId;

    @JsonProperty(value = "biz_id")
    private Integer bizId;

    @JsonProperty(value = "pc_url")
    private String pcUrl;

    @JsonProperty(value = "small_title")
    private String smallTitle;

    @JsonProperty(value = "is_publish")
    private Integer isPublish;

    private Integer duration;

    @JsonProperty(value = "series_ids")
    private List<Integer> seriesIds;


    public List<Integer> getSeriesIds() {
        return seriesIds;
    }

    public void setSeriesIds(List<Integer> seriesIds) {
        this.seriesIds = seriesIds;
    }

    @JsonProperty(value = "video_original_tag_names")
    private List<String> videoOriginalTagNames;

    public List<String> getVideoOriginalTagNames() {
        return videoOriginalTagNames;
    }

    public void setVideoOriginalTagNames(List<String> videoOriginalTagNames) {
        this.videoOriginalTagNames = videoOriginalTagNames;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }


    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Integer getVv() {
        return vv;
    }

    public void setVv(Integer vv) {
        this.vv = vv;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getPv() {
        return pv;
    }

    public void setPv(Integer pv) {
        this.pv = pv;
    }

    public String getMainDataType() {
        return mainDataType;
    }

    public void setMainDataType(String mainDataType) {
        this.mainDataType = mainDataType;
    }

    public String getAuthorImg() {
        return authorImg;
    }

    public void setAuthorImg(String authorImg) {
        this.authorImg = authorImg;
    }

    public String getImgUrl16x9() {
        return imgUrl16x9;
    }

    public void setImgUrl16x9(String imgUrl16x9) {
        this.imgUrl16x9 = imgUrl16x9;
    }

    public String getImgUrl4x3() {
        return imgUrl4x3;
    }

    public void setImgUrl4x3(String imgUrl4x3) {
        this.imgUrl4x3 = imgUrl4x3;
    }

    public Integer getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(Integer replyCount) {
        this.replyCount = replyCount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getVideoAppTitle() {
        return videoAppTitle;
    }

    public void setVideoAppTitle(String videoAppTitle) {
        this.videoAppTitle = videoAppTitle;
    }

    public String getmUrl() {
        return mUrl;
    }

    public void setmUrl(String mUrl) {
        this.mUrl = mUrl;
    }

    public String getBizUpdateTime() {
        return bizUpdateTime;
    }

    public void setBizUpdateTime(String bizUpdateTime) {
        this.bizUpdateTime = bizUpdateTime;
    }

    public List<Integer> getPoolBizTypes() {
        return poolBizTypes;
    }

    public void setPoolBizTypes(List<Integer> poolBizTypes) {
        this.poolBizTypes = poolBizTypes;
    }

    public Integer getParentBizId() {
        return parentBizId;
    }

    public void setParentBizId(Integer parentBizId) {
        this.parentBizId = parentBizId;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public Integer getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Integer authorId) {
        this.authorId = authorId;
    }

    public Integer getBizId() {
        return bizId;
    }

    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public String getSmallTitle() {
        return smallTitle;
    }

    public void setSmallTitle(String smallTitle) {
        this.smallTitle = smallTitle;
    }

    public Integer getIsPublish() {
        return isPublish;
    }

    public void setIsPublish(Integer isPublish) {
        this.isPublish = isPublish;
    }
}
