package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class TwoTabName {
    private String name;

    private boolean isSame;

    private boolean isEmpty;

    public TwoTabName() {
    }

    public TwoTabName(String name, boolean isSame, boolean isEmpty) {
        this.name = name;
        this.isSame = isSame;
        this.isEmpty = isEmpty;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("isSame")
    public boolean isSame() {
        return isSame;
    }

    public void setSame(boolean same) {
        isSame = same;
    }

    @JsonProperty("isEmpty")
    public boolean isEmpty() {
        return isEmpty;
    }

    public void setEmpty(boolean empty) {
        isEmpty = empty;
    }
}
