package com.autohome.applet.model.dto.carlibrary.carapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CarApiSpecPicDetail {
    @JsonProperty("specid")
    private int specId;

    @JsonProperty("specname")
    private String specName;

    @JsonProperty("seriesid")
    private int seriesId;

    @JsonProperty("seriesName")
    private String seriesName;

    @JsonProperty("brandid")
    private int brandId;

    @JsonProperty("brandname")
    private String brandName;

    @JsonProperty("specprice")
    private double specPrice;

    @JsonProperty("picitems")
    private List<PicItem> picItems;

    @JsonProperty("specstate")
    private int specState;

    @Data
    public static class PicItem {
        @JsonProperty("typename")
        private String typeName;

        @JsonProperty("picitems")
        private List<Item> picItems;
    }

    @Data
    public static class Item {
        @JsonProperty("itemid")
        private int itemId;

        @JsonProperty("itemname")
        private String itemName;

        @JsonProperty("typeid")
        private int typeId;

        @JsonProperty("picid")
        private long picId;

        @JsonProperty("picpath")
        private String picPath;

        @JsonProperty("remark")
        private String remark;

    }
}
