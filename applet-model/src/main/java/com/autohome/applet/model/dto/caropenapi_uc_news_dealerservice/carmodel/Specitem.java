package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

/**
 * Created by Administrator on 2018/8/19.
 */
public class Specitem {
    private int id;
    private String name;
    private int minprice;
    private int maxprice;
    private String logo;
    private int yearid;
    private int yearname;
    private int seriesid;
    private String seriesname;
    private String serieslogo;
    private String seriesofficialurl;
    private String seriesfirstletter;
    private int brandid;
    private String brandname;
    private String brandlogo;
    private String brandofficialurl;
    private String brandfirstletter;
    private int fctid;
    private String fctname;
    private String fctlogo;
    private String fctofficialurl;
    private String fctfirstletter;
    private int levelid;
    private String levelname;
    private String specquality;
    private int state;
    private int paramisshow;
    private String timemarket;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(int maxprice) {
        this.maxprice = maxprice;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public int getYearid() {
        return yearid;
    }

    public void setYearid(int yearid) {
        this.yearid = yearid;
    }

    public int getYearname() {
        return yearname;
    }

    public void setYearname(int yearname) {
        this.yearname = yearname;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSerieslogo() {
        return serieslogo;
    }

    public void setSerieslogo(String serieslogo) {
        this.serieslogo = serieslogo;
    }

    public String getSeriesofficialurl() {
        return seriesofficialurl;
    }

    public void setSeriesofficialurl(String seriesofficialurl) {
        this.seriesofficialurl = seriesofficialurl;
    }

    public String getSeriesfirstletter() {
        return seriesfirstletter;
    }

    public void setSeriesfirstletter(String seriesfirstletter) {
        this.seriesfirstletter = seriesfirstletter;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandlogo() {
        return brandlogo;
    }

    public void setBrandlogo(String brandlogo) {
        this.brandlogo = brandlogo;
    }

    public String getBrandofficialurl() {
        return brandofficialurl;
    }

    public void setBrandofficialurl(String brandofficialurl) {
        this.brandofficialurl = brandofficialurl;
    }

    public String getBrandfirstletter() {
        return brandfirstletter;
    }

    public void setBrandfirstletter(String brandfirstletter) {
        this.brandfirstletter = brandfirstletter;
    }

    public int getFctid() {
        return fctid;
    }

    public void setFctid(int fctid) {
        this.fctid = fctid;
    }

    public String getFctname() {
        return fctname;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public String getFctlogo() {
        return fctlogo;
    }

    public void setFctlogo(String fctlogo) {
        this.fctlogo = fctlogo;
    }

    public String getFctofficialurl() {
        return fctofficialurl;
    }

    public void setFctofficialurl(String fctofficialurl) {
        this.fctofficialurl = fctofficialurl;
    }

    public String getFctfirstletter() {
        return fctfirstletter;
    }

    public void setFctfirstletter(String fctfirstletter) {
        this.fctfirstletter = fctfirstletter;
    }

    public int getLevelid() {
        return levelid;
    }

    public void setLevelid(int levelid) {
        this.levelid = levelid;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public String getSpecquality() {
        return specquality;
    }

    public void setSpecquality(String specquality) {
        this.specquality = specquality;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getParamisshow() {
        return paramisshow;
    }

    public void setParamisshow(int paramisshow) {
        this.paramisshow = paramisshow;
    }

    public String getTimemarket() {
        return timemarket;
    }

    public void setTimemarket(String timemarket) {
        this.timemarket = timemarket;
    }
}