package com.autohome.applet.model.lightapp;

public class SummaryDto {
    private Integer SeriesKey;
    private Integer TypeKey;
    private Integer SalesTypeKey;
    private Integer SeriesSummaryKey;
    private String Combination;
    private Integer Volume;
    private Integer SentimentKey;

    public Integer getSeriesKey() {
        return SeriesKey;
    }

    public void setSeriesKey(Integer seriesKey) {
        SeriesKey = seriesKey;
    }

    public Integer getTypeKey() {
        return TypeKey;
    }

    public void setTypeKey(Integer typeKey) {
        TypeKey = typeKey;
    }

    public Integer getSalesTypeKey() {
        return SalesTypeKey;
    }

    public void setSalesTypeKey(Integer salesTypeKey) {
        SalesTypeKey = salesTypeKey;
    }

    public Integer getSeriesSummaryKey() {
        return SeriesSummaryKey;
    }

    public void setSeriesSummaryKey(Integer seriesSummaryKey) {
        SeriesSummaryKey = seriesSummaryKey;
    }

    public String getCombination() {
        return Combination;
    }

    public void setCombination(String combination) {
        Combination = combination;
    }

    public Integer getVolume() {
        return Volume;
    }

    public void setVolume(Integer volume) {
        Volume = volume;
    }

    public Integer getSentimentKey() {
        return SentimentKey;
    }

    public void setSentimentKey(Integer sentimentKey) {
        SentimentKey = sentimentKey;
    }
}
