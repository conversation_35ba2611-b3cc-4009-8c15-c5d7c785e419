package com.autohome.applet.model.dto.netcoreapi.series;

import java.math.BigDecimal;

public class SpecItem {

    private BigDecimal displacement;
    private String drivingmodename;
    private int electrickw;
    private int electrictype;
    private String emissionstandards;
    private int enginepower;
    private int flowmodeid;
    private String flowmodename;
    private String fueltype;
    private int fueltypeid;
    private String gearbox;
    private int id;
    private int isclassic;
    private int isnewcar;
    private int ispreferential;
    private int istaxexemption;
    private int istaxrelief;
    private String logo;
    private long maxprice;
    private long minprice;
    private String name;
    private int order;
    private int paramisshow;
    private String seat;
    private int specisimage;
    private int state;
    private String structtype;
    private int syearid;
    private String transmission;
    private int year;

    public void setDisplacement(BigDecimal displacement) {
        this.displacement = displacement;
    }

    public BigDecimal getDisplacement() {
        return displacement;
    }

    public void setDrivingmodename(String drivingmodename) {
        this.drivingmodename = drivingmodename;
    }

    public String getDrivingmodename() {
        return drivingmodename;
    }

    public void setElectrickw(int electrickw) {
        this.electrickw = electrickw;
    }

    public int getElectrickw() {
        return electrickw;
    }

    public void setElectrictype(int electrictype) {
        this.electrictype = electrictype;
    }

    public int getElectrictype() {
        return electrictype;
    }

    public void setEmissionstandards(String emissionstandards) {
        this.emissionstandards = emissionstandards;
    }

    public String getEmissionstandards() {
        return emissionstandards;
    }

    public void setEnginepower(int enginepower) {
        this.enginepower = enginepower;
    }

    public int getEnginepower() {
        return enginepower;
    }

    public void setFlowmodeid(int flowmodeid) {
        this.flowmodeid = flowmodeid;
    }

    public int getFlowmodeid() {
        return flowmodeid;
    }

    public void setFlowmodename(String flowmodename) {
        this.flowmodename = flowmodename;
    }

    public String getFlowmodename() {
        return flowmodename;
    }

    public void setFueltype(String fueltype) {
        this.fueltype = fueltype;
    }

    public String getFueltype() {
        return fueltype;
    }

    public void setFueltypeid(int fueltypeid) {
        this.fueltypeid = fueltypeid;
    }

    public int getFueltypeid() {
        return fueltypeid;
    }

    public void setGearbox(String gearbox) {
        this.gearbox = gearbox;
    }

    public String getGearbox() {
        return gearbox;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setIsclassic(int isclassic) {
        this.isclassic = isclassic;
    }

    public int getIsclassic() {
        return isclassic;
    }

    public void setIsnewcar(int isnewcar) {
        this.isnewcar = isnewcar;
    }

    public int getIsnewcar() {
        return isnewcar;
    }

    public void setIspreferential(int ispreferential) {
        this.ispreferential = ispreferential;
    }

    public int getIspreferential() {
        return ispreferential;
    }

    public void setIstaxexemption(int istaxexemption) {
        this.istaxexemption = istaxexemption;
    }

    public int getIstaxexemption() {
        return istaxexemption;
    }

    public void setIstaxrelief(int istaxrelief) {
        this.istaxrelief = istaxrelief;
    }

    public int getIstaxrelief() {
        return istaxrelief;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public void setMaxprice(long maxprice) {
        this.maxprice = maxprice;
    }

    public long getMaxprice() {
        return maxprice;
    }

    public void setMinprice(long minprice) {
        this.minprice = minprice;
    }

    public long getMinprice() {
        return minprice;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getOrder() {
        return order;
    }

    public void setParamisshow(int paramisshow) {
        this.paramisshow = paramisshow;
    }

    public int getParamisshow() {
        return paramisshow;
    }

    public void setSeat(String seat) {
        this.seat = seat;
    }

    public String getSeat() {
        return seat;
    }

    public void setSpecisimage(int specisimage) {
        this.specisimage = specisimage;
    }

    public int getSpecisimage() {
        return specisimage;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setStructtype(String structtype) {
        this.structtype = structtype;
    }

    public String getStructtype() {
        return structtype;
    }

    public void setSyearid(int syearid) {
        this.syearid = syearid;
    }

    public int getSyearid() {
        return syearid;
    }

    public void setTransmission(String transmission) {
        this.transmission = transmission;
    }

    public String getTransmission() {
        return transmission;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getYear() {
        return year;
    }

}