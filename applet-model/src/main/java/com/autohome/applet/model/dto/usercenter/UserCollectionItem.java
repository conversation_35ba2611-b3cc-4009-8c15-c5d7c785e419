package com.autohome.applet.model.dto.usercenter;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class UserCollectionItem {

    @JsonProperty("typeId")
    private int typeId;

    @JsonProperty("userId")
    private int userId;

    @JsonProperty("updateTime")
    private String updateTime;

    @JsonProperty("objId")
    private int objId;

    @JsonProperty("objTitle")
    private String objTitle;

    @JsonProperty("imgUrl")
    private String imgUrl;

    @JsonProperty("objUrl")
    private String objUrl;

    @JsonProperty("isNotice")
    private int isNotice;

    @JsonProperty("subType")
    private int subType;

    @JsonProperty("createTime")
    private String createTime;

    @JsonProperty("collectionId")
    private long collectionId;

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getObjId() {
        return objId;
    }

    public void setObjId(int objId) {
        this.objId = objId;
    }

    public String getObjTitle() {
        return objTitle;
    }

    public void setObjTitle(String objTitle) {
        this.objTitle = objTitle;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getObjUrl() {
        return objUrl;
    }

    public void setObjUrl(String objUrl) {
        this.objUrl = objUrl;
    }

    public int getIsNotice() {
        return isNotice;
    }

    public void setIsNotice(int isNotice) {
        this.isNotice = isNotice;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public long getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(long collectionId) {
        this.collectionId = collectionId;
    }

    public static class UserCollectionList {

        private int rowcount;
        private List<UserCollectionItem> list;

        public int getRowcount() {
            return rowcount;
        }

        public void setRowcount(int rowcount) {
            this.rowcount = rowcount;
        }

        public List<UserCollectionItem> getList() {
            return list;
        }

        public void setList(List<UserCollectionItem> list) {
            this.list = list;
        }
    }
}
