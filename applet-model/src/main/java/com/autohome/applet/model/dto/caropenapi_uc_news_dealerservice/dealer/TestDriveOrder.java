package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by hanshanfeng on 2019/5/20.
 */
public class TestDriveOrder {
    private int rowcount;

    private int pagecount;

    private int pageindex;
    
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OderList> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<OderList> getList() {
        return list;
    }

    public void setList(List<OderList> list) {
        this.list = list;
    }

    public static class OderList {
        @JsonProperty("orderId")
        private String orderid;
        @JsonProperty("userName")
        private String username;
        @JsonProperty("userMobile")
        private String usermobile;
        @JsonProperty("testDriveDate")
        private String testdrivedate;
        @JsonProperty("cityId")
        private int cityid;
        @JsonProperty("cityName")
        private String cityname;
        @JsonProperty("dealerInfoId")
        private int dealerinfoid;
        @JsonProperty("dealerInfoName")
        private String dealerinfoname;
        @JsonProperty("dealerLat")
        private int dealerlat;
        @JsonProperty("dealerLng")
        private double dealerlng;
        @JsonProperty("brandId")
        private int brandid;
        @JsonProperty("brandName")
        private String brandname;
        @JsonProperty("seriesId")
        private int seriesid;
        private String serieslogo;
        @JsonProperty("seriesName")
        private String seriesname;
        @JsonProperty("orderState")
        private int orderstate;
        @JsonProperty("userId")
        private int userid;
        @JsonProperty("isComment")
        private int iscomment;
        private String ordertime;
        private String seriespicurl;

        public String getSeriespicurl() {
            return seriespicurl;
        }

        public void setSeriespicurl(String seriespicurl) {
            this.seriespicurl = seriespicurl;
        }

        public String getOrdertime() {
            return ordertime;
        }

        public void setOrdertime(String ordertime) {
            this.ordertime = ordertime;
        }

        public String getSerieslogo() {
            if (StringUtil.isNotNullAndWhiteSpace(seriespicurl)) {
                return seriespicurl;
//                return CarSettings.ConvertImgNew(seriespicurl,"400x320_");
            }
            return serieslogo;
        }

        public void setSerieslogo(String serieslogo) {
            this.serieslogo = serieslogo;
        }

        public void setOrderid(String orderid) {
            this.orderid = orderid;
        }

        public String getOrderid() {
            return orderid;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getUsername() {
            return username;
        }

        public void setUsermobile(String usermobile) {
            this.usermobile = usermobile;
        }

        public String getUsermobile() {
            return usermobile;
        }

        public void setTestdrivedate(String testdrivedate) {
            this.testdrivedate = testdrivedate;
        }

        public String getTestdrivedate() {
            try {
                if (StringUtil.isNotNullAndWhiteSpace(testdrivedate) && testdrivedate.indexOf("T") >= 0) {
                    return FormatDate.dateToStr(FormatDate.strToTime(testdrivedate.replace("T", "")));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return testdrivedate;
        }

        public void setCityid(int cityid) {
            this.cityid = cityid;
        }

        public int getCityid() {
            return cityid;
        }

        public void setCityname(String cityname) {
            this.cityname = cityname;
        }

        public String getCityname() {
            return cityname;
        }

        public void setDealerinfoid(int dealerinfoid) {
            this.dealerinfoid = dealerinfoid;
        }

        public int getDealerinfoid() {
            return dealerinfoid;
        }

        public void setDealerinfoname(String dealerinfoname) {
            this.dealerinfoname = dealerinfoname;
        }

        public String getDealerinfoname() {
            return dealerinfoname;
        }

        public void setDealerlat(int dealerlat) {
            this.dealerlat = dealerlat;
        }

        public int getDealerlat() {
            return dealerlat;
        }

        public void setDealerlng(double dealerlng) {
            this.dealerlng = dealerlng;
        }

        public double getDealerlng() {
            return dealerlng;
        }

        public void setBrandid(int brandid) {
            this.brandid = brandid;
        }

        public int getBrandid() {
            return brandid;
        }

        public void setBrandname(String brandname) {
            this.brandname = brandname;
        }

        public String getBrandname() {
            return brandname;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setOrderstate(int orderstate) {
            this.orderstate = orderstate;
        }

        public int getOrderstate() {
            return orderstate;
        }

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public int getUserid() {
            return userid;
        }

        public void setIscomment(int iscomment) {
            this.iscomment = iscomment;
        }

        public int getIscomment() {
            return iscomment;
        }
    }
}
