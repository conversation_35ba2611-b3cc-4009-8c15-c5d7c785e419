package com.autohome.applet.model.dto.alipay;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;

@Data
public class OrderFreezeRequest {

    // 商户授权资金订单号。商家自定义需保证在商户端不重复。仅支持字母、数字、下划线。
    @NotBlank(message = "请输入outOrderNo")
    private String outOrderNo;
    // 商户本次资金操作的请求流水号，用于标示请求流水的唯一性。可与out_order_no相同，仅支持字母、数字、下划线。
    @NotBlank(message = "请输入outRequestNo")
    private String outRequestNo;
    // 收款账户的支付宝用户号
//    @NotBlank(message = "请输入payeeUserId")
//    private String payeeUserId;
    // 收款账户的支付宝登录号
//    @NotBlank(message = "请输入payeeLogonId")
//    private String payeeLogonId;
    // 需要冻结的金额，单位为：元（人民币），精确到小数点后两位
    @Digits(integer = 7, fraction = 2, message = "请输入正确金额")
    @DecimalMin(value = "0.01",message = "金额不得小于0元")
    private Double amount;

}
