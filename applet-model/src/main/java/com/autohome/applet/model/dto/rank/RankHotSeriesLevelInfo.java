package com.autohome.applet.model.dto.rank;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RankHotSeriesLevelInfo{
    private Integer levelId;
    private String levelName;
    private List<RankHotSeriesInfo> series;

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public List<RankHotSeriesInfo> getSeries() {
        return series;
    }

    public void setSeries(List<RankHotSeriesInfo> series) {
        this.series = series;
    }

    public static class RankHotSeriesInfo implements KoubeiScoreListable, SeriesInfoSetable {
        private Integer seriesId;
        private String seriesName;
        private Integer levelId;
        private Integer state;
        private Integer attNum;
        private Integer mixPrice;
        private Integer maxPrice;
        private String seriespnglogo;
        private Integer rank;
        private List<SeriesScoreList.Score> scoreList;

        @Override
        public int getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(Integer seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesName() {
            return seriesName;
        }

        @Override
        public void setSeriesName(String seriesName) {
            this.seriesName = seriesName;
        }

        public Integer getLevelId() {
            return levelId;
        }

        public void setLevelId(Integer levelId) {
            this.levelId = levelId;
        }

        public Integer getState() {
            return state;
        }

        public void setState(Integer state) {
            this.state = state;
        }

        public Integer getAttNum() {
            return attNum;
        }

        public void setAttNum(Integer attNum) {
            this.attNum = attNum;
        }

        public Integer getMixPrice() {
            return mixPrice;
        }

        public void setMixPrice(int mixPrice) {
            this.mixPrice = mixPrice;
        }

        @Override
        public void setMinPrice(int minPrice) {
            this.mixPrice = minPrice;
        }

        public Integer getMaxPrice() {
            return maxPrice;
        }

        @Override
        public void setMaxPrice(int maxPrice) {
            this.maxPrice = maxPrice;
        }

        public String getSeriespnglogo() {
            return seriespnglogo;
        }

        @Override
        public void setSeriespnglogo(String seriespnglogo) {
            this.seriespnglogo = seriespnglogo;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public List<SeriesScoreList.Score> getScoreList() {
            return scoreList;
        }

        @Override
        public void setScoreList(List<SeriesScoreList.Score> scoreList) {
            this.scoreList = scoreList;
        }

        @Override
        public void setBrandId(int brandId) {

        }
    }
}
