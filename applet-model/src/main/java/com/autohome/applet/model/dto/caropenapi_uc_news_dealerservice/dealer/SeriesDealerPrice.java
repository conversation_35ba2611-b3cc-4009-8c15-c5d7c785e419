package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by hanshanfeng on 2019/3/15.
 */
public class SeriesDealerPrice {
    @JsonProperty("seriesId")
    private int seriesid;
    @JsonProperty("specId")
    private int specid;
    @JsonProperty("seriesName")
    private String seriesname;
    @JsonProperty("specName")
    private String specname;
    @JsonProperty("seriesImageUrl")
    private String seriesimageurl;
    @JsonProperty("maxPriceOff")
    private int maxpriceoff;
    @JsonProperty("minOriginalPrice")
    private int minoriginalprice;
    @JsonProperty("maxOriginalPrice")
    private int maxoriginalprice;
    @JsonProperty("newsPrice")
    private int newsprice;
    @JsonProperty("cityId")
    private int cityid;
    @JsonProperty("brandId")
    private int brandid;
    @JsonProperty("brandName")
    private String brandname;
    @JsonProperty("manufactoryId")
    private int manufactoryid;
    @JsonProperty("manufactoryName")
    private String manufactoryname;

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public int getSpecid() {
        return specid;
    }

    public void setSpecid(int specid) {
        this.specid = specid;
    }

    public String getSeriesname() {
        return seriesname;
    }

    public void setSeriesname(String seriesname) {
        this.seriesname = seriesname;
    }

    public String getSpecname() {
        return specname;
    }

    public void setSpecname(String specname) {
        this.specname = specname;
    }

    public String getSeriesimageurl() {
        return seriesimageurl;
    }

    public void setSeriesimageurl(String seriesimageurl) {
        this.seriesimageurl = seriesimageurl;
    }

    public int getMaxpriceoff() {
        return maxpriceoff;
    }

    public void setMaxpriceoff(int maxpriceoff) {
        this.maxpriceoff = maxpriceoff;
    }

    public int getMinoriginalprice() {
        return minoriginalprice;
    }

    public void setMinoriginalprice(int minoriginalprice) {
        this.minoriginalprice = minoriginalprice;
    }

    public int getMaxoriginalprice() {
        return maxoriginalprice;
    }

    public void setMaxoriginalprice(int maxoriginalprice) {
        this.maxoriginalprice = maxoriginalprice;
    }

    public int getNewsprice() {
        return newsprice;
    }

    public void setNewsprice(int newsprice) {
        this.newsprice = newsprice;
    }

    public int getCityid() {
        return cityid;
    }

    public void setCityid(int cityid) {
        this.cityid = cityid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public int getManufactoryid() {
        return manufactoryid;
    }

    public void setManufactoryid(int manufactoryid) {
        this.manufactoryid = manufactoryid;
    }

    public String getManufactoryname() {
        return manufactoryname;
    }

    public void setManufactoryname(String manufactoryname) {
        this.manufactoryname = manufactoryname;
    }
}
