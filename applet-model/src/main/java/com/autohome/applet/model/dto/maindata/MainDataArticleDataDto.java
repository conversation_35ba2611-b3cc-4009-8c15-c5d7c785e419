package com.autohome.applet.model.dto.maindata;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class MainDataArticleDataDto {
    private Integer total;
    private List<ArticleData> items;

    @Data
    public static class ArticleData {
        /**
         * 作者名称
         */
        private  String author_name;
        /**
         * 文章标题
         */
        private  String title;
        /**
         * 文章内容
         */
        private  String content;
        /**
         * 发布时间
         */
        private String publish_time;


        /**
         * 文件id(素材上报)
         */
        private String fileId;


    }



}
