package com.autohome.applet.model.dto.carlibrary.carapi.out;

import com.autohome.applet.model.dto.carlibrary.carapi.CarApiBrand;
import com.autohome.applet.util.SecurityKit;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarApiBrandOut {

    private String id;
    private String name;
    private String logo;
    @JsonProperty("firstletter")
    private String firstLetter;
    private int rank;

    public static CarApiBrandOut by(CarApiBrand brand) {
        CarApiBrandOut out = new CarApiBrandOut();
        out.setId(SecurityKit.encrypt(brand.getId()));
        out.setName(brand.getName());
        out.setLogo(brand.getLogo());
        out.setFirstLetter(brand.getFirstLetter());
        out.setRank(brand.getRank());
        return out;
    }
}
