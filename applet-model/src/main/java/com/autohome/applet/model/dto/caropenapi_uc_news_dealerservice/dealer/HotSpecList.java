package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarSettings;
import com.autohome.applet.util.netcoreapi.StringUtil;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/25.
 */
public class HotSpecList {
    private int rowcount;
    private int pagecount;
    private int pageindex;
    private List<SpecList> list;

    public int getRowcount() {
        return rowcount;
    }

    public void setRowcount(int rowcount) {
        this.rowcount = rowcount;
    }

    public int getPagecount() {
        return pagecount;
    }

    public void setPagecount(int pagecount) {
        this.pagecount = pagecount;
    }

    public int getPageindex() {
        return pageindex;
    }

    public void setPageindex(int pageindex) {
        this.pageindex = pageindex;
    }

    public List<SpecList> getList() {
        return list;
    }

    public void setList(List<SpecList> list) {
        this.list = list;
    }

    public static class SpecList {

        private int specid;

        private String specname;

        private int seriesid;

        private String seriesname;

        private int price;

        private int maxprice;

        private int priceoff;

        private int newsid;

        private int equipcarid;

        private String imgurl;

        private int maxorigianlprice;

        private int minoriginalprice;

        private String seriesvrurl;

        private String shareimgurl;

        public String getShareimgurl() {
            if (StringUtil.isNotNullAndWhiteSpace(imgurl)) {
                return CarSettings.ConvertImgNew(imgurl, "400x320_").replace("http://", "https://");
            }
            return shareimgurl;
        }

        public void setShareimgurl(String shareimgurl) {
            this.shareimgurl = shareimgurl;
        }

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setPrice(int price) {
            this.price = price;
        }

        public int getPrice() {
            return price;
        }

        public void setMaxprice(int maxprice) {
            this.maxprice = maxprice;
        }

        public int getMaxprice() {
            return maxprice;
        }

        public void setPriceoff(int priceoff) {
            this.priceoff = priceoff;
        }

        public int getPriceoff() {
            return priceoff;
        }

        public void setNewsid(int newsid) {
            this.newsid = newsid;
        }

        public int getNewsid() {
            return newsid;
        }

        public void setEquipcarid(int equipcarid) {
            this.equipcarid = equipcarid;
        }

        public int getEquipcarid() {
            return equipcarid;
        }

        public void setImgurl(String imgurl) {
            this.imgurl = imgurl;
        }

        public String getImgurl() {
            if (StringUtil.isNotNullAndWhiteSpace(imgurl)) {
                return imgurl.replace("http://", "https://");
            }
            return imgurl;
        }

        public void setMaxorigianlprice(int maxorigianlprice) {
            this.maxorigianlprice = maxorigianlprice;
        }

        public int getMaxorigianlprice() {
            return maxorigianlprice;
        }

        public void setMinoriginalprice(int minoriginalprice) {
            this.minoriginalprice = minoriginalprice;
        }

        public int getMinoriginalprice() {
            return minoriginalprice;
        }

        public void setSeriesvrurl(String seriesvrurl) {
            this.seriesvrurl = seriesvrurl;
        }

        public String getSeriesvrurl() {
            return seriesvrurl;
        }

    }
}
