package com.autohome.applet.model.dto.cms;

import java.util.List;

public class ChejiahaoDetailResultArticle {

    private int autowriteinfoid;
    private String content;
    private String coverimage;
    private List<ChejiahaoDetailResultArticleImage> images;
    private long infoid;
    private String markdowncontent;
    private List<String> videoinforelation;

    public void setAutowriteinfoid(int autowriteinfoid) {
        this.autowriteinfoid = autowriteinfoid;
    }

    public int getAutowriteinfoid() {
        return autowriteinfoid;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setCoverimage(String coverimage) {
        this.coverimage = coverimage;
    }

    public String getCoverimage() {
        return coverimage;
    }

    public void setImages(List<ChejiahaoDetailResultArticleImage> images) {
        this.images = images;
    }

    public List<ChejiahaoDetailResultArticleImage> getImages() {
        return images;
    }

    public void setInfoid(long infoid) {
        this.infoid = infoid;
    }

    public long getInfoid() {
        return infoid;
    }

    public void setMarkdowncontent(String markdowncontent) {
        this.markdowncontent = markdowncontent;
    }

    public String getMarkdowncontent() {
        return markdowncontent;
    }

    public void setVideoinforelation(List<String> videoinforelation) {
        this.videoinforelation = videoinforelation;
    }

    public List<String> getVideoinforelation() {
        return videoinforelation;
    }

}