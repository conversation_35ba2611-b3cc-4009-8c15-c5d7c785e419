package com.autohome.applet.model.dto.alipay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class AlipayPublishVo {
    /**
     * 标题
     */
    private String source_title;
    /**
     *  作者
     */
    private String source_author;
    /**
     * 正文
     */
    private String source_text;
    /**
     * 摘要
     */
//    private String source_summary;
    /**
     * 内容类型
     * IMAGE-CONTENT：长图文
     */
    private String source_type = "IMAGE-CONTENT";
    /**
     * 发布时间
     * 仅支持  yyyy-MM-dd HH:mm:ss
     */
    private String source_publish_date;
    /**
     * 内容分发范围
     * whole - 所有人可见，part - 分发范围受限。
     */
    private String permission_status = "whole";
    /**
     * 图片和视频素材列表
     */
    private List<SourceMediaInfo> source_media_infos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SourceMediaInfo {
        /**
         * 素材ID
         */
        private String media_id;
        /**
         * 素材类型
         * image：图片 video：视频 cover_static：静态封面
         */
        private String media_type = "cover_static";
    }
}
