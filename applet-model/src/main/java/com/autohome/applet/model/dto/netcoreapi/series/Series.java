package com.autohome.applet.model.dto.netcoreapi.series;

import java.util.List;

public class Series {

    private int id;
    private int seriesRelationSeriesId;
    private String name;
    private long maxprice;
    private long minprice;
    private int state;
    private String seriesOfficialUrl;
    private int fctid;
    private String fctname;
    private int brandid;
    private String brandname;
    private List<String> structitems;
    private List<String> transmissionitems;
    private List<String> displacementitems;
    private int levelid;
    private String levelname;
    private List<String> picitems;
    private List<SeriesPicInfoitem> picinfoitems;
    private int specnum;
    private int sellspecnum;
    private int stopspecnum;
    private int waitspecnum;
    private int picnum;
    private int minfuelconsumption;
    private int maxfuelconsumption;
    private int isshow;
    private int paramisshow;
    private int existmaintain;
    private int showelectricparam;
    private List<Integer> electricmotormileage;
    private List<Integer> electricmotorkw;
    private String electricchargetime;
    private List<Integer> electricrongliang;
    private String createtime;
    private int containbookedspec;
    private double minoilwear;
    private double maxoilwear;
    private int containstopspec;
    private int newenergy;
    private String pnglogo;
    private String pricedescription;
    private String seriesplace;

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setSeriesRelationSeriesId(int seriesRelationSeriesId) {
        this.seriesRelationSeriesId = seriesRelationSeriesId;
    }

    public int getSeriesRelationSeriesId() {
        return seriesRelationSeriesId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setMaxprice(long maxprice) {
        this.maxprice = maxprice;
    }

    public long getMaxprice() {
        return maxprice;
    }

    public void setMinprice(long minprice) {
        this.minprice = minprice;
    }

    public long getMinprice() {
        return minprice;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setSeriesOfficialUrl(String seriesOfficialUrl) {
        this.seriesOfficialUrl = seriesOfficialUrl;
    }

    public String getSeriesOfficialUrl() {
        return seriesOfficialUrl;
    }

    public void setFctid(int fctid) {
        this.fctid = fctid;
    }

    public int getFctid() {
        return fctid;
    }

    public void setFctname(String fctname) {
        this.fctname = fctname;
    }

    public String getFctname() {
        return fctname;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setStructitems(List<String> structitems) {
        this.structitems = structitems;
    }

    public List<String> getStructitems() {
        return structitems;
    }

    public void setTransmissionitems(List<String> transmissionitems) {
        this.transmissionitems = transmissionitems;
    }

    public List<String> getTransmissionitems() {
        return transmissionitems;
    }

    public void setDisplacementitems(List<String> displacementitems) {
        this.displacementitems = displacementitems;
    }

    public List<String> getDisplacementitems() {
        return displacementitems;
    }

    public void setLevelid(int levelid) {
        this.levelid = levelid;
    }

    public int getLevelid() {
        return levelid;
    }

    public void setLevelname(String levelname) {
        this.levelname = levelname;
    }

    public String getLevelname() {
        return levelname;
    }

    public void setPicitems(List<String> picitems) {
        this.picitems = picitems;
    }

    public List<String> getPicitems() {
        return picitems;
    }

    public void setPicinfoitems(List<SeriesPicInfoitem> picinfoitems) {
        this.picinfoitems = picinfoitems;
    }

    public List<SeriesPicInfoitem> getPicinfoitems() {
        return picinfoitems;
    }

    public void setSpecnum(int specnum) {
        this.specnum = specnum;
    }

    public int getSpecnum() {
        return specnum;
    }

    public void setSellspecnum(int sellspecnum) {
        this.sellspecnum = sellspecnum;
    }

    public int getSellspecnum() {
        return sellspecnum;
    }

    public void setStopspecnum(int stopspecnum) {
        this.stopspecnum = stopspecnum;
    }

    public int getStopspecnum() {
        return stopspecnum;
    }

    public void setWaitspecnum(int waitspecnum) {
        this.waitspecnum = waitspecnum;
    }

    public int getWaitspecnum() {
        return waitspecnum;
    }

    public void setPicnum(int picnum) {
        this.picnum = picnum;
    }

    public int getPicnum() {
        return picnum;
    }

    public void setMinfuelconsumption(int minfuelconsumption) {
        this.minfuelconsumption = minfuelconsumption;
    }

    public int getMinfuelconsumption() {
        return minfuelconsumption;
    }

    public void setMaxfuelconsumption(int maxfuelconsumption) {
        this.maxfuelconsumption = maxfuelconsumption;
    }

    public int getMaxfuelconsumption() {
        return maxfuelconsumption;
    }

    public void setIsshow(int isshow) {
        this.isshow = isshow;
    }

    public int getIsshow() {
        return isshow;
    }

    public void setParamisshow(int paramisshow) {
        this.paramisshow = paramisshow;
    }

    public int getParamisshow() {
        return paramisshow;
    }

    public void setExistmaintain(int existmaintain) {
        this.existmaintain = existmaintain;
    }

    public int getExistmaintain() {
        return existmaintain;
    }

    public void setShowelectricparam(int showelectricparam) {
        this.showelectricparam = showelectricparam;
    }

    public int getShowelectricparam() {
        return showelectricparam;
    }

    public void setElectricmotormileage(List<Integer> electricmotormileage) {
        this.electricmotormileage = electricmotormileage;
    }

    public List<Integer> getElectricmotormileage() {
        return electricmotormileage;
    }

    public void setElectricmotorkw(List<Integer> electricmotorkw) {
        this.electricmotorkw = electricmotorkw;
    }

    public List<Integer> getElectricmotorkw() {
        return electricmotorkw;
    }

    public void setElectricchargetime(String electricchargetime) {
        this.electricchargetime = electricchargetime;
    }

    public String getElectricchargetime() {
        return electricchargetime;
    }

    public void setElectricrongliang(List<Integer> electricrongliang) {
        this.electricrongliang = electricrongliang;
    }

    public List<Integer> getElectricrongliang() {
        return electricrongliang;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setContainbookedspec(int containbookedspec) {
        this.containbookedspec = containbookedspec;
    }

    public int getContainbookedspec() {
        return containbookedspec;
    }

    public void setMinoilwear(double minoilwear) {
        this.minoilwear = minoilwear;
    }

    public double getMinoilwear() {
        return minoilwear;
    }

    public void setMaxoilwear(double maxoilwear) {
        this.maxoilwear = maxoilwear;
    }

    public double getMaxoilwear() {
        return maxoilwear;
    }

    public void setContainstopspec(int containstopspec) {
        this.containstopspec = containstopspec;
    }

    public int getContainstopspec() {
        return containstopspec;
    }

    public void setNewenergy(int newenergy) {
        this.newenergy = newenergy;
    }

    public int getNewenergy() {
        return newenergy;
    }

    public void setPnglogo(String pnglogo) {
        this.pnglogo = pnglogo;
    }

    public String getPnglogo() {
        return pnglogo;
    }

    public void setPricedescription(String pricedescription) {
        this.pricedescription = pricedescription;
    }

    public String getPricedescription() {
        return pricedescription;
    }

    public void setSeriesplace(String seriesplace) {
        this.seriesplace = seriesplace;
    }

    public String getSeriesplace() {
        return seriesplace;
    }

}