package com.autohome.applet.model.dto.netcoreapi.articleservice;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ActionSweetClientSlist {

    public int getSweetId() {
        return sweetId;
    }

    public void setSweetId(int sweetId) {
        this.sweetId = sweetId;
    }

    public String getSweetName() {
        return sweetName;
    }

    public void setSweetName(String sweetName) {
        this.sweetName = sweetName;
    }

    public int getClientType() {
        return clientType;
    }

    public void setClientType(int clientType) {
        this.clientType = clientType;
    }

    public String getJumpTo() {
        return jumpTo;
    }

    public void setJumpTo(String jumpTo) {
        this.jumpTo = jumpTo;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getOtherAppid() {
        return otherAppid;
    }

    public void setOtherAppid(String otherAppid) {
        this.otherAppid = otherAppid;
    }

    public String getReportPoint() {
        return reportPoint;
    }

    public void setReportPoint(String reportPoint) {
        this.reportPoint = reportPoint;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public List<String> getImglist() {
        return imglist;
    }

    public void setImglist(List<String> imglist) {
        this.imglist = imglist;
    }

    public String getShareDesc() {
        return shareDesc;
    }

    public void setShareDesc(String shareDesc) {
        this.shareDesc = shareDesc;
    }

    public String getShareImg() {
        return shareImg;
    }

    public void setShareImg(String shareImg) {
        this.shareImg = shareImg;
    }

    public String getShowPoint() {
        return showPoint;
    }

    public void setShowPoint(String showPoint) {
        this.showPoint = showPoint;
    }

    public int getNeedLogin() {
        return needLogin;
    }

    public void setNeedLogin(int needLogin) {
        this.needLogin = needLogin;
    }

    @JsonProperty("SweetId")
    private int sweetId;

    @JsonProperty("SweetName")
    private String sweetName;

    @JsonProperty("ClientType")
    private int clientType;

    @JsonProperty("JumpTo")
    private String jumpTo;

    @JsonProperty("Img")
    private String img;

    @JsonProperty("StartTime")
    private String startTime;

    @JsonProperty("EndTime")
    private String endTime;

    @JsonProperty("JumpType")
    private int jumpType;

    @JsonProperty("OtherAppid")
    private String otherAppid;

    //@JsonProperty("reportPoint")
    private String reportPoint;

    @JsonProperty("SortNum")
    private int sortNum;

    //@JsonProperty("imglist")
    private List<String> imglist;

    @JsonProperty("ShareDesc")
    private String shareDesc;

    @JsonProperty("ShareImg")
    private String shareImg;

    @JsonProperty("ShowPoint")
    private String showPoint;

    @JsonProperty("NeedLogin")
    private int needLogin;
}