package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by ha<PERSON><PERSON>feng on 2019/4/19.
 */
public class RescuePhoneAndDealer {

    //    @JsonProperty("dealerList")
//    private List<Dealerlist> dealerlist;
//    @JsonProperty("cityName")
//    private String cityname;
    @JsonProperty("rescuePhone")
    private Rescuephone rescuephone;
//    @JsonProperty("dealerCount")
//    private int dealercount;
//    public void setDealerlist(List<Dealerlist> dealerlist) {
//        this.dealerlist = dealerlist;
//    }
//    public List<Dealerlist> getDealerlist() {
//        return dealerlist;
//    }

//    public void setCityname(String cityname) {
//        this.cityname = cityname;
//    }
//    public String getCityname() {
//        return cityname;
//    }

    public void setRescuephone(Rescuephone rescuephone) {
        this.rescuephone = rescuephone;
    }

    public Rescuephone getRescuephone() {
        return rescuephone;
    }

    //    public void setDealercount(int dealercount) {
//        this.dealercount = dealercount;
//    }
//    public int getDealercount() {
//        return dealercount;
//    }
    public static class Dealerlist {

        private String address;
        @JsonProperty("headImage")
        private String headimage;
        private int id;
        private double latitude;
        private double longitude;
        private String name;
        private String phone;

        public void setAddress(String address) {
            this.address = address;
        }

        public String getAddress() {
            return address;
        }

        public void setHeadimage(String headimage) {
            this.headimage = headimage;
        }

        public String getHeadimage() {
            return headimage;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }

        public double getLatitude() {
            return latitude;
        }

        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }

        public double getLongitude() {
            return longitude;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getPhone() {
            return phone;
        }

    }

    public static class Rescuephone {

        private String phone;
        private String title;

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getPhone() {
            return phone;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitle() {
            return title;
        }

    }
}
