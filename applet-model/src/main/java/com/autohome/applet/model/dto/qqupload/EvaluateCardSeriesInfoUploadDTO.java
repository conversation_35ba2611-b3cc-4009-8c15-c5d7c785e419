package com.autohome.applet.model.dto.qqupload;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @description:
 * @author: WangBoWen
 * @date: 2024-05-23
 **/
@Data
public class EvaluateCardSeriesInfoUploadDTO {

    //车系名称
    private String key;

    private Display display;


    @Data
    public static class Display {

        //标题title(品牌名+车系名)，必填标签。
        private String title;

        //该车系点评跳转url，必填标签。(当前车系口碑综述页url)
        private String url;

        //该车系全部评论数，必填标签(当前车系口碑总条数)
        private String sum;

        //该车所属车系名称，多个别名之间用分号隔开，必填标签(车系名称)
        private String name;

        //品牌，别名用英文分号分隔，必填标签(车系对应品牌名称)
        private String brand;

        //厂商品牌，必填标签(车系对应的厂商名称)
        private String subBrand;

        //车系简称如a4，选填标签
//        private String simplename;

        //评分，不超过5，必填标签 口碑评分（0-5分）
        private String commScore;

        //车系展现图片地址，必填标签 (车系实拍图第一张)
        private String picsrc;

        private Comment comment;

        //该车系优点，多个优点用顿号分隔，必填标签。(当前车系最满意标签前5个)
        private String advantages;

        //该车系缺点，多个缺点用顿号分隔，必填标签。(当前车系最不满意标签前2个)
        private String disadvantages;
    }

    @Data
//    @JsonPropertyOrder(alphabetic = true)
    public static class Comment{

        //点评车款名称，必填标签 (口碑对应车型名称)
        @JsonProperty("CarName")
        private String carName;

        //该车款对应的点评链接，必填标签 (当前车型口碑对应详情页url)
        private String carLink;

        //最满意点评内容，必填标签 (每一个车型需要输出一条最满意口碑内容，输出该口碑全部内容，超过1000字节的内容不输出)
        private String commContent;

        //最不满意点评内容，必填标签 (每一个车型需要输出一条最不满意口碑内容，输出该口碑全部内容，超过1000字节的内)
        private String uncommContent;

        //该条评论详情链接，必填标签 (最满意/最不满意口碑内容详情页url)
        private String commLink;

        //该评论作者用户名，必填标签 (当前口碑用户名)
        private String author;

        //该作者头像地址，必填标签 (当前口碑用户头像图片地址)
        private String image;

        // 该评论发表日期，必填标签 (当前口碑发表日期（最后修改日期）)
        private String date;

        // 该评论的发表日期，不要短横线，排序用，必填标签 (当前口碑发表日期（最后修改日期）)
        private String rank;
    }
}
