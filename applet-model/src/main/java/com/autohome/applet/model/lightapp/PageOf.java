package com.autohome.applet.model.lightapp;

import java.util.List;

public class PageOf<T> {

    public PageOf(Long totalCount, List<T> items) {
        setTotalCount(totalCount);
        setItems(items);
    }

    public PageOf(){}

    /**
     * 总数
     */
    private Long totalCount;

    /**
     * 列表
     */
    private List<T> items;


    /**
     * 获取 总数
     *
     * @return totalCount 总数
     */
    public Long getTotalCount() {
        return this.totalCount;
    }

    /**
     * 设置 总数
     *
     * @param totalCount 总数
     */
    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * 获取 列表
     *
     * @return items 列表
     */
    public List<T> getItems() {
        return this.items;
    }

    /**
     * 设置 列表
     *
     * @param items 列表
     */
    public void setItems(List<T> items) {
        this.items = items;
    }
}
