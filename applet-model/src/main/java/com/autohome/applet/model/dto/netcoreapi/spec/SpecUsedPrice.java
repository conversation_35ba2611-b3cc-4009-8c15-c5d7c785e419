package com.autohome.applet.model.dto.netcoreapi.spec;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class SpecUsedPrice {

    @JsonProperty("title")
    private String title;

    @JsonProperty("subTitle")
    private String subtitle;

    @JsonProperty("addr")
    private String addr;

    @JsonProperty("minprice")
    private float minPrice;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public float getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(float minPrice) {
        this.minPrice = minPrice;
    }
}
