package com.autohome.applet.model.dto.rank;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RankElectricCarList {
    @JsonProperty("pageindex")
    private int pageIndex;

    @JsonProperty("pagecount")
    private int pageCount;

    @JsonProperty("pagesize")
    private int pageSize;

    @JsonProperty("saleranktip")
    private String saleRankTip;

    @JsonProperty("morescheme")
    private String moreScheme;

    @JsonProperty("scenetitle")
    private String sceneTitle;

    @JsonProperty("scenesubtitle")
    private String sceneSubtitle;

    @JsonProperty("list")
    private List<RankElectricCar> carList;

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSaleRankTip() {
        return saleRankTip;
    }

    public void setSaleRankTip(String saleRankTip) {
        this.saleRankTip = saleRankTip;
    }

    public String getMoreScheme() {
        return moreScheme;
    }

    public void setMoreScheme(String moreScheme) {
        this.moreScheme = moreScheme;
    }

    public String getSceneTitle() {
        return sceneTitle;
    }

    public void setSceneTitle(String sceneTitle) {
        this.sceneTitle = sceneTitle;
    }

    public String getSceneSubtitle() {
        return sceneSubtitle;
    }

    public void setSceneSubtitle(String sceneSubtitle) {
        this.sceneSubtitle = sceneSubtitle;
    }

    public List<RankElectricCar> getCarList() {
        return carList;
    }

    public void setCarList(List<RankElectricCar> carList) {
        this.carList = carList;
    }
}
