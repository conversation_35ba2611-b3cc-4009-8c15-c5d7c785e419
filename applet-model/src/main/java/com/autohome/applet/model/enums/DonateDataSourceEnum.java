package com.autohome.applet.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 捐赠数据来源
 * @createTime 2023-09-18 16:21:00
 */
public enum DonateDataSourceEnum {

    BEHAVIORHISTORY("0", "用户行为-原创+车家号"),//用户行为-原创+车家号
    BEHAVIORHISTORYSERIES("1", "用户行为-车系"), //用户行为-车系
    OPGC("2", "原创+车家号"), //原创+车家号
    DEFAULT("3", "固定排序页面"), //固定排序页面
    OVER("10", "结束")
    ;

    private final static Map<String, DonateDataSourceEnum> _CODES_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));
    private String code;
    private String info;

    private DonateDataSourceEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static DonateDataSourceEnum fromCode(String code) {
        return _CODES_MAP.getOrDefault(code, null);
    }

    public static int count(){
        int enumCount = DonateDataSourceEnum.values().length;
        return enumCount;
    }
}
