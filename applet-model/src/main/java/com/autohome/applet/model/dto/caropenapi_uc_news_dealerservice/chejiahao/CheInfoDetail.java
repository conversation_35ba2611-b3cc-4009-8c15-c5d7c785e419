package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chejiahao;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by ha<PERSON><PERSON><PERSON> on 2019/3/14.
 */
public class CheInfoDetail {
    private boolean isyouxuan;
    private String keywords;
    private int ident;
    private boolean recommendshowbigimg;
    private boolean isautowrite;
    private List<Relationcar> relationcar;
    private int hailiangstatus;
    private boolean isconverttext;
    private int authorid;
    private int infolevel;
    private String youxuantime;
    private boolean iswaitui;
    private boolean ispublish;
    private boolean recommedntoxny;
    private boolean istop;
    private long pageticks;
    private int showlevel;
    private boolean showimagelist;
    private int auditrecommend;
    private boolean ishot;
    private String image;
    private List<String> images;
    private int infoid;
    private String lastupdatetime;
    private boolean showbigimg;
    private Author author;
    private String autohomeua;
    private boolean hasreview;
    private String title2;
    private String title3;
    private String cityid;
    private String pushtitle;
    private Article article;
    private List<Tags> tags;
    private String statusnote;
    private String publishtime;
    private String pushdescription;
    private boolean isfirst;
    private int status;
    private String originaldescription;
    private int infotype;
    private int clienttype;
    private Infostatistics infostatistics;
    private int sorttype;
    private String pcurl;
    private String description;
    private String video;
    private String title;
    private String pageid;
    private String murl;
    private boolean recycled;
    private String audio;
    private String live;
    private String createtime;
    private List<Carmonads> carmonads;
    private boolean recommendtomoji;
    private int auditor;
    private boolean isbusiness;
    private boolean recommendtoseries;
    private String waituitime;
    private boolean isoriginal;
    private String newkeywords;
    private String newkeywordids;
    private String hailiangreason;
    private TextImage textimage;
    private String specids;
    private String seriesids;

    public String getSpecids() {
        if (this.carmonads != null && this.carmonads.size() > 0) {
            Set<String> specids = new HashSet<>();

            for (Carmonads carmonad : this.carmonads) {

                specids.add(Integer.toString(carmonad.getSpecid()));
            }
            if (specids != null && specids.size() > 0) {
                return String.join(",", specids);
            }
        }
        return specids;
    }

    public void setSpecids(String specids) {
        this.specids = specids;
    }

    public String getSeriesids() {
        if (this.carmonads != null && this.carmonads.size() > 0) {
            Set<String> seriesids = new HashSet<>();

            for (Carmonads carmonad : this.carmonads) {
                if (StringUtil.isBlank(carmonad.getOffprice()) || carmonad.getOffprice().equals("0")) {
                    seriesids.add(Integer.toString(carmonad.getSeriesid()));
                }

            }
            if (seriesids != null && seriesids.size() > 0) {
                return String.join(",", seriesids);
            }
        }
        return seriesids;
    }

    public void setSeriesids(String seriesids) {
        this.seriesids = seriesids;
    }

    public boolean isyouxuan() {
        return isyouxuan;
    }

    public boolean isRecommendshowbigimg() {
        return recommendshowbigimg;
    }

    public boolean isautowrite() {
        return isautowrite;
    }

    public boolean isconverttext() {
        return isconverttext;
    }

    public boolean iswaitui() {
        return iswaitui;
    }

    public boolean ispublish() {
        return ispublish;
    }

    public boolean isRecommedntoxny() {
        return recommedntoxny;
    }

    public boolean istop() {
        return istop;
    }

    public boolean isShowimagelist() {
        return showimagelist;
    }

    public boolean ishot() {
        return ishot;
    }

    public boolean isShowbigimg() {
        return showbigimg;
    }

    public boolean isHasreview() {
        return hasreview;
    }

    public boolean isfirst() {
        return isfirst;
    }

    public boolean isRecycled() {
        return recycled;
    }

    public boolean isRecommendtomoji() {
        return recommendtomoji;
    }

    public boolean isbusiness() {
        return isbusiness;
    }

    public boolean isRecommendtoseries() {
        return recommendtoseries;
    }

    public boolean isoriginal() {
        return isoriginal;
    }


    public void setIsyouxuan(boolean isyouxuan) {
        this.isyouxuan = isyouxuan;
    }

    public boolean getIsyouxuan() {
        return isyouxuan;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setIdent(int ident) {
        this.ident = ident;
    }

    public int getIdent() {
        return ident;
    }

    public void setRecommendshowbigimg(boolean recommendshowbigimg) {
        this.recommendshowbigimg = recommendshowbigimg;
    }

    public boolean getRecommendshowbigimg() {
        return recommendshowbigimg;
    }

    public void setIsautowrite(boolean isautowrite) {
        this.isautowrite = isautowrite;
    }

    public boolean getIsautowrite() {
        return isautowrite;
    }

    public void setRelationcar(List<Relationcar> relationcar) {
        this.relationcar = relationcar;
    }

    public List<Relationcar> getRelationcar() {
        return relationcar;
    }

    public void setHailiangstatus(int hailiangstatus) {
        this.hailiangstatus = hailiangstatus;
    }

    public int getHailiangstatus() {
        return hailiangstatus;
    }

    public void setIsconverttext(boolean isconverttext) {
        this.isconverttext = isconverttext;
    }

    public boolean getIsconverttext() {
        return isconverttext;
    }

    public void setAuthorid(int authorid) {
        this.authorid = authorid;
    }

    public int getAuthorid() {
        return authorid;
    }

    public void setInfolevel(int infolevel) {
        this.infolevel = infolevel;
    }

    public int getInfolevel() {
        return infolevel;
    }

    public void setYouxuantime(String youxuantime) {
        this.youxuantime = youxuantime;
    }

    public String getYouxuantime() {
        return youxuantime;
    }

    public void setIswaitui(boolean iswaitui) {
        this.iswaitui = iswaitui;
    }

    public boolean getIswaitui() {
        return iswaitui;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public boolean getIspublish() {
        return ispublish;
    }

    public void setRecommedntoxny(boolean recommedntoxny) {
        this.recommedntoxny = recommedntoxny;
    }

    public boolean getRecommedntoxny() {
        return recommedntoxny;
    }

    public void setIstop(boolean istop) {
        this.istop = istop;
    }

    public boolean getIstop() {
        return istop;
    }

    public void setPageticks(long pageticks) {
        this.pageticks = pageticks;
    }

    public long getPageticks() {
        return pageticks;
    }

    public void setShowlevel(int showlevel) {
        this.showlevel = showlevel;
    }

    public int getShowlevel() {
        return showlevel;
    }

    public void setShowimagelist(boolean showimagelist) {
        this.showimagelist = showimagelist;
    }

    public boolean getShowimagelist() {
        return showimagelist;
    }

    public void setAuditrecommend(int auditrecommend) {
        this.auditrecommend = auditrecommend;
    }

    public int getAuditrecommend() {
        return auditrecommend;
    }

    public void setIshot(boolean ishot) {
        this.ishot = ishot;
    }

    public boolean getIshot() {
        return ishot;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getImage() {
        return image;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<String> getImages() {
        return images;
    }

    public void setInfoid(int infoid) {
        this.infoid = infoid;
    }

    public int getInfoid() {
        return infoid;
    }

    public void setLastupdatetime(String lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    public String getLastupdatetime() {
        return lastupdatetime;
    }

    public void setShowbigimg(boolean showbigimg) {
        this.showbigimg = showbigimg;
    }

    public boolean getShowbigimg() {
        return showbigimg;
    }

    public void setAuthor(Author author) {
        this.author = author;
    }

    public Author getAuthor() {
        return author;
    }

    public void setAutohomeua(String autohomeua) {
        this.autohomeua = autohomeua;
    }

    public String getAutohomeua() {
        return autohomeua;
    }

    public void setHasreview(boolean hasreview) {
        this.hasreview = hasreview;
    }

    public boolean getHasreview() {
        return hasreview;
    }

    public void setTitle2(String title2) {
        this.title2 = title2;
    }

    public String getTitle2() {
        return title2;
    }

    public void setTitle3(String title3) {
        this.title3 = title3;
    }

    public String getTitle3() {
        return title3;
    }

    public void setCityid(String cityid) {
        this.cityid = cityid;
    }

    public String getCityid() {
        return cityid;
    }

    public void setPushtitle(String pushtitle) {
        this.pushtitle = pushtitle;
    }

    public String getPushtitle() {
        return pushtitle;
    }

    public void setArticle(Article article) {
        this.article = article;
    }

    public Article getArticle() {
        return article;
    }

    public void setTags(List<Tags> tags) {
        this.tags = tags;
    }

    public List<Tags> getTags() {
        return tags;
    }

    public void setStatusnote(String statusnote) {
        this.statusnote = statusnote;
    }

    public String getStatusnote() {
        return statusnote;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getPublishtime() {
        if (StringUtil.isNotNullAndWhiteSpace(this.publishtime)) {
            return FormatDate.netToJavaTime(this.publishtime);
        }
        return publishtime;
    }

    public void setPushdescription(String pushdescription) {
        this.pushdescription = pushdescription;
    }

    public String getPushdescription() {
        return pushdescription;
    }

    public void setIsfirst(boolean isfirst) {
        this.isfirst = isfirst;
    }

    public boolean getIsfirst() {
        return isfirst;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setOriginaldescription(String originaldescription) {
        this.originaldescription = originaldescription;
    }

    public String getOriginaldescription() {
        return originaldescription;
    }

    public void setInfotype(int infotype) {
        this.infotype = infotype;
    }

    public int getInfotype() {
        return infotype;
    }

    public void setClienttype(int clienttype) {
        this.clienttype = clienttype;
    }

    public int getClienttype() {
        return clienttype;
    }

    public void setInfostatistics(Infostatistics infostatistics) {
        this.infostatistics = infostatistics;
    }

    public Infostatistics getInfostatistics() {
        return infostatistics;
    }

    public void setSorttype(int sorttype) {
        this.sorttype = sorttype;
    }

    public int getSorttype() {
        return sorttype;
    }

    public void setPcurl(String pcurl) {
        this.pcurl = pcurl;
    }

    public String getPcurl() {
        return pcurl;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setVideo(String video) {
        this.video = video;
    }

    public String getVideo() {
        return video;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setMurl(String murl) {
        this.murl = murl;
    }

    public String getMurl() {
        return murl;
    }

    public void setRecycled(boolean recycled) {
        this.recycled = recycled;
    }

    public boolean getRecycled() {
        return recycled;
    }

    public void setAudio(String audio) {
        this.audio = audio;
    }

    public String getAudio() {
        return audio;
    }

    public void setLive(String live) {
        this.live = live;
    }

    public String getLive() {
        return live;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCarmonads(List<Carmonads> carmonads) {
        this.carmonads = carmonads;
    }

    public List<Carmonads> getCarmonads() {
        return carmonads;
    }

    public void setRecommendtomoji(boolean recommendtomoji) {
        this.recommendtomoji = recommendtomoji;
    }

    public boolean getRecommendtomoji() {
        return recommendtomoji;
    }

    public void setAuditor(int auditor) {
        this.auditor = auditor;
    }

    public int getAuditor() {
        return auditor;
    }

    public void setIsbusiness(boolean isbusiness) {
        this.isbusiness = isbusiness;
    }

    public boolean getIsbusiness() {
        return isbusiness;
    }

    public void setRecommendtoseries(boolean recommendtoseries) {
        this.recommendtoseries = recommendtoseries;
    }

    public boolean getRecommendtoseries() {
        return recommendtoseries;
    }

    public void setWaituitime(String waituitime) {
        this.waituitime = waituitime;
    }

    public String getWaituitime() {
        return waituitime;
    }

    public void setIsoriginal(boolean isoriginal) {
        this.isoriginal = isoriginal;
    }

    public boolean getIsoriginal() {
        return isoriginal;
    }

    public void setNewkeywords(String newkeywords) {
        this.newkeywords = newkeywords;
    }

    public String getNewkeywords() {
        return newkeywords;
    }

    public void setNewkeywordids(String newkeywordids) {
        this.newkeywordids = newkeywordids;
    }

    public String getNewkeywordids() {
        return newkeywordids;
    }

    public void setHailiangreason(String hailiangreason) {
        this.hailiangreason = hailiangreason;
    }

    public String getHailiangreason() {
        return hailiangreason;
    }

    public void setTextimage(TextImage textimage) {
        this.textimage = textimage;
    }

    public TextImage getTextimage() {
        return textimage;
    }

    public static class Article {
        private String coverimage;
        private List<Images> images;
        private int infoid;
        private int autowriteinfoid;
        private String content;
        private String markdowncontent;

        //        private List<String> videoinforelation;
        public void setCoverimage(String coverimage) {
            this.coverimage = coverimage;
        }

        public String getCoverimage() {
            return coverimage;
        }

        public void setImages(List<Images> images) {
            this.images = images;
        }

        public List<Images> getImages() {
            return images;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setAutowriteinfoid(int autowriteinfoid) {
            this.autowriteinfoid = autowriteinfoid;
        }

        public int getAutowriteinfoid() {
            return autowriteinfoid;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getContent() {
            return content;
        }

        public void setMarkdowncontent(String markdowncontent) {
            this.markdowncontent = markdowncontent;
        }

        public String getMarkdowncontent() {
            return markdowncontent;
        }

//        public void setVideoinforelation(List<String> videoinforelation) {
//            this.videoinforelation = videoinforelation;
//        }
//        public List<String> getVideoinforelation() {
//            return videoinforelation;
//        }
    }

    public static class Images {

        private int articleimageid;
        private int infoid;
        private String imageurl;
        private int width;
        private int sercarid;
        private int height;

        public void setArticleimageid(int articleimageid) {
            this.articleimageid = articleimageid;
        }

        public int getArticleimageid() {
            return articleimageid;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setImageurl(String imageurl) {
            this.imageurl = imageurl;
        }

        public String getImageurl() {
            return imageurl;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public int getWidth() {
            return width;
        }

        public void setSercarid(int sercarid) {
            this.sercarid = sercarid;
        }

        public int getSercarid() {
            return sercarid;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public int getHeight() {
            return height;
        }

    }

    public static class TextImage {

        private List<String> images;
        private int infoid;
        private String text;

        public void setImages(List<String> images) {
            this.images = images;
        }

        public List<String> getImages() {
            return images;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }

    }

    public static class Relationcar {

        private int specid;
        private String createtime;
        private int infoid;
        private String lastupdatetime;
        private int factoryid;
        private int appearcount;
        private String specname;
        private String yearname;
        private String brandname;
        private int seriesid;
        private int yearid;
        private boolean isfrommachine;
        private String seriesname;
        private int brandid;
        private int infocarrelationid;
        private String factoryname;
        private int status;

        public boolean isfrommachine() {
            return isfrommachine;
        }

        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setCreatetime(String createtime) {
            this.createtime = createtime;
        }

        public String getCreatetime() {
            return createtime;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setLastupdatetime(String lastupdatetime) {
            this.lastupdatetime = lastupdatetime;
        }

        public String getLastupdatetime() {
            return lastupdatetime;
        }

        public void setFactoryid(int factoryid) {
            this.factoryid = factoryid;
        }

        public int getFactoryid() {
            return factoryid;
        }

        public void setAppearcount(int appearcount) {
            this.appearcount = appearcount;
        }

        public int getAppearcount() {
            return appearcount;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setYearname(String yearname) {
            this.yearname = yearname;
        }

        public String getYearname() {
            return yearname;
        }

        public void setBrandname(String brandname) {
            this.brandname = brandname;
        }

        public String getBrandname() {
            return brandname;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setYearid(int yearid) {
            this.yearid = yearid;
        }

        public int getYearid() {
            return yearid;
        }

        public void setIsfrommachine(boolean isfrommachine) {
            this.isfrommachine = isfrommachine;
        }

        public boolean getIsfrommachine() {
            return isfrommachine;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setBrandid(int brandid) {
            this.brandid = brandid;
        }

        public int getBrandid() {
            return brandid;
        }

        public void setInfocarrelationid(int infocarrelationid) {
            this.infocarrelationid = infocarrelationid;
        }

        public int getInfocarrelationid() {
            return infocarrelationid;
        }

        public void setFactoryname(String factoryname) {
            this.factoryname = factoryname;
        }

        public String getFactoryname() {
            return factoryname;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }

    }

    public static class Statistics {

        private int articlereplycount;
        private int questioncount;
        private int articlepv;
        private int fanscount;
        private int userid;
        private int followcount;
        private int articleuv;
        private int articlecount;

        public void setArticlereplycount(int articlereplycount) {
            this.articlereplycount = articlereplycount;
        }

        public int getArticlereplycount() {
            return articlereplycount;
        }

        public void setQuestioncount(int questioncount) {
            this.questioncount = questioncount;
        }

        public int getQuestioncount() {
            return questioncount;
        }

        public void setArticlepv(int articlepv) {
            this.articlepv = articlepv;
        }

        public int getArticlepv() {
            return articlepv;
        }

        public void setFanscount(int fanscount) {
            this.fanscount = fanscount;
        }

        public int getFanscount() {
            return fanscount;
        }

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public int getUserid() {
            return userid;
        }

        public void setFollowcount(int followcount) {
            this.followcount = followcount;
        }

        public int getFollowcount() {
            return followcount;
        }

        public void setArticleuv(int articleuv) {
            this.articleuv = articleuv;
        }

        public int getArticleuv() {
            return articleuv;
        }

        public void setArticlecount(int articlecount) {
            this.articlecount = articlecount;
        }

        public int getArticlecount() {
            return articlecount;
        }

    }

    public static class Author {

        private String vtype;
        private int creditscore;
        private boolean isautowrite;
        private boolean isjointask;
        private boolean isshielded;
        private int recommendtype;
        private String pageid;
        private int activedegree;
        private int userid;
        private boolean enabled;
        private String jointaskdate;
        private int infolevel;
        private String vdescription;
        private boolean isjoinadplan;
        private String inforecommendleveltime;
        private String vtypedes;
        private String subscribetime;
        private String nickname;
        private String wechatid;
        private String joinadplandate;
        private boolean allowbigfile;
        private String userextend2;
        private String vremark;
        private List<String> businesstag;
        private String introduction;
        private String email;
        private boolean isaloneplay;
        private String createtime;
        private String relationname;
        private String mobile;
        private String auditor;
        private boolean iswhitelist;
        private String avatar;
        private String vcrid;
        private int inforecommendlevel;
        private String coverimage;
        private String usertitle;
        private int userlevel;
        private String recommendtime;
        private String jointime;
        private String statusdes;
        private int status;
        private Statistics statistics;
        private String authoric;

        public String getAuthoric() {
            return "https://chejiahao.autohome.com.cn/images/v/<EMAIL>";
        }

        public void setAuthoric(String authoric) {
            this.authoric = authoric;
        }

        public void setVtype(String vtype) {
            this.vtype = vtype;
        }

        public String getVtype() {
            return vtype;
        }

        public void setCreditscore(int creditscore) {
            this.creditscore = creditscore;
        }

        public int getCreditscore() {
            return creditscore;
        }

        public void setIsautowrite(boolean isautowrite) {
            this.isautowrite = isautowrite;
        }

        public boolean getIsautowrite() {
            return isautowrite;
        }

        public void setIsjointask(boolean isjointask) {
            this.isjointask = isjointask;
        }

        public boolean getIsjointask() {
            return isjointask;
        }

        public void setIsshielded(boolean isshielded) {
            this.isshielded = isshielded;
        }

        public boolean getIsshielded() {
            return isshielded;
        }

        public void setRecommendtype(int recommendtype) {
            this.recommendtype = recommendtype;
        }

        public int getRecommendtype() {
            return recommendtype;
        }

        public void setPageid(String pageid) {
            this.pageid = pageid;
        }

        public String getPageid() {
            return pageid;
        }

        public void setActivedegree(int activedegree) {
            this.activedegree = activedegree;
        }

        public int getActivedegree() {
            return activedegree;
        }

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public int getUserid() {
            return userid;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean getEnabled() {
            return enabled;
        }

        public void setJointaskdate(String jointaskdate) {
            this.jointaskdate = jointaskdate;
        }

        public String getJointaskdate() {
            return jointaskdate;
        }

        public void setInfolevel(int infolevel) {
            this.infolevel = infolevel;
        }

        public int getInfolevel() {
            return infolevel;
        }

        public void setVdescription(String vdescription) {
            this.vdescription = vdescription;
        }

        public String getVdescription() {
            return vdescription;
        }

        public void setIsjoinadplan(boolean isjoinadplan) {
            this.isjoinadplan = isjoinadplan;
        }

        public boolean getIsjoinadplan() {
            return isjoinadplan;
        }

        public void setInforecommendleveltime(String inforecommendleveltime) {
            this.inforecommendleveltime = inforecommendleveltime;
        }

        public String getInforecommendleveltime() {
            return inforecommendleveltime;
        }

        public void setVtypedes(String vtypedes) {
            this.vtypedes = vtypedes;
        }

        public String getVtypedes() {
            return vtypedes;
        }

        public void setSubscribetime(String subscribetime) {
            this.subscribetime = subscribetime;
        }

        public String getSubscribetime() {
            return subscribetime;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getNickname() {
            return nickname;
        }

        public void setWechatid(String wechatid) {
            this.wechatid = wechatid;
        }

        public String getWechatid() {
            return wechatid;
        }

        public void setJoinadplandate(String joinadplandate) {
            this.joinadplandate = joinadplandate;
        }

        public String getJoinadplandate() {
            return joinadplandate;
        }

        public void setAllowbigfile(boolean allowbigfile) {
            this.allowbigfile = allowbigfile;
        }

        public boolean getAllowbigfile() {
            return allowbigfile;
        }

        public void setUserextend2(String userextend2) {
            this.userextend2 = userextend2;
        }

        public String getUserextend2() {
            return userextend2;
        }

        public void setVremark(String vremark) {
            this.vremark = vremark;
        }

        public String getVremark() {
            return vremark;
        }

        public void setBusinesstag(List<String> businesstag) {
            this.businesstag = businesstag;
        }

        public String getBusinesstag() {
            if (businesstag == null || businesstag.size() == 0) {
                return "[]";
            }

            return "[" + String.join(",", businesstag) + "]";
        }

        public void setIntroduction(String introduction) {
            this.introduction = introduction;
        }

        public String getIntroduction() {
            return introduction;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getEmail() {
            return email;
        }

        public void setIsaloneplay(boolean isaloneplay) {
            this.isaloneplay = isaloneplay;
        }

        public boolean getIsaloneplay() {
            return isaloneplay;
        }

        public void setCreatetime(String createtime) {
            this.createtime = createtime;
        }

        public String getCreatetime() {
            return createtime;
        }

        public void setRelationname(String relationname) {
            this.relationname = relationname;
        }

        public String getRelationname() {
            return relationname;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getMobile() {
            return mobile;
        }

        public void setAuditor(String auditor) {
            this.auditor = auditor;
        }

        public String getAuditor() {
            return auditor;
        }

        public void setIswhitelist(boolean iswhitelist) {
            this.iswhitelist = iswhitelist;
        }

        public boolean getIswhitelist() {
            return iswhitelist;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setVcrid(String vcrid) {
            this.vcrid = vcrid;
        }

        public String getVcrid() {
            return vcrid;
        }

        public void setInforecommendlevel(int inforecommendlevel) {
            this.inforecommendlevel = inforecommendlevel;
        }

        public int getInforecommendlevel() {
            return inforecommendlevel;
        }

        public void setCoverimage(String coverimage) {
            this.coverimage = coverimage;
        }

        public String getCoverimage() {
            return coverimage;
        }

        public void setUsertitle(String usertitle) {
            this.usertitle = usertitle;
        }

        public String getUsertitle() {
            return usertitle;
        }

        public void setUserlevel(int userlevel) {
            this.userlevel = userlevel;
        }

        public int getUserlevel() {
            return userlevel;
        }

        public void setRecommendtime(String recommendtime) {
            this.recommendtime = recommendtime;
        }

        public String getRecommendtime() {
            return recommendtime;
        }

        public void setJointime(String jointime) {
            this.jointime = jointime;
        }

        public String getJointime() {
            return jointime;
        }

        public void setStatusdes(String statusdes) {
            this.statusdes = statusdes;
        }

        public String getStatusdes() {
            return statusdes;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }

        public void setStatistics(Statistics statistics) {
            this.statistics = statistics;
        }

        public Statistics getStatistics() {
            return statistics;
        }

    }

    public static class Tags {

        private boolean isshow;
        private int navorder;
        private String tagname;
        private boolean isnav;
        private int tagid;
        private int order;

        public void setIsshow(boolean isshow) {
            this.isshow = isshow;
        }

        public boolean getIsshow() {
            return isshow;
        }

        public void setNavorder(int navorder) {
            this.navorder = navorder;
        }

        public int getNavorder() {
            return navorder;
        }

        public void setTagname(String tagname) {
            this.tagname = tagname;
        }

        public String getTagname() {
            return tagname;
        }

        public void setIsnav(boolean isnav) {
            this.isnav = isnav;
        }

        public boolean getIsnav() {
            return isnav;
        }

        public void setTagid(int tagid) {
            this.tagid = tagid;
        }

        public int getTagid() {
            return tagid;
        }

        public void setOrder(int order) {
            this.order = order;
        }

        public int getOrder() {
            return order;
        }

    }

    public static class Infostatistics {

        private int vv;
        private int uv;
        private int infoid;
        private int pv;
        private int epv;
        private int praisecount;
        private int imageclickcount;
        private String pageid;
        private int playduration;
        private int detailpv;
        private int sharecount;
        private int playcount;
        private int favoritecount;
        private int replycount;

        public void setVv(int vv) {
            this.vv = vv;
        }

        public int getVv() {
            return vv;
        }

        public void setUv(int uv) {
            this.uv = uv;
        }

        public int getUv() {
            return uv;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setPv(int pv) {
            this.pv = pv;
        }

        public int getPv() {
            return pv;
        }

        public void setEpv(int epv) {
            this.epv = epv;
        }

        public int getEpv() {
            return epv;
        }

        public void setPraisecount(int praisecount) {
            this.praisecount = praisecount;
        }

        public int getPraisecount() {
            return praisecount;
        }

        public void setImageclickcount(int imageclickcount) {
            this.imageclickcount = imageclickcount;
        }

        public int getImageclickcount() {
            return imageclickcount;
        }

        public void setPageid(String pageid) {
            this.pageid = pageid;
        }

        public String getPageid() {
            return pageid;
        }

        public void setPlayduration(int playduration) {
            this.playduration = playduration;
        }

        public int getPlayduration() {
            return playduration;
        }

        public void setDetailpv(int detailpv) {
            this.detailpv = detailpv;
        }

        public int getDetailpv() {
            return detailpv;
        }

        public void setSharecount(int sharecount) {
            this.sharecount = sharecount;
        }

        public int getSharecount() {
            return sharecount;
        }

        public void setPlaycount(int playcount) {
            this.playcount = playcount;
        }

        public int getPlaycount() {
            return playcount;
        }

        public void setFavoritecount(int favoritecount) {
            this.favoritecount = favoritecount;
        }

        public int getFavoritecount() {
            return favoritecount;
        }

        public void setReplycount(int replycount) {
            this.replycount = replycount;
        }

        public int getReplycount() {
            return replycount;
        }

    }

    public static class Resondtos {

        private int carmonadid;
        private String reason;
        private int infoid;
        private String recommentdes;
        private int createuserid;
        private int id;
        @JsonProperty("created_stime")
        private String createdStime;
        private String recommentcode;

        public void setCarmonadid(int carmonadid) {
            this.carmonadid = carmonadid;
        }

        public int getCarmonadid() {
            return carmonadid;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public String getReason() {
            return reason;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setRecommentdes(String recommentdes) {
            this.recommentdes = recommentdes;
        }

        public String getRecommentdes() {
            return recommentdes;
        }

        public void setCreateuserid(int createuserid) {
            this.createuserid = createuserid;
        }

        public int getCreateuserid() {
            return createuserid;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public void setCreatedStime(String createdStime) {
            this.createdStime = createdStime;
        }

        public String getCreatedStime() {
            return createdStime;
        }

        public void setRecommentcode(String recommentcode) {
            this.recommentcode = recommentcode;
        }

        public String getRecommentcode() {
            return recommentcode;
        }

    }

    public static class Carmonads {

        private int specid;
        private String img;
        private int infoid;
        private String recommendreason;
        private int maxprice;
        private String specname;
        private int sort;
        private String brandname;
        private int seriesid;
        private List<Resondtos> resondtos;
        private String seriesname;
        private int score;
        private int minprice;
        private int brandid;
        private int id;
        private String fctprice;
        private String dealerprice;
        //最高优惠钱（优惠一万二）
        private String offprice;

        public String getOffprice() {
            return offprice;
        }

        public void setOffprice(String offprice) {
            this.offprice = offprice;
        }

        public String getFctprice() {
            if (StringUtil.isBlank(this.fctprice)) {
                return CarPriceUtils.GetSpecPrice(minprice, maxprice);
            }
            return fctprice;
        }

        public void setFctprice(String fctprice) {
            this.fctprice = fctprice;
        }

        public String getDealerprice() {
            return dealerprice;
        }

        public void setDealerprice(String dealerprice) {
            this.dealerprice = dealerprice;
        }


        public void setSpecid(int specid) {
            this.specid = specid;
        }

        public int getSpecid() {
            return specid;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getImg() {
            return img;
        }

        public void setInfoid(int infoid) {
            this.infoid = infoid;
        }

        public int getInfoid() {
            return infoid;
        }

        public void setRecommendreason(String recommendreason) {
            this.recommendreason = recommendreason;
        }

        public String getRecommendreason() {
            return recommendreason;
        }

        public void setMaxprice(int maxprice) {
            this.maxprice = maxprice;
        }

        public int getMaxprice() {
            return maxprice;
        }

        public void setSpecname(String specname) {
            this.specname = specname;
        }

        public String getSpecname() {
            return specname;
        }

        public void setSort(int sort) {
            this.sort = sort;
        }

        public int getSort() {
            return sort;
        }

        public void setBrandname(String brandname) {
            this.brandname = brandname;
        }

        public String getBrandname() {
            return brandname;
        }

        public void setSeriesid(int seriesid) {
            this.seriesid = seriesid;
        }

        public int getSeriesid() {
            return seriesid;
        }

        public void setResondtos(List<Resondtos> resondtos) {
            this.resondtos = resondtos;
        }

        public List<Resondtos> getResondtos() {
            return resondtos;
        }

        public void setSeriesname(String seriesname) {
            this.seriesname = seriesname;
        }

        public String getSeriesname() {
            return seriesname;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public int getScore() {
            return score;
        }

        public void setMinprice(int minprice) {
            this.minprice = minprice;
        }

        public int getMinprice() {
            return minprice;
        }

        public void setBrandid(int brandid) {
            this.brandid = brandid;
        }

        public int getBrandid() {
            return brandid;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

    }
}
