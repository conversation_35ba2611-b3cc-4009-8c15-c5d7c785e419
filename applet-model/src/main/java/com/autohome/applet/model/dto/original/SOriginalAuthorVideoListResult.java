package com.autohome.applet.model.dto.original;

import java.util.List;

/**
 * 原创视频作者视频列表
 * <AUTHOR>
 * @date 2021/05/13 12:37
 **/
public class SOriginalAuthorVideoListResult {

    private Integer returncode = 0;
    private String message = "";
    private String cdntime = "㊣CDNTIME㊣";
    private String codistime = "㊣CODISTIME㊣";
    private String codisver = "㊣CODISVER㊣";
    private String costtime = "㊣COSTTIME㊣";

    private Result result;

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    public static class Result {

        private List<SOriginalVideoEntity> items;

        private int totalcount;

        /**
         * 是否能上拉加载
         */
        private Boolean hasupmore;

        /**
         * 是否能下拉刷新
         */
        private Boolean hasdownmore;

        public List<SOriginalVideoEntity> getItems() {
            return items;
        }

        public void setItems(List<SOriginalVideoEntity> items) {
            this.items = items;
        }

        public int getTotalcount() {
            return totalcount;
        }

        public void setTotalcount(int totalcount) {
            this.totalcount = totalcount;
        }

        public Boolean getHasupmore() {
            return hasupmore;
        }

        public void setHasupmore(Boolean hasupmore) {
            this.hasupmore = hasupmore;
        }

        public Boolean getHasdownmore() {
            return hasdownmore;
        }

        public void setHasdownmore(Boolean hasdownmore) {
            this.hasdownmore = hasdownmore;
        }
    }
}
