package com.autohome.applet.model.dto.netcoreapi.output;

import com.autohome.applet.model.dto.netcoreapi.cms.CmsArticleSerializes;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetArticleDetailByIdPage {
    private int endPage;

    private int start;

    private int titletype;

    private long id;

    private String title;

    private String img;

    private int authorid;

    private String authorname;

    private String avatar;

    private String publishtime;

    private String content;

    private int class1;

    private String class1name;

    private int replycount;

    private int clickcount;

    private int likecount;

    private List<Object> images;

    private List<String> titleList;

    private int contentCount;

    private int lookcount;

    private String seriesIds;

    private int votePkid;

    private int pkid;

    private List<Integer> voids;

    private List<Integer> pkids;

    private boolean ispublish;

    private List<CmsArticleSerializes> ArticleSerializes;

    private String thumbnailimg;

    private String newkeywords;

    private String description;

    private String webUrl;

    private long memberId;

    private String showKeywords;

    private String editProvinceName;

    public int getEndPage() {
        return endPage;
    }

    public void setEndPage(int endPage) {
        this.endPage = endPage;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getTitletype() {
        return titletype;
    }

    public void setTitletype(int titletype) {
        this.titletype = titletype;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public int getAuthorid() {
        return authorid;
    }

    public void setAuthorid(int authorid) {
        this.authorid = authorid;
    }

    public String getAuthorname() {
        return authorname;
    }

    public void setAuthorname(String authorname) {
        this.authorname = authorname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getPublishtime() {
        return publishtime;
    }

    public void setPublishtime(String publishtime) {
        this.publishtime = publishtime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getClass1() {
        return class1;
    }

    public void setClass1(int class1) {
        this.class1 = class1;
    }

    public String getClass1name() {
        return class1name;
    }

    public void setClass1name(String class1name) {
        this.class1name = class1name;
    }

    public int getReplycount() {
        return replycount;
    }

    public void setReplycount(int replycount) {
        this.replycount = replycount;
    }

    public int getClickcount() {
        return clickcount;
    }

    public void setClickcount(int clickcount) {
        this.clickcount = clickcount;
    }

    public int getLikecount() {
        return likecount;
    }

    public void setLikecount(int likecount) {
        this.likecount = likecount;
    }

    public List<String> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public int getContentCount() {
        return contentCount;
    }

    public void setContentCount(int contentCount) {
        this.contentCount = contentCount;
    }

    public int getLookcount() {
        return lookcount;
    }

    public void setLookcount(int lookcount) {
        this.lookcount = lookcount;
    }

    public String getSeriesIds() {
        return seriesIds;
    }

    public void setSeriesIds(String seriesIds) {
        this.seriesIds = seriesIds;
    }

    public int getVotePkid() {
        return votePkid;
    }

    public void setVotePkid(int votePkid) {
        this.votePkid = votePkid;
    }

    public int getPkid() {
        return pkid;
    }

    public void setPkid(int pkid) {
        this.pkid = pkid;
    }

    public List<Integer> getVoids() {
        return voids;
    }

    public void setVoids(List<Integer> voids) {
        this.voids = voids;
    }

    public List<Integer> getPkids() {
        return pkids;
    }

    public void setPkids(List<Integer> pkids) {
        this.pkids = pkids;
    }

    public boolean isIspublish() {
        return ispublish;
    }

    public void setIspublish(boolean ispublish) {
        this.ispublish = ispublish;
    }

    public List<CmsArticleSerializes> getArticleSerializes() {
        return ArticleSerializes;
    }

    public void setArticleSerializes(List<CmsArticleSerializes> articleSerializes) {
        ArticleSerializes = articleSerializes;
    }

    public String getThumbnailimg() {
        return thumbnailimg;
    }

    public void setThumbnailimg(String thumbnailimg) {
        this.thumbnailimg = thumbnailimg;
    }

    public String getNewkeywords() {
        return newkeywords;
    }

    public void setNewkeywords(String newkeywords) {
        this.newkeywords = newkeywords;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public long getMemberId() {
        return memberId;
    }

    public void setMemberId(long memberId) {
        this.memberId = memberId;
    }

    public String getShowKeywords() {
        return showKeywords;
    }

    public void setShowKeywords(String showKeywords) {
        this.showKeywords = showKeywords;
    }

    public String getEditProvinceName() {
        return editProvinceName;
    }

    public void setEditProvinceName(String editProvinceName) {
        this.editProvinceName = editProvinceName;
    }
}