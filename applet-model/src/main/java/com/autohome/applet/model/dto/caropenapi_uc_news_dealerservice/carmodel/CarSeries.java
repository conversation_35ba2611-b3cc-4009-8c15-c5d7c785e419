package com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel;

import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarSettings;
import com.autohome.applet.util.netcoreapi.StringUtil;

import java.util.List;

/**
 * Created by jingliangliang on 2018/4/19.
 */
public class CarSeries {
    private int carnum;

    private int brandid;

    private String brandname;

    private String brandlogo;

    private int seriesid;

    private String name;

    private String level;

    private String pic;

    private int minprice;

    private String minpriceStr;

    private int maxprice;

    private String carprice;

    private String insightCount;
    private boolean seriesSelect;
    private String seriespnglogo;
    private String shareseriespnglogo;

    private int picallnum;

    private int type;

    private List<Integer> typeIds;

    public String getMinpriceStr() {
        return minpriceStr;
    }

    public void setMinpriceStr(String minpriceStr) {
        this.minpriceStr = minpriceStr;
    }

    public int getPicallnum() {
        return picallnum;
    }

    public void setPicallnum(int picallnum) {
        this.picallnum = picallnum;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getBrandlogo() {
        return brandlogo;
    }

    public void setBrandlogo(String brandlogo) {
        this.brandlogo = brandlogo;
    }

    public List<Integer> getTypeIds() {
        return typeIds;
    }

    public void setTypeIds(List<Integer> typeIds) {
        this.typeIds = typeIds;
    }

    public String getShareseriespnglogo() {
        if (StringUtil.isNotNullAndWhiteSpace(seriespnglogo)) {
            return CarSettings.ConvertImgNew(seriespnglogo, "400x320_");
        }
        return shareseriespnglogo;
    }

    public void setShareseriespnglogo(String shareseriespnglogo) {
        this.shareseriespnglogo = shareseriespnglogo;
    }

    public String getSeriespnglogo() {
        return seriespnglogo;
    }

    public void setSeriespnglogo(String seriespnglogo) {
        this.seriespnglogo = seriespnglogo;
    }

    public String getInsightCount() {
        return insightCount;
    }

    public void setInsightCount(String insightCount) {
        this.insightCount = insightCount;
    }

    public boolean isSeriesSelect() {
        return seriesSelect;
    }

    public void setSeriesSelect(boolean seriesSelect) {
        this.seriesSelect = seriesSelect;
    }

    public int getSeriesid() {
        return seriesid;
    }

    public void setSeriesid(int seriesid) {
        this.seriesid = seriesid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getCarprice() {
        return CarPriceUtils.GetSpecPrice(minprice, maxprice);
    }

    public void setCarprice(String carprice) {
        this.carprice = carprice;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public int getMinprice() {
        return minprice;
    }

    public void setMinprice(int minprice) {
        this.minprice = minprice;
    }

    public int getMaxprice() {
        return maxprice;
    }

    public void setMaxprice(int maxprice) {
        this.maxprice = maxprice;
    }

    public int getCarnum() {
        return carnum;
    }

    public void setCarnum(int carnum) {
        this.carnum = carnum;
    }

    public int getBrandid() {
        return brandid;
    }

    public void setBrandid(int brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }
}
