package com.autohome.applet.model.dto.resourcefeedpush;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushResourceFeedDataDTO {
    private Long id;

    private String bizId;

    private String bizType;

    private String publishTime;

    private Date createdStime;

    private Date modifiedStime;

    private String title;

    private String author;

    private String authorId;

    private String imgUrl;

    private String modifyTime;

    private String summary;

    private String replyCount;

    private String viewCount;

    private String isDelete;

    private String content;

    private String subjectId;

    private String cmsSeriesIds;

    private String cmsContentClass;

    private String cmsTags;

    private String categoryTags;

    private String recommendTime;

    private String duration;

    private String graphicImgList;

    private String updateAt;

    private String authorIcon;

    private String vId;

    private String cmsSeriesNames;

    private String nlpTagsChoose2;

    private String isCloseComment;

    private String graphicImgList3;

    /**
     * 点赞数
     * wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=209799341
     * */
    private String likeCount;
}
