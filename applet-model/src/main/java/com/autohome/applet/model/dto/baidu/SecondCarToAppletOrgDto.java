package com.autohome.applet.model.dto.baidu;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecondCarToAppletOrgDto {
    private String url;
    @JsonAlias({"orgId"})
    private String org_id;
    @JsonAlias({"orgName"})
    private String org_name;
    @JsonAlias({"branchName"})
    private String branch_name;
    @JsonAlias({"orgIcon"})
    private String org_icon;
    @JsonAlias({"orgScore"})
    private BigDecimal org_score;
    @JsonAlias({"orgCommentnum"})
    private int org_commentnum;
    @JsonAlias({"orgAvgprice"})
    private int org_avgprice;
    @JsonAlias({"orgArea"})
    private String org_area;
    @JsonAlias({"orgProvince"})
    private String org_province;
    @JsonAlias({"orgCity"})
    private String org_city;
    @JsonAlias({"orgDistrict"})
    private String org_district;
    private String lng;
    private String lat;
    @JsonAlias({"orgCommentdetail"})
    private String org_commentdetail;
    @JsonAlias({"orgServicetab"})
    private String org_servicetab;
    @JsonAlias({"orgYear"})
    private int org_year;
    @JsonAlias({"orgCase"})
    private int org_case;
    @JsonAlias({"orgServicecount"})
    private int org_servicecount;
    @JsonAlias({"orgBrand"})
    private String org_brand;
    @JsonAlias({"orgCategory"})
    private String org_category;
    @JsonAlias({"orgUrl"})
    private String org_url;
    @JsonAlias({"orgWorktime"})
    private String org_worktime;
    @JsonAlias({"orgWorkday"})
    private String org_workday;
    @JsonAlias({"orgAddress"})
    private String org_address;
    @JsonAlias({"orgTel"})
    private String org_tel;
    @JsonAlias({"orgSourcename"})
    private String org_sourcename;
    @JsonAlias({"orgGift"})
    private String org_gift;
    @JsonAlias({"orgPackage"})
    private String org_package;
    /**
     * org_status: 门店状态，门店状态(1/0，1-状态正常（含门店下班后打烊），0-状态不正常（如门店下线)
     * */
    @JsonAlias({"orgStatus"})
    private int org_status;
    @JsonAlias({"orgVerticalpic"})
    private String org_verticalpic;
    @JsonAlias({"orgDescription"})
    private String org_description;
    private String version;
    private String siteid;
    private List<SecondCarToAppletProductDto> service_result;
    //由于二手车门店没有小程序版本,所以此字段先注释掉
    //private SecondCarToAppletXcxParamsDto xcx_params;

    public String getOrg_id() {
        return StringUtils.isEmpty(org_id) ? "" : org_id;
    }

    public String getOrg_name() {
        return StringUtils.isEmpty(org_name) ? "" : org_name;
    }

    public String getBranch_name() {
        return StringUtils.isEmpty(branch_name) ? "" : branch_name;
    }

    public String getOrg_icon() {
        return StringUtils.isEmpty(org_icon) ? "" : org_icon;
    }

    public BigDecimal getOrg_score() {
        return org_score;
    }

    public int getOrg_commentnum() {
        return org_commentnum;
    }

    public int getOrg_avgprice() {
        return org_avgprice;
    }

    public String getOrg_area() {
        return StringUtils.isEmpty(org_area) ? "" : org_area;
    }

    public String getOrg_province() {
        return StringUtils.isEmpty(org_province) ? "" : org_province;
    }

    public String getOrg_city() {
        return StringUtils.isEmpty(org_city) ? "" : org_city;
    }

    public String getOrg_district() {
        return StringUtils.isEmpty(org_district) ? "" : org_district;
    }

    public String getLng() {
        return StringUtils.isEmpty(lng) ? "" : lng;
    }

    public String getLat() {
        return StringUtils.isEmpty(lat) ? "" : lat;
    }

    public String getOrg_commentdetail() {
        return StringUtils.isEmpty(org_commentdetail) ? "" : org_commentdetail;
    }

    public String getOrg_servicetab() {
        return StringUtils.isEmpty(org_servicetab) ? "" : org_servicetab;
    }

    public int getOrg_year() {
        return org_year;
    }

    public int getOrg_case() {
        return org_case;
    }

    public int getOrg_servicecount() {
        return org_servicecount;
    }

    public String getOrg_brand() {
        return StringUtils.isEmpty(org_brand) ? "" : org_brand;
    }

    public String getOrg_category() {
        return StringUtils.isEmpty(org_category) ? "" : org_category;
    }

    public String getOrg_url() {
        return StringUtils.isEmpty(org_url) ? "" : org_url;
    }

    public String getOrg_worktime() {
        return StringUtils.isEmpty(org_worktime) ? "" : org_worktime;
    }

    public String getOrg_workday() {
        return StringUtils.isEmpty(org_workday) ? "" : org_workday;
    }

    public String getOrg_address() {
        return StringUtils.isEmpty(org_address) ? "" : org_address;
    }

    public String getOrg_tel() {
        return StringUtils.isEmpty(org_tel) ? "" : org_tel;
    }

    public String getOrg_sourcename() {
        return StringUtils.isEmpty(org_sourcename) ? "" : org_sourcename;
    }

    public String getOrg_gift() {
        return StringUtils.isEmpty(org_gift) ? "" : org_gift;
    }

    public String getOrg_package() {
        return StringUtils.isEmpty(org_package) ? "" : org_package;
    }

    public int getOrg_status() {
        return org_status;
    }

    public String getOrg_verticalpic() {
        return StringUtils.isEmpty(org_verticalpic) ? "" : org_verticalpic;
    }

    public String getOrg_description() {
        return StringUtils.isEmpty(org_description) ? "" : org_description;
    }

    public String getVersion() {
        return version;
    }

    public static String getTestSecondCarToAppletOrgDto(){
        return "{\n" +
                "        \"url\": \"http://baidu.com/\",\n" +
                "        \"org_name\": \"铂爵旅拍\",\n" +
                "        \"branch_name\": \"陶然居店\",\n" +
                "        \"org_icon\": \"https://img2.baidu.com/it/u=2537370952\",\n" +
                "        \"org_score\": 4.5,\n" +
                "        \"org_commentnum\": 138,\n" +
                "        \"org_avgprice\": 234.2,\n" +
                "        \"org_area\": \"望京\",\n" +
                "        \"org_province\": \"广东\",\n" +
                "        \"org_city\": \"厦门\",\n" +
                "        \"org_district\": \"海淀区\",\n" +
                "        \"lng\": \"113.58762\",\n" +
                "        \"lat\": \"37.86236\",\n" +
                "        \"org_commentdetail\": \"这家店整体很满意，服务很不错，下次还会再来\",\n" +
                "        \"org_servicetab\": \"环境优雅 免费wifi 面积10000㎡\",\n" +
                "        \"org_year\": 2022,\n" +
                "        \"org_case\": 5,\n" +
                "        \"org_servicecount\": 230,\n" +
                "        \"org_brand\": \"铂爵旅拍\",    \n" +
                "        \"org_category\": \"摄影婚庆_婚礼策划\",\n" +
                "        \"org_url\": \"https://localsite.baidu.com/okam/pages/site/index?bidword=%E6%95%99%E8%82%B2\",\n" +
                "        \"org_worktime\": \"9:00-17:00\",\n" +
                "        \"org_workday\": \"周一至周日 全天 or 周一至周五\",\n" +
                "        \"org_address\": \"北京市海淀区xxxx\",\n" +
                "        \"org_tel\": \"137xxxxxxxx\",\n" +
                "        \"org_sourcename\": \"大众点评\",\n" +
                "        \"siteid\": \"6378648\",\n" +
                "        \"org_id\": \"26874826864\",\n" +
                "        \"org_gift\": \"到店豪礼试吃月子套餐\",\n" +
                "        \"org_package\": \"优惠放送中 试吃月子套餐\",\n" +
                "        \"org_status\": 1,\n" +
                "        \"org_verticalpic\": \"http://xxx.com/xxx.jpg\",\n" +
                "        \"org_description\": \"喜悦会是一家母婴健康服务连锁品牌，四城10家门店。九大核心服务体系...\"" +
                "    }";
    }
}