package com.autohome.applet.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description MainDataType与BizType匹配
 */
public enum MainDataTypeToBizTypeEnum {
    CMS("1", "cms", "原创文章","detailpackage/pages/article/index?type=0&articleid=%s"),//文章
    CHEJIAHAO12("12", "chejiahao", "车家号长文","detailpackage/pages/article/index?type=4&articleid=%s"), //车家号长文
    CHEJIAHAO13("13", "chejiahao", "车家号短文","detailpackage/pages/lighttext/index?objectid=%s"), //车家号短文
    CLUB5("5", "club", "论坛帖子","detailpackage/pages/club/index?id=%s"), //帖子
    CLUB33("33", "club", "论坛帖子精选","detailpackage/pages/club/index?id=%s"), //帖子精选
    CLUB74("74", "club", "论坛问答",""), //论坛-问答
    CMS700112("700112", "club", "原创轻文",""), //原创轻文
    KOUBEI("11", "koubei", "口碑",""), //口碑
    CARPRICE("36", "jiage", "车主价格",""), //车主价格
    HANGQING("100", "hangqing", "行情",""), //王万钊接口
    WENDA("101", "wenda", "问答",""), //王万钊接口
    BULIANG("110", "buliang", "补量程序",""), //不足8万补量
    DEFAULT("0", "",  "","")
    ;

    private final static Map<String, MainDataTypeToBizTypeEnum> _CODES_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));


    private String code;
    private String value;
    private String des;
    private String siteMap;

    private MainDataTypeToBizTypeEnum(String code, String value, String des, String siteMap) {
        this.code = code;
        this.value = value;
        this.des = des;
        this.siteMap = siteMap;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
    public String getSiteMap() {
        return siteMap;
    }

    public static MainDataTypeToBizTypeEnum fromCode(String bizType) {
        return _CODES_MAP.getOrDefault(String.valueOf(bizType), MainDataTypeToBizTypeEnum.DEFAULT);
    }
}
