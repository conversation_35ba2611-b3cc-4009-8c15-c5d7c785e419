/**
 * 2022年9月29日
 */
package com.autohome.applet.model.enums;

import java.util.Objects;

/**
 * 定点投放平台模块ID
 *
 */
public enum OpsPmModuleEnum {
    
    ORIGINAL_AUTOTEST(18, "原创频道-之家实测"),
    ORIGINAL_VIPCOLUMN(19, "原创频道-重磅栏目"),
    ORIGINAL_ORICOLUMN(21, "原创频道-原创栏目模块"),
    ORIGINAL_STAREDITOR(20, "原创频道-重磅栏目");

    /** 代码 */
    private int value;

    /** 描述 */
    private String describe;

    OpsPmModuleEnum(int value, String describe) {
        this.value = value;
        this.describe = describe;
    }

    public static OpsPmModuleEnum getEnumByValue(Integer value) {
        for (OpsPmModuleEnum obj : OpsPmModuleEnum.values()) {
            if (Objects.equals(obj.getValue(), value)) {
                return obj;
            }
        }
        return null;
    }

    public static String getEnumDescribeByValue(Integer value) {
        OpsPmModuleEnum typeEnum = OpsPmModuleEnum.getEnumByValue(value);
        return typeEnum != null ? typeEnum.getDescribe() : "";
    }

    public int getValue() {
        return value;
    }

    public String getDescribe() {
        return describe;
    }

}
