package com.autohome.applet.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 捐赠内容类型
 * @createTime 2023-09-18 16:21:00
 */
public enum DonateViewMarkEnum {
    SERIES("01", "车系"),
    CMS("06", "原创-文章详情页"),
    VIDEO("07", "原创-视频详情页"),
    CHEJIAHAO("08", "车家号"),
//    CHEJIAHAOARTICLE("08", "车家号-文章/长文"),
//    CHEJIAHAOTEXTIMAGE("09", "车家号-图文/轻文"),
//    CHEJIAHAOVIDEO("10", "车家号-视频"),
    ;

    private final static Map<String, DonateViewMarkEnum> _CODES_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));


    private String code;
    private String info;

    private DonateViewMarkEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static DonateViewMarkEnum fromCode(int code) {
        return _CODES_MAP.getOrDefault(code, null);
    }


}
