package com.autohome.applet.model.dto.netcoreapi.cms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CmsArticle {

    private String appcoverimages;
    private String appimg;
    private List<CmsArticleSerializes> ArticleSerializes;
    private String author;
    private int authorId;
    private int autoshowid;
    private String Avator;
    private String brandIds;
    private int cityId;
    private int class1;
    private String class1Name;
    private int class2;
    private String class2Name;
    private int clickCount;
    private String content;
    private String contentKeywords;
    private int countyId;
    private String coverimages;
    private int dealerId;
    private String description;
    private String dir;
    private String editIP;
    private String EditorAvatar;
    private String EditorPosition;
    private String editPositionId;
    private String editProvinceId;
    private String editProvinceName;
    private boolean FromCache;
    private int Gender;
    private long id;
    private int ImageCount;
    private List<Object> Images;
    private String img;
    private boolean isclosecomment;
    private boolean IsPublish;
    private boolean isSerializeOver;
    private long MemberId;
    private String MobileMemo;
    private String motorSeriesIds;
    private int pageCount;
    private int pageIndex;
    private List<CmsArticleTitle> pageTitles;
    private int provinceId;
    private String publishTime;
    private int replyCount;
    private int SecondCount;
    private int serializeOrders;
    private int seriesId;
    private String seriesIds;
    private String shortTitle;
    private String source;
    private String specIds;
    private String summary;
    private String title;
    private int totalCount;
    private String type;
    private String updatetime;
    private String webUrl;
    private int WordCount;

    public void setAppcoverimages(String appcoverimages) {
        this.appcoverimages = appcoverimages;
    }

    public String getAppcoverimages() {
        return appcoverimages;
    }

    public void setAppimg(String appimg) {
        this.appimg = appimg;
    }

    public String getAppimg() {
        return appimg;
    }

    public void setArticleSerializes(List<CmsArticleSerializes> ArticleSerializes) {
        this.ArticleSerializes = ArticleSerializes;
    }

    public List<CmsArticleSerializes> getArticleSerializes() {
        return ArticleSerializes;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthorId(int authorId) {
        this.authorId = authorId;
    }

    public int getAuthorId() {
        return authorId;
    }

    public void setAutoshowid(int autoshowid) {
        this.autoshowid = autoshowid;
    }

    public int getAutoshowid() {
        return autoshowid;
    }

    public void setAvator(String Avator) {
        this.Avator = Avator;
    }

    public String getAvator() {
        return Avator;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public int getCityId() {
        return cityId;
    }

    public void setClass1(int class1) {
        this.class1 = class1;
    }

    public int getClass1() {
        return class1;
    }

    public void setClass1Name(String class1Name) {
        this.class1Name = class1Name;
    }

    public String getClass1Name() {
        return class1Name;
    }

    public void setClass2(int class2) {
        this.class2 = class2;
    }

    public int getClass2() {
        return class2;
    }

    public void setClass2Name(String class2Name) {
        this.class2Name = class2Name;
    }

    public String getClass2Name() {
        return class2Name;
    }

    public void setClickCount(int clickCount) {
        this.clickCount = clickCount;
    }

    public int getClickCount() {
        return clickCount;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setContentKeywords(String contentKeywords) {
        this.contentKeywords = contentKeywords;
    }

    public String getContentKeywords() {
        return contentKeywords;
    }

    public void setCountyId(int countyId) {
        this.countyId = countyId;
    }

    public int getCountyId() {
        return countyId;
    }

    public void setCoverimages(String coverimages) {
        this.coverimages = coverimages;
    }

    public String getCoverimages() {
        return coverimages;
    }

    public void setDealerId(int dealerId) {
        this.dealerId = dealerId;
    }

    public int getDealerId() {
        return dealerId;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getDir() {
        return dir;
    }

    public void setEditIP(String editIP) {
        this.editIP = editIP;
    }

    public String getEditIP() {
        return editIP;
    }

    public void setEditorAvatar(String EditorAvatar) {
        this.EditorAvatar = EditorAvatar;
    }

    public String getEditorAvatar() {
        return EditorAvatar;
    }

    public void setEditorPosition(String EditorPosition) {
        this.EditorPosition = EditorPosition;
    }

    public String getEditorPosition() {
        return EditorPosition;
    }

    public void setEditPositionId(String editPositionId) {
        this.editPositionId = editPositionId;
    }

    public String getEditPositionId() {
        return editPositionId;
    }

    public void setEditProvinceId(String editProvinceId) {
        this.editProvinceId = editProvinceId;
    }

    public String getEditProvinceId() {
        return editProvinceId;
    }

    public void setEditProvinceName(String editProvinceName) {
        this.editProvinceName = editProvinceName;
    }

    public String getEditProvinceName() {
        return editProvinceName;
    }

    public void setFromCache(boolean FromCache) {
        this.FromCache = FromCache;
    }

    public boolean getFromCache() {
        return FromCache;
    }

    public void setGender(int Gender) {
        this.Gender = Gender;
    }

    public int getGender() {
        return Gender;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getId() {
        return id;
    }

    public void setImageCount(int ImageCount) {
        this.ImageCount = ImageCount;
    }

    public int getImageCount() {
        return ImageCount;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getImg() {
        return img;
    }

    public void setIsclosecomment(boolean isclosecomment) {
        this.isclosecomment = isclosecomment;
    }

    public boolean getIsclosecomment() {
        return isclosecomment;
    }

    public void setIsPublish(boolean IsPublish) {
        this.IsPublish = IsPublish;
    }

    public boolean getIsPublish() {
        return IsPublish;
    }

    public void setIsSerializeOver(boolean isSerializeOver) {
        this.isSerializeOver = isSerializeOver;
    }

    public boolean getIsSerializeOver() {
        return isSerializeOver;
    }

    public void setMemberId(long MemberId) {
        this.MemberId = MemberId;
    }

    public long getMemberId() {
        return MemberId;
    }

    public void setMobileMemo(String MobileMemo) {
        this.MobileMemo = MobileMemo;
    }

    public String getMobileMemo() {
        return MobileMemo;
    }

    public void setMotorSeriesIds(String motorSeriesIds) {
        this.motorSeriesIds = motorSeriesIds;
    }

    public String getMotorSeriesIds() {
        return motorSeriesIds;
    }

    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageTitles(List<CmsArticleTitle> pageTitles) {
        this.pageTitles = pageTitles;
    }

    public List<CmsArticleTitle> getPageTitles() {
        return pageTitles;
    }

    public void setProvinceId(int provinceId) {
        this.provinceId = provinceId;
    }

    public int getProvinceId() {
        return provinceId;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setReplyCount(int replyCount) {
        this.replyCount = replyCount;
    }

    public int getReplyCount() {
        return replyCount;
    }

    public void setSecondCount(int SecondCount) {
        this.SecondCount = SecondCount;
    }

    public int getSecondCount() {
        return SecondCount;
    }

    public void setSerializeOrders(int serializeOrders) {
        this.serializeOrders = serializeOrders;
    }

    public int getSerializeOrders() {
        return serializeOrders;
    }

    public void setSeriesId(int seriesId) {
        this.seriesId = seriesId;
    }

    public int getSeriesId() {
        return seriesId;
    }

    public void setSeriesIds(String seriesIds) {
        this.seriesIds = seriesIds;
    }

    public String getSeriesIds() {
        return seriesIds;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setSpecIds(String specIds) {
        this.specIds = specIds;
    }

    public String getSpecIds() {
        return specIds;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getSummary() {
        return summary;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWordCount(int WordCount) {
        this.WordCount = WordCount;
    }

    public int getWordCount() {
        return WordCount;
    }

}