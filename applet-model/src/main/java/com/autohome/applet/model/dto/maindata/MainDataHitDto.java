package com.autohome.applet.model.dto.maindata;


import java.util.List;

public class MainDataHitDto<T> {
    private int total;
    private String max_score;
    /**
     * 返回泛型
     * */
    private List<MainDataSourceDto<T>> hits;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getMax_score() {
        return max_score;
    }

    public void setMax_score(String max_score) {
        this.max_score = max_score;
    }

    public List<MainDataSourceDto<T>> getHits() {
        return hits;
    }

    public void setHits(List<MainDataSourceDto<T>> hits) {
        this.hits = hits;
    }
}
