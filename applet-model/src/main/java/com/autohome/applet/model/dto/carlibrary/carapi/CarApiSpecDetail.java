package com.autohome.applet.model.dto.carlibrary.carapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarApiSpecDetail {
    @JsonProperty("specid")
    private long specId;

    @JsonProperty("specname")
    private String specName;

    @JsonProperty("seriesid")
    private int seriesId;

    @JsonProperty("seriesname")
    private String seriesName;

    @JsonProperty("brandid")
    private int brandId;

    @JsonProperty("brandname")
    private String brandName;

    @JsonProperty("fctid")
    private int fctId;

    @JsonProperty("fctname")
    private String fctName;

    @JsonProperty("levelid")
    private int levelId;

    @JsonProperty("levelname")
    private String levelName;

    @JsonProperty("specpicount")
    private int specPiCount;

    @JsonProperty("speclogo")
    private String specLogo;

    @JsonProperty("specminprice")
    private int specMinPrice;

    @JsonProperty("specmaxprice")
    private int specMaxPrice;

    @JsonProperty("specengineid")
    private int specEngineId;

    @JsonProperty("specenginename")
    private String specEngineName;

    @JsonProperty("specstructuredoor")
    private int specStructureDoor;

    @JsonProperty("specstructureseat")
    private String specStructureSeat;

    @JsonProperty("specstructuretypename")
    private String specStructureTypeName;

    @JsonProperty("spectransmission")
    private String specTransmission;

    @JsonProperty("specstate")
    private int specState;

    @JsonProperty("specoiloffical")
    private int specOilOfficial;

    @JsonProperty("speclength")
    private int specLength;

    @JsonProperty("specwidth")
    private int specWidth;

    @JsonProperty("specheight")
    private int specHeight;

    @JsonProperty("specweight")
    private int specWeight;

    @JsonProperty("specdrivingmodename")
    private String specDrivingModeName;

//    1:"自然吸气"
//    2:"涡轮增压"
//    3:"机械增压"
//    4:"机械+涡轮增压"
//    5:"双涡轮增压"
//    6:"三涡轮增压"
//    7:"四涡轮增压"
//    8:"双机械增压"
//    9:"涡轮增压+电动增压"
    @JsonProperty("specflowmodeid")
    private int specFlowModeId;

    @JsonProperty("specflowmodename")
    private String specFlowModeName;

    @JsonProperty("specdisplacement")
    private double specDisplacement;

    @JsonProperty("specenginepower")
    private int specEnginePower;

    @JsonProperty("specparamisshow")
    private int specParamIsShow;

    @JsonProperty("specispreferential")
    private int specIsPreferential;

    @JsonProperty("specistaxrelief")
    private int specIsTaxRelief;

    @JsonProperty("specistaxexemption")
    private int specIsTaxExemption;

    @JsonProperty("specquality")
    private String specQuality;

    @JsonProperty("specisimport")
    private String specIsImport;

    @JsonProperty("specisbooked")
    private boolean specIsBooked;

    @JsonProperty("dynamicprice")
    private String dynamicPrice;

    @JsonProperty("oilboxvolume")
    private int oilBoxVolume;

    @JsonProperty("fueltype")
    private int fuelType;

    @JsonProperty("fastchargetime")
    private String fastChargeTime;

    @JsonProperty("slowchargetime")
    private String slowChargeTime;

    @JsonProperty("fastchargepercent")
    private String fastChargePercent;

    @JsonProperty("batterycapacity")
    private String batteryCapacity;

    @JsonProperty("mile")
    private String mile;

    @JsonProperty("fueltypedetail")
    private int fuelTypeDetail;

    @JsonProperty("fueltypename")
    private String fuelTypeName;

    @JsonProperty("greenstandards")
    private String greenStandards;

    @JsonProperty("enginetorque")
    private String engineTorque;

    @JsonProperty("engingkw")
    private String engingKw;

    @JsonProperty("qrcode")
    private String qrCode;

    @JsonProperty("pricedescription")
    private String priceDescription;

    @JsonProperty("electricmotorgrosspower")
    private String electricMotorGrossPower;

    @JsonProperty("electricmotorgrosstorque")
    private String electricMotorGrossTorque;

    @JsonProperty("oillabel")
    private String oilLabel;


}
