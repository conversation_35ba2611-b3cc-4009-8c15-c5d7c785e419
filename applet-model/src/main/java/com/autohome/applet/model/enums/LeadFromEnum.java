package com.autohome.applet.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 销售线索来源 1-画像 2-商业线索
 * @createTime 2023-10-18 16:21:00
 */
public enum LeadFromEnum {
    PORTRAIT(1, "画像"),//
    BUSINESS(2, "商业线索"), //标准经销商
    PORTRAITANDHOT(3, "用户偏好+热门车"), //标准经销商
    ;

    private final static Map<Integer, LeadFromEnum> _CODES_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));


    private Integer code;
    private String info;

    private LeadFromEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static LeadFromEnum fromCode(int code) {
        return _CODES_MAP.getOrDefault(code, LeadFromEnum.PORTRAIT);
    }
}
