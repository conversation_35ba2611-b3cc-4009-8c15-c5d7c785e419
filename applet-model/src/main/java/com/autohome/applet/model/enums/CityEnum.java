package com.autohome.applet.model.enums;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

public enum CityEnum {
    // 直辖市
    BEIJING("110100", "北京", "110000", "北京"),
    TIANJIN("120100", "天津", "120000", "天津"),
    SHANGHAI("310100", "上海", "310000", "上海"),
    CHONGQING("500100", "重庆", "500000", "重庆"),

    // 河北省（冀）
    SHIJIAZHUANG("130100", "石家庄", "130000", "河北"),
    TANGSHAN("130200", "唐山", "130000", "河北"),
    QINHUANGDAO("130300", "秦皇岛", "130000", "河北"),
    HANDAN("130400", "邯郸", "130000", "河北"),
    XINGTAI("130500", "邢台", "130000", "河北"),
    BAODING("130600", "保定", "130000", "河北"),
    ZHANGJIAKOU("130700", "张家口", "130000", "河北"),
    CHENGDE("130800", "承德", "130000", "河北"),
    CANGZHOU("130900", "沧州", "130000", "河北"),
    LANGFANG("131000", "廊坊", "130000", "河北"),
    HENGSHUI("131100", "衡水", "130000", "河北"),
//    HEBEI_OTHER("139900", "其它", "130000", "河北"),

    // 山西省（晋）
    TAIYUAN("140100", "太原", "140000", "山西"),
    DATONG("140200", "大同", "140000", "山西"),
    YANGQUAN("140300", "阳泉", "140000", "山西"),
    CHANGZHI("140400", "长治", "140000", "山西"),
    JINCHENG("140500", "晋城", "140000", "山西"),
    SHUOZHOU("140600", "朔州", "140000", "山西"),
    JINZHONG("140700", "晋中", "140000", "山西"),
    YUNCHENG("140800", "运城", "140000", "山西"),
    XINZHOU("140900", "忻州", "140000", "山西"),
    LINFEN("141000", "临汾", "140000", "山西"),
    LULIANG("141100", "吕梁", "140000", "山西"),
//    SHANXI_OTHER("149900", "其它", "140000", "山西"),

    // 内蒙古自治区（蒙）
    HUHEHAOTE("150100", "呼和浩特", "150000", "内蒙古"),
    BAOTOU("150200", "包头", "150000", "内蒙古"),
    WUHAI("150300", "乌海", "150000", "内蒙古"),
    CHIFENG("150400", "赤峰", "150000", "内蒙古"),
    TONGLIAO("150500", "通辽", "150000", "内蒙古"),
    // 其他内蒙古城市...
//    NEIMENGGU_OTHER("159900", "其它", "150000", "内蒙古"),

    // 辽宁省（辽）
    SHENYANG("210100", "沈阳", "210000", "辽宁"),
    DALIAN("210200", "大连", "210000", "辽宁"),
    ANSHAN("210300", "鞍山", "210000", "辽宁"),
    FUSHUN("210400", "抚顺", "210000", "辽宁"),
    BENXI("210500", "本溪", "210000", "辽宁"),
    DANDONG("210600", "丹东", "210000", "辽宁"),
    JINZHOU("210700", "锦州", "210000", "辽宁"),
    YINGKOU("210800", "营口", "210000", "辽宁"),
    FUXIN("210900", "阜新", "210000", "辽宁"),
    LIAOYANG("211000", "辽阳", "210000", "辽宁"),
    PANJIN("211100", "盘锦", "210000", "辽宁"),
    TIELING("211200", "铁岭", "210000", "辽宁"),
    CHAOYANG("211300", "朝阳", "210000", "辽宁"),
    HULUDAO("211400", "葫芦岛", "210000", "辽宁"),
//    LIAONING_OTHER("219900", "其它", "210000", "辽宁"),

    // 广东省（粤）
    GUANGZHOU("440100", "广州", "440000", "广东"),
    SHAOGUAN("440200", "韶关", "440000", "广东"),
    SHENZHEN("440300", "深圳", "440000", "广东"),
    ZHUHAI("440400", "珠海", "440000", "广东"),
    SHANTOU("440500", "汕头", "440000", "广东"),
    FOSHAN("440600", "佛山", "440000", "广东"),
    JIANGMEN("440700", "江门", "440000", "广东"),
    ZHANJIANG("440800", "湛江", "440000", "广东"),
    MAOMING("440900", "茂名", "440000", "广东"),
    ZHAOQING("441200", "肇庆", "440000", "广东"),
    HUIZHOU("441300", "惠州", "440000", "广东"),
    MEIZHOU("441400", "梅州", "440000", "广东"),
    SHANWEI("441500", "汕尾", "440000", "广东"),
    HEYUAN("441600", "河源", "440000", "广东"),
    YANGJIANG("441700", "阳江", "440000", "广东"),
    QINGYUAN("441800", "清远", "440000", "广东"),
    DONGGUAN("441900", "东莞", "440000", "广东"),
    ZHONGSHAN("442000", "中山", "440000", "广东"),
    CHAOZHOU("445100", "潮州", "440000", "广东"),
    JIEYANG("445200", "揭阳", "440000", "广东"),
    YUNFU("445300", "云浮", "440000", "广东"),
//    GUANGDONG_OTHER("449900", "其它", "440000", "广东"),

    // 其他省份数据（按相同格式补充完整）
    // 示例：四川省
    CHENGDU("510100", "成都", "510000", "四川"),
    ZIGONG("510300", "自贡", "510000", "四川"),
    PANZHIHUA("510400", "攀枝花", "510000", "四川");
    // ...其他四川城市
//    SICHUAN_OTHER("519900", "其它", "510000", "四川");

    //------------------- 字段定义 -------------------
    private final String cityId;
    private final String cityName;
    private final String provinceId;
    private final String provinceName;

    //------------------- 静态缓存 -------------------
    private static final Map<String, CityEnum> cityIdToEnumMap = new HashMap<>(500);
    private static final Map<String, ProvinceInfo> provinceInfoMap = new HashMap<>(50);

    // 省份信息内部类
    private static class ProvinceInfo {
        String provinceName;
        List<CityEnum> cities = new ArrayList<>();

        ProvinceInfo(String provinceName) {
            this.provinceName = provinceName;
        }
    }

    //------------------- 静态初始化 -------------------
    static {
        // 初始化所有城市数据
        for (CityEnum city : values()) {
            // 填充城市ID映射
            cityIdToEnumMap.put(city.cityId, city);

            // 填充省份信息
            provinceInfoMap.computeIfAbsent(city.provinceId,
                    k -> new ProvinceInfo(city.provinceName)
            ).cities.add(city);
        }
    }

    //------------------- 构造方法 -------------------
    CityEnum(String cityId, String cityName, String provinceId, String provinceName) {
        this.cityId = cityId;
        this.cityName = cityName;
        this.provinceId = provinceId;
        this.provinceName = provinceName;
    }

    //------------------- 公共方法 -------------------

    /**
     * 根据cityId查询城市
     * @param cityId
     * @return
     */
    public static CityEnum getByCityId(String cityId) {
        return cityIdToEnumMap.get(cityId);
    }


    /**
     * 获取所有省份列表（去重）
     * @return
     */
    public static List<String> getAllProvinces() {
        return provinceInfoMap.values().stream()
                .map(info -> info.provinceName)
                .distinct()
                .collect(Collectors.toList());
    }

    //------------------- Getter -------------------
    public String getCityId() { return cityId; }
    public String getCityName() { return cityName; }
    public String getProvinceId() { return provinceId; }
    public String getProvinceName() { return provinceName; }

    /**
     * 随机获取一个城市（包含所有城市）
     * @return 随机城市对象
     */
    public static CityEnum getRandomCity() {
        // 使用线程安全的随机数生成器
        return values()[ThreadLocalRandom.current().nextInt(values().length)];
    }
}
