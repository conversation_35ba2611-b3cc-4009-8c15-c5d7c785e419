package com.autohome.applet.model.dto.original;

import com.autohome.applet.util.JacksonHelper;

import java.util.Date;
import java.util.HashMap;

/**
 * 文章实体
 * <AUTHOR>
 * @date 2017年8月1日 上午9:48:13
 * @since
 */
public class NewsFinalResult {
	
    //数据库唯一标识
    private int id;
    
    //编号
    private int code;
    
    //标题
    private String title;
    
    //类型名称
    private String type;
    
    //数据类型
    private int dataType;
    
    //发布时间
    private Date publishtime;
    
    //图片地址
    private String imgUrl;
    
    //无水印视频图片地址
    private String videoImgUrl;
    
    //数据库记录创建时间
    private Date dbinsertTime;
    
    //数据库记录更新时间
    private Date dbupdateTime;
    
    //页面详情
    private String indexDetail;
    
    //评论数
    private int replyCount;
    
    //页码索引
    private int pageindex;
    
    private int jumptype;
    
    private String jumpUrl;
    
    //是否头条{1:是0:否}
    private int isHot;
    
    //媒体类型
    private int mediatype;
    
    //是否删除
    private int isDelete;
    
    //更新时间
    private Date updateTime;
    
    //文章概要
    private String summary;
    
    //图片集合（图文类型会用 v4.9.5 ）------如果不为空将判断为该文章为图文类型
    private String graphicImgList;

    private Integer subjectid;

    //图库  图片品牌id
    private Integer car_brandid;
    
    private Integer car_colorid;

    private Integer club_memberid;

    //浏览或展示数（文章，说客，视频,快报 , 帖子 )---注意在视频里面db中 viewcount 表示回复数
    private Integer viewcount;

    private String club_lastreplydate;

    private Integer club_classid;

    
    //车系id(文章，说客，视频)
    private String cms_seriesid;
    
    //内容分类(文章，说客，视频，快报)
    private String cms_contentclass;

    
    //内容标签(文章，说客，视频)
    private String cms_tags;

    
    //文章,是否关闭评论 1是(不能评论)  0 否 (可以评论)
    private Integer isclosecomment;

    //说客 1首发，2独家
    private Integer skisfirst;

    
    //是否大小图  1是 0否
    private Integer videocardtype;
    
    //卡片类型
    private Integer cardtype1;
    
    //卡片扩展属性集合
    private String cardextendattr1;
    
    //卡片扩展图片字段
    private String cardextendimg1;

    //数据唯一ID，
	//规则 拼接三个值组成Long数值 Unix（publishtime) - 10位、（1000-datatype） - 3位、（100000000+code).substr(后六位）- 6位
	//按以上顺序拼接，如：1543546800994594350 = 1543546800 994 594350
    private Long dbid;
    //图片地址（2019-6-26 日APP首页改版，App首页焦点图用此字段存特殊尺寸的图片，非焦点图类暂无使用）
	//如有其它用户，上面标注出来
    private String imgurl2;

    private String pvinfo;
	
	private String hotreply;
	
	private Integer editorid;
	private Integer userid;
	private String username;
	
	//视频横竖标识；1-竖版
	private Integer direction;
	
	private Integer width;
	
	private Integer height;
	
	public Integer getDirection() {
		return direction;
	}
	
	public void setDirection(Integer direction) {
		this.direction = direction;
	}
	
	public Integer getWidth() {
		return width;
	}
	
	public void setWidth(Integer width) {
		this.width = width;
	}
	
	public Integer getHeight() {
		return height;
	}
	
	public void setHeight(Integer height) {
		this.height = height;
	}
	
	public Integer getEditorid() {
		return editorid;
	}
	
	public void setEditorid(Integer editorid) {
		this.editorid = editorid;
	}
	
	public Integer getUserid() {
		return userid;
	}
	
	public void setUserid(Integer userid) {
		this.userid = userid;
	}
	
	public String getUsername() {
		return username;
	}
	
	public void setUsername(String username) {
		this.username = username;
	}
	
	public NewsFinalResult() {
		this.code = 0;
		this.title = "";
		this.type = "";
		this.imgUrl = "";
		this.indexDetail = "";
		this.isHot = 0;
		this.isDelete = 0;
		this.jumptype = 0;
		this.jumpUrl = "";
		this.replyCount = 0;
		this.summary = "";
		this.videoImgUrl = "";
		this.graphicImgList = "";
		this.subjectid = 0;
		this.videocardtype = 0;
		this.cardtype1 = -1;
		this.car_brandid = 0;
		this.car_colorid = 0;
		this.club_memberid = 0;
		this.club_classid = 0;
		this.isclosecomment = 0;
		this.viewcount = 0;
		this.skisfirst = 0;
		this.cardextendattr1 = JacksonHelper.serialize(new HashMap<String,String>());
		this.cardextendimg1 = "";
		this.imgurl2 = "";
		this.pvinfo = "";
	}

	public String getPvinfo() {
		return pvinfo;
	}

	public void setPvinfo(String pvinfo) {
		this.pvinfo = pvinfo;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public int getDataType() {
		return dataType;
	}

	public void setDataType(int dataType) {
		this.dataType = dataType;
	}

	public Date getPublishtime() {
		return publishtime;
	}

	public void setPublishtime(Date publishtime) {
		this.publishtime = publishtime;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getVideoImgUrl() {
		return videoImgUrl;
	}

	public void setVideoImgUrl(String videoImgUrl) {
		this.videoImgUrl = videoImgUrl;
	}

	public Date getDbinsertTime() {
		return dbinsertTime;
	}

	public void setDbinsertTime(Date dbinsertTime) {
		this.dbinsertTime = dbinsertTime;
	}

	public Date getDbupdateTime() {
		return dbupdateTime;
	}

	public void setDbupdateTime(Date dbupdateTime) {
		this.dbupdateTime = dbupdateTime;
	}

	public String getIndexDetail() {
		return indexDetail;
	}

	public void setIndexDetail(String indexDetail) {
		this.indexDetail = indexDetail;
	}

	public int getReplyCount() {
		return replyCount;
	}

	public void setReplyCount(int replyCount) {
		this.replyCount = replyCount;
	}

	public int getPageindex() {
		return pageindex;
	}

	public void setPageindex(int pageindex) {
		this.pageindex = pageindex;
	}

	public int getJumptype() {
		return jumptype;
	}

	public void setJumptype(int jumptype) {
		this.jumptype = jumptype;
	}

	public String getJumpUrl() {
		return jumpUrl;
	}

	public void setJumpUrl(String jumpUrl) {
		this.jumpUrl = jumpUrl;
	}

	public int getIsHot() {
		return isHot;
	}

	public void setIsHot(int isHot) {
		this.isHot = isHot;
	}

	public int getMediatype() {
		return mediatype;
	}

	public void setMediatype(int mediatype) {
		this.mediatype = mediatype;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public String getGraphicImgList() {
		return graphicImgList;
	}

	public void setGraphicImgList(String graphicImgList) {
		this.graphicImgList = graphicImgList;
	}

	public Integer getSubjectid() {
		return subjectid;
	}

	public void setSubjectid(Integer subjectid) {
		this.subjectid = subjectid;
	}

	public Integer getCar_brandid() {
		return car_brandid;
	}

	public void setCar_brandid(Integer car_brandid) {
		this.car_brandid = car_brandid;
	}

	public Integer getCar_colorid() {
		return car_colorid;
	}

	public void setCar_colorid(Integer car_colorid) {
		this.car_colorid = car_colorid;
	}

	public Integer getClub_memberid() {
		return club_memberid;
	}

	public void setClub_memberid(Integer club_memberid) {
		this.club_memberid = club_memberid;
	}

	public Integer getViewcount() {
		return viewcount;
	}

	public void setViewcount(Integer viewcount) {
		this.viewcount = viewcount;
	}

	public String getClub_lastreplydate() {
		return club_lastreplydate;
	}

	public void setClub_lastreplydate(String club_lastreplydate) {
		this.club_lastreplydate = club_lastreplydate;
	}

	public Integer getClub_classid() {
		return club_classid;
	}

	public void setClub_classid(Integer club_classid) {
		this.club_classid = club_classid;
	}

	public String getCms_seriesid() {
		return cms_seriesid;
	}

	public void setCms_seriesid(String cms_seriesid) {
		this.cms_seriesid = cms_seriesid;
	}

	public String getCms_contentclass() {
		return cms_contentclass;
	}

	public void setCms_contentclass(String cms_contentclass) {
		this.cms_contentclass = cms_contentclass;
	}

	public String getCms_tags() {
		return cms_tags;
	}

	public void setCms_tags(String cms_tags) {
		this.cms_tags = cms_tags;
	}

	public Integer getIsclosecomment() {
		return isclosecomment;
	}

	public void setIsclosecomment(Integer isclosecomment) {
		this.isclosecomment = isclosecomment;
	}

	public Integer getSkisfirst() {
		return skisfirst;
	}

	public void setSkisfirst(Integer skisfirst) {
		this.skisfirst = skisfirst;
	}

	public Integer getVideocardtype() {
		return videocardtype;
	}

	public void setVideocardtype(Integer videocardtype) {
		this.videocardtype = videocardtype;
	}

	public Integer getCardtype1() {
		return cardtype1;
	}

	public void setCardtype1(Integer cardtype1) {
		this.cardtype1 = cardtype1;
	}

	public String getCardextendattr1() {
		return cardextendattr1;
	}

	public void setCardextendattr1(String cardextendattr1) {
		this.cardextendattr1 = cardextendattr1;
	}

	public String getCardextendimg1() {
		return cardextendimg1;
	}

	public void setCardextendimg1(String cardextendimg1) {
		this.cardextendimg1 = cardextendimg1;
	}

	public Long getDbid() {
		return dbid;
	}

	public void setDbid(Long dbid) {
		this.dbid = dbid;
	}

	public String getImgurl2() {
		return imgurl2;
	}

	public void setImgurl2(String imgurl2) {
		this.imgurl2 = imgurl2;
	}
	
	public String getHotreply() {
		return hotreply;
	}
	
	public void setHotreply(String hotreply) {
		this.hotreply = hotreply;
	}

	@Override
	public String toString() {
		return "NewsFinalResult [id=" + id + ", code=" + code + ", title=" + title + ", type=" + type + ", dataType="
				+ dataType + ", publishtime=" + publishtime + ", imgUrl=" + imgUrl + ", videoImgUrl=" + videoImgUrl
				+ ", dbinsertTime=" + dbinsertTime + ", dbupdateTime=" + dbupdateTime + ", indexDetail=" + indexDetail
				+ ", replyCount=" + replyCount + ", pageindex=" + pageindex + ", jumptype=" + jumptype + ", jumpUrl="
				+ jumpUrl + ", isHot=" + isHot + ", mediatype=" + mediatype + ", isDelete=" + isDelete + ", updateTime="
				+ updateTime + ", summary=" + summary + ", graphicImgList=" + graphicImgList + ", subjectid="
				+ subjectid + ", car_brandid=" + car_brandid + ", car_colorid=" + car_colorid + ", club_memberid="
				+ club_memberid + ", viewcount=" + viewcount + ", club_lastreplydate=" + club_lastreplydate
				+ ", club_classid=" + club_classid + ", cms_seriesid=" + cms_seriesid + ", cms_contentclass="
				+ cms_contentclass + ", cms_tags=" + cms_tags + ", isclosecomment=" + isclosecomment + ", skisfirst="
				+ skisfirst + ", videocardtype=" + videocardtype + ", cardtype1=" + cardtype1 + ", cardextendattr1="
				+ cardextendattr1 + ", cardextendimg1=" + cardextendimg1 + "]";
	}
	
}
