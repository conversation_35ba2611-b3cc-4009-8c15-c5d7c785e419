package com.autohome.applet.util;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CityUtil {
    @Data
    @AllArgsConstructor
    public static class City {

        private int cityId;
        private int provinceId;
        private String cityName;
        private String provinceName;
        private String firstChar;
    }

    @Data
    @AllArgsConstructor
    public static class Province {

        private int provinceId;
        private String provinceName;
        private String firstChar;
    }

    public static HashMap<Integer, City> cityMap = new HashMap<>();
    public static HashMap<Integer, Province> provinceMap = new HashMap<>();

    public static Integer getProvinceId(Integer cityId) {
        if (cityId == null || cityId == 0) {
            return null;
        }
        City city = getCity(cityId);
        if (city == null) {
            return null;
        } else {
            return city.provinceId;
        }
    }

    public static City getCity(int cityId) {
        return cityMap.get(cityId);
    }

    public static Province getProvince(int provinceId) {
        return provinceMap.get(provinceId);
    }

    public static List<City> getCityByProvinceId(Integer provinceId) {
        return cityMap.values().stream().filter(x -> x.getProvinceId() == provinceId).collect(Collectors.toList());
    }

    static {
        provinceMap.put(110000, new Province(110000, "北京", "B"));
        provinceMap.put(120000, new Province(120000, "天津", "T"));
        provinceMap.put(130000, new Province(130000, "河北", "H"));
        provinceMap.put(140000, new Province(140000, "山西", "S"));
        provinceMap.put(150000, new Province(150000, "内蒙古", "N"));
        provinceMap.put(210000, new Province(210000, "辽宁", "L"));
        provinceMap.put(220000, new Province(220000, "吉林", "J"));
        provinceMap.put(230000, new Province(230000, "黑龙江", "H"));
        provinceMap.put(310000, new Province(310000, "上海", "S"));
        provinceMap.put(320000, new Province(320000, "江苏", "J"));
        provinceMap.put(330000, new Province(330000, "浙江", "Z"));
        provinceMap.put(340000, new Province(340000, "安徽", "A"));
        provinceMap.put(350000, new Province(350000, "福建", "F"));
        provinceMap.put(360000, new Province(360000, "江西", "J"));
        provinceMap.put(370000, new Province(370000, "山东", "S"));
        provinceMap.put(410000, new Province(410000, "河南", "H"));
        provinceMap.put(420000, new Province(420000, "湖北", "H"));
        provinceMap.put(430000, new Province(430000, "湖南", "H"));
        provinceMap.put(440000, new Province(440000, "广东", "G"));
        provinceMap.put(450000, new Province(450000, "广西", "G"));
        provinceMap.put(460000, new Province(460000, "海南", "H"));
        provinceMap.put(500000, new Province(500000, "重庆", "C"));
        provinceMap.put(510000, new Province(510000, "四川", "S"));
        provinceMap.put(520000, new Province(520000, "贵州", "G"));
        provinceMap.put(530000, new Province(530000, "云南", "Y"));
        provinceMap.put(540000, new Province(540000, "西藏", "X"));
        provinceMap.put(610000, new Province(610000, "陕西", "S"));
        provinceMap.put(620000, new Province(620000, "甘肃", "G"));
        provinceMap.put(630000, new Province(630000, "青海", "Q"));
        provinceMap.put(640000, new Province(640000, "宁夏", "N"));
        provinceMap.put(650000, new Province(650000, "新疆", "X"));
        provinceMap.put(710000, new Province(710000, "台湾", "T"));
        provinceMap.put(810000, new Province(810000, "香港", "X"));
        provinceMap.put(820000, new Province(820000, "澳门", "A"));
        provinceMap.put(910000, new Province(910000, "海外", "H"));
        provinceMap.put(990000, new Province(990000, "其它", "Q"));

        cityMap.put(140800, new City(140800, 140000, "运城", "山西", "S"));
        cityMap.put(230400, new City(230400, 230000, "鹤岗", "黑龙江", "H"));
        cityMap.put(371200, new City(371200, 370000, "莱芜", "山东", "S"));
        cityMap.put(652300, new City(652300, 650000, "昌吉", "新疆", "X"));
        cityMap.put(512000, new City(512000, 510000, "资阳", "四川", "S"));
        cityMap.put(469007, new City(469007, 460000, "东方", "海南", "H"));
        cityMap.put(469006, new City(469006, 460000, "万宁", "海南", "H"));
        cityMap.put(370700, new City(370700, 370000, "潍坊", "山东", "S"));
        cityMap.put(652800, new City(652800, 650000, "巴音郭楞", "新疆", "X"));
        cityMap.put(469005, new City(469005, 460000, "文昌", "海南", "H"));
        cityMap.put(460300, new City(460300, 460000, "三沙", "海南", "H"));
        cityMap.put(511500, new City(511500, 510000, "宜宾", "四川", "S"));
        cityMap.put(341000, new City(341000, 340000, "黄山", "安徽", "A"));
        cityMap.put(469002, new City(469002, 460000, "琼海", "海南", "H"));
        cityMap.put(430600, new City(430600, 430000, "岳阳", "湖南", "H"));
        cityMap.put(469001, new City(469001, 460000, "五指山", "海南", "H"));
        cityMap.put(140300, new City(140300, 140000, "阳泉", "山西", "S"));
        cityMap.put(520200, new City(520200, 520000, "六盘水", "贵州", "G"));
        cityMap.put(340500, new City(340500, 340000, "马鞍山", "安徽", "A"));
        cityMap.put(430100, new City(430100, 430000, "长沙", "湖南", "H"));
        cityMap.put(110100, new City(110100, 110000, "北京", "北京", "B"));
        cityMap.put(469023, new City(469023, 460000, "澄迈", "海南", "H"));
        cityMap.put(469022, new City(469022, 460000, "屯昌", "海南", "H"));
        cityMap.put(451100, new City(451100, 450000, "贺州", "广西", "G"));
        cityMap.put(469021, new City(469021, 460000, "定安", "海南", "H"));
        cityMap.put(532500, new City(532500, 530000, "红河", "云南", "Y"));
        cityMap.put(370200, new City(370200, 370000, "青岛", "山东", "S"));
        cityMap.put(131100, new City(131100, 130000, "衡水", "河北", "H"));
        cityMap.put(220700, new City(220700, 220000, "松原", "吉林", "J"));
        cityMap.put(511000, new City(511000, 510000, "内江", "四川", "S"));
        cityMap.put(540200, new City(540200, 540000, "日喀则", "西藏", "X"));
        cityMap.put(469030, new City(469030, 460000, "琼中", "海南", "H"));
        cityMap.put(420900, new City(420900, 420000, "孝感", "湖北", "H"));
        cityMap.put(469029, new City(469029, 460000, "保亭", "海南", "H"));
        cityMap.put(469028, new City(469028, 460000, "陵水", "海南", "H"));
        cityMap.put(510500, new City(510500, 510000, "泸州", "四川", "S"));
        cityMap.put(469027, new City(469027, 460000, "乐东", "海南", "H"));
        cityMap.put(621100, new City(621100, 620000, "定西", "甘肃", "G"));
        cityMap.put(469026, new City(469026, 460000, "昌江", "海南", "H"));
        cityMap.put(469025, new City(469025, 460000, "白沙", "海南", "H"));
        cityMap.put(469024, new City(469024, 460000, "临高", "海南", "H"));
        cityMap.put(130600, new City(130600, 130000, "保定", "河北", "H"));
        cityMap.put(441900, new City(441900, 440000, "东莞", "广东", "G"));
        cityMap.put(220200, new City(220200, 220000, "吉林", "吉林", "J"));
        cityMap.put(361000, new City(361000, 360000, "抚州", "江西", "J"));
        cityMap.put(450600, new City(450600, 450000, "防城港", "广西", "G"));
        cityMap.put(659002, new City(659002, 650000, "阿拉尔", "新疆", "X"));
        cityMap.put(620600, new City(620600, 620000, "武威", "甘肃", "G"));
        cityMap.put(360500, new City(360500, 360000, "新余", "江西", "J"));
        cityMap.put(659003, new City(659003, 650000, "图木舒克", "新疆", "X"));
        cityMap.put(450100, new City(450100, 450000, "南宁", "广西", "G"));
        cityMap.put(411700, new City(411700, 410000, "驻马店", "河南", "H"));
        cityMap.put(659001, new City(659001, 650000, "石河子", "新疆", "X"));
        cityMap.put(130100, new City(130100, 130000, "石家庄", "河北", "H"));
        cityMap.put(330800, new City(330800, 330000, "衢州", "浙江", "Z"));
        cityMap.put(659004, new City(659004, 650000, "五家渠", "新疆", "X"));
        cityMap.put(330300, new City(330300, 330000, "温州", "浙江", "Z"));
        cityMap.put(522300, new City(522300, 520000, "黔西南", "贵州", "G"));
        cityMap.put(211000, new City(211000, 210000, "辽阳", "辽宁", "L"));
        cityMap.put(441400, new City(441400, 440000, "梅州", "广东", "G"));
        cityMap.put(440900, new City(440900, 440000, "茂名", "广东", "G"));
        cityMap.put(411200, new City(411200, 410000, "三门峡", "河南", "H"));
        cityMap.put(210500, new City(210500, 210000, "本溪", "辽宁", "L"));
        cityMap.put(321100, new City(321100, 320000, "镇江", "江苏", "J"));
        cityMap.put(410700, new City(410700, 410000, "新乡", "河南", "H"));
        cityMap.put(150600, new City(150600, 150000, "鄂尔多斯", "内蒙古", "N"));
        cityMap.put(530500, new City(530500, 530000, "保山", "云南", "Y"));
        cityMap.put(620100, new City(620100, 620000, "兰州", "甘肃", "G"));
        cityMap.put(350800, new City(350800, 350000, "龙岩", "福建", "F"));
        cityMap.put(440400, new City(440400, 440000, "珠海", "广东", "G"));
        cityMap.put(150100, new City(150100, 150000, "呼和浩特", "内蒙古", "N"));
        cityMap.put(350300, new City(350300, 350000, "莆田", "福建", "F"));
        cityMap.put(231000, new City(231000, 230000, "牡丹江", "黑龙江", "H"));
        cityMap.put(320600, new City(320600, 320000, "南通", "江苏", "J"));
        cityMap.put(610900, new City(610900, 610000, "安康", "陕西", "S"));
        cityMap.put(410200, new City(410200, 410000, "开封", "河南", "H"));
        cityMap.put(320100, new City(320100, 320000, "南京", "江苏", "J"));
        cityMap.put(371300, new City(371300, 370000, "临沂", "山东", "S"));
        cityMap.put(533100, new City(533100, 530000, "德宏", "云南", "Y"));
        cityMap.put(341600, new City(341600, 340000, "亳州", "安徽", "A"));
        cityMap.put(431200, new City(431200, 430000, "怀化", "湖南", "H"));
        cityMap.put(140900, new City(140900, 140000, "忻州", "山西", "S"));
        cityMap.put(230500, new City(230500, 230000, "双鸭山", "黑龙江", "H"));
        cityMap.put(341100, new City(341100, 340000, "滁州", "安徽", "A"));
        cityMap.put(610400, new City(610400, 610000, "咸阳", "陕西", "S"));
        cityMap.put(430700, new City(430700, 430000, "常德", "湖南", "H"));
        cityMap.put(520300, new City(520300, 520000, "遵义", "贵州", "G"));
        cityMap.put(640100, new City(640100, 640000, "银川", "宁夏", "N"));
        cityMap.put(652900, new City(652900, 650000, "阿克苏", "新疆", "X"));
        cityMap.put(532600, new City(532600, 530000, "文山", "云南", "Y"));
        cityMap.put(140400, new City(140400, 140000, "长治", "山西", "S"));
        cityMap.put(370800, new City(370800, 370000, "济宁", "山东", "S"));
        cityMap.put(460400, new City(460400, 460000, "儋州", "海南", "H"));
        cityMap.put(511600, new City(511600, 510000, "广安", "四川", "S"));
        cityMap.put(810100, new City(810100, 810000, "香港", "香港", "X"));
        cityMap.put(370300, new City(370300, 370000, "淄博", "山东", "S"));
        cityMap.put(511100, new City(511100, 510000, "乐山", "四川", "S"));
        cityMap.put(340600, new City(340600, 340000, "淮北", "安徽", "A"));
        cityMap.put(430200, new City(430200, 430000, "株洲", "湖南", "H"));
        cityMap.put(340100, new City(340100, 340000, "合肥", "安徽", "A"));
        cityMap.put(220800, new City(220800, 220000, "白城", "吉林", "J"));
        cityMap.put(540300, new City(540300, 540000, "昌都", "西藏", "X"));
        cityMap.put(451200, new City(451200, 450000, "河池", "广西", "G"));
        cityMap.put(361100, new City(361100, 360000, "上饶", "江西", "J"));
        cityMap.put(450700, new City(450700, 450000, "钦州", "广西", "G"));
        cityMap.put(152200, new City(152200, 150000, "兴安盟", "内蒙古", "N"));
        cityMap.put(130700, new City(130700, 130000, "张家口", "河北", "H"));
        cityMap.put(421000, new City(421000, 420000, "荆州", "湖北", "H"));
        cityMap.put(220300, new City(220300, 220000, "四平", "吉林", "J"));
        cityMap.put(510600, new City(510600, 510000, "德阳", "四川", "S"));
        cityMap.put(330900, new City(330900, 330000, "舟山", "浙江", "Z"));
        cityMap.put(420500, new City(420500, 420000, "宜昌", "湖北", "H"));
        cityMap.put(510100, new City(510100, 510000, "成都", "四川", "S"));
        cityMap.put(620700, new City(620700, 620000, "张掖", "甘肃", "G"));
        cityMap.put(442000, new City(442000, 440000, "中山", "广东", "G"));
        cityMap.put(621200, new City(621200, 620000, "陇南", "甘肃", "G"));
        cityMap.put(130200, new City(130200, 130000, "唐山", "河北", "H"));
        cityMap.put(441500, new City(441500, 440000, "汕尾", "广东", "G"));
        cityMap.put(360600, new City(360600, 360000, "鹰潭", "江西", "J"));
        cityMap.put(450200, new City(450200, 450000, "柳州", "广西", "G"));
        cityMap.put(211100, new City(211100, 210000, "盘锦", "辽宁", "L"));
        cityMap.put(530600, new City(530600, 530000, "昭通", "云南", "Y"));
        cityMap.put(620200, new City(620200, 620000, "嘉峪关", "甘肃", "G"));
        cityMap.put(360100, new City(360100, 360000, "南昌", "江西", "J"));
        cityMap.put(411300, new City(411300, 410000, "南阳", "河南", "H"));
        cityMap.put(330400, new City(330400, 330000, "嘉兴", "浙江", "Z"));
        cityMap.put(650400, new City(650400, 650000, "吐鲁番", "新疆", "X"));
        cityMap.put(445100, new City(445100, 440000, "潮州", "广东", "G"));
        cityMap.put(210600, new City(210600, 210000, "丹东", "辽宁", "L"));
        cityMap.put(150700, new City(150700, 150000, "呼伦贝尔", "内蒙古", "N"));
        cityMap.put(350900, new City(350900, 350000, "宁德", "福建", "F"));
        cityMap.put(611000, new City(611000, 610000, "商洛", "陕西", "S"));
        cityMap.put(440500, new City(440500, 440000, "汕头", "广东", "G"));
        cityMap.put(321200, new City(321200, 320000, "泰州", "江苏", "J"));
        cityMap.put(410800, new City(410800, 410000, "焦作", "河南", "H"));
        cityMap.put(210100, new City(210100, 210000, "沈阳", "辽宁", "L"));
        cityMap.put(513200, new City(513200, 510000, "阿坝", "四川", "S"));
        cityMap.put(320700, new City(320700, 320000, "连云港", "江苏", "J"));
        cityMap.put(654000, new City(654000, 650000, "伊犁", "新疆", "X"));
        cityMap.put(410300, new City(410300, 410000, "洛阳", "河南", "H"));
        cityMap.put(150200, new City(150200, 150000, "包头", "内蒙古", "N"));
        cityMap.put(530100, new City(530100, 530000, "昆明", "云南", "Y"));
        cityMap.put(632500, new City(632500, 630000, "海南", "青海", "Q"));
        cityMap.put(231100, new City(231100, 230000, "黑河", "黑龙江", "H"));
        cityMap.put(419001, new City(419001, 410000, "济源市", "河南", "H"));
        cityMap.put(341700, new City(341700, 340000, "池州", "安徽", "A"));
        cityMap.put(640200, new City(640200, 640000, "石嘴山", "宁夏", "N"));
        cityMap.put(653000, new City(653000, 650000, "克孜勒苏", "新疆", "X"));
        cityMap.put(431300, new City(431300, 430000, "娄底", "湖南", "H"));
        cityMap.put(222400, new City(222400, 220000, "延边", "吉林", "J"));
        cityMap.put(350400, new City(350400, 350000, "三明", "福建", "F"));
        cityMap.put(542400, new City(542400, 540000, "那曲", "西藏", "X"));
        cityMap.put(141000, new City(141000, 140000, "临汾", "山西", "S"));
        cityMap.put(230600, new City(230600, 230000, "大庆", "黑龙江", "H"));
        cityMap.put(320200, new City(320200, 320000, "无锡", "江苏", "J"));
        cityMap.put(371400, new City(371400, 370000, "德州", "山东", "S"));
        cityMap.put(610500, new City(610500, 610000, "渭南", "陕西", "S"));
        cityMap.put(370900, new City(370900, 370000, "泰安", "山东", "S"));
        cityMap.put(511700, new City(511700, 510000, "达州", "四川", "S"));
        cityMap.put(341200, new City(341200, 340000, "阜阳", "安徽", "A"));
        cityMap.put(430800, new City(430800, 430000, "张家界", "湖南", "H"));
        cityMap.put(140500, new City(140500, 140000, "晋城", "山西", "S"));
        cityMap.put(520400, new City(520400, 520000, "安顺", "贵州", "G"));
        cityMap.put(230100, new City(230100, 230000, "哈尔滨", "黑龙江", "H"));
        cityMap.put(340700, new City(340700, 340000, "铜陵", "安徽", "A"));
        cityMap.put(430300, new City(430300, 430000, "湘潭", "湖南", "H"));
        cityMap.put(451300, new City(451300, 450000, "来宾", "广西", "G"));
        cityMap.put(370400, new City(370400, 370000, "枣庄", "山东", "S"));
        cityMap.put(421100, new City(421100, 420000, "黄冈", "湖北", "H"));
        cityMap.put(510700, new City(510700, 510000, "绵阳", "四川", "S"));
        cityMap.put(340200, new City(340200, 340000, "芜湖", "安徽", "A"));
        cityMap.put(130800, new City(130800, 130000, "承德", "河北", "H"));
        cityMap.put(220400, new City(220400, 220000, "辽源", "吉林", "J"));
        cityMap.put(450800, new City(450800, 450000, "贵港", "广西", "G"));
        cityMap.put(540400, new City(540400, 540000, "林芝", "西藏", "X"));
        cityMap.put(360700, new City(360700, 360000, "赣州", "江西", "J"));
        cityMap.put(450300, new City(450300, 450000, "桂林", "广西", "G"));
        cityMap.put(130300, new City(130300, 130000, "秦皇岛", "河北", "H"));
        cityMap.put(331000, new City(331000, 330000, "台州", "浙江", "Z"));
        cityMap.put(420600, new City(420600, 420000, "襄阳", "湖北", "H"));
        cityMap.put(232700, new City(232700, 230000, "大兴安岭", "黑龙江", "H"));
        cityMap.put(330500, new City(330500, 330000, "湖州", "浙江", "Z"));
        cityMap.put(420100, new City(420100, 420000, "武汉", "湖北", "H"));
        cityMap.put(211200, new City(211200, 210000, "铁岭", "辽宁", "L"));
        cityMap.put(530700, new City(530700, 530000, "丽江", "云南", "Y"));
        cityMap.put(620300, new City(620300, 620000, "金昌", "甘肃", "G"));
        cityMap.put(441600, new City(441600, 440000, "河源", "广东", "G"));
        cityMap.put(620800, new City(620800, 620000, "平凉", "甘肃", "G"));
        cityMap.put(360200, new City(360200, 360000, "景德镇", "江西", "J"));
        cityMap.put(650500, new City(650500, 650000, "哈密", "新疆", "X"));
        cityMap.put(411400, new City(411400, 410000, "商丘", "河南", "H"));
        cityMap.put(210700, new City(210700, 210000, "锦州", "辽宁", "L"));
        cityMap.put(321300, new City(321300, 320000, "宿迁", "江苏", "J"));
        cityMap.put(632600, new City(632600, 630000, "果洛", "青海", "Q"));
        cityMap.put(410900, new City(410900, 410000, "濮阳", "河南", "H"));
        cityMap.put(150800, new City(150800, 150000, "巴彦淖尔", "内蒙古", "N"));
        cityMap.put(513300, new City(513300, 510000, "甘孜", "四川", "S"));
        cityMap.put(445200, new City(445200, 440000, "揭阳", "广东", "G"));
        cityMap.put(910100, new City(910100, 910000, "海外", "海外", "H"));
        cityMap.put(210200, new City(210200, 210000, "大连", "辽宁", "L"));
        cityMap.put(440600, new City(440600, 440000, "佛山", "广东", "G"));
        cityMap.put(150300, new City(150300, 150000, "乌海", "内蒙古", "N"));
        cityMap.put(350500, new City(350500, 350000, "泉州", "福建", "F"));
        cityMap.put(610600, new City(610600, 610000, "延安", "陕西", "S"));
        cityMap.put(440100, new City(440100, 440000, "广州", "广东", "G"));
        cityMap.put(231200, new City(231200, 230000, "绥化", "黑龙江", "H"));
        cityMap.put(320800, new City(320800, 320000, "淮安", "江苏", "J"));
        cityMap.put(120100, new City(120100, 120000, "天津", "天津", "T"));
        cityMap.put(640300, new City(640300, 640000, "吴忠", "宁夏", "N"));
        cityMap.put(653100, new City(653100, 650000, "喀什", "新疆", "X"));
        cityMap.put(410400, new City(410400, 410000, "平顶山", "河南", "H"));
        cityMap.put(320300, new City(320300, 320000, "徐州", "江苏", "J"));
        cityMap.put(371500, new City(371500, 370000, "聊城", "山东", "S"));
        cityMap.put(542500, new City(542500, 540000, "阿里", "西藏", "X"));
        cityMap.put(341800, new City(341800, 340000, "宣城", "安徽", "A"));
        cityMap.put(141100, new City(141100, 140000, "吕梁", "山西", "S"));
        cityMap.put(230700, new City(230700, 230000, "伊春", "黑龙江", "H"));
        cityMap.put(341300, new City(341300, 340000, "宿州", "安徽", "A"));
        cityMap.put(430900, new City(430900, 430000, "益阳", "湖南", "H"));
        cityMap.put(520500, new City(520500, 520000, "毕节", "贵州", "G"));
        cityMap.put(140600, new City(140600, 140000, "朔州", "山西", "S"));
        cityMap.put(230200, new City(230200, 230000, "齐齐哈尔", "黑龙江", "H"));
        cityMap.put(533300, new City(533300, 530000, "怒江", "云南", "Y"));
        cityMap.put(622900, new City(622900, 620000, "临夏", "甘肃", "G"));
        cityMap.put(371000, new City(371000, 370000, "威海", "山东", "S"));
        cityMap.put(610100, new City(610100, 610000, "西安", "陕西", "S"));
        cityMap.put(511800, new City(511800, 510000, "雅安", "四川", "S"));
        cityMap.put(370500, new City(370500, 370000, "东营", "山东", "S"));
        cityMap.put(460100, new City(460100, 460000, "海口", "海南", "H"));
        cityMap.put(511300, new City(511300, 510000, "南充", "四川", "S"));
        cityMap.put(532300, new City(532300, 530000, "楚雄", "云南", "Y"));
        cityMap.put(340800, new City(340800, 340000, "安庆", "安徽", "A"));
        cityMap.put(430400, new City(430400, 430000, "衡阳", "湖南", "H"));
        cityMap.put(152900, new City(152900, 150000, "阿拉善盟", "内蒙古", "N"));
        cityMap.put(140100, new City(140100, 140000, "太原", "山西", "S"));
        cityMap.put(532800, new City(532800, 530000, "西双版纳", "云南", "Y"));
        cityMap.put(340300, new City(340300, 340000, "蚌埠", "安徽", "A"));
        cityMap.put(451400, new City(451400, 450000, "崇左", "广西", "G"));
        cityMap.put(310100, new City(310100, 310000, "上海", "上海", "S"));
        cityMap.put(450900, new City(450900, 450000, "玉林", "广西", "G"));
        cityMap.put(130900, new City(130900, 130000, "沧州", "河北", "H"));
        cityMap.put(421200, new City(421200, 420000, "咸宁", "湖北", "H"));
        cityMap.put(220500, new City(220500, 220000, "通化", "吉林", "J"));
        cityMap.put(510800, new City(510800, 510000, "广元", "四川", "S"));
        cityMap.put(331100, new City(331100, 330000, "丽水", "浙江", "Z"));
        cityMap.put(420700, new City(420700, 420000, "鄂州", "湖北", "H"));
        cityMap.put(510300, new City(510300, 510000, "自贡", "四川", "S"));
        cityMap.put(540500, new City(540500, 540000, "山南", "西藏", "X"));
        cityMap.put(630100, new City(630100, 630000, "西宁", "青海", "Q"));
        cityMap.put(130400, new City(130400, 130000, "邯郸", "河北", "H"));
        cityMap.put(441700, new City(441700, 440000, "阳江", "广东", "G"));
        cityMap.put(360800, new City(360800, 360000, "吉安", "江西", "J"));
        cityMap.put(450400, new City(450400, 450000, "梧州", "广西", "G"));
        cityMap.put(211300, new City(211300, 210000, "朝阳", "辽宁", "L"));
        cityMap.put(360300, new City(360300, 360000, "萍乡", "江西", "J"));
        cityMap.put(411500, new City(411500, 410000, "信阳", "河南", "H"));
        cityMap.put(620900, new City(620900, 620000, "酒泉", "甘肃", "G"));
        cityMap.put(330600, new City(330600, 330000, "绍兴", "浙江", "Z"));
        cityMap.put(420200, new City(420200, 420000, "黄石", "湖北", "H"));
        cityMap.put(522600, new City(522600, 520000, "黔东南", "贵州", "G"));
        cityMap.put(654200, new City(654200, 650000, "塔城", "新疆", "X"));
        cityMap.put(330100, new City(330100, 330000, "杭州", "浙江", "Z"));
        cityMap.put(445300, new City(445300, 440000, "云浮", "广东", "G"));
        cityMap.put(210800, new City(210800, 210000, "营口", "辽宁", "L"));
        cityMap.put(530300, new City(530300, 530000, "曲靖", "云南", "Y"));
        cityMap.put(632700, new City(632700, 630000, "玉树", "青海", "Q"));
        cityMap.put(441200, new City(441200, 440000, "肇庆", "广东", "G"));
        cityMap.put(150900, new City(150900, 150000, "乌兰察布", "内蒙古", "N"));
        cityMap.put(530800, new City(530800, 530000, "普洱", "云南", "Y"));
        cityMap.put(620400, new City(620400, 620000, "白银", "甘肃", "G"));
        cityMap.put(440700, new City(440700, 440000, "江门", "广东", "G"));
        cityMap.put(650100, new City(650100, 650000, "乌鲁木齐", "新疆", "X"));
        cityMap.put(411000, new City(411000, 410000, "许昌", "河南", "H"));
        cityMap.put(210300, new City(210300, 210000, "鞍山", "辽宁", "L"));
        cityMap.put(513400, new City(513400, 510000, "凉山", "四川", "S"));
        cityMap.put(320900, new City(320900, 320000, "盐城", "江苏", "J"));
        cityMap.put(632200, new City(632200, 630000, "海北", "青海", "Q"));
        cityMap.put(410500, new City(410500, 410000, "安阳", "河南", "H"));
        cityMap.put(150400, new City(150400, 150000, "赤峰", "内蒙古", "N"));
        cityMap.put(500100, new City(500100, 500000, "重庆", "重庆", "C"));
        cityMap.put(610700, new City(610700, 610000, "汉中", "陕西", "S"));
        cityMap.put(820100, new City(820100, 820000, "澳门", "澳门", "A"));
        cityMap.put(350600, new City(350600, 350000, "漳州", "福建", "F"));
        cityMap.put(440200, new City(440200, 440000, "韶关", "广东", "G"));
        cityMap.put(533400, new City(533400, 530000, "迪庆", "云南", "Y"));
        cityMap.put(350100, new City(350100, 350000, "福州", "福建", "F"));
        cityMap.put(623000, new City(623000, 620000, "甘南", "甘肃", "G"));
        cityMap.put(610200, new City(610200, 610000, "铜川", "陕西", "S"));
        cityMap.put(230800, new City(230800, 230000, "佳木斯", "黑龙江", "H"));
        cityMap.put(320400, new City(320400, 320000, "常州", "江苏", "J"));
        cityMap.put(371600, new City(371600, 370000, "滨州", "山东", "S"));
        cityMap.put(652700, new City(652700, 650000, "博尔塔拉", "新疆", "X"));
        cityMap.put(422800, new City(422800, 420000, "恩施", "湖北", "H"));
        cityMap.put(640400, new City(640400, 640000, "固原", "宁夏", "N"));
        cityMap.put(371100, new City(371100, 370000, "日照", "山东", "S"));
        cityMap.put(653200, new City(653200, 650000, "和田", "新疆", "X"));
        cityMap.put(511900, new City(511900, 510000, "巴中", "四川", "S"));
        cityMap.put(431000, new City(431000, 430000, "郴州", "湖南", "H"));
        cityMap.put(140700, new City(140700, 140000, "晋中", "山西", "S"));
        cityMap.put(520600, new City(520600, 520000, "铜仁", "贵州", "G"));
        cityMap.put(230300, new City(230300, 230000, "鸡西", "黑龙江", "H"));
        cityMap.put(430500, new City(430500, 430000, "邵阳", "湖南", "H"));
        cityMap.put(520100, new City(520100, 520000, "贵阳", "贵州", "G"));
        cityMap.put(140200, new City(140200, 140000, "大同", "山西", "S"));
        cityMap.put(532900, new City(532900, 530000, "大理", "云南", "Y"));
        cityMap.put(370600, new City(370600, 370000, "烟台", "山东", "S"));
        cityMap.put(460200, new City(460200, 460000, "三亚", "海南", "H"));
        cityMap.put(511400, new City(511400, 510000, "眉山", "四川", "S"));
        cityMap.put(630200, new City(630200, 630000, "海东", "青海", "Q"));
        cityMap.put(370100, new City(370100, 370000, "济南", "山东", "S"));
        cityMap.put(421300, new City(421300, 420000, "随州", "湖北", "H"));
        cityMap.put(510900, new City(510900, 510000, "遂宁", "四川", "S"));
        cityMap.put(340400, new City(340400, 340000, "淮南", "安徽", "A"));
        cityMap.put(152500, new City(152500, 150000, "锡林郭勒盟", "内蒙古", "N"));
        cityMap.put(131000, new City(131000, 130000, "廊坊", "河北", "H"));
        cityMap.put(220600, new City(220600, 220000, "白山", "吉林", "J"));
        cityMap.put(451000, new City(451000, 450000, "百色", "广西", "G"));
        cityMap.put(621000, new City(621000, 620000, "庆阳", "甘肃", "G"));
        cityMap.put(360900, new City(360900, 360000, "宜春", "江西", "J"));
        cityMap.put(450500, new City(450500, 450000, "北海", "广西", "G"));
        cityMap.put(130500, new City(130500, 130000, "邢台", "河北", "H"));
        cityMap.put(420800, new City(420800, 420000, "荆门", "湖北", "H"));
        cityMap.put(220100, new City(220100, 220000, "长春", "吉林", "J"));
        cityMap.put(510400, new City(510400, 510000, "攀枝花", "四川", "S"));
        cityMap.put(429006, new City(429006, 420000, "天门", "湖北", "H"));
        cityMap.put(330700, new City(330700, 330000, "金华", "浙江", "Z"));
        cityMap.put(420300, new City(420300, 420000, "十堰", "湖北", "H"));
        cityMap.put(429004, new City(429004, 420000, "仙桃", "湖北", "H"));
        cityMap.put(433100, new City(433100, 430000, "湘西", "湖南", "H"));
        cityMap.put(522700, new City(522700, 520000, "黔南", "贵州", "G"));
        cityMap.put(429005, new City(429005, 420000, "潜江", "湖北", "H"));
        cityMap.put(211400, new City(211400, 210000, "葫芦岛", "辽宁", "L"));
        cityMap.put(540100, new City(540100, 540000, "拉萨", "西藏", "X"));
        cityMap.put(441800, new City(441800, 440000, "清远", "广东", "G"));
        cityMap.put(650200, new City(650200, 650000, "克拉玛依", "新疆", "X"));
        cityMap.put(441300, new City(441300, 440000, "惠州", "广东", "G"));
        cityMap.put(999900, new City(999900, 990000, "其它", "其它", "Q"));
        cityMap.put(360400, new City(360400, 360000, "九江", "江西", "J"));
        cityMap.put(654300, new City(654300, 650000, "阿勒泰", "新疆", "X"));
        cityMap.put(411600, new City(411600, 410000, "周口", "河南", "H"));
        cityMap.put(210900, new City(210900, 210000, "阜新", "辽宁", "L"));
        cityMap.put(411100, new City(411100, 410000, "漯河", "河南", "H"));
        cityMap.put(429021, new City(429021, 420000, "神农架", "湖北", "H"));
        cityMap.put(530900, new City(530900, 530000, "临沧", "云南", "Y"));
        cityMap.put(620500, new City(620500, 620000, "天水", "甘肃", "G"));
        cityMap.put(330200, new City(330200, 330000, "宁波", "浙江", "Z"));
        cityMap.put(710100, new City(710100, 710000, "台湾", "台湾", "T"));
        cityMap.put(210400, new City(210400, 210000, "抚顺", "辽宁", "L"));
        cityMap.put(632300, new City(632300, 630000, "黄南", "青海", "Q"));
        cityMap.put(440800, new City(440800, 440000, "湛江", "广东", "G"));
        cityMap.put(150500, new City(150500, 150000, "通辽", "内蒙古", "N"));
        cityMap.put(530400, new City(530400, 530000, "玉溪", "云南", "Y"));
        cityMap.put(350700, new City(350700, 350000, "南平", "福建", "F"));
        cityMap.put(632800, new City(632800, 630000, "海西", "青海", "Q"));
        cityMap.put(440300, new City(440300, 440000, "深圳", "广东", "G"));
        cityMap.put(321000, new City(321000, 320000, "扬州", "江苏", "J"));
        cityMap.put(410600, new City(410600, 410000, "鹤壁", "河南", "H"));
        cityMap.put(320500, new City(320500, 320000, "苏州", "江苏", "J"));
        cityMap.put(371700, new City(371700, 370000, "菏泽", "山东", "S"));
        cityMap.put(410100, new City(410100, 410000, "郑州", "河南", "H"));
        cityMap.put(610300, new City(610300, 610000, "宝鸡", "陕西", "S"));
        cityMap.put(230900, new City(230900, 230000, "七台河", "黑龙江", "H"));
        cityMap.put(341500, new City(341500, 340000, "六安", "安徽", "A"));
        cityMap.put(610800, new City(610800, 610000, "榆林", "陕西", "S"));
        cityMap.put(431100, new City(431100, 430000, "永州", "湖南", "H"));
        cityMap.put(350200, new City(350200, 350000, "厦门", "福建", "F"));
        cityMap.put(640500, new City(640500, 640000, "中卫", "宁夏", "N"));
    }
}
