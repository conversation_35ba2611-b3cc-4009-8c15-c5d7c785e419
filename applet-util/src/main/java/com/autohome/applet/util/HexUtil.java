package com.autohome.applet.util;

public class HexUtil {

    private static final String HEX_CHARS = "0123456789ABCDEF";

    public static String bytes2HexStr(byte[] src){

        StringBuilder stringBuilder = new StringBuilder();

        for (int i = 0; i < src.length; i++) {

            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }

            stringBuilder.append(hv);
        }

        return stringBuilder.toString();
    }

    public static byte[] hexStr2Bytes(String hexString) {

        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];

        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }

        return d;
    }

    private static byte charToByte(char c) {
        return (byte) HEX_CHARS.indexOf(c);
    }
}

