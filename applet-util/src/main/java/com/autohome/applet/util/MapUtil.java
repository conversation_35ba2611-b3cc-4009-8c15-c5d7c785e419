package com.autohome.applet.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MapUtil {
    /**
     * 转换执行参数
     * k1=v1&k2=v2&k3=v3
     * @param s
     * @return
     */
    public static Map<String, String> parseExecParamForm(String s){
        if (StringUtils.isBlank(s)) {
            return Collections.emptyMap();
        }
        Map<String, String> map = new HashMap<>();
        String[] params = s.split("\\&");
        for (String param : params) {
            String[] tmp = param.split("\\=");
            map.put(tmp[0], tmp[1]);
        }
        return map;
    }
}
