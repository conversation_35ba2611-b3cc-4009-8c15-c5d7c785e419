package com.autohome.applet.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.lang.NonNull;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Stopwatch;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 如果需要开启sentinel okhttp组件流控和熔断，请配置spring.cloud.sentinel.enabled=true，然后配置好自己的sentinel相关配置
 * 如果配置了spring.cloud.sentinel.enabled=true，会实例化SentinelOkHttpInterceptor这个拦截器，拦截所有的okhttp请求，资源名为请求路径，不包含querystring
 * 目前项目中都是HttpHelper.getInstance()来做的，这块代码需要适配上spring才行，所以只能加上：public HttpHelper的构造方法
 * 为了适配代码，只能这么写了，如果觉得low或者有更好的方式，请联系我：pengyi
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "spring.cloud.sentinel", name = "enabled",havingValue = "true")
public class HttpHelper {
    final static ConcurrentHashMap<Long, HttpHelper> map = new ConcurrentHashMap<>();
    final static ConnectionPool connectionPool = new ConnectionPool(10, 5, TimeUnit.MINUTES);
    OkHttpClient okHttpClient;


    /**
     * 构建OkHttpClient
     *
     * @param millisecond
     * @return
     */
    public static HttpHelper getInstance(long millisecond) {
        HttpHelper httpHelper = map.computeIfAbsent(millisecond, k -> {
            OkHttpClient.Builder builder = new OkHttpClient.Builder()
                    .connectionPool(connectionPool)
                    .connectTimeout(millisecond, TimeUnit.MILLISECONDS)
                    .readTimeout(millisecond, TimeUnit.MILLISECONDS)
                    .writeTimeout(millisecond, TimeUnit.MILLISECONDS);
            return new HttpHelper(builder.build());
        });

        return httpHelper;
    }

    public static HttpHelper getInstance() {
        return getInstance(3000);
    }

    private HttpHelper(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }

    public HttpHelper(){

    }

    public @NonNull HttpResult httpGet(String url) {
        return httpGet(url, null, null);
    }

    public @NonNull HttpResult httpGet(String url, Map<String, Object> fields) {
        return httpGet(url, fields, null);
    }

    public @NonNull HttpResult httpGet(String url, Map<String, Object> fields, Map<String, Object> headers) {
        return httpGet(url, fields, headers, "utf-8");
    }

    public HttpResult httpGet(String url, Map<String, Object> fields, Map<String, Object> headers, String encoding) {
        Request.Builder requestBuilder = new Request.Builder();

        url = complementUrlScheme(url);

        if (fields != null && fields.size() > 0) {
            String query = URLEncodedUtils.format(fields.entrySet().stream().map(p -> new BasicNameValuePair(p.getKey(), (p.getValue() == null ? "" : p.getValue().toString()))).collect(Collectors.toList()), Charset.forName("utf-8"));
//            query = query.replaceAll("\\+", "%20");
            if (URI.create(url).getQuery() != null && !URI.create(url).getQuery().trim().equals(""))
                url += "&" + query;
            else
                url += "?" + query;
        }

        if (headers != null) {
            headers.forEach((key, value) -> {
                if (key != null && value != null)
                    requestBuilder.header(key, value.toString());
            });
        }

        Request request = requestBuilder
                .url(url)
                .build();
        log.info("GET request url:{}", url);
        return call(request, encoding);
    }


    public <T, K> T httpGet(String url, Map<String, Object> fields, Map<String, Object> headers, Class<T> parametrized, Class<K> parameterClass) {
        HttpResult httpGet = httpGet(url, fields, headers);
        if (httpGet != null) {
            return getResultFormJson(httpGet, parametrized, parameterClass);
        }
        return null;
    }

    public <T> T httpGet(String url, Map<String, Object> fields, Map<String, Object> headers, TypeReference<T> typeReference) {
        HttpResult httpGet = httpGet(url, fields, headers);
        if (httpGet != null) {
            return getResultFormJson(httpGet, typeReference);
        }
        return null;
    }

    public @NonNull HttpResult httpGetReportException(String url, Map<String, Object> fields) {
        return httpGetReportException(url, fields, null);
    }

    public @NonNull HttpResult httpGetReportException(String url, Map<String, Object> fields, Map<String, Object> headers) {
        HttpResult result = null;
        try {
            result = httpGet(url, fields, headers, "utf-8");
            if(ObjectUtils.isEmpty(result) || result.getStatusCode() != HttpStatus.SC_OK){
                log.error(String.format("HttpHelper: 获取HTTP服务数据异常。, url: %s, statusCode: %d ", url, result == null ? -1 : result.getStatusCode()));
                throw new RuntimeException("服务响应数据或状态码异常");
            }
        } catch (Exception ex) {
            log.error(String.format("{ HttpHelper: 获取HTTP服务数据异常。, url: %s }", url), ex);
        }
        return result;
    }

    public <T, K> T getResultFormJson(HttpResult httpResult, Class<T> parametrized, Class<K> parameterClass) {
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            if (parameterClass == null) {
                return JacksonHelper.deserialize(httpResult.getBody(), parametrized);
            }
            return JacksonHelper.deserialize(httpResult.getBody(), parametrized, parameterClass);
        }
        return null;
    }

    public static <T> T getResultFormJson(HttpResult httpResult, TypeReference<T> typeReference) {
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            return JacksonHelper.deserialize(httpResult.getBody(), typeReference);
        }
        return null;
    }


    public <T, K> T httpPost(String url, Map<String, Object> fields, Map<String, Object> headers, Class<T> parametrized, Class<K> parameterClass) {
        HttpResult httpPost = httpPost(url, fields, headers);
        if (httpPost != null) {
            return getResultFormJson(httpPost, parametrized, parameterClass);
        }
        return null;
    }

    public <T, K> T httpPost(String url, Map<String, Object> fields, Map<String, Object> headers, Class<T> parametrized) {
        HttpResult httpPost = httpPost(url, fields, headers);
        if (httpPost != null) {
            return getResultFormJson(httpPost, parametrized, null);
        }
        return null;
    }


    private static String complementUrlScheme(String url) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "http://" + url;
        }
        return url;
    }

    private @NonNull HttpResult call(Request request) {
        return call(request, null);
    }

    private HttpResult call(Request request, String encoding) {
        String url = request.url().toString();
        IOException ioException = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        Call call = okHttpClient.newCall(request);
        HttpResult build = HttpResult.build(0, null);

        try {
            Response response = call.execute();
            if (encoding == null) {
                build = HttpResult.build(response.code(), response.body().string());
            } else {
                build = HttpResult.build(response.code(), new String(response.body().bytes(), encoding));
            }

            return build;
        } catch (IOException e) {
            ioException = e;
        } finally {
            stopwatch.stop();
            long duration = stopwatch.elapsed(TimeUnit.MILLISECONDS);

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(request.method().toUpperCase()).append(" url:").append(url).append("\t");
            stringBuilder.append("costs:").append(duration).append("ms").append("\t");
            // 请求耗时大于100毫秒的记录日志
            stringBuilder.append("responseCode: ").append(build.getStatusCode()).append("\n")
                    .append("responseBody: ").append(build.getBody());

            if (ioException != null) {
                stringBuilder.append("\n").append("error: ").append(ioException.getMessage());
            }
            if (build.getStatusCode() != 200) {
                log.error(stringBuilder.toString());
            } else if (duration > 1000) {
                log.warn(stringBuilder.toString());
            } else if (duration > 300) {
                log.warn(stringBuilder.toString());
            } else if (duration > 100) {
                log.warn(stringBuilder.toString());
            } else {
                log.info(stringBuilder.toString());
            }

        }
        return build;
    }

    public @NonNull HttpResult httpPost(String url, Map<String, Object> fields) {
        return httpPost(url, fields, null);
    }

    public @NonNull HttpResult httpPostJson(String url, String body) {
        return httpPostJson(url, body, null);
    }

    public @NonNull HttpResult httpPostJson(String url, String body, Map<String, Object> headers) {
        url = complementUrlScheme(url);
        Request.Builder requestBuilder = new Request.Builder();

        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), body);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) -> {
                if (key != null && value != null)
                    requestBuilder.header(key, value.toString());
            });
        }

        Request request = requestBuilder
                .url(url)
                .post(reqBody)
                .build();
        log.debug("request url:{}", url);
        return call(request);
    }

    public @NonNull HttpResult httpPostJsonV2(String url, String body, Map<String, Object> headers) {
        url = complementUrlScheme(url);
        Request.Builder requestBuilder = new Request.Builder();

        RequestBody reqBody = RequestBody.create(null, body);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) -> {
                if (key != null && value != null)
                    requestBuilder.header(key, value.toString());
            });
        }

        Request request = requestBuilder
                .url(url)
                .post(reqBody)
                .build();
        log.debug("request url:{}", url);
        return call(request);
    }

    public @NonNull HttpResult httpPost(String url, Map<String, Object> fields, Map<String, Object> headers) {
        url = complementUrlScheme(url);
        Request.Builder requestBuilder = new Request.Builder();
        FormBody.Builder bodyBuilder = new FormBody.Builder();
        if (fields != null) {
            fields.forEach((key, value) -> bodyBuilder.add(key, (value == null ? "" : value.toString())));
        }
        if (headers != null) {
            headers.forEach((key, value) -> {
                if (key != null && value != null)
                    requestBuilder.header(key, value.toString());
            });
        }

        Request request = requestBuilder
                .url(url)
                .post(bodyBuilder.build())
                .build();
        log.debug("POST request url: {}, BODY: {}", url, fields != null ? params(fields) : "");
        return call(request);
    }


    public static class HttpResult {
        private int httpStatusCode;
        private String body;

        private HttpResult(int httpStatusCode, String body) {
            this.httpStatusCode = httpStatusCode;
            this.body = body;
        }

        public boolean isSuccess() {
            return httpStatusCode == 200 && body != null;
        }

        public <T> T deserialize(Class<T> clazz) {
            return JacksonHelper.deserialize(body, clazz);
        }

        public <T> T deserialize(TypeReference<T> typeReference) {
            return JacksonHelper.deserialize(body, typeReference);
        }

        public int getStatusCode() {
            return httpStatusCode;
        }

        public String getBody() {
            return body;
        }

        public static HttpResult build(int httpStatusCode, String body) {
            return new HttpResult(httpStatusCode, body);
        }
    }

    public static String params(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        if (params != null && params.size() > 0) {
            Set<Map.Entry<String, Object>> entrySet = params.entrySet();

            for (Map.Entry<String, Object> entry : entrySet) {
                sb.append(entry.getKey());
                sb.append("=");
                try {
                    String value = entry.getValue() == null ? "" : entry.getValue().toString();
                    sb.append(URLEncoder.encode(value, "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                }
                sb.append("&");
            }
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }


}
