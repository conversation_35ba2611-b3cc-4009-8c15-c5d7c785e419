package com.autohome.applet.util;

import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;

import java.util.regex.Pattern;

public class StringHelper {
    /**
     * 字符串合并方法，返回一个合并后的字符串,像.net里的format函数
     *
     * @param str
     * @param args
     * @return
     */
    public static String fmt(String str, Object... args) {
        if (str == null || "".equals(str) || args.length == 0) {
            return str;
        }

        String result = str.intern();
        Pattern p = Pattern.compile("\\{(\\d+)\\}");
        java.util.regex.Matcher m = p.matcher(str);

        while (m.find()) {
            int index = Integer.parseInt(m.group(1));
            if (index < args.length) {
                result = result.replace(m.group(), args[index] == null ? "" : args[index].toString());
            }
        }
        return result;
    }

    public final static String md5(String data) {
        Hasher hasher = Hashing.md5().newHasher();
        hasher.putString(data, Charsets.UTF_8);
        return hasher.hash().toString().toUpperCase();
    }

    public final static String phoneDesensitization(String phone) {
        if (Strings.isNullOrEmpty(phone)) {
            return "";
        }
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}
