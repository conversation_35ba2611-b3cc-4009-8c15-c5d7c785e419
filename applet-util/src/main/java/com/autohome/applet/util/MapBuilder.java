package com.autohome.applet.util;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * A {@code Builder} pattern implementation for the {@link Map}.
 *
 * @param <K> The Map key type.
 * @param <V> The Map value type.
 * <AUTHOR> Bilan
 * @since 5.0
 */
public class MapBuilder<K, V> {
    private final Map<K, V> map = new LinkedHashMap<>();
    /***
     * 泛型类型参数key,保证类型安全
     */
    private Class<K> key;
    /***
     * 泛型类型参数value,保证类型安全
     */
    private Class<V> value;

    public MapBuilder(Class<K> kClass, Class<V> vClass) {
        this.key = kClass;  // NOSONAR
        this.value = vClass;   // NOSONAR
    }

    public MapBuilder() {
    }

    public static <K, V> MapBuilder<K, V> newInstance() {
        return new MapBuilder<>();
    }

    public static <K, V> MapBuilder<K, V> newInstance(Class<K> kClass, Class<V> vClass) {
        return new MapBuilder<>(kClass, vClass);
    }

    public MapBuilder<K, V> put(K key, V value) {
        this.map.put(key, value);
        return _this();
    }

    public Map<K, V> build() {
        return this.map;
    }

    @SuppressWarnings("unchecked")
    protected final MapBuilder<K, V> _this() { // NOSONAR name
        return this;
    }

}