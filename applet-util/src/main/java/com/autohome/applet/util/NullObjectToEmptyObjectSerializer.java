package com.autohome.applet.util;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

public class NullObjectToEmptyObjectSerializer extends JsonSerializer<Object> {

    @Override
    public void serialize(Object value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {

        if (value == null) {
            //jsonGenerator.writeString("{}");
            jsonGenerator.writeEndObject();
        }
//        else {
//
//            ObjectMapper mapper = new ObjectMapper();
//            String res = mapper.writeValueAsString(value);
//            jsonGenerator.writeString(res);
//        }

    }
}
