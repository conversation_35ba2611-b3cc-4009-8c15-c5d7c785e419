package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;

import com.autohome.applet.util.netcoreapi.StringUtil;

import java.math.BigDecimal;
import java.text.DecimalFormat;

import static com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.TypeParseUtils.parseDoubleWithScale;

/**
 * Created by hanshanfeng on 2017/12/22.
 */
public class CarPriceUtils {

    public static String getStr() {
        return "2.0升";
    }

    public static String getDeliveryCapacityName(String dc, int fuelType) {
        DecimalFormat df = new DecimalFormat("0.0");
        BigDecimal dcBD = new BigDecimal(dc);
        if (dc.equals(BigDecimal.ZERO)) {
            return fuelType == 4 ? "电动" : "";
        } else {
            return df.format(dcBD) + "升";
        }
    }

    // 将元转为万元保留两位小数
    // <param name="price">价格</param>
    public static BigDecimal getBigDecimalPrice(String price) {
        if (price == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal p = new BigDecimal(price).divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        return p;

    }

    public static BigDecimal getBigDecimalPrice(int price) {
        if (price == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal p = new BigDecimal(price).divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        return p;

    }

    /**
     * @param price
     * @return
     */
    public static String getFormateStrPrice(String price) {
        try {
            if (!StringUtil.isNotNullAndWhiteSpace(price)) {
                return "0";
            }
            DecimalFormat df = new DecimalFormat("0.00");
            BigDecimal min = new BigDecimal(price);
            return df.format(min);
        } catch (Exception e) {
            return "";
        }
    }


    /**
     * 车系关注人数
     *
     * @param insightCount
     * @return
     */
    public static String getInsightCount(String insightCount) {
        DecimalFormat df = new DecimalFormat("0.00");
        BigDecimal min = getBigDecimalPrice(insightCount);
        BigDecimal thousand = new BigDecimal("1000");
        if (min.compareTo(BigDecimal.ZERO) == 0) {
            return "暂无数据";
        } else {
            return df.format(min) + "万";
        }
    }

    public static String GetDifferenceFormat(int minprice, int maxprice) {

        if (minprice <= 0 || maxprice <= 0 || minprice == maxprice || minprice > maxprice) {
            return "0";
        }
        //else if (minprice == 0 || maxprice == 0)
        //{
        //    price = ((minprice + maxprice) / 10000.0).ToString("F2") + "万";
        //}
        //else if (minprice == maxprice)
        //{
        //    price = (maxprice / 10000.0).ToString("F2") + "万";
        //}
        else {
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(getBigDecimalPrice(((maxprice - minprice)))) + "万";
            // price = (minprice / 10000.0).ToString("F2") + "-" + (maxprice / 10000.0).ToString("F2") + "万";
        }
    }

    public static String getStrPrice(String minPrice, String maxPrice) {
        DecimalFormat df = new DecimalFormat("0.00");
        BigDecimal min = getBigDecimalPrice(minPrice);
        BigDecimal max = getBigDecimalPrice(maxPrice);
        BigDecimal thousand = new BigDecimal("1000");
        if (min.compareTo(BigDecimal.ZERO) == 0 && max.compareTo(BigDecimal.ZERO) == 0) {
            return "暂无报价";
        } else {
            if (min.equals(max)) {
                return df.format(min) + "万";
            } else {
                if (min.compareTo(thousand) >= 0 && max.compareTo(thousand) < 0) {
                    return min.toString() + "-" + df.format(max) + "万";
                } else if (min.compareTo(thousand) < 0 && max.compareTo(thousand) >= 0) {
                    return df.format(min) + "-" + max.toString() + "万";
                } else if (min.compareTo(thousand) >= 0 && min.compareTo(thousand) >= 0) {
                    return min.toString() + "-" + max.toString() + "万";
                } else
                    return df.format(min) + "-" + df.format(max) + "万";
            }
        }
    }

    public static String getStrPrice(int minPrice, int maxPrice) {
        DecimalFormat df = new DecimalFormat("0.00");
        BigDecimal min = getBigDecimalPrice(minPrice);
        BigDecimal max = getBigDecimalPrice(maxPrice);
        BigDecimal thousand = new BigDecimal("1000");
        if (min.compareTo(BigDecimal.ZERO) == 0 && max.compareTo(BigDecimal.ZERO) == 0) {
            return "暂无报价";
        } else {
            if (min.equals(max)) {
                return df.format(min) + "万";
            } else {
                if (min.compareTo(thousand) >= 0 && max.compareTo(thousand) < 0) {
                    return min.toString() + "-" + df.format(max) + "万";
                } else if (min.compareTo(thousand) < 0 && max.compareTo(thousand) >= 0) {
                    return df.format(min) + "-" + max.toString() + "万";
                } else if (min.compareTo(thousand) >= 0 && min.compareTo(thousand) >= 0) {
                    return min.toString() + "-" + max.toString() + "万";
                } else
                    return df.format(min) + "-" + df.format(max) + "万";
            }
        }
    }

    /**
     * 格式化价格
     *
     * @param minPrice
     * @param maxPrice
     * @return
     */
    public static String GetSpecPrice(Object minPrice, Object maxPrice) {
        double min = parseDoubleWithScale(Double.parseDouble(minPrice.toString()) / 10000);
        double max = parseDoubleWithScale(Double.parseDouble(maxPrice.toString()) / 10000);

        //System.out.println("min" + Double.toString(min));
        //System.out.println("max" + Double.toString(max));
        DecimalFormat df = new DecimalFormat("#####0.00");
        DecimalFormat dfNoFloat = new DecimalFormat("#####0");
        if (min == 0 || max == 0) {
            return "暂无报价";
        } else {
            if (min == max) {
                return df.format(min) + "万";
            } else {
                if (min >= 1000 && max < 1000) {
                    return dfNoFloat.format(min) + "-" + df.format(max) + "万";
                } else if (min < 1000 && max >= 1000) {
                    return df.format(min) + "-" + dfNoFloat.format(max) + "万";
                } else if (min >= 1000 && max >= 1000) {
                    return dfNoFloat.format(min) + "-" + dfNoFloat.format(max) + "万";
                } else {
                    return df.format(min) + "-" + df.format(max) + "万";
                }
            }
        }
    }

    /**
     * 格式化价格, 返回 ####.00万起
     * */
    public static String getStrPrice(int minPrice) {
        DecimalFormat df = new DecimalFormat("0.00");
        BigDecimal min = getBigDecimalPrice(minPrice);
        BigDecimal thousand = new BigDecimal("1000");
        if (min.compareTo(BigDecimal.ZERO) == 0 ) {
            return "暂无报价";
        } else {
            return min.toString() + "万起";
        }
    }
}
