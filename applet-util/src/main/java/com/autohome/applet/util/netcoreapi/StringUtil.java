package com.autohome.applet.util.netcoreapi;


import java.security.MessageDigest;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;

/**
 * Create By Qiuximing On: 2018/3/7
 */
public class StringUtil {
    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public static String signString(String salt, SortedMap<String, String> map) {
        StringBuilder sb = new StringBuilder();
        sb.append(salt);
        for (Map.Entry<String, String> me : map.entrySet()) {
            sb.append(me.getKey() + me.getValue());
        }
        sb.append(salt);
        return md5(sb.toString());
    }

    public static String md5(String data) {
        return encode("md5", data).toUpperCase();
    }


    private static String encode(String algorithm, String value) {
        if (value == null) {
            return null;
        }
        try {
            MessageDigest messageDigest
                    = MessageDigest.getInstance(algorithm);
            messageDigest.update(value.getBytes());
            return getFormattedText(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String getFormattedText(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        for (int j = 0; j < len; j++) {
            buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
        }
        return buf.toString();
    }

    /**
     * Trim leading and trailing whitespace from the given {@code String}.
     *
     * @param str the {@code String} to check
     * @return the trimmed {@code String}
     * @see Character#isWhitespace
     */
    public static String trimWhitespace(String str) {
        if (!hasLength(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder(str);
        while (sb.length() > 0 && Character.isWhitespace(sb.charAt(0))) {
            sb.deleteCharAt(0);
        }
        while (sb.length() > 0 && Character.isWhitespace(sb.charAt(sb.length() - 1))) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * Check that the given {@code String} is neither {@code null} nor of length 0.
     * <p>Note: this method returns {@code true} for a {@code String} that
     * purely consists of whitespace.
     *
     * @param str the {@code String} to check (may be {@code null})
     * @return {@code true} if the {@code String} is not {@code null} and has length
     */
    public static boolean hasLength(String str) {
        return (str != null && str.length() > 0);
    }

    /**
     * Check whether the given {@code String} contains actual <em>text</em>.
     * <p>More specifically, this method returns {@code true} if the
     * {@code String} is not {@code null}, its length is greater than 0,
     * and it contains at least one non-whitespace character.
     *
     * @param str the {@code String} to check (may be {@code null})
     * @return {@code true} if the {@code String} is not {@code null}, its
     * length is greater than 0, and it does not contain whitespace only
     */
    public static boolean hasText(String str) {
        return hasLength(trimWhitespace(str));
    }

    public static boolean isNullOrEmpty(String string) {
        return string == null || string.length() == 0; // string.isEmpty() in Java 6
    }

    public static boolean isEmpty(String string) {
        return isNullOrEmpty(string);
    }

    public static boolean isNotEmpty(String string) {
        return !isNullOrEmpty(string);
    }

    public static String trimToEmpty(String str) {
        return str == null ? "" : str.trim();
    }

    public static boolean isBlank(CharSequence cs) {
        int strLen;
        if (cs != null && (strLen = cs.length()) != 0) {
            for (int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(cs.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return true;
        }
    }

    public static boolean isNotBlank(CharSequence cs) {
        return !isBlank(cs);
    }

    public static String listToString(List list, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            sb.append(list.get(i)).append(separator);
        }
        return sb.toString().substring(0, sb.toString().length() - 1);
    }

    public static String subString(String text, int maxSize) {
        if (text == null) return "";
        return text.length() > maxSize ? text.substring(0, maxSize) : text;
    }


    public static boolean isNotNullAndWhiteSpace(String str) {
        return str != null && str.trim().length() > 0;
    }
//    public static boolean isNotEmpty(String str) {
//        return str!=null && str.trim().length() > 0;
//    }

    public static boolean isBlank(String str) {
        return str == null || str.trim().length() == 0;
    }

    public static String trimStart(String s1, char c) {
        char[] c1 = s1.toCharArray();
        int s = 0;

        for (int i = 0; i < c1.length; i++) {
            if (c1[i] != c) {
                s = i;//第一个非空格元素角标
                break;
            }
        }

        char[] cc = new char[s + 1];
        System.arraycopy(c1, s, cc, 0, s + 1);
        String s3 = new String(cc);
        return s3;
    }
}
