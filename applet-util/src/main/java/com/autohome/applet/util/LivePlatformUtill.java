package com.autohome.applet.util;

import com.autohome.applet.util.netcoreapi.StringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LivePlatformUtill {

    public static String matchDomain(String url) {
        if (StringUtil.isEmpty(url)) {
            return "";
        }
        int len = "//".length();
        int start = url.indexOf("//");
        if (start < 0) {
            return "";
        }
        int end = url.indexOf("/", start + len);
        if (end < 0) {
            return "";
        }
        String str = url.substring(start + len, end);
        return str;
    }

    public static Integer safeToInteger(Integer def, String intStr) {
        try {
            return Integer.valueOf(intStr);
        } catch (NumberFormatException e) {
            log.error(e.getLocalizedMessage(), e);
            if (def == null) {
                return Integer.valueOf("0");
            }
            return def;
        }
    }

    public static Long safeToLong(Long def, String longStr) {
        try {
            return Long.valueOf(longStr);
        } catch (NumberFormatException e) {
            log.error(e.getLocalizedMessage(), e);
            if (def == null) {
                return Long.valueOf("0");
            }
            return def;
        }
    }
}
