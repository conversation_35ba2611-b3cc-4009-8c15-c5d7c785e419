package com.autohome.applet.util;

import java.io.*;
import java.util.HashMap;

public class FileUtil {
    /**
     *
     * @param fileName
     * @return
     */
    public static HashMap<Integer, String> readFileContentMap(String fileName) {
        HashMap<Integer, String> flowcodeSet = new HashMap<Integer, String>();
        BufferedReader reader = null;
        StringBuffer sbf = new StringBuffer();
        try {
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream(fileName);
            reader = new BufferedReader(new InputStreamReader(path,"utf-8"));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {

                flowcodeSet.put(flowcodeSet.size(), tempStr);
            }
            reader.close();
            return flowcodeSet;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return flowcodeSet;
    }

    public static InputStream getResourcesFileInputStream(String fileName) {
        return Thread.currentThread().getContextClassLoader().getResourceAsStream("" + fileName);
    }

    public static String getPath() {
        return FileUtil.class.getResource("/").getPath();
    }

    public static File createNewFile(String pathName) {
        File file = new File(getPath() + pathName);
        if (file.exists()) {
            file.delete();
        } else {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
        }
        return file;
    }

    public static File readFile(String pathName) {
        return new File(getPath() + pathName);
    }

    public static File readUserHomeFile(String pathName) {
        return new File(System.getProperty("user.home") + File.separator + pathName);
    }
}

