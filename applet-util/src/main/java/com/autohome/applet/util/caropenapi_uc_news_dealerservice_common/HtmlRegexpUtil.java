package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HtmlRegexpUtil {
    private final static String regxpForHtml = "<([^>]*)>"; // 过滤所有以<开头以>结尾的标签

    private final static String regxpForImgTag = "<\\s*img\\s+([^>]*)\\s*>"; // 找出IMG标签

    private final static String regxpForImaTagSrcAttrib = "src=\"([^\"]+)\""; // 找出IMG标签的SRC属性

    /**
     *
     */
    public HtmlRegexpUtil() {
        // TODO Auto-generated constructor stub
    }

    /**
     * 基本功能：替换标记以正常显示
     * <p>
     *
     * @param input
     * @return String
     */
    public String replaceTag(String input) {
        if (!hasSpecialChars(input)) {
            return input;
        }
        StringBuffer filtered = new StringBuffer(input.length());
        char c;
        for (int i = 0; i <= input.length() - 1; i++) {
            c = input.charAt(i);
            switch (c) {
                case '<':
                    filtered.append("&lt;");
                    break;
                case '>':
                    filtered.append("&gt;");
                    break;
                case '"':
                    filtered.append("&quot;");
                    break;
                case '&':
                    filtered.append("&amp;");
                    break;
                default:
                    filtered.append(c);
            }

        }
        return (filtered.toString());
    }

    /**
     * 基本功能：判断标记是否存在
     * <p>
     *
     * @param input
     * @return boolean
     */
    public boolean hasSpecialChars(String input) {
        boolean flag = false;
        if ((input != null) && (input.length() > 0)) {
            char c;
            for (int i = 0; i <= input.length() - 1; i++) {
                c = input.charAt(i);
                switch (c) {
                    case '>':
                        flag = true;
                        break;
                    case '<':
                        flag = true;
                        break;
                    case '"':
                        flag = true;
                        break;
                    case '&':
                        flag = true;
                        break;
                }
            }
        }
        return flag;
    }

    /**
     * 基本功能：过滤所有以"<"开头以">"结尾的标签
     * <p>
     *
     * @param str
     * @return String
     */
    public static String filterHtml(String str) {
        Pattern pattern = Pattern.compile(regxpForHtml);
        Matcher matcher = pattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        boolean result1 = matcher.find();
        while (result1) {
            matcher.appendReplacement(sb, "");
            result1 = matcher.find();
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 基本功能：过滤指定标签
     * <p>
     *
     * @param str
     * @param tag 指定标签
     * @return String
     */
    public static String fiterHtmlTag(String str, String tag) {
        String regxp = "<\\s*" + tag + "\\s+([^>]*)\\s*>";
        Pattern pattern = Pattern.compile(regxp);
        Matcher matcher = pattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        boolean result1 = matcher.find();
        while (result1) {
            matcher.appendReplacement(sb, "");
            result1 = matcher.find();
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static String filterHtmlAttr(String str, String regEx) {
//        String regEx = "style=\"(.*?)\"";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        boolean result1 = matcher.find();
        while (result1) {
            matcher.appendReplacement(sb, "");
            result1 = matcher.find();
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 基本功能：替换指定的标签
     * <p>
     *
     * @param str
     * @param beforeTag 要替换的标签
     * @param tagAttrib 要替换的标签属性值
     * @param startTag  新标签开始标记
     * @param endTag    新标签结束标记
     * @return String
     * @如：替换img标签的src属性值为[img]属性值[/img]
     */
    public static String replaceHtmlTag(String str, String beforeTag,
                                        String tagAttrib, String startTag, String endTag) {
        String regxpForTag = "<\\s*" + beforeTag + "\\s+([^>]*)\\s*>";
        String regxpForTagAttrib = tagAttrib + "=\"([^\"]+)\"";
        Pattern patternForTag = Pattern.compile(regxpForTag);
        Pattern patternForAttrib = Pattern.compile(regxpForTagAttrib);
        Matcher matcherForTag = patternForTag.matcher(str);
        StringBuffer sb = new StringBuffer();
        boolean result = matcherForTag.find();
        while (result) {
            StringBuffer sbreplace = new StringBuffer();
            Matcher matcherForAttrib = patternForAttrib.matcher(matcherForTag
                    .group(1));
            if (matcherForAttrib.find()) {
                matcherForAttrib.appendReplacement(sbreplace, startTag
                        + matcherForAttrib.group(1) + endTag);
            }
            matcherForTag.appendReplacement(sb, sbreplace.toString());
            result = matcherForTag.find();
        }
        matcherForTag.appendTail(sb);
        return sb.toString();
    }


    public static void main(String[] arg) {
        String str = "<p align=\"center\" class=\"contimg\"><img alt=\"\" imageid=\"45068534\" imagestate=\"2\" src=\"https://dealer2.autoimg.cn/dealerdfs/g27/M0A/58/10/620x0_1_q87_autohomedealer__wKgHE1tfr0yAU-xUAAjc5_1yVKw496.jpg\" /></p>\n" +
                "<p microsoft=\"\" style=\"margin-top: 0px; margin-bottom: 10px; padding: 0px; line-height: 28px; color: rgb(51, 51, 51); font-family: \">保定轩宇陆威汽车销售服务有限公司成立于2009年8月，位于保定高碑店市盛世北大街16号。轩宇陆威雪佛兰4S店隶属于保定轩宇汽车集团，是集汽车销售、汽车维修、零部件供应、汽车装修装饰、汽车消费贷款等服务功能于一身，为广大客户提供全方位汽车销售与售后服务。</p>\n" +
                "<p microsoft=\"\" style=\"margin-top: 0px; margin-bottom: 10px; padding: 0px; line-height: 28px; color: rgb(51, 51, 51); font-family: \">历时九年的发展，轩宇陆威雪佛兰4S店培养了一批销售精英和售后服务技师，我们为每一位客户提供专业的购车建议和售后服务支持。</p>\n" +
                "<p microsoft=\"\" style=\"margin-top: 0px; margin-bottom: 10px; padding: 0px; line-height: 28px; color: rgb(51, 51, 51); font-family: \">轩宇陆威秉承轩宇汽车集团德、信、和的经营理念，尊重、公平、正直、诚信的价值观，以专业品质，与您共享美好汽车生活。轩宇陆威店全体员工竭诚为您服务。&nbsp;</p>\n" +
                "<p microsoft=\"\" style=\"margin-top: 0px; margin-bottom: 10px; padding: 0px; line-height: 28px; color: rgb(51, 51, 51); font-family: \">牵手轩宇雪佛兰，乐享全程关爱！</p>\n" +
                "<p style=\"text-align:center;\"><img alt=\"\" src=\"http://www.autoimg.cn/dealer/upload/dl/81617/newsimg/130670616403582700.jpg\" /></p>\n";


        System.out.println(filterHtmlAttr(str, "style=\\\"(.*?)\\\""));

    }
}
