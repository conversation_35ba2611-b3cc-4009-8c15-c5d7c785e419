package com.autohome.applet.util;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.net.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class IPHelper {

    private static final Pattern ipPattern = Pattern.compile(
            "\\b((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.(" +
            "(?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\b");
    private static final Pattern ipv4Pattern = Pattern.compile(
            "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$");
    private static final Pattern ipv6Pattern = Pattern.compile("^([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}$");

    /**
     * 判断一个IP是不是在一个网段下的
     */
    public static boolean isInRange(String ip, String cidr) {

        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            if (inetAddress instanceof Inet6Address) {
                return false;
            }

            if (!cidr.contains("/")) {
                cidr = StringHelper.fmt("{0}/32", ip);
            }

            String[] ips = ip.split("\\.");
            int ipAddr = (Integer.parseInt(ips[0]) << 24)
                         | (Integer.parseInt(ips[1]) << 16)
                         | (Integer.parseInt(ips[2]) << 8) | Integer.parseInt(ips[3]);
            int type = Integer.parseInt(cidr.replaceAll(".*/", ""));
            int mask = 0xFFFFFFFF << (32 - type);
            String cidrIp = cidr.replaceAll("/.*", "");
            String[] cidrIps = cidrIp.split("\\.");
            int cidrIpAddr = (Integer.parseInt(cidrIps[0]) << 24)
                             | (Integer.parseInt(cidrIps[1]) << 16)
                             | (Integer.parseInt(cidrIps[2]) << 8)
                             | Integer.parseInt(cidrIps[3]);

            return (ipAddr & mask) == (cidrIpAddr & mask);
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 是否是局域网ip
     *
     * @param ip
     * @return
     */
    public static boolean isLan(String ip) {
        return Strings.isNullOrEmpty(ip)
               ? false
               : (ip.equals("localhost")
                  || ip.equals("0:0:0:0:0:0:0:1")
                  || ip.startsWith("127.")
                  || ip.startsWith("10.")
                  || ip.startsWith("192.168.")
                  || Pattern.compile(
                                    "(10|172|192)\\\\.([0-1][0-9]{0,2}|[2][0-5]{0,2}|[3-9][0-9]{0,1})\\\\." +
                                    "([0-1][0-9]{0,2}|[2][0-5]{0,2}|[3-9][0-9]{0,1})\\\\.([0-1][0-9]{0,2}|[2][0-5]{0," +
                                    "2}|[3-9][0-9]{0,1})")
                            .matcher(ip).find());
    }

    public static boolean verifyIp(String ip) {
        return !Strings.isNullOrEmpty(ip) && ipPattern.matcher(ip).matches();
    }

    public static long ipToLong(String ip) {
        long[] array = new long[4];
        int position1 = ip.indexOf(".");
        int position2 = ip.indexOf(".", position1 + 1);
        int position3 = ip.indexOf(".", position2 + 1);
        array[0] = Long.parseLong(ip.substring(0, position1));
        array[1] = Long.parseLong(ip.substring(position1 + 1, position2));
        array[2] = Long.parseLong(ip.substring(position2 + 1, position3));
        array[3] = Long.parseLong(ip.substring(position3 + 1));
        return (array[0] << 24) + (array[1] << 16) + (array[2] << 8) + array[3];
    }

    /**
     * 判断是否是内网ip
     *
     * @param ip
     * @return
     */
    public static boolean isLanClient(String ip) {
        return Strings.isNullOrEmpty(ip)
               ? false
               : (ip.equals("localhost")
                  || ip.equals("0:0:0:0:0:0:0:1")
                  || ip.equals("*************")
                  || ip.equals("*************")
                  || ip.startsWith("127.")
                  || ip.startsWith("10.")
                  || ip.startsWith("192.168.")
                  || Pattern.compile(
                                    "(10|172|192)\\\\.([0-1][0-9]{0,2}|[2][0-5]{0,2}|[3-9][0-9]{0,1})\\\\." +
                                    "([0-1][0-9]{0,2}|[2][0-5]{0,2}|[3-9][0-9]{0,1})\\\\.([0-1][0-9]{0,2}|[2][0-5]{0," +
                                    "2}|[3-9][0-9]{0,1})")
                            .matcher(ip).find());
    }

    public static String ipToString(long ip) {
        StringBuilder sb = new StringBuilder(10);
        sb.append(String.valueOf(ip >>> 24));
        sb.append(".");
        sb.append(String.valueOf((ip & 16777215L) >>> 16));
        sb.append(".");
        sb.append(String.valueOf((ip & 65535L) >>> 8));
        sb.append(".");
        sb.append(String.valueOf(ip & 255L));
        return sb.toString();
    }

    public static boolean isIPV4(String addr) {
        if (null == addr) {
            return false;
        }
        Matcher mat = ipv4Pattern.matcher(addr);

        boolean ipAddress = mat.find();
        return ipAddress;
    }

    public static boolean isIPV6(String addr) {
        if (null == addr) {
            return false;
        }
        Matcher mat = ipv6Pattern.matcher(addr);

        boolean ipAddress = mat.find();
        return ipAddress;
    }

    public static Optional<String> getServerIp() {
        return getServerIpList().stream().map(ip -> {
            String[] ipArr = ip.split("\\.");
            if (ipArr.length == 4) {
                return String.format("%s.%s", ipArr[2], ipArr[3]);
            } else {
                return null;
            }
        }).filter(t -> !StringUtils.isEmpty(t)).reduce((a, b) -> String.format("%s|%s", a, b));
    }

    public static List<String> getServerIpList() {
        List<String> ipList = new ArrayList<>();

        try {
            Enumeration netInterfaces = NetworkInterface.getNetworkInterfaces();

            InetAddress ip = null;
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();

                if (ni.isLoopback() || ni.isPointToPoint() || !ni.isUp() || ni.isVirtual()) {
                    continue;
                }

                ip = ni.getInetAddresses().nextElement();

                if (ip instanceof Inet4Address && ip.isSiteLocalAddress()) {

                    String serverIp = ip.getHostAddress();

                    if (serverIp.indexOf(":") == -1) {
                        ipList.add(serverIp);
                    }
                } else {
                    ip = null;
                }
            }
        } catch (SocketException e) {
            log.error(e.getMessage(), e);
        }
        return ipList;
    }

    public static String getHostName() {
        try {
            return (InetAddress.getLocalHost()).getHostName();
        } catch (UnknownHostException uhe) {
            String host = uhe.getMessage(); // host = "hostname: hostname"
            if (host != null) {
                int colon = host.indexOf(':');
                if (colon > 0) {
                    return host.substring(0, colon);
                }
            }
            return "UnknownHost";
        }
    }
}