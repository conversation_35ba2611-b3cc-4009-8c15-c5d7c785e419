package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;


import java.io.*;
import java.text.MessageFormat;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/7.
 */
public class JaxbReadXml {
    private final static Logger LOGGER = LoggerFactory.getLogger(JaxbReadXml.class);

    @SuppressWarnings("unchecked")
    public static <T> T readString(Class<T> clazz, String context) throws JAXBException {
        InputStream inputStream = null;
        try {
            JAXBContext jc = JAXBContext.newInstance(clazz);
            Unmarshaller u = jc.createUnmarshaller();
            inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(context);
            return (T) u.unmarshal(inputStream);
        } catch (JAXBException e) {
            LOGGER.info("{\"JaxbReadXmlError\":\"{}\"}", e.getMessage());
            throw e;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * @param clazz
     * @param context
     * @param encode
     * @param <T>
     * @return
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public static <T> T readString(Class<T> clazz, String context, String encode)
            throws UnsupportedEncodingException, JAXBException {
        InputStream inputStream = null;
        try {
            inputStream = new ByteArrayInputStream(context.getBytes(encode));
            JAXBContext jc = JAXBContext.newInstance(clazz);
            Unmarshaller u = jc.createUnmarshaller();
            return (T) u.unmarshal(inputStream);
        } catch (JAXBException e) {
            LOGGER.info("{\"JaxbReadXmlError\":\"{}\"}", e.getMessage());
            throw e;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T readConfig(Class<T> clazz, String config, Object... arguments) throws IOException,
            JAXBException {
        InputStream is = null;
        try {
            if (arguments.length > 0) {
                config = MessageFormat.format(config, arguments);
            }
            // logger.trace("read configFileName=" + config);
            JAXBContext jc = JAXBContext.newInstance(clazz);
            Unmarshaller u = jc.createUnmarshaller();
            is = new FileInputStream(config);
            return (T) u.unmarshal(is);
        } catch (IOException e) {
            LOGGER.error(config, e);
            throw e;
        } catch (JAXBException e) {
            LOGGER.error(config, e);
            throw e;
        } finally {
            if (is != null) {
                is.close();
            }
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T readConfigFromStream(Class<T> clazz, InputStream dataStream) throws JAXBException {
        try {
            JAXBContext jc = JAXBContext.newInstance(clazz);
            Unmarshaller u = jc.createUnmarshaller();
            return (T) u.unmarshal(dataStream);
        } catch (JAXBException e) {
            LOGGER.info("{\"Error\":\"{}\"}", e.getMessage());
            throw e;
        }
    }
}
