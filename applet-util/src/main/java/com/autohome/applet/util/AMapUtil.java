package com.autohome.applet.util;

import java.io.*;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

/**
 * <AUTHOR>
 */
public class AMapUtil {

    /**
     * 使用商家私钥生成签名
     *
     * @param paramMap 待生成加密sign的参数集合
     * @return 生成的签名字符串
     * @throws Exception
     */
    public static String generateSign(Map<String, Object> paramMap, String privateKey) throws Exception {
        String signContent = getSignContent(paramMap);
        return getSign(signContent, privateKey);
    }

    /**
     * 参数转换为待加签字符串
     *
     * @param paramMap 待生成加密sign的参数集合
     * @return 待加签字符串
     */
    private static String getSignContent(Map<String, Object> paramMap) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<>(paramMap.keySet());
        Collections.sort(keys); // 将参数集合排序

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            //排除不需要参与签名的公共参数
            if ("sign_type".equals(key) || "sign".equals(key) || "need_encrypt".equals(key)) {
                continue;
            }
            Object value = paramMap.get(key);
            // 拼装所有非空参数
            if (key != null && !"".equalsIgnoreCase(key) && value != null && !"".equalsIgnoreCase(value.toString())) {
                content.append(i == 0 ? "" : "&").append(key).append("=").append(value);
            }
        }

        return content.toString();
    }

    /**
     * 字符串加签
     *
     * @param signContent 待加密的参数字符串
     * @param merchantPrivateKey 商家应用私钥
     * @return 加签后的字符串
     * @throws IOException
     * @throws GeneralSecurityException
     */
    private static String getSign(String signContent, String merchantPrivateKey) throws IOException, GeneralSecurityException {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = readText(new ByteArrayInputStream(merchantPrivateKey.getBytes())).getBytes();
        encodedKey = Base64.getDecoder().decode(encodedKey);
        PrivateKey priKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));

        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(priKey);
        signature.update(signContent.getBytes("UTF-8"));
        byte[] signed = signature.sign();
        return new String(Base64.getEncoder().encode(signed));
    }

    /**
     * 读取文本内容
     *
     * @param in 文本输入流对象
     * @return 文本内容
     * @throws IOException
     */
    private static String readText(InputStream in) throws IOException {
        Reader reader = new InputStreamReader(in);
        StringWriter writer = new StringWriter();

        int bufferSize = 4096;
        char[] buffer = new char[bufferSize];
        int amount;

        while ((amount = reader.read(buffer)) >= 0) {
            writer.write(buffer, 0, amount);
        }

        return writer.toString();
    }

}
