package com.autohome.applet.util;

import java.util.HashMap;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.analysis.function.Constant;

/**
 * @description: 对外开放接口签名工具类
 * @author: WangBoWen
 * @date: 2024-03-14
 **/
@Slf4j
public class OpenApiSignUtil {

    private static HashMap<String,String> PING_AN_HAO_CHE_ZHU;

    static {
        //todo 密钥还未确定
        PING_AN_HAO_CHE_ZHU.put("201691575_openId", "F82527BD4D54420891656504C8F60D22");
    }

    public static String sign(Map<String, String> paramsMap, String openId) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry me : p.entrySet()) {
            sb.append((String) me.getKey()).append("=").append((String) me.getValue()).append("&");
        }
        sb.append(PING_AN_HAO_CHE_ZHU.get(openId + "_openId"));
        String md = "";
        try {
            md = MD5Util.md5Encode(sb.toString());
        } catch (Exception e) {
            log.error("签名加密失败",e);
        }
        return md.toUpperCase();
    }

}
