package com.autohome.applet.util;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public class ExcelUtils {

    public static <T> List<T> read(InputStream inputStream, Class<T> clazz) throws IOException {
        ExcelListener<T> listener = new ExcelListener<>();
        ExcelReaderBuilder builder = EasyExcel.read(inputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .head(clazz)
                .autoCloseStream(true)
                .registerReadListener(listener);
        builder.sheet().doRead();
        return listener.getList();
    }

}
