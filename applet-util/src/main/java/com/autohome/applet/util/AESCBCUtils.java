package com.autohome.applet.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class AESCBCUtils {
    private static final String PADDING = "AES/CBC/PKCS5Padding";
    private final static String ivParameter = "0000000000000000";

    public final static String MINIPROGRAMKEY = "_sessionFromMini";

    /**
     * AES加密
     *
     * @param source 需要加密的内容
     * @return 加密后的字符串
     */
    public static String encryptAES(String source, String aes_encrypt_key) {
        if (null == source || source.isEmpty()) {
            return null;
        }
        try {
            SecretKeySpec key = new SecretKeySpec(aes_encrypt_key.getBytes("utf-8"), "AES");
            Cipher cipher = Cipher.getInstance(PADDING);// 创建密码器
            byte[] byteContent = source.getBytes();
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes("utf-8"));
            cipher.init(Cipher.ENCRYPT_MODE, key, iv);
            byte[] result = cipher.doFinal(byteContent);
            return Base64.encodeBase64String(result).toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * AES解密
     *
     * @param encryptSource 密文
     * @return 加密后的字符串
     */
    public static String decryptAES(String encryptSource, String aes_encrypt_key) {
        if (null == encryptSource || encryptSource.isEmpty()) {
            return null;
        }
        try {
            SecretKeySpec key = new SecretKeySpec(aes_encrypt_key.getBytes("utf-8"), "AES");
            Cipher cipher = Cipher.getInstance(PADDING);// 创建密码器
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes("utf-8"));
            cipher.init(Cipher.DECRYPT_MODE, key, iv);// 初始化
            byte[] result = cipher.doFinal(Base64.decodeBase64(encryptSource));
            return new String(result);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return "";
    }
    public static void main(String[] args) throws Exception {
        String key = "_sessionFromMini";
        String source = "39uIXKG5gbPWwHaa5j43ZMEbvo7OC/GVlqC0m7A52cHbrFyKktfcD+6+L5hHKzta";
//        String encryptStr = AESCBCUtils.encryptAES(source, key);
//        System.out.println(encryptStr);
        String re = AESCBCUtils.decryptAES(source, key);
        System.out.println(re);
    }
}
