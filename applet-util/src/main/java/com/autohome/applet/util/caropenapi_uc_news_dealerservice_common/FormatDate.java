package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;

import com.autohome.applet.util.netcoreapi.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by ha<PERSON>hanfeng on 2017/9/21.
 */
public class FormatDate {
    private static final Logger LOGGER = LoggerFactory.getLogger(FormatDate.class);

    /**
     * 得到当前系统日期,格式:yyyy-mm-dd
     *
     * @return
     */
    public static String getCurrentDateTime() {

        return getFormatDate("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前系统日期,格式:yyyy-mm-dd
     *
     * @return
     */
    public static String getCurrentDate() {

        return getFormatDate("yyyy-MM-dd");
    }

    /**
     * 返回年份
     *
     * @return
     */
    public static String getCurrentYear() {

        return getFormatDate("yyyy");
    }

    /**
     * 返回年份
     *
     * @return
     */
    public static String getYearByDate(Date date) {

        return getFormatDate("yyyy", date);
    }

    /**
     * 返回月份
     */
    public static String getCurrentMonth() {
        return getFormatDate("MM");
    }

    /**
     * 返回特定格式的日期
     * 格式如下:
     * yyyy-mm-dd
     *
     * @param formatString
     * @return
     */
    protected static String getFormatDate(String formatString) {
        String currentDate = "";
        SimpleDateFormat format1 = new SimpleDateFormat(formatString);
        currentDate = format1.format(new Date());
        return currentDate;
    }

    /**
     * 返回特定格式的日期
     * 格式如下:
     * yyyy-mm-dd
     *
     * @param formatString
     * @return
     */
    protected static String getFormatDate(String formatString, Date date) {
        String currentDate = "";
        SimpleDateFormat format1 = new SimpleDateFormat(formatString);
        currentDate = format1.format(date);
        return currentDate;
    }

    /**
     * 返回时间格式样式
     *
     * @return
     */
    public static String getFormateDateAll() {
        return getFormatDate("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取简化时间格式
     *
     * @return
     */
    public static String getFormateDateSimple() {
        return getFormatDate("yyyyMMddHHmmss");
    }


    /**
     * 获取几天后的时间
     *
     * @param d
     * @param day
     * @return
     */
    public static Date getDateAfterDay(Date d, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    /**
     * 获取几天后的时间
     *
     * @param d
     * @param day
     * @return
     */
    public static String getStrDateAfterDay(Date d, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return getFormatDate("yyyy-MM-dd", now.getTime());
    }

    /**
     * 获取几年后的时间
     *
     * @param d
     * @param year
     * @return
     */
    public static Date getDateAfterYear(Date d, int year) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.YEAR, now.get(Calendar.YEAR) + year);
        return now.getTime();
    }

    /**
     * 返回几个小时后的时间
     *
     * @param d
     * @param hour
     * @return
     */
    public static Date getDateAfterHour(Date d, int hour) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.HOUR, now.get(Calendar.HOUR) + hour);
        return now.getTime();
    }

    /**
     * 返回几分钟后的某个时间
     *
     * @param d
     * @param minutes
     * @return
     */
    public static Date getDateAfterMinute(Date d, int minutes) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.MINUTE, now.get(Calendar.MINUTE) + minutes);
        return now.getTime();
    }

    public static String convertLongToDate(String dateFormat, long millSec) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Date date = new Date(millSec);
        return sdf.format(date);
    }

    /**
     * 比较两个日期的大小
     * true date1比date2前
     * false date1比date2后
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean dateCompare(Date date1, Date date2) {
        if (date1 == null) {
            return true;
        }
        if (date2 == null) {
            return false;
        }
        boolean dateComPareFlag = true;

        if (date2.compareTo(date1) != 1) {
            dateComPareFlag = false;
        }
        return dateComPareFlag;
    }

    /**
     * 返回两时间之差
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static long dateMinus(Date startTime, Date endTime) {
        return endTime.getTime() - startTime.getTime();
    }

    public static String dateToStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (null == date) {
            return null;
        }

        return sdf.format(date);
    }

    public static String dateToStr(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        if (null == date) {
            return null;
        }

        return sdf.format(date);
    }

    public static Date strToDate(String dateString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(dateString);
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    public static Date strToTime(String dateString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(dateString);
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 转换时间格式
     *
     * @param dateString
     * @param format
     * @return
     */
    public static String strToDate(String dateString, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            Date date = sdf.parse(dateString);

            return sdf.format(date);
        } catch (Exception e) {
            return null;
        }
    }

    public static int diffIntervalMonths(Date end, Date start) {

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(start);
        c2.setTime(end);
        if (c1.getTimeInMillis() < c2.getTimeInMillis()) return 0;
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        // 获取年的差值 假设 d1 = 2015-8-16  d2 = 2011-9-30
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if (month1 < month2 || month1 == month2 && day1 < day2) yearInterval--;
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        if (day1 < day2) monthInterval--;
        else if (day1 > day2) monthInterval++;
        monthInterval %= 12;
        return yearInterval * 12 + monthInterval;

    }

    public static int getIntervalDays(Date fDate, Date oDate) {

        if (null == fDate || null == oDate) {

            return -1;

        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(fDate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(oDate);
        long time2 = cal.getTimeInMillis();
        long betweendays = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(betweendays));

    }

    public static int getIntervalDaysAccurateToDay(Date fDate, Date oDate) {

        if (null == fDate || null == oDate) {

            return -1;

        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            fDate = sdf.parse(sdf.format(fDate));
            oDate = sdf.parse(sdf.format(oDate));
        } catch (Exception e) {
            LOGGER.error("日期转换异常:" + e.getMessage());
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(fDate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(oDate);
        long time2 = cal.getTimeInMillis();
        long betweendays = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(betweendays));

    }

    public static String subTime(Date indate, Date outdate) {
        try {
            long totalhours = (outdate.getTime() - indate.getTime()) / (1000 * 60 * 60 * 24); //天
            String totaltime = totalhours + 1 + "天";
            return totaltime;
        } catch (Exception ex) {
            return "";
        }
    }

    /**
     * 计算两个string类的时间差
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String subTime(String startTime, String endTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 格式化时间
            Date outdate = sdf.parse(endTime);
            Date indate = sdf.parse(startTime);
            long totalhours = (outdate.getTime() - indate.getTime()) / (1000 * 60 * 60); //时
            long totalminutes = (outdate.getTime() - indate.getTime()) / (1000 * 60) - totalhours * 60; //分
            long totalseconds = (outdate.getTime() - indate.getTime()) / (1000) - totalminutes * 60; //秒
            String totaltime = totalhours + "时" + totalminutes + "分" + totalseconds + "秒";
            return totaltime;
        } catch (Exception ex) {
            return "";
        }
    }


    /**
     * 将秒数转换为日时分秒，
     *
     * @param time
     * @return
     */
    public static String secToTime(int time) {
        String timeStr = null;
        int hour = 0;
        int minute = 0;
        int second = 0;
        if (time <= 0)
            return "00:00";
        else {
            minute = time / 60;
            if (minute < 60) {
                second = time % 60;
                timeStr = unitFormat(minute) + ":" + unitFormat(second);
            } else {
                hour = minute / 60;
                if (hour > 99)
                    return "99:59:59";
                minute = minute % 60;
                second = time - hour * 3600 - minute * 60;
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second);
            }
        }
        return timeStr;
    }

    public static String unitFormat(int i) {
        String retStr = null;
        if (i >= 0 && i < 10)
            retStr = "0" + Integer.toString(i);
        else
            retStr = "" + i;
        return retStr;
    }

    //计算剩余时间
    public static String getRemainTime(String startTime, String endTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 格式化时间
            Date outdate = sdf.parse(endTime);
            Date indate = sdf.parse(startTime);
            long totalminutes = (outdate.getTime() - indate.getTime()) / (1000 * 60); //分
            long totalseconds = (outdate.getTime() - indate.getTime()) / (1000) - totalminutes * 60; //秒
            String remainTime = totalminutes + "#" + totalseconds;
            return remainTime;
        } catch (Exception ex) {
            return "";
        }
    }

    //返回自定义规则时间描述
    public static String getFormatDateStr(Date dt, String format) {
        try {
            String strTime = "";
            Date now = new Date();
            long totalseconds = (now.getTime() - dt.getTime()) / (1000); //秒
            if (totalseconds < 60) {
                if (totalseconds < 1)
                    totalseconds = 1;
                strTime = totalseconds + "秒前";
            } else if (totalseconds > 59 && totalseconds < 3600) {
                int m = Integer.parseInt(Long.toString(totalseconds / 60));
                strTime = m + "分钟前"; // +s.ToString() + "秒";
            } else if (totalseconds > 3599 && totalseconds < 86400) {
                int h = Integer.parseInt(Long.toString(totalseconds / 3600));
                strTime = h + "小时前"; // +m.ToString() + "分";
            } else {
                int d = Integer.parseInt(Long.toString(totalseconds / 86400));
                if (d < 30) {
                    int h = (int) (totalseconds % 86400) / 3600;
                    strTime = d + "天前"; // +h.ToString() + "小时";
                } else {
                    strTime = getFormatDate(format, dt);
                }
            }
            return strTime;
        } catch (Exception ex) {
            return "";
        }
    }

    public static boolean compareTimestamp(long time) {
        long now = System.currentTimeMillis();
        if (Math.abs((now / 1000) - time) > 300) {
            return false;
        }
        return true;
    }

    public static String netToJavaTime(String nettime) {
        if (StringUtil.isNotNullAndWhiteSpace(nettime)) {
            String jtime = nettime.replace("T", " ").replace("Z", "");
            jtime = jtime.substring(0, jtime.indexOf("."));
            return jtime;
        }
        return "";
    }

    public static String netConvertJavaTime(String nettime) {
        if (StringUtil.isNotNullAndWhiteSpace(nettime)) {
            String jtime = nettime.replace("/Date(", "").replace("+0800)/", "");
            return dateToStr(new Date(Long.parseLong(jtime)));
        }
        return "";
    }

    public static String diffTimeCompare(String nettimestart, String nettimeend) {
        if (StringUtil.isNotNullAndWhiteSpace(nettimestart) && StringUtil.isNotNullAndWhiteSpace(nettimeend)) {

            nettimeend = nettimeend.replace("/Date(", "").replace("+0800)/", "");
            nettimeend = dateToStr(new Date(Long.parseLong(nettimeend)), "yyyy-MM-dd");
            return subTime(strToDate(nettimestart), strToDate(nettimeend));
        }
        return "";
    }

    public static String netTimeCompare(String nettimestart, String nettimeend) {
        if (StringUtil.isNotNullAndWhiteSpace(nettimestart) && StringUtil.isNotNullAndWhiteSpace(nettimeend)) {
            nettimestart = nettimestart.replace("/Date(", "").replace("+0800)/", "");
            nettimestart = dateToStr(new Date(Long.parseLong(nettimestart)), "yyyy-MM-dd");
            nettimeend = nettimeend.replace("/Date(", "").replace("+0800)/", "");
            nettimeend = dateToStr(new Date(Long.parseLong(nettimeend)), "yyyy-MM-dd");
            return subTime(strToDate(nettimestart), strToDate(nettimeend));
        }
        return "";
    }

    public static String netTimeCompare(Date nettimestart, String nettimeend) {
        if (StringUtil.isNotNullAndWhiteSpace(nettimeend)) {

            nettimeend = nettimeend.replace("/Date(", "").replace("+0800)/", "");
            nettimeend = dateToStr(new Date(Long.parseLong(nettimeend)), "yyyy-MM-dd");
            return subTime(nettimestart, strToDate(nettimeend));
        }
        return "";
    }

    public static void main(String[] arg) {
        try {
//            Base64.Encoder encoder=new Base64.Encoder();
        } catch (Exception e) {
            e.printStackTrace();
        }

        String img = "http://dealer0.autoimg.cn/shop/121/201307290842494249.jpg";
        int size = img.lastIndexOf("/");
        img = img.substring(size, img.length());
        System.out.println(CarSettings.ConvertImgNew("https://car3.autoimg.cn/cardfs/series/g25/M0B/29/45/autohomecar__ChcCr1qnPbKAJu6KAAZZkFkszhU583.png", "280x210"));
        System.out.println(CarSettings.ConvertImgNew("https://car0.autoimg.cn/upload/2014/6/23/20140623181443204240.jpg", "280x210"));
//        float f = 3.5f;
//        BigDecimal b = new BigDecimal(f);
//        int f1 = b.setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
//        System.out.println(Integer.toString(f1));
//        System.out.println(dateToStr(new Date(1557212747317L), "yyyy-MM-dd"));
//        System.out.println(netTimeCompare("/Date(1557212747317+0800)/", "/Date(1557158400000+0800)/"));
    }
}
