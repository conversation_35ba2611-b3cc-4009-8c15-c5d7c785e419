package com.autohome.applet.util.qqupload;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 应用签名及请求响应数据签名类
 *
 */
public class Crypto {

    private static final String[] ignoredSigName = {"appsig", "reqsig", "paysig"};
    private static final String encoding = "UTF-8";
    //public static final String APPKEY = "bfe4807068e18689";
    //品牌卡
    public static final String BRANDAPPID = "50028291";
    public static final String BRANDA_APPKEY = "lMQ43QFCVaQ1zWvK";

    //车系卡
    public static final String SERIESAPPID = "50028294";
    public static final String SERIES_APPKEY = "227JLLW98Y02E0v1";

    //评价卡
    public static final String EVALUATEAPPID = "50028298";
    public static final String EVALUATEA_APPKEY = "FNgU6HzgI0Fo213W";

    //图片卡
    public static final String PICAPPID = "50028286";
    public static final String PIC_APPKEY = "0G6MU2T9991egO0e";
    /**
     * 生成请求或响应数据的签名
     *
     * @param cipherData 需解密的明文
     * @param appKey 应用密钥
     * @return 返回请求或响应签名
     */
    public static String GetDataSig(String cipherData, String appKey) {
        String dataSig = Crypto.GetSig(cipherData, appKey);
        return dataSig;
    }

    /**
     * 获取应用签名
     *
     * @param dataKey 数据加密密钥
     * @return 返回应用签名
     */
    public static String GetAppSig(String index, String num, String starttime, String nonce, String appKey) {
        String src = index + "_" + num + "_" + starttime + "_" + nonce;
        String appsig = Crypto.GetSig(src, appKey);
        return appsig;
    }
	
	/**
     * 获取推送应用签名
     *
     * @param dataKey 数据加密密钥
     * @return 返回应用签名
     */
    public static String GetPushAppSig(String appid, String timestamp, String nonce, String appKey) {
        String src = appid + "_" + timestamp + "_" + nonce;
        String appsig = Crypto.GetSig(src, appKey);
        return appsig;
    }

    /**
     * 获取签名
     *
     * @param rawData 需签名的数据
     * @param appKey 密钥
     * @return 返回数据的签名
     */
    public static String GetSig(String rawData, String appKey) {
        String sig = "";
        try {
            byte[] rawBytes = rawData.getBytes(encoding);

            String macName = "HmacSHA1";
            byte[] keyBytes = appKey.getBytes(encoding);

            SecretKey secretKey = new SecretKeySpec(keyBytes, macName);
            Mac mac = Mac.getInstance(macName);
            mac.init(secretKey);
            byte[] sigBytes = mac.doFinal(rawBytes);

            sig = new BASE64Encoder().encode(sigBytes);
            //System.out.println(sig);
            sig = UrlEncode(sig, 0);

        } catch (UnsupportedEncodingException e) {

        } catch (NoSuchAlgorithmException e) {

        } catch (InvalidKeyException e) {

        } catch (IllegalStateException e) {

        }

        return sig;
    }

    /**
     * 获取加密密文
     *
     * @param rawData 需加密的明文数据
     * @param dataKey 密钥
     * @return 返回加密密文的Base64及UrlEncode后的串
     */
    public static String GetCipherData(String rawData, String dataKey) {
        String cipherData = "";
        try {
            //在末尾补结束符\0
            int blockSize = 16;
            byte[] rawBytes0 = rawData.getBytes(encoding);

            int padding = blockSize - rawBytes0.length % blockSize;
            StringBuilder sb = new StringBuilder();
            while (padding-- > 0) {
                sb.append('\0');
            }
            rawData += sb.toString();

            //创建密钥
            byte[] keyBytes = dataKey.getBytes(encoding);
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");

            //加密
            byte[] rawBytes = rawData.getBytes(encoding);
            Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");
            cipher.init(Cipher.ENCRYPT_MODE, key);//加密模式
            byte[] cipherBytes = cipher.doFinal(rawBytes); //完成加密操作

            cipherData = new BASE64Encoder().encode(cipherBytes);//Base64
            cipherData = UrlEncode(cipherData, 0);

        } catch (NoSuchAlgorithmException e) {
            //e.printStackTrace();
        } catch (NoSuchPaddingException e) {

        } catch (InvalidKeyException e) {

        } catch (UnsupportedEncodingException e) {

        } catch (IllegalBlockSizeException e) {

        } catch (BadPaddingException e) {

        }
        return cipherData;
    }

    /**
     * 获取明文
     *
     * @param cipherData 需解密的明文
     * @param dataKey 密钥
     * @return 返回解密后的明文数据（utf8格式）
     */
    public static String GetPlainData(String cipherData, String dataKey) {
        String plainData = "";
        try {
            //urldecode及base64解密还原加密数据
            cipherData = UrlDecode(cipherData);
            byte[] cipherBytes = new BASE64Decoder().decodeBuffer(cipherData);//Base64 

            //创建密钥
            byte[] keyBytes = dataKey.getBytes(encoding);
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");

            //解密
            Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");//PKCS5Padding
            cipher.init(Cipher.DECRYPT_MODE, key);//解密模式 
            byte[] plainBytes = cipher.doFinal(cipherBytes);
            plainData = new String(plainBytes, encoding);

            //去掉结束符
            int index = plainData.indexOf('\0');
            if (index > -1) {
                plainData = plainData.substring(0, index);
            }

        } catch (NoSuchAlgorithmException e) {
            //e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            //e.printStackTrace();
        } catch (InvalidKeyException e) {
            //e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            //e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            //e.printStackTrace();
        } catch (BadPaddingException e) {
            //e.printStackTrace();
        } catch (IOException e) {
            //e.printStackTrace();
        }
        return plainData;
    }

    /**
     * 兼容的url编码
     *
     * @param rawData 待编码的数据
     * @param isSignSrc 是否为签名的源串
     * @return url编码后的字符串
     */
    public static String UrlEncode(String rawData, int isSignSrc) {
        String result = rawData;

        try {
            result = URLEncoder.encode(rawData, "utf-8").replaceAll("\\*", "%2A");
            result = result.replaceAll("\\+", "%20"); //空格
            if (isSignSrc != 1) {
                result = result.replaceAll("%7E", "~");
            }

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return result;
    }

    public static String UrlDecode(String encodedData) {
        String result = encodedData;
        try {
            result = URLDecoder.decode(encodedData, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return result;
    }
}
