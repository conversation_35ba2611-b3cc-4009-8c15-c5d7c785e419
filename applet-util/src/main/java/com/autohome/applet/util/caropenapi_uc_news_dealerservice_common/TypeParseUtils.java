package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * Created by Administrator on 2018/4/21.
 */
public class TypeParseUtils {
    /**
     * 后两位四舍五入
     *
     * @param f
     * @return
     */
    public static double parseDoubleWithScale(double f) {
        BigDecimal b = new BigDecimal(f);
        double f1 = b.setScale(2, RoundingMode.HALF_UP).doubleValue();
        return f1;
    }


    public static String parseIntWithUnit(long no) {
        DecimalFormat formater = new DecimalFormat("0.0");
        //1万以内：具体数
        //大于等于1000万小于1亿：*千万
        //大于等于1亿：*亿
        if (no < 10000) {
            return String.valueOf(no);
        }
        if (no < 10000000) {
            return formater.format((double) no / 10000) + "万";
        }
        if (no < 100000000) {
            return Integer.parseInt(String.valueOf((no / 10000000))) + "千万";
        }
        if (no < 1000000000) {
            return Integer.parseInt(String.valueOf((no / 10000000))) / 10 + "亿";
        }
        return Integer.parseInt(String.valueOf((no / 100000000))) + "亿";
    }

    public static String parseIntWithUnit(double no) {
        DecimalFormat formater = new DecimalFormat("0.0");
        //1万以内：具体数
        //大于等于1000万小于1亿：*千万
        //大于等于1亿：*亿
        if (no < 10000) {
            return String.valueOf(no);
        }
        if (no < 10000000) {
            return formater.format(no / 10000) + "万";
        }
        if (no < 100000000) {
            return Integer.parseInt(String.valueOf((no / 10000000))) + "千万";
        }
        if (no < 1000000000) {
            return Integer.parseInt(String.valueOf((no / 10000000))) / 10 + "亿";
        }
        return Integer.parseInt(String.valueOf((no / 100000000))) + "亿";
    }
}
