package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;

import com.autohome.applet.util.netcoreapi.StringUtil;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Created by <PERSON><PERSON><PERSON>feng on 2018/5/11.
 */
public class LatMapLonBaidu {
    public static double DistanceOfTwoPoints(double lng1, double lat1, double lng2, double lat2) {
        try {
            double radLat1 = Rad(lat1);
            double radLat2 = Rad(lat2);
            double a = radLat1 - radLat2;
            double b = Rad(lng1) - Rad(lng2);
            double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                    Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
            s = s * (6378.137);
            s = Math.round(s);
            return s;
        } catch (Exception e) {
            return 0;
        }
    }

    private static double Rad(double d) {
        return d * Math.PI / 180.0;
    }

    public static String format(String doubledata) {
        try {
            if (!StringUtil.isNotNullAndWhiteSpace(doubledata)) {
                return "";
            }
            DecimalFormat df = new DecimalFormat("0.00");
            BigDecimal b = new BigDecimal(doubledata);
            double doubleValue = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            return df.format(doubleValue);
        } catch (Exception e) {
            return "";
        }
    }

    public static void main(String[] arg) {
        System.out.println(format("12.98999"));
        System.out.println(format("11.3977578356649"));
    }
}
