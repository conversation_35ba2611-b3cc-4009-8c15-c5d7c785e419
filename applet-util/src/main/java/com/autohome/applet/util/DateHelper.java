package com.autohome.applet.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Preconditions;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DateHelper {

    public final static String DATEFORMAT_FULL = "yyyy-MM-dd HH:mm:ss.SSS";
    public final static String DATEFORMAT_STANDARD = "yyyy-MM-dd HH:mm:ss";
    public final static String DATEFORMAT_ONLY_DATE = "yyyy-MM-dd";
    public final static String DATEFORMAT_NEW = "yyyy/MM/dd HH:mm:ss";
    public final static String DATEFORMAT_SHORT = "yyyy-MM-dd HH:mm";
    public final static String DATEFORMAT_STANDARD_UTC = "yyyy-MM-dd'T'HH:mm:ss";
    public final static String DATEFORMAT_STANDARD_2 = "yyyyMMddHHmmss";
    public final static String DATEFORMAT_YEARMONTH = "yyyyMM";
    public final static String DATEFORMAT_MONTH_DAY = "M-d";
    public final static String DATEFORMAT_MONTH_DAY_2 = "MM-dd";
    public final static String DATEFORMAT_MONTH = "M";
    public final static String DATEFORMAT_MONTH_2 = "MM";

    public static final DateFormat DF_STANDARD = new SimpleDateFormat(DateHelper.DATEFORMAT_STANDARD);

    public static Date deserialize(String source, String... dateFormat) {
        Date date = null;
        if (dateFormat != null && dateFormat.length > 0) {
            for (String df : dateFormat) {
                try {
                    date = new SimpleDateFormat(df).parse(source);
                    break;
                } catch (Exception e) {
                    log.debug(e.getMessage());
                }
            }

        } else {
            date = parse(source);
            if (date == null) {
                Matcher matcher = Pattern.compile("\\/Date\\((-?\\d+)\\)\\/").matcher(source);
                if (matcher.matches()) {
                    date = new Date(Long.valueOf(matcher.group(1)));
                }
            }
        }
        Preconditions.checkNotNull(date, String.format("date %s can't be resolved", source));
        return date;

    }

    /**
     * 解析日期格式：/Date(1713974400000)/
     * */
    public static Date deserializeDateT(String source) {
        Date date = null;
        Matcher matcher = Pattern.compile("\\/Date\\((-?\\d+)\\)\\/").matcher(source);
        if (matcher.matches()) {
            date = new Date(Long.valueOf(matcher.group(1)));
        }
        Preconditions.checkNotNull(date, String.format("date %s can't be resolved", source));
        return date;

    }

    /**
     * 使用dateFormat格式化date对象
     *
     * @param date
     * @param dateFormat
     * @return
     */
    public static String serialize(Date date, String dateFormat) {
        DateFormat df = new SimpleDateFormat(dateFormat);
        String str = df.format(date);
        return str;
    }

    /**
     * 使用yyyy-MM-dd HH:mm:ss.SSS格式化date对象
     *
     * @param date
     * @return
     */
    public static String serialize(Date date) {
        return DF_STANDARD.format(date);
    }

    /**
     * 返回代表当前时间格式化的字符串（格式：yyyy-MM-dd HH:mm:ss.SSS）
     *
     * @return
     */
    public static String getNowString() {
        return serialize(getNow(), DATEFORMAT_FULL);
    }

    public static String getTomorrowDateStr(String format) {
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return tomorrow.format(formatter);
    }


    /**
     * 返回代表今天格式化的字符串（格式：yyyy-MM-dd）
     *
     * @return
     */
    public static String getTodayString() {
        return serialize(getNow(),  DATEFORMAT_ONLY_DATE);
    }

    /**
     * 返回代表当前时间格式化的字符串
     *
     * @return
     */
    public static String getNowString(String dateFormat) {
        return serialize(getNow(), dateFormat);
    }

    /**
     * 返回一个代表当前时间的Date对象
     *
     * @return
     */
    public static Date getNow() {
        return Calendar.getInstance().getTime();
    }

    /**
     * 返回代表当前时间的毫秒数
     *
     * @return
     */
    public static long getNowInInMillis() {
        return Calendar.getInstance().getTimeInMillis();
    }

    /**
     * 获取指定的Date
     *
     * @param timeUnit 时间单位
     * @param account  添加到calendarTimeField的时间或日期数量
     * @return
     */
    public static Date getDate(TemporalUnit timeUnit, int account) {
        return Date.from(LocalDateTime.now().plus(account, timeUnit).atZone(ZoneOffset.systemDefault()).toInstant());
    }

    /**
     * 秒转换为指定格式的日期
     *
     * @param second
     * @return
     */
    public static String secondToDate(long second) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(second * 1000);//转换为毫秒
        Date date = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String dateString = format.format(date);
        return dateString;
    }

    public static void main(String[] args) {
        String s = secondToDate(**********);
        String s1 = secondToDate(**********);

        System.out.println(s);
        System.out.println(s1);
    }

    /**
     * 获取指定的timestamp
     *
     * @param timeUnit 时间单位
     * @param account  添加到calendarTimeField的时间或日期数量
     * @return
     */
    public static long getTimeInMillis(TemporalUnit timeUnit, int account) {
        return LocalDateTime.now().plus(account, timeUnit).atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 函数功能描述:UTC时间转本地时间格式
     *
     * @param datetime 日期字符串
     * @return 本地日期
     */
    public static Date parse(String datetime) {
        boolean isUTC = false;
        String utcTimePattern = "yyyy-MM-dd";
        String subTime = datetime.substring(10);//UTC时间格式以 yyyy-MM-dd 开头,将utc时间的前10位截取掉,之后是含有多时区时间格式信息的数据

        //处理当后缀为:+8:00时,转换为:+08:00 或 -8:00转换为-08:00
        if (subTime.indexOf("+") != -1) {
            subTime = changeUtcSuffix(subTime, "+");
        } else if (subTime.indexOf("-") != -1) {
            subTime = changeUtcSuffix(subTime, "-");
        }
        datetime = datetime.substring(0, 10) + subTime;

        //依据传入函数的utc时间,得到对应的utc时间格式
        //步骤一:处理 T
        if (datetime.indexOf("T") != -1) {
            utcTimePattern += "'T'";
        }

        //步骤二:处理毫秒SSS
        if (!StringUtils.isBlank(subTime)) {
            if (datetime.indexOf(".") != -1) {
                utcTimePattern = utcTimePattern + "HH:mm:ss.SSS";
            } else {
                utcTimePattern = utcTimePattern + "HH:mm:ss";
            }
        }

        //步骤三:处理时区问题
        if (subTime.indexOf("+") != -1 || subTime.indexOf("-") != -1) {
            utcTimePattern += "XXX";
            isUTC = true;
        } else if (subTime.indexOf("Z") != -1) {
            utcTimePattern += "'Z'";
            isUTC = true;
        }


        SimpleDateFormat utcFormater = new SimpleDateFormat(utcTimePattern);
        if (isUTC)
            utcFormater.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date date = null;
        try {
            date = utcFormater.parse(datetime);
        } catch (ParseException e) {
            log.error("时间转换失败:", e.getMessage());
        }
        return date;

    }

    /**
     * 函数功能描述:修改时间格式后缀
     * 函数使用场景:处理当后缀为:+8:00时,转换为:+08:00 或 -8:00转换为-08:00
     *
     * @param subTime
     * @param sign
     * @return
     */
    private static String changeUtcSuffix(String subTime, String sign) {
        String timeSuffix = null;
        String[] splitTimeArrayOne = subTime.split("[" + sign + "]");
        String[] splitTimeArrayTwo = splitTimeArrayOne[1].split(":");
        if (splitTimeArrayTwo[0].length() < 2) {
            timeSuffix = sign + "0" + splitTimeArrayTwo[0] + ":" + splitTimeArrayTwo[1];
            subTime = splitTimeArrayOne[0] + timeSuffix;
            return subTime;
        }
        return subTime;
    }

    public static long pastDays(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

   /* public enum TimeUnit {
        YEAR(Calendar.YEAR),
        MONTH(Calendar.MONTH),
        DAY(Calendar.DAY_OF_YEAR),
        HOUR(Calendar.HOUR_OF_DAY),
        MINUTE(Calendar.MINUTE),
        SECOND(Calendar.SECOND),
        MILLISECOND(Calendar.MILLISECOND);


        private int v;

        TimeUnit(int value) {
            this.v = value;
        }

        int getValue() {
            return this.v;
        }
    }*/

    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);

        Date date = Date.from(zdt.toInstant());
        return date;
    }

    public static LocalDate dateToLocalDate(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate;
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);

        Date date = Date.from(zdt.toInstant());
        return date;
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        LocalDateTime LocalDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return LocalDateTime;
    }

    public static Date beforeNDaysDate(int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR, -days);
        return cal.getTime();
    }

    public static Date beforeNDaysDate(Date currentDay, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDay);
        cal.add(Calendar.DAY_OF_YEAR, -days);
        return cal.getTime();
    }

    public static String secondsToHMS (int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int remainingSeconds = seconds % 60;
        String hoursStr = extracted(hours);
        String minutesStr = extracted(minutes);
        String remainingSecondsStr = extracted(remainingSeconds);
        if (hours == 0){
            return minutesStr + ":" + remainingSecondsStr;
        }
        return hoursStr + ":" +minutesStr + ":" + remainingSecondsStr;
    }

    private static String extracted(int hands) {
        String handsStr;
        if (hands < 10){
            handsStr = "0" + hands;
        }else {
            handsStr = String.valueOf(hands);
        }
        return handsStr;
    }

    public static String localDateToDateTimeStr(LocalDateTime localDateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        String formatDateTime = localDateTime.format(formatter);
        return formatDateTime;
    }

    public static String localDateToDateTimeStr(LocalDate localDate, String format) {
        LocalDateTime dateTime = localDate.atStartOfDay();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        String formatDateTime = dateTime.format(formatter);
        return formatDateTime;
    }

    public static String convertFormatDate(String dateStr, String sourceFormat, String objectFormat){
        try{
            Date date = deserialize(dateStr, sourceFormat);
            return serialize(date, objectFormat);
        }
        catch (Exception ex){
            log.error("convertFormatDate error:{}, ", dateStr, ex);
            return null;
        }
    }

    public static Date parseDateFromWithDateStr(String str) {
        return StringUtils.isNotEmpty(str) && str.length() == 21 ? new Date(Long.parseLong(str.substring(6, 19))) : null;
    }

    /**
     * 获取指定日期范围内的随机时间
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（包含）
     * @param dailyStartHour 每天开始小时（0-23）
     * @param dailyEndHour 每天结束小时（0-23）
     * @return 格式化的随机时间字符串（yyyy-MM-dd HH:mm:ss）
     */
    public static String getRandomTime(Date startDate, Date endDate, int dailyStartHour, int dailyEndHour) {
        Preconditions.checkNotNull(startDate, "开始日期不能为空");
        Preconditions.checkNotNull(endDate, "结束日期不能为空");
        Preconditions.checkArgument(!startDate.after(endDate), "开始日期不能晚于结束日期");
        Preconditions.checkArgument(dailyStartHour >= 0 && dailyStartHour <= 23,
                "每天开始小时必须在0-23之间");
        Preconditions.checkArgument(dailyEndHour >= 0 && dailyEndHour <= 23,
                "每天结束小时必须在0-23之间");
        Preconditions.checkArgument(dailyStartHour < dailyEndHour,
                "每天开始小时必须小于结束小时");

        try {
            // 计算时间差（毫秒）
            long startMillis = startDate.getTime();
            long endMillis = endDate.getTime();
            long randomMillis = ThreadLocalRandom.current().nextLong(startMillis, endMillis + 1);

            // 创建随机时间
            Date randomDate = new Date(randomMillis);
            Calendar cal = Calendar.getInstance();
            cal.setTime(randomDate);

            // 调整到当天的有效时间范围内
            int hour = cal.get(Calendar.HOUR_OF_DAY);
            if (hour < dailyStartHour) {
                cal.set(Calendar.HOUR_OF_DAY, dailyStartHour);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
            } else if (hour >= dailyEndHour) {
                cal.set(Calendar.HOUR_OF_DAY, dailyEndHour - 1);
                cal.set(Calendar.MINUTE, 59);
                cal.set(Calendar.SECOND, 59);
            }

            return serialize(cal.getTime(), DATEFORMAT_STANDARD);
        } catch (Exception e) {
            log.error("生成随机时间失败", e);
            return serialize(getNow(), DATEFORMAT_STANDARD);
        }
    }

    /**
     * 获取指定日期范围内的随机时间（字符串参数版本）
     * @param startDateStr 开始日期字符串（格式：yyyy-MM-dd）
     * @param endDateStr 结束日期字符串（格式：yyyy-MM-dd）
     * @param dailyStartHour 每天开始小时（0-23）
     * @param dailyEndHour 每天结束小时（0-23）
     * @return 格式化的随机时间字符串（yyyy-MM-dd HH:mm:ss）
     * @throws ParseException 如果日期格式不正确
     */
    public static String getRandomTime(String startDateStr, String endDateStr,
                                       int dailyStartHour, int dailyEndHour) {
        try {
            Date startDate = deserialize(startDateStr + " 00:00:00", DATEFORMAT_STANDARD);
            Date endDate = deserialize(endDateStr + " 23:59:59", DATEFORMAT_STANDARD);
            return getRandomTime(startDate, endDate, dailyStartHour, dailyEndHour);
        } catch (Exception ex) {
            log.error("getRandomTime 随机时间生成失败 startDateStr={} endDateStr={} dailyStartHour={} dailyEndHour={}",
                    startDateStr, endDateStr, dailyStartHour, dailyEndHour, ex);
            return null;
        }
    }
}
