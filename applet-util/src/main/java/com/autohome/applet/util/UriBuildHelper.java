package com.autohome.applet.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 */
public class UriBuildHelper {

    public static final class AutoOpenFrom {
        public static final String ALIPAY_STORE_SERVICE_DEALER = "alipay_store_service_dealer";
        public static final String ALIPAY_STORE_SERVICE_BAOYANG = "alipay_store_service_baoyang";
    }

    public static final class Alipay {
        /**
         * 支付宝4S店大全appId
         */
        public static final String ALIPAY_ALL4S_SHOP_APPID = "2021002127684760";

        public static String buildAlipayAll4sShopPage(String page, String autoOpenFrom) {
            String ext = String.format("auto_open_from=%s", autoOpenFrom);
            return buildAlipayUri(ALIPAY_ALL4S_SHOP_APPID, page, ext);
        }

        public static String buildAlipayAll4sShopH5(String h5Url, String autoOpenFrom) {
            String page = String.format("/pages/webview/webview?sendtype=mine&url=%s", urlEncode(h5Url));
            String ext = String.format("auto_open_from=%s", autoOpenFrom);
            return buildAlipayUri(ALIPAY_ALL4S_SHOP_APPID, page, ext);
        }

        /**
         * 支付宝 alipay:// 地址拼接规则：
         * alipays://platformapi/startapp?appId=小程序appId&page=encodeURIComponent(小程序路径)&query=encodeURIComponent(扩展参数)
         */
        public static String buildAlipayUri(String appId, String page, String ext) {
            String encodePage = urlEncode(page);
            String encodeExt = urlEncode(ext);
            return String.format("alipays://platformapi/startapp?appId=%s&page=%s&query=%s", appId, encodePage, encodeExt);
        }
    }

    public static final class QuickApp {

        public static String transfer(String uri) {
            uri = uri.replace("hap://app/com.autohome.quickapp", "");
            return String.format("hap://app/com.autohome.quickapp/PageTransfer?url=%s", urlEncode(uri));
        }

        /**
         * 快应用页面地址
         * @param path 页面
         * @param query 参数
         * @param autoOpenFrom from
         */
        public static String buildQuickAppPage(String path, String query, String autoOpenFrom) {
            String url = String.format("hap://app/com.autohome.quickapp/%s?%s&auto_open_from=%s"
                    , path, query, autoOpenFrom);
            return transfer(url);
        }

        /**
         * 车系页 PageCarSeries?seriesid=车系id
         * @param seriesId 车系id
         */
        public static String buildQuickAppSeriesPage(String seriesId, String autoOpenFrom) {
            String query = String.format("seriesid=${%s}", SecurityKit.encrypt(seriesId));
            return buildQuickAppPage("PageCarSeries", query, autoOpenFrom);
        }

        // PackageExtend/PageOrderDeal?seriesId=【车系ID填这里，去掉括号】&linkid=【申请个eid填这里，去掉括号】&auto_open_from=hw_230420
        public static String buildQuickAppPageOrderDeal(String seriesId) {
            String eid = "4|127|1783|21306|205684|304835";
            String query = String.format("seriesId=${%s}&linkid=%s", SecurityKit.encrypt(seriesId), eid);
            return buildQuickAppPage("PackageExtend/PageOrderDeal", query, "hw_230420");
        }
    }

    private static String urlEncode(String url) {
        try {
            return URLEncoder.encode(url, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return url;
    }

}