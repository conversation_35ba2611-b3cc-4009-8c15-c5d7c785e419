package com.autohome.applet.util.uploadfile;

public class ImageHelper {
    ImageHelper() {
    }

    public static String AddImageDomain(String imagepath) {
        return AddImageDomain(imagepath, "www%1$s.autoimg.cn");
    }

    public static String AddImageDomain(String imagepath, String host) {
        if (host == null || host.isEmpty()) {
            host = "www%1$s.autoimg.cn";
        }

        if (imagepath != null && !imagepath.isEmpty()) {
            if (imagepath.indexOf("http://") > -1) {
                return "";
            } else {
                imagepath = imagepath.trim();
                host = host.trim();
                int no = 0;
                if (imagepath.indexOf("autohomecar__") != -1) {
                    no = 2;
                }

                no += GetImgDomainNum(imagepath);
                return String.format("https://" + host + "/" + imagepath, no);
            }
        } else {
            return "";
        }
    }

    public static int GetImgDomainNum(String name) {
        int r = 0;
        int b = 0;

        while(true) {
            r += 4;
            if (r >= name.length()) {
                b %= 2;
                return b;
            }

            b ^= name.toCharArray()[r];
        }
    }
}
