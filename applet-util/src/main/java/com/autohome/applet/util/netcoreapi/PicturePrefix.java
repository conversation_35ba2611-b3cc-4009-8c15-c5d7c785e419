package com.autohome.applet.util.netcoreapi;


import org.apache.commons.lang3.StringUtils;

/// <summary>
/// 图片前缀
/// </summary>
/// <remarks>
/// 作者：何剑伟.Jorben
/// </remarks>
public class PicturePrefix {
    public static String ConvertCarImg(String img, String size) {
        return ConvertImg(img, size, "car%d.m.autoimg.cn");
    }

    /// <summary>
    /// 通用图片转换
    ///ConvertImg("http://img.autohome.com.cn/2012/2/17/17-13-59-18-953876669.jpg", "x_");
    ///ConvertImg("http://img.autohome.com.cn/2012/2/17/17-13-59-18-953876669.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///ConvertImg("http://www0.m.autoimg.cn/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_");
    ///ConvertImg("http://www0.m.autoimg.cn/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///ConvertImg("/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_");
    ///ConvertImg("/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_");
    ///ConvertImg("/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///ConvertImg("~/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///ConvertImg("~/zx/newspic/2015/3/4/2015030409342769313.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///ConvertImg("zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///ConvertImg("http://www0.m.autoimg.cn/zx/newspic/2015/3/4/640x320_0_q30_2015030409342769313.jpg", "100x100_0_q30_", "www{0}.m.autoimg.cn");
    ///老的blog图片处理——ConvertImg(" http://www1.autoimg.cn/Blog/Article/2013/8/20/a_2013082011015776978.jpg", "180x135_0_q30_", "www{0}.m.autoimg.cn");
    ///  ConvertImg("http://car2.autoimg.cn/cardfs/product/g11/M15/0E/04/2000x111_0_q30_autohomecar__wKjBzFWbLoiAGS_NAAISDz2LG1w816.jpg", "180x135_0_q30_", "car{0}.m.autoimg.cn")
    /// ConvertImg("http://car2.autoimg.cn/cardfs/product/g11/M15/0E/04/autohomecar__wKjBzFWbLoiAGS_NAAISDz2LG1w816.jpg", "180x135_0_q30_", "car{0}.m.autoimg.cn")
    /// ConvertImg("http://car2.autoimg.cn/cardfs/product/g11/M15/0E/04/2000x111_0_q30_autohomecar__wKjBzFWbLoiAGS_NAAISDz2LG1w816.jpg", "180x135_0_q30_", "car{0}.m.autoimg.cn")
    ///
    /// 老的视频图片：
    /// http://www0.autoimg.cn/video/wwwimgpic/2012/11/7/x_d498_e0a60e6e.jpg
    /// http://www0.autoimg.cn/video/wwwimgpic/2012/11/7/y_d498_e0a60e6e.jpg
    /// http://www0.autoimg.cn/video/wwwimgpic/2012/11/7/z_d498_e0a60e6e.jpg
    /// http://www1.autoimg.cn/video/wwwimg/2013/5/13/x_2013051309143831286.jpg
    /// </summary>
    /// <param name="imgSrc">图片地址</param>
    /// <param name="size">转换大小 例如：【100x100_0_q30_】</param>
    /// <param name="hosts">图片域名 例如[默认]：【www{0}.m.autoimg.cn】</param>
    /// <returns></returns>
    public static String ConvertImg(String imgsrc, String size, String hosts) {
        if (StringUtil.isNullOrEmpty(imgsrc)) {
            return StringUtils.EMPTY;
        }
        if (StringUtil.isNullOrEmpty(hosts)) {
            hosts = "www%d.m.autoimg.cn";
        }

        if (imgsrc.indexOf("~") == 0) {

            imgsrc = StringUtils.removeStart(imgsrc, "~");
        } else if (imgsrc.indexOf("/") != 0 && imgsrc.indexOf("http://") != 0) {
            imgsrc = "/" + imgsrc;
        }

        String[] pArray = imgsrc.split("/");
        String picName = pArray[pArray.length - 1];
        int offset = 0;

        if (picName.indexOf("autohomecar__") != -1) // 问过常玉增:cardfs只能说明产品库，文件名 内含有[autohomecar__] 才是完全统一的
        {
            offset = 2;
            picName = picName.substring(picName.lastIndexOf("autohomecar__"));
        } else if (picName.indexOf("x_") == 0 && pArray[3].indexOf("2") == 0) //处理 文字400x300代表图
        {
            picName = picName.substring(picName.indexOf("x_") + 2);
            pArray[3] = "3w/" + pArray[3];
        }  //http://www1.autoimg.cn/video/wwwimg/2013/5/13/x_2013051309143831286.jpg
        else if (pArray.length > 4 && picName.indexOf("z_") == 0 && (pArray[4] == "wwwimgpic" || pArray[4] == "wwwimg")) //处理文字代表图
        {
            //http://www0.autoimg.cn/video/wwwimgpic/2012/11/20/z_609c_d5817a8a.jpg
            //在这里处理 视频图片 把文件名前缀z_  x_ 统统修改为 x_ 这个x_的图片最大
            if (pArray[3] == ("video") && picName.indexOf("z_") == 0) {
                picName = "x_" + picName.substring(picName.indexOf("z_") + 2);// 替换z_ 为y_
            } else {
                picName = picName.substring(picName.indexOf("z_") + 2);
            }
        } else {
            picName = picName.substring(picName.lastIndexOf("_") + 1); // 不存在的时候 (-1+1=0); 删去标示图片大小的前缀
        }

        if (picName.equals(StringUtils.EMPTY) || pArray.length < 2)  //如果只传入 [asdfasdfsdaf.jpg]|[sdfadsf.jpg/]只有文件名，或者以/结尾
            return StringUtils.EMPTY;

        pArray[pArray.length - 1] = size + picName;


        if (pArray[0].indexOf("http:") == 0) {
            if (!hosts.equals(StringUtils.EMPTY)) {
                pArray[2] = hosts;
            }
            return String.format(String.join("/", pArray), GetImgDomainNum(picName) + offset);
        } else {

            return "http://" + String.format(hosts, GetImgDomainNum(picName) + offset) + String.join("/", pArray);
        }
    }


    private static int GetImgDomainNum(String name) {
        int r = 0, b = 0;
        while ((r += 4) < name.length()) {
            b ^= name.charAt(r);
        }
        b %= 2;
        return b;
    }
}
