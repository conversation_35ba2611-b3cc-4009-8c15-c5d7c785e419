package com.autohome.applet.util;

import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.security.SecureRandom;


public class SecurityKit {
    private final static String gaodepassword = "lightapp_2_gaode";

    public static void main(String[] arg) throws UnsupportedEncodingException {
//        String content = "s5IvDu9mqDvXEVKjH4d6Bw==";
//        String encryptstr = encrypt(content, "lightapp_2_gaode");
//        String decryptstr = decrypt("41B44B3085CB7BEDC7424EFB0205E05C", "lightapp_2_gaode");
//        System.out.println(encryptstr);
//        System.out.println(decryptstr);

//        String decode = URLDecoder.decode("%2Fcar-package%2Fpages%2Fpic%2Flist%2Findex%3Fauto_open_from%3Doutside_qixiubao%26seriesid%3D%24%7BF16141BC41FF6C746AE1A0278BD01719%7D%26specid%3D0", "UTF-8");
//        System.out.println(decode);
//
//        int decrypt = decrypt("B15CBB227087DC20AB3725EC59EF7E1A");
//        System.out.println(decrypt);
    }

    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    private static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    private static SecretKey generateKey(String strKey) {
        try {
            KeyGenerator generator = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(strKey.getBytes());
            generator.init(128, secureRandom);
            return generator.generateKey();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static String encrypt(Integer content) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            byte[] byteContent = Integer.toString(content).getBytes("utf-8");
            cipher.init(Cipher.ENCRYPT_MODE, generateKey(gaodepassword));
            byte[] result = cipher.doFinal(byteContent);
            return parseByte2HexStr(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(Integer content, String prefix) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            byte[] byteContent = Integer.toString(content).getBytes("utf-8");
            cipher.init(Cipher.ENCRYPT_MODE, generateKey(gaodepassword));
            byte[] result = cipher.doFinal(byteContent);
            return prefix + parseByte2HexStr(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(String content) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            byte[] byteContent = content.getBytes("utf-8");
            cipher.init(Cipher.ENCRYPT_MODE, generateKey(gaodepassword));
            byte[] result = cipher.doFinal(byteContent);
            return parseByte2HexStr(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(String content, String password) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            byte[] byteContent = content.getBytes("utf-8");
            cipher.init(Cipher.ENCRYPT_MODE, generateKey(password));
            byte[] result = cipher.doFinal(byteContent);
            return parseByte2HexStr(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String decrypt(String content, String password) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, generateKey(password));
            byte[] result = cipher.doFinal(parseHexStr2Byte(content));
            return new String(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static int decrypt(String content) {
        try {
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, generateKey(gaodepassword));
            byte[] result = cipher.doFinal(parseHexStr2Byte(content));
            String intvalue = new String(result);
            return StringUtils.isBlank(intvalue) ? 0 : Integer.parseInt(intvalue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
