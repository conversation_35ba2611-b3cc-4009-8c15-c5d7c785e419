package com.autohome.applet.util.caropenapi_uc_news_dealerservice_common;


import com.autohome.applet.util.netcoreapi.StringUtil;

import java.io.File;
import java.text.MessageFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by ha<PERSON>hanfeng on 2017/11/24.
 */
public class CarSettings {
    private CarSettings() {
    }

    private static CarSettings single = null;

    public static CarSettings getInstance() {
        if (single == null) {
            synchronized (CarSettings.class) {
                if (single == null) {
                    single = new CarSettings();
                }
            }
        }
        return single;
    }

    private String imageDomain = "https://car0.autoimg.cn";
    private String ImageDomain;
    private String webPhysicsPath;

    public static CarSettings getSingle() {
        return single;
    }

    public static void setSingle(CarSettings single) {
        CarSettings.single = single;
    }

    public String getWebPhysicsPath() {
        return webPhysicsPath;
    }

    public void setWebPhysicsPath(String webPhysicsPath) {
        this.webPhysicsPath = webPhysicsPath;
    }

    //region 。net方法
    //    public static string ConvertImg(string imgsrc, string size, string hosts = "www{0}.autoimg.cn")
//    {
//
//        if (string.IsNullOrEmpty(imgsrc))
//        {
//            return string.Empty;
//        }
//        if (imgsrc.IndexOf("car0.autoimg.cn") >= 0 || imgsrc.IndexOf("car1.autoimg.cn") >= 0)
//        {
//            if ((imgsrc.IndexOf("cardfs") >= 0) || imgsrc.IndexOf("carnews/") >= 0)
//            {
//                string _url = imgsrc;
//                string filename = Path.GetFileName(imgsrc);
//                _url = _url.Replace(filename, "");
//                Regex tNameReg = new Regex("^[a-zA-Z]+_");
//                if (tNameReg.IsMatch(filename))
//                {
//                    filename = tNameReg.Replace(filename, "");
//                }
//                switch (size)
//                {
//                    case "620x0_1_":
//                        _url = _url + "620x0_1_q87_" + filename; break;
//                    case "620x0_0_":
//                        _url = _url + "620x0_0_q87_" + filename; break;
//                    case "620x0_m910_":
//                        _url = _url + "620x0_m910_" + filename; break;
//                    default:
//                        _url = _url + filename;
//                        break;
//                }
//                return _url;
//            }
//            return fenbushi(imgsrc, size, hosts);
//        }
//        if (imgsrc.IndexOf("img3.autoimg.cn") >= 0)
//        {
//            return ConvertImgNew(imgsrc, size);
//        }
//        else if (imgsrc.IndexOf("qnwww2.autoimg.cn") >= 0 || imgsrc.IndexOf("qn.www2.autoimg.cn") >= 0)
//        {
//            return ConvertImgQiNiu(imgsrc, size);
//        }
//        else
//        {
//            return fenbushi(imgsrc, size, hosts);
//        }
//    }
    //    private static string ConvertImgNew(string imgsrc, string size)
//    {
//        string _url = imgsrc;
//        _url = _url.Contains("/soudfs/") ? _url : _url.Replace("img3.autoimg.cn/", "img3.autoimg.cn/soudfs/");
//        string filename = Path.GetFileName(imgsrc);
//        string filename1 = Path.GetFileName(imgsrc);
//        _url = _url.Replace(filename, "");
//        _url = _url + size + "autohomecar__" + filename1;
//        return _url;
//    }
//    private string fenbushi(string imgsrc, string size, string hosts = "www{0}.autoimg.cn")
//    {
//        if (imgsrc.IndexOf("~") == 0)
//        {
//            imgsrc = imgsrc.TrimStart('~');
//        }
//        else if (imgsrc.IndexOf("/") != 0 && imgsrc.IndexOf("http://") != 0 && imgsrc.IndexOf("https://") != 0)
//        {
//            imgsrc = "/" + imgsrc;
//        }
//
//        string[] pArray = imgsrc.Split('/');
//        string picName = pArray[pArray.Length - 1];
//        int offset = 0;
//
//        if (picName.IndexOf("autohomecar__") != -1) // 问过常玉增:cardfs只能说明产品库，文件名 内含有[autohomecar__] 才是完全统一的
//        {
//            offset = 2;
//            picName = picName.Substring(picName.LastIndexOf("autohomecar__"));
//        }
//        else
//        {
//            if (imgsrc.IndexOf("img3.autoimg.cn") >= 0)
//            {
//                return ConvertImgNew(imgsrc, size);
//            }
//            else
//            {
//                return imgsrc;
//            }
//        }
//        if (picName == "" || pArray.Length < 2)  //如果只传入 [asdfasdfsdaf.jpg]|[sdfadsf.jpg/]只有文件名，或者以/结尾
//            return "";
//        pArray[pArray.Length - 1] = size + picName;
//        if (pArray[0].IndexOf("http:") == 0|| pArray[0].IndexOf("https:") == 0)
//        {
//            pArray[2] = hosts;
//            return string.Format(string.Join("/", pArray), GetImgDomainNum(picName) + offset);
//        }
//        else
//        {
//            return string.Format("http://" + hosts + "{1}", GetImgDomainNum(picName) + offset, string.Join("/", pArray));
//        }
//    }
//    private static int GetImgDomainNum(string name)
//    {
//        int r = 0, b = 0;
//        while ((r += 4) < name.Length) { b ^= name[r]; }
//        b %= 2;
//        return b;
//    }
    //endregion
//    public String trimEnd(String value) {
//        int len = value.length();
//        int st = 0;
//        while ((st < len) && value.charAt(len - 1) == ' ') {
//            len--;
//        }
//        return value.substring(0, len);
//    }
//    private static String trimStart(String value){
//        int len = value.length();
//        int st = 0;
//        while ((st < len) && value.charAt(len - 1) == ' ') {
//            len--;
//        }
//        return value.substring(0, len);
//    }
    private static int GetImgDomainNum(String name) {
        int r = 0, b = 0;
        while ((r += 4) < name.length()) {
            b ^= name.toCharArray()[r];
        }
        b %= 2;
        return b;
    }

    private static String fenbushi(String imgsrc, String size, String hosts) {
        hosts = "www{0}.autoimg.cn";

        if (imgsrc.indexOf("~") == 0) {
            imgsrc = imgsrc.replace("~", "");
        } else if (imgsrc.indexOf("/") != 0 && imgsrc.indexOf("http://") != 0 && imgsrc.indexOf("https://") != 0) {
            imgsrc = "/" + imgsrc;
        }

        String[] pArray = imgsrc.split("/");
        String picName = pArray[pArray.length - 1];
        int offset = 0;

        if (picName.indexOf("autohomecar__") != -1) // 问过常玉增:cardfs只能说明产品库，文件名 内含有[autohomecar__] 才是完全统一的
        {
            offset = 2;
            picName = picName.substring(picName.lastIndexOf("autohomecar__"));
        } else {
            if (imgsrc.indexOf("img3.autoimg.cn") >= 0) {
                return ConvertImgNew(imgsrc, size);
            } else {
                return imgsrc;
            }
        }
        if (picName == "" || pArray.length < 2)  //如果只传入 [asdfasdfsdaf.jpg]|[sdfadsf.jpg/]只有文件名，或者以/结尾
            return "";
        pArray[pArray.length - 1] = size + picName;
        if (pArray[0].indexOf("http:") == 0 || pArray[0].indexOf("https:") == 0) {
            pArray[2] = hosts;
            return String.format(String.join("/", pArray), GetImgDomainNum(picName) + offset);
        } else {
            return String.format("http://" + hosts + "{1}", GetImgDomainNum(picName) + offset, String.join("/", pArray));
        }
    }

    public static String ConvertImg(String imgsrc, String size, String hosts) {
        hosts = "www{0}.autoimg.cn";
        if (StringUtil.isBlank(imgsrc)) {
            return "";
        }
        if (imgsrc.indexOf("car0.autoimg.cn") >= 0 || imgsrc.indexOf("car1.autoimg.cn") >= 0) {
            if ((imgsrc.indexOf("cardfs") >= 0) || imgsrc.indexOf("carnews/") >= 0) {
                String _url = imgsrc;
                File tempFile = new File(imgsrc.trim());
                // 图片名称
                String filename = tempFile.getName();
                _url = _url.replace(filename, "");
                String pattern = "^[a-zA-Z]+_";
                Pattern p = Pattern.compile(pattern);
                Matcher m = p.matcher(filename);
                if (m.find()) {
                    filename = m.replaceAll("");
                }
                switch (size) {
                    case "620x0_1_":
                        _url = _url + "620x0_1_q87_" + filename;
                        break;
                    case "620x0_0_":
                        _url = _url + "620x0_0_q87_" + filename;
                        break;
                    case "620x0_m910_":
                        _url = _url + "620x0_m910_" + filename;
                        break;
                    default:
                        _url = _url + filename;
                        break;
                }
                return _url;
            }
            return fenbushi(imgsrc, size, hosts);
        }
        if (imgsrc.indexOf("img3.autoimg.cn") >= 0) {
            return ConvertImgNew(imgsrc, size);
        }
//        else if (imgsrc.indexOf("qnwww2.autoimg.cn") >= 0 || imgsrc.indexOf("qn.www2.autoimg.cn") >= 0)
//        {
//            return ConvertImgQiNiu(imgsrc, size);
//        }
        else {
            return fenbushi(imgsrc, size, hosts);
        }
    }

    public static String ConvertImgNew(String imgsrc, String size) {
        String _url = imgsrc;
        _url = _url.contains("/soudfs/") ? _url : _url.replace("img3.autoimg.cn/", "img3.autoimg.cn/soudfs/");
        File tempFile = new File(imgsrc.trim());
        String filename = tempFile.getName();
        String filename1 = tempFile.getName().lastIndexOf("_autohomecar__") > 0 ? tempFile.getName().substring(tempFile.getName().lastIndexOf("_autohomecar__") + "_autohomecar__".length(), tempFile.getName().length()) : tempFile.getName();
        _url = _url.replace(filename, "");
        _url = _url + size + "autohomecar__" + filename1;
        return _url;
    }

    /// <summary>
    /// 获取带域名的图片路径
    /// </summary>
    /// <param name="imgurl">图片相对路径</param>
    /// <returns></returns>
    public String GetImageDomain(String imgurl) {
        if (!StringUtil.isNotNullAndWhiteSpace(imgurl))
            return imgurl;
        char[] str = imgurl.toLowerCase().substring(imgurl.lastIndexOf('/') + 1).toCharArray();
        int r = 0, b = 0;
        while ((r += 4) < imgurl.length()) {
            b ^= str[r];
        }
        b %= 2;
        return MessageFormat.format("https://car{0}.autoimg.cn{1}", Integer.toString(b), imgurl);
    }

    /// <summary>
    /// 通过图片路径获取图片域名
    /// </summary>
    /// <param name="path">图片路径</param>
    /// <returns>图片域名</returns>
    private String GetImageDomainNew(String path) {
        if (!StringUtil.isNotNullAndWhiteSpace(path))
            return path;
        char[] str = path.toLowerCase().substring(path.lastIndexOf('/') + 1, path.length()).toCharArray();
        int r = 0, b = 0;
        while ((r += 4) < path.length()) {
            b ^= Integer.parseInt(Integer.toString(path.toCharArray()[r]));
        }
        b %= 2;
        return MessageFormat.format("https://car{0}.autoimg.cn", path.contains("/cardfs/") ? Integer.toString(b + 2) : Integer.toString(b));
    }

    /// <summary>
    /// 获取图片完整路径
    /// </summary>
    /// <param name="path">图片路径</param>
    /// <returns>图片完整路径</returns>
    public String GetFullImagePath(String path) {
        if (!StringUtil.isNotNullAndWhiteSpace(path))
            return path;
        return MessageFormat.format("{0}{1}", GetImageDomainNew(path), path.replace("~", ""));
    }

    /// <summary>
    /// 获取图片完整路径(通过前缀改变图片尺寸)
    /// </summary>
    /// <param name="path">图片路径</param>
    /// <param name="prefix">前缀（图片尺寸区分）</param>
    /// <returns>图片完整路径</returns>
    public String GetFullImagePathByPrefix(String path, String prefix) {
        if (!StringUtil.isNotNullAndWhiteSpace(prefix)) {
            prefix = "";
        }
        if (!StringUtil.isNotNullAndWhiteSpace(path))
            return path;
        File tempFile = new File(path.trim());
        // 图片名称
        String fileName = tempFile.getName();
        // 图片目录
//        String directoryName = Path.GetDirectoryName(path).Replace("~", "").Replace("\\", "/");
        String directoryName = tempFile.getParent().replace("~", "").replace("\\", "/");
        // 图片域名
        String _imageDomain = GetImageDomainNew(path);
        // 图片名称起始索引
        int startIndex = path.contains("/cardfs/") ? fileName.indexOf("autohomecar__") : (fileName.indexOf("_") + 1);
        // 去掉已有前缀
        fileName = startIndex > 0 ? fileName.substring(startIndex, fileName.length()) : fileName;

        return MessageFormat.format("{0}{1}/{2}{3}", _imageDomain, directoryName, prefix, fileName);
    }

    /// <summary>
    /// 获取图片完整路径(通过前缀改变图片尺寸)
    /// </summary>
    /// <param name="path">图片路径</param>
    /// <param name="prefix">前缀（图片尺寸区分）</param>
    /// <param name="type">0不转换域名 1转换域名</param>
    /// <returns>图片完整路径</returns>
    public String GetFullImagePathByPrefix(String path, String prefix, int type) {
        if (!StringUtil.isNotEmpty(prefix)) {
            prefix = "";
        }
        // 图片域名
        String pattern = "^((http://)|(https://|//))?([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,6}(/)";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(path);
        String _imageDomain = "";
        if (m.find()) {
            _imageDomain = m.group();
        }
        if (type == 1) {
            _imageDomain = GetImageDomainNew(path);
        }
        if (!StringUtil.isNotEmpty(path))
            return path;
        File tempFile = new File(path.trim());
        // 图片名称
        String fileName = tempFile.getName();
        String directoryName = tempFile.getParent().replace("~", "").replace("\\", "/");
        if (StringUtil.isNotNullAndWhiteSpace(_imageDomain)) {
            directoryName = directoryName.replace(_imageDomain, "");
        }

        // 图片名称起始索引
        int startIndex = path.contains("/cardfs/") ? fileName.indexOf("autohomecar__") : (fileName.indexOf("_") + 1);
        // 去掉已有前缀
        fileName = startIndex > 0 ? fileName.substring(startIndex, fileName.length()) : fileName;

        return MessageFormat.format("{0}{1}/{2}{3}", _imageDomain, directoryName, prefix, fileName);
    }

    /// <summary>
    /// 获取图片完整路径(通过文件目录改变图片尺寸)
    /// 调用： GetFullImagePathByPrefix(path,"/oa/","/500a/")
    /// </summary>
    /// <param name="path">图片路径</param>
    /// <param name="oldDir">原文件目录</param>
    /// <param name="newDir">新文件目录</param>
    /// <returns>图片完整路径</returns>
    public String GetFullImagePathByDirectory(String path, String oldDir, String newDir) {
        if (!StringUtil.isNotNullAndWhiteSpace(oldDir)) {
            oldDir = "";
        }
        if (!StringUtil.isNotNullAndWhiteSpace(newDir)) {
            newDir = "";
        }
        if (!StringUtil.isNotNullAndWhiteSpace(path))
            return path;
        return MessageFormat.format("{0}{1}", GetImageDomainNew(path), (!StringUtil.isNotNullAndWhiteSpace(oldDir) || !StringUtil.isNotNullAndWhiteSpace(newDir)) ? path.replace("~", "") : path.replace(oldDir, newDir).replace("~", ""));
    }

    ///<summary>
    /// 为头像添加域名前缀
    /// </summary>
    /// <param name="headImageUrl">头像url</param>
    /// <returns></returns>
    public static String GetHeadImageUrl(String headImageUrl) {
        if (!StringUtil.isNotNullAndWhiteSpace(headImageUrl))
            return "";
        if (headImageUrl.startsWith("http://"))
            return headImageUrl;
        String domain = "";
        File tempFile = new File(headImageUrl.trim());
        // 图片名称
        String fileName = tempFile.getName();
        int r = 0, b = 0;
        while ((r += 4) < fileName.length()) {
            b ^= fileName.toCharArray()[r];
        }
        b %= 2;
        if (headImageUrl.contains("autohomecar__")) {
            b += 2;
            domain = MessageFormat.format("http://i{0}.autoimg.cn/usercenter/", b);
            if (headImageUrl.startsWith("/")) {
                headImageUrl = headImageUrl.substring(1);
            }
        } else {
            domain = MessageFormat.format("http://i{0}.autoimg.cn/album/userheaders", b);
        }

        return MessageFormat.format("{0}{1}", domain, headImageUrl);
    }
}
