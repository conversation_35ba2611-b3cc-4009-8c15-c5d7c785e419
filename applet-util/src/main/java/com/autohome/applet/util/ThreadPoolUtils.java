package com.autohome.applet.util;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工具类
 *
 * <AUTHOR>
 */
public class ThreadPoolUtils {

    private static ThreadPoolExecutor threadPool;


    static {
        /**
         * 核心线程数20个,达到队列上限 线程+1,线程数最大100个
         * 线程池最多能处理 1000+100,再多则需要本地线程执行
         * ThreadPoolExecutor.CallerRunsPolicy: 当线程池拒绝该任务的时候，线程在本地线程直接execute
         */
        threadPool = new ThreadPoolExecutor(
                4,
                50,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(100),
                new ThreadPoolExecutor.CallerRunsPolicy());// 丢弃，但是不抛出异常。等待桑芳再一次通知
    }


    private ThreadPoolUtils() {
    }

    /**
     * 获取线程池
     *
     * @return
     */
    public static ThreadPoolExecutor getInstance() {
        return threadPool;
    }


    /**
     * 异步执行任务
     *
     * @param command
     */
    public static void execute(Runnable command) {
        if (command != null) {
            getInstance().execute(command);
        }
    }
}
