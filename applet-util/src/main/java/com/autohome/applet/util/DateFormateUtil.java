package com.autohome.applet.util;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DateFormateUtil {

    public static String DEFAULT_FORMATE = "yyyy-MM-dd HH:mm:ss.SSS";

    public static String FULL_FORMATE = "yyyyMMdd";

    public static String getDateString(String pattern, Date date) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
            String rs = dateFormat.format(date);
            return rs;
        } catch (Exception e) {
            log.error("日期格式化错误," + e.getMessage(), e);
            return "";
        }
    }

    public static Date paresDate(String pattern, String strDate) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        try {
            return dateFormat.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static LocalDate paresLocalDate(String pattern, String strDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(strDate, formatter);
    }

    public static String formatLocalDate(String pattern, LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return formatter.format(date);
    }

    public static String formatLocalDateTime(String pattern, LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return formatter.format(dateTime);
    }

    public static Date dateAddMinute(Date date, int addMinute) {
        Calendar gc = new GregorianCalendar();
        gc.setTime(date);
        gc.add(Calendar.MINUTE, addMinute);
        return gc.getTime();
    }

    public static Date dateAddYear(Date date, int addYear) {
        Calendar gc = new GregorianCalendar();
        gc.setTime(date);
        gc.add(Calendar.YEAR, addYear);
        return gc.getTime();
    }

    public static void main(String args[]) {
        System.out.println(DateFormateUtil.getDateString(DateFormateUtil.FULL_FORMATE, new Date()));
    }


}
