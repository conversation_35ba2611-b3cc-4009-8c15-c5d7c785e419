package com.autohome.applet.util;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Arrays;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class AESUtil {

    /**
     * 加密算法
     */
    private static final String ENCRY_ALGORITHM_AES = "AES";

    /**
     * 加密算法/加密模式/填充类型
     * 本例采用AES加密，ECB加密模式，PKCS5Padding填充
     */
    private static final String CIPHER_MODE = "AES/ECB/PKCS5Padding";

    /**
     * 设置iv偏移量
     * 本例采用ECB加密模式，不需要设置iv偏移量
     */
    private static final String IV_ = null;

    /**
     * 设置加密字符集
     * 本例采用 UTF-8 字符集
     */
    private static final String UTF_8 = "UTF-8";

    /**
     * 密码处理方法
     * 如果加解密出问题，
     * 请先查看本方法，排除密码长度不足填充0字节,导致密码不一致
     *
     * @param password 待处理的密码
     * @return
     * @throws UnsupportedEncodingException
     */
    private static byte[] pwdHandler(String password) throws UnsupportedEncodingException {
        byte[] data = new byte[0];
        if (password != null) {
            byte[] bytes = password.getBytes(UTF_8);
            if (bytes.length > 32)
                throw new IllegalArgumentException("password length exceeds maximum supported length");

            int size = 0;
            int[] lengthArray = Arrays.stream(EnumPasswordSize.values()).mapToInt(p -> p.getValue()).sorted().toArray();
            for (int i = 0; i < lengthArray.length; i++) {
                if (bytes.length <= lengthArray[i]) {
                    size = lengthArray[i];
                    break;
                }
            }

            System.arraycopy(bytes, 0, data = new byte[size], 0, bytes.length);
        }
        return data;
    }

    //======================>原始加密<======================

    /**
     * 原始加密
     *
     * @param cipherMode 加密规则
     * @param textBytes  明文字节数组，待加密的字节数组
     * @param pwdBytes   加密密码字节数组
     * @return 返回加密后的密文字节数组，加密错误返回null
     */
    public static byte[] encrypt(String cipherMode, byte[] textBytes, byte[] pwdBytes) {
        try {
            // 1 获取加密密钥
            SecretKeySpec keySpec = new SecretKeySpec(pwdBytes, ENCRY_ALGORITHM_AES);
            Security.addProvider(new BouncyCastleProvider());
            // 2 获取Cipher实例
            Cipher cipher = Cipher.getInstance(cipherMode);

            // 查看数据块位数 默认为16（byte） * 8 =128 bit
//            System.out.println("数据块位数(byte)：" + cipher.getBlockSize());

            // 3 初始化Cipher实例。设置执行模式以及加密密钥
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            // 4 执行
            byte[] cipherTextBytes = cipher.doFinal(textBytes);

            // 5 返回密文字符集
            return cipherTextBytes;

        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解密
     *
     * @param cipherMode      加密规则
     * @param cipherTextBytes 待解密的字节数组
     * @param pwdBytes        解密密码字节数组
     * @return 返回解密后的明文字节数组，解密错误返回null
     */
    public static byte[] decrypt(String cipherMode, byte[] cipherTextBytes, byte[] pwdBytes) {

        try {
            // 1 获取解密密钥
            SecretKeySpec keySpec = new SecretKeySpec(pwdBytes, ENCRY_ALGORITHM_AES);
            Security.addProvider(new BouncyCastleProvider());
            // 2 获取Cipher实例
            Cipher cipher = Cipher.getInstance(cipherMode);

            // 3 初始化Cipher实例。设置执行模式以及加密密钥
            cipher.init(Cipher.DECRYPT_MODE, keySpec);

            // 4 执行
            byte[] clearTextBytes = cipher.doFinal(cipherTextBytes);

            // 5 返回明文字符集
            return clearTextBytes;

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 解密错误 返回null
        return null;
    }

    //======================>BASE64<======================


    //======================>HEX<======================

    /**
     * 加密
     *
     * @param cipherMode 加密规则
     * @param clearText  明文，待加密的内容
     * @param password   密码，加密的密码
     * @return 返回密文字节数组的Hex形式。加密错误返回null
     */
    public static String encryptHex(String cipherMode, String clearText, String password) {
        try {
            // 1 获取加密密文字节数组
            byte[] cipherTextBytes = encrypt(cipherMode, clearText.getBytes(UTF_8), pwdHandler(password));

            // 2 对密文字节数组进行 转换为 HEX输出密文
            String cipherText = byte2hex(cipherTextBytes);

            // 3 返回 HEX输出密文
            return cipherText;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 加密错误返回null
        return null;
    }

    /**
     * 加密
     *
     * @param cipherMode 加密规则
     * @param clearText 明文，待加密的内容
     * @param password  密码，加密的密码
     * @return 返回密文字节数组的base64形式。加密错误返回null
     */
//    public static String encryptBase64(String cipherMode, String clearText, String password) {
//        try {
//            // 1 获取加密密文字节数组
//            byte[] cipherTextBytes = encrypt(cipherMode, clearText.getBytes(UTF_8), pwdHandler(password));
//
//            // 2 对密文字节数组进行 转换为 base64输出密文
//            String cipherText = Base64Utils.encodeToString(cipherTextBytes);
//
//            // 3 返回 base64输出密文
//            return cipherText;
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        // 加密错误返回null
//        return null;
//    }

    /**
     * 解密Hex字符串
     *
     * @param cipherText 密文，带解密的内容
     * @param password   密码，解密的密码
     * @return 返回明文，解密后得到的内容。解密错误返回null
     */
    public static String decryptHex(String cipherMode, String cipherText, String password) {
        try {
            // 1 将HEX输出密文 转为密文字节数组
            byte[] cipherTextBytes = hex2byte(cipherText);

            // 2 将密文字节数组进行解密 得到明文字节数组
            byte[] clearTextBytes = decrypt(cipherMode, cipherTextBytes, pwdHandler(password));

            // 3 根据 CHARACTER 转码，返回明文字符串
            return new String(clearTextBytes, UTF_8);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 解密错误返回null
        return null;
    }

    /**
     * 解密base64字符串
     *
     * @param cipherText 密文，带解密的内容
     * @param password   密码，解密的密码
     * @return 返回明文，解密后得到的内容。解密错误返回null
     */
//    public static String decryptBase64(String cipherMode, String cipherText, String password) {
//        try {
//            // 1 将HEX输出密文 转为密文字节数组
//            byte[] cipherTextBytes = Base64Utils.decodeFromString(cipherText);
//
//            // 2 将密文字节数组进行解密 得到明文字节数组
//            byte[] clearTextBytes = decrypt(cipherMode, cipherTextBytes, pwdHandler(password));
//
//            // 3 根据 CHARACTER 转码，返回明文字符串
//            return new String(clearTextBytes, UTF_8);
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        // 解密错误返回null
//        return null;
//    }

    /*字节数组转成16进制字符串  */
    public static String byte2hex(byte[] bytes) { // 一个字节的数，
        StringBuffer sb = new StringBuffer(bytes.length * 2);
        String tmp = "";
        for (int n = 0; n < bytes.length; n++) {
            // 整数转成十六进制表示
            tmp = (Integer.toHexString(bytes[n] & 0XFF));
            if (tmp.length() == 1) {
                sb.append("0");
            }
            sb.append(tmp);
        }
        return sb.toString().toUpperCase(); // 转成大写
    }

    /*将hex字符串转换成字节数组 */
    private static byte[] hex2byte(String str) {
        if (str == null || str.length() < 2) {
            return new byte[0];
        }
        str = str.toLowerCase();
        int l = str.length() / 2;
        byte[] result = new byte[l];
        for (int i = 0; i < l; ++i) {
            String tmp = str.substring(2 * i, 2 * i + 2);
            result[i] = (byte) (Integer.parseInt(tmp, 16) & 0xFF);
        }
        return result;
    }

    public enum EnumPasswordSize {
        Sixteen(16), TweentyFour(24), ThirtyTwo(32);
        int value;

        EnumPasswordSize(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public final static String md5(String plainText) {

        // 返回字符串
        String md5Str = null;
        try {
            // 操作字符串
            StringBuffer buf = new StringBuffer();

            /**
             * MessageDigest 类为应用程序提供信息摘要算法的功能，如 MD5 或 SHA 算法。
             * 信息摘要是安全的单向哈希函数，它接收任意大小的数据，并输出固定长度的哈希值。
             *
             * MessageDigest 对象开始被初始化。
             * 该对象通过使用 update()方法处理数据。
             * 任何时候都可以调用 reset()方法重置摘要。
             * 一旦所有需要更新的数据都已经被更新了，应该调用digest()方法之一完成哈希计算。
             *
             * 对于给定数量的更新数据，digest 方法只能被调用一次。
             * 在调用 digest 之后，MessageDigest 对象被重新设置成其初始状态。
             */
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 添加要进行计算摘要的信息,使用 plainText 的 byte 数组更新摘要。
            md.update(plainText.getBytes());

            // 计算出摘要,完成哈希计算。
            byte b[] = md.digest();
            int i;

            for (int offset = 0; offset < b.length; offset++) {

                i = b[offset];

                if (i < 0) {
                    i += 256;
                }

                if (i < 16) {
                    buf.append("0");
                }

                // 将整型 十进制 i 转换为16位，用十六进制参数表示的无符号整数值的字符串表示形式。
                buf.append(Integer.toHexString(i));

            }

            // 32位的加密
            md5Str = buf.toString();

            // 16位的加密
            // md5Str = buf.toString().md5Strstring(8,24);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5Str;
    }

    private static final String KEY_AES = "AES";
    /**
     * 通过AES算法解密（平安解密）
     * @param src 需要解密得字符串
     * @param key 密钥
     * @return
     * @throws Exception
     */
    public static String decrypt(String src, String key) throws Exception {

        SecretKeySpec secretKeySpec = getSecretKeySpec(key);

        Cipher cipher = Cipher.getInstance(KEY_AES);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);

        byte[] encryptedBytes = HexUtil.hexStr2Bytes(src);
        byte[] decryptBytes = cipher.doFinal(encryptedBytes);

        return new String(decryptBytes);
    }


    private static SecretKeySpec getSecretKeySpec(String key){

        byte[] raw = HexUtil.hexStr2Bytes(key);
        return new SecretKeySpec(raw, KEY_AES);
    }
}