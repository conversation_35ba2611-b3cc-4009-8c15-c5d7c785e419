package com.autohome.applet.util.uploadfile;

import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ImageUploader {
    private String GetTokenUrl;
    private String SaveFileUrl;
    private String CheckTokenUrl;
    private String DelFileUrl;
    private int minutes;
    private boolean isValid;
    private String _token;
    private String host;
    private String path;
    private String source;
    private String key;

    public ImageUploader(String host, String path, String source, String key) {
        this.GetTokenUrl = "http://baseup.afs.bj.autohome.com.cn/service/get/token";
        this.SaveFileUrl = "http://baseup.afs.bj.autohome.com.cn/service/fastUp";
        this.CheckTokenUrl = "http://baseup.sjz.autohome.com.cn/service/search/token";
        this.DelFileUrl = "http://baseup.afs.bj.autohome.com.cn/service/DelImgNew";
        this.minutes = 100;
        this.isValid = false;
        this._token = "";
        this.host = host;
        this.path = path;
        this.source = source;
        this.key = key;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    private String getToken() throws Exception {
        if (this._token.isEmpty()) {
            String url = this.GetTokenUrl + "?source=" + this.source + "&key=" + this.key + "&minutes=" + this.minutes + "";
            Map<String, Object> field = new HashMap<>();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance(5000).httpPost(url, field);
            if (httpResult == null || httpResult.getStatusCode() != 200 ) {
                log.error("getToken {} is error result: {}",url, JacksonHelper.serialize(httpResult));
                return "";
            }

            if (org.springframework.util.StringUtils.isEmpty(httpResult.getBody())){
                log.warn("getToken 源接口调用为null url:{}",url);
                return null;
            }

            JsonResult jsonResult = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<JsonResult>() {});

            if (jsonResult == null) {
                log.warn("getToken returnResult is null");
                return null;
            }

            if (jsonResult.getCode() != 0 ){
                log.warn("getToken {} is error result: {}",url, JacksonHelper.serialize(httpResult));
                return null;
            }

            if (jsonResult.getResult() == null){
                log.warn("getToken 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(jsonResult));
                return null;
            }
//            String jsonResult = HttpClientUtils.post(this.GetTokenUrl, "source=" + this.source + "&key=" + this.key + "&minutes=" + this.minutes + "", "application/x-www-form-urlencoded", "UTF-8", 1000, 3000);
//            JsonResult result = JacksonHelper.deserialize(jsonResult, JsonResult.class);
//            if (result.getCode() != 0 || result.getResult().length() <= 0) {
//                throw new Exception("获取token失败");
//            }

            this._token = jsonResult.getResult();
            return this._token;
        }

        return this._token;
    }

//    public boolean Del(String img) throws Exception {
//        return this.Del(img, 1);
//    }
//
//    public boolean Del(String img, int type) throws Exception {
//        JsonResult jsonResult = this.DelImg(img, type);
//        return jsonResult.getCode() == 0;
//    }
//
//    public JsonResult DelImg(String img, int type) throws Exception {
//        String jsonResult = HttpClientUtils.post(this.DelFileUrl, "source=" + this.source + "&type=" + type + "&token=" + this.getToken() + "&url=" + img, "application/x-www-form-urlencoded", "UTF-8", 1000, 3000);
//        return (JsonResult)JsonUtils.toObject(jsonResult, JsonResult.class);
//    }

    public UploadFileInfo Save(String img) throws Exception {
        String[] parts = img.split(",");
        String imageString = parts[1];
        String exName = "jpg";
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] imageByte = decoder.decode(imageString);
        ByteArrayInputStream bis = new ByteArrayInputStream(imageByte);
        UploadFileInfo fileInfo = this.Save((InputStream)bis, exName);
        bis.close();
        return fileInfo;
    }

    public UploadFileInfo Save(InputStream stream, String exName) throws Exception {
        ContentBody cd = new InputStreamBody(stream, "1.jpg");
        return this.Save((ContentBody)cd, exName);
    }

    public UploadFileInfo Save(ContentBody file, String exName) throws Exception {
        UploadFileInfo result = new UploadFileInfo();
        HttpPost httpRequest = new HttpPost(this.SaveFileUrl);
        MultipartEntity multipartEntity = new MultipartEntity();
        multipartEntity.addPart("token", new StringBody(this.getToken()));
        multipartEntity.addPart("exName", new StringBody(exName));
        multipartEntity.addPart("source", new StringBody(this.source));
        multipartEntity.addPart("file", file);

        try {
            httpRequest.setEntity(multipartEntity);
            HttpResponse httpResponse = (new DefaultHttpClient()).execute(httpRequest);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String jsonResult = EntityUtils.toString(httpResponse.getEntity());
                JsonResult obj = JacksonHelper.deserialize(jsonResult, JsonResult.class);
                if (obj.getCode() == 0 && obj.getResult().length() > 0) {
                    String result1 = this.toHttpImageUrl(obj.getResult());
                    result.setImgUrl(result1);
                } else {
                    result.setReturnCode(obj.getCode());
                    result.setMessage("上传图片失败");
                }
            } else {
                result.setReturnCode(-10000);
                result.setMessage("图片服务器返回异常");
            }
        } catch (Exception var10) {
            result.setReturnCode(-10000);
            result.setMessage("图片服务器返回异常");
        }

        return result;
    }

    private String toHttpImageUrl(String result1) {
        Path p = Paths.get(result1);
        String imageName = p.getFileName().toString();
        if (StringUtils.isEmpty(this.path)) {
            this.path = "newsdfs";
        }

        String newPath = this.path + "/" + result1.replace(imageName, "autohomecar__" + imageName);
        String url;
        if (StringUtils.isEmpty(this.host)) {
            url = ImageHelper.AddImageDomain(newPath);
        } else {
            url = ImageHelper.AddImageDomain(newPath, this.host);
        }

        return url;
    }

    public static final class Builder {
        private String host;
        private String path;
        private String source;
        private String key;

        private Builder() {
            this.host = null;
            this.path = null;
        }

        public Builder host(String host) {
            this.host = host;
            return this;
        }

        public Builder path(String path) {
            this.path = path;
            return this;
        }

        public Builder source(String source) {
            this.source = source;
            return this;
        }

        public Builder key(String key) {
            this.key = key;
            return this;
        }

        public ImageUploader build() {
            if (StringUtils.isEmpty(this.source)) {
                throw new RuntimeException("SOURCE 未设置，请设置");
            } else if (StringUtils.isEmpty(this.key)) {
                throw new RuntimeException("KEY 未设置，请设置");
            } else {
                return new ImageUploader(this.host, this.path, this.source, this.key);
            }
        }
    }
}
