package com.autohome.applet.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class SignUtil {

    private static final Logger logger = LoggerFactory.getLogger(SignUtil.class);

    public static String createLightAppSign(Map<String, String[]> paramsMap, String appkey, String className) {
        List<String> keys = new ArrayList<String>(paramsMap.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        sb.append(appkey);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String[] value = paramsMap.get(key);
            if (!key.equals("_sign")) {
                String val = value == null || value.length == 0 ? "" : value[0];
                sb.append(key + val);
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        //LogHelper.logInfo(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        //LogHelper.logInfo(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }


    /**
     * 经销商签名
     */
    public static String createDealerSign(Map<String, String> paramsMap, String appkey, String className) {
        appkey = "#JesuisDealerCApi!";
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        sb.append(appkey);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                sb.append(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }

    /**
     * 违章
     */
    public static String createWzSign(TreeMap<String, String> p, String vkey, String className) {
        vkey = "gVLV2xHHrDdpna7S";
        List<String> keys = new ArrayList<String>(p.keySet());
        int keysize = keys.size();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keysize; i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                sb.append(key + "=" + value.toString());
                if (i < keysize - 1) {
                    sb.append("&");
                }
            }
        }
        String signstr = sb.toString().replace("\\s*", "");
        signstr += vkey;
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", signstr));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(signstr.getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }


    public static String createChejiahaoSign(Map<String, String> paramsMap, String appkey, String className) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        sb.append(appkey);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                sb.append(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }


    public static String createSign(Map<String, String> paramsMap, String appkey, String className) {
        appkey = "@7U$aPOE@$";
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        sb.append(appkey);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                sb.append(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }


    public static String createDanmuSign(Map<String, String> paramsMap, String appkey, String className) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        sb.append(appkey);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("_sign")) {
                sb.append(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }


    /**
     * 拍照图像识别接口（对外）生成签名
     *
     * @param paramsMap
     * @param appkey
     * @return
     */
    public static String createRecognizePicSign(Map<String, String> paramsMap, String appkey) {
        String[] keys = new String[]{"service_type", "_appId", "imageType", "fileBase64"};
        Arrays.sort(keys);
        HashMap<String, String> values = new HashMap<>();
        values.put("service_type", "quickapp");
        values.put("_appId", "quickapp");
        values.put("imageType", paramsMap.get("imageType"));
        values.put("fileBase64", paramsMap.get("fileBase64"));

        StringBuffer plaintext = new StringBuffer();
        plaintext.append(appkey);

        for (int i = 0; i < keys.length; i++) {
            plaintext.append(keys[i]);
            plaintext.append(values.get(keys[i]));
        }
        plaintext.append(appkey);
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plaintext.toString().getBytes());
            return new BigInteger(1, md.digest()).toString(16).toUpperCase();
        } catch (Exception e) {
            return "";
        }
    }


    public static String createAppSign(Map<String, String> paramsMap, String appkey, String className) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        sb.append(appkey);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                sb.append(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString().toUpperCase()));
        return buf.toString().toUpperCase();
    }

    public static String sign(Map<String, String> map, String appkey) throws NoSuchAlgorithmException {

        StringBuilder sb = new StringBuilder();
        for (Map.Entry me : map.entrySet()) {
            sb.append((String) me.getKey()).append("=").append((String) me.getValue()).append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(appkey);
        System.out.println("加密前：" + sb.toString());
        String md = "";
        try {
            md = Md5(sb.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("加密后：" + md);
        return md;
    }

    public static String Md5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(input.getBytes());
        byte b[] = md.digest();
        int i;
        StringBuffer buf = new StringBuffer("");
        for (int offset = 0; offset < b.length; offset++) {
            i = b[offset];
            if (i < 0) i += 256;
            if (i < 16) buf.append("0");
            buf.append(Integer.toHexString(i));
        }
        //debug
        System.out.println("Md5=" + buf.toString().toUpperCase());

        return buf.toString();
    }


    public static String createChefuwuApiSign(Map<String, String> paramsMap, String appkey, String className) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        List<String> item = new ArrayList<>();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                item.add(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString()));
        return buf.toString();
    }


    public static String createCouponapiSign(Map<String, String> paramsMap, String appkey, String className) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        List<String> item = new ArrayList<>();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                item.add(key + value.toString());
            }
        }
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString()));
        return buf.toString();
    }


    public static String createWzopenSign(Map<String, String> paramsMap, String appkey, String className) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        List<String> item = new ArrayList<>();
        for (int i = 0; i < keys.size(); i++) {

            String key = keys.get(i);
            Object value = p.get(key);
            if (value == null || value.toString().equals("")) {
                continue;
            }
            if (!key.equals("sign")) {
                item.add(key + "=" + value.toString());
            }
        }
        String params = StringUtils.join(item, "&");
        sb.append(params);
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString()));
        return buf.toString();
    }

    public static String createRiskSign(Map<String, String> paramsMap, String appkey, String className) {
        appkey = "@7U$aPOE@$";
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        List<String> item = new ArrayList<>();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("sign")) {
                item.add(key + "=" + value.toString());
            }
        }
        String params = StringUtils.join(item, "&");
        sb.append(params);
        sb.append(appkey);
        String mark = String.valueOf(System.currentTimeMillis()) + String.valueOf((int) (Math.random() * 1000));
        logger.info(String.format("[" + mark + "]接口[" + className + "]签名前字符串信息:[%s]", sb.toString()));
        MessageDigest md;
        StringBuffer buf = new StringBuffer("");
        try {
            md = MessageDigest.getInstance("MD5");
            try {
                md.update(sb.toString().getBytes("utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            byte b[] = md.digest();
            int i;
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        logger.info(String.format("[" + mark + "]接口[" + className + "]主软件服务器签名:[%s]", buf.toString()));
        return buf.toString();
    }

    public static String createQuickPassSign(Map<String, String> paramsMap) {
        SortedMap<String, String> p = new TreeMap<String, String>(paramsMap);
        List<String> keys = new ArrayList<String>(p.keySet());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = p.get(key);
            if (!key.equals("signature")) {
                sb.append(key).append("=").append(value.toString()).append("&");
            }
        }
        String substring = sb.substring(0, sb.toString().lastIndexOf("&"));
        logger.info("云闪付验签的字符串:{}", substring);
        return sha256(substring.getBytes(StandardCharsets.UTF_8));
    }

    public static String sha256(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            return bytesToHex(md.digest(data));
        } catch (Exception ex) {
            logger.info("Never happen.", ex);
            return null;
        }
    }

    public static String createNonceStr() {
        String sl = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            sb.append(sl.charAt(new Random().nextInt(sl.length())));
        }
        return sb.toString();
    }

    public static String bytesToHex(byte[] bytes) {
        String hexArray = "0123456789abcdef";
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            int bi = b & 0xff;
            sb.append(hexArray.charAt(bi >> 4));
            sb.append(hexArray.charAt(bi & 0xf));
        }
        return sb.toString();
    }

}
