package com.autohome.applet.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @description: 车系配置接口
 * @author: <PERSON>BoWen
 * @date: 2023-11-27
 **/
@Data
@Component
@ConfigurationProperties( prefix = "car.api")
public class CarApiUrl {

    private String getallseries;

    private String getallbrands;

    private String getallfcts;

    private String getUserLikeCarSeries;

    private String getHotCarSeries;

    private String getCarSeriesInfo;

}
