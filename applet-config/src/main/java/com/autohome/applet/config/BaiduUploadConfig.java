package com.autohome.applet.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "baidu.alading")
@Data
@Slf4j
/**
 * 百度分配的资源信息
 * */
public class BaiduUploadConfig {
    private String appkey;
    private String from;
    private String orgPath;
    private String orgParams;
    private String orgUrl;
    private String productPath;
    private String productParams;
    private String productUrl;
    private String uploadUrl;
    /**
     * 百度分配的资源id
     * */
    private String siteid;
    private String orgCategory;
    private String orgSourcename;
    private String productCategory;
}
