spring:
  redis-ext:
    liveplatform:
      host: *************
      port: 29005
      password:
      db: 0
      lettuce:
        pool:
          max-active: 10
          max-idle: 10
          max-wait: -1
          min-idle: 10
      timeout: 1000
      shutdown-timeout: 100
    lightapp:
      host: *************
      port: 29005
      password:
      db: 0
      lettuce:
        pool:
          max-active: 10
          max-idle: 10
          max-wait: -1
          min-idle: 10
      timeout: 1000
      shutdown-timeout: 100
    dealerservice:
      host: 127.0.0.1
      port: 6379
      password:
      db: 0
      lettuce:
        pool:
          max-active: 10
          max-idle: 10
          max-wait: -1
          min-idle: 10
      timeout: 1000
      shutdown-timeout: 100
    apidataredis:
      host: *************
      port: 29005
      password:
      db: 0
      lettuce:
        pool:
          max-active: 100
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
    redishosts:
      host: **************
      port: 7000
      password: Auto_2019
      db: 0
      lettuce:
        pool:
          max-active: 30
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: ALWAYS

mybatis:
  mapper-locations: classpath:javaapi/mapper/*.xml,classpath:netcoreapi/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 云闪付配置
quickpass:
  appid: fe19a406d6f44c50932fe0029c86a247
  secret: b4695b5a38fa46d1b4f9fc482a0de597
  decode-secret: f74f8932a1e98fec403bea649dc84feaf74f8932a1e98fec
wechat:
  search: #搜一搜功能
    test: 0 # 接入微信API的测试环境,0正式，1测试

baidu:
  alading:
    appkey: U45U6Em0gQvkASWjnND9XvQdKGg2xc6o
    from: autohome
    siteid: 672613
    org_path: /usedcar/shop
    org_params: id=%s
    org_url: https://quickapp-h5.autohome.com.cn/usedcar/shop?id=%s
    org_category: 交通出行_车辆平台_汽车电商_二手车平台_二手车售卖
    org_sourcename: 汽车之家
    product_path: /usedcar
    product_params: id=%s
    product_category: 交通出行_车辆平台_汽车电商_二手车平台_二手车售卖
    product_url: https://quickapp-h5.autohome.com.cn/usedcar?id=%s
    upload_url: http://ping.kg.baidu.com/push?cat=672613&site=http%3A%2F%2Fwww.autohome.com.cn%2F&schema=10958&source=4856&token=MDZmNDhiM2I1NWNhNzljOGYyMDdmOWNmMjA1NjJlNTRiZGY5YTcxOA%3D%3D&s=1048576&router=aladdin

car:
  api:
    getallseries: http://car.api.autohome.com.cn/v2/Base/Series_GetAllSeries.ashx?_appid=car
    getallbrands: http://car.api.autohome.com.cn/v2/Base/Brand_GetAllBrands.ashx?_appid=car
    getallfcts: http://car.api.autohome.com.cn/v2/Base/Fct_GetAllFcts.ashx
    get_user_like_car_series: http://portrayal-app-personas.autohome.com.cn/w/upb2
    get_hot_car_series: http://portrayal-app-personas.autohome.com.cn/cpc/getHotCar
    get_car_series_info: http://car.api.autohome.com.cn/v1/CarPrice/Series_BaseInfoBySeriesList.ashx


qq:
  upload:
    url: https://xsearch.qq.com/data_access/push?appid={appid}&timestamp={timestamp}&nonce={nonce}&sign={sign}


dealer:
  api:
    getfilters: https://dealer.api.autohome.com.cn/dealerlist/list/GetFilters?_appId=miniprogram&cid={cid}&brandid={brandid}
    getseriesminpriceextends: http://dealer.api.lq.autohome.com.cn/statistics/seriesprice/getSeriesMinPriceExtends?_appId=miniprogram&cityid={cid}&seriesIds={seriesId}&isextends=0
    # getdealerlistseriesnew: http://apigateway.corpautohome.com/jxsjs/ics/yhz/dealerlq/v1/dealerlist/list/GetDealerListSeriesNew?_appId=miniprogram&cityId={cid}&seriesId={seriesId}
    getdealerlistseriesnew: http://dealer.api.lq.autohome.com.cn/dealerlist/list/GetDealerListSeriesNew?_appid=car&cityId={cityId}&seriesId={seriesId}&pageIndex={pageIndex}&pageSize={pageSize}&isNeedVirtual={isNeedVirtual}

wx:
  #汽车之家微信公众号token
  officialAccountTokenUrl: http://x.api.corpautohome.com/ltam/mp/getAccessToken
  unionIdUrl: https://api.weixin.qq.com/cgi-bin/user/info

newcar:
  foucs-url: http://autoshow.in.autohome.com.cn/newseriesoperation/gettoplist