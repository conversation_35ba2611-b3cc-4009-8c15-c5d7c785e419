spring:
  redis-ext:
    liveplatform:
      host: lightapp-fl.rediscluster.cache.corpautohome.com
      port: 31200
      password: Applet666
      db: 0
      lettuce:
        pool:
          max-active: 100
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
    dealerservice:
      host: applet-fl.rediscluster.cache.corpautohome.com
      port: 31160
      password: Applet666
      db: 0
      lettuce:
        pool:
          max-active: 100
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
    lightapp:
      host: lightapp-fl.rediscluster.cache.corpautohome.com
      port: 31200
      password: Applet666
      db: 0
      lettuce:
        pool:
          max-active: 100
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
    apidataredis:
      host: light-data-fl.rediscluster.cache.corpautohome.com
      port: 31220
      password: Applet666
      db: 0
      lettuce:
        pool:
          max-active: 100
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
    redishosts:
      host: light-core-fl.rediscluster.cache.corpautohome.com
      port: 31210
      password: Applet666
      db: 0
      lettuce:
        pool:
          max-active: 30
          max-idle: 30
          max-wait: -1
      timeout: 1000
      shutdown-timeout: 100
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: ALWAYS

mybatis:
  mapper-locations: classpath:javaapi/mapper/*.xml,classpath:netcoreapi/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true

# 云闪付配置
quickpass:
  appid: cd327e826b844fb98b4088a538ac38b1
  secret: 5be3fccf24fc46e686394bb452d77cdd
  decode-secret: ba37975de5ece68abac7a29b89859132ba37975de5ece68a
wechat:
  search: #搜一搜功能
    test: 0 # 接入微信API的正式环境


baidu:
  alading:
    appkey: U45U6Em0gQvkASWjnND9XvQdKGg2xc6o
    from: autohome
    siteid: 672613
    org_path: /usedcar/shop
    org_params: id=%s
    org_url: https://quickapp-h5.autohome.com.cn/usedcar/shop?id=%s
    org_category: 交通出行_车辆平台_汽车电商_二手车平台_二手车售卖
    org_sourcename: 汽车之家
    product_path: /usedcar
    product_params: id=%s
    product_category: 交通出行_车辆平台_汽车电商_二手车平台_二手车售卖
    product_url: https://quickapp-h5.autohome.com.cn/usedcar?id=%s
    upload_url: http://ping.kg.baidu.com/push?cat=672613&site=http%3A%2F%2Fwww.autohome.com.cn%2F&schema=10958&source=4856&token=MDZmNDhiM2I1NWNhNzljOGYyMDdmOWNmMjA1NjJlNTRiZGY5YTcxOA%3D%3D&s=1048576&router=aladdin

car:
  api:
    getallseries: http://car.api.autohome.com.cn/v2/Base/Series_GetAllSeries.ashx?_appid=car
    getallbrands: http://car.api.autohome.com.cn/v2/Base/Brand_GetAllBrands.ashx?_appid=car
    getallfcts: http://car.api.autohome.com.cn/v2/Base/Fct_GetAllFcts.ashx
    get_user_like_car_series: http://portrayal-app-personas.autohome.com.cn/w/upb2
    get_hot_car_series: http://portrayal-app-personas.autohome.com.cn/cpc/getHotCar
    get_car_series_info: http://car.api.autohome.com.cn/v1/CarPrice/Series_BaseInfoBySeriesList.ashx

qq:
  upload:
    url: https://xsearch.qq.com/data_access/push?appid={appid}&timestamp={timestamp}&nonce={nonce}&sign={sign}



dealer:
  api:
    getfilters: https://dealer.api.autohome.com.cn/dealerlist/list/GetFilters?_appId=miniprogram&cid={cid}&brandid={brandid}
    getseriesminpriceextends: http://dealer.api.lq.autohome.com.cn/statistics/seriesprice/getSeriesMinPriceExtends?_appId=miniprogram&cityid={cid}&seriesIds={seriesId}&isextends=0
    # getdealerlistseriesnew: http://apigateway.corpautohome.com/jxsjs/ics/yhz/dealerlq/v1/dealerlist/list/GetDealerListSeriesNew?_appId=miniprogram&cityId={cid}&seriesId={seriesId}
    getdealerlistseriesnew: http://dealer.api.lq.autohome.com.cn/dealerlist/list/GetDealerListSeriesNew?_appid=car&cityId={cityId}&seriesId={seriesId}&pageIndex={pageIndex}&pageSize={pageSize}&isNeedVirtual={isNeedVirtual}


wx:
  #汽车之家微信公众号token
  officialAccountTokenUrl: http://x.api.corpautohome.com/ltam/mp/getAccessToken
  unionIdUrl: https://api.weixin.qq.com/cgi-bin/user/info

newcar:
  foucs-url: http://autoshow.in.autohome.com.cn/newseriesoperation/gettoplist