package com.autohome.applet.service.openapi;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarVrInfo2;
import com.autohome.applet.model.dto.vrinfodata.*;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: WangBoWen
 * @date: 2024-03-15
 **/
public interface VRInfoService {

    List<PingAnCarSalesRankDTO> getCarSalesRank();

    List<CarSalesRankDTO> getCarSalesRankData(Integer maxPrice, Integer minPrice,
                                              String beginMonth, String endMonth,
                                              String levels, Integer size,
                                              String manutype, Integer isNewenergy);

    NewSeriesNew getSerieslistHasVRByBrandId(Integer brandId);

    CarVrInfo2 getcarvrinfobyseriesid(String category, int seriesid);

    List<BrandListNewResult> getVrBrandListNewToList();

    Map<Integer, Brandlist> getBrandlistHasVR();

    List<BrandListNewResult> getBrandListNewToList(Integer state);
}
