package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.netcoreapi.UserService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UserServiceImpl implements UserService {

    String urlWxUserRegisterBind = "http://user.api.autohome.com.cn/api/WxUser/RegisterBind";
    String urlRegOrLoginByMobileCode = "http://user.api.autohome.com.cn/api/userlogin/RegOrLoginByMobileCode";

    @Autowired
    private HttpService httpService;

    @Override
    public Map<String, Object> WxUserRegisterBind(String mobilePhone, String validCode, String sessionKey, String iv
            , String rawData, String signature, String encryptedData, String clientIp, String openid, String clienttype) throws UnsupportedEncodingException {
        if (openid == null) {
            openid = "";
        }
        if (clienttype == null) {
            clienttype = "16";
        }
        if (StringUtils.isEmpty(openid)) {
            openid = clientIp;
        }
        if (!StringUtils.isEmpty(sessionKey)) {
            Map<String, Object> params = new HashMap<>();
            params.put("_appid", "wxapp.car");
            params.put("wechatid", 55);
            params.put("clienttype", clienttype);
            params.put("mobilePhone", mobilePhone);
            params.put("validCode", clientIp);
            params.put("sessionKey", sessionKey);
            params.put("iv", iv);
            params.put("rawData", URLEncoder.encode(rawData, "utf-8"));
            params.put("signature", signature);
            params.put("encryptedData", encryptedData);
            params.put("sessionId", openid);
            params.put("ip", validCode);
            Map<String, Object> item = httpService.httpPostForReturnValue(urlWxUserRegisterBind, params, new TypeReference<ReturnValue<Map<String, Object>>>() {
            });
            String newUserName = UpdateNickName(item, rawData);
//            SmsCenter.send("", item.get("UserId"), "", 1, 0, "", StringUtils.isEmpty(newUserName) ? item.get("UserName") : newUserName);
            String headImage = item.get("HeadImage").toString();
            if (headImage != null && !"".equals(headImage)) {
                if (headImage.startsWith("//")) {
                    headImage = "https:" + headImage;
                } else if (headImage.startsWith("/")) {
                    headImage = "https://i3.autoimg.cn/userscenter" + headImage;
                }
            }
            String finalHeadImage = headImage;
            return new HashMap<String, Object>() {
                {
                    put("userid", item.get("UserId"));
                    put("nickname", StringUtils.isEmpty(newUserName) ? item.get("UserName") : newUserName);
                    put("headimage", finalHeadImage);
                    put("userstate", item.get("UserState"));
                    put("sex", item.get("Sex"));
                    put("session", item.get("Session"));
                }
            };
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("_appid", "wxapp.car");
            params.put("mobilePhone", mobilePhone);
            params.put("validCode", validCode);
            params.put("ip", clientIp);
            params.put("sessionId", openid);
            Map<String, Object> header = new HashMap<>();
            header.put("Authorization", "APPCODE ced1d36511ed49b799dd103b3b13b1e9");
            Map<String, Object> item = httpService.httpPostForReturnValue(urlRegOrLoginByMobileCode, params, header, new TypeReference<ReturnValue<Map<String, Object>>>() {
            });

            String headimage = item.get("Minpic").toString();
            if (!"//x.autoimg.cn/club/v1Content/images/Detail/user120_default.gif".equals(headimage)) {
                headimage = "https://i2.autoimg.cn/userscenter" + headimage;
            } else {
                headimage = "https:" + headimage;
            }
            String finalHeadimage = headimage;
            return new HashMap<String, Object>() {
                {
                    put("userid", item.get("UserId"));
                    put("nickname", item.get("NickName"));
                    put("headimage", finalHeadimage);
                    put("userstate", item.get("UserState"));
                    put("sex", item.get("Sex"));
                    put("session", item.get("PcpopClub"));
                }
            };
        }
    }

    private String UpdateNickName(Map item, String rawData) {
        return null;
    }
}
