package com.autohome.applet.service.wxapi;

import java.io.File;

/**
 * Created by pengy<PERSON> on 2016/12/15.
 */
public class UserHeaderUrlTool {
    /**
     * 为头像添加域名前缀
     *
     * @param headImageUrl 用户头像
     * @return
     */
    public static String getHeadImageUrl(String headImageUrl) {
        //头像为空返回默认头像
        if (headImageUrl == null || headImageUrl.isEmpty()) {
            //return "http://x.autoimg.cn/space/images/head_120X120.gif";
            return "https://x.autoimg.cn/club/wxapp/male_default.png";
        }
        //完整路径头像直接返回
        if (headImageUrl.startsWith("https://")) {
            return headImageUrl;
        }
        if (headImageUrl.startsWith("http://")) {
            return headImageUrl.replace("http://", "https://");
        }
        if (headImageUrl.startsWith("//")) {
            return "https:" + headImageUrl;
        }
        //论坛头像数据带有 "userheaders" ，需要把"userheaders"处理掉
        headImageUrl = headImageUrl.replace("userheaders", "");
        String domain = "";
        File file = new File(headImageUrl);
        String fileName = file.getName();
        int r = 0, b = 0;
        char[] chars = fileName.toCharArray();
        while ((r += 4) < chars.length) {
            b ^= chars[r];
        }
        b %= 2;
        if (headImageUrl.contains("autohomecar__")) {
            b += 2;
            domain = String.format("http://i%s.autoimg.cn/usercenter", b);
        }
        else {
            domain = String.format("http://i%s.autoimg.cn/album/userheaders", b);
        }
        if (!headImageUrl.startsWith("/")) {
            domain += "/";
        }
        return domain + headImageUrl;
    }
}
