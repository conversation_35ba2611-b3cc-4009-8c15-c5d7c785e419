package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl.buycar;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.CarCalculator;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecParam;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCar4Service;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCar6Service;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCarCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BuyCar6ServiceImpl implements BuyCar6Service {

    @Autowired
    private BuyCar4Service buyCar4Service;

    @Autowired
    private BuyCarCommonService buyCarCommonService;

    /**
     * 全款购车
     *
     * @param specid
     * @param price
     * @param _appid
     * @param cityid
     * @param provinceid
     * @return
     */
    @Override
    public ResponseContent<CarCalculator> calBuyCarCostBySpecid(Integer specid, Integer price, String _appid, int cityid, int provinceid) {
//        SpecPriceList splist = buyCar5Service.getminpricebyspec(_appid, String.valueOf(specid), String.valueOf(cityid), String.valueOf(provinceid));
        ResponseContent<SpecParam> specReponse = buyCarCommonService.spec_parambyspecid(_appid, String.valueOf(specid));
        SpecParam sp = null;
        if (specReponse != null && specReponse.getResult() != null) {
            sp = specReponse.getResult();
        }
        if (sp == null) {
            return new ResponseContent<>(102, "无法获取车门数");
        }

        if (price == 0) {
            price = sp.getSpecmaxprice();
        }
        if (price == 0) {
            return new ResponseContent<>(102, "无法获取车型价格");
        }
        return new ResponseContent<>(buyCar4Service.getCalculatorByCarInfo(String.valueOf(price), String.valueOf(sp.getSpecstructureseatnum()), String.valueOf(sp.getSpecistaxexemption())));
    }
}
