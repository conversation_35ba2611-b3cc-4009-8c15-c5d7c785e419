package com.autohome.applet.service.impl;

import com.autohome.applet.dao.javaapi.mapper.AppletOpenAutohomeRelationMapper;
import com.autohome.applet.dao.javaapi.model.AppletOpenAutohomeRelation;
import com.autohome.applet.service.AppletOpenAutohomeRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppletOpenAutohomeRelationServiceImpl implements AppletOpenAutohomeRelationService {
    @Autowired
    private AppletOpenAutohomeRelationMapper appletOpenAutohomeRelationMapper;
    @Override
    public Map<String, Integer> getUserIdByOpenIds(List<String> openIdList,Integer platformType) {
        // 取最后一个
        List<AppletOpenAutohomeRelation> list =appletOpenAutohomeRelationMapper.getUserIdByOpenIds(openIdList,platformType);

        Map<String, Integer> map = new HashMap<>();
        for (AppletOpenAutohomeRelation item : list) {
            if (!map.containsKey(item.getOpenid())) {
                map.put(item.getOpenid(), item.getUserId());
            }
        }
        return map;
    }
}
