package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.serachcars.SerachCarsInfo;
import com.autohome.applet.model.dto.serachcars.SeriesAdds;
import com.autohome.applet.model.dto.serachcars.SeriesItem;
import com.autohome.applet.service.javaapi.SerachCarsService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class SerachCarsServiceImpl implements SerachCarsService {

    @Autowired
    @Qualifier("apidataredis")
    RedisClient redisClientApiDataRedis;

    private final String serachCarsUrl = "http://uc-car-findcar.msapi.autohome.com.cn/searchcars";

    private final String addUrl = "http://dsj.dataservice.autohome.com.cn/api/app/getSeriesHotRankNew?topN=0";

    private final static String REDIS_SERIES_ATT_NUM_KEY = "applet:api:serachcars:hotrankseriesid:";

    @Override
    public void cacheSeriesAttNum() {

        HttpHelper.HttpResult httpResAddNums = HttpHelper.getInstance().httpGet(addUrl);
        if (httpResAddNums.getStatusCode() != 200 || httpResAddNums.getBody() == null) {
            JobLog.warn("getSeriesHotRankNew code != 200 or body = null");
            return;
        }

        SeriesAdds respAddNums = JacksonHelper.deserialize(httpResAddNums.getBody(), SeriesAdds.class);
        if (respAddNums == null || respAddNums.result == null
                || respAddNums.result.seriesData == null
                || respAddNums.result.seriesData.size() == 0) {
            return;
        }
        Map<Integer, Integer>  seriesMaps = respAddNums.result.seriesData.stream()
                .flatMap((Function<SeriesAdds.SeriesData, Stream<SeriesItem>>) seriesData -> seriesData.getSeries().stream())
                .collect(Collectors.toMap(k -> k.seriesId, v -> v.attNum, (ov, nv) -> ov));
        JobLog.info(JacksonHelper.serialize(seriesMaps));
        final int[] i = {0};
        seriesMaps.forEach((k, v) -> {
            redisClientApiDataRedis.setValue(REDIS_SERIES_ATT_NUM_KEY + k, String.valueOf(v), 30, TimeUnit.DAYS);
            JobLog.info(String.format("cacheSeriesAttNum index %s, key %s, value %d", i[0], REDIS_SERIES_ATT_NUM_KEY + k, v));
            i[0]++;
        });
    }

    public ReturnValue<?> getSerachCars(HttpServletRequest request) {
//        String _dateKey = "wxcar:serachcars:date:v8:today";
//        String key = "wxcar:serachcars:info:v8:adds";
//
//        String _today = DateHelper.getTodayString();
//        String _cacheDate = redisClientApiDataRedis.get(_dateKey);
//
//        boolean _isToday = false;
//        boolean _isNeedLoad = false;
//
//        List<SeriesItem> series;
//        Map<Integer, Integer> _seriesMaps = new HashMap<>();
//
//        if (_cacheDate != null && _today.equals(_cacheDate) ) {
//            // 对比时间
//            _isToday = true;
//
//            String cache = redisClientApiDataRedis.get(key);
//
//            if (cache != null) {
//                _seriesMaps = JacksonHelper.deserialize(cache, new TypeReference<Map<Integer, Integer>>() {
//                });
//            } else {
//                _isNeedLoad = true;
//            }
//        } else {
//            _isNeedLoad = true;
//
//        }
//
//        if (_isNeedLoad) {
//            HttpHelper.HttpResult httpResAddNums = HttpHelper.getInstance().httpGet(addUrl);
//            if (httpResAddNums.getStatusCode() != 200 || httpResAddNums.getBody() == null) {
//                return ReturnValue.buildErrorResult(1001, "源接口请求失败");
//            }
//
//            SeriesAdds respAddNums = JacksonHelper.deserialize(httpResAddNums.getBody(), SeriesAdds.class);
//            if (respAddNums.result.seriesData.size() == 0) {
//                return ReturnValue.buildErrorResult(1002, "源接口获取关注数为空");
//            }
//            List<SeriesItem> _series = respAddNums.result.seriesData.get(0).series;
//
//            for (SeriesItem item : _series) {
//                _seriesMaps.put(item.seriesId, item.attNum);
//            }
//            redisClientApiDataRedis.set(key, _seriesMaps, 2, TimeUnit.HOURS);
//            redisClientApiDataRedis.set(_dateKey, _today, 2, TimeUnit.HOURS);
//        }

        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(serachCarsUrl, params);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        SerachCarsInfo resp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<SerachCarsInfo>() {
        });
        if (resp == null || resp.getResult() == null || resp.getResult().seriesGroupList == null) {
            return ReturnValue.buildErrorResult(1001, "源接口解析失败");
        }
        List<SerachCarsInfo.SubSeries> list = resp.getResult().getSeriesGroupList();
        if (list.size() == 0) {
            return ReturnValue.buildSuccessResult(resp);
        }
        List<String> keys = list.stream()
                .map(item -> REDIS_SERIES_ATT_NUM_KEY + item.getSeriesId()).collect(Collectors.toList());
        Map<String, String> seriesAttNum = redisClientApiDataRedis.getValuesMap(keys);
        for (SerachCarsInfo.SubSeries subSeries : list) {
            int seriesId = subSeries.getSeriesId();
            String attNum = seriesAttNum.get(REDIS_SERIES_ATT_NUM_KEY + seriesId);
            if (attNum != null) {
                subSeries.setAttNum(Integer.parseInt(attNum));
            }
        }
//        for (int i = 0; i < resp.result.seriesGroupList.size(); i++) {
//            SerachCarsInfo.SubSeries subSeries = resp.result.seriesGroupList.get(i);
//            int seriesId = subSeries.seriesId;
//            if (_seriesMaps.get(seriesId) != null) resp.result.seriesGroupList.get(i).attNum = _seriesMaps.get(seriesId);
//        }

        return ReturnValue.buildSuccessResult(resp);
    }

    @Override
    public ReturnValue<List<SerachCarsInfo.SubSeries>> getSerachCars(Map<String, Object> params) {
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(serachCarsUrl, params);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("params:{}, error:源接口请求失败", JacksonHelper.serialize(params));
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        SerachCarsInfo resp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<SerachCarsInfo>() {
        });
        if (resp == null || resp.getResult() == null || resp.getResult().seriesGroupList == null) {
            return ReturnValue.buildErrorResult(1001, "源接口解析失败");
        }
        List<SerachCarsInfo.SubSeries> list = resp.getResult().getSeriesGroupList();
        return ReturnValue.buildSuccessResult(list);
    }
}
