package com.autohome.applet.service.wxapi.impl;

import com.autohome.applet.dao.javaapi.model.wxapp.ClubVideo;
import com.autohome.applet.dao.javaapi.model.wxapp.DetailModel;
import com.autohome.applet.dao.javaapi.model.wxapp.TopicLive;
import com.autohome.applet.dao.javaapi.model.wxapp.WxUser;
import com.autohome.applet.dao.javaapi.model.wxapp.topic.*;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.wxapi.*;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TopicServiceImpl
 *
 * <AUTHOR>
 * @date 2024/05/06
 */
@Service
public class TopicServiceImpl implements TopicService {

    private static final String GET_CLUB_LIKE_LIST = "http://clubapi.in.autohome.com.cn/web/zan/list";
    private static final String GET_TOPIC_BY_ID_PATH = "http://clubapi.in.autohome.com.cn/japi/topic/gettopicbyid";
    private static final String CLUB_LIVE_RELATION_URL = "http://clubapi.in.autohome.com.cn/api/topic/GetLiveIds";
    private static final String APPID = "wxapp";

    @Autowired
    private IUserService userService;
    @Autowired
    private NewsService newsService;
    @Autowired
    private HttpService httpService;


    /**
     * 获得最终页帖子信息
     *
     * @param topicid 帖子id
     * @param issend  是否是发帖时调用   发帖时调用一定是卡片贴
     * @return
     */
    public ReturnValue<DetailModel> topicNewDetail(Integer topicid, int issend, int isRetainA) {
        int isCard = 0;
        int liveroomid = 0;
        int videoId = 0;
        String url = "";
        if (issend == 1) {
            isCard = 1;
        }
        else {
            GetTopicByIdResult getTopicByIdResult = getTopicById(topicid);
            if (getTopicByIdResult != null) {
                url = getTopicByIdResult.getUrl();
                isCard = getTopicByIdResult.getIscard();
                //ispoll 70 是直播贴
                if (getTopicByIdResult.getIspoll() == 70) {
                    liveroomid = GetLiveTopicRoomId(topicid);
                }
            }
        }


        GetMainTopicResult maintopicResult = getMainTopic(topicid, isCard);
        if (maintopicResult == null) {
            return ReturnValue.buildErrorResult(101, "获得帖子失败,请重试");
        }
        ReturnValue<DetailModel> response = new ReturnValue<>();
        DetailModel detailModel = new DetailModel();
        GetMainTopicResult mainTopic = maintopicResult;
        int tDelete = mainTopic.getTdelete();

        // 详情页、个人中心：所有用户tDelete = 0 或 40 外展，作者本人tDelete = 20、21、22 外展，其余不外展
        if (tDelete > 0 && tDelete != 20 && tDelete != 21 && tDelete != 22 && tDelete != 40) {
            detailModel.setTitle("\"帖子被删除(或屏蔽)\"");
            List<ContentCard> contentCards = new ArrayList<>(1);
            contentCards.add(ContentCard.getHasDeletedMainContentCard());
            detailModel.setContentCards(contentCards);
            detailModel.setReplyCount(0);//如果主贴已经删除了，就不显示回复列表
        }
        else {
            detailModel.setBbs(mainTopic.getBbs());
            detailModel.setBbsid(mainTopic.getBbsid());
            detailModel.setMemberId(mainTopic.getT_memberid());
            detailModel.setReplyCount(mainTopic.getReplycounts());
            detailModel.setId(topicid);
            detailModel.setTitle(mainTopic.getTitle());
            detailModel.setPublishTime(mainTopic.getTdate());
            detailModel.setIspoll(mainTopic.getIspoll());
            detailModel.setPubprovincename(mainTopic.getPubprovincename());
            detailModel.setTsource(mainTopic.getTsource());
            detailModel.setTdelete(tDelete);

            WxUser user = userService.getUserInfo(mainTopic.getT_memberid());
            detailModel.setHeaderImg(UserHeaderUrlTool.getHeadImageUrl(user.getAvatar()));
            detailModel.setName(user.getNickName());
            detailModel.setViews(mainTopic.getViews());

            detailModel.setLiveroomId(liveroomid);
            detailModel.setUrl(url);

            detailModel.setTispoll(mainTopic.getTispoll());
            detailModel.setSubtitle(mainTopic.getSubtitle());

            String html = mainTopic.getTispoll() == 70 ? mainTopic.getOrgincontent() : mainTopic.getT_content().toString();
            //解决帖子108069676 因为下面css导致的乱码问题 font-family:\" helvetica\"
            if (topicid == 108069676) {
                html = html.replace("font-family:\" helvetica\"", "font-family:\"helvetica\"");
            }
            detailModel.setContentHtml(html);
            List<ContentCard> contentCards = new ArrayList<>();
            if (isCard == 1 || mainTopic.getTispoll() == 80) {
                contentCards = getCards(html, isRetainA);
                Matcher vpm = Pattern.compile("data-vid=\"([A-Z0-9]+)\"").matcher(html);
                while (vpm.find()) {
                    ContentCard videoCard = new ContentCard();
                    String vid = vpm.group(1);
                    videoCard.setCtype(5);
                    ClubVideo video = new ClubVideo();
                    video.set_img("");
                    video.set_type(5);
                    video.set_vid(vid);
                    videoCard.setOtherattributes(video);
                    contentCards.add(0, videoCard);
                }
            }
            else if (isRetainA == 0) {
                contentCards = RichTextParser.parseDetail(html);
            }
            else {
                contentCards = RichTextParser.parseDetailRetainA(html);
            }

            detailModel.setContentCards(contentCards);
        }

        Map<Integer, Integer> like = getClubLikeList(topicid, null);
        if (like != null && like.containsKey(topicid)) {
            detailModel.setLikeCount(like.get(topicid));
        }

        if (videoId > 0) {
            ReturnValue<DetailModel> videoDetail = newsService.getVideoDetails(videoId);
            if (videoDetail != null && videoDetail.getResult() != null) {
                detailModel.setVideo(videoDetail.getResult().getVideo());
            }
        }
        response.setResult(detailModel);
        return response;
    }


    public List<ContentCard> getCards(String html, int isRetainA) {
        List<ContentCard> cards = new ArrayList<>();
        int index = 0;
        String imgStart = "<div layer1=\"img-s\"></div>";
        String imgEnd = "<div layer1=\"img-e\"></div>";

        String textStart = "<div layer1=\"text-s\"></div>";
        String textEnd = "<div layer1=\"text-e\"></div>";

        String hStart = "<h2 id=";
        String hEnd = "</h2>";

        while (index < html.length()) {
            int imgIndex = html.indexOf(imgStart, index);
            int textIndex = html.indexOf(textStart, index);
            int hIndex = html.indexOf(hStart, index);

            if (imgIndex >= 0 && (textIndex < 0 || imgIndex < textIndex) && (hIndex < 0 || imgIndex < hIndex)) {
                index = imgIndex;
                int startIndex = index + imgStart.length();
                int endIndex = html.indexOf(imgEnd, startIndex);
                index = endIndex + imgEnd.length();
                ContentCard card = getImg(html.substring(startIndex, endIndex), isRetainA);
                if (card == null) {continue;}
                cards.add(card);
                continue;
            }
            if (textIndex >= 0 && (imgIndex < 0 || textIndex < imgIndex) && (hIndex < 0 || textIndex < hIndex)) {
                index = textIndex;
                int startIndex = index + textStart.length();
                int endIndex = html.indexOf(textEnd, startIndex);
                index = endIndex + textEnd.length();
                ContentCard card = getText(html.substring(startIndex, endIndex), isRetainA);
                if (card == null) {continue;}
                cards.add(card);
                continue;
            }
            if (hIndex >= 0 && (imgIndex < 0 || hIndex < imgIndex) && (textIndex < 0 || hIndex < textIndex)) {
                index = hIndex;
                int startIndex = index;
                int endIndex = html.indexOf(hEnd, startIndex);
                index = endIndex + hEnd.length();
                ContentCard card = getH(html.substring(startIndex, endIndex + hEnd.length()), isRetainA);
                if (card == null) {continue;}
                cards.add(card);
                continue;
            }
            break;
        }
        return cards;
    }

    public ContentCard getImg(String content, int isRetainA) {
        Pattern pattern = Pattern.compile("<img(.*?)src=\"(.*?)\"(.*?)>", Pattern.CASE_INSENSITIVE);
        Matcher imgMather = pattern.matcher(content);
        if (imgMather.find()) {
            ContentCard card = new ContentCard();
            card.setCtype(1);
            Pattern imgWidthPattern = Pattern.compile("width(.*?)(\\d+)", Pattern.CASE_INSENSITIVE);
            Pattern imgHeightPattern = Pattern.compile("height(.*?)(\\d+)", Pattern.CASE_INSENSITIVE);
            String url = imgMather.group(2);
            Matcher widthM = imgWidthPattern.matcher(imgMather.group());
            Matcher heightM = imgHeightPattern.matcher(imgMather.group());
            Matcher desM = Pattern.compile("<div class=\"description\">([\\S\\s]+)</div>", Pattern.CASE_INSENSITIVE).matcher(content);
            int width = widthM.find() ? Integer.parseInt(widthM.group(2)) : 0;
            int height = heightM.find() ? Integer.parseInt(heightM.group(2)) : 0;
            ImageOtherAttr attr = new ImageOtherAttr(width, height);
            card.setUrl(url);
            card.setOtherattributes(attr);
            if (desM.find()) {
                card.setDes(clearHtml(desM.group(1), isRetainA));
            }
            else {
                String dStart = "<div layer2=\"desc-s\"></div>";
                int dsi = content.indexOf(dStart);
                if (dsi > 0) {
                    String dEnd = "<div layer2=\"desc-e\"></div>";
                    card.setDes(clearHtml(content.substring(dsi + dStart.length(), content.indexOf(dEnd)), isRetainA));
                }
            }
            return card;
        }
        return null;
    }

    public String clearHtml(String html, int isRetainA) {
        if (html == null || html.equals("")) {return "";}
        html = html.replaceAll("&nbsp;", " ").replaceAll("&.{2,6}?;", " ");
        html = html.replaceAll("<br />|<br/>|<BR/>|<BR />|</p>|</P>", "\n");
        html = html.replaceAll("<(\\s*?)", "<");
        html = html.replaceAll("(\\s*?)/>", "/>");
        html = html.replaceAll("\n{2,}", "\n");
        if (isRetainA == 1) {
            html = html.replaceAll("<(?!img|IMG|Img|a|/a)[^>]*>|㊣|<(img|IMG).*\\.gif.*>|≮[^≯]*≯",
                    "");//.replaceAll("<(img|IMG).*\\.gif.*>","");//过滤非图片标签
        }
        else {
            html = html.replaceAll("<(?!img|IMG|Img)[^>]*>|㊣|<(img|IMG).*\\.gif.*>|≮[^≯]*≯", "");//.replaceAll("<(img|IMG).*\\.gif.*>","");//过滤非图片标签
        }
        return html;
    }

    public ContentCard getText(String content, int isRetainA) {
        Matcher m = Pattern.compile("<div class=\"tz-paragraph\">([\\S\\s]+)</div>", Pattern.CASE_INSENSITIVE).matcher(content);
        if (m.find()) {
            ContentCard card = new ContentCard();
            card.setCtype(2);
            card.setDes(clearHtml(m.group(1), isRetainA));
            return card;
        }
        return null;
    }


    public ContentCard getH(String content, int isRetainA) {
        Pattern text = Pattern.compile("<h2 id=\"([0-9]+)\" class=\"tz-paragraph-title\">([\\S\\s]+)</h2>", Pattern.CASE_INSENSITIVE);
        Matcher m = text.matcher(content);
        if (m.find()) {
            HOtherAttr attr = new HOtherAttr();
            ContentCard card = new ContentCard();
            card.setCtype(6);
            attr.setTitle(clearHtml(m.group(2), isRetainA));
            card.setOtherattributes(attr);
            return card;
        }
        return null;
    }

    /**
     * 获得主贴信息
     *
     * @param topicid 帖子id
     * @param iscard  是否需要卡片贴json,如果不需要,则返回富文本
     */
    private GetMainTopicResult getMainTopic(Integer topicid, int iscard) {
        Map<String, Object> queryString = new HashMap<>();
//        queryString.put("topicid", topicid.toString());
//        queryString.put("iscard", iscard + "");
//        queryString.put("_appid", APPID);
        //http://wiki.corpautohome.com/pages/viewpage.action?pageId=145097449
        String url = "http://clubapi.in.autohome.com.cn/web/topicdetail/" + topicid + "-0-1?isAnti=1";
        ReturnValue<TopicDetailDto> response = httpService.httpGetFor(url, queryString, new TypeReference<ReturnValue<TopicDetailDto>>() {});
        if (response == null || response.getResult() == null || response.getResult().getMaintopic() == null) {return null;}
        GetMainTopicResult result = response.getResult().getMaintopic();
        result.setReplycounts(response.getResult().getRowcount());
        return result;
    }

    private GetTopicByIdResult getTopicById(Integer topicid) {
        Map<String, Object> queryString = new HashMap<>(2);
        queryString.put("topicid", topicid.toString());
        queryString.put("fields", "isdelete,isclose,replycount,iscard,ispoll,url");
        queryString.put("_appid", APPID);
        ReturnValue<GetTopicByIdResult[]> response =
                httpService.httpGetFor(GET_TOPIC_BY_ID_PATH, queryString, new TypeReference<ReturnValue<GetTopicByIdResult[]>>() {});
        if (response == null || response.getResult() == null || response.getResult().length == 0) {return null;}
        return response.getResult()[0];
    }

    private int GetLiveTopicRoomId(int topicid) {
        Map<String, Object> queryString = new HashMap<>();
        queryString.put("topicIds", String.valueOf(topicid));
        queryString.put("_appid", APPID);
        List<TopicLive> returnValue =
                httpService.httpGetForReturnValue(CLUB_LIVE_RELATION_URL, queryString, new TypeReference<ReturnValue<List<TopicLive>>>() {});
        if (returnValue != null && !returnValue.isEmpty()) {
            return returnValue.get(0).getLiveId();
        }
        else {
            return 0;
        }
    }

    @Override
    public ReturnValue<DetailModel> topicDetail(Integer topicid, int nocache, int isRetainA) {
        return topicNewDetail(topicid, nocache, isRetainA);
    }

    public Map<Integer, Integer> getClubLikeList(int topicid, String replyids) {
        Map<Integer, Integer> clubLikes = null;
        HashMap<String, Object> queryStrings = new HashMap<>();
        queryStrings.put("input", topicid + "-" + (replyids == null || replyids.isEmpty() ? "0" : replyids));
        String s = httpService.httpGet(GET_CLUB_LIKE_LIST, queryStrings);
        if (s != null && !s.isEmpty()) {
            Pattern pattern = Pattern.compile("\"r\":(\\d+),\"z\":(\\d+)", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(s);
            clubLikes = new HashMap<>();
            while (matcher.find()) {
                clubLikes.put(new Integer(matcher.group(1)), new Integer(matcher.group(2)));
            }
        }
        return clubLikes;
    }
}
