package com.autohome.applet.service.caropenapi_uc_news_dealerservice;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.Api8Dto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface FctApiService {
    ResponseContent<?> getOpenModuleByFctIdNew(String _appid, int fctid, String userip, String deviceid, String gps, String netstate, int brandid, HttpServletRequest request);

    public List<Api8Dto.Picitems> getApi_8(String seriesId );
}
