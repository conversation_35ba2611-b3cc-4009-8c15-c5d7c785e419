package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.service.caropenapi_uc_news_dealerservice.LeadsApiService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class LeadsApiServiceImpl implements LeadsApiService {

    @Override
    public HashMap specAllPrice(int cityId, int specId) {
        String url = "https://leads.autohome.com.cn/third/specAllPrice";
        HashMap param = new HashMap();
        param.put("cityId", Integer.toString(cityId));
        param.put("specId", Integer.toString(specId));
        try {
//            String result = HttpHelper.getResponse(url, param, null).getContent();
//            HashMap resultmap = JSON.parseObject(result, HashMap.class);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://leads.autohome.com.cn/third/specAllPrice", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            HashMap resultmap = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<HashMap>() {
            });
            return resultmap;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public HashMap detailPrice(int carPrice, int displacementType, int seatType, int thirdInsuranceType,
                               int glassInsuranceType, int scratchInsuranceType, int allPay, int prepayment,
                               int prepaymentType, int loanTypeForYear) {

        Map<String, Object> param = new HashMap<>();
        param.put("carPrice", Integer.toString(carPrice));
        param.put("displacementType", Integer.toString(displacementType));
        param.put("seatType", Integer.toString(seatType));
        param.put("thirdInsuranceType", Integer.toString(thirdInsuranceType));
        param.put("glassInsuranceType", Integer.toString(glassInsuranceType));
        param.put("scratchInsuranceType", Integer.toString(scratchInsuranceType));
        param.put("allPay", Integer.toString(allPay));
        if (prepayment > 0)
            param.put("prepayment", Integer.toString(prepayment));
        if (prepaymentType > 0)
            param.put("prepaymentType", Integer.toString(prepaymentType));
        if (loanTypeForYear > 0)
            param.put("loanTypeForYear", Integer.toString(loanTypeForYear));
        try {
            //https://leads.autohome.com.cn/third/detailPrice
            Map<String, Object> headparam = new HashMap<>();
            headparam.put("Content-Type", "application/json");
//            String result = RecogHttpHelper.post(detailPriceUrl, JSON.toJSONString(param), 3000, "application/json", "utf-8", "utf-8");
//            HashMap resultmap = JSON.parseObject(result, HashMap.class);
            HttpHelper.HttpResult result = HttpHelper.getInstance().httpPostJson("http://leads.autohome.com.cn/third/detailPrice", JacksonHelper.serialize(param), headparam);
            HashMap map = JacksonHelper.deserialize(result.getBody(), HashMap.class);
            return map;
        } catch (Exception e) {
            return null;//ReturnValue.buildErrorResult(500, e.getMessage());
        }
    }

}
