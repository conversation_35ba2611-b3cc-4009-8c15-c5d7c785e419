package com.autohome.applet.service.seriesbasecardinfo;

import com.autohome.applet.model.dto.seriesbasecardinfo.SeriesTabInfoQuery;
import com.autohome.applet.util.AllCitiesUtil;

import java.util.List;

/**
 * 车系综述页相关接口
 * */
public interface SeriesBaseCardInfoServies {
    /**
     * 刷新车系综述页动态tab的列表
     *
     * 一，车型：
     * 获取车系页菜单：https://openapi.autohome.com.cn/autohome/uc-news-quickappservice/msapi/car/series_speclistbyseriesId?seriesid=18&cityid=110100&_appid=car&_v=v9.3.31&_timestamp=1733388223&_sign=48480306A5E167E84708E5AF2A3BDF0B
     * 获取停售车列表：https://openapi.autohome.com.cn/autohome/uc-news-quickappservice/msapi/car/series_specstoplistbyseriesId?seriesid=18&cityid=110100&year=2022&_appid=car&_v=v9.3.31&_timestamp=1733388610&_sign=4FF985A2AC00592FA0D7604D33A05146
     *
     * 二，资讯
     * 获取全部/车闻/评测/精选/视频，入参info_type不同
     * https://openapi.autohome.com.cn/autohome/maindata/api/data/more/series_news_list?_appid=app&channel=all&express_message=1&page_size=5&search_after=&series_id=18&info_type=recommend&_v=v9.3.31&_timestamp=1733389344&_sign=CDCE920B86E2A3E44BE5E653FD9871BB
     *
     * 三，视频
     * 推荐：https://openapi.autohome.com.cn/autohome/uc-news-quickappservice/msapi/videoplatform/getVidesBySeriesId?seriesid=18&pageindex=1&pagesize=5&isIncludeYK=false&_appid=car&_v=v9.3.31&_timestamp=1733389344&_sign=6BE1CCF89E9BAD7703CF6D0A2BF1E993
     *
     * 原创评测：https://a.athm.cn/wxcar.api.autohome.com.cn/api/article/getVidesBySeriesIdLimitBySpecialUserOrTag?series_id=18&pagesize=5&_appid=wxapp&_v=v9.3.31&_timestamp=1733389503&_sign=146366329a961ad23b06c41b246f5c7c
     *
     * 二级
     *
     * 一，车型：
     * 获取车系页菜单：https://openapi.autohome.com.cn/autohome/uc-news-quickappservice/msapi/car/series_speclistbyseriesId?seriesid=18&cityid=110100&_appid=car&_v=v9.3.31&_timestamp=1733388223&_sign=48480306A5E167E84708E5AF2A3BDF0B
     * 获取停售车列表：https://openapi.autohome.com.cn/autohome/uc-news-quickappservice/msapi/car/series_specstoplistbyseriesId?seriesid=18&cityid=110100&year=2022&_appid=car&_v=v9.3.31&_timestamp=1733388610&_sign=4FF985A2AC00592FA0D7604D33A05146
     *
     * 二，资讯
     * 获取全部/车闻/评测/精选/视频，入参info_type不同（全部：recommend，车闻：news，评测：review，精选：original，视频：video）其它入参包括：_appid，channel，express_message（是否展示快讯），page_size，search_after，series_id，info_type
     * https://openapi.autohome.com.cn/autohome/maindata/api/data/more/series_news_list?_appid=app&channel=all&express_message=1&page_size=5&search_after=&series_id=18&info_type=recommend&_v=v9.3.31&_timestamp=1733389344&_sign=CDCE920B86E2A3E44BE5E653FD9871BB
     *
     * 三，视频
     * 推荐：参数：seriesid，pageindex，pagesize，isIncludeYK（固定为false）
     * https://openapi.autohome.com.cn/autohome/uc-news-quickappservice/msapi/videoplatform/getVidesBySeriesId?seriesid=18&pageindex=1&pagesize=5&isIncludeYK=false&_appid=car&_v=v9.3.31&_timestamp=1733389344&_sign=6BE1CCF89E9BAD7703CF6D0A2BF1E993
     *
     * 原创评测：参数：series_id，pageId，pagesize
     * https://a.athm.cn/wxcar.api.autohome.com.cn/api/article/getVidesBySeriesIdLimitBySpecialUserOrTag?series_id=18&pagesize=5&_appid=wxapp&_v=v9.3.31&_timestamp=1733389503&_sign=146366329a961ad23b06c41b246f5c7c
     *
     * */
    void refreshSeriesTabInfoDtosCache(Integer seriesId);
    /**
     * 刷新车系综述页动态tab的列表
     *
     * 二手车：
     * 综合排序/价格低/价格高/最新发布，入参sort不同
     * https://a.athm.cn/lapp.api.autohome.com.cn/apiautoappsh/che168/autohome/xcx/search?pid=110000&cid=110100&seriesid=18&pageindex=1&pagesize=10&sort=4&deviceid=oLJUI0YfIGIbLgJf9mPbaG9BXZ8g&_appid=atc.iphone&sceneno=124&_v=v9.3.31&_timestamp=1733389619&_sign=301A35F6FC67329392D61B3651C6A648
     *
     * 同级车：
     * https://openapi.autohome.com.cn/autohome/recommend/dealer/api/lq/buym/seriesRecommend/getRecommendOrderSeries?seriesId=18&cityId=110100&_appid=car&_v=v9.3.31&_timestamp=1733389695&_sign=7E77EC67585D2A5B8C073EC01FEC2CE6
     *
     *
     * 二手车：
     * 综合排序/价格低/价格高/最新发布，入参sort不同，接口文档：https://zhishi.autohome.com.cn/home/<USER>/file?targetId=ziqtrRiqFU
     * https://a.athm.cn/lapp.api.autohome.com.cn/apiautoappsh/che168/autohome/xcx/search?pid=110000&cid=110100&seriesid=18&pageindex=1&pagesize=10&sort=4&deviceid=oLJUI0YfIGIbLgJf9mPbaG9BXZ8g&_appid=atc.iphone&sceneno=124&_v=v9.3.31&_timestamp=1733389619&_sign=301A35F6FC67329392D61B3651C6A648
     * lapp.api.autohome.com.cn/apiautoappsh/che168/autohome/xcx/search?pid=110000&cid=110100&seriesid=18&pageindex=1&pagesize=10&sort=4&deviceid=oLJUI0YfIGIbLgJf9mPbaG9BXZ8g&_appid=atc.iphone&sceneno=124&_v=v9.3.31&_timestamp=1733389619&_sign=301A35F6FC67329392D61B3651C6A648
     *
     * 同级车：
     * 参数：seriesId，cityId
     * https://openapi.autohome.com.cn/autohome/recommend/dealer/api/lq/buym/seriesRecommend/getRecommendOrderSeries?seriesId=18&cityId=110100&_appid=car&_v=v9.3.31&_timestamp=1733389695&_sign=7E77EC67585D2A5B8C073EC01FEC2CE6
     * */
    void refreshSeriesTabInfoCityDtosCache(Integer seriesId, AllCitiesUtil.CityItem cityItem);
}
