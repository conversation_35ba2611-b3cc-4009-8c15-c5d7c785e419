package com.autohome.applet.service.seriesbasecardinfo.impl;

import cn.hutool.core.lang.hash.Hash;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecParam;
import com.autohome.applet.model.dto.netcoreapi.series.Series;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfKoubeiDTO;
import com.autohome.applet.model.dto.seriesbasecardinfo.SeriesTabInfoDto;
import com.autohome.applet.model.dto.seriesbasecardinfo.SeriesTabInfoQuery;
import com.autohome.applet.service.seriesbasecardinfo.SeriesBaseCardInfoServies;
import com.autohome.applet.util.AllCitiesUtil;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import static com.alibaba.fastjson.JSONValidator.Type.Array;

@Service
@Slf4j
public class SeriesBaseCardInfoServiesImpl implements SeriesBaseCardInfoServies {

    @Autowired
    @Resource(name = "lightapp")
    private RedisClient redisClient;

    private static String REDIS_SERIESINFO_NEWS_FIRST_KEY = "series:baseinfo:tab:%s";//车系id

    private static String REDIS_SERIESINFO_NEWS_FIRST_CITY_KEY = "series:baseinfo:tab:city:%s:%s";//车id + 城市id

    //咨询
    private static String NEWS_URL = "https://openapi.autohome.com.cn/autohome/maindata/api/data/more/series_news_list?_appid=app&channel=all&express_message=1&page_size=5&search_after=&series_id={seriesId}&info_type={recommend}&_v=v9.3.31&_timestamp=1733389344&_sign=CDCE920B86E2A3E44BE5E653FD9871BB";
    //视频
    private static String VIDEO_URL = "http://uc-news-quickappservice.msapi.autohome.com.cn/videoplatform/getVidesBySeriesId?seriesid={seriesId}&pageindex=1&pagesize=5&isIncludeYK=false&_appid=car&_v=v9.3.31&_timestamp=1733389344&_sign=6BE1CCF89E9BAD7703CF6D0A2BF1E993";
    //二手车
    private static String ERSHOU_URL = "http://apiautoappsh.che168.com/xcx/search?pid={pid}&cid={cid}&seriesid={seriesId}&pageindex=1&pagesize=10&sort=4&deviceid=oLJUI0YfIGIbLgJf9mPbaG9BXZ8g&_appid=atc.iphone&sceneno=124&_v=v9.3.31&_timestamp=1733389619&_sign=301A35F6FC67329392D61B3651C6A648";
    //同级车
    private static String SAMECLASSSERIES_URL = "https://openapi.autohome.com.cn/autohome/recommend/dealer/api/lq/buym/seriesRecommend/getRecommendOrderSeries?seriesId={seriesId}&cityId={cid}&_appid=car&_v=v9.3.31&_timestamp=1733389695&_sign=7E77EC67585D2A5B8C073EC01FEC2CE6";

    //原创视频
    private static String ORIGINALREVIEW_URL = "http://wxcar.api.autohome.com.cn/api/article/getVidesBySeriesIdLimitBySpecialUserOrTag?series_id={seriesId}&pagesize=5&_appid=test&_v=v9.3.31&_timestamp=1733389503&_sign=146366329a961ad23b06c41b246f5c7c";

    @Override
    public void refreshSeriesTabInfoDtosCache(Integer seriesId) {
        //咨询
        CompletableFuture<ReturnValue<HashMap<String, Object>>> recommendCompletableFuture = completableFutureSeriesNewsList("recommend", seriesId);
        CompletableFuture<ReturnValue<HashMap<String, Object>>> newsCompletableFuture = completableFutureSeriesNewsList("news", seriesId);
        CompletableFuture<ReturnValue<HashMap<String, Object>>> reviewCompletableFuture = completableFutureSeriesNewsList("review", seriesId);
        CompletableFuture<ReturnValue<HashMap<String, Object>>> originalCompletableFuture = completableFutureSeriesNewsList("original", seriesId);
        CompletableFuture<ReturnValue<HashMap<String, Object>>> videoCompletableFuture = completableFutureSeriesNewsList("video", seriesId);

        //视频
//        CompletableFuture<ReturnValue<HashMap<String, Object>>> videoTabCompletableFuture = completableFutureVideoList(1, seriesId);
//        //原创视频
//        CompletableFuture<ReturnValue<HashMap<String, Object>>> originalReviewCompletableFuture = completableFutureVideoList(2, seriesId);

        CompletableFuture.allOf(recommendCompletableFuture, newsCompletableFuture, reviewCompletableFuture, originalCompletableFuture, videoCompletableFuture).join();

        try{
            ReturnValue<HashMap<String, Object>> recommendReturnValue = recommendCompletableFuture.get();
            ReturnValue<HashMap<String, Object>> newsReturnValue = newsCompletableFuture.get();
            ReturnValue<HashMap<String, Object>> reviewReturnValue = reviewCompletableFuture.get();
            ReturnValue<HashMap<String, Object>> originalReturnValue = originalCompletableFuture.get();
            ReturnValue<HashMap<String, Object>> videoReturnValue = videoCompletableFuture.get();

//            ReturnValue<HashMap<String, Object>> videoTabReturnValue = videoTabCompletableFuture.get();
//            ReturnValue<HashMap<String, Object>> originalReviewReturnValue = originalReviewCompletableFuture.get();

            List<SeriesTabInfoDto> seriesTabInfoDtoList = SeriesTabInfoDto.getSeriesTabInfoDtos();

            fillSeriesTabInfoDto(seriesTabInfoDtoList, recommendReturnValue, "article", "recommend", "total_count");
            fillSeriesTabInfoDto(seriesTabInfoDtoList, newsReturnValue, "article", "news", "total_count");
            fillSeriesTabInfoDto(seriesTabInfoDtoList, reviewReturnValue, "article", "review", "total_count");
            fillSeriesTabInfoDto(seriesTabInfoDtoList, originalReturnValue, "article", "original", "total_count");
            fillSeriesTabInfoDto(seriesTabInfoDtoList, videoReturnValue, "article", "video", "total_count");

//            fillSeriesTabInfoDto(seriesTabInfoDtoList, videoTabReturnValue, "video", "recommend", "total_count");
//            fillSeriesTabInfoDto(seriesTabInfoDtoList, originalReviewReturnValue, "video", "original", "totalcount");

            String key = String.format(REDIS_SERIESINFO_NEWS_FIRST_KEY, seriesId);
            redisClient.set(key, JacksonHelper.serialize(seriesTabInfoDtoList), 3, TimeUnit.DAYS);
            String content = "refreshSeriesTabInfoDtosCache key : "+ key +" , result : "+ JacksonHelper.serialize(seriesTabInfoDtoList);
            log.info(content);
            JobLog.info(content);
//            boolean newsTabShow = false;
//            SeriesTabInfoDto seriesTabInfoDto = seriesTabInfoDtoList.stream().filter(o -> "article".equals(o.getTabkey())).findFirst().get();
//            if(recommendReturnValue != null || recommendReturnValue.getResult() != null || recommendReturnValue.getResult().get("total_count") == "0"){
//                seriesTabInfoDto.getSecondSeriesTabList().stream().filter(o -> "recommend".equals(o.getTabkey())).findFirst().get().setShow(false);
//            }
//            else{
//                newsTabShow = true;
//            }
        }
        catch (Exception ex){
            log.error("refreshSeriesTabInfoDtosCache error seriesId : {} ", seriesId,  ex);
            JobLog.error("refreshSeriesTabInfoDtosCache error seriesId : " + seriesId, ex);
        }
    }

    @Override
    public void refreshSeriesTabInfoCityDtosCache(Integer seriesId, AllCitiesUtil.CityItem cityItem) {
        List<SeriesTabInfoDto> seriesTabInfoDtoList = SeriesTabInfoDto.getSeriesTabInfoDtosForCity();
        //二手车
//        CompletableFuture<ReturnValue<HashMap<String, Object>>> ershouCompletableFuture = completableFutureErShouList(seriesId, cityId, pid);
        //同级车
        CompletableFuture<ReturnValue<List<HashMap<String, Object>>>> tongjicheCompletableFuture = completableFutureTongjicheList(seriesId, cityItem.getCityId(), cityItem.getProvinceId());
        CompletableFuture.allOf(tongjicheCompletableFuture).join();

        try{
            ReturnValue<List<HashMap<String, Object>>> tongjicheReturnValue = tongjicheCompletableFuture.get();

            List<SeriesTabInfoDto> seriesTabInfoDtoListForCity = SeriesTabInfoDto.getSeriesTabInfoDtosForCity();
//        fillSeriesTabInfoDto(seriesTabInfoDtoListForCity, ershouReturnValue, "ershou", "totalcount");
            fillSeriesTabInfoDto(seriesTabInfoDtoListForCity, tongjicheReturnValue, "tongjiche");

            String key = String.format(REDIS_SERIESINFO_NEWS_FIRST_CITY_KEY, seriesId, cityItem.getCityId());
            redisClient.set(key, JacksonHelper.serialize(seriesTabInfoDtoListForCity), 7, TimeUnit.DAYS);
            log.info("refreshSeriesTabInfoCityDtosCache key :{} , result {}" , key, JacksonHelper.serialize(seriesTabInfoDtoListForCity));
        }
        catch (Exception ex){
            log.error("refreshSeriesTabInfoCityDtosCache error seriesId : {} , pid : {} , cid : {}", seriesId, cityItem.getProvinceId(), cityItem.getCityId(),  ex);
            JobLog.error("refreshSeriesTabInfoDtosCache error seriesId : " + seriesId, ex);
        }
    }

    /**
     * 资讯
     * */
    private CompletableFuture<ReturnValue<HashMap<String, Object>>> completableFutureSeriesNewsList(String typeInfo, int seriesId) {
        return CompletableFuture.supplyAsync(() -> {
            String newsUrlFirstRecommendUrl = NEWS_URL.replace("{seriesId}", String.valueOf(seriesId)).replace("{recommend}", typeInfo);
            HashMap<String, Object> param = new HashMap<>();

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(newsUrlFirstRecommendUrl, param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<HashMap<String, Object>>>() {});
        });
    }

    /**
     * 视频（videoType 1：视频， 2：原创视频）
     * */
    private CompletableFuture<ReturnValue<HashMap<String, Object>>> completableFutureVideoList(int videoType, int seriesId) {
        String videoUrl = "";
        if(videoType == 1){
            videoUrl = VIDEO_URL.replace("{seriesId}", String.valueOf(seriesId));
        }
        else if(videoType == 2){
            videoUrl = ORIGINALREVIEW_URL.replace("{seriesId}", String.valueOf(seriesId));
        }
        String finalVideoUrl = videoUrl;
        return CompletableFuture.supplyAsync(() -> {
            HashMap<String, Object> param = new HashMap<>();

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(finalVideoUrl, param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<HashMap<String, Object>>>() {});
        });
    }

    /**
     * 二手车
     * */
    private CompletableFuture<ReturnValue<HashMap<String, Object>>> completableFutureErShouList(int seriesId, String cityId, String pid) {
        String url = ERSHOU_URL.replace("{seriesId}", String.valueOf(seriesId)).replace("{pid}", pid).replace("{cid}", cityId);

        return CompletableFuture.supplyAsync(() -> {
            HashMap<String, Object> param = new HashMap<>();

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<HashMap<String, Object>>>() {});
        });
    }
    /**
     * 同级车
     * */
    private CompletableFuture<ReturnValue<List<HashMap<String, Object>>>> completableFutureTongjicheList(int seriesId, String cityId, String pid) {
        String url = SAMECLASSSERIES_URL.replace("{seriesId}", String.valueOf(seriesId)).replace("{cid}", cityId);

        return CompletableFuture.supplyAsync(() -> {
            HashMap<String, Object> param = new HashMap<>();

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<HashMap<String, Object>>>>() {});
        });
    }

    private void fillSeriesTabInfoDto(List<SeriesTabInfoDto> seriesTabInfoDtoList, ReturnValue<HashMap<String, Object>> tempReturnValue, String firstTab, String secondTab, String resultFilter){
        SeriesTabInfoDto seriesTabInfoDto = seriesTabInfoDtoList.stream().filter(o -> firstTab.equals(o.getTabkey())).findFirst().get();

        if(tempReturnValue == null || tempReturnValue.getResult() == null || tempReturnValue.getResult().get(resultFilter) == null || "0".equals(String.valueOf(tempReturnValue.getResult().get(resultFilter)))){
            //二级菜单tab隐藏显示
            seriesTabInfoDto.getSecondSeriesTabList().stream().filter(o -> secondTab.equals(o.getTabkey())).findFirst().get().setShow(false);
        }
        else{
            //任何一个二级菜单 show是true，说明一级菜单也是true
            seriesTabInfoDto.setShow(true);
        }
    }

    private void fillSeriesTabInfoDto(List<SeriesTabInfoDto> seriesTabInfoDtoList, ReturnValue<List<HashMap<String, Object>>> tempReturnValue, String firstTab){
        SeriesTabInfoDto seriesTabInfoDto = seriesTabInfoDtoList.stream().filter(o -> firstTab.equals(o.getTabkey())).findFirst().get();

        if(tempReturnValue == null || tempReturnValue.getResult() == null || CollectionUtils.isEmpty(tempReturnValue.getResult())){
            seriesTabInfoDto.setShow(false);
        }
        else{
            seriesTabInfoDto.setShow(true);
        }
    }
}
