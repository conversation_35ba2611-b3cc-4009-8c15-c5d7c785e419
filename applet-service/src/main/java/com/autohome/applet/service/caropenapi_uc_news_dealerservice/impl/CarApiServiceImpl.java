package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.mapper.autoopen.OpenModuleRepository;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.SortSeries;
import com.autohome.applet.model.dto.car.SpecBaseInfoBySpecListDTO;
import com.autohome.applet.model.dto.car.SpecBaseInfoTO;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.autoopen.SpecWhiteLogo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecPV;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecPvResult;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.*;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.FctSeries;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.Seriesitems;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.oppo.CarSpec;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.PanoApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCar1Service;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarApiServiceImpl implements CarApiService {
    @Autowired
    private OpenModuleRepository openModuleRepository;

    @Autowired
    private PanoApiService panoApiService;

    @Autowired
    private BuyCar1Service buyCar1Service;

    /**
     * 车系基本信息
     *
     * @param seriesids
     * @return
     */
    @Override
    public CarSeriesList getBaseInfoBySeriesList(String seriesids) {
        try {
            HashMap<String, Object> params = new HashMap<>();
            params.put("serieslist", String.valueOf(seriesids));
            params.put("_appid", "m");

//            ResponseContent<CarSeriesList> response = carApi.get("car.api.Series_BaseInfoBySeriesList", params, CarSeriesList.class);
//            if (response != null) {
//                return response.getResult();
//            }
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/CarPrice/Series_BaseInfoBySeriesList.ashx", params);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            } else {
                ResponseContent<CarSeriesList> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<CarSeriesList>>() {
                });
                if (responseContent == null || responseContent.getResult() == null) {
                    return null;
                }
                return responseContent.getResult();
            }
        } catch (Exception e) {
            log.error("getBaseInfoBySeriesList error", e);
        }
        return null;
    }

    /**
     * 车系基本信息
     *
     * @param seriesids
     * @return
     */
    @Override
    public ResponseContent<CarSeriesList> getBaseInfoBySeriesListResponse(String seriesids) {
        CarSeriesList list = getBaseInfoBySeriesList(seriesids);
        ResponseContent<CarSeriesList> responseContent = new ResponseContent<>();
        responseContent.setResult(list);
        return responseContent;

    }

    /**
     * 根据车系id获取各个类型前五张图片
     *
     * @param _appid
     * @param seriesid
     * @return
     */
    @Override
    public ResponseContent<?> series_classpicturebyseriesId(String _appid, int seriesid) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", _appid);
        param.put("seriesid", Integer.toString(seriesid));
        try {
//            String result = HttpHelper.getResponse(series_classpicturebyseriesIdURL, param, ENCODING, 3000).getContent();
//            String result = carApi.get("car.api.series_classpicturebyseriesId", param);
//            if (!StringUtil.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(200, "暂无数据");
//            }
//            ResponseContent<SeriesClassPicResult> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<SeriesClassPicResult>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carpic/series_classpicturebyseriesId.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(200, "暂无数据");
            } else {
                ResponseContent<SeriesClassPicResult> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SeriesClassPicResult>>() {
                });
                if (responseContent == null || responseContent.getResult() == null) {
                    return new ResponseContent<>(200, "暂无数据");
                }
                return responseContent;
            }

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("series_classpicturebyseriesId-withexception", e.getMessage())));
            log.error("series_classpicturebyseriesId error", e);
            return new ResponseContent<>(102, "暂无数据", new HashMap<>());
        }
    }
  /**
     * 根据车系id获取各个类型前五张图片，返回所有圖片信息
     *
     * @param _appid
     * @param seriesid
     * @return
     */
    @Override
    public ResponseContent<?> series_classpicturebyseriesIdRetAll(String _appid, int seriesid) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", _appid);
        param.put("seriesid", Integer.toString(seriesid));
        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carpic/series_classpicturebyseriesId.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(200, "暂无数据");
            } else {
                ResponseContent<SeriesClassPictureDto> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SeriesClassPictureDto>>() {
                });
                if (responseContent == null || responseContent.getResult() == null) {
                    return new ResponseContent<>(200, "暂无数据");
                }
                return responseContent;
            }

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("series_classpicturebyseriesId-withexception", e.getMessage())));
            log.error("series_classpicturebyseriesId error", e);
            return new ResponseContent<>(102, "暂无数据", new HashMap<>());
        }
    }

    /**
     * 根据厂商id获取车系
     *
     * @param _appid
     * @param state
     * @param fctid
     * @return
     */
    @Override
    public FctSeries getSeriesByFctId(String _appid, String state, int fctid, int size) {
        try {
            if (StringUtil.isBlank(state)) {
                state = "0X000E";
            }
            HashMap<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("state", state);
            param.put("store", "1");

            param.put("fctid", Integer.toString(fctid));
            param.put("size", Integer.toString(size));
            //筛选车系
//            String result = carApi.get("car.api.series_menubysearch", param);
//            if (StringUtil.isBlank(result)) {
//                return null;
//            }
//            ResponseContent<FctSeries> fctSeriesResponseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<FctSeries>>() {
//            });
//            if (fctSeriesResponseContent == null || fctSeriesResponseContent.getResult() == null || fctSeriesResponseContent.getResult().getSeriesitems() == null) {
//                return null;
//            }
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/series_menubysearch.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<FctSeries> fctSeriesResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<FctSeries>>() {
            });
            if (fctSeriesResponseContent == null || fctSeriesResponseContent.getResult() == null || fctSeriesResponseContent.getResult().getSeriesitems() == null) {
                return null;
            }
            List<SortSeries> sortSeries;
            try {
                sortSeries = openModuleRepository.getSortSeriesByFctId(fctSeriesResponseContent.getResult().getSeriesids());
            } catch (Exception e) {
                log.error("openModuleRepository.getSortSeriesByFctId err", e);
                sortSeries = null;
            }
            if (sortSeries == null || sortSeries.size() == 0) {
                fctSeriesResponseContent.getResult().getSeriesitems().forEach(s -> {
                    if (s.getSeriesstate() == 40) {
                        s.setStatus(0);
                    } else {
                        s.setStatus(1);
                    }
                });
                return fctSeriesResponseContent.getResult();
            }
            for (Seriesitems seriesitems : fctSeriesResponseContent.getResult().getSeriesitems()) {
                for (SortSeries series : sortSeries) {

                    if (series.getSeriesId() == seriesitems.getId()) {
                        seriesitems.setTopStatus(series.getTopStatus());
                        seriesitems.setRecommendTime(series.getRecommendTime());
                        seriesitems.setRecommendStatus(series.getRecommendStatus());
                        seriesitems.setSortNum(series.getSortNum());
                        seriesitems.setStatus(series.getStatus());
                        seriesitems.setHaveStore(1);
                    }
                }
                //数据库没有存的车，除了停售车系默认显示状态
                if (seriesitems.getHaveStore() == 0) {
                    if (seriesitems.getSeriesstate() == 40)
                        seriesitems.setStatus(0);
                    else
                        seriesitems.setStatus(1);
                }
            }
            return fctSeriesResponseContent.getResult();
        } catch (Exception e) {
            //LogHelper.logError("getSeriesByFctId-withException", e, e.getMessage());
            log.error("getSeriesByFctId error", e);
            return null;
        }
    }

    /**
     * 根据品牌id厂商id获取车系
     *
     * @param _appid
     * @param state
     * @param fctid
     * @return
     */
    @Override
    public FctSeries getSeriesByBrandIdAndFctId(String _appid, String state, int fctid, int size, int brandid) {
        try {
            if (StringUtil.isBlank(state)) {
                state = "0X000E";
            }

            HashMap<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("state", state);
            param.put("store", "1");
            if (brandid > 0) {
                param.put("brandid", String.valueOf(brandid));
            }
            param.put("fctid", Integer.toString(fctid));
            param.put("size", Integer.toString(size));
            //筛选车系

//            String result = "";
//            if (brandid > 0) {
//                result = HttpHelper.getResponse("http://car.api.autohome.com.cn/v1/carprice/series_menubysearch.ashx", param, null).getContent();//carApi.get("car.api.series_menubysearchforbrand", param);
//            } else {
//                result = carApi.get("car.api.series_menubysearch", param);
//            }
//            if (StringUtil.isBlank(result)) {
//                return null;
//            }
//            System.out.println("getSeriesByBrandIdAndFctId:" + result);
//            ResponseContent<FctSeries> fctSeriesResponseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<FctSeries>>() {
//            });
            HttpHelper.HttpResult httpResult;
            httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/series_menubysearch.ashx", param);

            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<FctSeries> fctSeriesResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<FctSeries>>() {
            });
            if (fctSeriesResponseContent == null || fctSeriesResponseContent.getResult() == null || fctSeriesResponseContent.getResult().getSeriesitems() == null) {
                return null;
            }

            List<SortSeries> sortSeries = new ArrayList<>();
            try {
                sortSeries = openModuleRepository.getSortSeriesByFctId(fctSeriesResponseContent.getResult().getSeriesids());
            } catch (Exception e) {
                sortSeries = null;
            }
            if (sortSeries == null || sortSeries.size() == 0) {
                fctSeriesResponseContent.getResult().getSeriesitems().forEach(s -> {
                    if (s.getSeriesstate() == 40) {
                        s.setStatus(0);
                    } else {
                        s.setStatus(1);
                    }
                });
                return fctSeriesResponseContent.getResult();
            }
            for (Seriesitems seriesitems : fctSeriesResponseContent.getResult().getSeriesitems()) {
                for (SortSeries series : sortSeries) {

                    if (series.getSeriesId() == seriesitems.getId()) {
                        seriesitems.setTopStatus(series.getTopStatus());
                        seriesitems.setRecommendTime(series.getRecommendTime());
                        seriesitems.setRecommendStatus(series.getRecommendStatus());
                        seriesitems.setSortNum(series.getSortNum());
                        seriesitems.setStatus(series.getStatus());
                        seriesitems.setHaveStore(1);
                    }
                }
                //数据库没有存的车，除了停售车系默认显示状态
                if (seriesitems.getHaveStore() == 0) {
                    if (seriesitems.getSeriesstate() == 40)
                        seriesitems.setStatus(0);
                    else
                        seriesitems.setStatus(1);
                }
            }


            return fctSeriesResponseContent.getResult();
        } catch (Exception e) {
//            System.out.println("getSeriesByFctIdwithException" + e.getMessage());
//            e.printStackTrace();
//            LogHelper.logError("getSeriesByFctId-withException", e, e.getMessage());
            log.error("getSeriesByBrandIdAndFctId error", e);
            return null;
        }
    }

    /**
     * 根据车系id,图片id 获取某图片附近列表 用于轮播效果
     *
     * @param _appid
     * @param seriesid
     * @param classid
     * @param imageid
     * @param size
     * @return
     */
    @Override
    public ResponseContent<?> getPictureListsByCondition(String _appid, int seriesid, int classid, int pageindex, int imageid, int size) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", _appid);
        param.put("seriesid", Integer.toString(seriesid));
        param.put("classid", Integer.toString(classid));
        param.put("page", Integer.toString(pageindex));
        param.put("imageid", Integer.toString(imageid));
        param.put("size", Integer.toString(size));
        try {
//            String result = HttpHelper.getResponse(GetPictureListsByConditionURL, param, ENCODING, 3000).getContent();
//            String result = carApi.get("car.api.Pic_PictureListsByCondition", param);
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(200, "暂无数据");
//            }
            //ResponseContent<SpecPictures> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<SpecPictures>>() {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/CarPic/Pic_PictureListsByCondition.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(200, "暂无数据");
            }
            ResponseContent<SpecPictures> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecPictures>>() {
            });

            if (responseContent == null || responseContent.getResult() == null) {
                return new ResponseContent<>(200, "暂无数据");
            }
            return responseContent;

        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getPictureListsByCondition", e.getMessage())));
            log.error("getPictureListsByCondition error", e);
            return new ResponseContent<>(102, "暂无数据", new HashMap<>());
        }
    }

//    /**
//     * 内网接口
//     *
//     * @return
//     */
//    public HashMap inner_series_menubysearch(String _appid, String state, int fctid, int brandId) {
//        try {
//            if (StringUtil.isBlank(state)) {
//                state = "0X000E";
//            }
//            HashMap<String, Object> param = new HashMap<>();
//            param.put("_appid", _appid);
//            param.put("state", state);
//            param.put("store", "1");
//
//            param.put("brandId", Integer.toString(brandId));
//            param.put("fctid", Integer.toString(fctid));
//            param.put("size", Integer.toString(50));
//            //筛选车系
////            String result = carApi.get("car.api.inner_series_menubysearch", param);
////            HashMap responseSpecContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<HashMap>() {
////            });
//            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://uc-news-dealerservice.yz-bk.prod.autohome.com.cn/car/inner_series_menubysearch", param);
//            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
//                return null;
//            }
//            HashMap responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<HashMap>() {
//            });
//
//            return responseSpecContent;
//        } catch (Exception e) {
////            logger.error(JSON.toJSONString(new LogFormat("inner_series_menubysearch-WithException", e.getMessage())));
//            log.error("getPictureListsByCondition error", e);
//            return null;
//        }
//
//    }

    /**
     * 根据多个条件筛选车系【厂商id获取所有车系】
     *
     * @param _appid
     * @param state
     * @param fctid
     * @return
     */
    @Override
    public ResponseContent<?> series_menubysearch(String _appid, String state, int fctid, int brandid) {
        try {
            FctSeries fctSeries = getSeriesByBrandIdAndFctId(_appid, state, fctid, 50, brandid);
            if (fctSeries == null || fctSeries.getSeriesitems() == null || fctSeries.getSeriesitems().size() == 0) {
                return null;
            }
            Iterator<Seriesitems> it = fctSeries.getSeriesitems().iterator();
            while (it.hasNext()) {
                Seriesitems f = it.next();
                if (f.getSeriesstate() == 0) {
                    it.remove();
                    continue;
                }
                if (f.getStatus() == 0 && f.getSeriesstate() != 40) {
                    it.remove();
                    continue;
                }
                f.setHaveVr(false);
                ResponseContent<CarVrInfo> carVrInfoResponseContent = panoApiService.getcarvrinfobyseriesid(_appid, "car", f.getId());
                if (carVrInfoResponseContent != null && carVrInfoResponseContent.getResult() != null) {
                    if (carVrInfoResponseContent.getResult().getHasexterior())
                        f.setHaveVr(true);
                }
            }
            fctSeries.getSeriesitems().sort(new Comparator<Seriesitems>() {
                @Override
                public int compare(Seriesitems o1, Seriesitems o2) {
                    if (o1.getTopStatus() > 0 && !(o2.getTopStatus() > 0)) {
                        return -1;
                    }
                    if (!(o1.getTopStatus() > 0) && o2.getTopStatus() > 0) {
                        return 1;
                    }
                    return Integer.compare(o1.getSortNum(), o2.getSortNum());
                }
            });
            List<GroupFctSeries> groupFctSeriesList = new ArrayList<>();
            Map<String, List<Seriesitems>> mapgroup = fctSeries.getSeriesitems().stream().collect(Collectors.groupingBy(Seriesitems::getSeriesStateGroupForFct));
            for (Map.Entry<String, List<Seriesitems>> entry : mapgroup.entrySet()) {
                GroupFctSeries groupFctSeries = new GroupFctSeries();
                groupFctSeries.setSeriesstate(entry.getKey());
                groupFctSeries.setSeriesitems(entry.getValue());
                groupFctSeriesList.add(groupFctSeries);
            }

            return new ResponseContent<>(groupFctSeriesList);
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("series_menubysearch-WithException", e.getMessage())));
            log.error("series_menubysearch error", e);
            return null;
        }

    }

    /**
     * 根据多个车型id获取多个参数信息
     *
     * @param _appid
     * @param speclist
     * @return
     */
    @Override
    public ResponseContent<?> getSpecParamAndConfigByspeclist(String _appid, String speclist) {
        Map<String, Object> result = new HashMap<>();
        //SpecConfigAndParam specConfigAndParam = new SpecConfigAndParam();
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", _appid);
        param.put("speclist", speclist);
        try {
            List<Specitem> specstate = getSpecInfoBySpeclist(speclist, 1);
            result.put("specinfo", specstate);
//            String resultparam = HttpHelper.getResponse(spec_paramlistbyspeclisturl, param, ENCODING, 3000).getContent();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_paramlistbyspeclist.ashx", param);

            if (!StringUtil.isNotNullAndWhiteSpace(httpResult.getBody())) {
                result.put("paramtypeitems", "");
            } else {
                //HashMap responseParam = JSON.parseObject(JSON.toJSONString(JSON.parseObject(httpResult.getBody(), ResponseContent.class).getResult()), HashMap.class);
                HashMap responseParam = JacksonHelper.deserialize(JacksonHelper.serialize(JacksonHelper.deserialize(httpResult.getBody(), ResponseContent.class).getResult()), HashMap.class);
                if (responseParam == null) {
                    result.put("paramtypeitems", "");
                } else {
                    result.put("paramtypeitems", responseParam.get("paramtypeitems"));
                    //String resultConfig = HttpHelper.getResponse(Config_GetListBySpecListURL, param, ENCODING, 3000).getContent();
                    HttpHelper.HttpResult httpResultConfitg = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/carprice/Config_GetListBySpecList.ashx", param);
                    if (!StringUtil.isNotNullAndWhiteSpace(httpResultConfitg.getBody())) {
                        result.put("configtypeitems", "");
                    } else {
                        //HashMap responseConfig = JSON.parseObject(JSON.toJSONString(JSON.parseObject(httpResultConfitg.getBody(), ResponseContent.class).getResult()), HashMap.class);
                        HashMap responseConfig = JacksonHelper.deserialize(JacksonHelper.serialize(JacksonHelper.deserialize(httpResultConfitg.getBody(), ResponseContent.class).getResult()), HashMap.class);
                        result.put("configtypeitems", responseConfig.get("configtypeitems"));
                    }
                }
            }

            return new ResponseContent<>(result);

        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getSpec_paramlistbyspeclist-withexception", e.getMessage())));
            log.error("getSpecParamAndConfigByspeclist error", e);
            result.put("paramtypeitems", "");
            result.put("configtypeitems", "");
            return new ResponseContent<>(102, "暂无数据" + e.getMessage(), result);
        }
    }

    /**
     * 根据多个车型id获取多个车型数据
     *
     * @param isconfig
     * @param speclist
     * @return
     */
    public List<Specitem> getSpecInfoBySpeclist(String speclist, int isconfig) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("speclist", speclist);
        params.put("isconfig", isconfig);
        params.put("_appid", "car");
        try {

//            String result = carApi.get("car.api.spec_infobyspeclist", params);
//            JSONObject responseContent = JSON.parseObject(httpResult.getBody());
//            if (responseContent.getJSONObject("result") != null && responseContent.getJSONObject("result").getInteger("total") > 0) {
//                return responseContent.getJSONObject("result").getObject("specitems", new com.alibaba.fastjson.TypeReference<List<Specitem>>() {
//                });
//            }
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_infobyspeclist.ashx", params);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ArrayList<>();
            }
            ResponseContent<SpecInfo> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecInfo>>() {
            });

            return responseSpecContent.getResult().getSpecitems();

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("getSpecInfoBySpeclist--withexception:" + speclist, e.getMessage())));
            log.error("getSpecInfoBySpeclist error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取厂商下关注度最高的车型
     *
     * @param _appid
     * @param state
     * @param fctid
     * @return
     */
    @Override
    public ResponseContent<?> getHotsSpecByFctId(String _appid, String state, int fctid) {
        try {
            FctSeries fctSeries = getSeriesByFctId(_appid, state, fctid, 50);
            ResponseContent<SpecPvResult> specPvResponseContent = buyCar1Service.attentionspec(null, fctSeries.getSeriesids());
            if (specPvResponseContent == null || specPvResponseContent.getResult() == null ||
                    specPvResponseContent.getResult().getResultlist() == null || specPvResponseContent.getResult().getResultlist().size() == 0) {
                return null;
            }
            List<SpecPV> specPVs = new ArrayList<>();
            specPvResponseContent.getResult().getResultlist().forEach(s -> {
                specPVs.addAll(s.getSpeclist());
            });
            SpecPV specPv = specPVs.stream().max(new Comparator<SpecPV>() {
                @Override
                public int compare(SpecPV o1, SpecPV o2) {
                    if (o1.getPv() > o2.getPv()) {
                        return 1;
                    } else {
                        return -1;
                    }
                }
            }).get();

            ResponseContent<CarSpec> carSpecResponseContent = spec_infobyspecid(Integer.toString(specPv.getSpecid()));
            if (carSpecResponseContent == null || carSpecResponseContent.getResult() == null) {
                return null;
            }
            SpecWhiteLogo specWhiteLogo = getSpecWhiteLogoBySpeclist(Integer.toString(specPv.getSpecid()));
            if (specWhiteLogo != null && specWhiteLogo.getSpecitems() != null && specWhiteLogo.getSpecitems().size() > 0) {
                String speclogo = StringUtil.isBlank(specWhiteLogo.getSpecitems().get(0).getPicpath()) ? "" : specWhiteLogo.getSpecitems().get(0).getPicpath();
                if (StringUtil.isNotNullAndWhiteSpace(speclogo))
                    carSpecResponseContent.getResult().setLogo(speclogo);
            }
            return carSpecResponseContent;
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("series_menubysearch-WithException", e.getMessage())));
            log.error("series_menubysearch-WithException", e);
            return null;
        }

    }

    /**
     * 根据车型id获取车型信息
     *
     * @param specid
     * @return
     */
    public ResponseContent<CarSpec> spec_infobyspecid(String specid) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("specid", specid);
            param.put("_appid", "car");
            //筛选车系
//            String result = carApi.get("car.api.spec_infobyspecid", param);
//            ResponseContent<CarSpec> responseSpecContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<CarSpec>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_infobyspecid.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<CarSpec> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<CarSpec>>() {
            });
            if (responseSpecContent == null || responseSpecContent.getResult() == null) {
                return null;
            }

            return responseSpecContent;
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("spec_infobyspecid-WithException", e.getMessage())));
            log.error("spec_infobyspecid-WithException", e);
            return null;
        }

    }

    /**
     * 根据多个车系id获取多个车系颜色
     *
     * @param speclist
     * @return
     */
    @Override
    public SpecWhiteLogo getSpecWhiteLogoBySpeclist(String speclist) {
        Map<String, Object> params = new HashMap<>();
        params.put("speclist", speclist);
        params.put("_appid", "car");
        try {

//            String result = carApi.get("car.api.Spec_PhotoBySpecId", params);
//            if (StringUtils.isBlank(result)) {
//                return null;
//            }
//            ResponseContent<SpecWhiteLogo> listResponseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<SpecWhiteLogo>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/CarPic/Spec_PhotoBySpecId.ashx", params);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<SpecWhiteLogo> listResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecWhiteLogo>>() {
            });
            if (listResponseContent == null || listResponseContent.getResult() == null) {
                return null;
            }
            return listResponseContent.getResult();
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("getSpecWhiteLogoBySpeclist--withexception:" + speclist, e.getMessage())));
            log.error("getSpecWhiteLogoBySpeclist--withexception", e);
        }
        return null;
    }

    @Override
    public List<SpecBaseInfoBySpecListDTO> SpecBaseInfbySpecList(String speclist) {
        Map<String, Object> params = new HashMap<>();
        params.put("speclist", speclist);
        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/CarPrice/Spec_BaseInfbySpecList.ashx", params);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<SpecBaseInfoTO> listResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecBaseInfoTO>>() {
            });
            if (listResponseContent == null || listResponseContent.getResult() == null) {
                return null;
            }
            return listResponseContent.getResult().getList();
        } catch (Exception e) {
            log.error("getSpecWhiteLogoBySpeclist--withexception", e);
        }
        return null;
    }

}
