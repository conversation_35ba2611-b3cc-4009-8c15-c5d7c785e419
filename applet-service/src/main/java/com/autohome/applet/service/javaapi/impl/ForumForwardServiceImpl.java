package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.basicservice.ForumForwardFeed;
import com.autohome.applet.service.javaapi.ForumForwardService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ForumForwardServiceImpl implements ForumForwardService {

    @Override
    public ReturnValue<?> getAppCustomQueryTypeList(HttpServletRequest request) {
        final String url = "http://la.corpautohome.com/club/app_custom_query_type_list";
        return getReturnValue(request, url);
    }

    @Override
    public ReturnValue<?> getAppFeedByCustomQuery(HttpServletRequest request) {
        final String url = "http://la.corpautohome.com/club/app_feed_by_custom_query";

        String _body = getRequestBody(request, url);

        if (_body == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<ForumForwardFeed> resp = JacksonHelper.deserialize(_body, new TypeReference<ReturnValue<ForumForwardFeed>>() {
        });

        if (resp == null || resp.getResult() == null) {
            return ReturnValue.buildErrorResult(1002, "源接口数据反序列化失败");
        }

        List<Integer> authorids = new ArrayList<>();

        for (int i = 0; i < resp.getResult().items.size(); i++) {
            Map<String, Object> _map = resp.getResult().items.get(i);

            Integer id = (Integer) _map.get("author_id");
            authorids.add(id);
        }


        String userids = authorids.stream().map(String::valueOf).collect(Collectors.joining(","));

        List<Map<String, Object>> _userRespBody = getUserInfoByUserIds(userids);

        if (_userRespBody == null) {
            return ReturnValue.buildErrorResult(1005, "源接口数据反序列化失败");
        }

        Map<Integer, Map<String, Object>> _authorBody = getAuthorCarInfo(userids);

        if (_authorBody == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        Map<Integer, Map<String, Object>> _authorMaps = new HashMap<>();

        // 此处做这一步主要是为了防止作者id为空的情况
        for (Map<String, Object> _mapItem:_userRespBody) {
            Integer _userid = (Integer) _mapItem.get("userid");
            _authorMaps.put(_userid, _mapItem);
        }

        final String hosturl = "https://i2.autoimg.cn/userscenter";

        // 此处可以做个author_id == 的判断
        for (int i = 0; i < resp.getResult().items.size(); i++) {
            Map<String, Object> _map = resp.getResult().items.get(i);
            Integer id = (Integer) _map.get("author_id");
            Map<String, Object> _info = _authorMaps.get(id);
            _info.put("basehost", hosturl);
//            resp.getResult().authors = userResp.getResult();
            _map.put("authorInfo", _info);
            _map.put("authorExpand", _authorBody.get(id));
        }

        return resp;
    }

    // 需要作者信息的pid array
    final List<String> pids = new ArrayList<String>() {{
        add("90400042");
    }};

    @Override
    public ReturnValue<?> getRcmRcmland(HttpServletRequest request) {
        final String url = "http://data.in.corpautohome.com/oneapi/v2";

        Map<String, String[]> parameterMap = request.getParameterMap();
        String[] values = parameterMap.get("pid");

        String pid = values != null ? values[0] : null;

        if (!pids.contains(pid)) return getReturnValue(request, url);

        String _body = getRequestBody(request, url);
        if (_body == null) {
            return ReturnValue.buildErrorResult(10001, "源接口请求失败");
        }

        Map<String, Object> _map = JacksonHelper.deserialize(_body, new TypeReference<Map<String, Object>>() {
        });
        if (_map == null) {
            return ReturnValue.buildErrorResult(10005, "源接口数据反序列化失败");
        }

        LinkedHashMap<String, Object> hashMap = (LinkedHashMap) _map.get("result");

        List<Map<String, Object>> _llist = (ArrayList<Map<String, Object>>) hashMap.get("itemlist");

        if (_llist == null) {
            return ReturnValue.buildErrorResult(10010, "源接口暂无数据");
        }

        List<String> _aids = new ArrayList<>();

        for (Map<String, Object> item : _llist) {
            LinkedHashMap<String, Object> _resHashMap = (LinkedHashMap) item.get("resourceobj");

            LinkedHashMap<String, Object> _showHashMap = (LinkedHashMap) _resHashMap.get("show");
            String _aid = (String) _showHashMap.get("author_id");
            if (_aid != null && !_aid.equals("")) {
                _aids.add(_aid);
            } else {
                log.warn("author_id:{}, loguuid:{}", _aid, _map.get("loguuid"));
            }

        }

        String _authorids = _aids.stream().map(String::valueOf).collect(Collectors.joining(","));

        List<Map<String, Object>> _userRespBody = getUserInfoByUserIds(_authorids);
        if (_userRespBody == null) {
            return ReturnValue.buildErrorResult(10005, "源接口数据反序列化失败");
        }

        log.warn("推荐数据量量:{}, authorid 数据量:{}, 查询用户信息量{}", _llist.size(), _aids.size(), _userRespBody.size());

        Map<Integer, Map<String, Object>> _authorMap = new HashMap<>();

        for (Map<String, Object> _tempmap : _userRespBody) {
            Integer _userId = (Integer) _tempmap.get("userid");
            _authorMap.put(_userId, _tempmap);
        }


        Map<Integer, Map<String, Object>> _authorBody = getAuthorCarInfo(_authorids);

        if (_authorBody == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        final String hosturl = "https://i2.autoimg.cn/userscenter";

        for (Map<String, Object> item : _llist) {
            LinkedHashMap<String, Object> _resHashMap = (LinkedHashMap) item.get("resourceobj");
            LinkedHashMap<String, Object> _showHashMap = (LinkedHashMap) _resHashMap.get("show");
            String _aid = (String) _showHashMap.get("author_id");
            if (_aid == null || _aid.equals("")) _showHashMap.put("authorInfo", null);
            else {
                Map<String, Object> _info = _authorMap.get(Integer.parseInt(_aid));
                _info.put("basehost", hosturl);
                _showHashMap.put("authorInfo", _info);
                _showHashMap.put("authorExpand", _authorBody.get(Integer.parseInt(_aid)));
            }

        }

        return ReturnValue.buildSuccessResult(_map);
    }

    @Override
    public ReturnValue<?> getTopicsList(String club_bbs_id, String club_order_type, int page_num, int page_size) {
        final String hosturl = "https://i2.autoimg.cn/userscenter";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "club");
        param.put("club_bbs_id", club_bbs_id);
        param.put("club_order_type", club_order_type);
        param.put("page_num", page_num);
        param.put("page_size", page_size);
        HttpHelper.HttpResult httpGet = null;
        if (!StringUtils.isEmpty(club_order_type) && !club_order_type.equals("3")){
            //发布 + 回复
            //http://maindata.api.autohome.com.cn/doc/detail?id=53
            httpGet = HttpHelper.getInstance().httpGet("http://maindata.api.autohome.com.cn/data/page/club_get_topics_list", param);
        }else {
            //热度排序
            //http://maindata.api.autohome.com.cn/doc/detail?id=819
            httpGet = HttpHelper.getInstance().httpGet("http://maindata.api.autohome.com.cn/data/page/club_get_topics_list_by_hot", param);
        }

        if (httpGet.isSuccess()) {
            ReturnValue<Map<String, Object>> result = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<Map<String, Object>>>() {
            });
            if (result != null && !CollectionUtils.isEmpty(result.getResult())) {
                Map<String, Object> resultResult = result.getResult();
                Object items = resultResult.get("items");
                List<Map> itemsList = (List<Map>)items;
                List<String> aids = new ArrayList<>();
                for (Map itemsMap : itemsList) {
                    itemsMap.put("authorInfo",null);
                    itemsMap.put("authorExpand",null);
                    Object authorId = itemsMap.get("author_id");
                    aids.add(authorId.toString());
                }
                String authorids = aids.stream().map(String::valueOf).collect(Collectors.joining(","));
                //List<Map<String, Object>> userRespBody = getUserInfoByUserIds(authorids);
                CompletableFuture<List<Map<String, Object>>> userRespBodyItem = CompletableFuture.supplyAsync(() -> {
                    try {
                        return getUserInfoByUserIds(authorids);
                    } catch (BusinessException e) {
                        log.error("getTopicsList getUserInfoByUserIds exception ", e);
                    }
                    return null;
                });
                //Map<Integer, Map<String, Object>> authorBody = getAuthorCarInfo(authorids);
                CompletableFuture<Map<Integer, Map<String, Object>>> authorBodyItem = CompletableFuture.supplyAsync(() -> {
                    try {
                        return getAuthorCarInfo(authorids);
                    } catch (BusinessException e) {
                        log.error("getTopicsList getAuthorCarInfo exception ", e);
                    }
                    return null;
                });
                CompletableFuture<?> thenApply = CompletableFuture.allOf(userRespBodyItem, authorBodyItem ).thenApply(unused -> {
                    try {
                        List<Map<String, Object>> userRespBody = userRespBodyItem.get();
                        if (!CollectionUtils.isEmpty(userRespBody)){
                            for (Map<String, Object> tempmap : userRespBody) {
                                String headimage = (String)tempmap.get("headimage");
                                tempmap.put("headimage", headimage.replace("//i2.autoimg.cn/userscenter",""));
                                tempmap.put("basehost", hosturl);
                                Integer userId = (Integer) tempmap.get("userid");
                                for (Map itemsMap : itemsList) {
                                    Object authorId = itemsMap.get("author_id");
                                    if (authorId.equals(userId)){
                                        itemsMap.put("authorInfo",tempmap);
                                    }
                                }
                            }

                        }
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("getTopicsList userRespBody exception ", e);
                    }

                    try {
                        Map<Integer, Map<String, Object>> authorBody = authorBodyItem.get();
                        if (!CollectionUtils.isEmpty(authorBody)){
                            for (Map itemsMap : itemsList) {
                                Object authorId = itemsMap.get("author_id");
                                Map<String, Object> authorExpandtMap = authorBody.get(authorId);
                                itemsMap.put("authorExpand",authorExpandtMap);
                            }

                        }
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("getTopicsList authorBody exception ", e);
                    }

                    return null;
                });
                try {
                    thenApply.get();
                } catch (Exception e) {
                    log.error("thenApply.get() exception ", e);
                }
                return result;

            }
        }
        return null;
    }

    @Override
    public ReturnValue<?> getTopicListByContentType(int pageindex, int pagesize, int tagInfoId, String orderby, int bbsid, String fields, int containTag, int containImg, int refine) {
        final String hosturl = "https://i2.autoimg.cn/userscenter";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "club");
        param.put("pageindex", pageindex);
        param.put("pagesize", pagesize);
        param.put("tagInfoId", tagInfoId);
        param.put("orderby", orderby);
        param.put("bbsid", bbsid);
        param.put("fields", fields);
        param.put("containTag", containTag);
        param.put("containImg", containImg);
        param.put("refine", refine);
        //http://wiki.corpautohome.com/pages/viewpage.action?pageId=92209426
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://clubapi.in.autohome.com.cn/api/topic/GetTopicListByContentType", param);;
        if (httpGet.isSuccess()) {
            ReturnValue<Map<String, Object>> result = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<Map<String, Object>>>() {
            });

            if (result != null && !CollectionUtils.isEmpty(result.getResult())) {
                Map<String, Object> resultResult = result.getResult();
                Object list = resultResult.get("list");
                List<Map> listsList = (List<Map>)list;
                if (CollectionUtils.isEmpty(listsList)){
                    return result;
                }
                List<String> aids = new ArrayList<>();
                for (Map listsMap : listsList) {
                    listsMap.put("authorInfo",null);
                    listsMap.put("authorExpand",null);
                    Object memberid = listsMap.get("post_memberid");
                    aids.add(memberid.toString());
                }
                String authorids = aids.stream().map(String::valueOf).collect(Collectors.joining(","));
                //List<Map<String, Object>> userRespBody = getUserInfoByUserIds(authorids);
                CompletableFuture<List<Map<String, Object>>> userRespBodyItem = CompletableFuture.supplyAsync(() -> {
                    try {
                        return getUserInfoByUserIds(authorids);
                    } catch (BusinessException e) {
                        log.error("getTopicListByContentType getUserInfoByUserIds exception ", e);
                    }
                    return null;
                });
                //Map<Integer, Map<String, Object>> authorBody = getAuthorCarInfo(authorids);
                CompletableFuture<Map<Integer, Map<String, Object>>> authorBodyItem = CompletableFuture.supplyAsync(() -> {
                    try {
                        return getAuthorCarInfo(authorids);
                    } catch (BusinessException e) {
                        log.error("getTopicListByContentType getAuthorCarInfo exception ", e);
                    }
                    return null;
                });
                CompletableFuture<?> thenApply = CompletableFuture.allOf(userRespBodyItem, authorBodyItem ).thenApply(unused -> {
                    try {
                        List<Map<String, Object>> userRespBody = userRespBodyItem.get();
                        if (!CollectionUtils.isEmpty(userRespBody)){
                            for (Map<String, Object> tempmap : userRespBody) {
                                String headimage = (String)tempmap.get("headimage");
                                tempmap.put("headimage", headimage.replace("//i2.autoimg.cn/userscenter",""));
                                tempmap.put("basehost", hosturl);
                                Integer userId = (Integer) tempmap.get("userid");
                                for (Map listsMap : listsList) {
                                    Object memberid = listsMap.get("post_memberid");
                                    if (memberid.equals(userId)){
                                        listsMap.put("authorInfo",tempmap);
                                    }
                                }
                            }
                        }
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("getTopicListByContentType userRespBody exception ", e);
                    }

                    try {
                        Map<Integer, Map<String, Object>> authorBody = authorBodyItem.get();
                        if (!CollectionUtils.isEmpty(authorBody)){
                            for (Map listsMap : listsList) {
                                Object memberid = listsMap.get("post_memberid");
                                Map<String, Object> authorExpandtMap = authorBody.get(memberid);
                                listsMap.put("authorExpand",authorExpandtMap);
                            }

                        }
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("getTopicListByContentType authorBody exception ", e);
                    }

                    return null;
                });
                try {
                    thenApply.get();
                } catch (Exception e) {
                    log.error("thenApply.get() exception ", e);
                }
                return result;

            }
        }
        return null;
    }

    public List<Map<String, Object>> getUserInfoByUserIds(String userids) {
//        final String url = "http://user.api.autohome.com.cn/api/userInfo/GetUserInfoByUserIds";
        final String url = "https://user.api.autohome.com.cn/api/go_userInfo/getuserinfolist";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "user");
        param.put("useridlist", userids);
        param.put("fields", "userid,newnickname,headimage,adddate,CityName");

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }

        ReturnValue<List<Map<String, Object>>> userResp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<Map<String, Object>>>>() {
        });

        return userResp.getResult();
    }

    private Map<Integer, Map<String, Object>> getAuthorCarInfo(String userids) {
        final String url = "http://rzc.api.in.autohome.com.cn/api/CarOwnerCamp/SelecCarLevelsListByUserids";

        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "rzc");
        param.put("userIds", userids);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }

        ReturnValue<List<Map<String, Object>>> userResp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<Map<String, Object>>>>() {
        });

        Map<Integer, Map<String, Object>> _authorInfo = new HashMap<>();
        Set<Integer> _brandList = new HashSet<>();
        Map<Integer, Integer> _authorBrands = new HashMap<>();

        for (Map<String, Object> item : userResp.getResult()) {
            Integer _userId = (Integer) item.get("userid");
            List<Map<String, Object>> _carList = (List<Map<String, Object>>) item.get("list");

            Collections.sort(_carList, new Comparator<Map<String, Object>>() {

                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    String _str1 = (String) o1.get("registertime");
                    String _str2 = (String) o2.get("registertime");

                    if (_str1 == null || _str1.equals("")) {
                        _str1 = "2000-01-01";
                    }

                    if (_str2 == null || _str2.equals("")) {
                        _str2 = "2000-01-01";
                    }

                    Date _d1 = DateHelper.parse(formatLocal(_str1));
                    Date _d2 = DateHelper.parse(formatLocal(_str2));
                    int flag = _d2.compareTo(_d1);
                    return flag;
                }
            });
            _authorInfo.put(_userId, _carList.get(0));
            if (_carList.get(0) != null) {
                Integer brandId = (Integer) _carList.get(0).get("brandId");
                _brandList.add( brandId);
                _authorBrands.put(_userId, brandId);
            }
        }

        String _brandIds = _brandList.stream().map(String::valueOf).collect(Collectors.joining(","));

        final String brandUrl = "http://car.api.autohome.com.cn/v1/carprice/brand_logobybrandlist.ashx";

        Map<String, Object> bparam = new HashMap<>();
        bparam.put("_appid", "car");
        bparam.put("brandlist", _brandIds);

        HttpHelper.HttpResult bhttpResult = HttpHelper.getInstance().httpGet(brandUrl, bparam);

        if (bhttpResult.getStatusCode() != 200 || bhttpResult.getBody() == null) {
            return null;
        }

        ReturnValue<Map<String, Object>> brandResp = JacksonHelper.deserialize(bhttpResult.getBody(), new TypeReference<ReturnValue<Map<String, Object>>>() {
        });

        if (brandResp == null || brandResp.getResult() == null) {
            return null;
        }

        List<Map<String, Object>> _branditems = ( List<Map<String, Object>>) brandResp.getResult().get("branditems");
        Map<Integer, String> _logoMap = new HashMap<>();
        for (Map<String, Object> brand: _branditems) {
            Integer _bid = (Integer) brand.get("id");
            String _logo = (String) brand.get("logo");
            _logoMap.put(_bid, _logo);

        }


        for (Map.Entry<Integer, Map<String, Object>> authorM: _authorInfo.entrySet()) {
//            Map<String, Object> author = _authorInfo.get(userId);
            Map<String, Object> author = authorM.getValue();
            Integer _userId = authorM.getKey();
            author.put("brandlogo", _logoMap.get(_authorBrands.get(_userId)));
        }


        return _authorInfo;
    }

    private ReturnValue<?> getReturnValue(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }

        Map<String, Object> _header = new HashMap<>();
        _header.put("Authorization", "Basic dWMtbmV3cy1kZWFsZXJzZXJ2aWNlOkw3bSoqckBtSlk=");

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, params, _header);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<?> resp = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        return resp;
    }

    private String getRequestBody(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }

        Map<String, Object> _header = new HashMap<>();
        _header.put("Authorization", "Basic dWMtbmV3cy1kZWFsZXJzZXJ2aWNlOkw3bSoqckBtSlk=");

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGetReportException(url, params, _header);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        return httpResult.getBody();
    }

    private Map<String, Object> deserialize(String _content) {
        Map<String, Object> _desMap = JacksonHelper.deserialize(_content, new TypeReference<Map<String, Object>>() {
        });

        if (_desMap == null) return null;
        return _desMap;
    }

    public static <T> List<List<T>> splistList(List<T> list, int subNum) {
        List<List<T>> tNewList = new ArrayList<List<T>>();
        int priIndex = 0;
        int lastPriIndex = 0;
        int insertTimes = list.size() / subNum;
        List<T> subList = new ArrayList<>();
        for (int i = 0; i <= insertTimes; i++) {
            priIndex = subNum * i;
            lastPriIndex = priIndex + subNum;
            if (i == insertTimes) {
                subList = list.subList(priIndex, list.size());
            } else {
                subList = list.subList(priIndex, lastPriIndex);
            }
            if (subList.size() > 0) {
                tNewList.add(subList);
            }
        }
        return tNewList;
    }


    private String formatLocal(String datetime) {
        if (datetime.equals("") || datetime == null) {
            return "2000-01-01";
        }
        else if (datetime.length() <= 8) {
            SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = null;

            try {
                Date date = format1.parse(datetime);
                formattedDate = format2.format(date);
            } catch (ParseException e) {

            }
            return formattedDate;
        }
        else if(datetime.indexOf("-") > -1 && datetime.length() > 10) {
            return datetime.substring(0, 10);
        }
        else if (datetime.length() == 10) {
            return datetime;
        }
        return "";
    }
}
