package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.serachcars.SerachCarsInfo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface SerachCarsService {
    ReturnValue<?> getSerachCars(HttpServletRequest request);

    ReturnValue<List<SerachCarsInfo.SubSeries>> getSerachCars(Map<String, Object> params);

    void cacheSeriesAttNum();
}
