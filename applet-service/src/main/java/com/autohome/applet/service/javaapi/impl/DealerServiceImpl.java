package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.RowCountPage;
import com.autohome.applet.model.dto.dealer.DealerBrandCityActivity;
import com.autohome.applet.model.dto.dealer.MinSeriesDealPriceFormat;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.javaapi.DealerService;
import com.autohome.applet.util.CityUtil;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("javaDealerService")
public class DealerServiceImpl implements DealerService {

    private static final String DEALER_BRAND_ACTIVITY_KEY = "dealer_brand_activity_key_city:%s:brand:%d";
    private static final String DEALER_CITY_ACTIVITY_BRANDS_KEY = "dealer_activity_brand_key_city:%s";

    @Qualifier("dealerservice")
    @Autowired
    RedisClient redisClient;

    @Autowired
    HttpService httpService;

    @Value("${zhiyutong.api.activityList}")
    String activityList;

    @Override
    public void getDealerBrandActivity() {
        for (Integer cityId : CityUtil.cityMap.keySet()) {
            List<String> cityBrands = new ArrayList<>();
            Map<Integer, List<DealerBrandCityActivity>> map = loadCityActivityMap(String.valueOf(cityId));
            map.forEach((k, v) -> {
                if (v.size() > 0) {
                    JobLog.info("city:" + cityId + " brand:" + k + " has " + v.size() + " activities");
                }
                cityBrands.add(String.valueOf(k));
                redisClient.setValue(String.format(DEALER_BRAND_ACTIVITY_KEY, cityId, k), JacksonHelper.serialize(v)
                        , 30, TimeUnit.DAYS);
            });
            String join = String.join(",", cityBrands);
            redisClient.setValue(String.format(DEALER_CITY_ACTIVITY_BRANDS_KEY, cityId), join, 30, TimeUnit.DAYS);
        }
    }

    private Map<Integer, List<DealerBrandCityActivity>> loadCityActivityMap(String cityId) {
        List<DealerBrandCityActivity> activities = loadCityActivity(cityId);
        Map<Integer, List<DealerBrandCityActivity>> map = new HashMap<>();
        for (DealerBrandCityActivity activity : activities) {
            if (activity.getBrandIdList() == null) {
                continue;
            }
            for (Integer brandId : activity.getBrandIdList()) {
                List<DealerBrandCityActivity> ifAbsent = map.computeIfAbsent(brandId, b -> new ArrayList<>());
                ifAbsent.add(activity);
            }
        }
        return map;
    }

    private List<DealerBrandCityActivity> loadCityActivity(String cityId) {
        List<DealerBrandCityActivity> activities = new ArrayList<>();
        List<DealerBrandCityActivity> list;
        int pageIndex = 1;
        do {
            list = loadPageCityActivity(cityId, pageIndex++);
            if (!CollectionUtils.isEmpty(list)){
                activities.addAll(list);
            }
        } while (!CollectionUtils.isEmpty(list));
        return activities;
    }

    private List<DealerBrandCityActivity> loadPageCityActivity(String cityId, int pageIndex) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appId", "lightapp");
        param.put("cityId", cityId);
        param.put("pageIndex", pageIndex);
        param.put("pageSize", "50");
        try {
            RowCountPage<DealerBrandCityActivity> activities = httpService.httpGetForReturnValue(activityList, param
                    , new TypeReference<ReturnValue<RowCountPage<DealerBrandCityActivity>>>() {
                    });
            return activities.getList();
        } catch (BusinessException e) {
            log.warn("loadPageCityActivity", e);
        }
        return null;
    }

    @Override
    public MinSeriesDealPriceFormat getMinSeriesDealPriceFormat(int seriesId, String cityId) {
        try{

            String url = "http://dealer.api.lq.autohome.com.cn/statistics/seriesprice/getMinSeriesDealPriceFormat";
            Map<String, Object> param = new HashMap<>();
            param.put("_appId", "dealer");
            param.put("cityId", cityId);
            param.put("seriesId", seriesId);
    //        ReturnValue<MinSeriesDealPriceFormat> activities =
            MinSeriesDealPriceFormat minSeriesDealPriceFormat = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<MinSeriesDealPriceFormat>>() {
            });
            return minSeriesDealPriceFormat;
        }catch (Exception e){
            return null;
        }
    }

}
