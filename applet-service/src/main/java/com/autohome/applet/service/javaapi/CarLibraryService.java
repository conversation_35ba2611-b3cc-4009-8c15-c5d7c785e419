package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.carlibrary.*;

import java.util.List;

/**
 * 汽车产品库相关接口
 * 汽车品牌，车系，车型等信息
 */
public interface CarLibraryService {


    /**
     * 获取产品库所有汽车品牌列表
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=6358635
     */
    BrandInfoDto getBrandList();

    /**
     * 根据品牌获取所有车系列表
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=6358641
     *
     * @param brandId
     * @return
     */
    SeriesInfoDto getSeriesByBrandId(int brandId);

    /**
     * 根据车系ID查询车系详情
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=26511062
     *
     * @param seriesId
     * @return
     */
    SeriesDetailDto getSeriesBySeriesId(int seriesId);


    /**
     * 根据车系id获取车型基本信息
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=113486349
     *
     * @param seriesId
     * @return
     */
    SpecDto getSpecsBySeriesId(int seriesId);

    ElectricSpecDto getElectricSpecsBySeriesId(int seriesId);

    /**
     * 根据车系ID获取汽车外观/内饰图片
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=5177555
     *
     * @param seriesId
     * @param classid  10 内饰
     * @return
     */
    CarImgDto getCarImgsBySeriesId(int seriesId, int classid);


    /**
     * 根据车系ID查询汽车外观颜色
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=6357839
     *
     * @param seriesId
     * @param state
     * @return
     */
    CarColorDto getCarColorDtoBySeriesId(int seriesId, String state);

    /**
     * 获取新能源车信息
     *
     * @param seriesId
     */
    List<NewenergyCarDto> getNewenergyCarInfo(String seriesId);

}
