package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.carlibrary.SpecDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.autoopen.CarSpec;
import com.autohome.applet.model.dto.dealer.SpecDealPrice;
import com.autohome.applet.model.dto.dealer.SpecMinPrice;
import com.autohome.applet.model.dto.netcoreapi.car.CarPic;
import com.autohome.applet.model.dto.netcoreapi.car.CarPicItem;
import com.autohome.applet.model.dto.netcoreapi.car.Spec_Electric;
import com.autohome.applet.model.dto.netcoreapi.dealer.Dealer;
import com.autohome.applet.model.dto.netcoreapi.dealer.DealerV2;
import com.autohome.applet.model.dto.netcoreapi.jiage.CarPrice;
import com.autohome.applet.model.dto.netcoreapi.output.GetAttentionBySeries;
import com.autohome.applet.model.dto.netcoreapi.output.GetSpecInfoquick;
import com.autohome.applet.model.dto.netcoreapi.output.SeriesSpecInfoOut;
import com.autohome.applet.model.dto.netcoreapi.series.*;
import com.autohome.applet.model.dto.netcoreapi.spec.SpecBaseInfo;
import com.autohome.applet.service.dealer.DealerNewService;
import com.autohome.applet.service.javaapi.CarLibraryService;
import com.autohome.applet.service.netcoreapi.SeriesService;
import com.autohome.applet.service.netcoreapi.SpecService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.netcoreapi.PicturePrefix;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SeriesServiceImpl implements SeriesService {

    @Autowired
    @Qualifier("lightapp")
    RedisClient redisClientLightapp;

    @Autowired
    private DealerNewService dealerNewService;

    @Autowired
    private CarLibraryService carLibraryService;
    @Autowired
    private SpecService specService;


    @Override
    public ReturnValue<SeriesInfo> GetSeriesInfo(int seriesId) throws ExecutionException, InterruptedException {
        return GetSeriesInfo(seriesId, 110100);
    }

    @Override
    public ReturnValue<SeriesInfo> GetSeriesInfo(int seriesId, int cityId) throws ExecutionException, InterruptedException {
        CompletableFuture<ReturnValue<Series>> series_ParamBySeriesId = series_ParamBySeriesId(seriesId);
        CompletableFuture<ReturnValue<CarPrice>> getCarPriceInfoSpecList = getCarPriceInfoSpecList(seriesId);
        CompletableFuture<ReturnValue<SeriesPhoto>> series_photowhitelogobyseriesid = series_photowhitelogobyseriesid(seriesId);
        CompletableFuture<ReturnValue<List<HashMap>>> getSeriesEvalScore = getSeriesEvalScore(seriesId);
        CompletableFuture<ReturnValue<DimSeriesPRCT>> loadSeriesPRCType = loadSeriesPRCType(seriesId);
        CompletableFuture<ReturnValue<Subsidy>> series_BuTieJiaBySeriesId = series_BuTieJiaBySeriesId(seriesId, cityId);
        CompletableFuture<ReturnValue<VR>> getvrType1 = getvrlistbyseriesid(seriesId, 1);
        CompletableFuture<ReturnValue<VR>> getvrType2 = getvrlistbyseriesid(seriesId, 2);
        CompletableFuture<ReturnValue<Brand>> brand_infobyseriesid = brand_infobyseriesid(seriesId);
        CompletableFuture<ReturnValue<Spec>> spec_detailbyseriesIdone = spec_detailbyseriesIdone(seriesId);
        CompletableFuture<ReturnValue<MinPrice>> getMinPriceBySeries = getMinPriceBySeries(seriesId, cityId);

        CompletableFuture.allOf(series_ParamBySeriesId, getCarPriceInfoSpecList, series_photowhitelogobyseriesid, getSeriesEvalScore,
                loadSeriesPRCType, series_BuTieJiaBySeriesId, getvrType1, getvrType2, brand_infobyseriesid, spec_detailbyseriesIdone, getMinPriceBySeries).join();

        ReturnValue<Series> tmpModel = series_ParamBySeriesId.get();
        if (tmpModel == null || tmpModel.getReturncode() != 0 || tmpModel.getResult() == null) {
            return ReturnValue.buildSuccessResult(null);
        }

        ReturnValue<CarPrice> modelCount = getCarPriceInfoSpecList.get();
        int chezhujiagecount = 0;
        if (modelCount != null && modelCount.getResult() != null) {
            chezhujiagecount = modelCount.getResult().getRowcount();
        }
        ReturnValue<MinPrice> body1TaskPrice = getMinPriceBySeries.get();
        List<DealerSpecPrice> dealerPrices = new ArrayList<>();
        if (body1TaskPrice != null && body1TaskPrice.getReturncode() == 0 && body1TaskPrice.getResult().getList() != null) {
            for (MinPriceItem item : body1TaskPrice.getResult().getList()) {
                DealerSpecPrice dealerSpecPrice = new DealerSpecPrice();
                dealerSpecPrice.setSpecId(item.getSpecId());
                dealerSpecPrice.setMinPrice(item.getMinPrice());
                dealerSpecPrice.setDealPriceQ(item.getDealPrice());
                dealerSpecPrice.setOriginalPrice(item.getOriginalPrice());
                dealerSpecPrice.setPrice(item.getPrice());
                dealerSpecPrice.setUrl(item.getUrl());

                dealerPrices.add(dealerSpecPrice);
            }
        }

        int fprice = 0;
        int lprice = 0;

        ReturnValue<Spec> bodyTaskList = spec_detailbyseriesIdone.get();
        List<SpecItem> specListSell;
        if (bodyTaskList != null && bodyTaskList.getResult() != null && bodyTaskList.getResult().getSpecitems() != null) {
            specListSell = bodyTaskList.getResult().getSpecitems();
        } else {
            specListSell = new ArrayList<>();
        }
        String delids = specListSell.stream().filter(f -> f.getState() == 20 || f.getState() <= 30).map(m -> String.valueOf(m.getId())).collect(Collectors.joining(","));
        if (!delids.equals(StringUtils.EMPTY)) {
            ReturnValue<MinPriceItemSimple> specdealprice = GetMinPriceBySpecSimple(delids, cityId);
            List<DealerSpecPrice> specdealpricelist = new ArrayList<>();
            if (specdealprice != null && specdealprice.getResult() != null && specdealprice.getResult().getList() != null) {
                for (MinPriceItemSimpleItem itemSimple : specdealprice.getResult().getList()) {
                    DealerSpecPrice dealerSpecPrice = new DealerSpecPrice();
                    dealerSpecPrice.setSpecId(itemSimple.getSpecId());
                    dealerSpecPrice.setMinOriginalPrice(itemSimple.getMinPrice());
                    specdealpricelist.add(dealerSpecPrice);
                }
            }
            if (specdealpricelist.size() > 0) {
                specdealpricelist = specdealpricelist.stream().sorted(Comparator.comparing(DealerSpecPrice::getMinOriginalPrice)).collect(Collectors.toList());
                fprice = specdealpricelist.get(0).getMinOriginalPrice();
                lprice = specdealpricelist.get(specdealpricelist.size() - 1).getMinOriginalPrice();
            }
        }
        String MaxCPrice = StringUtils.EMPTY;
        if (dealerPrices.size() > 0) {
            List<Double> maxcpricelist = new ArrayList<>();
            for (SpecItem specItem : specListSell) {
                Long dealerprice = dealerPrices.stream().filter(f -> f.getSpecId() == specItem.getId()).findAny().map(DealerSpecPrice::getMinPrice).orElse(null);
                if (specItem.getState() == 20 && dealerprice != null && dealerprice != 0) {
                    maxcpricelist.add(specItem.getMinprice() / 10000.0 - dealerprice / 10000.0);
                }
            }
            if (maxcpricelist.size() > 0 && Collections.max(maxcpricelist) > 0) {
                MaxCPrice = CarPriceUtils.getFormateStrPrice(Collections.max(maxcpricelist).toString()) + "万";
            }

        }
        String whitelog = StringUtils.EMPTY;
        String EvalScore = StringUtils.EMPTY;
        String transparentphone = StringUtils.EMPTY;
        ReturnValue<SeriesPhoto> tmpModelWhitePic = series_photowhitelogobyseriesid.get();
        if (tmpModelWhitePic != null && tmpModelWhitePic.getResult() != null && tmpModelWhitePic.getResult().getSerieslist() != null && tmpModelWhitePic.getResult().getSerieslist().size() > 0) {
            whitelog = tmpModelWhitePic.getResult().getSerieslist().get(0).getPicpath();
            transparentphone = tmpModelWhitePic.getResult().getSerieslist().get(0).getSeriespnglogo();
        }

        ReturnValue<List<HashMap>> tmpModelEvalScore = getSeriesEvalScore.get();
        if (tmpModelEvalScore != null && tmpModelEvalScore.getResult() != null && tmpModelEvalScore.getResult().size() > 0) {
            EvalScore = tmpModelEvalScore.getResult().get(0).get("kbscore").toString();
        }

        ReturnValue<DimSeriesPRCT> tmpModelLoadSeriesPRCType = loadSeriesPRCType.get();
        List<String> LoadSeriesPRCTypeStr = new ArrayList<>();
        if (tmpModelLoadSeriesPRCType != null && tmpModelLoadSeriesPRCType.getResult() != null && (tmpModelLoadSeriesPRCType.getResult().getDimSeriesPRCTypes().size() > 0)) {
            for (DimSeriesPRCTypeSummary summary : tmpModelLoadSeriesPRCType.getResult().getDimSeriesPRCTypes().get(0).getSummary()) {
                LoadSeriesPRCTypeStr.add(summary.getCombination());
            }
        }
        String butiePrice = "暂无报价";
        ReturnValue<Subsidy> tmpModelBuTieJia = series_BuTieJiaBySeriesId.get();

        if (tmpModelBuTieJia != null && tmpModelBuTieJia.getReturncode() == 0 && tmpModelBuTieJia.getResult() != null) {
            butiePrice = CarPriceUtils.GetSpecPrice(tmpModelBuTieJia.getResult().getSeriesitems().get(0).getMinprice(), tmpModelBuTieJia.getResult().getSeriesitems().get(0).getMaxprice());
        }

        String brandlog = StringUtils.EMPTY;
        ReturnValue<Brand> tmpModelBrand = brand_infobyseriesid.get();
        if (tmpModelBrand != null && tmpModelBrand.getResult() != null && tmpModelBrand.getResult().getItem() != null) {
            brandlog = tmpModelBrand.getResult().getItem().getBrandlogo();
            if (StringUtil.isNotNullAndWhiteSpace(brandlog)) {
                brandlog = brandlog.replace("/autohomecar", "/100x100_f40_autohomecar");
            }
        }

        String kuaichong = StringUtils.EMPTY;
        String manchong = StringUtils.EMPTY;
        String rongliang = StringUtils.EMPTY;
        String xuhang = StringUtils.EMPTY;
        int Cartype = 0;
        Series item = tmpModel.getResult();
        String fuelconsumption = StringUtils.EMPTY;
        if (item.getNewenergy() == 1) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("_appid", "m");
            param.put("seriesid", seriesId);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/App/Electric_SpecParamBySeriesId.ashx", param);
            if (httpResult.getStatusCode() == 200 || httpResult.getBody() != null) {
                ReturnValue<SpecParam> NewenergyTmpModel = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SpecParam>>() {
                });
                if (NewenergyTmpModel != null && NewenergyTmpModel.getResult() != null && NewenergyTmpModel.getResult().getSpecitems() != null) {

                    List<Integer> mileagelist = new ArrayList<>();
                    List<Float> officialfastchargetimelist = new ArrayList<>();
                    List<Float> officialslowchargetimelist = new ArrayList<>();
                    List<Float> batterycapacitylist = new ArrayList<>();
                    for (SpecParamItem itemNewenergy : NewenergyTmpModel.getResult().getSpecitems()) {
                        if (itemNewenergy.getSpecstate() == 20) {
                            if (itemNewenergy.getMileage() != 0) {
                                mileagelist.add(itemNewenergy.getMileage());
                            }
                            if (itemNewenergy.getOfficialfastchargetime() != 0) {
                                officialfastchargetimelist.add(itemNewenergy.getOfficialfastchargetime());
                            }
                            if (itemNewenergy.getOfficialslowchargetime() != 0) {
                                officialslowchargetimelist.add(itemNewenergy.getOfficialslowchargetime());
                            }
                            if (itemNewenergy.getBatterycapacity() != 0) {
                                batterycapacitylist.add(itemNewenergy.getBatterycapacity());
                            }

                        }
                    }

                    if (mileagelist.size() > 0) {
                        xuhang = Collections.max(mileagelist).toString();
                    }
                    if (officialfastchargetimelist.size() > 0) {
                        kuaichong = new BigDecimal(Collections.max(officialfastchargetimelist).toString()).stripTrailingZeros().toPlainString();
                    }
                    if (officialslowchargetimelist.size() > 0) {
                        manchong = new BigDecimal(Collections.max(officialslowchargetimelist).toString()).stripTrailingZeros().toPlainString();
                    }
                    if (batterycapacitylist.size() > 0) {
                        rongliang = new BigDecimal(Collections.max(batterycapacitylist).toString()).stripTrailingZeros().toPlainString();
                    }
                }

            }
            List<Integer> sss = new ArrayList<>(item.getElectricmotormileage());
            if (sss.size() > 0) {
                if (sss.get(0).equals(sss.get(sss.size() - 1))) {
                    fuelconsumption = sss.get(0) + "公里";
                } else {
                    sss.sort(Comparator.naturalOrder());
                    fuelconsumption = sss.get(0) + "-" + sss.get(sss.size() - 1) + "公里";

                }
            } else {
                fuelconsumption = "暂无";
            }
            Cartype = 1;

        } else {
            if (item.getMinfuelconsumption() == 0 && item.getMaxfuelconsumption() == 0) {
                fuelconsumption = "暂无";
            } else if (item.getMinfuelconsumption() == item.getMaxfuelconsumption()) {
                fuelconsumption = item.getMinfuelconsumption() + "L/100km";
            } else {
                fuelconsumption = item.getMinfuelconsumption() + "-" + item.getMaxfuelconsumption() + "L/100km";
            }
        }

        String jxspriceroot = CarPriceUtils.GetSpecPrice(fprice, lprice);
        ReturnValue<VR> tmpModelVrone = getvrType1.get();
        ReturnValue<VR> tmpModelVrtwo = getvrType2.get();
        SeriesInfo seriesInfo = new SeriesInfo();
        seriesInfo.setCartype(Cartype);
        seriesInfo.setBrandid(item.getBrandid());
        seriesInfo.setBrandname(item.getBrandname());
        seriesInfo.setFctid(item.getFctid());
        seriesInfo.setFctname(item.getFctname());
        seriesInfo.setFuelconsumption(fuelconsumption);
        seriesInfo.setId(item.getId());
        seriesInfo.setLevelid(item.getLevelid());
        seriesInfo.setLevelname(item.getLevelname());
        seriesInfo.setLogo(item.getPicitems() == null ? null : item.getPicitems().get(0).replace("autohomecar", "640x480_0_q30_autohomecar"));
        seriesInfo.setName(item.getName());
        seriesInfo.setPrice(CarPriceUtils.GetSpecPrice(item.getMinprice(), item.getMaxprice()));
        seriesInfo.setSpeccount(item.getSellspecnum());
        seriesInfo.setState(item.getState());
        seriesInfo.setChezhuCount(chezhujiagecount);
        seriesInfo.setWhitelog(!StringUtil.isNotNullAndWhiteSpace(whitelog) ? seriesInfo.getLogo() : whitelog);
        seriesInfo.setTransparentphone(transparentphone);
        seriesInfo.setEvalScore(EvalScore);
        seriesInfo.setLoadSeriesPRCTypeStr(LoadSeriesPRCTypeStr.size() > 6 ? LoadSeriesPRCTypeStr.subList(0, 6) : LoadSeriesPRCTypeStr);
        seriesInfo.setNewenergy(item.getNewenergy());
        seriesInfo.setButiePrice(butiePrice);
        seriesInfo.setVrNS(tmpModelVrone == null || tmpModelVrone.getResult() == null || tmpModelVrone.getResult().getDatalist() == null || tmpModelVrone.getResult().getDatalist().size() == 0 ? new Object() : tmpModelVrone.getResult().getDatalist().get(0));
        seriesInfo.setVrWG(tmpModelVrtwo == null || tmpModelVrtwo.getResult() == null || tmpModelVrtwo.getResult().getDatalist() == null || tmpModelVrtwo.getResult().getDatalist().size() == 0 ? new Object() : tmpModelVrtwo.getResult().getDatalist().get(0));
        seriesInfo.setBrandlog(brandlog);
        seriesInfo.setPicnum(item.getPicnum());
        seriesInfo.setJXSprice(jxspriceroot);
        seriesInfo.setMaxCPrice(MaxCPrice);
        seriesInfo.setFprice(fprice);
        seriesInfo.setLprice(lprice);
        seriesInfo.setKuaichong(kuaichong);
        seriesInfo.setManchong(manchong);
        seriesInfo.setRongliang(rongliang);
        seriesInfo.setXuhang(xuhang);

        return ReturnValue.buildSuccessResult(seriesInfo);
    }

    private CompletableFuture<ReturnValue<Series>> series_ParamBySeriesId(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Series>>() {
            @Override
            public ReturnValue<Series> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("seriesid", seriesId);
                param.put("state", "0X001");

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/CarPrice/Series_ParamBySeriesId.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Series>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<CarPrice>> getCarPriceInfoSpecList(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<CarPrice>>() {
            @Override
            public ReturnValue<CarPrice> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "app");
                param.put("seriesid", seriesId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://jiageapi.in.autohome.com.cn/api/carprice/GetCarPriceInfoSpecList", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarPrice>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<SeriesPhoto>> series_photowhitelogobyseriesid(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<SeriesPhoto>>() {
            @Override
            public ReturnValue<SeriesPhoto> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("serieslist", seriesId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/carpic/series_photowhitelogobyseriesid.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SeriesPhoto>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<List<HashMap>>> getSeriesEvalScore(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<List<HashMap>>>() {
            @Override
            public ReturnValue<List<HashMap>> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "m");
                param.put("seriesIds", seriesId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://koubei.api.sjz.autohome.com.cn/api/Series/GetSeriesEvalScore", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<HashMap>>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<DimSeriesPRCT>> loadSeriesPRCType(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<DimSeriesPRCT>>() {
            @Override
            public ReturnValue<DimSeriesPRCT> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "m");
                param.put("seriesId", seriesId);
                param.put("typekey", 1);
                param.put("year", 1);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://koubei.api.sjz.autohome.com.cn/api/Semantic/LoadSeriesPRCType", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<DimSeriesPRCT>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<Subsidy>> series_BuTieJiaBySeriesId(int seriesId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Subsidy>>() {
            @Override
            public ReturnValue<Subsidy> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("serieslist", seriesId);
                param.put("cityid", cityId);
                param.put("seriesstate", 1);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/CarPrice/Series_BuTieJiaBySeriesId.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Subsidy>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<VR>> getvrlistbyseriesid(int seriesId, int vrType) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<VR>>() {
            @Override
            public ReturnValue<VR> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("category", "car");
                param.put("seriesid", seriesId);
                param.put("vrtype", vrType);
                param.put("orderby", 1);
                param.put("pagesize", 1);
                param.put("pageindex", 1);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://panocms.api.autohome.com.cn/v1/pano/getvrlistbyseriesid", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<VR>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<Brand>> brand_infobyseriesid(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Brand>>() {
            @Override
            public ReturnValue<Brand> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("seriesid", seriesId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/brand_infobyseriesid.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Brand>>() {
                });
            }
        });
    }

    @Override
    public CompletableFuture<ReturnValue<Spec>> spec_detailbyseriesIdone(int seriesId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Spec>>() {
            @Override
            public ReturnValue<Spec> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("seriesid", seriesId);
                param.put("state", "0X001F");

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_detailbyseriesId.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Spec>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<MinPrice>> getMinPriceBySeries(int seriesId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<MinPrice>>() {
            @Override
            public ReturnValue<MinPrice> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("seriesId", seriesId);
                param.put("CityId", cityId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/price/getminPriceBySeries", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }

                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<MinPrice>>() {
                });
            }
        });
    }

    private ReturnValue<MinPriceItemSimple> GetMinPriceBySpecSimple(String specIds, int cityId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "cms");
        param.put("SpecIds", specIds);
        param.put("cityid", cityId);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/price/GetMinPriceBySpecSimple", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<MinPriceItemSimple>>() {
        });
    }


    @Override
    public ReturnValue<GetSpecInfoquick> GetSpecInfoquick(int specId, int cityId) throws ExecutionException, InterruptedException {
        CompletableFuture<ReturnValue<SpecInfo>> spec_infobyspecid = spec_infobyspecid(specId);
        CompletableFuture<ReturnValue<CarSpec>> GetJiageListBySpec = GetJiageListBySpec(specId, cityId);
        CompletableFuture<ReturnValue<CarPic>> picclass_classitemsbyspecid = picclass_classitemsbyspecid(specId);
        CompletableFuture<ReturnValue<Spec_Electric>> Spec_ElectricSubsidyBySpecList = Spec_ElectricSubsidyBySpecList(specId, cityId);
        CompletableFuture<ReturnValue<Dealer>> GetDealerListSpec = GetDealerListSpec(specId, cityId);

        CompletableFuture.allOf(spec_infobyspecid, GetJiageListBySpec, picclass_classitemsbyspecid, Spec_ElectricSubsidyBySpecList, GetDealerListSpec).join();

        ReturnValue<SpecInfo> tmpModel = spec_infobyspecid.get();
        if (tmpModel == null || tmpModel.getReturncode() != 0 || tmpModel.getResult() == null) {
            return ReturnValue.buildSuccessResult(null);
        }

        ReturnValue<CarPic> tmpModelColorCount = picclass_classitemsbyspecid.get();
        int colorCount = 0;
        if (tmpModelColorCount != null && tmpModelColorCount.getResult() != null) {
            for (CarPicItem item : tmpModelColorCount.getResult().getClassitems()) {
                colorCount = colorCount + item.getPiccount();
            }
        }

        ReturnValue<Spec_Electric> tmpModelbutiePrice = Spec_ElectricSubsidyBySpecList.get();
        String butiePrice = "暂无报价";
        if (tmpModelbutiePrice != null && tmpModelbutiePrice.getResult() != null && tmpModelbutiePrice.getResult().getSpecitems().size() > 0) {
            //butiePrice = GetPriceFormat(tmpModelbutiePrice.result.specitems[0].price, tmpModelbutiePrice.result.specitems[0].price);
            butiePrice = CarPriceUtils.getStrPrice(tmpModelbutiePrice.getResult().getSpecitems().get(0).getPrice(), tmpModelbutiePrice.getResult().getSpecitems().get(0).getPrice());
        }

        int minNewsPrice = 0;
        String minNewsPriceC = StringUtils.EMPTY;
        ReturnValue<Dealer> tmpModelminimumPrice = GetDealerListSpec.get();
        if (tmpModelminimumPrice != null && tmpModelminimumPrice.getResult() != null && tmpModelminimumPrice.getResult().getList() != null && tmpModelminimumPrice.getResult().getList().size() > 0) {
            minNewsPrice = tmpModelminimumPrice.getResult().getList().get(0).getMinNewsPrice();
            minNewsPriceC = CarPriceUtils.getStrPrice(tmpModelminimumPrice.getResult().getList().get(0).getMinNewsPrice(), tmpModelminimumPrice.getResult().getList().get(0).getMinNewsPrice());
        }

        SpecInfo item = tmpModel.getResult();
        // var fuelconsumption = "";
        String Spceprice = CarPriceUtils.getStrPrice(item.getMinprice(), item.getMinprice()); //2021 车型只显示最小价格
        String Differenceprice = CarPriceUtils.GetDifferenceFormat(minNewsPrice, item.getMinprice());

        ReturnValue<SeriesPhoto> tmpModelWhitePic = series_photowhitelogobyseriesid(item.getSeriesid()).get();
        Map<Integer, String> dicPic = new HashMap<>();
        if (tmpModelWhitePic != null && tmpModelWhitePic.getResult() != null) {
            for (SeriesPhotoItem itemWhite : tmpModelWhitePic.getResult().getSerieslist()) {
                dicPic.put(itemWhite.getId(), itemWhite.getSeriespnglogo());
            }
        }

        ReturnValue<CarSpec> model = GetJiageListBySpec.get();
        GetSpecInfoquick output = new GetSpecInfoquick();
        output.setSpecid(item.getId());
        output.setSpecname(item.getName());
        //speclog = PicturePrefix.ConvertCarImg(item.logo.ToString().Replace("https", "http"), ""),
        //speclog = item.logo.ToString(),
        output.setSpeclog(GetFullImagePathByPrefix(item.getLogo()));
        output.setSpceprice(Spceprice);
        output.setSeriesname(item.getSeriesname());
        output.setState(item.getState());
        output.setBrand(item.getBrandname());
        output.setSeries(item.getSeriesname());
        output.setYear(item.getYearname());
        output.setJiagecount(model == null || model.getResult() == null ? 0 : model.getResult().getRowcont());
        output.setSeriesinfo(GetSeriesInfo(item.getSeriesid()).getResult());
        output.setWhiteimg(dicPic.size() == 0 ? PicturePrefix.ConvertCarImg(item.getLogo().replace("https", "http").replace("upload", "carnews"), "160x120_0_f40_") : dicPic.get(item.getSeriesid()).replace("autohomecar", "400x300_0_f40_autohomecar"));
        output.setSeriesid(item.getSeriesid());
        output.setSpecstate(item.getState());
        output.setRanliaoxingshi(item.getRanliaoxingshi());
        output.setButiePrice(butiePrice);
        output.setBrandfirstletter(item.getBrandfirstletter());
        output.setColorCount(colorCount);
        //经销商列表最低价
        output.setMinNewsPrice(minNewsPriceC);
        output.setDifferenceprice(Differenceprice);
        output.setFueltype(item.getFueltype());

        return ReturnValue.buildSuccessResult(output);
    }

    @Override
    public CompletableFuture<ReturnValue<SpecInfo>> spec_infobyspecid(int specId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<SpecInfo>>() {
            @Override
            public ReturnValue<SpecInfo> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("specid", specId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_infobyspecid.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                ReturnValue<SpecInfo> rv = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SpecInfo>>() {
                });
                if (rv != null) {
                    rv.setMessage(StringUtils.EMPTY);
                }
                return rv;
            }
        });
    }

    @Override
    public ReturnValue<?> getBannerAttentionBySeries(String seriesIds, int cityid) {
        List<String> seriesIdslist = Arrays.asList(seriesIds.split(","));
        List<BannerAttentionBySeries> bySeries = new ArrayList<>();
        ArrayList<CompletableFuture<BannerAttentionBySeries>> bannerAttentionBySeries = new ArrayList<>();
        try {
            for (String seriesId : seriesIdslist) {
                CompletableFuture<BannerAttentionBySeries> completable =CompletableFuture.supplyAsync(()->
                        this.getAttentionBySeries(seriesId, cityid)
                );
                bannerAttentionBySeries.add(completable);
            }
            CompletableFuture[] carHashArray = bannerAttentionBySeries.toArray(new CompletableFuture[bannerAttentionBySeries.size()]);
            CompletableFuture.allOf(carHashArray).join();
            for (CompletableFuture<BannerAttentionBySeries> bannerAttentionBySery : bannerAttentionBySeries) {
                if(bannerAttentionBySery.get() != null){
                    bySeries.add(bannerAttentionBySery.get());
                }
            }
        } catch (Exception e) {
            log.error("getBannerAttentionBySeries exception ", e);
        }
        return new ReturnValue(0, "success", bySeries);
    }

    /**
     * 批量获取车型+城市销售价格
     * 获取格式化的车型成交价，砍价页uv等信息接口
     * wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=93326796
     * */
    @Override
    public List<SpecDealPrice> getDealPriceListBySpecIds(String specIds, int cityId) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("cityId", cityId);
        param.put("specIds", specIds);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/statistics/spec/getDealPriceFormatByCitySpecs", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        ReturnValue<SpecDealPrice.SpecDealPriceList> specDealPriceReturnValue = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SpecDealPrice.SpecDealPriceList>>() {
        });
        if(specDealPriceReturnValue != null && specDealPriceReturnValue.getResult() != null && !CollectionUtils.isEmpty(specDealPriceReturnValue.getResult().getList())){
            return specDealPriceReturnValue.getResult().getList();
        }
        else{
            log.warn("获取成交价数据异常, getDealPriceListBySpecIds : specIds:{}, cityId:{}", specIds, cityId);
            return null;
        }
    }

    @Override
    public List<SpecDealPrice> getDealPriceSpecBaseInfoListBySpecIds(String specIds, int cityId) {
        List<SpecDealPrice> specDealPriceList = getDealPriceListBySpecIds(specIds, cityId);
        if(!CollectionUtils.isEmpty(specDealPriceList)){
            //批量获取车型基本信息
            List<SpecBaseInfo> specParamsList = specService.specBaseInfoList(specIds);
            specDealPriceList.forEach(o ->{
                SpecBaseInfo specBaseInfo = specParamsList.stream().filter(s -> o.getSpecId() == s.getSpecId()).findFirst().orElse(null);
                if(specBaseInfo != null){
                    o.setSeriesName(specBaseInfo.getSeriesName());
                    o.setSpecName(specBaseInfo.getSpecName());
                }
            });
        }
        return specDealPriceList;
    }

    private BannerAttentionBySeries getAttentionBySeries(String seriesId, int cityid) {
        ReturnValue<?> returnValue = null;
        try {
            //查询redis
            String key = "series:hot:spec:baseinfo:v3:"+ seriesId + ":" + cityid;
            String redisValue = redisClientLightapp.getValue(key);
            if(!StringUtils.isEmpty(redisValue)){
                try{
                    BannerAttentionBySeries bannerAttentionBySeries = JacksonHelper.deserialize(redisValue, BannerAttentionBySeries.class);
                    return bannerAttentionBySeries;
                }
                catch (Exception ex){
                    log.error("getAttentionBySeries 缓存数据反序列化异常");
                }
            }
            //调用根据车系id获取当前车系的热门车型id
            SpecMinPrice specMinPrice = dealerNewService.getMinPriceDTO(Integer.valueOf(seriesId), cityid);
            Integer specid = specMinPrice == null ? 0 : specMinPrice.getSpecId();
            int minNewsPrice = specMinPrice == null ? 0 : specMinPrice.getNewsPrice();
            if (specid == null || specid <= 0) {
                log.info("getAttentionBySeries 地区车系最低价为空 seriesId:{}",seriesId);
                specid = getOnSaleSpecId(Integer.parseInt(seriesId));
            }

            if (specid != null && specid > 0){
                BannerAttentionBySeries bannerAttentionBySeries = new BannerAttentionBySeries();
                //封装基础数据
                CompletableFuture<ReturnValue<SpecInfo>> spec_infobyspecid = spec_infobyspecid(specid);
                CompletableFuture<ReturnValue<Series>> series_ParamBySeriesId = series_ParamBySeriesId(Integer.valueOf(seriesId));
                CompletableFuture<ReturnValue<Spec>> spec_detailbyseriesIdone = spec_detailbyseriesIdone(Integer.valueOf(seriesId));
//                CompletableFuture<ReturnValue<MinPrice>> getMinPriceBySeries = getMinPriceBySeries(Integer.valueOf(seriesId), cityid);
                CompletableFuture<ReturnValue<DealerV2>> getDealerListSpec = GetDealerListSpecV2(specid, cityid);
                CompletableFuture<ReturnValue<Subsidy>> series_BuTieJiaBySeriesId = series_BuTieJiaBySeriesId(Integer.valueOf(seriesId), cityid);
                CompletableFuture<ReturnValue<SpecDealPrice.SpecDealPriceList>> dealPrice = getDealPrice(specid, cityid);
                CompletableFuture<ReturnValue<DecisionFinanceBest>> returnValueCompletableFuture = decisionFinanceBestResult(specid, cityid);

                CompletableFuture.allOf(spec_infobyspecid, series_ParamBySeriesId, spec_detailbyseriesIdone ,
                        getDealerListSpec ,series_BuTieJiaBySeriesId ,dealPrice ,returnValueCompletableFuture).join();
                //车系id
                bannerAttentionBySeries.setSeriesId(seriesId);
                //填充attentionBySeries对象
                ReturnValue<SpecInfo> tmpModel = spec_infobyspecid.get();
                if (tmpModel != null && tmpModel.getResult() != null) {
                    SpecInfo specInfo = tmpModel.getResult();
                    PopularSpecInfo popularSpecInfo = new PopularSpecInfo();
                    popularSpecInfo.setId(specInfo.getId());
                    popularSpecInfo.setMaxprice(specInfo.getMaxprice());
                    popularSpecInfo.setMinprice(specInfo.getMinprice());
                    popularSpecInfo.setState(specInfo.getState());
                    bannerAttentionBySeries.setAttentionBySeries(popularSpecInfo);
                }
                //填充seriesInfo对象
                ReturnValue<Series> seriesReturnValue = series_ParamBySeriesId.get();
                ReturnValue<Spec> specReturnValue = spec_detailbyseriesIdone.get();
//                ReturnValue<MinPrice> minPriceReturnValue = getMinPriceBySeries.get();
                if (seriesReturnValue != null && seriesReturnValue.getResult() != null){
                    Series item = seriesReturnValue.getResult();
                    PopularSeriesInfo seriesInfo = new PopularSeriesInfo();
                    seriesInfo.setId(item.getId());
                    seriesInfo.setName(item.getName());
                    seriesInfo.setPrice(CarPriceUtils.GetSpecPrice(item.getMinprice(), item.getMaxprice()));
                    seriesInfo.setState(item.getState());
                    seriesInfo.setTransparentphone(item.getPnglogo());
                    String jxspriceroot = getJxspriceroot(cityid, specReturnValue);
                    seriesInfo.setJxSprice(jxspriceroot);
//                    String MaxCPrice = getMaxCPrice(specReturnValue, minPriceReturnValue);
                    seriesInfo.setMaxCPrice("");//与前端确认，这个字段未使用
                    bannerAttentionBySeries.setSeriesInfo(seriesInfo);
                }
                //填充specInfoquick对象
                SpecInfoquick specInfoquick = new SpecInfoquick();
                //获取minNewsPrice（非电车）
                if(minNewsPrice == 0){
                    //未获取到最低报价,走此处逻辑
                    ReturnValue<DealerV2> tmpModelminimumPrice = getDealerListSpec.get();
                    if (tmpModelminimumPrice != null
                            && tmpModelminimumPrice.getResult() != null
                            && tmpModelminimumPrice.getResult().getList() != null
                            && tmpModelminimumPrice.getResult().getList().size() > 0) {

                        minNewsPrice = tmpModelminimumPrice.getResult().getList().get(0).getMinNewsPrice();
                    }
                    specInfoquick.setMinNewsPrice(String.valueOf(minNewsPrice));
                }
                else{
                    //最低报价赋值
                    specInfoquick.setMinNewsPrice(String.valueOf(minNewsPrice));
                }

                //获取butiePrice（电车）
                String butiePrice = "暂无报价";
                ReturnValue<Subsidy> tmpModelBuTieJia = series_BuTieJiaBySeriesId.get();
                if (tmpModelBuTieJia != null && tmpModelBuTieJia.getReturncode() == 0 && tmpModelBuTieJia.getResult() != null) {
                    butiePrice = CarPriceUtils.GetSpecPrice(tmpModelBuTieJia.getResult().getSeriesitems().get(0).getMinprice(), tmpModelBuTieJia.getResult().getSeriesitems().get(0).getMaxprice());
                }
                specInfoquick.setButiePrice(butiePrice);
                //获取降价金额（Differenceprice）、 厂商指导价（spceprice）
                if (tmpModel != null && tmpModel.getResult() != null) {
                    SpecInfo specInfo = tmpModel.getResult();
                    String Spceprice = CarPriceUtils.getStrPrice(specInfo.getMinprice(), specInfo.getMinprice()); //2021 车型只显示最小价格
                    String Differenceprice = CarPriceUtils.GetDifferenceFormat(minNewsPrice, specInfo.getMinprice());
                    specInfoquick.setDifferenceprice(Differenceprice);
                    specInfoquick.setSpceprice(Spceprice);
                }
                //获取近期最低成交价（lastMonthDealPriceFormat）
                ReturnValue<SpecDealPrice.SpecDealPriceList> specDealPriceReturnValue = dealPrice.get();
                if (specDealPriceReturnValue.getResult() != null && !CollectionUtils
                        .isEmpty(specDealPriceReturnValue.getResult().getList())){
                    specInfoquick.setLastMonthDealPriceFormat(specDealPriceReturnValue.getResult().getList().get(0).getLastMonthDealPriceFormat());
                }
                //获取分期特惠：预估首付价格（downpayAmount）、预估月供价格（monthPay）
                ReturnValue<DecisionFinanceBest> decisionFinanceBestReturnValue = returnValueCompletableFuture.get();
                if (decisionFinanceBestReturnValue != null && decisionFinanceBestReturnValue.getResult() != null) {
                    specInfoquick.setDownpayAmount(decisionFinanceBestReturnValue.getResult().getDownpayAmount());
                    specInfoquick.setMonthPay(decisionFinanceBestReturnValue.getResult().getMonthPay());
                }
                bannerAttentionBySeries.setSpecInfoquick(specInfoquick);
                //设置缓存
                if(bannerAttentionBySeries.getAttentionBySeries() != null ){
                    redisClientLightapp.set(key, JacksonHelper.serialize(bannerAttentionBySeries), 2, TimeUnit.HOURS);
                }
                return bannerAttentionBySeries;
            }
        } catch (Exception e) {
            log.error("getBannerAttentionBySeries exception ", e);
        }
        return null;
    }

    /**
     * 查询车系下热门车型接口
     * */
    public Integer getSpecid(String seriesIds) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("seriesids", seriesIds);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/Car/GetSeriesRate.ashx", param);
        if (httpResult.getBody() != null && httpResult.getStatusCode() == 200) {
            ReturnValue<List<SeriesRate>> rsp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<SeriesRate>>>() {
            });
            /*if (!CollectionUtils.isEmpty(rsp.getResult())){
                SeriesRate seriesRate = rsp.getResult().get(0);
                if (seriesRate != null && !CollectionUtils.isEmpty(seriesRate.getSpecattentions())){
                    return seriesRate.getSpecattentions().get(0).getSpecid();
                }
            }*/
            List<SeriesRate> seriesRateList = rsp.getResult();
            for (SeriesRate series : seriesRateList) {
                List<SeriesRateSpecattentions> specattentions = series.getSpecattentions();
                for (SeriesRateSpecattentions specattention : specattentions) {
                    //在售和停产在售
                    if (specattention.getSpecstate() == (20) || specattention.getSpecstate() == (30)){
                        return specattention.getSpecid();
                    }

                }

            }
        }
        return null;
    }

    /**
     * 获取车系下热门车型列表
     * @param seriesIds
     * @return
     */
    public ReturnValue<List<SeriesRate>> getHotSpecidList(String seriesIds) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("seriesids", seriesIds);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/Car/GetSeriesRate.ashx", param);
        if (httpResult.getBody() != null && httpResult.getStatusCode() == 200) {
            ReturnValue<List<SeriesRate>> rsp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<SeriesRate>>>() {
            });
            return rsp;
        }
        return null;
    }

    private Integer getOnSaleSpecId (Integer seriesId){

        String redisKey = String.format("getAttentionBySeries getOnSaleSpecId::%d", seriesId);
        String redisValue = redisClientLightapp.get(redisKey);
        if (!StringUtil.isEmpty(redisValue)) {
            return JacksonHelper.deserialize(redisValue, Integer.class);
        }
        SpecDto specDto = carLibraryService.getSpecsBySeriesId(seriesId);
        if (specDto != null) {
            List<SpecDto.Specitems> specList = specDto.getSpecitems();
            if (!CollectionUtils.isEmpty(specList)) {

                for (SpecDto.Specitems specitems : specList) {
                    if (specitems.getState() == 20 || specitems.getState() == 30) {
                        redisClientLightapp.set(redisKey,specitems.getId(),1,TimeUnit.DAYS);
                        return specitems.getId();
                    }
                }
            }
        }

        return null;
    }

    private String getJxspriceroot(int cityid, ReturnValue<Spec> specReturnValue) {
        int fprice = 0;
        int lprice = 0;
        List<SpecItem> specListSell;
        if (specReturnValue != null && specReturnValue.getResult() != null && specReturnValue.getResult().getSpecitems() != null) {
            specListSell = specReturnValue.getResult().getSpecitems();
        } else {
            specListSell = new ArrayList<>();
        }
        String delids = specListSell.stream().filter(f -> f.getState() == 20 || f.getState() <= 30).map(m -> String.valueOf(m.getId())).collect(Collectors.joining(","));
        if (!delids.equals(StringUtils.EMPTY)) {
            ReturnValue<MinPriceItemSimple> specdealprice = GetMinPriceBySpecSimple(delids, cityid);
            List<DealerSpecPrice> specdealpricelist = new ArrayList<>();
            if (specdealprice != null && specdealprice.getResult() != null && specdealprice.getResult().getList() != null) {
                for (MinPriceItemSimpleItem itemSimple : specdealprice.getResult().getList()) {
                    DealerSpecPrice dealerSpecPrice = new DealerSpecPrice();
                    dealerSpecPrice.setSpecId(itemSimple.getSpecId());
                    dealerSpecPrice.setMinOriginalPrice(itemSimple.getMinPrice());
                    specdealpricelist.add(dealerSpecPrice);
                }
            }
            if (specdealpricelist.size() > 0) {
                specdealpricelist = specdealpricelist.stream().sorted(Comparator.comparing(DealerSpecPrice::getMinOriginalPrice)).collect(Collectors.toList());
                fprice = specdealpricelist.get(0).getMinOriginalPrice();
                lprice = specdealpricelist.get(specdealpricelist.size() - 1).getMinOriginalPrice();
            }
        }
        String jxspriceroot = CarPriceUtils.GetSpecPrice(fprice, lprice);
        return jxspriceroot;
    }

    private String getMaxCPrice(ReturnValue<Spec> specReturnValue, ReturnValue<MinPrice> minPriceReturnValue) {
        List<SpecItem> specListSell;
        if (specReturnValue != null && specReturnValue.getResult() != null && specReturnValue.getResult().getSpecitems() != null) {
            specListSell = specReturnValue.getResult().getSpecitems();
        } else {
            specListSell = new ArrayList<>();
        }
        List<DealerSpecPrice> dealerPrices = new ArrayList<>();
        if (minPriceReturnValue != null && minPriceReturnValue.getReturncode() == 0 && minPriceReturnValue.getResult().getList() != null) {
            for (MinPriceItem minPriceItem : minPriceReturnValue.getResult().getList()) {
                DealerSpecPrice dealerSpecPrice = new DealerSpecPrice();
                dealerSpecPrice.setSpecId(minPriceItem.getSpecId());
                dealerSpecPrice.setMinPrice(minPriceItem.getMinPrice());
                dealerSpecPrice.setDealPriceQ(minPriceItem.getDealPrice());
                dealerSpecPrice.setOriginalPrice(minPriceItem.getOriginalPrice());
                dealerSpecPrice.setPrice(minPriceItem.getPrice());
                dealerSpecPrice.setUrl(minPriceItem.getUrl());
                dealerPrices.add(dealerSpecPrice);
            }
        }
        String MaxCPrice = StringUtils.EMPTY;
        if (dealerPrices.size() > 0) {
            List<Double> maxcpricelist = new ArrayList<>();
            for (SpecItem specItem : specListSell) {
                Long dealerprice = dealerPrices.stream().filter(f -> f.getSpecId() == specItem.getId()).findAny().map(DealerSpecPrice::getMinPrice).orElse(null);
                if (specItem.getState() == 20 && dealerprice != null && dealerprice != 0) {
                    maxcpricelist.add(specItem.getMinprice() / 10000.0 - dealerprice / 10000.0);
                }
            }
            if (maxcpricelist.size() > 0 && Collections.max(maxcpricelist) > 0) {
                MaxCPrice = CarPriceUtils.getFormateStrPrice(Collections.max(maxcpricelist).toString()) + "万";
            }
        }
        return MaxCPrice;
    }

    private CompletableFuture<ReturnValue<SpecDealPrice.SpecDealPriceList>> getDealPrice(int specId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<SpecDealPrice.SpecDealPriceList>>() {
            @Override
            public ReturnValue<SpecDealPrice.SpecDealPriceList> get() {
                Map<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("cityId", cityId);
                param.put("specIds", specId);
                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/statistics/spec/getDealPriceFormatByCitySpecs", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                ReturnValue<SpecDealPrice.SpecDealPriceList> specDealPriceReturnValue = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SpecDealPrice.SpecDealPriceList>>() {
                });
                return specDealPriceReturnValue;
            }
        });
    }

    private CompletableFuture<ReturnValue<DecisionFinanceBest>> decisionFinanceBestResult(int specId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<DecisionFinanceBest>>() {
            @Override
            public ReturnValue<DecisionFinanceBest> get() {
                Map<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("cityId", cityId);
                param.put("specId", specId);
                param.put("isGoldClue", 1);
                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://fc-autoloan-api.openapi.corpautohome.com/decision/decisionFinanceBestResult", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<DecisionFinanceBest>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<CarSpec>> GetJiageListBySpec(int specId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<CarSpec>>() {
            @Override
            public ReturnValue<CarSpec> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "app");
                param.put("specid", specId);
                param.put("provinceid", 0);
                param.put("cityid", cityId);
                param.put("pageindex", 1);
                param.put("pagesize", 10);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://jiageapi.in.autohome.com.cn/api/carprice/GetCarSpecList", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarSpec>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<CarPic>> picclass_classitemsbyspecid(int specId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<CarPic>>() {
            @Override
            public ReturnValue<CarPic> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("specid", specId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carpic/picclass_classitemsbyspecid.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarPic>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<Spec_Electric>> Spec_ElectricSubsidyBySpecList(int specId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Spec_Electric>>() {
            @Override
            public ReturnValue<Spec_Electric> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "car");
                param.put("speclist", specId);
                param.put("cityid", cityId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/CarPrice/Spec_ElectricSubsidyBySpecList.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Spec_Electric>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<Dealer>> GetDealerListSpec(int specId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Dealer>>() {
            @Override
            public ReturnValue<Dealer> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "cms");
                param.put("specId", specId);
                param.put("cityId", cityId);
                param.put("orderType", "1");
                param.put("pageSize", "1");
                param.put("lat", "0");
                param.put("lon", "0");
                param.put("isCPL", "1");

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/dealerlist/list/GetDealerListSpec", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Dealer>>() {
                });
            }
        });
    }

    //旧的源接口调用方式返回结构多变,字段多,序列化错误难以排查
    private CompletableFuture<ReturnValue<DealerV2>> GetDealerListSpecV2(int specId, int cityId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<DealerV2>>() {
            @Override
            public ReturnValue<DealerV2> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "cms");
                param.put("specId", specId);
                param.put("cityId", cityId);
                param.put("orderType", "1");
                param.put("pageSize", "1");
                param.put("lat", "0");
                param.put("lon", "0");
                param.put("isCPL", "1");

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(
                        "http://dealer.api.lq.autohome.com.cn/dealerlist/list/GetDealerListSpec", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<DealerV2>>() {
                });
            }
        });
    }

    public String GetFullImagePathByPrefix(String path) {
        return GetFullImagePathByPrefix(path, StringUtils.EMPTY);
    }

    public String GetFullImagePathByPrefix(String path, String prefix) {
        if (StringUtil.isNullOrEmpty(path))
            return path;
        if (!path.contains("autohomecar__")) {
            String lastpath = "400x300_0_q95_autohomecar__" + path.substring(path.lastIndexOf('/') + 1);
            path = path.replace(path.substring(path.lastIndexOf('/') + 1), lastpath);
        } else {
            path = path.replace("autohomecar__", "400x300_0_q95_autohomecar__");

        }
        return path;
        //// 图片名称
        //string fileName = Path.GetFileName(path);
        //// 图片目录
        //string directoryName = Path.GetDirectoryName(path).Replace("~", "").Replace("\\", "/");
        //// 图片域名
        //string _imageDomain = GetImageDomain(path);
        //// 图片名称起始索引
        //int startIndex = path.Contains("/cardfs/") ? fileName.IndexOf("autohomecar__") : (fileName.IndexOf("_") + 1);
        //// 去掉已有前缀
        //fileName = startIndex > 0 ? fileName.Remove(0, startIndex) : fileName;

        //return string.Format("{0}{1}/{2}{3}", _imageDomain, directoryName, prefix, fileName);
    }

    @Override
    public ReturnValue<?> GetAttentionBySeries(String seriesid) throws ExecutionException, InterruptedException {
        return GetAttentionBySeries(seriesid, 0);
    }

    @Override
    public ReturnValue<?> GetAttentionBySeries(String seriesid, int quicktype) throws ExecutionException, InterruptedException {
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("seriesids", seriesid);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/Car/GetSeriesRate.ashx", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(729, "源接口错误");
        }
        ReturnValue<List<SeriesRate>> rsp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<SeriesRate>>>() {
        });
        //rsp.getResult().clear();
        if (rsp != null && rsp.getResult().size() > 0) {
            List<SeriesRateSpecattentions> list = new ArrayList<>();
            List<SeriesRateSpecattentions> listDef = new ArrayList<>();
            for (SeriesRateSpecattentions item : rsp.getResult().get(0).getSpecattentions()) {
                if (item.getSpecstate() == 20 || item.getSpecstate() == 30) {
                    list.add(item);
                } else {
                    listDef.add(item);
                }
            }
            int hotSpecId;
            if (list.size() > 0) {
                hotSpecId = list.stream().sorted(Comparator.comparing(SeriesRateSpecattentions::getAttention).reversed()).collect(Collectors.toList()).get(0).getSpecid();
            } else {
                if (listDef.size() > 0) {
                    hotSpecId = listDef.stream().sorted(Comparator.comparing(SeriesRateSpecattentions::getAttention).reversed()).collect(Collectors.toList()).get(0).getSpecid();
                } else {
                    return ReturnValue.buildErrorResult(751, "无数据");
                }
            }
            ReturnValue<SpecInfo> rv = spec_infobyspecid(hotSpecId).get();
            if (rv != null) {
                if (quicktype != 0) {
                    rv.getResult().setLogo(PicturePrefix.ConvertCarImg(rv.getResult().getLogo().replace("https", "http").replace("upload", "carnews"), "480x360_0_q30_"));
                }
                return rv;
            }
        }
        if (quicktype == 0) {
                /*state 值
                 * 未上市	0X0001
                   即将上市	0X0002
                   在产在售	0X0004
                   停产在售	0X0008
                   在售	0X000C
                   停售	0X0010
                   未售+在售	0X000F
                   在售+停售	0X001C
                   全部	0X001F
                 */
            //Spec_SpeclistBySeriesIds.ashx?_appid=car&seriesids=18,692&state=0X001F
            param.clear();
            param.put("_appid", "car");
            param.put("seriesids", seriesid);
            param.put("state", "0X000C");
            HttpHelper.HttpResult h = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/App/Spec_SpeclistBySeriesIds.ashx", param);
            if (h.getStatusCode() != 200 || h.getBody() == null) {
                return ReturnValue.buildErrorResult(782, "源接口错误");
            }
            ReturnValue<List<SpeclistBySeries>> bodySpce = JacksonHelper.deserialize(h.getBody(), new TypeReference<ReturnValue<List<SpeclistBySeries>>>() {
            });

            if (bodySpce != null && bodySpce.getResult().get(0).getList().size() > 0) {
                GetAttentionBySeries mresutl = new GetAttentionBySeries();
                SpeclistBySeries s = bodySpce.getResult().get(0);
                Speclist l = s.getList().get(0);
                mresutl.setSeriesname(s.getSeriesname());
                mresutl.setId(l.getSpecid());
                mresutl.setName(l.getSpecname());
                mresutl.setLogo(l.getImg());
                mresutl.setMinprice(l.getMinprice());

                // Config.CacheRedis.Set(rediskey, mresutl, DateTime.Now.AddMinutes(150));
                //return mresutl;
                return ReturnValue.buildSuccessResult(mresutl);
            }
            return ReturnValue.buildErrorResult(796, "源接口错误");
        } else {
            param.clear();
            param.put("_appid", "car");
            param.put("seriesid", seriesid);
            param.put("state", "0X001F");//全部
            HttpHelper.HttpResult h = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/carprice/spec_detailbyseriesId.ashx", param);
            if (h.getStatusCode() != 200 || h.getBody() == null) {
                return ReturnValue.buildErrorResult(808, "源接口错误");
            }
            ReturnValue<SeriesSpecInfoBody> bodySpce = JacksonHelper.deserialize(h.getBody(), new TypeReference<ReturnValue<SeriesSpecInfoBody>>() {
            });

            if (bodySpce != null && bodySpce.getResult().getSpecitems() != null && bodySpce.getResult().getSpecitems().size() > 0) {
                SeriesSpecInfoOut out = new SeriesSpecInfoOut();
                out.setId(bodySpce.getResult().getSpecitems().get(0).getId());
                out.setName(bodySpce.getResult().getSpecitems().get(0).getName());
                out.setMinprice(bodySpce.getResult().getSpecitems().get(0).getMinprice());
                out.setMaxprice(bodySpce.getResult().getSpecitems().get(0).getMaxprice());
                out.setLogo(PicturePrefix.ConvertCarImg(bodySpce.getResult().getSpecitems().get(0).getLogo().replace("https", "http").replace("upload", "carnews"), "480x360_0_q30_"));
                out.setYearid(bodySpce.getResult().getSpecitems().get(0).getSyearid());
                out.setYearname(bodySpce.getResult().getSpecitems().get(0).getYear());
                out.setSeriesid(seriesid);
                out.setSeriesname(StringUtils.EMPTY);
                out.setSerieslogo(StringUtils.EMPTY);
                out.setState(bodySpce.getResult().getSpecitems().get(0).getState());
                return ReturnValue.buildSuccessResult(out);
            }
            return ReturnValue.buildErrorResult(829, "源接口错误");
        }

    }
    @Override
    public ReturnValue<?> getbrightpointsconfigbyseriesid(HttpServletRequest request) {
        final String url = "https://carservice.autohome.com.cn/brightpointsconfig/getbrightpointsconfigbyseriesid";
        return getReturnValue(request, url);
    }


    private ReturnValue<?> getReturnValue(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, params);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<?> resp = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        return resp;
    }

    private ReturnValue<?> postReturnValue(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }


        String paramJson = JacksonHelper.serialize(params);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(url, paramJson);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<?> resp = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        return resp;
    }

}