package com.autohome.applet.service.zfb.impl;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.FileItem;
import com.alipay.api.domain.AlipaySocialBaseContentlibStandardcontentPublishModel;
import com.alipay.api.domain.SourceMediaInfo;
import com.alipay.api.domain.SourceOffer;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayOpenFileUploadRequest;
import com.alipay.api.request.AlipaySocialBaseContentlibStandardcontentDeleteRequest;
import com.alipay.api.request.AlipaySocialBaseContentlibStandardcontentPublishRequest;
import com.alipay.api.request.AlipaySocialBaseContentlibStandardcontentQueryRequest;
import com.alipay.api.response.AlipayOpenFileUploadResponse;
import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentDeleteResponse;
import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentPublishResponse;
import com.alipay.api.request.AlipaySocialBaseLifeprodStandardcontentPublishRequest;
import com.alipay.api.response.AlipaySocialBaseLifeprodStandardcontentPublishResponse;

import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentQueryResponse;
import com.autohome.applet.model.dto.alipay.AlipayPublishVo;
import com.autohome.applet.model.dto.maindata.MainDataArticleDataDto;
import com.autohome.applet.service.zfb.ZFBPushInfoService;
import com.autohome.applet.util.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class ZFBPushInfoServiceImpl implements ZFBPushInfoService {

    private static final  String AlipayAPPID ="2015122501040373";

    private static final  String AlipayPrivateKey ="MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCj2IfwuVJaOITUZrYah0mEisFdFWw55+57Atbe1/28xGtjg0gG98RtR/sFP0e2wRic9qf+wOeN4yuez6aCdB8WDyi0Jh12/Jewo4cAkx+UUcz9c5jYEuQwZGXPDWkuo8h0TIir3Gz5e1wHrsJJZmm70uizyhjlpqOE8miNEwDce55sd/jOpeaXEtn77yXJ8eXHJefcvBd8WO7S/gd4xVm+xCG3svVGJyeKz71UHMLxxe431mUgxf6JJeNhzs39aB+fyewaSBxtT2lI7SZkfwRVStoqCvfUUHHqwlfa55UjTQm6535Y12mTtQJbA/qNJ3A4UcKLs45HhxVjQTo9AIfjAgMBAAECggEAN88ChuFpBqgnvn1529FnHklxEbbBVXwFxEq0S9WAbTLubUgDWbQ9aQfSnffvAnYFXzm5AVJNzQurCP4r9lV9XTMupyYwdR4WwSkCTBTQ9eFjFJWyYy8UINOhxgQIZsWM+ugyyW9cgyIct9U0AnJq43o0n0DgeCDpo9g5+ouViIPbOZ0/GPnKW+31OX9G6DVOB9qgEj7qiN6xcY2h4e2Vcr8dis9dSYzcrq6dx8JNIoLvHfXgKmMoA3BmpL5YQjfxMe7zAfqo35EqKt9BzcvI0qCu5xIdyLo8X+kADec5pSlWAtT6+JEJKwQYRPyDKDgCqUmuBdxCkjZ8nKV9KNJxAQKBgQDYNw+Zjt+P5IqTqXZIB/UZ8xLsDzO4lb207vwj4nOYuLmMr37l/vQV2lh4W43LaL4F8vIhTEuVkYl7U++gJZJWsjqjVXW6LYEsow74l2gYAgJBPLxeW3ZX41D84NV6bcNLxiDNBs94u/+1QvOg3JfU3W+gDuYlI8rlWvwseR8fYwKBgQDB/pXVoXLtmLD5C6ypm3Pdmht+Y++BP/lLY1L8VCTIn4LSHIaVDaDiSS6Fy5GPVtieGGhLZVaRZYP0rE1f/2W588MhuJy6C6vzXfVtoaFtLMqL7NM9bofRUgDdwkmF2aO3DO2Gid5cY4u/1JDWCkvMSReyuckybo03fCcUI0adgQKBgBVAxE3J13vjcSO2oqPN9G/Kju+ZRvI79dvVKf+1ALvgXIzRq12cRA3p2oxqI3P9JcCb8uUHBN784VC4HXPWfZ38AvuPKPVWB76FgLE64MaLXAFhoPp9bxKQi6Nvzkwsgefu38aHhtlNUYNVw3Dqfho0cgZ/sGuyKv2EhgkxFrgVAoGALhgj2gLmn/Xr9wEDIbnCKiF37uwn+HoO7g7HpRUJOHJOT0Zf5AK8+4eP+lKIX5qvFcGO5ntJunp4hCZ8rBavQSPedaC7/KjRTp5Atq/0/0/pQJ4hAzPEKcRmWErV1WwFLnVv6svPffxZNFd5oqIHqJ6ldt4WJFmITGq/jlVjbgECgYBNJr6gOG6VvECYuakh5zfWX3xsa5L7OAhQCmpOUbDy041dswl5f1mdYu3cjoPJaqKGFFtImQOjfM66NpEOteP+oW72Rq+k6/uLOI3AacuGYeCQo2QSuaW0Jjjg+lVDFxcPSUKZWZUJgFQWNyXlZ1YAOc6XM3GdbTeqeIFgT9U1gw==";

    private static final String AlipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk8xmPbViOcM1m8FvJDM0cjlXKd24IBlOomRlQbUlGca85VsDoZ4TcebvGDmE3f42DTgB9x25PS5Ifb8i8D249jgwrAMUzfzC8xIJud4TbIkV/TNBIpIYLIY10XLlFkgCxrWa1DbsBv/6TEQth6QP0Odo/MTmxjupwIDYA6v65jMfXDbZaARxvDv6d38nbvKbvvZwgRlHmuY8HssEcK2llp+QUvRRVjoFKVdqi0Dn5vccdB7W13VxEas2Z+Yt6d7ZkBTbRsZCSGp++K8G9lhzP/tDUZiV4AajHB65QwXIsmhoins9fv7oUK90cWJWybXeeIJZf8KvAMScwyh5fqRleQIDAQAB";

    private  AlipayConfig getAlipayConfig() {
        String privateKey  = AlipayPrivateKey;
        String alipayPublicKey = AlipayPublicKey;
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId(AlipayAPPID);
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }

    /**
     * 上报素材
     * @param fileContent
     */
    @Override
    public AlipayOpenFileUploadResponse uploadMaterial(FileItem fileContent) {
        try {
            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());
            //这是一个1x1像素的图片流
//            String imageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAEUlEQVR42mP4TyRgGFVIX4UAI/uOgGWVNeQAAAAASUVORK5CYII=";
            // 下列FileItem中也可用直接读取本地文件的方式来获取文件
            // FileItem imageContent = new FileItem("本地文件的绝对路径");

            // 构造请求参数以调用接口
            AlipayOpenFileUploadRequest request = new AlipayOpenFileUploadRequest();

            // 设置文件内容
//        FileItem fileContent = new FileItem("fileContent.jpg", Base64.getDecoder().decode(imageBase64));
            //文件内容:文件内容的二进制流
            request.setFileContent(fileContent);

            // 设置素材使用场景:素材使用场景，请关联其他开放平台接口联合使用。如果素材用于内容发布，请必传入content_creation
            request.setBizCode("content_creation");

            //文件上传额外参数:
//        FileUploadExtraParam extraParameters = new FileUploadExtraParam();
//        extraParameters.setExternUpload("youku");
//        extraParameters.setFileEncryptType("NO_ENCRYPT");
//        request.setExtraParameters(extraParameters);

            // 文件的md5值 文件的md5值，如果不传则不校验md5
//        request.setMd5("9cff2d79bc81acf12abbaca7328a4dc5");

            AlipayOpenFileUploadResponse response = alipayClient.execute(request);
            log.info( "ZFBPushInfoService.uploadMaterial 上报素材，调用结果 response:{}" ,response.getBody());

            if (response.isSuccess()) {
                log.info("ZFBPushInfoService.uploadMaterial 上报素材-->调用成功" ,response.getBody());
                return response;
            } else {
                log.info("ZFBPushInfoService.uploadMaterial 上报素材-->调用失败" ,response.getBody());
            }
        }catch (Exception e) {
            e.printStackTrace();
            log.error("ZFBPushInfoService.uploadMaterial 上报素材-->异常,e{}" ,e.getMessage());
        }
        return null;
    }

    /**
     * 推送长内容
     * @param contentId
     * @param content
     */
    @Override
    public AlipaySocialBaseLifeprodStandardcontentPublishResponse pushLongContent(MainDataArticleDataDto.ArticleData articleData) {

        try {

            // 初始化支付宝客户端
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 创建请求对象
            AlipaySocialBaseLifeprodStandardcontentPublishRequest request = new AlipaySocialBaseLifeprodStandardcontentPublishRequest();
            AlipayPublishVo alipayPublishVo = new AlipayPublishVo();
            alipayPublishVo.setSource_title(articleData.getTitle());
            alipayPublishVo.setSource_author("汽车之家");
            alipayPublishVo.setSource_text(articleData.getContent());
//            alipayPublishVo.setSource_text("<p>这个是文章正文，</p>");
            alipayPublishVo.setSource_type("IMAGE-CONTENT");
            alipayPublishVo.setSource_publish_date("2018-09-30 10:10:10");
            alipayPublishVo.setPermission_status("whole");
            alipayPublishVo.setSource_media_infos(Arrays.asList(new AlipayPublishVo.SourceMediaInfo(articleData.getFileId(), "cover_static")));
            request.setBizContent(JacksonHelper.serialize(alipayPublishVo));
            // 发送请求
            AlipaySocialBaseLifeprodStandardcontentPublishResponse response = alipayClient.execute(request);
            log.info("ZFBPushInfoService.pushLongContent 推送长内容，调用结果 response:{}" ,response.getBody());

            if(response.isSuccess()){
                log.info("ZFBPushInfoService.pushLongContent 推送长内容-->调用成功" ,response.getBody());
            } else {
                log.info("ZFBPushInfoService.pushLongContent 推送长内容-->调用失败" ,response.getBody());
            }
            return response;

        }catch (Exception e) {
            log.error("ZFBPushInfoService.pushLongContent 推送长内容-->异常,e{}" ,e.getMessage());
        }
        return null;
    }

    /**
     * 推送内容
     */
    @Override
    public AlipaySocialBaseContentlibStandardcontentPublishResponse pushContent(MainDataArticleDataDto.ArticleData articleData) {
        try {
            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

            // 构造请求参数以调用接口
            AlipaySocialBaseContentlibStandardcontentPublishRequest request = new AlipaySocialBaseContentlibStandardcontentPublishRequest();
            AlipaySocialBaseContentlibStandardcontentPublishModel model = new AlipaySocialBaseContentlibStandardcontentPublishModel();

            // 内容类型:1、短图文；2、视频（必选）
            model.setSourceType("2");

            // 内容标题（可选）
            model.setSourceTitle("这是文章标题");

            // 内容正文（可选）
            model.setSourceContent("文章正文");

            // 作者:预留字段，当前不对用户透出 （可选）
            model.setSourceAuthor("汽车之家");

            // 文章发布时间 仅支持  yyyy-MM-dd HH:mm:ss （可选）
            model.setSourcePublishDate("2018-09-30 10:10:10");

            // 来源文章的原始链接地址（可选）
//            model.setSourceLink("");

            // 设置素材列表（可选）
            List<SourceMediaInfo> sourceMediaInfos = new ArrayList<SourceMediaInfo>();
            SourceMediaInfo sourceMediaInfosVideo = new SourceMediaInfo();
            //image：图片 video：视频 cover_static：静态封面
            sourceMediaInfosVideo.setMediaType("video");
            //素材ID，对应“支付宝文件上传接口”获取的file_id
            sourceMediaInfosVideo.setMediaId(articleData.getFileId());

            SourceMediaInfo sourceMediaInfosImg = new SourceMediaInfo();
            sourceMediaInfosImg.setMediaType("cover_static");
            sourceMediaInfosImg.setMediaId("A*8zqQTp6__C4AAAAAR0AAAAgAfdp1AQ");

            sourceMediaInfos.add(sourceMediaInfosVideo);
            sourceMediaInfos.add(sourceMediaInfosImg);

            model.setSourceMediaInfos(sourceMediaInfos);

            // 设置内容关联服务
//            List<SourceOffer> sourceOffers = new ArrayList<SourceOffer>();
//            SourceOffer sourceOffers0 = new SourceOffer();
            //根据offer类型对应不同ID。支持小程序的APPID/小程序服务的服务编码/优惠券的活动ID
//            sourceOffers0.setOfferId("a2344");
            //offer类型。目前仅支持小程序（mini_app/service）、小程序服务（subservice）、营销活动（voucher）、商品(goods)、小游戏(game)。 注意：其中“小程序”offer类型，在发布接口请用mini_app，查询接口返回为service
//            sourceOffers0.setOfferType("mini_app");
//            sourceOffers.add(sourceOffers0);
//            model.setSourceOffers(sourceOffers);

            // 文章摘要:暂不需要（可选）
//            model.setSourceSummary("这是文章摘要");
            // 对应的appId  如果发送类型=自研/三方发送，不要传入；发送类型=mcn发送，请传入public_id【mcn机构绑定达人号ID】（条件必选）
//            model.setPublicId("");
            // 设置内容分发范围 1 - 所有人可见，2 - 分发范围受限。
//            model.setPermissionStatus("1");

            // 设置高德POI位置信息  暂不需要
//        AmapPoiInfo poiInfo = new AmapPoiInfo();
//        poiInfo.setPoiAddress("东环路50号");
//        poiInfo.setPoiName("苏州大学东校区");
//        poiInfo.setTypeName("科教文化服务;学校;高等院校");
//        poiInfo.setAreaCode("320508");
//        poiInfo.setLatitude("31.302929");
//        poiInfo.setPoiCode("B020001NCH");
//        poiInfo.setLongitude("120.64851");
//        poiInfo.setTypeCode("141201");
//        model.setPoiInfo(poiInfo);

            request.setBizModel(model);
            // 第三方代调用模式下请设置app_auth_token
            // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

            AlipaySocialBaseContentlibStandardcontentPublishResponse response = alipayClient.execute(request);
            log.info("ZFBPushInfoService.pushContent 内容发布，调用结果 response:{}" ,response.getBody());

            if (response.isSuccess()) {
                log.info("ZFBPushInfoService.pushContent 内容发布-->调用成功" );
            } else {
                log.info("ZFBPushInfoService.pushContent 内容发布-->调用失败" );
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }
            return response;
        }catch (Exception e) {
            log.error("ZFBPushInfoService.pushContent 内容发布-->异常,e{}" ,e.getMessage());
        }
        return null;
    }


    /**
     * 删除素材
     * @param contentId
     */
    @Override
    public void deleteMaterial(String contentId) {
        try {
            AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do","app_id","your private_key","json","GBK","alipay_public_key","RSA2");
            AlipaySocialBaseContentlibStandardcontentDeleteRequest request = new AlipaySocialBaseContentlibStandardcontentDeleteRequest();
            request.setBizContent("{" +
                    "  \"content_id\":\"20210927OB020010029500039941\"," +
                    "  \"public_id\":\"203123121232\"" +
                    "}");
            AlipaySocialBaseContentlibStandardcontentDeleteResponse response = alipayClient.execute(request);
            log.info("ZFBPushInfoService.deleteMaterial 删除素材，调用结果 response:{}" ,response.getBody());
            if(response.isSuccess()){
                log.info("ZFBPushInfoService.deleteMaterial 删除素材-->调用成功" );
            } else {
                log.info("ZFBPushInfoService.deleteMaterial 删除素材-->调用失败" );
            }
        }catch (Exception e) {
            log.error("ZFBPushInfoService.deleteMaterial 删除素材-->异常,e{}" ,e.getMessage());
        }
    }

    /**
     * 单条内容状态查询
     * @param contentId
     */
    @Override
    public void queryContentStatus(String contentId) {
        try {
            AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do","app_id","your private_key","json","GBK","alipay_public_key","RSA2");
            AlipaySocialBaseContentlibStandardcontentQueryRequest request = new AlipaySocialBaseContentlibStandardcontentQueryRequest();
            request.setBizContent("{" +
                    "  \"content_id\":"+contentId+
                    "}");
            AlipaySocialBaseContentlibStandardcontentQueryResponse response = alipayClient.execute(request);
            log.info("ZFBPushInfoService.queryContentStatus 单条内容状态查询，调用结果 response:{}" ,response.getBody());

            if(response.isSuccess()){
                log.info("ZFBPushInfoService.queryContentStatus 单条内容状态查询-->调用成功" );
            } else {
                log.info("ZFBPushInfoService.queryContentStatus 单条内容状态查询-->调用失败" );
            }
        }catch (Exception e) {
            log.error("ZFBPushInfoService.queryContentStatus 单条内容状态查询-->异常,e{}" ,e.getMessage());
        }
    }


    public static void main(String[] args)  {
        try {
            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig2());
            //这是一个1x1像素的图片流
            String imageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAEUlEQVR42mP4TyRgGFVIX4UAI/uOgGWVNeQAAAAASUVORK5CYII=";

            // 构造请求参数以调用接口
            AlipayOpenFileUploadRequest request = new AlipayOpenFileUploadRequest();

            // 设置文件内容
            FileItem fileContent = new FileItem("fileContent.jpg",Base64.getDecoder().decode(imageBase64));
            request.setFileContent(fileContent);

            // 设置素材使用场景
            request.setBizCode("content_creation");

            // 设置文件上传额外参数
//        FileUploadExtraParam extraParameters = new FileUploadExtraParam();
//        extraParameters.setExternUpload("youku");
//        extraParameters.setFileEncryptType("NO_ENCRYPT");
//        request.setExtraParameters(extraParameters);

            // 设置文件的md5值
//            request.setMd5("9cff2d79bc81acf12abbaca7328a4dc5");

            AlipayOpenFileUploadResponse response = alipayClient.execute(request);
            System.out.println(response.getBody());

            if (response.isSuccess()) {
                System.out.println("调用成功");
            } else {
                System.out.println("调用失败");
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }
        }catch (Exception e) {
            log.error("[上传文件]: 响应参数{}", e.getMessage());
        }

    }

    private static AlipayConfig getAlipayConfig2() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId(AlipayAPPID);
        alipayConfig.setPrivateKey(AlipayPrivateKey);
        alipayConfig.setFormat("json");
//        alipayConfig.setAlipayPublicKey(AlipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
}
