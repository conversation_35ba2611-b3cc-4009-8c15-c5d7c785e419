package com.autohome.applet.service.javaapi.impl;


import cn.hutool.core.bean.BeanUtil;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.SeriesBaseDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.*;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsResult;
import com.autohome.applet.service.javaapi.NewCarLaunchService;
import com.autohome.applet.service.javaapi.RankService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NewCarLaunchServiceImpl implements NewCarLaunchService {

    @Autowired
    private RankService rankService;
    @Resource(name = "lightapp")
    RedisClient redisClient;

    private final static String CARLAUNCHSERVICEHEADE = "new:car:launch:head";
    /**
     * new:car:launch:list:{a}:{车系级别}
     * {a}赋值说明：tab类型/每月第一天
     *      如果 tab类型 是即将上市， 赋值2099-12-31
     *      如果 tab类型 是已上市月份， 赋值当月第一天
     * 如：
     *  即将上市tab的新能源  new:car:launch:list:1:2
     *  5月份tab的新能源  new:car:launch:list:2024-05-01:2
     * */
    private final static String CARLAUNCHSERVICELIST = "new:car:launch:list:v2:%s:%s";

    @Override
    public ReturnValue<?> getNewCarLaunchHead() {
        //有个定制任务刷新这个缓存
        String redisValue = redisClient.get(CARLAUNCHSERVICEHEADE);

        if (!StringUtil.isEmpty(redisValue)) {
            List<NewCarLaunchHead> list = JacksonHelper
                    .deserialize(redisValue, new TypeReference<List<NewCarLaunchHead>>() {
                    });
            return ReturnValue.buildSuccessResult(list);
        }
        //没有命中缓存，说明缓存服务器有问题，需要排查
        log.error("getNewCarLaunchHead no cache, 需要检查缓存服务器(在task中)");
        List<NewCarLaunchHead> newCarLaunchHeadList = getNewCarLaunchHeadNoCache();
        return ReturnValue.buildSuccessResult(newCarLaunchHeadList);
    }

    @Override
    public List<NewCarLaunchHead> getNewCarLaunchHeadNoCache() {
        Long timebegin = System.currentTimeMillis();
        List<DateDto> dateList = getDateList();
        List<NewCarLaunchHead> newCarLaunchHeadList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dateList)){
//            ResponseContent<List<AllNewBrands>> responseSpecContent = getAllNewBrands("2024-05-11", "2025-05-10");
            ResponseContent<List<AllNewBrands>> responseSpecContent = getAllNewBrands(dateList.get(dateList.size()-1).getStartTime(), dateList.get(0).getEndTime());
            List<AllNewBrands> allNewBrandsList = responseSpecContent.getResult();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                for (int i = 0; i < dateList.size(); i++) {
                    Date startDate = sdf.parse(dateList.get(i).getStartTime());
                    Date endDate = sdf.parse(dateList.get(i).getEndTime());
                    List<AllNewBrands> newBrandsList = allNewBrandsList.stream().filter(f -> f.getOntimeDate() >= startDate.getTime()
                            && f.getOntimeDate() < endDate.getTime()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(newBrandsList)){
                        if (i == 0){
                            getHead(dateList.get(i).getStartTime(),dateList.get(i).getEndTime(), newCarLaunchHeadList , newBrandsList ,"即将上市");
                        }else {
                            getHead(dateList.get(i).getStartTime(),dateList.get(i).getEndTime(), newCarLaunchHeadList , newBrandsList ,null);
                        }
                    }
                }
            } catch (Exception e) {
                log.info("getNewCarLaunchHead",e);
            }
        }

//        int seconds = this.computeTime().intValue();
//        redisClient.set(redisKey,JacksonHelper.serialize(newCarLaunchHeadList),seconds,TimeUnit.SECONDS);
        redisClient.set(CARLAUNCHSERVICEHEADE,JacksonHelper.serialize(newCarLaunchHeadList),7,TimeUnit.DAYS);

        Long timeend = System.currentTimeMillis();
        log.warn("duration getNewCarLaunchHead: {}", timeend - timebegin);
        return newCarLaunchHeadList;
    }

    @Override
    public ReturnValue<?> getNewCarLaunchList(String startTime, String endTime, int levelid, String month) {
        //判断是即将上市，还是已经上市
        boolean isComingSoon = startTime.equals(DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE));
        String tabTypeKey = isComingSoon ? "2099-12-31" : startTime;
        String redisKey = String.format(CARLAUNCHSERVICELIST,tabTypeKey,levelid);

        String redisValue = redisClient.get(redisKey);
        if (!StringUtil.isEmpty(redisValue)) {
            List<BrandsTimeModel> List = JacksonHelper.deserialize(redisValue, new TypeReference<List<BrandsTimeModel>>() {
            });
            return ReturnValue.buildSuccessResult(List);
        }

        //没有命中缓存，说明缓存服务器有问题，需要排查
        log.error("getNewCarLaunchList no cache, 需要检查缓存服务器(在task中)");
        List<BrandsTimeModel> brandsTimeModels = getNewCarLaunchListNoCache(startTime, endTime, levelid, month);
        return ReturnValue.buildSuccessResult(brandsTimeModels);
    }

    @Override
    public List<BrandsTimeModel> getNewCarLaunchListNoCache(String startTime, String endTime, int levelid, String month) {
        //判断是即将上市，还是已经上市
        boolean isComingSoon = startTime.equals(DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE));
        String tabTypeKey = isComingSoon ? "2099-12-31" : startTime;
        String redisKey = String.format(CARLAUNCHSERVICELIST,tabTypeKey,levelid);

        Long timebegin = System.currentTimeMillis();

        ResponseContent<List<AllNewBrands>> allNewBrands = getAllNewBrands(startTime, endTime);
        List<AllNewBrands> newBrands = allNewBrands.getResult();
        if (isComingSoon){
            //如果是即将上市， 单独处理
            newBrands = newBrands.stream().sorted(Comparator.comparingLong(AllNewBrands::getOntimeDate)).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(newBrands)){
            switch (levelid) {
                case 1://全部
                    padField(newBrands);
                    break;
                case 2://新能源 需要单独处理
                    List<AllNewBrands> newEnergyBrands = new ArrayList<>();
                    List<Integer> seriesidList = newBrands.stream().map(AllNewBrands::getSeriesid).collect(Collectors.toList());
                    List<CarMainresult> getcarmain = getcarmainList(seriesidList);
                    List<CarMainresult> carMainresultList = getcarmain.stream().filter(f -> f.getNewenergy() == 1).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(carMainresultList)){
                        for (AllNewBrands newBrand : newBrands) {
                            for (CarMainresult carMainresult : carMainresultList) {
                                if (newBrand.getSeriesid() == carMainresult.getId()){
                                    newEnergyBrands.add(newBrand);
                                    break;
                                }
                            }
                        }
                        padField(newEnergyBrands);
                        List<BrandsTimeModel> brandsTimeModels = convertBrandsTimeModelList(newEnergyBrands);
                        redisClient.set(redisKey,JacksonHelper.serialize(brandsTimeModels),7,TimeUnit.DAYS);
                        return brandsTimeModels;
                    }else {
                        newBrands = null;
                    }
                    break;
                case 3://轿车
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 1 || f.getLevelid() == 2 || f.getLevelid() == 3 || f.getLevelid() == 4 || f.getLevelid() == 5 || f.getLevelid() == 6).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 4://SUV
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 16 || f.getLevelid() == 17 || f.getLevelid() == 18 || f.getLevelid() == 19 || f.getLevelid() == 20 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 5://MPV
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 21 || f.getLevelid() == 22 || f.getLevelid() == 23 || f.getLevelid() == 24 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 6://跑车
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 7 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 7://微面
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 11 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 8://轻客
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 13 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 9://皮卡
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 14 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                case 10://微卡
                    newBrands = newBrands.stream().filter(f -> f.getLevelid() == 12 ).collect(Collectors.toList());
                    padField(newBrands);
                    break;
                default:
                    newBrands = null;
                    break;
            }
        }

        List<BrandsTimeModel> brandsTimeModels = convertBrandsTimeModelList(newBrands);
        redisClient.set(redisKey,JacksonHelper.serialize(brandsTimeModels),7,TimeUnit.DAYS);

        Long timeend = System.currentTimeMillis();
        log.warn("duration getNewCarLaunchList : {}", timeend - timebegin);
        return brandsTimeModels;
    }

    @Override
    public List<AllNewBrands> allNewBrands() {
        List<DateDto> dateList = getDateList();
        ResponseContent<List<AllNewBrands>> responseSpecContent = getAllNewBrands(dateList.get(dateList.size()-1).getStartTime(), dateList.get(0).getEndTime());
        return responseSpecContent.getResult();
    }

    //填充字段
    private void padField(List<AllNewBrands> newBrands) {
        List<Integer> seriesidList = newBrands.stream().map(AllNewBrands::getSeriesid).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(seriesidList)){
            return;
        }
        String seriesids = seriesidList.stream().map(String::valueOf).collect(Collectors.joining(","));
        try {
            CompletableFuture<List<SeriesMinPriceExtendsResult>> seriesMinPriceExtendsFuture = CompletableFuture.supplyAsync(() -> getSeriesMinPriceExtends(seriesids));
            CompletableFuture<List<CarVrInfo>> carVrInfoListFuture = CompletableFuture.supplyAsync(() -> getcarvrinfobyseriesid(seriesidList));
            //CompletableFuture<List<CarMainresult>> carMainresultListFuture = CompletableFuture.supplyAsync(() -> getcarmain(seriesids));
            CompletableFuture<List<CarMainresult>> carMainresultListFuture = CompletableFuture.supplyAsync(() -> getcarmainList(seriesidList));
            CompletableFuture.allOf(seriesMinPriceExtendsFuture, carVrInfoListFuture, carMainresultListFuture).join();
            //是否可询价状态
            List<SeriesMinPriceExtendsResult> seriesMinPriceExtendsResultList = seriesMinPriceExtendsFuture.get();
            //车系全景
            List<CarVrInfo> carVrInfoList = carVrInfoListFuture.get();
            //车系在售状态
            List<CarMainresult> carMainresultList = carMainresultListFuture.get();
            for (AllNewBrands newBrand : newBrands) {
                if (!CollectionUtils.isEmpty(seriesMinPriceExtendsResultList)){
                    for (SeriesMinPriceExtendsResult seriesMinPriceExtendsResult : seriesMinPriceExtendsResultList) {
                        if (newBrand.getSeriesid() == seriesMinPriceExtendsResult.getSeriesId() && seriesMinPriceExtendsResult.getNewsPrice() > 0){
                            newBrand.setIsInquiry(1);
                            break;
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(carVrInfoList)){
                    for (CarVrInfo carVrInfo : carVrInfoList) {
                        if (carVrInfo.getHasexterior() && !CollectionUtils.isEmpty(carVrInfo.getExtinfo()) && newBrand.getSeriesid() == carVrInfo.getExtinfo().get(0).getSeriesid()){
                            newBrand.setShowUrl(carVrInfo.getExtinfo().get(0).getShowurl());
                            break;
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(carMainresultList)){
                    for (CarMainresult carMainresult : carMainresultList) {
                        if (newBrand.getSeriesid() == carMainresult.getId()){
                            newBrand.setState(carMainresult.getState());
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("padField error params:{}", JacksonHelper.serialize(newBrands), e);
        }
    }


    private void getHead(String startTime, String endTime, List<NewCarLaunchHead> newCarLaunchHeadList , List<AllNewBrands> newBrandsList, String type) {
        if (newBrandsList != null) {
            NewCarLaunchHead newCarLaunchHead = new NewCarLaunchHead();
            List<NewCarLaunchHead.level> levelidList = new ArrayList<>();
            //全部
            NewCarLaunchHead.level level1 = new NewCarLaunchHead.level();
            level1.setLevelid(1);
            level1.setLevelname("全部");
            levelidList.add(level1);
            //新能源
            List<Integer> seriesidList = newBrandsList.stream().map(AllNewBrands::getSeriesid).collect(Collectors.toList());
            List<CarMainresult> xingnengyuan = new ArrayList<>();
            if (seriesidList.size() < 50){
                String seriesids = seriesidList.stream().map(String::valueOf).collect(Collectors.joining(","));
                List<CarMainresult> carMainresultList = getcarmain(seriesids);
                if (!CollectionUtils.isEmpty(carMainresultList)){
                    xingnengyuan = carMainresultList.stream().filter(f -> f.getNewenergy() == 1).collect(Collectors.toList());
                }
            }else {
                for (int i = 0; i < seriesidList.size() / 50; i++) {
                    List<Integer> seriesidSubList = seriesidList.subList(i * 50, (i + 1) * 50);
                    List<CarMainresult> carMainresults = getcarmain(seriesidSubList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                    if (!CollectionUtils.isEmpty(carMainresults)){
                        xingnengyuan.addAll(carMainresults.stream().filter(f -> f.getNewenergy() == 1).collect(Collectors.toList()));
                    }
                }
                if (seriesidList.size() % 50 > 0){
                    List<Integer> seriesidSubList = seriesidList.subList((seriesidList.size() / 50) * 50,seriesidList.size());
                    List<CarMainresult> carMainresults = getcarmain(seriesidSubList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                    if (!CollectionUtils.isEmpty(carMainresults)){
                        xingnengyuan.addAll(carMainresults.stream().filter(f -> f.getNewenergy() == 1).collect(Collectors.toList()));
                    }
                }
            }
            if (!CollectionUtils.isEmpty(xingnengyuan)){
                NewCarLaunchHead.level level2 = new NewCarLaunchHead.level();
                level2.setLevelid(2);
                level2.setLevelname("新能源");
                levelidList.add(level2);
            }
            //轿车
            List<AllNewBrands> jiaoche = newBrandsList.stream().filter(f -> f.getLevelid() == 1 || f.getLevelid() == 2 || f.getLevelid() == 3 || f.getLevelid() == 4 || f.getLevelid() == 5 || f.getLevelid() == 6).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(jiaoche)){
                NewCarLaunchHead.level level3 = new NewCarLaunchHead.level();
                level3.setLevelid(3);
                level3.setLevelname("轿车");
                levelidList.add(level3);
            }
            //SUV
            List<AllNewBrands> SUV = newBrandsList.stream().filter(f -> f.getLevelid() == 16 || f.getLevelid() == 17 || f.getLevelid() == 18 || f.getLevelid() == 19 || f.getLevelid() == 20 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(SUV)){
                NewCarLaunchHead.level level4 = new NewCarLaunchHead.level();
                level4.setLevelid(4);
                level4.setLevelname("SUV");
                levelidList.add(level4);
            }
            //MPV
            List<AllNewBrands> MPV = newBrandsList.stream().filter(f -> f.getLevelid() == 21 || f.getLevelid() == 22 || f.getLevelid() == 23 || f.getLevelid() == 24 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(MPV)){
                NewCarLaunchHead.level level5 = new NewCarLaunchHead.level();
                level5.setLevelid(5);
                level5.setLevelname("MPV");
                levelidList.add(level5);
            }
            //跑车
            List<AllNewBrands> paoche = newBrandsList.stream().filter(f -> f.getLevelid() == 7 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(paoche)){
                NewCarLaunchHead.level level6 = new NewCarLaunchHead.level();
                level6.setLevelid(6);
                level6.setLevelname("跑车");
                levelidList.add(level6);
            }
            // 微面
            List<AllNewBrands> weimian = newBrandsList.stream().filter(f -> f.getLevelid() == 11 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(weimian)){
                NewCarLaunchHead.level level7 = new NewCarLaunchHead.level();
                level7.setLevelid(7);
                level7.setLevelname("微面");
                levelidList.add(level7);
            }
            // 轻客
            List<AllNewBrands> qingke = newBrandsList.stream().filter(f -> f.getLevelid() == 13 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(qingke)){
                NewCarLaunchHead.level level8 = new NewCarLaunchHead.level();
                level8.setLevelid(8);
                level8.setLevelname("轻客");
                levelidList.add(level8);
            }
            // 皮卡
            List<AllNewBrands> pika = newBrandsList.stream().filter(f -> f.getLevelid() == 14 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pika)){
                NewCarLaunchHead.level level9 = new NewCarLaunchHead.level();
                level9.setLevelid(9);
                level9.setLevelname("皮卡");
                levelidList.add(level9);
            }
            // 微卡
            List<AllNewBrands> weika = newBrandsList.stream().filter(f -> f.getLevelid() == 12 ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(weika)){
                NewCarLaunchHead.level level10 = new NewCarLaunchHead.level();
                level10.setLevelid(10);
                level10.setLevelname("微卡");
                levelidList.add(level10);
            }

            if (!StringUtil.isEmpty(type)){
                newCarLaunchHead.setMonth(type);
            }else {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate date = LocalDate.parse(startTime, formatter);
                YearMonth yearMonth = YearMonth.from(date);
                newCarLaunchHead.setMonth(yearMonth.getMonthValue() + "月");
            }
            newCarLaunchHead.setSum(newBrandsList.size());
            newCarLaunchHead.setStartTime(startTime);
            newCarLaunchHead.setEndTime(endTime);
            newCarLaunchHead.setLevelidList(levelidList);
            newCarLaunchHeadList.add(newCarLaunchHead);
        }
    }

    private List<DateDto> getDateList() {
        List<DateDto> dateList = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDateString = today.format(formatter);
        // 计算一年后的日期的前一天
        LocalDate nextYearDate = today.plusYears(1).minusDays(1);
        String nextYearDateString = nextYearDate.format(formatter);
        DateDto dateDto1 = new DateDto();
        dateDto1.setStartTime(currentDateString);
        dateDto1.setEndTime(nextYearDateString);
        dateList.add(dateDto1);

        //本月的开始时间和结束时间
        // 获取本月第一天日期
        LocalDate yearFirstDay = today.with(TemporalAdjusters.firstDayOfMonth());
        String firstDayString = yearFirstDay.format(formatter);
        //如果是当前时间月份, 获取当月1日到当前日期
        String lastDayOfMonthString = DateHelper.serialize(DateHelper.beforeNDaysDate(1), DateHelper.DATEFORMAT_ONLY_DATE); //lastDayOfMonth.format(formatter);
        DateDto dateDto2 = new DateDto();
        dateDto2.setStartTime(firstDayString);
        dateDto2.setEndTime(lastDayOfMonthString);
        dateList.add(dateDto2);

        //前五个月的开始时间和结束时间
        for (int i = 1; i <= 5; i++) {
            int type = i;
            LocalDate firstDayOfLastMonth = today.minusMonths(type).withDayOfMonth(1);
            LocalDate lastDayOfLastMonth = today.minusMonths(type).withDayOfMonth(today.minusMonths(type).lengthOfMonth());
            String firstDay = firstDayOfLastMonth.format(formatter);
            String lastDay = lastDayOfLastMonth.format(formatter);
            DateDto dateDto = new DateDto();
            dateDto.setStartTime(firstDay);
            dateDto.setEndTime(lastDay);
            dateList.add(dateDto);
        }
        return dateList;
    }


    //新车上市
    private ResponseContent<List<AllNewBrands>> getAllNewBrands(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime, formatter).minusDays(1);
        LocalDate endDate = LocalDate.parse(endTime, formatter).plusDays(1);
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "app");
        param.put("startTime", startDate.format(formatter));
        param.put("endTime", endDate.format(formatter));
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/CmsJava/Wcf/CarSerivce.svc/GetAllNewBrandsByTimeNofilter", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        ResponseContent<List<AllNewBrands>> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<List<AllNewBrands>>>() {
        });
        if (responseSpecContent == null || responseSpecContent.getResult() == null) {
            return null;
        }
        return responseSpecContent;
    }

    private List<CarMainresult> getcarmainList(List<Integer> seriesidList) {
        List<CarMainresult> carMainresults = new ArrayList<>();
        if (seriesidList.size() < 50){
            String seriesids = seriesidList.stream().map(String::valueOf).collect(Collectors.joining(","));
            List<CarMainresult> carMainresultList = getcarmain(seriesids);
            return CollectionUtils.isEmpty(carMainresultList) ? carMainresults : carMainresultList;
        }else {
            for (int i = 0; i < seriesidList.size() / 50; i++) {
                List<Integer> seriesidSubList = seriesidList.subList(i * 50, (i + 1) * 50);
                List<CarMainresult> carMainresult = getcarmain(seriesidSubList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                if(!CollectionUtils.isEmpty(carMainresult)){
                    carMainresults.addAll(carMainresult);
                }
            }
            if (seriesidList.size() % 50 > 0){
                List<Integer> seriesidSubList = seriesidList.subList((seriesidList.size() / 50) * 50,seriesidList.size());
                List<CarMainresult> carMainresult = getcarmain(seriesidSubList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                if(!CollectionUtils.isEmpty(carMainresult)){
                    carMainresults.addAll(carMainresult);
                }
            }
        }
        return carMainresults;
    }

    //根据多个车系id获取车系相关参数信息
    private List<CarMainresult> getcarmain(String seriesids) {
        String url = "http://car.api.autohome.com.cn/v1/carprice/series_parambyserieslist.ashx?_appid=car&serieslist=" + seriesids;
        BaseModel<List<CarMainresult>> result = HttpClientUtils.get(url, new TypeReference<BaseModel<List<CarMainresult>>>() {
        });
        if (result == null || result.getReturncode() != 0 || result.getResult().size() < 1) {
            log.warn("getcarmain no data :{},URL:{}", seriesids,url);
            return null;
        }
        return result.getResult();
    }

    //是否可询价状态（接口是否存在车辆信息）
    /**
     * wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=104080024
     * */
    private List<SeriesMinPriceExtendsResult> getSeriesMinPriceExtends(String seriesids) {
        Map<String, Object> param = new HashMap<>();
        String url = "http://dealer.api.lq.autohome.com.cn/statistics/seriesprice/getSeriesMinPriceExtends";
        param.put("_appId", "applet");
//        param.put("cityid", 110100);
        param.put("seriesIds", seriesids);
        param.put("isextends", 1);
        param.put("_entryid", 89);
        param.put("isExtends", 1);
        param.put("_sourceId", "series_minipro");
        param.put("isNeedUnSale", 1);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        ResponseContent<List<SeriesMinPriceExtendsResult>> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<List<SeriesMinPriceExtendsResult>>>() {
        });
        if (responseSpecContent == null || responseSpecContent.getResult() == null) {
            return null;
        }
        return responseSpecContent.getResult();
    }

    //车系全景
    //wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=81001784
    private List<CarVrInfo> getcarvrinfobyseriesid(List<Integer> seriesidList) {
        List<CarVrInfo> carVrInfoList = new ArrayList<>();
        for (Integer seriesid : seriesidList) {
            String carvrinfobyseriesidKey = "applet:api:car:getcarvrinfobyseriesid:v2:"+seriesid;
            String carvrinfobyseriesidStr = redisClient.get(carvrinfobyseriesidKey);
            if(carvrinfobyseriesidStr!=null&&carvrinfobyseriesidStr.length()>0){
                carVrInfoList.add(JacksonHelper.deserialize(carvrinfobyseriesidStr,CarVrInfo.class));
                continue;
            }

            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("category", "car");
            param.put("seriesid", seriesid);
            param.put("vrtype", 2);//查询类型：0全部 1内饰 2外观
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://pano.api.lq.autohome.com.cn/v1/vr/getcarvrinfobyseriesid", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                continue;
            }
            ResponseContent<CarVrInfo> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<CarVrInfo>>() {
            });
            if (responseContent == null || responseContent.getResult() == null) {
                continue;
            }
            redisClient.set(carvrinfobyseriesidKey, JacksonHelper.serialize(responseContent.getResult()), 1, TimeUnit.DAYS);
            carVrInfoList.add(responseContent.getResult());
        }
        return carVrInfoList;
    }

    private List<BrandsTimeModel> convertBrandsTimeModelList(List<AllNewBrands> newBrands){
        if(CollectionUtils.isEmpty(newBrands)){
            return new ArrayList<>();
        }
        List<BrandsTimeModel> brandsTimeModelList = new ArrayList<>();
        //1.取车系的所有时间
        List<String> distinctDateList = newBrands.stream().map(o -> o.getOntimeDateStr(DateHelper.DATEFORMAT_MONTH_DAY)).distinct().collect(Collectors.toList());
        //2.将相同时间的数据归类
        distinctDateList.forEach(d ->{
            List<Integer> seriesIds = new ArrayList<>();
            List<AllNewBrands> tempNewBrands = newBrands.stream().filter(o -> o.getOntimeDateStr(DateHelper.DATEFORMAT_MONTH_DAY).equals(d)).collect(Collectors.toList());
//            BrandsTimeModel brandsTimeModel = new BrandsTimeModel();
            List<BrandsTimeModel.SeriesInfo> seriesInfoList = new ArrayList<>();
            tempNewBrands.forEach(t -> {
                BrandsTimeModel.SeriesInfo seriesInfo = new BrandsTimeModel.SeriesInfo();
                BeanUtil.copyProperties(t, seriesInfo);
                //赋值日期
                seriesInfo.setOntime(t.getOntimeDateStr(DateHelper.DATEFORMAT_ONLY_DATE));
//                if(seriesInfo.isOnmarket()){
//                    seriesIds.add(t.getSeriesid());
//                }
                seriesIds.add(t.getSeriesid());

                seriesInfoList.add(seriesInfo);
            });

            //赋值出厂价格
            List<SeriesBaseDto> seriesList = rankService.getSeriesBaseDtoCache(seriesIds.stream().map(String::valueOf).collect(Collectors.toList()));
            seriesInfoList.forEach(o -> {
                SeriesBaseDto seriesBaseDto = seriesList.stream().filter(s -> s.getSeriesid().equals(o.getSeriesid())).findFirst().orElse(null);
                if(seriesBaseDto != null){
                    o.setMinprice(seriesBaseDto.getMinprice());
                    o.setMaxprice(seriesBaseDto.getMaxprice());
                    o.setSerieswhitelogo(seriesBaseDto.getSeriespnglogo());
                }
            });
            brandsTimeModelList.add(BrandsTimeModel.builder().date(d).serieslist(seriesInfoList).build());
        });
        return brandsTimeModelList;
    }

    private Long computeTime (){
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        long seconds = ChronoUnit.SECONDS.between(LocalDateTime.now(), midnight);
        return seconds;
    }
}

