package com.autohome.applet.service.donate;

import com.autohome.applet.dao.javaapi.model.DonateLog;
import com.autohome.applet.model.dto.donate.DonateIosModel;
import com.autohome.applet.model.dto.donate.DonateQueryModel;
import com.autohome.applet.model.enums.DonateDataSourceEnum;
import com.autohome.applet.model.enums.DonateTerminalEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Math.abs;

@Service
@Slf4j
public class IosDonateClass extends DonateBaseClass {

    private final static Integer TOTALDONATE = 2000;
    //设置分页数据量
    private final static int PAGESIZE = 2000;

    /**
     * 提前将原创和车家号数据存储到物理表中
     * */
    @Override
    public void doBusiness(DonateQueryModel donateQuery) {
        donateQuery.setPageSize(100);
        donateQuery.setDonateTerminalEnum(DonateTerminalEnum.APPLE);
        donateQuery.setTotal(TOTALDONATE);
        donateQuery.setDonateDataSourceEnum(DonateDataSourceEnum.OPGC);
        //先删除当天的捐赠数据, 然后重新查询保存(方便反复执行)
        deleteDonate(donateQuery.getDaily(), donateQuery.getDonateTerminalEnum());
        //先删除当天的捐赠数据, 然后重新查询保存(方便反复执行)
        deleteDonate(donateQuery.getDaily(), donateQuery.getDonateTerminalEnum());
        List<DonateLog> donateLogList = new ArrayList<>();
        //保存原创视频
        fillDonateLogFromCMS(donateQuery, donateLogList);
        //保存原创文章
        fillDonateLogFromVideo(donateQuery, donateLogList);
        //保存车家号
        fillDonateLogFromChejiahao(donateQuery, donateLogList);
        //保存到数据库
        saveDonateLog(donateLogList);
        System.out.println("IosDonateClass doBusiness log");
    }

    public int deleteData30(String endDate){
        return deleteData30(DonateTerminalEnum.APPLE.getCode(), endDate);
    }

    public DonateIosModel getDonateData(String deviceId) {
        DonateIosModel donateIosModel = new DonateIosModel();
        DonateQueryModel donateQuery = new DonateQueryModel();
        donateQuery.setDeviceId(deviceId);
        donateQuery.setDonateTerminalEnum(DonateTerminalEnum.APPLE);
        donateQuery.setTotal(TOTALDONATE);
        int deviceHash = abs(donateQuery.getDeviceId().hashCode());
        donateQuery.setDeviceHash(deviceHash);
        String totalTmp = redisClient.get(getDonateRedisKey(deviceHash, 2));
        int total = StringUtils.isEmpty(totalTmp) ? 0 : Integer.valueOf(totalTmp);
        //判断是否到上限
        if(total >= TOTALDONATE){
            return DonateIosModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
        }
        //设置剩余的需要请求的条数
        donateQuery.setPageSize(PAGESIZE - total);
        //4个数据源,调用4次
        for(int i = 0; i< DonateDataSourceEnum.count() - 1; i++) {

            long startTime = System.currentTimeMillis(); // 获取开始时间
            //填充查询数据
            List<DonateLog> donateLogList = fillDonateData(donateQuery);
            long endTime = System.currentTimeMillis(); // 获取结束时间
            long executionTime = endTime - startTime; // 计算执行时间
//            if(executionTime > 50){
//                log.info("ios-fill-data-{} {}", donateQuery.getDonateDataSourceEnum().getCode(), executionTime);
//            }
            if(!CollectionUtils.isEmpty(donateLogList)){
                donateIosModel.setHasMore(1);//还有数据
                donateIosModel.setList(convertDonateIosItem(donateLogList));
                return donateIosModel;
            }
            if(donateQuery.getDonateDataSourceEnum() == DonateDataSourceEnum.OVER){
                break;
            }
        }
        return DonateIosModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
    }

    public DonateIosModel getDonateDataTest(String deviceId) {
        DonateIosModel donateIosModel = new DonateIosModel();
        DonateQueryModel donateQuery = new DonateQueryModel();
        donateQuery.setDeviceId(deviceId);
        donateQuery.setDonateTerminalEnum(DonateTerminalEnum.APPLE);
        donateQuery.setTotal(TOTALDONATE);
        int deviceHash = abs(donateQuery.getDeviceId().hashCode());
        donateQuery.setDeviceHash(deviceHash);
        String totalTmp = redisClient.get(getDonateRedisKey(deviceHash, 2));
        int total = StringUtils.isEmpty(totalTmp) ? 0 : Integer.valueOf(totalTmp);
        //判断是否到上限
        if(total >= TOTALDONATE){
            return DonateIosModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
        }
        //设置剩余的需要请求的条数
        donateQuery.setPageSize(PAGESIZE - total);
        //4个数据源,调用4次
        for(int i = 0; i< DonateDataSourceEnum.count() - 1; i++) {

            long startTime = System.currentTimeMillis(); // 获取开始时间
            //填充查询数据
            List<DonateLog> donateLogList = fillDonateData(donateQuery);
            long endTime = System.currentTimeMillis(); // 获取结束时间
            long executionTime = endTime - startTime; // 计算执行时间

            if(!CollectionUtils.isEmpty(donateLogList)){
                donateIosModel.setHasMore(1);//还有数据
//                //todo : 此处是测试代码
//                if(donateQuery.getDonateDataSourceEnum() == DonateDataSourceEnum.BEHAVIORHISTORYSERIES){
//                    donateLogList.addAll(fillDataForPart3(donateQuery));
//                }

                donateIosModel.setList(convertDonateIosItem(donateLogList));
                return donateIosModel;
            }
            if(donateQuery.getDonateDataSourceEnum() == DonateDataSourceEnum.OVER){
                break;
            }
        }
        return DonateIosModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
    }

    public DonateIosModel getDonateData(String deviceId, Integer totalDonate, Integer pageSize) {
        DonateIosModel donateIosModel = new DonateIosModel();
        DonateQueryModel donateQuery = new DonateQueryModel();
        donateQuery.setDeviceId(deviceId);
        donateQuery.setDonateTerminalEnum(DonateTerminalEnum.APPLE);
        donateQuery.setTotal(totalDonate);
        int deviceHash = abs(donateQuery.getDeviceId().hashCode());
        donateQuery.setDeviceHash(deviceHash);
        String totalTmp = redisClient.get(getDonateRedisKey(deviceHash, 2));
        int total = StringUtils.isEmpty(totalTmp) ? 0 : Integer.valueOf(totalTmp);
        //判断是否到上限
        if(total >= totalDonate){
            return DonateIosModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
        }
        //设置剩余的需要请求的条数
        donateQuery.setPageSize(Math.abs(totalDonate - total) >= pageSize ? pageSize : Math.abs(totalDonate - total));
        //4个数据源,调用4次
        for(int i = 0; i< DonateDataSourceEnum.count() - 1; i++) {

            long startTime = System.currentTimeMillis(); // 获取开始时间
            //填充查询数据
            List<DonateLog> donateLogList = fillDonateData(donateQuery);
            long endTime = System.currentTimeMillis(); // 获取结束时间
            long executionTime = endTime - startTime; // 计算执行时间
//            if(executionTime > 50){
//                log.info("ios-fill-data-{} {}", donateQuery.getDonateDataSourceEnum().getCode(), executionTime);
//            }
//            //todo : 此处是测试代码
//            if(donateLogList == null){
//                donateLogList = new ArrayList<>();
//                donateLogList.addAll(fillDataForPart3(donateQuery));
//            }
            if(!CollectionUtils.isEmpty(donateLogList)){
                donateIosModel.setHasMore(1);//还有数据
                donateIosModel.setList(convertDonateIosItem(donateLogList));
                return donateIosModel;
            }
            if(donateQuery.getDonateDataSourceEnum() == DonateDataSourceEnum.OVER){
                break;
            }
        }
        return DonateIosModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
    }

    private List<DonateIosModel.DonateIosItem> convertDonateIosItem(List<DonateLog> donateLogList){
        if(!CollectionUtils.isEmpty(donateLogList)){
            return donateLogList.stream().filter(Objects::nonNull).map(o ->{
                DonateIosModel.DonateIosItem donateIosItem = new DonateIosModel.DonateIosItem();
                donateIosItem.setDescription(o.getDescribe() == null ? "" : (o.getDescribe().length() > 40 ? o.getDescribe().substring(0,39) : o.getDescribe()));
                donateIosItem.setTitle(o.getTitle() == null ? "" : (o.getTitle().length() > 30 ? o.getTitle().substring(0,29) : o.getTitle()));
                donateIosItem.setKeyword(StringUtils.isEmpty(o.getKeyWords()) ? Collections.EMPTY_LIST : Arrays.asList(o.getKeyWords().split(",")));
//                donateIosItem.setImageid(o.getLogoUrl());
                donateIosItem.setUrl(o.getDataUrl() + "&sourceid=spotlight&dataSource="+ convertDataSource(o.getDataSource()) +"&viewMark=" + o.getViewMark());
//                donateIosItem.setUniqueldentifier(o.getUniqueldentifier());
//                donateIosItem.setDataSource(convertDataSource(o.getDataSource()));
//                donateIosItem.setViewMark(o.getViewMark());
                return donateIosItem;
            }).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }
}
