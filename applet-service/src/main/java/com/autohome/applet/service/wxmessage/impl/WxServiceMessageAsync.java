package com.autohome.applet.service.wxmessage.impl;

import com.alibaba.fastjson.JSONObject;
import com.autohome.applet.dao.javaapi.model.WxMiniAccount;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.model.dto.wsmessage.SubscriptionRequest;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.javaapi.UserCenterService;
import com.autohome.applet.service.wxmessage.WxMessageService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxServiceMessageAsync {
    @Autowired
    private CarSeriesService carSeriesService;

    @Autowired
    private HttpService httpService;

    @Autowired
    UserCenterService userCenterService;

    @Autowired
    WxMessageService wxMessageService;

    @Value("${third.service.url.sendfuwuhaomsg:\"\"}")
    private String sendfuwuhaomsgUrl;

    /**
     * 异步发送服务号消息
     *
     * @param subscriptionRequest
     * @return
     */
    @Async
    public ReturnValue<?> sendSubscribeMsg(SubscriptionRequest subscriptionRequest, WxMiniAccount exWxMiniAccount) {

        ReturnValue<?> returnValue = null;
        JSONObject param = new JSONObject();
        String seriesName = "";
        try {
            List<SeriesInfoDto> allSeriesInfos = carSeriesService.getAllSeriesInfos();
            Map<Integer, SeriesInfoDto> seriesInfoMap = allSeriesInfos.stream()
                    .collect(Collectors.toMap(SeriesInfoDto::getId, Function.identity()));
            seriesName = seriesInfoMap.get(Integer.parseInt(exWxMiniAccount.getSeriesId())).getName();
        } catch (Exception e) {
            log.error("sendSubscribeMsg 获取车系名称失败{}", e);
        }
        try {
            param.put("openId", subscriptionRequest.getFwhOpenId());
            param.put("tmpId", "jRZi0k_0b5DyEz1E_dL6TorZlrgFF7dNSy-5VWGB2WQ");
            JSONObject data = new JSONObject();
            String first = String.format("您关注的%s车主价已更新，快去看看吧", seriesName);
            String dateTime = DateHelper.getTodayString();
            JSONObject value1 = new JSONObject();
            value1.put("value", dateTime);
            JSONObject value2 = new JSONObject();
            value2.put("value", first);
            data.put("keyword1", value1);
            data.put("keyword2", value2);
            param.put("data", data);
            returnValue = httpService.httpPostJsonFor(sendfuwuhaomsgUrl, param.toJSONString(), new TypeReference<ReturnValue<?>>() {
            });
            log.info("发送服务号接口报错 源接口调用返回:{} , url:{} , 参数:{} ", returnValue, sendfuwuhaomsgUrl, JacksonHelper.serialize(param));
            return returnValue;
        } catch (Exception e) {
            log.error("发送服务号接口报错 源接口调用报错:{} , url:{} , 参数:{}  error", returnValue, sendfuwuhaomsgUrl, JacksonHelper.serialize(param), e);

            return ReturnValue.buildErrorResult(-1, "发送失败");
        }

    }


}
