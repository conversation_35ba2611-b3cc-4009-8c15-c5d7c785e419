package com.autohome.applet.service.impl;
import com.autohome.applet.model.dto.carlibrary.SpecDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.SeriesClassPictureDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.Api8Dto;
import com.autohome.applet.model.dto.netcoreapi.series.*;
import com.autohome.applet.model.dto.qqupload.PicInfoUploadDtoV3.Pic;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.DealerService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.FctApiService;
import com.autohome.applet.service.javaapi.CarLibraryService;
import com.autohome.applet.model.dto.qqupload.PicInfoUploadDtoV3.Display;

import com.autohome.applet.model.dto.qqupload.SpecInfoUploadDtoV3.Display.CarInfo;
import com.autohome.applet.model.dto.newenergy.SeriesConfigInfoResult;
import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.carlibrary.carapi.out.RankListpageResult;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarVrInfo2;

import com.autohome.applet.dao.javaapi.mapper.BrandSeriesUploadQqLogMapper;
import com.autohome.applet.dao.javaapi.mapper.BrandSeriesUploadQqMapper;
import com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQq;
import com.autohome.applet.dao.javaapi.model.BrandSeriesUploadQqLog;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.carlibrary.carapi.out.CarApiBrandOut;
import com.autohome.applet.model.dto.dealer.DealerFilterUrlResult;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsResult;
import com.autohome.applet.model.dto.qqupload.*;
import com.autohome.applet.model.lightapp.DimSeriesPRCTypesDto;
import com.autohome.applet.model.lightapp.PageKBOf;
import com.autohome.applet.model.lightapp.WordOfMouthDto;
import com.autohome.applet.service.DealerDataToQqService;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.lightapp.MiscsService;
import com.autohome.applet.service.netcoreapi.SeriesService;
import com.autohome.applet.service.newenergy.CarOwnerRealEnduranceV2Service;
import com.autohome.applet.service.openapi.VRInfoService;
import com.autohome.applet.util.*;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.autohome.applet.util.qqupload.Crypto;
import com.fasterxml.jackson.core.type.TypeReference;
import com.autohome.applet.service.javaapi.CarService;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DealerDataToQqServiceImpl implements DealerDataToQqService {

    private final static String FORWARDBASEURL = "/view/transferpage/index?auto_open_from=outside_replace&id={0}";
    private final static String FORWARDBASEH5URL = "https://quickapp-h5.autohome.com.cn/transferpage?auto_open_from=outside_replace&id={0}";
    private final static String QQFORWARDBASEURL = "qb://wxapp/?appid=wx84dd50b996d3d862&username=gh_bfcd071dba17&originid=gh_bfcd071dba17&path={0}";
    //生成环境地址
//    private final static String UPLOADQQURL = "https://kdsearch.qq.com/data_access/push?appid={appid}&timestamp={timestamp}&nonce={nonce}&sign={sign}";
    //测试环境地址
//    private final static String UPLOADQQURL = "https://xsearch.qq.com/data_access/push_test?appid={appid}&timestamp={timestamp}&nonce={nonce}&sign={sign}";

    @Value("${qq.upload.url:\"\"}")
    private  String UPLOADQQURL = "";
    // 品牌卡类型f
    private final static Integer BRANDTYPE = 1000;
    // 车系卡类型
    private final static Integer SERIESTYPE = 1002;

    private final static Integer PINGJIATYPE=1005;

    private final static Integer IMGTYPE =1006;

    //v3版本 品牌数据推送
    private final static Integer BRANDTYPEV3 = 1003;
    // v3版本 车系数据推送
    private final static Integer SERIESTYPEV3 = 1004;

    @Value("${car.api.getallseries}")
    private String getAllSeriesUrl;
    @Value("${dealer.api.getfilters}")
    private String getDealerFiltersUrl;
    @Value("${dealer.api.getseriesminpriceextends}")
    private String getSeriesMinPriceExtendsUrl;
    @Value("${dealer.api.getdealerlistseriesnew}")
    private String GetDealerListSeriesNewUrl;

    @Autowired
    private HttpService httpService;

    @Autowired
    private BrandSeriesUploadQqMapper brandSeriesUploadQqMapper;
    @Autowired
    private SeriesService seriesService;

    @Autowired
    private BrandSeriesUploadQqLogMapper brandSeriesUploadQqLogMapper;

    @Autowired
    private CarService carService;

    @Autowired
    private VRInfoService vrInfoService;
    @Autowired
    private CarOwnerRealEnduranceV2Service carOwnerRealEnduranceV2Service;

    @Autowired
    private MiscsService miscsService;

    @Autowired
    private FctApiService fctApiService;

    @Autowired
    private CarApiService carApiService;

    @Autowired
    private CarLibraryService carLibraryService;

    @Autowired
    private DealerService dealerService;

    @Autowired
    private CarSeriesService carSeriesService;

    /**
     * 需求文档
     * https://doc.autohome.com.cn/docapi/page/share/share_qNQesyCDXk
     * */

    @Override
    public void uploadBrandToQQ(BrandForQQUtil.BrandForQQ brandInfo, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList) {
        //遍历城市
        cityItemList.forEach(c -> {
            uploadBrandToQQ(brandInfo, c, seriesInfoDtoList);
        });
    }

    @Override
    public void uploadBrandToQQV2(BrandForQQUtil.BrandForQQ brandInfo, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList) {
        //遍历brandInfo的citycodelist
        if(!CollectionUtils.isEmpty(brandInfo.getCityCodeList())){
            brandInfo.getCityCodeList().forEach(obj -> {
                AllCitiesUtil.CityItem c = cityItemList.stream().filter(t -> t.getCityId().equals(obj)).findFirst().orElse(null);
                if(c != null){
                    uploadBrandToQQ(brandInfo, c, seriesInfoDtoList);
                }
            });
        }
    }
    @Override
    public void uploadBrandToQQV3(AllBrandInfoDto.BrandInfoDto brandInfo, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList, SeriesInfoBrandDto seriesInfoBrandParam, boolean isDel) {
        //遍历所有城市 cityItemList
        cityItemList.forEach(obj ->{
            uploadBrandToQQ3(brandInfo,obj, seriesInfoDtoList,seriesInfoBrandParam, isDel);
        });
    }

    @Override
    public void uploadBrandToQQ(BrandForQQUtil.BrandForQQ brandInfo, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList) {
        //查询有报价的车系 wiki:http://wiki.corpautohome.com/pages/viewpage.action?pageId=28739202
        //如果没有,直接跳出
        List<Integer> tempSeriesIdList = getDealerFilters(cityItem.getCityId(), brandInfo.getBrandId());
        if(CollectionUtils.isEmpty(tempSeriesIdList)){
            log.info("uploadBrandToQQ do not upload，没有报价车系, brandInfo:{}, cityItem:{}", JacksonHelper.serialize(brandInfo), JacksonHelper.serialize(cityItem));

            return;
        }
        //取tempSeriesIdList与seriesInfoDtoList的交集, 作为推送的数据
        List<SeriesInfoDto> intersection = seriesInfoDtoList.stream().filter(s -> tempSeriesIdList.contains(s.getId())).collect(Collectors.toList());
        //需求规定, 车系小于等于1 不同步
        if(CollectionUtils.isEmpty(intersection) || intersection.size() <= 3) {
            log.info("uploadBrandToQQ do not upload，有报价的在售车系小于等于3, brandInfo:{}, cityItem:{}", JacksonHelper.serialize(brandInfo), JacksonHelper.serialize(cityItem));
            return;
        }
        //汇总车系级
        Map<Integer, String> tabMap = intersection.stream().collect(Collectors.toMap(
                SeriesInfoDto::getLevelid,
                SeriesInfoDto::getLevelname,
                (oldValue, newValue) -> newValue));
        //存储推送对象表BrandSeriesUploadQq
        BrandSeriesUploadQq brandSeriesUploadQq = getBrandSeriesUploadQq(brandInfo, cityItem);
        brandSeriesUploadQqMapper.save(brandSeriesUploadQq);
        //组装对象
        BrandInfoUploadDto brandInfoDto = assembleQqBrandInfo(brandInfo, cityItem, tabMap, intersection);
        //推送
        uploadToQQ(brandInfoDto,brandSeriesUploadQq, cityItem);
    }


    public void uploadBrandToQQ3( AllBrandInfoDto.BrandInfoDto brandInfo, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList,SeriesInfoBrandDto seriesInfoBrandParam, boolean isDel) {
        //查询有报价的车系 wiki:http://wiki.corpautohome.com/pages/viewpage.action?pageId=28739202
        //如果没有,直接跳出
        List<Integer> tempSeriesIdList = getDealerFilters(cityItem.getCityId(), brandInfo.getId()+"");
        if(CollectionUtils.isEmpty(tempSeriesIdList)){
            log.info("uploadBrandToQQ do not upload，没有报价车系, brandInfo:{}, cityItem:{}", JacksonHelper.serialize(brandInfo), JacksonHelper.serialize(cityItem));
            return;
        }
        //取tempSeriesIdList与seriesInfoDtoList的交集, 作为推送的数据
        List<SeriesInfoDto> intersection = seriesInfoDtoList.stream().filter(s -> tempSeriesIdList.contains(s.getId())).collect(Collectors.toList());
        //需求规定, 车系小于等于1 不同步
        if(CollectionUtils.isEmpty(intersection) || intersection.size() <= 3) {
            log.info("uploadBrandToQQ do not upload，有报价的在售车系小于等于3, brandInfo:{}, cityItem:{}", JacksonHelper.serialize(brandInfo), JacksonHelper.serialize(cityItem));
            return;
        }
//        //汇总车系级
//        Map<Integer, String> tabMap = intersection.stream().collect(Collectors.toMap(
//                SeriesInfoDto::getLevelid,
//                SeriesInfoDto::getLevelname,
//                (oldValue, newValue) -> newValue));

        String carlevel = getCarlevel(intersection);

        //按照车系维度推送数据
        for (SeriesInfoDto seriesInfoDto : intersection) {
//            if(!seriesInfoDto.getName().equals("大众ID.3")){
//                continue;
//            }

            //存储推送对象表BrandSeriesUploadQq
            BrandSeriesUploadQq brandSeriesUploadQq = getBrandSeriesUploadQqV3(brandInfo, cityItem, seriesInfoDto);
            brandSeriesUploadQqMapper.save(brandSeriesUploadQq);
            //组装对象
            BrandInfoUploadDtoV3 brandInfoDto3 = assembleQqBrandInfo3(brandInfo, cityItem, carlevel, seriesInfoDto, seriesInfoBrandParam);
//            log.info("车系数据：{}", JacksonHelper.serialize(brandInfoDto3));
            //推送
            uploadToQQV3(brandInfoDto3,brandSeriesUploadQq, cityItem, isDel);
        }

    }

    /**
     * 获取车系级别
     * @param intersection
     * @return
     */
    public String getCarlevel(List<SeriesInfoDto> intersection){
        //汇总车系级
        List<String> levelnames = intersection.stream()
                .map(SeriesInfoDto::getLevelname)
                .collect(Collectors.toList());
        Map<String, List<String>> classification = new HashMap<>();

        classification.put("轿车", Arrays.asList("微型车", "小型车", "紧凑型车", "中型车", "中大型车", "大型车"));
        classification.put("SUV", Arrays.asList("小型SUV", "紧凑型SUV", "中型SUV", "中大型SUV", "大型SUV"));
        classification.put("MPV", Arrays.asList("紧凑型MPV", "中型MPV", "中大型MPV", "大型MPV"));

        Set<String> newLevelNames = levelnames.stream()
                .map(levelname -> {
                    for (Map.Entry<String, List<String>> entry : classification.entrySet()) {
                        if (entry.getValue().contains(levelname)) {
                            return entry.getKey();
                        }
                    }
                    return levelname;
                })
                .collect(Collectors.toSet());

        String result = String.join("; ", newLevelNames);

        return result;
    }

    @Override
    public void uploadSeriesToQQ(SeriesForQQUtil.SeriesForQQ seriesForQQ, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList) {
        //遍历城市
        cityItemList.forEach(c -> {
            uploadSeriesToQQ(seriesForQQ, c, seriesInfoDtoList);
        });
    }

    @Override
    public void uploadSeriesToQQV2(SeriesForQQUtil.SeriesForQQ seriesForQQ, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList) {
        //遍历brandInfo的citycodelist
        if(!CollectionUtils.isEmpty(seriesForQQ.getCityCodeList())){
            seriesForQQ.getCityCodeList().forEach(obj -> {
                AllCitiesUtil.CityItem c = cityItemList.stream().filter(t -> t.getCityId().equals(obj)).findFirst().orElse(null);
                if(c != null){
                    uploadSeriesToQQ(seriesForQQ, c, seriesInfoDtoList);
                }
            });
        }
    }

    @Override
    public void uploadSpecToQQV3(SeriesForQQUtil.SeriesForQQ seriesForQQ, List<AllCitiesUtil.CityItem> cityItemList, SpecParamDto specParamDto, boolean isDel) {

        //车系下所有车型配置信息
        SeriesConfigInfoResult seriesConfigInfoResult = carOwnerRealEnduranceV2Service.getConfigFromCarServicesBySeriesId(Integer.parseInt(seriesForQQ.getSeriesId()));
        specParamDto.setSeriesConfigInfoResult(seriesConfigInfoResult);
        //获取车系下最热车型
        Integer hotSpecid = seriesService.getSpecid(seriesForQQ.getSeriesId());
        specParamDto.setHotSpecid(hotSpecid);

        cityItemList.forEach(obj -> {

            uploadSpecToQQ(seriesForQQ, obj, specParamDto, isDel);

        });

    }


    @Override
    public void uploadPicToQQV3(SeriesForQQUtil.SeriesForQQ seriesForQQ, SpecParamDto specParamDto, boolean isDel) {

        //车系下所有车型配置信息
        SeriesConfigInfoResult seriesConfigInfoResult = carOwnerRealEnduranceV2Service.getConfigFromCarServicesBySeriesId(Integer.parseInt(seriesForQQ.getSeriesId()));
        specParamDto.setSeriesConfigInfoResult(seriesConfigInfoResult);
        //获取车系下最热车型
        ReturnValue<List<SeriesRate>> hotSpecidList = seriesService.getHotSpecidList(seriesForQQ.getSeriesId());
        specParamDto.setHotSpecs(hotSpecidList.getResult());

        //根据车系id获取各个类型前五张图片 外观、中空、座椅、其他
        ResponseContent<SeriesClassPictureDto> pic5 =(ResponseContent< SeriesClassPictureDto >) carApiService.series_classpicturebyseriesIdRetAll("car", Integer.parseInt(seriesForQQ.getSeriesId()));
        specParamDto.setTypeitems(pic5.getResult().getTypeitems());
        //获取该车系下所有车型信息
        SpecDto specsBySeriesId = carLibraryService.getSpecsBySeriesId(Integer.parseInt( seriesForQQ.getSeriesId() ));
        specParamDto.setSpecsBySeriesId(specsBySeriesId);

        //车系实拍首图,一次获取10张
        List<Api8Dto.Picitems> api8 = fctApiService.getApi_8(seriesForQQ.getSeriesId());
        specParamDto.setApi8(api8);

        //此处原来是按照多城市进行循环调用的，目前qq没有城市信息，所以无需循环
        uploadPicToQQ(seriesForQQ, specParamDto, isDel);
    }

    @Override
    public void uploadSeriesToQQ(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList) {
        //组装对象
        SeriesInfoDto seriesInfoDto = seriesInfoDtoList.stream().filter(s -> s.getId().equals(Integer.valueOf(seriesInfo.getSeriesId()))).findFirst().orElse(null);
        if(seriesInfoDto == null){
            log.warn("assembleQqSeriesInfo no series, {}", JacksonHelper.serialize(seriesInfo));
            return;
        }

        //查询车系详情, 获取对应品牌
        List<CarApiBrandOut> carApiBrandOutList = carService.getBrandInfos(Arrays.asList(seriesInfoDto.getBrandid()));
        if(CollectionUtils.isEmpty(carApiBrandOutList)){
            log.error("assembleQqSeriesInfo no brand, {}", JacksonHelper.serialize(seriesInfo));
            return;
        }
        CarApiBrandOut carApiBrandOut = carApiBrandOutList.get(0);

        SeriesInfoUploadDto seriesInfoUploadDto = assembleQqSeriesInfo(seriesInfo, cityItem, seriesInfoDto, carApiBrandOut);
        if(seriesInfoUploadDto == null){
            log.warn("uploadSeriesToQQ do not upload, seriesInfoUploadDto is null, seriesInfo:{}, cityItem:{}", JacksonHelper.serialize(seriesInfo), JacksonHelper.serialize(cityItem));
            return;
        }
        //存储推送对象表BrandSeriesUploadQq
        BrandSeriesUploadQq brandSeriesUploadQq = getBrandSeriesUploadQq(seriesInfo, cityItem, carApiBrandOut);
        brandSeriesUploadQqMapper.save(brandSeriesUploadQq);
        //推送
        uploadToQQ(seriesInfoUploadDto,brandSeriesUploadQq, cityItem);
    }

    /**
     * 车型维度上传数据
     * @param seriesInfo
     * @param cityItem
     */
    @Override
    public void uploadSpecToQQ(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem,  SpecParamDto specParamDto, boolean isDel) {
        //当前车系是否是在售或停产在售车系
        SeriesInfoDto seriesInfoDto = specParamDto.getSeriesInfoDtoList().stream().filter(s -> s.getId().equals(Integer.valueOf(seriesInfo.getSeriesId()))).findFirst().orElse(null);
        if(seriesInfoDto == null){
            log.warn("assembleQqSeriesInfo no series, {}", JacksonHelper.serialize(seriesInfo));
            return;
        }

        specParamDto.setSeriesInfoDto(seriesInfoDto);

        //获取底价
        SeriesMinPriceExtendsResult seriesMinPriceExtendsResult = getSeriesMinPriceExtends(cityItem.getCityId(), seriesInfoDto.getId());
        if(seriesMinPriceExtendsResult == null){
            log.warn("assembleQqSeriesInfo assembleQqSeriesInfo 没有报价,不需要推送, seriesInfo:{}, cityItem:{}", JacksonHelper.serialize(seriesInfo), JacksonHelper.serialize(cityItem));
        }
        specParamDto.setSeriesMinPriceExtendsResult(seriesMinPriceExtendsResult);

        List<Integer> allSpecBySeries = getAllSpecBySeries(specParamDto.getSeriesConfigInfoResult());

        specParamDto.setSpecStatusMap(getSpecStatus(specParamDto.getSeriesConfigInfoResult()));

        for (Integer specifySpec : allSpecBySeries) {
            specParamDto.setSpecId(specifySpec);
            SpecInfoUploadDtoV3 specInfoUploadDtoV3 = assembleQqSpecInfo(seriesInfo, cityItem, specParamDto);
            if(specInfoUploadDtoV3 == null){
                log.warn("uploadSeriesToQQ do not upload, seriesInfoUploadDto is null, seriesInfo:{}, cityItem:{}", JacksonHelper.serialize(seriesInfo), JacksonHelper.serialize(cityItem));
                return;
            }
            //存储推送对象表BrandSeriesUploadQq
            BrandSeriesUploadQq brandSeriesUploadQq = getBrandSeriesUploadQqV3(seriesInfo, cityItem, specParamDto);
            brandSeriesUploadQqMapper.save(brandSeriesUploadQq);
            //推送
            uploadSpecToQQ(specInfoUploadDtoV3,brandSeriesUploadQq, cityItem, isDel);

        }
    }


    /**
     * 图片维度上传数据
     * @param seriesInfo

     */
    @Override
    public void uploadPicToQQ(SeriesForQQUtil.SeriesForQQ seriesInfo, SpecParamDto specParamDto, boolean isDel) {

        //车系下所有车型配置信息
        SeriesConfigInfoResult seriesConfigInfoResult = carOwnerRealEnduranceV2Service.getConfigFromCarServicesBySeriesId(Integer.parseInt(seriesInfo.getSeriesId()));
        specParamDto.setSeriesConfigInfoResult(seriesConfigInfoResult);

        //当前车系是否是在售或停产在售车系
        SeriesInfoDto seriesInfoDto = specParamDto.getSeriesInfoDtoList().stream().filter(s -> s.getId().equals(Integer.valueOf(seriesInfo.getSeriesId()))).findFirst().orElse(null);
        if(seriesInfoDto == null){
            log.warn("assembleQqSeriesInfo no series, {}", JacksonHelper.serialize(seriesInfo));
            return;
        }
        specParamDto.setSeriesInfoDto(seriesInfoDto);
//
//        List<Integer> allSpecBySeries = getAllSpecBySeries(specParamDto.getSeriesConfigInfoResult());
//
//        String specIds = allSpecBySeries.stream().map(Object::toString).collect(Collectors.joining(", "));

        //进行pic数据组装
        PicInfoUploadDtoV3 picInfoUploadDtoV3 = assembleQqPicInfo(seriesInfo, specParamDto);
        if(picInfoUploadDtoV3 == null){
            log.warn("uploadPicToQQ do not upload, seriesInfoUploadDto is null, seriesInfo:{}", JacksonHelper.serialize(seriesInfo));
            return;
        }
        //存储推送对象表BrandSeriesUploadQq
        BrandSeriesUploadQq brandSeriesUploadQq = gePicSeriesUploadQqV3(seriesInfo, specParamDto);
        brandSeriesUploadQqMapper.save(brandSeriesUploadQq);
        //推送
        uploadPicToQQ(picInfoUploadDtoV3, brandSeriesUploadQq, null, isDel);
    }

    /**
     * 返回车型+车型状态
     * @param seriesConfigInfoResult
     * @return
     */
    public Map<Integer,Integer> getSpecStatus(SeriesConfigInfoResult seriesConfigInfoResult){
        Map<Integer,Integer> mapStatus = new HashMap<>();
        if (seriesConfigInfoResult.getSpecList() != null) {
            for (Object obj : seriesConfigInfoResult.getSpecList()) {
                if (obj instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    Integer specState = (Integer) map.get("specstate");
                    if (specState != null && (specState == 20 || specState == 30)) {
                        Integer specId = (Integer) map.get("specid");
                        if (specId != null) {
                            mapStatus.put(specId,specState);
                        }
                    }
                }
            }
        }
        return mapStatus;
    }
    public List<Integer> getAllSpecBySeries(SeriesConfigInfoResult seriesConfigInfoResult){
        List<Integer> specIds = new ArrayList<>();
        if (seriesConfigInfoResult.getSpecList() != null) {
            for (Object obj : seriesConfigInfoResult.getSpecList()) {
                if (obj instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    Integer specState = (Integer) map.get("specstate");
                    if (specState != null && (specState == 20 || specState == 30)) {
                        Integer specId = (Integer) map.get("specid");
                        if (specId != null) {
                            specIds.add(specId);
                        }
                    }
                }
            }
        }
        return specIds;
    }

    @Override
    public List<SeriesInfoDto> getSeriesInfoDtoList(){
        Map<String, Object> param = new HashMap<>();
        try {
            AllSeriesInfoDto allSeriesInfoDtoReturnValue = httpService.httpGetForReturnValue(getAllSeriesUrl, param
                    , new TypeReference<ReturnValue<AllSeriesInfoDto>>() {
                    },10 * 1000);
            if(allSeriesInfoDtoReturnValue == null){
                return null;
            }
            List<SeriesInfoDto> seriesInfoDtoList = allSeriesInfoDtoReturnValue.getSeriesitems();
            return seriesInfoDtoList.stream().filter(s -> s.getState() == 20 || s.getState() == 30).collect(Collectors.toList());
        } catch (BusinessException e) {
            log.error("getSeriesInfoDtoList", e);
        }
        return null;
    }

    @Override
    public List<SeriesInfoDto> getAllSeriesInfoDtoList(){
        Map<String, Object> param = new HashMap<>();
        try {
            AllSeriesInfoDto allSeriesInfoDtoReturnValue = httpService.httpGetForReturnValue(getAllSeriesUrl, param
                    , new TypeReference<ReturnValue<AllSeriesInfoDto>>() {
                    });
            if(allSeriesInfoDtoReturnValue == null){
                return null;
            }
            List<SeriesInfoDto> seriesInfoDtoList = allSeriesInfoDtoReturnValue.getSeriesitems();
            return seriesInfoDtoList;
        } catch (BusinessException e) {
            log.error("getAllSeriesInfoDtoList", e);
        }
        return null;
    }

    /**
     * 获取该区县,或者城市,或者省份,维度下该品牌的所有经销商报价的厂商和车系列表
     * return: 车系id列表
     * */
    @Override
    public List<Integer> getDealerFilters(String cityCode, String brandId){
        Map<String, Object> param = new HashMap<>();
        try {
            DealerFilterUrlResult dealerFilterUrlResult = httpService.httpGetForReturnValue(
                    getDealerFiltersUrl.replace("{cid}", cityCode).replace("{brandid}", brandId), param
                    , new TypeReference<ReturnValue<DealerFilterUrlResult>>() {
                    });
            if(dealerFilterUrlResult == null){
                return null;
            }
            List<Integer> seriesIdList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(dealerFilterUrlResult.getList())){
                dealerFilterUrlResult.getList().forEach(d ->{
                    if(!CollectionUtils.isEmpty(d.getManufactoryList())){
                        d.getManufactoryList().forEach(f ->{
                            if(!CollectionUtils.isEmpty(f.getSeries())){
                                seriesIdList.addAll(f.getSeries().stream().map(DealerFilterUrlResult.Serie::getSeriesId).collect(Collectors.toList()));
                            }
                        });
                    }
                });
            }
            return seriesIdList;
        } catch (BusinessException e) {
            log.error("getDealerFilters", e);
        }
        return null;
    }

    /**
     * 获取该区县,或者城市,或者省份的底价经销商, 只能传一个车系id
     * params:
     *  saleScope 0：为车商汇销售状态；；；1：产品库在售状态
     * return: 最低价格的经销商信息
     * */
    @Override
    public SeriesMinPriceExtendsResult getSeriesMinPriceExtends(String cityCode, Integer seriesId){
        Map<String, Object> param = new HashMap<>();
        try {
            String url = getSeriesMinPriceExtendsUrl.replace("{cid}", cityCode).replace("{seriesId}", String.valueOf(seriesId));
            List<SeriesMinPriceExtendsResult> seriesMinPriceExtendsResults = httpService.httpGetForReturnValue(url
                    , param
                    , new TypeReference<ReturnValue<List<SeriesMinPriceExtendsResult>>>() {
                    });
            if(CollectionUtils.isEmpty(seriesMinPriceExtendsResults)){
                return null;
            }
            SeriesMinPriceExtendsResult seriesMinPriceExtendsResult = seriesMinPriceExtendsResults.stream().filter(s -> seriesId.equals(s.getSeriesId())).findFirst().orElse(null);
            return seriesMinPriceExtendsResult;
        } catch (BusinessException e) {
            log.error("getSeriesMinPriceExtends", e);
        }
        return null;
    }

    /**
     * 获取该区县,或者城市,或者省份的底价经销商, 只能传一个车系id
     * params:
     *  saleScope 0：为车商汇销售状态；；；1：产品库在售状态
     * return: 最低价格的经销商信息
     * */
    @Override
    public List<SeriesMinPriceExtendsResult> getSeriesMinPriceExtends(String cityCode, List<Integer> seriesIdList) {
        Map<String, Object> param = new HashMap<>();
        try {
            String url = getSeriesMinPriceExtendsUrl.replace("{cid}", cityCode).replace("{seriesId}", Joiner.on(",").join(seriesIdList));
            List<SeriesMinPriceExtendsResult> seriesMinPriceExtendsResults = httpService.httpGetForReturnValue(url
                    , param
                    , new TypeReference<ReturnValue<List<SeriesMinPriceExtendsResult>>>() {
                    });
            if (CollectionUtils.isEmpty(seriesMinPriceExtendsResults)) {
                return null;
            }
            return seriesMinPriceExtendsResults;
        } catch (BusinessException e) {
            log.error("getSeriesMinPriceExtends", e);
        }
        return null;
    }

    @Override
    public void uploadEvaluateCardToQQ(SeriesForQQUtil.SeriesForQQ customSeriesItem,
                                       List<SeriesInfoDto> allSeriesInfoDtoList,SpecParamDto specParamDto, boolean isDel) {
        //组装数据
        //需要品牌 , 车系 , 口碑, 车系满意度标签
        SeriesInfoDto seriesInfoDto = allSeriesInfoDtoList.stream().filter(
                s -> s.getId().equals(
                        Integer.parseInt(customSeriesItem.getSeriesId())))
                                                          .findFirst().orElse(null);

        if (seriesInfoDto == null) {
            log.error("uploadEvaluateCardToQQ 不存在该车系, {}",JacksonHelper.serialize(seriesInfoDto));
            return;
        }

        //获取品牌信息
        List<CarApiBrandOut> carApiBrandOutList = carService.getBrandInfos(Arrays.asList(seriesInfoDto.getBrandid()));

        if(CollectionUtils.isEmpty(carApiBrandOutList)){
            log.error("uploadEvaluateCardToQQ 不存在的品牌, {}", JacksonHelper.serialize(customSeriesItem));
            return;
        }

        CarApiBrandOut carApiBrandOut = carApiBrandOutList.get(0);

        //评价卡片不需要城市数据
        AllCitiesUtil.CityItem cityItem = new AllCitiesUtil.CityItem();
        cityItem.setCityId("0");
        cityItem.setCityName("");
        cityItem.setProvinceId("0");

        //组装要推送的数据
        EvaluateCardSeriesInfoUploadDTO evaluateCardSeriesInfoUploadDTO = this
                .assembleEvaluateCardSeriesInfo(customSeriesItem, cityItem, seriesInfoDto, carApiBrandOut,specParamDto);

        if(evaluateCardSeriesInfoUploadDTO == null){
            log.warn("uploadEvaluateCardToQQ do not upload, evaluateCardSeriesInfoUploadDTO is null, seriesInfo:{}, cityItem:{}", JacksonHelper.serialize(customSeriesItem), JacksonHelper.serialize(cityItem));
            return;
        }
        log.info("评价卡输出信息：{}",JacksonHelper.serialize(evaluateCardSeriesInfoUploadDTO));

        BrandSeriesUploadQq brandSeriesUploadQq = getBrandSeriesUploadQqPingJia(customSeriesItem, cityItem, carApiBrandOut);
        brandSeriesUploadQqMapper.save(brandSeriesUploadQq);

        //推送
        uploadEvaluateToQQ(evaluateCardSeriesInfoUploadDTO,brandSeriesUploadQq, null, isDel);

    }

    private BrandInfoUploadDto assembleQqBrandInfo(BrandForQQUtil.BrandForQQ brandInfo, AllCitiesUtil.CityItem cityItem, Map<Integer, String> tabMap, List<SeriesInfoDto> seriesInfoDtoList){
        BrandInfoUploadDto brandInfoDto = new BrandInfoUploadDto();
        brandInfoDto.setId(SecurityKit.encrypt(brandInfo.getBrandId()) + "_" + cityItem.getCityId());
        brandInfoDto.setQuerys(brandInfo.getQuery());
        brandInfoDto.setType(BRANDTYPE);//固定制 1000
        brandInfoDto.setCity_code(cityItem.getCityId());
        String tmpUrl = "/car-package/pages/brand/index?auto_open_from=qb_channel_a&id=${{0}}".replace("{0}", SecurityKit.encrypt(brandInfo.getBrandId()));
        String miniUrl = getEncodeUrl(tmpUrl, true);
        List<BrandInfoUploadDto.UrlEntity> urls = new ArrayList<BrandInfoUploadDto.UrlEntity>() {{
            add(new BrandInfoUploadDto.UrlEntity(miniUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new BrandInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};
        brandInfoDto.setHeader(new BrandInfoUploadDto.Header(brandInfo.getBrandName() + " 汽车车系-汽车之家"
                , miniUrl
                , "为您提供及时、全面、准确的价格信息"
                , urls));

        List<BrandInfoUploadDto.Tab> tabs = new ArrayList<>();

        //插入全部车系
        tabs.add(assembleTab(0, "全部", brandInfo, cityItem, seriesInfoDtoList));

        int i = 1;
        for (Map.Entry<Integer, String> entry : tabMap.entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();
            if(i > 7){
                log.warn("assembleQqBrandInfo out 7个, brandInfo:{}, cityItem:{}, tapL{}"
                        , JacksonHelper.serialize(brandInfo)
                        , JacksonHelper.serialize(cityItem)
                        , JacksonHelper.serialize(tabMap) );
                break;
            }
            tabs.add(assembleTab(key, value, brandInfo, cityItem, seriesInfoDtoList));
            i++;
        }

        brandInfoDto.setTabs(tabs);

        return brandInfoDto;
    }

    /**
     * 获取热度
     * @param seriesId
     * @param rank
     * @return
     */
    private String getRank(int seriesId,RankListpageResult rank) {

        //销量排行热度值
        Map<String, String> map = new HashMap<>();

        if (rank.getResult() != null && rank.getResult().getList() != null) {
            map = rank.getResult().getList().stream()
                    .filter(item -> item.getRightinfo() != null) // 过滤rightinfo为null的项
                    .collect(Collectors.toMap(
                            RankListpageResult.ListItem::getSeriesid, // 获取seriesid作为key
                            item -> item.getRightinfo().getRighttextone() // 获取righttextone作为value
                    ));
        }
        if(map.containsKey(seriesId+"")){
            String s = map.get(seriesId + "");
            if(s!=null){
                return "1";
            }else{
               return s;
            }
        }

        return "1";
    }

    private BrandInfoUploadDtoV3 assembleQqBrandInfo3( AllBrandInfoDto.BrandInfoDto brandInfo, AllCitiesUtil.CityItem cityItem,
                                                       String carlevel, SeriesInfoDto seriesInfoDto,
                                                       SeriesInfoBrandDto seriesInfoBrandParam){

        BrandInfoUploadDtoV3 item = new BrandInfoUploadDtoV3();
        CarSeriesInfoDTO carSeriesInfoDTO = seriesInfoBrandParam.carSeriesInfoMap.get(seriesInfoDto.getId());
        String fctName = carSeriesInfoDTO != null ? carSeriesInfoDTO.getFctname() : "";
        if(StringUtils.isEmpty(fctName)){
            List<FctItemListDto.FctItem> fctItemList = carSeriesService.getAllFctInfos();
            FctItemListDto.FctItem fctItem = fctItemList.stream().filter(o -> o.getId() == seriesInfoDto.getFctid()).findFirst().orElse(null);
            if(fctItem != null && StringUtil.isNotEmpty(fctItem.getName())){
                fctName = fctItem.getName();
            }
        }
        String enSeriesId= SecurityKit.encrypt(seriesInfoDto.getId());
        BrandInfoUploadDtoV3.Display display = new BrandInfoUploadDtoV3.Display();
//        display.setBuildyear("");
//        display.setZongbuadd("");
        display.setTitle(brandInfo.getName());

//        "/car-package/pages/series/index?auto_open_from=qb_channel_b&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(s.getId()));
        display.setUrl(getEncodeUrl("/car-package/pages/brand/index?auto_open_from=qb_search_brand&linkid=4|127|1837|0|206388|306020&id=${{0}}".replace( "{0}", SecurityKit.encrypt(brandInfo.getId())),true));//品牌车系列表页URL
        display.setBrand(brandInfo.getName());
        display.setNation(brandInfo.getCountry());
        display.setOnsaleseries(seriesInfoBrandParam.carSeriesInfoMap.size()+"");
        display.setCarSeries(seriesInfoDto.getName());
        display.setCarSerieshot(getRank(seriesInfoDto.getId(),seriesInfoBrandParam.getRank()));
        display.setSubBrand(fctName);
        display.setCarLevel(getLevelname(seriesInfoDto.getLevelname()));
//        display.setCarLevel(seriesInfoDto.getLevelname());
        display.setCarLevelID(getLevelId(display.getCarLevel(),seriesInfoDto.getLevelid()+""));
//        display.setCarLevelID(seriesInfoDto.getLevelid()+"");
        display.setCarlevellist(carlevel);
        display.setGuidedPrice(CarPriceUtils.getStrPrice(seriesInfoDto.getMinprice(), seriesInfoDto.getMaxprice()));
        display.setWx_logo(brandInfo.getLogo());
        display.setWx_carPic(seriesInfoDto.getLogo());
        display.setWx_carurl(getEncodeUrl("/car-package/pages/series/index?auto_open_from=qb_search_series&linkid=4|127|1837|0|206389|306020&seriesid=${{0}}".replace( "{0}",enSeriesId),true));
        //vr 信息
        Set<Integer> seriesIdsSet = seriesInfoBrandParam.getSeriesIdsSet();
        boolean isvr = seriesIdsSet.contains(seriesInfoDto.getId());

        display.setVrviewID(isvr?"1":"0");
        display.setWx_vrviewurl(isvr?getEncodeUrl("/package/pages/h5/index/index?auto_open_from=qb_search_vr&url="+getVrUrl(seriesInfoDto.getId()),true):"");
        display.setMinpriceurl(getEncodeUrl("/package2/tools/final-price/index?auto_open_from=qb_search_final&linkid=4|127|1837|0|206390|306020&seriesid=${{0}}".replace( "{0}",enSeriesId),true));
//        Changshanglist changshanglist = new Changshanglist();
//        changshanglist.setChangshangname("");
//        changshanglist.setChangshangpic("");
//        display.setChangshanglist(changshanglist);
//        display.setSummaryUrl("");
//        display.setArticleListUrl("");
        item.setKey(SecurityKit.encrypt(seriesInfoDto.getName()+cityItem.getCityName())); //车系名称+城市，做到唯一标识
        item.setDisplay(display);
        item.setLocation(getLocation(cityItem));

        return item;
    }
    public String getLocation(AllCitiesUtil.CityItem cityItem){
        String retLocation = "";
        if(cityItem.getCityName().contains("北京")||cityItem.getCityName().contains("上海")||cityItem.getCityName().contains("天津")||cityItem.getCityName().contains("重庆")){
            retLocation = cityItem.getCityName();
        }else{
            retLocation =  cityItem.getProvinceName() +"    "+cityItem.getCityName();
        }

        return retLocation;
    }


    public String getLevelname(String levelname) {
        Map<String, List<String>> classification = new HashMap<>();
        classification.put("轿车", Arrays.asList("微型车", "小型车", "紧凑型车", "中型车", "中大型车", "大型车"));
        classification.put("SUV", Arrays.asList("小型SUV", "紧凑型SUV", "中型SUV", "中大型SUV", "大型SUV"));
        classification.put("MPV", Arrays.asList("紧凑型MPV", "中型MPV", "中大型MPV", "大型MPV"));

        for (Map.Entry<String, List<String>> entry : classification.entrySet()) {
            if (entry.getValue().contains(levelname)) {
                return entry.getKey();
            }
        }
        return levelname;
    }
    public String getLevelId(String levelname,String  levelid) {
        Map<String,String> classification = new HashMap<>();
        classification.put("轿车", "1");
        classification.put("SUV", "2");
        classification.put("MPV", "3");
        if (classification.containsKey(levelname)) {
            return classification.get(levelname);
        }
        return levelid;
    }

    /**
     * 返回一个外观vr,需要encode
     * @param seriesid
     * @return
     */
    private String getVrUrl( int seriesid) {
        CarVrInfo2 car = vrInfoService.getcarvrinfobyseriesid("car", seriesid);
        List<CarVrInfo2.Extinfo> extinfo = car.getExtinfo().stream().filter(e -> e.getShowurl()!=null).collect(Collectors.toList());
        return URLEncoder.encode(extinfo.get(0).getShowurl()+"&pagesrc=miniprogram");
    }



    private BrandInfoUploadDto.Tab assembleTab(Integer levelId, String levelName, BrandForQQUtil.BrandForQQ brandInfo, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList){
        Integer key = levelId;
        String value = levelName;
        BrandInfoUploadDto.Tab tab = new BrandInfoUploadDto.Tab();
        tab.setTitle(value);

        List<BrandInfoUploadDto.Item> itemList = new ArrayList<>();
        List<SeriesInfoDto> tmpSeriesInfoDtoList = null;
        if(levelId == 0){
            //全部车系
            tmpSeriesInfoDtoList = seriesInfoDtoList;
        }
        else{
            tmpSeriesInfoDtoList = seriesInfoDtoList.stream().filter(s -> key.equals(s.getLevelid())).collect(Collectors.toList());
        }
        tmpSeriesInfoDtoList.forEach(s ->{
            BrandInfoUploadDto.Item item = new BrandInfoUploadDto.Item();
            item.setImage_url(s.getSeriespnglogo().replace("autohomecar__", "200x200_f40_autohomecar__"));//白底图
            String tmpUrl = "/car-package/pages/series/index?auto_open_from=qb_channel_b&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(s.getId()));
            String miniUrl = getEncodeUrl(tmpUrl, true);
            List<BrandInfoUploadDto.UrlEntity> urls = new ArrayList<BrandInfoUploadDto.UrlEntity>() {{
                add(new BrandInfoUploadDto.UrlEntity(miniUrl, QQUrlTypeEnum.MINIURL.getCode()));
                add(new BrandInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl), QQUrlTypeEnum.H5URL.getCode()));
            }};
            item.setUrl(miniUrl);
            item.setUrls(urls);
            item.setMain_text(s.getName());

            //获取底价
            SeriesMinPriceExtendsResult seriesMinPriceExtendsResult = getSeriesMinPriceExtends(cityItem.getCityId(), s.getId());
            if(seriesMinPriceExtendsResult != null) {
                item.setSub_text(CarPriceUtils.getStrPrice(seriesMinPriceExtendsResult.getNewsPrice()));
            }
            else{
                item.setSub_text("暂无报价");
            }
            //赋值link
            BrandInfoUploadDto.Link link = new BrandInfoUploadDto.Link();
            link.setText("查成交价");
            String tmpUrl1 = "/package2/tools/final-price/index?auto_open_from=qb_channel_f&linkid=4|127|1837|0|206091|305463&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(s.getId()));
            String miniUrl1 = getEncodeUrl(tmpUrl1, true);
            List<BrandInfoUploadDto.UrlEntity> urls1 = new ArrayList<BrandInfoUploadDto.UrlEntity>() {{
                add(new BrandInfoUploadDto.UrlEntity(miniUrl1, QQUrlTypeEnum.MINIURL.getCode()));
                add(new BrandInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl1), QQUrlTypeEnum.H5URL.getCode()));
            }};
            link.setUrl(miniUrl1);
            link.setUrls(urls1);
            item.setLink(link);

            item.setScale_type(1);//传1比1

            itemList.add(item);
        });
        tab.setItems(itemList);
        String tmpUrl = "/car-package/pages/brand/index?auto_open_from=qb_channel_a&id=${{0}}".replace("{0}", SecurityKit.encrypt(brandInfo.getBrandId()));
        String miniUrl = getEncodeUrl(tmpUrl, true);
        List<BrandInfoUploadDto.UrlEntity> urls = new ArrayList<BrandInfoUploadDto.UrlEntity>() {{
            add(new BrandInfoUploadDto.UrlEntity(miniUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new BrandInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};
        tab.setMore(new BrandInfoUploadDto.Link("查看更多"
                , miniUrl, urls));

        return tab;
    }

    private SeriesInfoUploadDto assembleQqSeriesInfo(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem, SeriesInfoDto seriesInfoDto, CarApiBrandOut carApiBrandOut){

        SeriesInfoUploadDto seriesInfoUploadDto = new SeriesInfoUploadDto();
        seriesInfoUploadDto.setId(SecurityKit.encrypt(seriesInfo.getSeriesId()) + "_" + cityItem.getCityId());
        seriesInfoUploadDto.setQuerys(seriesInfo.getQuery());
        seriesInfoUploadDto.setType(SERIESTYPE);//固定制 1001
        seriesInfoUploadDto.setCity_code(cityItem.getCityId());

        String tmpUrl = "/car-package/pages/brand/index?auto_open_from=qb_channel_a&id=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getBrandid()));
        String miniUrl = getEncodeUrl(tmpUrl, true);
        List<SeriesInfoUploadDto.UrlEntity> urls = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};

        seriesInfoUploadDto.setHeader(new SeriesInfoUploadDto.Header(carApiBrandOut.getName() + " 汽车车系-汽车之家"
                , miniUrl
                , "为您提供及时、全面、准确的价格信息"
                , urls));



        SeriesInfoUploadDto.Item item = new SeriesInfoUploadDto.Item();
        item.setImage_url(seriesInfoDto.getSeriespnglogo().replace("autohomecar__", "300x200_f40_autohomecar__"));

        //获取底价
        SeriesMinPriceExtendsResult seriesMinPriceExtendsResult = getSeriesMinPriceExtends(cityItem.getCityId(), seriesInfoDto.getId());
        if(seriesMinPriceExtendsResult == null){
            log.warn("assembleQqSeriesInfo assembleQqSeriesInfo 没有报价,不需要推送, seriesInfo:{}, cityItem:{}", JacksonHelper.serialize(seriesInfo), JacksonHelper.serialize(cityItem));
            return null;
        }

        String tmpUrl2 = "/car-package/pages/series/index?auto_open_from=qb_channel_b&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getId()));
        String miniUrl2 = getEncodeUrl(tmpUrl2, true);
        List<SeriesInfoUploadDto.UrlEntity> urls2 = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniUrl2, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl2), QQUrlTypeEnum.H5URL.getCode()));
        }};
        item.setUrl(miniUrl2);
        item.setUrls(urls2);
        item.setMain_text("厂商指导价：" + CarPriceUtils.getStrPrice(seriesInfoDto.getMinprice(), seriesInfoDto.getMaxprice()));
        item.setAccent_text(CarPriceUtils.getStrPrice(seriesMinPriceExtendsResult.getNewsPrice()));
        item.setSub_text("经销商报价：");
        item.setScale_type(1);//传1比1

        String tmpUrl1 = "/car-package/pages/series/index?auto_open_from=qb_channel_b&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getId()));
        String miniUrl1 = getEncodeUrl(tmpUrl1, true);
        List<SeriesInfoUploadDto.UrlEntity> urls1 = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniUrl1, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpUrl1), QQUrlTypeEnum.H5URL.getCode()));
        }};

        //赋值link
        item.setLink(new SeriesInfoUploadDto.Link("免费查成交价"
                , miniUrl1
                , urls1));

        seriesInfoUploadDto.setItem(item);

        //赋值links
        List<SeriesInfoUploadDto.Link> linkList = new ArrayList<>();
        String tmpSpecUrl = "/car-package/pages/series/index?auto_open_from=qb_channel_b&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getId()));
        String miniSpecUrl = getEncodeUrl(tmpSpecUrl, true);
        List<SeriesInfoUploadDto.UrlEntity> specUrls = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniSpecUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpSpecUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};
        linkList.add(new SeriesInfoUploadDto.Link("车型大全", miniSpecUrl, specUrls));

        String tmpParamUrl = "/car-package/pages/series-config/index?auto_open_from=qb_channel_c&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getId()));
        String miniParamUrl = getEncodeUrl(tmpParamUrl, true);
        List<SeriesInfoUploadDto.UrlEntity> paramUrls = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniParamUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpParamUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};
        linkList.add(new SeriesInfoUploadDto.Link("参数配置", miniParamUrl, paramUrls));

        String tmpPicUrl = "/car-package/pages/pic/list/index?auto_open_from=qb_channel_d&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getId()));
        String miniPicUrl = getEncodeUrl(tmpPicUrl, true);
        List<SeriesInfoUploadDto.UrlEntity> picUrls = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniPicUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpPicUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};
        linkList.add(new SeriesInfoUploadDto.Link("实拍图片", miniPicUrl, picUrls));

        String tmpPriceUrl = "/package2/pages/price/index?auto_open_from=qb_channel_e&seriesid=${{0}}".replace("{0}", SecurityKit.encrypt(seriesInfoDto.getId()));
        String miniPriceUrl = getEncodeUrl(tmpPriceUrl, true);
        List<SeriesInfoUploadDto.UrlEntity> priceUrls = new ArrayList<SeriesInfoUploadDto.UrlEntity>() {{
            add(new SeriesInfoUploadDto.UrlEntity(miniPriceUrl, QQUrlTypeEnum.MINIURL.getCode()));
            add(new SeriesInfoUploadDto.UrlEntity(getH5EncodeUrl(tmpPriceUrl), QQUrlTypeEnum.H5URL.getCode()));
        }};
        linkList.add(new SeriesInfoUploadDto.Link("车主价格", miniPriceUrl, priceUrls));

        seriesInfoUploadDto.setLinks(linkList);

        return seriesInfoUploadDto;
    }


    /**
     * 组装车型上报数据
     * @param seriesInfo
     * @param cityItem
     * @return
     */
    private SpecInfoUploadDtoV3 assembleQqSpecInfo(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem, SpecParamDto specParamDto ){

        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = specParamDto.getCarSeriesInfoMap();
        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoMap.get(Integer.parseInt(seriesInfo.getSeriesId()));


        String encryptSeriesId = SecurityKit.encrypt(specParamDto.getSeriesInfoDto().getId());
        String encryptSpecId = SecurityKit.encrypt(specParamDto.getSpecId());
        SpecInfoUploadDtoV3 specInfoUploadDtoV3 = new SpecInfoUploadDtoV3();
        //车型信息
        CarInfo carInfo = new CarInfo();
        carInfo.setCarName(getName(specParamDto.getSeriesConfigInfoResult(), specParamDto.getSpecId()));
        carInfo.setCarUrl(getEncodeUrl("/car-package/pages/spec/index?auto_open_from=qb_search_spec&linkid=4|127|1837|0|206392|306020&specid=${{0}}".replace( "{0}",encryptSpecId), true));


        Map<Integer, Integer> specStatusMap = specParamDto.getSpecStatusMap();
        Integer i = specStatusMap.get(specParamDto.getSpecId());

        carInfo.setSaleState( i==20?"在售":"停产在售");
        carInfo.setCarmguidedPrice(getZhiDaoJia(specParamDto.getSeriesConfigInfoResult(), specParamDto.getSpecId())); //在售状态下车型的指导价
        carInfo.setCarPailiang(getPaiLiang(specParamDto.getSeriesConfigInfoResult(),specParamDto.getSpecId()));
        carInfo.setNiankuan("");//TODO 从哪个接口获取？
        carInfo.setBanxing("未知");
        carInfo.setCarJiegou(getJieGou(specParamDto.getSeriesConfigInfoResult(), specParamDto.getSpecId()));
        carInfo.setChekuanxinghao(carInfo.getCarName());
//        carInfo.setColor("");
//        carInfo.setCarHot("");
        carInfo.setCarMinPriceURL(getEncodeUrl("/package2/tools/final-price/index?auto_open_from=qb_search_final&linkid=4|127|1837|0|206390|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId)+"&specid=${{0}}".replace( "{0}",encryptSpecId),true));
//        carInfo.setSeat("");
//        carInfo.setOil("");
//        carInfo.setDoor("");

        //车系信息
        SpecInfoUploadDtoV3.Display display = new SpecInfoUploadDtoV3.Display();
        display.setTitle(carSeriesInfoDTO.getBrandname()+seriesInfo.getSeriesName() +"_汽车之家");
        display.setUrl(getEncodeUrl("/car-package/pages/series/index?auto_open_from=qb_search_series&linkid=4|127|1837|0|206389|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId),true));
        display.setName(seriesInfo.getSeriesName());
        display.setByname(seriesInfo.getAlias()==null?"":seriesInfo.getAlias());
        display.setBrand(carSeriesInfoDTO.getBrandname());
        display.setSubBrand(carSeriesInfoDTO.getFctname());
//        display.setSimplename("");

        display.setQuotedPrice(CarPriceUtils.getStrPrice(specParamDto.getSeriesMinPriceExtendsResult()==null?0:specParamDto.getSeriesMinPriceExtendsResult().getNewsPrice()));
        display.setGuidedPrice(CarPriceUtils.getStrPrice(specParamDto.getSeriesInfoDto().getMinprice(), specParamDto.getSeriesInfoDto().getMaxprice()));
        display.setCarspailiang(getPaiLiang(specParamDto.getSeriesConfigInfoResult()));
        display.setOilWear(getOil(specParamDto.getSeriesConfigInfoResult()));
        display.setPeizhiLink(getEncodeUrl("/car-package/pages/series-config/index?auto_open_from=qb_search_series_config&linkid=4|127|1837|0|206391|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId),true));
        display.setBaojiaLink(getEncodeUrl("/car-package/pages/spec/index?auto_open_from=qb_search_spec&linkid=4|127|1837|0|206392|306020&specid=${{0}}".replace( "{0}",SecurityKit.encrypt(specParamDto.getHotSpecid())),true));//当前车系最热车型的车型综述页url
        display.setLuntanurl("");
        display.setJibie(specParamDto.getSeriesInfoDto().getLevelid()+"");
//        display.setLeixing("");
        display.setCarInfo(carInfo);
        display.setHot("");// TODO
//        display.setChandi("");
        display.setJiegou(getJieGouAll(specParamDto.getSeriesConfigInfoResult()));
        display.setPlace("全国");
        display.setShowurl("汽车之家");
        display.setMore("更多车型");
        display.setMorelink(getEncodeUrl("/car-package/pages/series/index?auto_open_from=qb_search_series&linkid=4|127|1837|0|206389|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId),true));
//        display.setEnterprise("");
//        display.setEn_place("");
//        display.setArticleListUrl("");
//        display.setXundijiaLink("");
//        display.setVideo_miaodong("");
//        display.setVideo_instruction("");
        display.setPicLink(getEncodeUrl("/car-package/pages/pic/list/index?auto_open_from=qb_search_series_pics&linkid=4|127|1837|0|206396|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId),true));

        specInfoUploadDtoV3.setKey(SecurityKit.encrypt(specParamDto.getSpecId() + "_" + cityItem.getCityId())); //车型+城市
        specInfoUploadDtoV3.setDisplay(display);
        specInfoUploadDtoV3.setLocation(getLocation(cityItem));

//        log.info("组装后数据：{}",JacksonHelper.serialize(specInfoUploadDtoV3));

        return specInfoUploadDtoV3;
    }


    /**
     * 组装车型上报数据
     * @param seriesInfo
     * @return
     */
    private PicInfoUploadDtoV3 assembleQqPicInfo(SeriesForQQUtil.SeriesForQQ seriesInfo, SpecParamDto specParamDto ){

        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = specParamDto.getCarSeriesInfoMap();
        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoMap.get(Integer.parseInt(seriesInfo.getSeriesId()));
        //获取第一张图
        Api8Dto.Picitems picitems = specParamDto.getApi8().get(0);

        PicInfoUploadDtoV3 picInfoUploadDtoV3 = new PicInfoUploadDtoV3();
        picInfoUploadDtoV3.setKey(SecurityKit.encrypt(seriesInfo.getSeriesId()) );

        Display display =new Display();

//        display.setSimplename("");
        display.setTitle(seriesInfo.getSeriesName()+"图片");
        display.setUrl(getEncodeUrl("/car-package/pages/pic/list/index?auto_open_from=qb_search_series_pics&linkid=4|127|1837|0|206396|306020&seriesid=${{0}}".replace( "{0}",SecurityKit.encrypt(seriesInfo.getSeriesId())),true));
        display.setBrand(carSeriesInfoDTO.getBrandname());
        display.setSubBrand(carSeriesInfoDTO.getFctname());
        display.setCarSeries(carSeriesInfoDTO.getName());

        //1. 同类热销车型列表
        List<PicInfoUploadDtoV3.Car> samelist = new ArrayList<>();
        Pic pic = new Pic();
        List<PicInfoUploadDtoV3.PicList> piclist  = new ArrayList<>();
        pic.setPiclist(setPiclist(piclist,specParamDto,seriesInfo.getSeriesId()));

        display.setPic(pic);
        display.setSamelist(setSamelist(samelist,specParamDto,seriesInfo));
        picInfoUploadDtoV3.setDisplay(display);

        log.info("pic 组装后数据：{}",JacksonHelper.serialize(picInfoUploadDtoV3));

        return picInfoUploadDtoV3;
    }
    private List<PicInfoUploadDtoV3.PicList> setPiclist(List<PicInfoUploadDtoV3.PicList> piclist,  SpecParamDto specParamDto , String seriesId){
        // 将车系的现有图片分类归类为：外观、中空、座椅、其他
        List<SeriesClassPictureDto.Typeitems> collect = specParamDto.getTypeitems().stream().filter(e -> e.getName().contains("外观") || e.getName().contains("中控") || e.getName().contains("座椅") || e.getName().contains("其它")).collect(Collectors.toList());

        for (SeriesClassPictureDto.Typeitems typeitems : collect) {

            PicInfoUploadDtoV3.PicList pics = new PicInfoUploadDtoV3.PicList();
            pics.setPcurl("");
            pics.setPicname(typeitems.getName());
            pics.setWapurl(getEncodeUrl("/car-package/pages/pic/list/index?auto_open_from=qb_search_series_pics&linkid=4|127|1837|0|206396|306020&seriesid=${{0}}".replace( "{0}",SecurityKit.encrypt(seriesId)),true));
            List<PicInfoUploadDtoV3.ValueList> valuelist = new ArrayList<>();
            for (SeriesClassPictureDto.Picitems picitem : typeitems.getPicitems()) {

                // 当前车系图片分类前10张实拍图地址
                PicInfoUploadDtoV3.ValueList value = new PicInfoUploadDtoV3.ValueList();
                value.setValue(picitem.getFilepath());
//                value.setValuelink("");
                valuelist.add(value);
            }
            pics.setValuelist(valuelist);
            pics.setPicnum(valuelist.size());
            piclist.add(pics);
        }


        return piclist;
    }


    private PicInfoUploadDtoV3.Samelist setSamelist(List<PicInfoUploadDtoV3.Car> samelist,  SpecParamDto specParamDto ,SeriesForQQUtil.SeriesForQQ seriesInfo){

        PicInfoUploadDtoV3.Samelist samelist1 = new PicInfoUploadDtoV3.Samelist();
        //车型基本信息
        List<SpecDto.Specitems> specitems = specParamDto.getSpecsBySeriesId().getSpecitems();
        Map<Integer, SpecDto.Specitems> specInfoMap = specitems.stream()
                .collect(Collectors.toMap(SpecDto.Specitems::getId, Function.identity(), (oldValue, newValue) -> oldValue));

        List<SeriesRateSpecattentions> specattentions = specParamDto.getHotSpecs().get(0).getSpecattentions();

        List<Api8Dto.Picitems> api8 = specParamDto.getApi8();
        Map<Integer, Api8Dto.Picitems> collect = api8.stream().collect(Collectors.toMap(Api8Dto.Picitems::getSpecid, Function.identity(), (oldValue, newValue) -> oldValue));

        int i = 0;
        for (SeriesRateSpecattentions seriesRateSpecattentions : specattentions) {
            //遍历下面所有车型，最多10个
            if(i>9){
                continue;
            }

            SpecDto.Specitems specitems2 = specInfoMap.get(seriesRateSpecattentions.getSpecid());

            if(specitems2==null){
                continue;
            }
            PicInfoUploadDtoV3.Car car  = new PicInfoUploadDtoV3.Car();
            car.setCarpcurl("");
            car.setCarname(specitems2.getName());
            car.setCarpic(specitems2.getLogo());
            car.setCarprice(specitems2.getDynamicprice().replace("指导价:",""));
            car.setCarurl(getEncodeUrl("/car-package/pages/pic/list/index?auto_open_from=qb_search_spec_pics&linkid=4|127|1837|0|206397|306020&seriesid=${{0}}".replace( "{0}",SecurityKit.encrypt(seriesInfo.getSeriesId()))+"&specid=${{0}}".replace( "{0}", SecurityKit.encrypt(specitems2.getId())),true));
            i++;
            samelist.add(car);
        }
        if(samelist.size()<1){
            return null;
        }
        samelist1.setCar(samelist);

        return samelist1;

    }

    private String getPaiLiang( SeriesConfigInfoResult seriesConfigInfoResult,int specId){
        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramtypeitems = seriesConfigInfoResult.getParamtypeitems();
        Optional<String> value = paramtypeitems.stream()
                .filter(item -> "发动机".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "排量(L)".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .filter(val -> val.getSpecid().equals(specId))
                .map(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO::getValue)
                .findFirst();
//        log.info("name= {}",value.get());

        return value.orElse("0")+"L";
    }
    private String getPaiLiang( SeriesConfigInfoResult seriesConfigInfoResult){
        String pailiang = seriesConfigInfoResult.getParamtypeitems().stream()
                .filter(item -> "发动机".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "排量(L)".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .map(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO::getValue)
                .distinct()
                .collect(Collectors.joining("L "));
//        log.info("---排量：{},  {}  ---------排量",pailiang,JacksonHelper.serialize(seriesConfigInfoResult));
        if(!pailiang.endsWith("L")){
            pailiang = pailiang+"L";
        }
        return pailiang;
    }
    private String getOil( SeriesConfigInfoResult seriesConfigInfoResult){
        String oil = "";
        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramtypeitems = seriesConfigInfoResult.getParamtypeitems();

        Optional<Double> min = paramtypeitems.stream()
                .filter(item -> "基本参数".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "WLTC综合油耗(L/100km)".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .map(value -> NumberUtils.toDouble(value.getValue(),0d))
                .min(Comparator.naturalOrder());

        Optional<Double> max = paramtypeitems.stream()
                .filter(item -> "基本参数".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "WLTC综合油耗(L/100km)".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .map(value -> NumberUtils.toDouble(value.getValue(),0d))
                .max(Comparator.naturalOrder());

        oil = min.orElse(0d)+"L-"+max.orElse(0d)+"L";
        return oil;
    }

    private String getName( SeriesConfigInfoResult seriesConfigInfoResult,int specId){

        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramtypeitems = seriesConfigInfoResult.getParamtypeitems();

        Optional<String> value = paramtypeitems.stream()
                .filter(item -> "基本参数".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "车型名称".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .filter(val -> val.getSpecid().equals(specId))
                .map(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO::getValue)
                .findFirst();
//        log.info("name= {}",value.get());

        return value.get();
    }


    private String getZhiDaoJia( SeriesConfigInfoResult seriesConfigInfoResult,int specId){

        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramtypeitems = seriesConfigInfoResult.getParamtypeitems();

        Optional<String> value = paramtypeitems.stream()
                .filter(item -> "基本参数".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "厂商指导价(元)".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .filter(val -> val.getSpecid().equals(specId))
                .map(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO::getValue)
                .findFirst();

        return value.get();
    }

    private String getJieGou( SeriesConfigInfoResult seriesConfigInfoResult,int specId){

        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramtypeitems = seriesConfigInfoResult.getParamtypeitems();

        Optional<String> value = paramtypeitems.stream()
                .filter(item -> "基本参数".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "车身结构".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .filter(val -> val.getSpecid().equals(specId))
                .map(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO::getValue)
                .findFirst();

        return value.get();
    }


    private String getJieGouAll( SeriesConfigInfoResult seriesConfigInfoResult){

        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramtypeitems = seriesConfigInfoResult.getParamtypeitems();

        String jiegou  = seriesConfigInfoResult.getParamtypeitems().stream()
                .filter(item -> "基本参数".equals(item.getName()))
                .flatMap(item -> item.getParamitems().stream())
                .filter(param -> "车身结构".equals(param.getName()))
                .flatMap(param -> param.getValueitems().stream())
                .map(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO::getValue)
                .distinct()
                .collect(Collectors.joining(";"));
//        log.info("---排量：{},  {}  ---------排量",pailiang,JacksonHelper.serialize(seriesConfigInfoResult));

        return jiegou;
    }
    private String getEncodeUrl(String url, boolean needForward){
        try {
            if (needForward) {
                return QQFORWARDBASEURL.replace("{0}", URLEncoder.encode(FORWARDBASEURL.replace("{0}", URLEncoder.encode(url, "utf-8")), "utf-8"));
            }
            else{
                return QQFORWARDBASEURL.replace("{0}", URLEncoder.encode(url, "utf-8"));
            }
        }
        catch(Exception ex){
            log.error("encodeurl error, url:{}, needforward:{}", url, needForward);
            return null;
        }
    }

    private String getH5EncodeUrl(String url){
        String H5EncodeUrl = null;
        try {
            H5EncodeUrl = FORWARDBASEH5URL.replace("{0}", URLEncoder.encode(url, "utf-8"));
            return H5EncodeUrl;
        }
        catch(Exception ex){
            log.error("getH5EncodeUrl error, url:{}, H5EncodeUrl:{}", url, H5EncodeUrl);
            return null;
        }
    }

    /**
     * 上报品牌卡
     * */
    private void uploadToQQV3(BrandInfoUploadDtoV3 brandInfoDto,BrandSeriesUploadQq brandSeriesUploadQq, AllCitiesUtil.CityItem cityItem, boolean isDel) {
        List<BrandInfoUploadDtoV3> brandInfoUploadDtos = new ArrayList<>();
        brandInfoUploadDtos.add(brandInfoDto);
        UpdataDataDto.DataList<BrandInfoUploadDtoV3> data = new UpdataDataDto.DataList<>();

        if(!isDel){
            data.setAdd_data_list(brandInfoUploadDtos);
        }
        else{
            data.setDelete_data_list(brandInfoUploadDtos);
        }
        UpdataDataDto updataDataDto = UpdataDataDto.builder()
                .count(1)
                .data_list(data)
                .build();
        //由于两边直辖市城市编码规则不一致, 推送时候,转换直辖市的城市编码
//        brandSeriesUploadQq.setCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode()));
//        brandInfoDto.setCity_code(reChangeCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode())));
        publishToQQ(updataDataDto, Crypto.BRANDAPPID , brandSeriesUploadQq,Crypto.BRANDA_APPKEY);
    }
    /**
     * 上报品牌卡
     * */
    private void uploadToQQ(BrandInfoUploadDto brandInfoDto,BrandSeriesUploadQq brandSeriesUploadQq, AllCitiesUtil.CityItem cityItem) {
        List<BrandInfoUploadDto> brandInfoUploadDtos = new ArrayList<>();
        brandInfoUploadDtos.add(brandInfoDto);
        UpdataDataDto.DataList<BrandInfoUploadDto> data = new UpdataDataDto.DataList<>();

        if(checkAdd(cityItem)){
            data.setAdd_data_list(brandInfoUploadDtos);
        }
        else{
            data.setDelete_data_list(brandInfoUploadDtos);
        }
        UpdataDataDto updataDataDto = UpdataDataDto.builder()
                .count(1)
                .data_list(data)
                .build();
        //由于两边直辖市城市编码规则不一致, 推送时候,转换直辖市的城市编码
        brandSeriesUploadQq.setCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode()));
        brandInfoDto.setCity_code(reChangeCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode())));
        publishToQQ(updataDataDto, Crypto.BRANDAPPID , brandSeriesUploadQq,Crypto.BRANDA_APPKEY);
    }

    /**
     * 上报商品卡
     * */
    private void uploadToQQ(SeriesInfoUploadDto seriesInfoDto,BrandSeriesUploadQq brandSeriesUploadQq, AllCitiesUtil.CityItem cityItem) {
        List<SeriesInfoUploadDto> seriesInfoUploadDtos = new ArrayList<>();
        seriesInfoUploadDtos.add(seriesInfoDto);
        UpdataDataDto.DataList<SeriesInfoUploadDto> data = new UpdataDataDto.DataList<>();

        if(checkAdd(cityItem)){
            data.setAdd_data_list(seriesInfoUploadDtos);
        }
        else{
            data.setDelete_data_list(seriesInfoUploadDtos);
        }
        UpdataDataDto updataDataDto = UpdataDataDto.builder()
                .count(1)
                .data_list(data)
                .build();
        //由于两边直辖市城市编码规则不一致, 推送时候,转换直辖市的城市编码
        brandSeriesUploadQq.setCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode()));
        seriesInfoDto.setCity_code(reChangeCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode())));
        publishToQQ(updataDataDto, Crypto.SERIESAPPID , brandSeriesUploadQq,Crypto.SERIES_APPKEY);
    }

    /**
     * 上报商品卡
     * */
    private void uploadSpecToQQ(SpecInfoUploadDtoV3 seriesInfoDto,BrandSeriesUploadQq brandSeriesUploadQq, AllCitiesUtil.CityItem cityItem, boolean isDel) {
        List<SpecInfoUploadDtoV3> seriesInfoUploadDtos = new ArrayList<>();
        seriesInfoUploadDtos.add(seriesInfoDto);
        UpdataDataDto.DataList<SpecInfoUploadDtoV3> data = new UpdataDataDto.DataList<>();

        if(!isDel){
            data.setAdd_data_list(seriesInfoUploadDtos);
        }
        else{
            data.setDelete_data_list(seriesInfoUploadDtos);
        }
        UpdataDataDto updataDataDto = UpdataDataDto.builder()
                .count(1)
                .data_list(data)
                .build();
        //由于两边直辖市城市编码规则不一致, 推送时候,转换直辖市的城市编码
//        brandSeriesUploadQq.setCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode()));
//        seriesInfoDto.setCity_code(reChangeCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode())));
        publishToQQ(updataDataDto, Crypto.SERIESAPPID , brandSeriesUploadQq,Crypto.SERIES_APPKEY);
    }

    /**
     * 上报评价卡
     * */
    private void uploadEvaluateToQQ(EvaluateCardSeriesInfoUploadDTO evaluateCardSeriesInfoUploadDTO,BrandSeriesUploadQq brandSeriesUploadQq, AllCitiesUtil.CityItem cityItem, boolean isDel) {
        List<EvaluateCardSeriesInfoUploadDTO> picInfoUploadDtos = new ArrayList<>();
        picInfoUploadDtos.add(evaluateCardSeriesInfoUploadDTO);
        UpdataDataDto.DataList<EvaluateCardSeriesInfoUploadDTO> data = new UpdataDataDto.DataList<>();

        if(!isDel){
            data.setAdd_data_list(picInfoUploadDtos);
        }
        else{
            data.setDelete_data_list(picInfoUploadDtos);
        }
        UpdataDataDto updataDataDto = UpdataDataDto.builder()
                .count(1)
                .data_list(data)
                .build();
        //由于两边直辖市城市编码规则不一致, 推送时候,转换直辖市的城市编码
//        brandSeriesUploadQq.setCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode()));
//        seriesInfoDto.setCity_code(reChangeCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode())));
        publishToQQ(updataDataDto, Crypto.EVALUATEAPPID , brandSeriesUploadQq,Crypto.EVALUATEA_APPKEY);
    }
    /**
     * 上报图片卡
     * */
    private void uploadPicToQQ(PicInfoUploadDtoV3 picInfoUploadDtoV3,BrandSeriesUploadQq brandSeriesUploadQq, AllCitiesUtil.CityItem cityItem, boolean isDel) {
        List<PicInfoUploadDtoV3> picInfoUploadDtos = new ArrayList<>();
        picInfoUploadDtos.add(picInfoUploadDtoV3);
        UpdataDataDto.DataList<PicInfoUploadDtoV3> data = new UpdataDataDto.DataList<>();

        if(!isDel){
            data.setAdd_data_list(picInfoUploadDtos);
        }
        else{
            data.setDelete_data_list(picInfoUploadDtos);
        }
        UpdataDataDto updataDataDto = UpdataDataDto.builder()
                .count(1)
                .data_list(data)
                .build();
        //由于两边直辖市城市编码规则不一致, 推送时候,转换直辖市的城市编码
//        brandSeriesUploadQq.setCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode()));
//        seriesInfoDto.setCity_code(reChangeCityCode(reChangeCityCode(brandSeriesUploadQq.getCityCode())));
        publishToQQ(updataDataDto, Crypto.PICAPPID , brandSeriesUploadQq, Crypto.PIC_APPKEY);
    }

    private boolean publishToQQ(UpdataDataDto updataDataDto, String appid , BrandSeriesUploadQq brandSeriesUploadQq,String appKey){
        String timestamp = String.valueOf(DateHelper.getNow().getTime());
        String nonce = String.valueOf(ThreadLocalRandom.current().nextInt(100000,999999));//timestamp.substring(1,7);
        String sign = Crypto.GetPushAppSig(appid, timestamp, nonce, appKey);
        String url = UPLOADQQURL.replace("{appid}", appid).replace("{timestamp}", timestamp). replace("{nonce}", nonce).replace("{sign}", sign);
        String paramJson = JacksonHelper.serialize(updataDataDto);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        String body;
        HttpHelper.HttpResult httpResult = null;
        QQUploadResult resp = null;
        try{
            httpResult = HttpHelper.getInstance().httpPostJsonV2(url, paramJson, headers);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return false;
            }
            resp = JacksonHelper.deserialize(httpResult.getBody(), QQUploadResult.class);
//            resp = JacksonHelper.deserialize("{\"errCode\":0,\"errMsg\":\"success\"}", QQUploadResult.class);
//            log.info("请求url:{},请求参数:{}  , heanders:{}, 返回值：{}",url,paramJson,headers,resp);

        }
        catch (Exception ex){
            log.error("publishToQQ error updataDataDto:{}", paramJson, ex);
        }
        finally {
            //todo 更新对象表BrandSeriesUploadQq,更新最后一次上报结果
            brandSeriesUploadQq.setRequestUrl(url);
            if(httpResult != null){
                brandSeriesUploadQq.setRequestStatus(httpResult.getStatusCode());
            }
            if(resp != null){
                brandSeriesUploadQq.setResponseData(JacksonHelper.serialize(resp));
                brandSeriesUploadQq.setRequestCode(String.valueOf(resp.getErrCode()));
            }
            brandSeriesUploadQq.setRequestData(paramJson);
            brandSeriesUploadQqMapper.updateByPrimary(brandSeriesUploadQq);
            //todo 记录上报日志BrandSeriesUploadQqLog
            //brandSeriesUploadQqLogMapper.insert(getBrandSeriesUploadQqLog(brandSeriesUploadQq));
            //上面insert日志量太大，改成记录到鹰眼日志里
            log.info("brandSeriesUploadQqLogMapper.insert:{}",getBrandSeriesUploadQqLog(brandSeriesUploadQq));
        }
        return resp.getErrCode() == 0 ? true : false;
    }

    /**
     * 获取品牌卡数据记录
     * */
    private BrandSeriesUploadQq getBrandSeriesUploadQq(BrandForQQUtil.BrandForQQ brandInfo, AllCitiesUtil.CityItem cityItem){
        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(Integer.valueOf(brandInfo.getBrandId()));
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(brandInfo.getBrandId()));
        brandSeriesUploadQq.setBrandName(brandInfo.getBrandName());
        brandSeriesUploadQq.setSeriesId(0);
        brandSeriesUploadQq.setSeriesIdEncode("");
        brandSeriesUploadQq.setSeriesName("");
        brandSeriesUploadQq.setCityCode(cityItem.getCityId());
        brandSeriesUploadQq.setCityName(cityItem.getCityName());
        brandSeriesUploadQq.setCardType(BRANDTYPE);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery(brandInfo.getQuery());
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        return brandSeriesUploadQq;
    }

    /**
     * 获取图片卡数据记录
     * */

    private BrandSeriesUploadQq gePicSeriesUploadQqV3(SeriesForQQUtil.SeriesForQQ seriesInfo,  SpecParamDto specParamDto){

        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = specParamDto.getCarSeriesInfoMap();
        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoMap.get(Integer.parseInt(seriesInfo.getSeriesId()));

        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(carSeriesInfoDTO.getBrandid());
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(carSeriesInfoDTO.getBrandid()));
        brandSeriesUploadQq.setBrandName(carSeriesInfoDTO.getBrandname());
        brandSeriesUploadQq.setSeriesId(Integer.valueOf(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesIdEncode(SecurityKit.encrypt(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesName(seriesInfo.getSeriesName());
        brandSeriesUploadQq.setCityCode("");
        brandSeriesUploadQq.setCityName("");
        brandSeriesUploadQq.setCardType(IMGTYPE);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery(seriesInfo.getQuery()+"");
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        brandSeriesUploadQq.setSpecId(specParamDto.getSpecId());
        return brandSeriesUploadQq;
    }
    /**
     * 获取品牌卡数据记录
     * */
    private BrandSeriesUploadQq getBrandSeriesUploadQqV3(AllBrandInfoDto.BrandInfoDto  brandInfo, AllCitiesUtil.CityItem cityItem,SeriesInfoDto seriesInfoDto){
        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(Integer.valueOf(brandInfo.getId()));
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(brandInfo.getId()));
        brandSeriesUploadQq.setBrandName(brandInfo.getName());
        brandSeriesUploadQq.setSeriesId(seriesInfoDto.getId());
        brandSeriesUploadQq.setSeriesIdEncode(SecurityKit.encrypt(seriesInfoDto.getId()));
        brandSeriesUploadQq.setSeriesName(seriesInfoDto.getName());
        brandSeriesUploadQq.setCityCode(cityItem.getCityId());
        brandSeriesUploadQq.setCityName(cityItem.getCityName());
        brandSeriesUploadQq.setCardType(BRANDTYPEV3);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery("");
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        brandSeriesUploadQq.setSpecId(0);
        return brandSeriesUploadQq;
    }

    /**
     * 获取评价卡数据记录
     * */
    private BrandSeriesUploadQq getBrandSeriesUploadQqPingJia(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem, CarApiBrandOut carApiBrandOut){
        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(Integer.parseInt(carApiBrandOut.getId()));
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(carApiBrandOut.getId()));
        brandSeriesUploadQq.setBrandName(carApiBrandOut.getName());
        brandSeriesUploadQq.setSeriesId(Integer.valueOf(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesIdEncode(SecurityKit.encrypt(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesName(seriesInfo.getSeriesName());
        brandSeriesUploadQq.setCityCode(cityItem.getCityId());
        brandSeriesUploadQq.setCityName(cityItem.getCityName());
        brandSeriesUploadQq.setCardType(PINGJIATYPE);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery("");
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        brandSeriesUploadQq.setSpecId(0);
        return brandSeriesUploadQq;
    }
    /**
     * 获取商品卡数据记录
     * */
    private BrandSeriesUploadQq getBrandSeriesUploadQq(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem, CarApiBrandOut carApiBrandOut){
        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(Integer.parseInt(carApiBrandOut.getId()));
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(carApiBrandOut.getId()));
        brandSeriesUploadQq.setBrandName(carApiBrandOut.getName());
        brandSeriesUploadQq.setSeriesId(Integer.valueOf(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesIdEncode(SecurityKit.encrypt(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesName(seriesInfo.getSeriesName());
        brandSeriesUploadQq.setCityCode(cityItem.getCityId());
        brandSeriesUploadQq.setCityName(cityItem.getCityName());
        brandSeriesUploadQq.setCardType(SERIESTYPE);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery(seriesInfo.getQuery());
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        return brandSeriesUploadQq;
    }

    private BrandSeriesUploadQq getBrandSeriesUploadQqV3(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem,  SpecParamDto specParamDto){

        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = specParamDto.getCarSeriesInfoMap();
        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoMap.get(Integer.parseInt(seriesInfo.getSeriesId()));

        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(carSeriesInfoDTO.getBrandid());
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(carSeriesInfoDTO.getBrandid()));
        brandSeriesUploadQq.setBrandName(carSeriesInfoDTO.getBrandname());
        brandSeriesUploadQq.setSeriesId(Integer.valueOf(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesIdEncode(SecurityKit.encrypt(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesName(seriesInfo.getSeriesName());
        brandSeriesUploadQq.setCityCode(cityItem.getCityId());
        brandSeriesUploadQq.setCityName(cityItem.getCityName());
        brandSeriesUploadQq.setCardType(SERIESTYPEV3);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery(seriesInfo.getQuery()+"");
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        brandSeriesUploadQq.setSpecId(specParamDto.getSpecId());
        return brandSeriesUploadQq;
    }


    private BrandSeriesUploadQq getPicSeriesUploadQqV3(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem,  SpecParamDto specParamDto){

        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = specParamDto.getCarSeriesInfoMap();
        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoMap.get(Integer.parseInt(seriesInfo.getSeriesId()));

        BrandSeriesUploadQq brandSeriesUploadQq = new BrandSeriesUploadQq();
        brandSeriesUploadQq.setBrandId(carSeriesInfoDTO.getBrandid());
        brandSeriesUploadQq.setBrandIdEncode(SecurityKit.encrypt(carSeriesInfoDTO.getBrandid()));
        brandSeriesUploadQq.setBrandName(carSeriesInfoDTO.getBrandname());
        brandSeriesUploadQq.setSeriesId(Integer.valueOf(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesIdEncode(SecurityKit.encrypt(seriesInfo.getSeriesId()));
        brandSeriesUploadQq.setSeriesName(seriesInfo.getSeriesName());
        brandSeriesUploadQq.setCityCode(cityItem.getCityId());
        brandSeriesUploadQq.setCityName(cityItem.getCityName());
        brandSeriesUploadQq.setCardType(IMGTYPE);
        brandSeriesUploadQq.setCardStatus(1);
        brandSeriesUploadQq.setQuery(seriesInfo.getQuery()+"");
        brandSeriesUploadQq.setRequestUrl("");
        brandSeriesUploadQq.setRequestData("");
        brandSeriesUploadQq.setRequestCode("");
        brandSeriesUploadQq.setResponseData("");
        brandSeriesUploadQq.setRequestStatus(0);
        brandSeriesUploadQq.setCreatedStime(new Date());
        brandSeriesUploadQq.setModifiedStime(new Date());
        brandSeriesUploadQq.setIsDel(0);
        return brandSeriesUploadQq;
    }

    private BrandSeriesUploadQqLog getBrandSeriesUploadQqLog(BrandSeriesUploadQq brandSeriesUploadQq){
        BrandSeriesUploadQqLog brandSeriesUploadQqLog = new BrandSeriesUploadQqLog();
        brandSeriesUploadQqLog.setBrandId(brandSeriesUploadQq.getBrandId());
        brandSeriesUploadQqLog.setBrandIdEncode(brandSeriesUploadQq.getBrandIdEncode());
        brandSeriesUploadQqLog.setBrandName(brandSeriesUploadQq.getBrandName());
        brandSeriesUploadQqLog.setSeriesId(brandSeriesUploadQq.getSeriesId());
        brandSeriesUploadQqLog.setSeriesIdEncode(brandSeriesUploadQq.getSeriesIdEncode());
        brandSeriesUploadQqLog.setSeriesName(brandSeriesUploadQq.getSeriesName());
        brandSeriesUploadQqLog.setCityCode(brandSeriesUploadQq.getCityCode());
        brandSeriesUploadQqLog.setCityName(brandSeriesUploadQq.getCityName());
        brandSeriesUploadQqLog.setCardType(brandSeriesUploadQq.getCardType());
        brandSeriesUploadQqLog.setCardStatus(brandSeriesUploadQq.getCardStatus());
        brandSeriesUploadQqLog.setQuery(brandSeriesUploadQq.getQuery());
        brandSeriesUploadQqLog.setRequestUrl(brandSeriesUploadQq.getRequestUrl());
        brandSeriesUploadQqLog.setRequestData(brandSeriesUploadQq.getRequestData());
        brandSeriesUploadQqLog.setRequestCode(brandSeriesUploadQq.getRequestCode());
        brandSeriesUploadQqLog.setRequestStatus(brandSeriesUploadQq.getRequestStatus());
        brandSeriesUploadQqLog.setResponseData(brandSeriesUploadQq.getResponseData());
        brandSeriesUploadQqLog.setSpecId(brandSeriesUploadQq.getSpecId());
        return brandSeriesUploadQqLog;
    }

    private String reChangeCityCode(String cityCode){
        if("110100".equals(cityCode)){
            cityCode = "110000";
        }
        else if("120100".equals(cityCode)){
            cityCode = "120000";
        }
        else if("310100".equals(cityCode)){
            cityCode = "310000";
        }
        else if("500100".equals(cityCode)){
            cityCode = "500000";
        }
        return cityCode;
    }
    private boolean checkAdd(AllCitiesUtil.CityItem cityItem){
        return true;
//        String cityCode = cityItem.getCityId();
//        if("110100".equals(cityCode) || "110000".equals(cityCode)){
//            return true;
//        }
//        if("120100".equals(cityCode) || "120000".equals(cityCode)){
//            return true;
//        }
//        if("310100".equals(cityCode) || "310000".equals(cityCode)){
//            return true;
//        }
//        if("500100".equals(cityCode) || "500000".equals(cityCode)){
//            return true;
//        }
//        return false;
    }

    private EvaluateCardSeriesInfoUploadDTO assembleEvaluateCardSeriesInfo (SeriesForQQUtil.SeriesForQQ customSeriesItem,
                                                                            AllCitiesUtil.CityItem cityItem,
                                                                            SeriesInfoDto seriesInfoDto,
                                                                            CarApiBrandOut carApiBrandOut,
                                                                            SpecParamDto specParamDto){

        //获取口碑数据
        PageKBOf<WordOfMouthDto, DimSeriesPRCTypesDto> kouBeiData = this
                .getKouBeiData(Integer.parseInt(customSeriesItem.getSeriesId()));

        EvaluateCardSeriesInfoUploadDTO.Comment comment = new EvaluateCardSeriesInfoUploadDTO.Comment();
        EvaluateCardSeriesInfoUploadDTO.Display display = new EvaluateCardSeriesInfoUploadDTO.Display();
        WordOfMouthDto KouBeiEvaluate = null;
        if (kouBeiData != null && !CollectionUtils.isEmpty(kouBeiData.getItems())) {
            KouBeiEvaluate = kouBeiData.getItems().get(0);
        }
        String encryptSeriesId = SecurityKit.encrypt(customSeriesItem.getSeriesId());
        String encryptSpecId = SecurityKit.encrypt(KouBeiEvaluate.getSpecid());
        comment.setCarName(KouBeiEvaluate.getSpecName());
        comment.setCommContent(KouBeiEvaluate.getBest().replace("【最满意】",""));
        comment.setUncommContent(KouBeiEvaluate.getWorst().replace("【最不满意】",""));
        comment.setAuthor(KouBeiEvaluate.getNickName());
        comment.setImage("https://i2.autoimg.cn/koubei"+KouBeiEvaluate.getHeadImage());
        String lastEdit = KouBeiEvaluate.getLastEdit();
        String lastEditDate = lastEdit.substring(0, 10);
        comment.setDate(lastEditDate);
        comment.setRank(lastEditDate.replace("-",""));
        comment.setCarLink(getEncodeUrl("/detailpackage/pages/koubei-detail/index?auto_open_from=qb_search_koubei&linkid=4|127|1837|0|206395|306020&koubeiid="+KouBeiEvaluate.getId(),true));
//        comment.setCarLink(getEncodeUrl("/car-package/pages/series-koubei/index?auto_open_from=qb_search_spec_koubei&linkid=4|127|1837|0|206394|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId)+"&specid=${{0}}".replace( "{0}",encryptSpecId),true));
        comment.setCommLink(getEncodeUrl("/detailpackage/pages/koubei-detail/index?auto_open_from=qb_search_koubei&linkid=4|127|1837|0|206395|306020&koubeiid="+KouBeiEvaluate.getId(),true));
        display.setSum(kouBeiData.getTotalCount().toString());
        display.setUrl(getEncodeUrl("/car-package/pages/series-koubei/index?auto_open_from=qb_search_series_koubei&linkid=4|127|1837|0|206393|306020&seriesid=${{0}}".replace( "{0}",encryptSeriesId),true));
        display.setTitle(carApiBrandOut.getName()+seriesInfoDto.getName());
        display.setName(seriesInfoDto.getName());
        display.setBrand(carApiBrandOut.getName());
        Map<Integer, CarSeriesInfoDTO> carSeriesInfoMap = specParamDto.getCarSeriesInfoMap();
        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoMap.get(Integer.parseInt(customSeriesItem.getSeriesId()));
        display.setSubBrand(carSeriesInfoDTO.getFctname());
//        display.setSimplename("");
        List<HashMap> tmpModelEvalScore = getSeriesEvalScore(Integer.parseInt(customSeriesItem.getSeriesId()));
        if (tmpModelEvalScore.size() > 0) {
            String evalScore = tmpModelEvalScore.get(0).get("kbscore").toString();
            display.setCommScore( evalScore.split("[.]")[0]);
        }else{
            display.setCommScore("0");
        }

        display.setPicsrc(seriesInfoDto.getLogo());
        display.setComment(comment);
        String[] KouBeiApprovingTags = this.loadSeriesPRCType(Integer.parseInt(customSeriesItem.getSeriesId()),1);
        List<String> KouBeiApprovingTagList = Arrays.asList(KouBeiApprovingTags);
        List<String> KouBeiApprovingTagTop5List = KouBeiApprovingTagList.subList(0, KouBeiApprovingTagList.size() >= 5 ? 5 : KouBeiApprovingTagList.size());
        String kouBeiApprovingTagTop5Str = KouBeiApprovingTagTop5List.toString().replace("[", "").replace("]", "");
        display.setAdvantages(kouBeiApprovingTagTop5Str);

        String[] KouBeiDiscontentTags = this.loadSeriesPRCType(Integer.parseInt(customSeriesItem.getSeriesId()),2);
        List<String> KouBeiDiscontentTagList = Arrays.asList(KouBeiDiscontentTags);
        List<String> KouBeiDiscontentTop5TagList = KouBeiDiscontentTagList.subList(0, KouBeiDiscontentTagList.size() >= 5 ? 5 : KouBeiDiscontentTagList.size());
        String kouBeiDiscontentTagTop5Str = KouBeiDiscontentTop5TagList.toString().replace("[", "").replace("]", "");

        display.setDisadvantages(kouBeiDiscontentTagTop5Str);

        EvaluateCardSeriesInfoUploadDTO result = new EvaluateCardSeriesInfoUploadDTO();
        result.setDisplay(display);
        result.setKey(SecurityKit.encrypt(seriesInfoDto.getName()));

        return result;
    }

    //获取口碑标签
    //type = 1 最满意标签
    //type = 2 最不满意标签
    private String[] loadSeriesPRCType(int seriesId,Integer type) {
        try {
            ReturnValue<DimSeriesPRCT> dimSeriesPRCTReturnValue = CompletableFuture
                    .supplyAsync(new Supplier<ReturnValue<DimSeriesPRCT>>() {
                        @Override
                        public ReturnValue<DimSeriesPRCT> get() {
                            HashMap<String, Object> param = new HashMap<>();
                            param.put("_appid", "m");
                            param.put("seriesId", seriesId);
                            param.put("typekey", type);
                            param.put("year", 1);

                            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://koubei.api.sjz.autohome.com.cn/api/Semantic/LoadSeriesPRCType", param);
                            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                                return null;
                            }
                            return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<DimSeriesPRCT>>() {
                            });
                        }
                    }).get();
            String[] combinationArr = null;
            if (dimSeriesPRCTReturnValue.getResult()!= null && !CollectionUtils.isEmpty(dimSeriesPRCTReturnValue.getResult().getDimSeriesPRCTypes())){
                List<DimSeriesPRCType> dimSeriesPRCTypes = dimSeriesPRCTReturnValue.getResult().getDimSeriesPRCTypes();
                List<List<DimSeriesPRCTypeSummary>> summaryList = dimSeriesPRCTypes.stream().map(DimSeriesPRCType::getSummary).collect(Collectors.toList());
                for (List<DimSeriesPRCTypeSummary> dimSeriesPRCTypeSummaries : summaryList) {
                    String combinationStr = dimSeriesPRCTypeSummaries.stream().map(DimSeriesPRCTypeSummary::getCombination).map(String::valueOf).collect(Collectors.joining(","));
                    combinationArr = combinationStr.split(",");

                }

            }
            return combinationArr;
        } catch (Exception e) {
            log.error("CarBaseService loadSeriesPRCType",e);
        }
        return null;
    }

    //获取评分
    private List<HashMap> getSeriesEvalScore(int seriesId) {

        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "m");
        param.put("seriesIds", seriesId);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://koubei.api.sjz.autohome.com.cn/api/Series/GetSeriesEvalScore", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        ReturnValue<List<HashMap>> returnValue = JacksonHelper
                .deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<HashMap>>>() {
                });

        if (returnValue != null && returnValue.getResult() != null) {
            return returnValue.getResult();
        }

        return returnValue.getResult();
    }

    //获取口碑评价
    private PageKBOf<WordOfMouthDto, DimSeriesPRCTypesDto> getKouBeiData (Integer seriesId){

        PageKBOf<WordOfMouthDto, DimSeriesPRCTypesDto> kouBeiData = miscsService
                .wordOfMouth(seriesId, 1, 10, 0, 0, 180, 0, 0, 0, 0, 0, 110100);
        return kouBeiData;
    }
}
