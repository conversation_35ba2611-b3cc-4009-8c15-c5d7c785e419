package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.VideoplatformService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class VideoplatformServiceImpl implements VideoplatformService {

    /**
     * 批量获取小视频
     *
     * @param _appid
     * @param videoids
     * @param userid
     * @return
     */
    @Override
    public ResponseContent<?> getvideoinfolist(String _appid, String videoids, String userid) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("videoids", videoids);
            param.put("verifystatus", "0");

            if (StringUtil.isNotNullAndWhiteSpace(userid)) {
                param.put("userid", userid);
            }
//            String result = HttpHelper.getResponse(getvideoinfolist, param, ENCODING, 3000).getContent();
//            if (!StringUtil.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(200, "暂无数据");
//            }
//            ResponseContent<List<HashMap>> responseContent = JSON.parseObject(result, new TypeReference<ResponseContent<List<HashMap>>>() {
//            });

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://intranet.sv.api.autohome.com.cn/100/video/getvideoinfolist", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(200, "暂无数据");
            }

            ResponseContent<List<HashMap>> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<List<HashMap>>>() {
            });

            if (responseContent == null || responseContent.getResult() == null) {
                return new ResponseContent<>(200, "暂无数据");
            }
            responseContent.getResult().removeIf(item -> Integer.parseInt(item.get("ispass").toString()) == 0);
            return responseContent;

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("getvideoinfolist-withexception", e.getMessage())));
            log.error("getvideoinfolist-withexception", e);
            return new ResponseContent<>(102, "暂无数据", new HashMap<>());
        }
    }
}
