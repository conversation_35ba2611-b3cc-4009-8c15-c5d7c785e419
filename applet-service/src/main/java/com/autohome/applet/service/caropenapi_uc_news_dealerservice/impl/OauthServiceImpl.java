package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.service.caropenapi_uc_news_dealerservice.OauthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OauthServiceImpl implements OauthService {

    private static String authorizeUrl = "https://openapi.autohome.com.cn/api/oauth2/authorize";
    private static String authtokenkey = "OauthService";

//    @Autowired
//    @Qualifier("dealerservice")
//    RedisClient redisClient;

//    @Override
//    public String getQuickToken() {
//        try {
//            String token = redisClient.get(authtokenkey);
//            if (StringUtil.isNotNullAndWhiteSpace(token)) {
//                token = token.replace("\\", "");
//                return JSON.toJSONString(new ResponseContent(JSON.parseObject(token.substring(token.indexOf("\"") + 1, token.length() - 1), HashMap.class)));
//            }
//            Map param = new HashMap<>();
//            param.put("client_id", "4jU4cJ5ocANou7QOjy3xj2tWRRiQt4sO");
//            param.put("client_secret", "7aWWU6GEMFYABzw6FVa9wksKTSWEcAfy");
//            param.put("response_type", "token");
//
//            //String result = HttpHelper.getInstance().httpPost(authorizeUrl, param).getBody();
//            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPost(authorizeUrl, param);
//
//            if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
//                //缓存1小时
//                redisClient.set(authtokenkey, JSON.toJSONString(httpResult.getBody()), 3500, TimeUnit.SECONDS);
//                return JSON.toJSONString(new ResponseContent(httpResult.getBody()));
//            } else {
//                return JSON.toJSONString(new ResponseContent(200, "获取token失败", null));
//            }
//
////            if (!StringUtil.isNotNullAndWhiteSpace(httpResult.getBody())) {
////                return JSON.toJSONString(new ResponseContent(200, "获取token失败", null));
////            }
////            HashMap responseContent = JSON.parseObject(httpResult.getBody(), HashMap.class);
////            if (responseContent == null) {
////                return JSON.toJSONString(new ResponseContent(200, "获取token失败", null));
////            }
//
//        } catch (Exception e) {
//            log.error("OauthService:getQuickToken", e);
//            return JSON.toJSONString(new ResponseContent(500, "getToken-WithException" + e.getMessage(), new ArrayList<AutoHomeUser>()));
//        }
//    }

    @Override
    public String getDealerToken() {
        return "{\n" +
                "    \"returncode\": 0,\n" +
                "    \"message\": \"成功\",\n" +
                "    \"result\": {\n" +
                "        \"data\": {\n" +
                "            \"access_token\": \"Odm2zaWlECQRHVo0KV4mfzSJfVeQ63BN\",\n" +
                "            \"token_type\": \"bearer\",\n" +
                "            \"expires_in\": 7200\n" +
                "        }\n" +
                "    }\n" +
                "}";
    }
}