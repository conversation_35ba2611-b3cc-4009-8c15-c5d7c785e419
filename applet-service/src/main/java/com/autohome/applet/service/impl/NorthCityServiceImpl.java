package com.autohome.applet.service.impl;

import com.autohome.applet.model.dto.newenergy.NorthCityDTO;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: WangBoWen
 * @date: 2024-01-15
 **/
@Slf4j
@Service
public class NorthCityServiceImpl {

    private static String CITY_INFO = null;

    private static Map<Integer,String> NORTH_CITY_MAP = null;

    public Map<Integer, String> getNorthCityMap (){

        List<NorthCityDTO> cityList = JacksonHelper.deserialize(getCityInfoJsonStr(), new TypeReference<List<NorthCityDTO>>() {});

        if (NORTH_CITY_MAP == null) {
            Map<Integer,String> northCityMapCurrent = new HashMap<>();
            for (NorthCityDTO northCityDTO : cityList) {
                northCityMapCurrent.put(northCityDTO.getCode(), northCityDTO.getName());
            }

            if (!northCityMapCurrent.isEmpty()) {
                NORTH_CITY_MAP = northCityMapCurrent;
            }

        }

        return NORTH_CITY_MAP;

    }

    public String getCityInfoJsonStr (){
        if (CITY_INFO == null) {
            StringBuilder builder = new StringBuilder();
            try {
                InputStream inputStream = NorthCityServiceImpl.class.getClassLoader()
                                                                 .getResourceAsStream("json/north_city.json");
                List<String> strs = IOUtils.readLines(inputStream, "UTF-8");
                for (String str : strs) {
                    builder.append(str);
                }
            } catch (Exception e) {
                log.error("getCityInfoJsonStr is err", e);
            }
            CITY_INFO = builder.toString();
        }

        return CITY_INFO;
    }



}
