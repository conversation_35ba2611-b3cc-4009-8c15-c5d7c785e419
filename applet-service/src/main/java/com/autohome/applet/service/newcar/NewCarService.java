package com.autohome.applet.service.newcar;


import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.newcar.NewCarFocusCardDTO;

import java.util.HashMap;
import java.util.List;

public interface NewCarService {
    /**
     * 新车频道改版:新车频道焦点图
     * wiki:https://doc.autohome.com.cn/docapi/page/share/share_10SxLpZWUSm
     * */
    ReturnValue<List<NewCarFocusCardDTO>> getFocusCard();

    /**
     * 混合车系维度
     * 每个车系取Top50条内容 更新到 db.autopoen.newcarevent 表
     * 返回影响行数
     * 接口文档：http://la.corpautohome.com/doc/detail?laFlowId=1618
     *
     */
    HashMap<String, Integer> newcareventDataSync();

//    /**
//     * 单车系维度
//     * 每个车系取TOP10条内容 更新到缓存
//     */
//    void newcareventSeriesCacheSync();

    /**
     * 单内容大事件缓存
     * 每天一个key，按日期定义 value格式：objtype_objid 的集合 更新到缓存
     */
    void newcareventObjectsCacheSync();
}
