package com.autohome.applet.service.caropenapi_uc_news_dealerservice;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.JaxbReadXml;
import com.autohome.applet.util.netcoreapi.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.JAXBException;
import javax.xml.bind.annotation.*;
import java.util.List;
import java.util.Optional;


/**
 * Created by hanshanfeng on 2017/12/7.
 */
public class BusinessApp {
    private final static Logger LOGGER = LoggerFactory.getLogger(JaxbReadXml.class);
    public static Businesses businesses;

    /**
     * 校验appid
     *
     * @param appid
     * @return
     */
    public static ResponseContent checkAppidForSou(String appid, Object result) {
        if (!StringUtil.isNotEmpty(appid)) {
            return new ResponseContent(103, "appid不能为空", result);
        }
        boolean ishaveappid = BusinessApp.isHaveAppid(appid);
        if (!ishaveappid) {
            return new ResponseContent(104, "不合法的appid", result);
        }
        return null;
    }

    /**
     * 校验appid
     *
     * @param appid
     * @return
     */
    public static ResponseContent<?> checkAppid(String appid) {
        if (!StringUtils.isNotEmpty(appid)) {
            return new ResponseContent<>(103, "appid不能为空");
        }
        boolean ishaveappid = BusinessApp.isHaveAppid(appid);
        if (!ishaveappid) {
            return new ResponseContent<>(104, "不合法的appid");
        }
        return null;
    }

    public static boolean isHaveAppid(String appid) {
        boolean result = false;
        if (businesses == null) {
            build();
        }
        if (businesses == null || businesses.businesses.size() == 0) {
            return false;
        }
        Optional<Business> business = businesses.businesses.stream().filter(s -> s.appid.equals(appid)).findFirst();
        if (business.isPresent()) {
            result = true;
        }
        return result;
    }

    public static void main(String[] arg) {
        System.out.println(isHaveAppid("car"));
        System.out.println(isHaveAppid("car11"));
        System.out.println(isHaveAppid("cms"));
        System.out.println("11111");
    }

    public static Businesses build() {
        try {
            businesses = JaxbReadXml.readString(Businesses.class, "business.xml");
            if (businesses == null) {
                return null;
            }
            return businesses;
        } catch (JAXBException e) {
            LOGGER.error("BusinessAppParsingxmlexception" + e.getMessage(), e);
        }
        return null;
    }

    @XmlRootElement(name = "businesses")
    @XmlAccessorType(XmlAccessType.FIELD)
    private static class Businesses {
        @XmlElement(name = "business")
        private List<Business> businesses;
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    private static class Business {
        @XmlAttribute(name = "id")
        private int id;
        @XmlAttribute(name = "appid")
        private String appid;
        @XmlAttribute(name = "name")
        private String name;
    }
}

