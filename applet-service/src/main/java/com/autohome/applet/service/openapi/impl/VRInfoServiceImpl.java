package com.autohome.applet.service.openapi.impl;

import com.autohome.applet.model.constants.RedisKeys;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.carlibrary.SeriesDetailDto;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarVrInfo2;
import com.autohome.applet.model.dto.vrinfodata.*;
import com.autohome.applet.service.javaapi.CarLibraryService;
import com.autohome.applet.service.openapi.VRInfoService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.MapBuilder;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 平安好车接口service
 * @author: WangBoWen
 * @date: 2024-03-15
 **/
@Slf4j
@Service
public class VRInfoServiceImpl implements VRInfoService {

    @Autowired
    private CarLibraryService carLibraryService;

    @Autowired
    RedisClient redisClient;

    @Override
    public List<PingAnCarSalesRankDTO> getCarSalesRank() {

        String redisValue = redisClient.getValue(RedisKeys.CAR_SALES_RANK);

        if (!StringUtils.isEmpty(redisValue)) {
            return JacksonHelper.deserialize(redisValue,new TypeReference<List<PingAnCarSalesRankDTO>>(){});
        }

        List<CarSalesRankDTO> carSalesRankDataList = this.getCarSalesRankData(9000000, 0, null, null, null, 10, null, null);

        List<PingAnCarSalesRankDTO> resultList = new ArrayList<>();
        //查询品牌列表
//        BrandInfoDto brandInfoDto = carLibraryService.getBrandList();

        List<CompletableFuture<SeriesDetailDto>> tasks = new ArrayList<>();

        for (CarSalesRankDTO carSalesRankDTO : carSalesRankDataList) {
            PingAnCarSalesRankDTO pingAnCarSalesRankDTO = new PingAnCarSalesRankDTO();
            pingAnCarSalesRankDTO.setSeriesid(carSalesRankDTO.getSeriesid());
            pingAnCarSalesRankDTO.setMax_guidance_price(carSalesRankDTO.getMax_guidance_price());
            pingAnCarSalesRankDTO.setMin_guidance_price(carSalesRankDTO.getMin_guidance_price());
            pingAnCarSalesRankDTO.setPageUrl("carpackage/pages/series/index?seriesid="+carSalesRankDTO.getSeriesid()+"&auto_open_from=hcz_series");
            pingAnCarSalesRankDTO.setSecondPageUrl("/car-package/pages/series/index?seriesid="+carSalesRankDTO.getSeriesid()+"&auto_open_from=hcz_series");
//            if(brandInfoDto != null && !CollectionUtils.isEmpty(brandInfoDto.getBranditems())){
//                BrandInfoDto.Branditem item = brandInfoDto.getBranditems().stream().filter(b -> b.getId().equals(carSalesRankDTO.getBrandid())).findFirst().orElse(null);
//                if(item != null){
//                    pingAnCarSalesRankDTO.setLogo(item.getLogo());
//                }
//            }

            CompletableFuture<SeriesDetailDto> completable = CompletableFuture.supplyAsync(
                    () -> carLibraryService.getSeriesBySeriesId(Integer.valueOf(carSalesRankDTO.getSeriesid())));

            tasks.add(completable);

            resultList.add(pingAnCarSalesRankDTO);
        }

        CompletableFuture[] carHashArray = tasks.toArray(new CompletableFuture[tasks.size()]);
        CompletableFuture.allOf(carHashArray).join();

        for (CompletableFuture<SeriesDetailDto> task : tasks) {
            try {
                SeriesDetailDto seriesDetailDto = task.get();

                if (seriesDetailDto == null) {
                    continue;
                }
                for (int i = 0; i < resultList.size(); i++) {
                    PingAnCarSalesRankDTO pingAnCarSalesRankDTO = resultList.get(i);
                    if (pingAnCarSalesRankDTO.getSeriesid().equals(String.valueOf(seriesDetailDto.getId()))) {
                        resultList.get(i).setSeriesname(seriesDetailDto.getName());
                        resultList.get(i).setLogo(seriesDetailDto.getPnglogo());
                        break;
                    }
                }

            } catch (Exception e) {
                log.error("getCarSalesRank :: ",e);
            }
        }

        redisClient.setValue(RedisKeys.CAR_SALES_RANK,JacksonHelper.serialize(resultList),1, TimeUnit.HOURS);

        return resultList;
    }

    @Override
    public List<BrandListNewResult> getVrBrandListNewToList() {

        //todo 结果37kb 是否需要处理大key?
//        String redisValue = redisClient.getValue(RedisKeys.VR_BRAND_LIST_NEW);
//        if (!StringUtils.isEmpty(redisValue)) {
//            return JacksonHelper.deserialize(redisValue, new TypeReference<List<BrandListNewResult>>() {});
//        }

        List<BrandListNewResult> result = new ArrayList<>();
        Map<Integer, Brandlist> vrMap = getBrandlistHasVR();

        if(CollectionUtils.isEmpty(vrMap)){
            throw new BusinessException("没有vr数据",-1);
        }

        List<BrandListNewResult> brandListNewToList = getBrandListNewToList(null);

        result = brandListNewToList;

        for(BrandListNewResult outLetterList : result){
            if(CollectionUtils.isEmpty(outLetterList.getBranditems())){
                continue;
            }
            outLetterList.getBranditems().removeIf(item->!vrMap.containsKey(item.getId()) || item.getState() == 0);
        }

        redisClient.setValue(RedisKeys.VR_BRAND_LIST_NEW,JacksonHelper.serialize(result),2,TimeUnit.HOURS);

        return result;
    }

    @Override
    public NewSeriesNew getSerieslistHasVRByBrandId(Integer brandId) {

        //todo 同样的问题 大key
//        String redisKey = String.format(RedisKeys.VR_SERIES_LIST, brandId);
//
//        String redisValue = redisClient.getValue(redisKey);
//
//        if (!StringUtils.isEmpty(redisValue)) {
//            return JacksonHelper.deserialize(redisValue,NewSeriesNew.class);
//        }

        Map<Integer, SeriesListVr> vrMap = getVrSeriesListByBrandId(brandId);
        if(CollectionUtils.isEmpty(vrMap)){
            log.error("getSerieslistHasVRByBrandId brandId:{} 没有vr车系",brandId);
            return null;
//            throw new BusinessException("没有vr车系",-1);
        }
        NewSeriesNew oldSeriesList = getSeriesInfoByBrandAndStateNew( brandId.toString(),0,0);

        if(!CollectionUtils.isEmpty(oldSeriesList.getSellseries())){
            for(BrandNewNew.Fctlist fct:oldSeriesList.getSellseries()){
                if(CollectionUtils.isEmpty(fct.getSerieslist())){
                    continue;
                }

                List<BrandNewNew.Serieslist> filter = fct.getSerieslist().stream().filter(item -> vrMap.containsKey(item.getId())).collect(Collectors.toList());
                fct.setSerieslist(filter);
            }
        }

        if(!CollectionUtils.isEmpty(oldSeriesList.getUnsellseries())){
            for(BrandNewNew.Fctlist fct:oldSeriesList.getUnsellseries()){
                if(CollectionUtils.isEmpty(fct.getSerieslist())){
                    continue;
                }

                List<BrandNewNew.Serieslist> filter = fct.getSerieslist().stream().filter(item -> vrMap.containsKey(item.getId())).collect(Collectors.toList());
                fct.setSerieslist(filter);
            }
        }

//        redisClient.setValue(redisKey,JacksonHelper.serialize(oldSeriesList),2,TimeUnit.HOURS);

        return oldSeriesList;
    }

    @Override
    public CarVrInfo2 getcarvrinfobyseriesid(String category, int seriesid) {

//        String redisKey = String.format(RedisKeys.CAR_VR_INFO, category, seriesid);

//        String redisValue = redisClient.getValue(redisKey);
//
//        if (!StringUtils.isEmpty(redisValue)) {
//            return JacksonHelper.deserialize(redisValue,CarVrInfo2.class);
//        }

        String url = "http://pano.api.autohome.com.cn/v1/vr/getcarvrinfobyseriesid";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                .put("_appid", "car")
                .put("category", category)
//                .put("vrtype", 2)
                .put("seriesid", Integer.toString(seriesid)).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance(5000).httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("getcarvrinfobyseriesid {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getcarvrinfobyseriesid 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<CarVrInfo2> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarVrInfo2>>() {});

        if (returnValue == null) {
            log.warn("getcarvrinfobyseriesid 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0  ){
            log.error("getcarvrinfobyseriesid {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null){
            log.warn("getcarvrinfobyseriesid 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        List<CarVrInfo2.Extinfo> extinfo = returnValue.getResult().getExtinfo();

        for (int i = 0; i < extinfo.size(); i++) {

            String showurl = extinfo.get(i).getShowurl();

            if (StringUtils.isEmpty(showurl)) {
                continue;
            }

            if (showurl.indexOf("?") < 0) {

                String currentShowURL = showurl + "?appversion=&pagesrc=miniprogram_hcz_vr&ep=w0";
                extinfo.get(i).setShowurl(currentShowURL);
            }else{
                String currentShowURL = showurl.replace("&pure=1","").replace("pure=1","").replace("?", "?appversion=&pagesrc=miniprogram&ep=w0");
                extinfo.get(i).setShowurl(currentShowURL);
            }

        }

        //redisClient.setValue(redisKey,JacksonHelper.serialize(returnValue.getResult()),2,TimeUnit.HOURS);

        return returnValue.getResult();
    }

    @Override
    public List<CarSalesRankDTO> getCarSalesRankData(Integer maxPrice, Integer minPrice,
                                                     String beginMonth, String endMonth,
                                                     String levels, Integer size,
                                                     String manutype, Integer isNewenergy) {


        String url = "http://carservice2.cupid.autohome.com.cn/salecount/carseriesCLHSaleMixRank";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                .put("maxPrice", maxPrice)
                .put("minPrice", minPrice)
                .put("beginMonth",beginMonth == null ? "" : beginMonth)
                .put("endMonth",endMonth == null ? "" : endMonth)
                .put("levels",levels == null ? "" : levels)
                .put("size",size == null ? "" : size)
                .put("manutype",manutype == null ? "" : manutype)
                .put("isNewenergy",isNewenergy == null ? "" : isNewenergy).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200) {
            log.error("getCarSalesRankData {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getCarSalesRankData 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<List<CarSalesRankDTO>> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<CarSalesRankDTO>>>() {});

        if (returnValue == null) {
            log.warn("getCarSalesRankData 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0){
            log.error("getCarSalesRankData {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("getCarSalesRankData 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        return returnValue.getResult();
    }

    @Override
    public Map<Integer, Brandlist> getBrandlistHasVR() {

        Map<Integer, Brandlist> ret = new HashMap<>();

        String url = "http://panocms.api.autohome.com.cn/v1/pano/getbrandlisthasvr";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                .put("_appid", "car")
                .put("category", "car")
                .put("vrtype",2)
                .build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200) {
            log.error("getBrandlistHasVR {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getBrandlistHasVR 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<List<Brandlist>> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<Brandlist>>>() {});

        if (returnValue == null) {
            log.warn("getBrandlistHasVR 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0 ){
            log.error("getBrandlistHasVR {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("getBrandlistHasVR 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        for(Brandlist vr : returnValue.getResult()){
            ret.put(Integer.valueOf(vr.getBrandid()),vr);
        }

        return ret;
    }

    public List<BrandListNewResult> getBrandListNewToList(Integer state){

        String url = "http://carservice.autohome.com.cn/carapi/getbrandmenulist";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                .put("_appid", "car")
                .put("name", "v")
                .build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200) {
            log.error("getBrandListNewToList {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getBrandListNewToList 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<BrandListNewResult> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<BrandListNewResult>>() {});

        if (returnValue == null) {
            log.warn("getBrandListNewToList 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0){
            log.error("getBrandListNewToList {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("getBrandListNewToList 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if(state!=null&&state>0){
            returnValue.getResult().setBrandlist(returnValue.getResult().getBranditems().stream().filter(x->state.equals(x.getState())).collect(
                    Collectors.toList()));
        }

        Map<String, List<BrandlistNew>> groupletter = returnValue.getResult().getBranditems().stream().collect(Collectors.groupingBy(BrandlistNew::getFirstletter));
        List<BrandListNewResult> resultList = new ArrayList<>();
        BrandListNewResult brandListResult = new BrandListNewResult();
        for (Map.Entry<String, List<BrandlistNew>> entry : groupletter.entrySet()) {
            brandListResult = new BrandListNewResult();
            brandListResult.setFirstletter(entry.getKey());
            brandListResult.setBrandlist(entry.getValue());
            resultList.add(brandListResult);
        }

        return resultList;
    }

    public Map<Integer, SeriesListVr> getVrSeriesListByBrandId(Integer brandId){

        Map<Integer, SeriesListVr> ret = new HashMap<>();

        String url = "http://panocms.api.autohome.com.cn/v1/exterior/getserielisthasvrextbybrandid";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                .put("_appid", "car")
                .put("category", "car")
                .put("brandid", brandId.toString())
                .build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("getVrSeriesListByBrandId {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getVrSeriesListByBrandId 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<List<SeriesListVr>> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<SeriesListVr>>>() {});

        if (returnValue == null ) {
            log.warn("getVrSeriesListByBrandId 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0 ){
            log.error("getVrSeriesListByBrandId {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null ) {
            log.warn("getVrSeriesListByBrandId 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        for(SeriesListVr vr:returnValue.getResult()){
            ret.put(vr.getSeriesid(),vr);
        }

        return ret;
    }

    public NewSeriesNew getSeriesInfoByBrandAndStateNew ( String brandId,int order,Integer newenergy){

        String url = "http://carservice.autohome.com.cn/carapi/getserieslistbybrand";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                .put("_appid", "car")
                .put("brandid", brandId)
                .build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("getSeriesInfoByBrandAndStateNew {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getSeriesInfoByBrandAndStateNew 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<BrandNewNew> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<BrandNewNew>>() {});

        if (returnValue == null) {
            log.warn("getSeriesInfoByBrandAndStateNew 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0 ){
            log.error("getSeriesInfoByBrandAndStateNew {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("getSeriesInfoByBrandAndStateNew 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        List<BrandNewNew.Fctlist> sellfctlistnew = new ArrayList<>();
        List<BrandNewNew.Fctlist> unsellfctlistnew = new ArrayList<>();

        Map<String,List<BrandNewNew.Serieslist>> tags = new HashMap<>();


        String newenergyTab = "新能源";
        for (BrandNewNew.Fctlist fctitem : returnValue.getResult().getFctlist()) {
            BrandNewNew.Fctlist sellfctnew = new BrandNewNew.Fctlist();
            BrandNewNew.Fctlist unsellfctnew = new BrandNewNew.Fctlist();
            if (fctitem != null && fctitem.getSerieslist() != null && fctitem.getSerieslist().size() > 0) {
                List<BrandNewNew.Serieslist> sellserieslistnew = new ArrayList<>();
                List<BrandNewNew.Serieslist> unsellserieslistnew = new ArrayList<>();
                for (BrandNewNew.Serieslist seriesitem : fctitem.getSerieslist()) {

                    if(newenergy.equals(1)){
                        if(!seriesitem.getNewenergy().equals(1)) continue;
                    }
                    if (seriesitem.getSeriesState() == 40 || seriesitem.getSeriesState() == 0) {
                        unsellserieslistnew.add(seriesitem);
                    } else {
                        sellserieslistnew.add(seriesitem);
                    }

                }
                if (sellserieslistnew.size() > 0) {
                    sellfctnew.setFctid(fctitem.getFctid());
                    sellfctnew.setFctname(fctitem.getFctname());
                    sellfctnew.setSerieslist(sellserieslistnew);
                    sellfctlistnew.add(sellfctnew);
                }
                if (unsellserieslistnew.size() > 0) {
                    unsellfctnew.setFctid(fctitem.getFctid());
                    unsellfctnew.setFctname(fctitem.getFctname());
                    unsellfctnew.setSerieslist(unsellserieslistnew);
                    unsellfctlistnew.add(unsellfctnew);
                }
            }
        }

        NewSeriesNew newSeries = new NewSeriesNew();
        if (sellfctlistnew != null && sellfctlistnew.size() > 0) {
            newSeries.setSellseries(sellfctlistnew);
        }
        if (unsellfctlistnew != null && unsellfctlistnew.size() > 0) {
            newSeries.setUnsellseries(unsellfctlistnew);
        }
        newSeries.setTags(new ArrayList<>());
        List<String> tabs = new ArrayList<>(Arrays.asList("轿车","SUV","MPV","跑车","皮卡","微面","微卡","轻客",newenergyTab));
        if(newenergy.equals(1)){
            tabs.remove(newenergyTab);
        }


        switch (order){
            case 2:
                newSeries.getSellseries().forEach(x->{
                    x.getSerieslist().sort(Comparator.comparing(BrandNewNew.Serieslist::getMinprice));
                });
                newSeries.getUnsellseries().forEach(x->{
                    x.getSerieslist().sort(Comparator.comparing(BrandNewNew.Serieslist::getMinprice));
                });
                newSeries.getTags().forEach(x->{
                    x.getSeries().sort(Comparator.comparing(BrandNewNew.Serieslist::getMinprice));
                });
                break;
            case 3:
                newSeries.getSellseries().forEach(x->{
                    x.getSerieslist().sort(Comparator.comparing(BrandNewNew.Serieslist::getMinprice).reversed());
                });
                newSeries.getUnsellseries().forEach(x->{
                    x.getSerieslist().sort(Comparator.comparing(BrandNewNew.Serieslist::getMinprice).reversed());
                });
                newSeries.getTags().forEach(x->{
                    x.getSeries().sort(Comparator.comparing(BrandNewNew.Serieslist::getMinprice).reversed());
                });
                break;
            case 4:
                newSeries.getSellseries().forEach(x->{
                    x.getSerieslist().sort(Comparator.comparing(BrandNewNew.Serieslist::getRank));
                });
                newSeries.getUnsellseries().forEach(x->{
                    x.getSerieslist().sort(Comparator.comparing(BrandNewNew.Serieslist::getRank));
                });
                newSeries.getTags().forEach(x->{
                    x.getSeries().sort(Comparator.comparing(BrandNewNew.Serieslist::getRank));
                });
                break;
        }


        return newSeries;


    }

}
