package com.autohome.applet.service.wxapi;

import com.autohome.applet.dao.javaapi.model.wxapp.DetailModel;
import com.autohome.applet.model.dto.ReturnValue;

/**
 * Created by pengyi on 2016/12/15.
 */
public interface DetailService {
    /**
     * 最终页接口
     *
     * @param id
     * @param type      最终页类型
     * @param nocache   是否从缓存中查询(目前只有论坛最终页使用,默认走缓存接口)  0 有缓存    1无缓存
     * @param isRetainA 摘取富文本时候,是否保留a标签  0 不保留    1保留
     * @return
     */
    ReturnValue<DetailModel> detail(int id, int type, int nocache, int isRetainA);
}
