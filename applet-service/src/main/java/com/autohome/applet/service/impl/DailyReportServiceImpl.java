package com.autohome.applet.service.impl;

import com.autohome.applet.dao.javaapi.mapper.LeadBuyUseCarDailyRepoterDingdingMapper;
import com.autohome.applet.dao.javaapi.mapper.PanoramicBuyUseCarDailyRepoterDingdingMapper;
import com.autohome.applet.dao.javaapi.model.LeadBuyUseCarDailyRepoterDingding;
import com.autohome.applet.dao.javaapi.model.PanoramicBuyUseCarDailyRepoterDingding;
import com.autohome.applet.service.DailyReportService;
import com.autohome.applet.util.DingDingUtil;
import com.autohome.applet.util.JobLog;
import com.autonews.springboot.util.RedisClient;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.primitives.Doubles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DailyReportServiceImpl implements DailyReportService {

    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String ATTENTION_STRING_FORMAT = "其中，%s 请及时关注%n%n";

    @Autowired
    PanoramicBuyUseCarDailyRepoterDingdingMapper panoramicBuyUseCarDailyRepoterDingdingMapper;

    @Autowired
    LeadBuyUseCarDailyRepoterDingdingMapper leadBuyUseCarDailyRepoterDingdingMapper;

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    public static class DingRobot {

        private String groupApi;
        private String secret;

        public DingRobot(String groupApi, String secret) {
            this.groupApi = groupApi;
            this.secret = secret;
        }

        public String getGroupApi() {
            return groupApi;
        }

        public void setGroupApi(String groupApi) {
            this.groupApi = groupApi;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }
    }

    @Override
    public void dailyReport(LocalDate localDate, Map<String, List<DingRobot>> mapDingRobot, boolean force) {
        if (localDate == null) {
            localDate = LocalDate.now().minusDays(1);
        }

        JobLog.info("dailyReport for date " + localDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        List<DingRobot> lead = mapDingRobot.get("LeadBuyUseCarDaily");
        if (!CollectionUtils.isEmpty(lead)) {
            String leadBuyUseCarDaily = getLeadBuyUseCarDaily(localDate);
            if (StringUtils.hasText(leadBuyUseCarDaily)) {
                String dailyReportSentKey = getDailyReportSentKey(localDate, leadBuyUseCarDaily);
                JobLog.info("dailyReport lead redisKey="+ dailyReportSentKey);
                if (!force && redisClient.getValue(dailyReportSentKey) != null)  {
                    JobLog.info("dailyReport lead have send for content " + leadBuyUseCarDaily);
                    return;
                }
                JobLog.info("dailyReport lead for content " + leadBuyUseCarDaily);
                for (DingRobot dingRobot : lead) {
                    DingDingUtil.sendMarkdownMessage(dingRobot.getGroupApi() ,dingRobot.getSecret() , "数据日报", leadBuyUseCarDaily);
                }
                redisClient.setValue(dailyReportSentKey, "true", 1, TimeUnit.DAYS);
            } else {
                JobLog.info("dailyReport lead content is empty for " + localDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
        }
        List<DingRobot> panoramic = mapDingRobot.get("PanoramicBuyUseCarDaily");
        if (!CollectionUtils.isEmpty(panoramic)) {
            String panoramicBuyUseCarDaily = getPanoramicBuyUseCarDaily(localDate);
            if (StringUtils.hasText(panoramicBuyUseCarDaily)) {
                String dailyReportSentKey = getDailyReportSentKey(localDate, panoramicBuyUseCarDaily);
                JobLog.info("dailyReport panoramic redisKey="+ dailyReportSentKey);
                if (!force && redisClient.getValue(dailyReportSentKey) != null)  {
                    JobLog.info("dailyReport panoramic have send for content " + panoramicBuyUseCarDaily);
                    return;
                }
                JobLog.info("dailyReport panoramic for content " + panoramicBuyUseCarDaily);
                for (DingRobot dingRobot : panoramic) {
                    DingDingUtil.sendMarkdownMessage(dingRobot.getGroupApi(), dingRobot.getSecret(), "数据日报", panoramicBuyUseCarDaily);
                }
                redisClient.setValue(dailyReportSentKey, "true", 1, TimeUnit.DAYS);
            } else {
                JobLog.info("dailyReport panoramic content is empty for " + localDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
        }
    }

    private String getPanoramicBuyUseCarDaily(LocalDate localDate) {
        JobLog.info("getPanoramicBuyUseCarDaily " + (localDate == null));
        PanoramicBuyUseCarDailyRepoterDingding data = panoramicBuyUseCarDailyRepoterDingdingMapper.getReport(localDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        if (data != null) {
            String message = "【全景生态小程序昨日数据】%n%n" +
//                    "【整体流量】%s万 %n%n环比昨日%s 同比上周%s%n%n" +
//                    CLUE_STRING_FORMAT +
//                    "【整体线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【小程序流量】%s万 %n%n环比昨日%s 同比上周%s%n%n" +
                    "【小程序线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【小程序线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

                    "【小程序独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【小程序流量端独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【小程序线索端独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

                    "【对外合作流量】%s万 %n%n环比昨日%s 同比上周%s%n%n" +
                    "【高德流量】%s万 %n%n环比昨日%s 同比上周%s%n%n" +
                    "【对外合作线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【高德线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +

                    "【本月均值】" +
                    "小程序流量:%s万、小程序线索:%s条、小程序线索转化率:%s%%、" +
                    "小程序独号:%s条、小程序流量端独号转化率:%s%%、小程序有线索端独号转化率:%s%%、" +
                    "对外合作流量:%s万、高德流量:%s万、" +
                    "对外合作线索:%s条、高德线索:%s条 %n%n" +
                    " %n%n%s%n%n%n%n";

            String maxIncrease = getMaxFluctuation(ImmutableMap.<String, List<String>>builder()
//                    .put("整体线索", Arrays.asList(data.getTotalLeadChainratio(), data.getTotalLeadYearonyear()))
//                    .put("整体流量", Arrays.asList(data.getTotalFlowChainratio(), data.getTotalFlowYearonyear()))
                    .put("小程序线索", Arrays.asList(data.getMiniprogramLeadChainratio(), data.getMiniprogramLeadYearonyear()))
                    .put("小程序流量", Arrays.asList(data.getMiniprogramFlowChainratio(), data.getMiniprogramFlowYearonyear()))
                    .put("小程序独号", Arrays.asList(data.getMiniprogramPhoneChainRatio(), data.getMiniprogramPhoneYearOnYear()))
                    .put("对外合作流量", Arrays.asList(data.getCooperationFlowChainRatio(), data.getCooperationFlowYearOnYear()))
                    .put("高德流量", Arrays.asList(data.getGaodeFlowChainratio(), data.getGaodeFlowYearonyear()))
                    .put("对外合作线索", Arrays.asList(data.getCooperationLeadChainRatio(), data.getCooperationLeadYearOnYear()))
                    .put("高德线索", Arrays.asList(data.getGaodeLeadChainratio(), data.getGaodeLeadYearonyear()))
                    .build());

            message = String.format(message,
//                    //整体流量
//                    unitizeNumber2(data.getTotalFlowSum()), getHumanUndulation(data.getTotalFlowChainratio()),
//                    getHumanUndulation(data.getTotalFlowYearonyear()),
//                    //整体线索
//                    unitizeNumber2(data.getTotalLeadSum()), getHumanUndulation(data.getTotalLeadChainratio()), data.getTotalLeadChainratiodiff(),
//                    getHumanUndulation(data.getTotalLeadYearonyear()), data.getTotalLeadYearonyeardiff(),
//                    //整体线索转化率
//                    percentWithoutPercentSymbol(data.getTotalLeadrateSum()), getHumanUndulation(data.getTotalLeadrateChainratiodiff()), getHumanUndulation(data.getTotalLeadrateYearonyeardiff()),

                    // "【小程序流量】%s万 %n%n环比昨日%s 同比上周%s%n%n"
                    unitizeNumber(data.getMiniprogramFlowSum()),
                    getHumanUndulation(data.getMiniprogramFlowChainratio()),
                    getHumanUndulation(data.getMiniprogramFlowYearonyear()),
                    // "【小程序线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n"
                    unitizeNumber2(data.getMiniprogramLeadSum()),
                    getHumanUndulation(data.getMiniprogramLeadChainratio()),
                    getDefault(data.getMiniprogramLeadChainratiodiff()),
                    getHumanUndulation(data.getMiniprogramLeadYearonyear()),
                    getDefault(data.getMiniprogramLeadYearonyeardiff()),
                    // "【小程序线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n"
                    percentWithoutPercentSymbol(data.getMiniprogramLeadrateSum()),
                    getHumanUndulation(data.getMiniprogramLeadrateChainratiodiff()),
                    getHumanUndulation(data.getMiniprogramLeadrateYearonyeardiff()),

                    // "【小程序独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n"
                    unitizeNumber2(data.getMiniprogramPhoneSum()),
                    getHumanUndulation(data.getMiniprogramPhoneChainRatio()),
                    getDefault(data.getMiniprogramPhoneChainRatioDiff()),
                    getHumanUndulation(data.getMiniprogramPhoneYearOnYear()),
                    getDefault(data.getMiniprogramPhoneYearOnYearDiff()),

                    // "【小程序流量端独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n"
                    percentWithoutPercentSymbol(data.getMiniprogramFlowPhoneRate()),
                    getHumanUndulation(data.getMiniprogramFlowphoneRateChainRatioDiff()),
                    getHumanUndulation(data.getMiniprogramFlowphoneRateYearOnYearDiff()),

                    // "【小程序线索端独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n"
                    percentWithoutPercentSymbol(data.getMiniprogramLeadPhoneRate()),
                    getHumanUndulation(data.getMiniprogramLeadphoneRateChainRatioDiff()),
                    getHumanUndulation(data.getMiniprogramLeadphoneRateYearOnYearDiff()),

                    // "【对外合作流量】%s万 %n%n环比昨日%s 同比上周%s%n%n"
                    unitizeNumber(data.getCooperationFlowSum()),
                    getHumanUndulation(data.getCooperationFlowChainRatio()),
                    getHumanUndulation(data.getCooperationFlowYearOnYear()),

                    // "【高德流量】%s万 %n%n环比昨日%s 同比上周%s%n%n"
                    unitizeNumber(data.getGaodeFlowSum()),
                    getHumanUndulation(data.getGaodeFlowChainratio()),
                    getHumanUndulation(data.getGaodeFlowYearonyear()),

                    // "【对外合作线索】%s万条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n"
                    unitizeNumber2(data.getCooperationLeadSum()),
                    getHumanUndulation(data.getCooperationLeadChainRatio()),
                    getDefault(data.getCooperationLeadChainRatioDiff()),
                    getHumanUndulation(data.getCooperationLeadYearOnYear()),
                    getDefault(data.getCooperationLeadYearOnYearDiff()),

                    // "【高德线索】%s万条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n"
                    unitizeNumber2(data.getGaodeLeadSum()),
                    getHumanUndulation(data.getGaodeLeadChainratio()),
                    getDefault(data.getGaodeLeadChainratiodiff()),
                    getHumanUndulation(data.getGaodeLeadYearonyear()),
                    getDefault(data.getGaodeLeadYearonyeardiff()),

//                    //高德线索转化率
//                    percentWithoutPercentSymbol(data.getGaodeLeadrateSum()), getHumanUndulation(data.getGaodeLeadrateChainratiodiff()), getHumanUndulation(data.getGaodeLeadrateYearonyeardiff()),

                    // "【本月均值】"
                    // "小程序流量:%s万、小程序线索:%s万条、小程序线索转化率:%s%%、" +
                    // "小程序独号:%s万条、小程序流量端独号转化率:%s%%、小程序有线索端独号转化率:%s%%、" +
                    // "对外合作流量:%s万、高德流量:%s万、" +
                    // "对外合作线索:%s万条、高德线索:%s万条 %n%n" +
//                    unitizeNumber2(data.getTotalFlowMonthavg()), unitizeNumber2(data.getTotalLeadMonthavg()), percentWithoutPercentSymbol(data.getTotalLeadrateMonthavg()),
                    unitizeNumber(data.getMiniprogramFlowMonthavg()),
                    unitizeNumber2(data.getMiniprogramLeadMonthavg()),
                    percentWithoutPercentSymbol(data.getMiniprogramLeadrateMonthavg()),

                    unitizeNumber2(data.getMiniprogramPhoneMonthAvg()),
                    percentWithoutPercentSymbol(data.getMiniprogramFlowphoneRateMonthAvg()),
                    percentWithoutPercentSymbol(data.getMiniprogramLeadphoneRateMonthAvg()),

                    unitizeNumber(data.getCooperationFlowMonthAvg()), unitizeNumber(data.getGaodeFlowMonthavg()),
                    unitizeNumber2(data.getCooperationLeadMonthAvg()), unitizeNumber2(data.getGaodeLeadMonthavg()),
//                    percentWithoutPercentSymbol(data.getGaodeLeadrateMonthavg()),
                    StringUtils.hasText(maxIncrease) ? String.format(ATTENTION_STRING_FORMAT, maxIncrease) : ""

            );
            return message;
        }
        return "";
    }

    private String getLeadBuyUseCarDaily(LocalDate localDate) {
        LeadBuyUseCarDailyRepoterDingding data = this.leadBuyUseCarDailyRepoterDingdingMapper.getReport(localDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        JobLog.info("getLeadBuyUseCarDaily " + (data == null));
        if (data != null) {
            PanoramicBuyUseCarDailyRepoterDingding data2 = this.panoramicBuyUseCarDailyRepoterDingdingMapper.getReport(localDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
            String message = "【全景生态整体昨日线索数据】%n%n" +
                    "【小程序+PC+M线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【小程序+PC+M独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【App线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【App线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【App独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【App独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

                    "【小程序线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【小程序线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【小程序独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【小程序整体独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【小程序有线索独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

//                    "【直播线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
//                    "【直播线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

                    "【PC线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【PC线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【PC独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【PC独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

                    "【M线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【M线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【M独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
                    "【M独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +

//                    "【社区线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
//                    "【社区线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
//                    "【热聊线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n" +
//                    "【热聊线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n" +
                    "【本月均值】整体线索:%s条、整体独号:%s条、" +
                    "App线索:%s条、App线索转化率:%s%%、App独号:%s条、" +
                    "小程序线索:%s条、小程序线索转化率:%s%%、小程序独号:%s条、小程序整体独号转化率:%s%%、小程序有线索端独号转化率:%s%%、" +
//                    "直播线索:%s条、直播线索转化率:%s%%、社区线索:%s条、社区线索转化率:%s%%、热聊线索:%s条、热聊线索转化率:%s%%、" +
                    "PC线索:%s条、PC线索转化率:%s%%、PC独号:%s条、PC独号转化率:%s%%、" +
                    "M线索:%s条、M线索转化率:%s%%、【M独号】:%s条、【M独号转化率】%s%%%n%n" +
                    " %n%n%s%n%n%n%n";

            String maxIncrease = getMaxFluctuation(ImmutableMap.<String, List<String>>builder()
                    .put("小程序+PC+M线索", Arrays.asList(data.getTotalLeadChainratio(), data.getTotalLeadYearonyear()))
                    .put("小程序+PC+M独号", Arrays.asList(data.getTotalPhoneChainRatio(), data.getTotalPhoneYearOnYear()))
                    .put("小程序线索", Arrays.asList(data.getMiniprogramLeadChainratio(), data.getMiniprogramLeadYearonyear()))
                    .put("小程序独号", Arrays.asList(data.getMiniprogramPhoneChainRatio(), data.getMiniprogramPhoneYearOnYear()))
//                    .put("直播线索", Arrays.asList(data.getLiveLeadChainratio(), data.getLiveLeadYearonyear()))
                    .put("PC线索", Arrays.asList(data.getPcLeadChainratio(), data.getPcLeadYearonyear()))
                    .put("PC独号", Arrays.asList(data.getPcPhoneChainRatio(), data.getPcPhoneYearOnYear()))
                    .put("M线索", Arrays.asList(data.getmLeadChainratio(), data.getmLeadYearonyear()))
                    .put("M独号", Arrays.asList(data.getmPhoneChainRatio(), data.getmPhoneYearOnYear()))
//                    .put("社区线索", Arrays.asList(data.getCommunityLeadChainratio(), data.getCommunityLeadYearonyear()))
//                    .put("热聊线索", Arrays.asList(data.getHotchatLeadChainratio(), data.getHotchatLeadYearonyear()))
                    .build());

            message = String.format(message,
//                  // 小程序+PC+M线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getTotalLeadSum()),
                    getHumanUndulation(data.getTotalLeadChainratio()),
                    getDefault(data.getTotalLeadChainratiodiff()),
                    getHumanUndulation(data.getTotalLeadYearonyear()),
                    getDefault(data.getTotalLeadYearonyeardiff()),
                    // 【小程序+PC+M独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getTotalPhoneSum()),
                    getHumanUndulation(data.getTotalPhoneChainRatio()),
                    getDefault(data.getTotalPhoneChainRatioDiff()),
                    getHumanUndulation(data.getTotalPhoneYearOnYear()),
                    getDefault(data.getTotalPhoneYearOnYearDiff()),

                    // 【App线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getAppLeadSum()),
                    getHumanUndulation(data.getAppLeadChainratio()),
                    getDefault(data.getAppLeadChainratiodiff()),
                    getHumanUndulation(data.getAppLeadYearonyear()),
                    getDefault(data.getAppLeadYearonyeardiff()),
                    // App线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getAppLeadRate()),
                    getHumanUndulation(data.getAppLeadrateChainratiodiff()),
                    getHumanUndulation(data.getAppLeadrateYearonyeardiff()),

                    // 【App独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getAppPhoneSum()),
                    getHumanUndulation(data.getAppPhoneChainRatio()),
                    getDefault(data.getAppPhoneChainRatioDiff()),
                    getHumanUndulation(data.getAppPhoneYearOnYear()),
                    getDefault(data.getAppPhoneYearOnYearDiff()),
                    // 【App独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getAppPhoneRate()),
                    getHumanUndulation(data.getAppPhoneRateChainRatioDiff()),
                    getHumanUndulation(data.getAppPhoneRateYearOnYearDiff()),

                    // 【小程序线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getMiniprogramLeadSum()),
                    getHumanUndulation(data.getMiniprogramLeadChainratio()),
                    getDefault(data.getMiniprogramLeadChainratiodiff()),
                    getHumanUndulation(data.getMiniprogramLeadYearonyear()),
                    getDefault(data.getMiniprogramLeadYearonyeardiff()),
                    // 【小程序线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getMiniprogramLeadRate()),
                    getHumanUndulation(data.getMiniprogramLeadrateChainratiodiff()),
                    getHumanUndulation(data.getMiniprogramLeadrateYearonyeardiff()),
                    // 【小程序独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getMiniprogramPhoneSum()),
                    getHumanUndulation(data.getMiniprogramPhoneChainRatio()),
                    getDefault(data.getMiniprogramPhoneChainRatioDiff()),
                    getHumanUndulation(data.getMiniprogramPhoneYearOnYear()),
                    getDefault(data.getMiniprogramPhoneYearOnYearDiff()),
                    // 【小程序整体独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data2 != null ? data2.getMiniprogramFlowPhoneRate() : "0"),
                    getHumanUndulation(data2 != null ? data2.getMiniprogramFlowphoneRateChainRatioDiff() : "0"),
                    getHumanUndulation(data2 != null ? data2.getMiniprogramFlowphoneRateYearOnYearDiff() : "0"),
                    // 【小程序有线索独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data2 != null ? data2.getMiniprogramLeadPhoneRate() : "0"),
                    getHumanUndulation(data2 != null ? data2.getMiniprogramLeadphoneRateChainRatioDiff() : "0"),
                    getHumanUndulation(data2 != null ? data2.getMiniprogramLeadphoneRateYearOnYearDiff() : "0"),

//                    //直播线索
//                    unitizeNumber2(data.getLiveLeadSum()), getHumanUndulation(data.getLiveLeadChainratio()), data.getLiveLeadChainratiodiff(),
//                    getHumanUndulation(data.getLiveLeadYearonyear()), data.getLiveLeadYearonyeardiff(),
//                    //直播线索转化率--
//                    percentWithoutPercentSymbol(data.getLiveLeadRate()), getHumanUndulation(data.getLiveLeadrateChainratiodiff()), getHumanUndulation(data.getLiveLeadrateYearonyeardiff()),

                    // 【PC线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getPcLeadSum()),
                    getHumanUndulation(data.getPcLeadChainratio()),
                    getDefault(data.getPcLeadChainratiodiff()),
                    getHumanUndulation(data.getPcLeadYearonyear()),
                    getDefault(data.getPcLeadYearonyeardiff()),
                    // 【PC线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getPcLeadRate()),
                    getHumanUndulation(data.getPcLeadrateChainratiodiff()),
                    getHumanUndulation(data.getPcLeadrateYearonyeardiff()),
                    // 【PC独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getPcPhoneSum()),
                    getHumanUndulation(data.getPcPhoneChainRatio()),
                    getDefault(data.getPcPhoneChainRatioDiff()),
                    getHumanUndulation(data.getPcPhoneYearOnYear()),
                    getDefault(data.getPcPhoneYearOnYearDiff()),
                    // 【PC独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getPcPhoneRate()),
                    getHumanUndulation(data.getPcPhoneRateChainRatioDiff()),
                    getHumanUndulation(data.getPcPhoneRateYearOnYearDiff()),

                    // 【M线索】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getmLeadSum()),
                    getHumanUndulation(data.getmLeadChainratio()),
                    getDefault(data.getmLeadChainratiodiff()),
                    getHumanUndulation(data.getmLeadYearonyear()),
                    getDefault(data.getmLeadYearonyeardiff()),
                    // M线索转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getmLeadRate()),
                    getHumanUndulation(data.getmLeadrateChainratiodiff()),
                    getHumanUndulation(data.getmLeadrateYearonyeardiff()),
                    // 【M独号】%s条 %n%n环比昨日%s（%s）同比上周%s（%s）%n%n
                    unitizeNumber2(data.getmPhoneSum()),
                    getHumanUndulation(data.getmPhoneChainRatio()),
                    getDefault(data.getmPhoneChainRatioDiff()),
                    getHumanUndulation(data.getmPhoneYearOnYear()),
                    getDefault(data.getmPhoneYearOnYearDiff()),
                    // 【M独号转化率】%s%% %n%n比前一天%s 较比上周同期%s%n%n
                    percentWithoutPercentSymbol(data.getmPhoneRate()),
                    getHumanUndulation(data.getmPhoneRateChainRatioDiff()),
                    getHumanUndulation(data.getmPhoneRateYearOnYearDiff()),


//                    //社区线索
//                    unitizeNumber2(data.getCommunityLeadSum()), getHumanUndulation(data.getCommunityLeadChainratio()), data.getCommunityLeadChainratiodiff(),
//                    getHumanUndulation(data.getCommunityLeadYearonyear()), data.getCommunityLeadYearonyeardiff(),
//                    //社区线索转化率--
//                    percentWithoutPercentSymbol(data.getCommunityLeadRate()), getHumanUndulation(data.getCommunityLeadrateChainratiodiff()), getHumanUndulation(data.getCommunityLeadrateYearonyeardiff()),
//                    //热聊线索
//                    unitizeNumber2(data.getHotchatLeadSum()), getHumanUndulation(data.getHotchatLeadChainratio()), data.getHotchatLeadChainratiodiff(),
//                    getHumanUndulation(data.getHotchatLeadYearonyear()), data.getHotchatLeadYearonyeardiff(),
//                    //热聊线索转化率--
//                    percentWithoutPercentSymbol(data.getHotchatLeadRate()), getHumanUndulation(data.getHotchatLeadrateChainratiodiff()), getHumanUndulation(data.getHotchatLeadrateYearonyeardiff()),
                    //本月均值

                    // "【本月均值】整体线索:%s条、整体独号:%s条、"

                    // 【本月均值】整体线索:%s条、整体独号:%s条
                    unitizeNumber2(data.getTotalLeadMonthavg()),
                    unitizeNumber2(data.getTotalPhoneMonthAvg()),
                    // "App线索:%s条、App线索转化率:%s%%、App独号:%s条、"
                    unitizeNumber2(data.getAppLeadMonthavg()),
                    percentWithoutPercentSymbol(data.getAppLeadrateMonthavg()),
                    unitizeNumber2(data.getAppPhoneMonthAvg()),
                    // "小程序线索:%s条、小程序线索转化率:%s%%、小程序独号:%s条、小程序整体独号转化率:%s%%、小程序有线索端独号转化率:%s%%、"
                    unitizeNumber2(data.getMiniprogramLeadMonthavg()),
                    percentWithoutPercentSymbol(data.getMiniprogramLeadrateMonthavg()),
                    unitizeNumber2(data.getMiniprogramPhoneMonthAvg()),
                    percentWithoutPercentSymbol(data2 != null ? data2.getMiniprogramFlowphoneRateMonthAvg() : "0"),
                    percentWithoutPercentSymbol(data2 != null ? data2.getMiniprogramLeadphoneRateMonthAvg() : "0"),
//                    unitizeNumber2(data.getLiveLeadMonthavg()), percentWithoutPercentSymbol(data.getLiveLeadrateMonthavg()),
//                    unitizeNumber2(data.getCommunityLeadMonthavg()), percentWithoutPercentSymbol(data.getCommunityLeadrateMonthavg()),
//                    unitizeNumber2(data.getHotchatLeadMonthavg()), percentWithoutPercentSymbol(data.getHotchatLeadrateMonthavg()),
                    // "PC线索:%s条、PC线索转化率:%s%%、PC独号:%s条、PC独号转化率:%s%%、"
                    unitizeNumber2(data.getPcLeadMonthavg()),
                    percentWithoutPercentSymbol(data.getPcLeadrateMonthavg()),
                    unitizeNumber2(data.getPcPhoneMonthAvg()),
                    percentWithoutPercentSymbol(data.getPcPhoneRateMonthAvg()),
                    // "M线索:%s条、M线索转化率:%s%%、【M独号】:%s条、【M独号转化率】%s%%%n%n"
                    unitizeNumber2(data.getmLeadMonthavg()),
                    percentWithoutPercentSymbol(data.getmLeadrateMonthavg()),
                    unitizeNumber2(data.getmPhoneMonthAvg()),
                    percentWithoutPercentSymbol(data.getmPhoneRateMonthAvg()),
                    StringUtils.hasText(maxIncrease) ? String.format(ATTENTION_STRING_FORMAT, maxIncrease) : ""
            );
            return message;
        }
        return "";
    }

    /**
     * 获取变化较大的指标
     *
     * @param map
     * @return
     */
    private String getMaxFluctuation(Map<String, List<String>> map) {
        BigDecimal threshold = BigDecimal.valueOf(0.05);
        Map<String, List<BigDecimal>> entries = map.entrySet().stream().filter(entry -> !ObjectUtils.isEmpty(entry.getValue()) && entry.getValue().size() == 2 && entry.getValue().stream().allMatch(v -> StringUtils.hasText(v) && Doubles.tryParse(v) != null)).collect(Collectors.toMap(Map.Entry::getKey, p -> p.getValue().stream().map(BigDecimal::new).collect(Collectors.toList())));


        //环比涨幅较大
        String up1 = entries.entrySet().stream().filter(p -> p.getValue().get(0).compareTo(threshold) >= 0).map(Map.Entry::getKey).collect(Collectors.joining(","));
        //同步涨幅较大
        String up2 = entries.entrySet().stream().filter(p -> p.getValue().get(1).compareTo(threshold) >= 0).map(Map.Entry::getKey).collect(Collectors.joining(","));
        //环比跌幅较大
        String down1 = entries.entrySet().stream().filter(p -> p.getValue().get(0).compareTo(threshold.multiply(BigDecimal.valueOf(-1))) <= 0).map(Map.Entry::getKey).collect(Collectors.joining(","));
        //同步跌幅较大
        String down2 = entries.entrySet().stream().filter(p -> p.getValue().get(1).compareTo(threshold.multiply(BigDecimal.valueOf(-1))) <= 0).map(Map.Entry::getKey).collect(Collectors.joining(","));


        String upText = "<font color=\"#ff0000\">涨幅↑</font>";
        String downText = "<font color=\"#66cc00\">跌幅↓</font>";

        StringBuilder stringBuffer = new StringBuilder();
        if (StringUtils.hasText(up1)) {
            stringBuffer.append(String.format("%n%n【%s】 环比%s较大;", up1, upText));
        }
        if (StringUtils.hasText(up2)) {
            stringBuffer.append(String.format("%n%n【%s】 同比%s较大;", up2, upText));
        }
        if (StringUtils.hasText(down1)) {
            stringBuffer.append(String.format("%n%n【%s】 环比%s较大;", down1, downText));
        }
        if (StringUtils.hasText(down2)) {
            stringBuffer.append(String.format("%n%n【%s】 同比%s较大;", down2, downText));
        }
        return stringBuffer.toString();

    }


    /**
     * 转为以万为单位
     *
     * @param number
     * @return
     */
    private String unitizeNumber(String number) {
        if (StringUtils.hasText(number)) {
            return new BigDecimal(number).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        }
        return "";
    }

    /**
     * 转为以个为单位
     *
     * @param number
     */
    private String unitizeNumber2(String number) {
        if (StringUtils.hasText(number)) {
            return new DecimalFormat("#,##0").format(new BigDecimal(number));
        }
        return "";
    }

    /**
     * 小数转化为百分比
     *
     * @param number            小数
     * @param withPercentSymbol 转为百分数以后，是否跟随百分号
     * @return
     */
    private String percent(String number, boolean withPercentSymbol) {

        if (StringUtils.hasText(number)) {
            try {
                return new BigDecimal(number).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()
                        + (withPercentSymbol ? "%%" : "");
            } catch (Exception e) {
                return number;
            }
        }
        return "";
    }

    /**
     * 小数转化为百分比，不带百分号
     *
     * @param number
     * @return
     */
    private String percentWithoutPercentSymbol(String number) {
        return percent(number, false);
    }


    /**
     * 小数转化为涨跌表示的百分比
     * 波动为0%时，改为环比昨日无波动/比前一天变化较平稳
     * @param percentValue
     * @return
     */
    private String getHumanUndulation(String percentValue) {
        if (StringUtils.hasText(percentValue)) {
            String upText = "<font color=\"#ff0000\">上涨↑</font>";
            String downText = "<font color=\"#66cc00\">下跌↓</font>";

            int compared = new BigDecimal(percentValue).setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO);
            BigDecimal bigDecimal = new BigDecimal(percentValue).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);

            if (compared > 0) {
                return String.format("%s %s%%", upText, bigDecimal.stripTrailingZeros().toPlainString());
            } else if (compared < 0) {
                return String.format("%s %s%%", downText, bigDecimal.multiply(BigDecimal.valueOf(-1)).stripTrailingZeros().toPlainString());
            } else {
                return "不变"; // String.format("不变 %s%%", percentValue);
            }
        }
        return "--";
    }

    private String getDefault(String defaultValue) {
        if (StringUtils.isEmpty(defaultValue)) {
            return "--";
        }
        return defaultValue;
    }

    private String getDailyReportSentKey(LocalDate localDate, String msg) {
        return Joiner.on(":").join("applet:task:dailyreport", localDate.toString(), DigestUtils.md5DigestAsHex(msg.getBytes(StandardCharsets.UTF_8)));
    }
}
