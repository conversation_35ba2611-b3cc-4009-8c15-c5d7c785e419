package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.user.DecryptUserResultDto;
import com.autohome.applet.model.dto.user.UserInfoDto;
import com.autohome.applet.model.dto.usercenter.AutoUserInfo;
import com.autohome.applet.model.dto.usercenter.User;
import com.autohome.applet.model.dto.usercenter.UserInfoDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface UserCenterService {

    /**
     * 云闪付 获取/注册 手机号
     * 如果手机号没有注册，则进行注册然后返回用户id，如果已经注册了，则直接返回用户id
     *
     * @param mobilePhone
     * @return
     */
    User registerThenGetUser(String mobilePhone);

    String encryptPhone(String mobilePhone);

    /**
     * 获取用户信息
     * http://wiki.corpautohome.com/pages/viewpage.action?pageId=5767566
     *
     * @param userId
     * @return
     */
    User getUserInfoByUserId(long userId);

    User getUserInfoByPhone(String phone);

    AutoUserInfo getAutoUser(int userId);

    ReturnValue<?> RegOrLoginByMobileCode(HttpServletRequest request);

    DecryptUserResultDto decryptPcPopClubCookie(String auth);

    String getPhoneNumber(int myUid, String clientIp);

    List<UserInfoDto> getUserInfos(String userIds, int myUid);

    ReturnValue<?> getInfoByauthorid(String appid, Integer page_size, String authorId, String infoType, String search_after, String v, String timestamp, String sign, int isSelf);

    ReturnValue<?> getMyStatistics(String appid, Integer author_id);

    ReturnValue<?> getuserinfolist(String appid, String useridlist);

    List<UserInfoDTO> getUserInfoList(List<Integer> userIds);
}
