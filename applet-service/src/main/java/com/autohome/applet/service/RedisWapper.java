package com.autohome.applet.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class RedisWapper {

    @Autowired
    @Qualifier("lightappTemplate")
    RedisTemplate<String, String> rt;

    public Boolean hasKey(String key) {
        return rt.hasKey(key);
    }

    public void rPush(String key, List<String> values, long timeout, TimeUnit timeUnit) {
        ListOperations<String, String> listOperations = rt.opsForList();
        StringRedisSerializer keySerializer = (StringRedisSerializer) rt.getKeySerializer();
        Jackson2JsonRedisSerializer<String> valueSerializer = (Jackson2JsonRedisSerializer<String>) rt.getValueSerializer();
        byte[] serialize = keySerializer.serialize(key);
        if (serialize == null) {
            return;
        }
        listOperations.getOperations().executePipelined((RedisCallback<?>) (connection) -> {
            values.stream().map(valueSerializer::serialize).forEach(it -> connection.rPush(serialize, it));
            connection.pExpire(serialize, TimeoutUtils.toMillis(timeout, timeUnit));
            return null;
        });
    }

    public List<String> range(String key, int begin, int end) {
        return rt.opsForList().range(key, begin, end);
    }

}
