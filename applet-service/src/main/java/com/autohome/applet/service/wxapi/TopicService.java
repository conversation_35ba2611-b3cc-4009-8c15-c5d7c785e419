package com.autohome.applet.service.wxapi;

import com.autohome.applet.dao.javaapi.model.wxapp.DetailModel;
import com.autohome.applet.model.dto.ReturnValue;

/**
 * Topic Service
 *
 * <AUTHOR>
 * @date 2024/05/06
 */
public interface TopicService {

    /**
     * isRetainA 摘取富文本时候,是否保留a标签  0 不保留    1保留
     */
    ReturnValue<DetailModel> topicDetail(Integer topicid, int nocache, int isRetainA);

}
