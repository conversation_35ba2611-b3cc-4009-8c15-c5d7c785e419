package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.javaapi.ShadingWordService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class ShadingWordServiceImpl implements ShadingWordService {

    @Autowired
    @Qualifier("lightapp")
    RedisClient redisClient;

    @Override
    public ReturnValue<?> getSerachForShadingWord(Integer tabType) {
        String key = "applet:api:api_search_getShadingWord";
        String value = redisClient.getValue(key);
        if (value != null) {
            return JacksonHelper.deserialize(value, ReturnValue.class);
        } else {
            final String taburl1 = "https://sou.api.autohome.com.cn/search/api/search_initialize/v1?deviceid=512c47f3068370e830e186a9597c4312ab2faadc&perscont=1";

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(taburl1);

            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return ReturnValue.buildErrorResult(1001, "源接口请求失败");
            }
            String body = httpResult.getBody();
            redisClient.setValue(key, body, 1, TimeUnit.HOURS);

            return JacksonHelper.deserialize(body, ReturnValue.class);
        }
    }
}
