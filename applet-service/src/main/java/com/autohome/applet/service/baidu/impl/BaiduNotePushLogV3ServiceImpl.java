package com.autohome.applet.service.baidu.impl;

import com.autohome.applet.dao.javaapi.mapper.BaiduNotePushLogV3Mapper;
import com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.model.enums.CityEnum;
import com.autohome.applet.service.baidu.BaiduNotePushLogV3Service;
import com.autohome.applet.util.BaiduNoteUtil;
import com.autohome.applet.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BaiduNotePushLogV3ServiceImpl implements BaiduNotePushLogV3Service {

    @Autowired
    private BaiduNotePushLogV3Mapper baiduNotePushLogV3Mapper;
    @Override
    public int handleBaiduNoteExcel(List<BaiduNoteUtil.BaiduNoteExcel> baiduNoteExcelList) {
        Date nowDate = new Date();
        String pushDate = DateHelper.serialize(nowDate, DateHelper.DATEFORMAT_ONLY_DATE);
        int size = 0;

        for (BaiduNoteUtil.BaiduNoteExcel baiduNoteExcel : baiduNoteExcelList) {
            if (baiduNoteExcel.getExId() == null || baiduNoteExcel.getExId().isEmpty()) {
                continue;
            }
            int exId = (int) Double.parseDouble(baiduNoteExcel.getExId());
            BaiduNotePushLogV3 byExId = baiduNotePushLogV3Mapper.getByExId(exId);

            BaiduNotePushLogV3 baiduNotePushLogV3 = new BaiduNotePushLogV3();
            baiduNotePushLogV3.setExId(exId);
            baiduNotePushLogV3.setTitle(baiduNoteExcel.getTitle());
            baiduNotePushLogV3.setContent(baiduNoteExcel.getCotent());
            baiduNotePushLogV3.setImgs(String.join(",", baiduNoteExcel.getImgList()));
            /**
             * 发布时间随机范围
             * 2025年4月1日-4月15日（8:00-21:00）
             * */
            String randomTime = DateHelper.getRandomTime("2025-04-01", "2025-04-15", 8, 21);
            if(!StringUtils.isEmpty(randomTime)){
                baiduNotePushLogV3.setPushTime(DateHelper.deserialize(randomTime));//按当天算
                baiduNotePushLogV3.setPushDate(DateHelper.serialize(baiduNotePushLogV3.getPushTime(), DateHelper.DATEFORMAT_ONLY_DATE));//按当天算
            }
            baiduNotePushLogV3.setPushState(0);//未同步百度
            baiduNotePushLogV3.setIsDel(0);//暂时都是未删除状态， 后续如果有删除需求，需要在excel中增加删除列，并存储到数据表中
            // 作者信息,固定
            baiduNotePushLogV3.setAuthorName(baiduNoteExcel.getAuthorName());
            baiduNotePushLogV3.setAuthorImg(baiduNoteExcel.getAuthorImg());
            baiduNotePushLogV3.setOpAccount("JOB导入");
            baiduNotePushLogV3.setApprovalStatus(1);//审核（此处需求设置审核通过，后续如果有后台管理页面，按步骤赋值）
            //ip城市固定在北京
//                CityEnum randomCity = CityEnum.getRandomCity();
            CityEnum randomCity = CityEnum.BEIJING;
            baiduNotePushLogV3.setCityId(randomCity.getCityId());
            baiduNotePushLogV3.setCityName(randomCity.getCityName());
            baiduNotePushLogV3.setProvinceId(randomCity.getProvinceId());
            baiduNotePushLogV3.setProvinceName(randomCity.getProvinceName());
            if (byExId == null) {
                //新增
                if (baiduNotePushLogV3Mapper.insert(baiduNotePushLogV3) > 0) {
                    size++;
                }else {
                    log.error("数据导入,添加失败,exid={}",baiduNotePushLogV3.getExId());
                }
            }else {
                baiduNotePushLogV3.setModifiedSTime(nowDate);
                //更新
                if (baiduNotePushLogV3Mapper.updateByExId(baiduNotePushLogV3) > 0) {
                    size++;
                }else {
                    log.error("数据导入,更新失败,exid={}",baiduNotePushLogV3.getExId());
                }
            }
        }
        log.info("需导入{}条数据，已导入{}条数据",baiduNoteExcelList.size(),size);
        return size;
    }

    @Override
    public List<BaiduNotePushLogV3> getNoPushList() {
        return baiduNotePushLogV3Mapper.getBaiduNote(0);
    }

    @Override
    public Integer getNoPushCount() {
        return baiduNotePushLogV3Mapper.getNoPushCount(0);
    }

    @Override
    public List<PushResourceFeedDataDTO> getPushResourceFeedDataDTOList() {
        List<BaiduNotePushLogV3> noPushList = this.getNoPushList();
        if(!CollectionUtils.isEmpty(noPushList)){
            return null;
        }
        List<PushResourceFeedDataDTO> pushResourceFeedDataDTOList = new ArrayList<>();
        for (BaiduNotePushLogV3 baiduNotePushLogV3 : noPushList) {
            PushResourceFeedDataDTO dto = new PushResourceFeedDataDTO();
            dto.setAuthor(baiduNotePushLogV3.getAuthorName());
            dto.setTitle(baiduNotePushLogV3.getTitle());

            pushResourceFeedDataDTOList.add(dto);
        }
        return pushResourceFeedDataDTOList;
    }

    @Override
    public int updateByExId(BaiduNotePushLogV3 baiduNotePushLogV3) {
        return baiduNotePushLogV3Mapper.updateByExId(baiduNotePushLogV3);
    }
}
