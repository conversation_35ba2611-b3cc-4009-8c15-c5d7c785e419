package com.autohome.applet.service.javaapi;

import java.util.Map;

public interface MotoService {

    /**
     * 1、https://carservice.autohome.com.cn/motorapi/getspecgrouplistbyseriesid?seriesId=10006289&_appid=car
     * 2、https://cars.app.autohome.com.cn/carext/motoseriessummary/tabcardinfo?pm=1&brandid=10000001&seriesid=10000444&tabids=2&pluginversion=11.40.0
     * 3、http://moto-dealerapi.corpautohome.com/dealer/moto/dealerInfos?pageSize=20&pageIndex=1&seriesId=10006289&brandId=10000028&cityId=652900&sortType=2&lat=39.89786626175921&lng=116.39881865193846&_appid=car
     * 3.1、http://moto-dealerapi.corpautohome.com/dealer/moto/nonlocal/dealerInfos?seriesId=10006289&cityId=652900&_appid=car
     * 4、https://carservice.autohome.com.cn/motorapi/spec_paramlistbyspeclist?seriesId=10006289&specIds=10270244&_appid=car
     * @param seriesId
     * @return
     */
    Map<String, Object> seriesTabAndContent(int brandId, int seriesId, String specIds, String cityId, double lat, double lng);
}
