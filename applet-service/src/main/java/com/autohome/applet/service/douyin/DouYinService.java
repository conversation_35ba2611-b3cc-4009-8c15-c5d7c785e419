package com.autohome.applet.service.douyin;

import com.autohome.applet.dao.javaapi.model.MiniTokenValue;
import com.autohome.applet.dao.javaapi.model.SweetCity;
import com.autohome.applet.model.dto.douyin.DouYinBaseReturn;

import java.util.List;
import java.util.Map;

public interface DouYinService {
    public Map<String, String> selectAccessTokenByMiniType(Integer minitype);
    public DouYinBaseReturn uploadSiteMap(List<String> pagePaths, int pushFlag);
    public DouYinBaseReturn delSiteMap(List<String> pagePaths, int pushFlag);
    public Object getHangQing(Map<String, Object> param,String url,int flag);
//    public Object getHangQingDel(Map<String, Object> param,String url);
    public Object getWenDa(Map<String, Object> param,String url, int flag);
    public void addDouYinSiteMapRedis(String keyHeader,int source,int flag,int type,int num);
    /**
     * 百度的report
     * */
    public void addBaiduReportRedis(String keyHeader, String date,int source,int flag,int type,int num);
    /**
     * 删除key， 重复执行时，需要清理
     * */
    public void removeBaiduReportRedis(String keyHeader, String date, int source, int flag, int type);

    public Object report();
    public Object baiduReport();
    /**
     * 日期列表汇总
     * */
    public Object baiduReport(List<String> dateList);
    public Object douYinSiteMapFillIn() ;
}
