package com.autohome.applet.service.wxapi.impl;

import com.autohome.applet.dao.javaapi.model.wxapp.WxUser;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.wxapi.IUserService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;


/**
 * UserService
 *
 * <AUTHOR>
 * @date 2024/05/06
 */
@Service
public class UserService implements IUserService {
    private String baseUrl = "http://user.api.autohome.com.cn/";
    private final String USER_APP_ID = "wxapp.club";
    public static final String USER_DEFAULT_AVATAR = "https://x.autoimg.cn/club/wxapp/male_default.png";
    public static final String USER_AVATAR_PREFIX = "https://i1.autoimg.cn/album/userheaders";

    @Autowired
    private HttpService httpService;

    @Override
    public WxUser getUserInfo(int userId) {
        String pathUrl = "api/userInfo/GetUserInfoDyn";
        HashMap<String, Object> map = new HashMap<>();
        map.put("_appid", USER_APP_ID);
        map.put("userids", String.valueOf(userId));
        ReturnValue<JsonNode> rt = httpService.httpGetFor(baseUrl + pathUrl, map, new TypeReference<ReturnValue<JsonNode>>() {});
        if (rt != null && rt.getReturncode() == 0 && rt.getResult() != null) {
            JsonNode result = rt.getResult();
            if (result.has(0)) {
                JsonNode user = result.get(0);
                WxUser userInfo = new WxUser();
                userInfo.setNickName(user.path("Nickname").asText());
                userInfo.setAvatar(user.path("HeadImage").asText());
                if (userInfo.getAvatar() == null || userInfo.getAvatar().trim().isEmpty()) {userInfo.setAvatar(USER_DEFAULT_AVATAR);}
                else if (!userInfo.getAvatar().startsWith("http://") && !userInfo.getAvatar().startsWith("https://")) {
                    userInfo.setAvatar(USER_AVATAR_PREFIX + userInfo.getAvatar());
                }
                return userInfo;
            }
        }
        throw new BusinessException(rt.getMessage());
    }
}
