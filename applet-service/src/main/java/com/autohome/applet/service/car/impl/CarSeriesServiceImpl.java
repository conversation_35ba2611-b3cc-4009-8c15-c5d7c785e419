package com.autohome.applet.service.car.impl;


import com.autohome.applet.config.CarApiUrl;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.car.portrayal.Upb2Item;
import com.autohome.applet.model.dto.car.portrayal.Upb2Result;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.javaapi.CarService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.MapBuilder;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 车系service
 * @author: WangBoWen
 * @date: 2023-11-27
 **/
@Service
@Slf4j
public class CarSeriesServiceImpl implements CarSeriesService {

    @Autowired
    private CarSeriesService self;

    @Autowired
    private CarApiUrl carApiUrl;

    @Autowired
    private CarService carService;

    @Autowired
    HttpService httpService;

    @Autowired
    @Resource(name = "lightapp")
    private RedisClient redisClient;

    /**
     * 获取询价卡片列表
     * @param userId 用户id
     * @return 询价卡9个
     */
    @Override
    public List<EnquiryCardDTO> getEnquiryCardList(int userId,String uuid, String appkey) {

        Supplier<List<Integer>> getUserLikeCarSeriesIdsFunction = () -> self.getUserLikeCarSeriesIds(userId,uuid,appkey);

        Supplier<List<Integer>> getHotCarSeriesIdsFunction = () -> self.getHotCarSeriesIds();

        CompletableFuture<List<Integer>> userCarIdsFuture = CompletableFuture
                .supplyAsync(getUserLikeCarSeriesIdsFunction);

        CompletableFuture<List<Integer>> hotCarIdsFuture = CompletableFuture
                .supplyAsync(getHotCarSeriesIdsFunction);

        CompletableFuture.allOf(userCarIdsFuture,hotCarIdsFuture).join();

        List<CarSeriesInfoDTO> carSeriesInfo ;

        try {

            List<Integer> userCarSeriesIds = userCarIdsFuture.get();

            List<Integer> hotCarSeriesIds = hotCarIdsFuture.get();

            List<Integer> carSeriesIds;

            if (!CollectionUtils.isEmpty(userCarSeriesIds) && userCarSeriesIds.size() < 9) {

                //两者取并集(去重复)
                userCarSeriesIds.removeAll(hotCarSeriesIds);
                hotCarSeriesIds.addAll(userCarSeriesIds);

                carSeriesIds = hotCarSeriesIds;

            }else if (CollectionUtils.isEmpty(userCarSeriesIds)){
                carSeriesIds = hotCarSeriesIds;
            }else {
                carSeriesIds = userCarSeriesIds;
            }

            if (carSeriesIds.size() > 9){
                carSeriesInfo = self.getCarSeriesInfo(carSeriesIds.subList(0,9));
            }else {
                carSeriesInfo = self.getCarSeriesInfo(carSeriesIds);
            }

        }catch (Exception e){
            log.error("getEnquiryCardList error",e);
            throw new BusinessException("源接口调用失败",1001);
        }

        List<EnquiryCardDTO> enquiryCardDTOList = new ArrayList<>();

        String seriesIds = carSeriesInfo.stream().map(CarSeriesInfoDTO :: getSeriesid).map(String::valueOf).collect(Collectors.joining(","));
        List<WhitePhotoSerieslistDTO> whitePhotoSerieslist = getWhitePhotoSerieslist(seriesIds);

        for (CarSeriesInfoDTO carSeriesInfoDTO : carSeriesInfo) {
            EnquiryCardDTO enquiryCardDTO = new EnquiryCardDTO();
            enquiryCardDTO.setCarSeriesId(carSeriesInfoDTO.getSeriesid());
            enquiryCardDTO.setCarSeriesName(carSeriesInfoDTO.getName());
            enquiryCardDTO.setLowestGuidePrice(carSeriesInfoDTO.getMinprice());
            for (WhitePhotoSerieslistDTO whitePhotoSerieslistDTO : whitePhotoSerieslist) {
                if (carSeriesInfoDTO.getSeriesid() == whitePhotoSerieslistDTO.getId()){
                    enquiryCardDTO.setImgUrl(whitePhotoSerieslistDTO.getPnglogo());
                    break;
                }
            }
            //enquiryCardDTO.setImgUrl(carSeriesInfoDTO.getPic());
            enquiryCardDTOList.add(enquiryCardDTO);

        }


        return enquiryCardDTOList;
    }

    /**
     * 获取询价卡添加白底图
     * @param seriesIds
     * @return
     */
    private List<WhitePhotoSerieslistDTO> getWhitePhotoSerieslist(String seriesIds) {
        final String url = "https://lapp.api.autohome.com.cn/cloud/live/dealer/getSeriesWhitePhotoBySeriesIds";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "user");
        param.put("serieslist", seriesIds);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }

        ReturnValue<WhitePhotoDTO> userResp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<WhitePhotoDTO>>() {
        });

        return userResp.getResult().getSerieslist();
    }

    /**
     * 获取用户喜好车系
     * @param userId 用户id
     * @return 车系id
     */
    @Override
    public List<Integer> getUserLikeCarSeriesIds(int userId,String uuid,String appkey) {

        Upb2Result result = carService.getUpb2(uuid, appkey, String.valueOf(userId));

        List<Upb2Item> userViewCarSeriesList = result.getUserViewCarSeriesList();

        if (CollectionUtils.isEmpty(userViewCarSeriesList)) {
            return null;
        }

        List<Integer> userLikeSeriesIds = userViewCarSeriesList.stream().map(Upb2Item::getCode).collect(Collectors.toList());

        return userLikeSeriesIds;
    }

    /**
     * 获取热门车系id
     * @return 热门车系id
     */
    @Override
    public List<Integer> getHotCarSeriesIds() {

        return carService.getHotCar("series", 9, null, null);
    }

    /**
     * 根据车系id 获取车系信息
     * @param seriesIds 车系id
     * @return 车系信息
     */
    @Override
    public List<CarSeriesInfoDTO> getCarSeriesInfo(List<Integer> seriesIds) {

        String url =  carApiUrl.getGetCarSeriesInfo();

        String seriesIdsStr = seriesIds.toString().replace("[","").replace("]","").replace(" ","");

        Map<String, Object> fields = MapBuilder.<String, Object>newInstance()
                .put("serieslist",seriesIdsStr)
                .put("_appId","car")
                .build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200){
            log.error("getHotCarSeriesIds {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getHotCarSeriesIds 源接口调用返回null , url:{} , 参数:{} ",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<CarSeriesResultInfoDTO> returnValue= JacksonHelper
                .deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarSeriesResultInfoDTO>>() {});

        if (returnValue == null){
            log.warn("getHotCarSeriesIds 源接口调用返回null , url:{} , 参数:{} ",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0 ){
            log.error("getHotCarSeriesIds {} is error result: {},seriesIds:{}",url, JacksonHelper.serialize(httpResult),seriesIds);
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("getHotCarSeriesIds 源接口调用返回null , url:{} , 参数:{} ",url,JacksonHelper.serialize(fields));
            return null;
        }

        CarSeriesResultInfoDTO carSeriesResultInfoDto = returnValue.getResult();

        List<CarSeriesInfoDTO> carSeriesInfoList = carSeriesResultInfoDto.getList();

        if (CollectionUtils.isEmpty(carSeriesInfoList)) {
            return null;
        }

        return carSeriesInfoList;
    }

    @Override
    public List<SeriesInfoDto> getAllSeriesInfos() {
        String key = "applet:base:car:series:all";
        String obj = redisClient.get(key);
        if(StringUtils.isNotEmpty(obj)){
            List<SeriesInfoDto> seriesInfoDtoList = JacksonHelper.deserialize(obj, new TypeReference<List<SeriesInfoDto>>() {});
            return seriesInfoDtoList;
        }
        return refreshAllSeriesInfos();
    }

    @Override
    public List<FctItemListDto.FctItem> getAllFctInfos() {
        String key = "applet:base:car:fct:all";
        String obj = redisClient.get(key);
        if(StringUtils.isNotEmpty(obj)){
            List<FctItemListDto.FctItem> fctItemList = JacksonHelper.deserialize(obj, new TypeReference<List<FctItemListDto.FctItem>>() {});
            return fctItemList;
        }
        return refreshAllFctItems();
    }

    @Override
    public List<SeriesInfoDto> getAllSeriesInfosFormCache(List<Integer> seriesIds) {
        List<String> keys = seriesIds.stream().map(k -> String.format(CarSeriesService.CACHE_SERIES_INFO_KEY, k)).collect(Collectors.toList());
        Map<String, SeriesInfoDto> map = redisClient.getAll(keys, SeriesInfoDto.class);
        if(map == null){
            return null;
        }
        return map.values().stream().collect(Collectors.toList());
    }

    @Override
    public List<AllBrandInfoDto.BrandInfoDto> getAllBrandInfos() {
        String key = "applet:base:car:brand:all";
        String obj = redisClient.get(key);
        if(StringUtils.isNotEmpty(obj)){
            List<AllBrandInfoDto.BrandInfoDto> brandsInfoDtoList = JacksonHelper.deserialize(obj, new TypeReference<List<AllBrandInfoDto.BrandInfoDto>>() {});
            return brandsInfoDtoList;
        }
        return refreshAllBrandInfos();
    }

    @Override
    public List<SeriesInfoDto> refreshAllSeriesInfos() {
        String key = "applet:base:car:series:all";
        List<SeriesInfoDto> seriesInfoDtoList = getAllSeriesInfosFromUrlNoCache();
        if(CollectionUtils.isNotEmpty(seriesInfoDtoList)){
            //存入redis
            redisClient.set(key, JacksonHelper.serialize(seriesInfoDtoList), 3, TimeUnit.DAYS);
        }
        return seriesInfoDtoList;
    }

    @Override
    public List<SeriesInfoDto> refreshAllSeriesInfos(List<SeriesInfoDto> seriesInfoDtoList) {
        String key = "applet:base:car:series:all";
        //存入redis
        redisClient.set(key, JacksonHelper.serialize(seriesInfoDtoList), 3, TimeUnit.DAYS);

        return seriesInfoDtoList;
    }

    @Override
    public List<FctItemListDto.FctItem> refreshAllFctItems() {
        String key = "applet:base:car:fct:all";
        List<FctItemListDto.FctItem> fctItemList = getAllFctInfosFromUrl();
        //存入redis
        redisClient.set(key, JacksonHelper.serialize(fctItemList), 3, TimeUnit.DAYS);

        return fctItemList;
    }

    @Override
    public List<AllBrandInfoDto.BrandInfoDto> refreshAllBrandInfos() {
        String key = "applet:base:car:brand:all";
        List<AllBrandInfoDto.BrandInfoDto> brandInfoDtoList = getAllBrandInfosFromUrl();
        if(CollectionUtils.isNotEmpty(brandInfoDtoList)){
            //存入redis
            redisClient.set(key, JacksonHelper.serialize(brandInfoDtoList), 3, TimeUnit.DAYS);
        }
        return brandInfoDtoList;
    }

    @Override
    public List<SeriesInfoDto> getAllSeriesInfosFromUrlNoCache() {
        Map<String, Object> param = new HashMap<>();
        try {
            AllSeriesInfoDto allSeriesInfoDtoReturnValue = httpService.httpGetForReturnValue(carApiUrl.getGetallseries(), param
                    , new TypeReference<ReturnValue<AllSeriesInfoDto>>() {
                    });
            if(allSeriesInfoDtoReturnValue == null){
                return null;
            }
            List<SeriesInfoDto> seriesInfoDtoList = allSeriesInfoDtoReturnValue.getSeriesitems();
            return seriesInfoDtoList;
        } catch (BusinessException e) {
            log.error("cacheAllSeriesInfos", e);
        }
        return null;
    }

    public List<AllBrandInfoDto.BrandInfoDto> getAllBrandInfosFromUrl() {
        Map<String, Object> param = new HashMap<>();
        try {
            AllBrandInfoDto allBrandInfoDto = httpService.httpGetForReturnValue(carApiUrl.getGetallbrands(), param
                    , new TypeReference<ReturnValue<AllBrandInfoDto>>() {
                    });
            if(allBrandInfoDto == null){
                return null;
            }
            List<AllBrandInfoDto.BrandInfoDto> brandInfoDtoList = allBrandInfoDto.getBranditems();
            return brandInfoDtoList;
        } catch (BusinessException e) {
            log.error("cacheAllBrandInfos", e);
        }
        return null;
    }

    public List<FctItemListDto.FctItem> getAllFctInfosFromUrl() {
        Map<String, Object> param = new HashMap<>();
        try {
            FctItemListDto fctItemListDto = httpService.httpGetForReturnValue(carApiUrl.getGetallfcts(), param
                    , new TypeReference<ReturnValue<FctItemListDto>>() {
                    });
            if(fctItemListDto == null || CollectionUtils.isEmpty(fctItemListDto.getFctitems())){
                return null;
            }
            List<FctItemListDto.FctItem> fctItemList = fctItemListDto.getFctitems();
            return fctItemList;
        } catch (BusinessException e) {
            log.error("getAllFctInfosFromUrl", e);
        }
        return null;
    }
}
