package com.autohome.applet.service.constant;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class InquiryModel {
    //关联车系
    //新车车系卡片
    private static final String OGCNewCarSeries = ",整车,电动车车闻,新车预告,新车效果图/假想图,海外谍照,车辆OTA资讯,新车图解,新车官图,新车上市/价格,新车首次发布,新车现已到店,最萌选车差,选车导购,车闻中心,进口新车,电动车导购,电动车技术,新能源,热点事件挖掘,智能网联,数据分析,零部件,新技术,车闻轶事,商业模式,新鲜技术解读,政策法规,企业营收,资本运作,技术设计,汽车文化,技术讲堂,市场分析,热点追踪,技术体验,新产品计划,国内谍照,";
    private static final String PGCNewCarSeries = ",新车资讯,新车国内上市,新车国内发布预告,新车国内预售,提车报告,买车/提车,新车国内发布,热点首发,提车分享,国内新车上市,国内新车发布,配置升级,美容装饰,驾驶技巧,越野,自驾游,用车攻略,改装车,汽车维修,车险,改装,车友会,用车技巧与优缺点,汽车美容,赛事,汽车技术,技术解析,明星座驾,汽车影视,汽车文化,汽车娱乐,热点舆情与公共事件,好物推荐,静态体验,智能安全体验,智能与主动安全,动力与行驶,生产与制造,结构材料与碰撞安全,影视游戏,结构材料解读,新能源特有技术,新车海外上市,新车海外实拍快讯,新车海外计划,新车海外预售,新车国内计划,新车海外上市预告,新车海外谍照,新车海外发布预告,新车海外发布,新车海外到店,海外新车,";
    private static final String UGCNewCarSeries = ",新车价格,现场实拍,新车感受,买车分享,日常随拍,质量反馈,汽车写真,日常记录,多车对比,选车建议,上海车展,车展,汽车实拍,新车发布,提车作业,新能源,车展车模,优缺点分析,改装清单,问题描述,自驾游,性能改装,维修方案,改装升级,汽车装饰,非性能改装,维修价格,事故记录,加价改装,活动信息,城市美食,避坑指南,手办展示,模型评测,装备推荐,好物展示,晒娃,电价/电耗分享,";
    //新车查成交价卡片
    private static final String OGCNewCarTransactionPrice = ",配置/售价调整,同价选谁,中型车试驾评测,SUV试驾评测,编辑体验记,试驾评测,AH数据,后市场,用车宝典,油价变动,用车养车,用车指南,轮胎解读,用车资讯,召回,召回碰撞,销量榜-销量解读,行业视角,产销数据,,销量榜-品牌动态,企业动向,带你懂行业,行业报告,行业政策,行业/政策,产业动态,行业动态,初步海选,设计解码,车市消费洞察,行情分析,";
    private static final String PGCNewCarTransactionPrice = ",评测体验,单车评测,车价变动,专业测试,汽车用品,用车养车,汽车保养,行业动态,碰撞安全,行业资讯,行情,品牌动态,产经信息,旅游,市场调查,名人明星,生活类,行业热点,品牌热点,旅行,";
    private static final String UGCNewCarTransactionPrice = ",优惠信息,单车评测,车辆价格,购车总价,车品,车品种草,用车经验,用车感受,用车成本,油耗分享,保养项目,用车故事,保养价格,露营实拍,短途自驾,随拍,车友聚会,模型展示,旅游,户外游,风景展示,日常分享,亲子游,自拍,人物展示,钓鱼实记,生活写真,街拍,城市景点,生活兴趣,行业信息,航拍,";
    //预约试驾
    private static final String OGCBookingTestDrive = ",试驾体验,";
    private static final String PGCBookingTestDrive = ",多车体验,单车体验,双车体验,单车试驾,动态体验,";
    private static final String UGCBookingTestDrive = ",到店看车,玩车,静态体验,驾驶体验,";
    //二手车车源卡片
    private static final String OGCUsedCar = ",经典名车,汽车生活,经典设计,";
    private static final String PGCUsedCar = ",汽车生活,历史,车型历史,用车成本,汽车历史,行业历史,";
    private static final String UGCUsedCar = ",买卖信息,";
    //CPS购车福利
    private static final String OGCCPS =",SUV导购,30-50万车型导购,20-30万车型导购,";
    private static final String PGCCPS =",多车导购,单车导购,海选导购,双车导购,车型导购,";
    //分期特惠
    private static final String OGCInstallmentDiscount = ",中国品牌,国产新车,";
    private static final String PGCInstallmentDiscount = ",汽车金融,汽车保险,";
    //摩托车卡片
    private static final String UGCMotorcycle = ",摩托保养,摩托车旅行,摩托车改装,";

    //无关联车系
    //卖车服务
    private static final String OGCSellingCars = ",轮胎解读,用车资讯,召回,召回碰撞,";
    private static final String PGCSellingCars = ",美容装饰,驾驶技巧,越野,自驾游,用车攻略,改装车,汽车维修,车险,改装,车友会,用车技巧与优缺点,汽车美容,";
    private static final String UGCSellingCars = ",改装清单,问题描述,自驾游,性能改装,维修方案,改装升级,汽车装饰,非性能改装,维修价格,事故记录,加价改装,买卖信息,";
    //油卡充值
    private static final String OGCOilCard = ",后市场,用车宝典,油价变动,用车养车,用车指南,";
    private static final String PGCOilCard = ",汽车用品,用车养车,汽车保养,用车成本,";
    private static final String UGCOilCard = ",车品,车品种草,用车经验,用车感受,用车成本,油耗分享,保养项目,用车故事,保养价格,露营实拍,短途自驾,";
    //降价榜单
    private static final String OGCPrice = ",配置/售价调整,同价选谁,中型车试驾评测,SUV试驾评测,编辑体验记,试驾评测,AH数据,热点事件挖掘,智能网联,数据分析,零部件,新技术,车闻轶事,商业模式,新鲜技术解读,政策法规,企业营收,资本运作,技术设计,汽车文化,技术讲堂,市场分析,热点追踪,技术体验,SUV导购,30-50万车型导购,20-30万车型导购,";
    private static final String PGCPrice = ",赛事,汽车技术,技术解析,明星座驾,汽车影视,汽车文化,汽车娱乐,热点舆情与公共事件,好物推荐,静态体验,智能安全体验,智能与主动安全,动力与行驶,生产与制造,结构材料与碰撞安全,影视游戏,结构材料解读,评测体验,单车评测,车价变动,专业测试,多车导购,单车导购,海选导购,双车导购,车型导购,";
    private static final String UGCPrice = ",活动信息,城市美食,避坑指南,手办展示,模型评测,装备推荐,好物展示,晒娃,优惠信息,单车评测,车辆价格,购车总价,";
    //销量榜单
    private static final String OGCSalesVolume = ",整车,电动车车闻,车辆OTA资讯,最萌选车差,选车导购,车闻中心,销量榜-销量解读,行业视角,产销数据,,销量榜-品牌动态,企业动向,带你懂行业,行业报告,行业政策,行业/政策,产业动态,行业动态,初步海选,设计解码,车市消费洞察,行情分析,试驾体验,经典名车,汽车生活,经典设计,中国品牌,国产新车,";
    private static final String PGCSalesVolume = ",提车报告,买车/提车,提车分享,配置升级,汽车历史,行业动态,碰撞安全,行业资讯,行情,品牌动态,产经信息,旅游,市场调查,名人明星,生活类,行业热点,品牌热点,行业历史,旅行,多车体验,单车体验,双车体验,单车试驾,动态体验,汽车生活,历史,车型历史,汽车金融,汽车保险,";
    private static final String UGCSalesVolume = ",随拍,车友聚会,模型展示,旅游,户外游,风景展示,日常分享,亲子游,自拍,人物展示,钓鱼实记,生活写真,街拍,城市景点,生活兴趣,行业信息,航拍,现场实拍,买车分享,日常随拍,质量反馈,汽车写真,日常记录,多车对比,选车建议,上海车展,车展,汽车实拍,提车作业,车展车模,优缺点分析,摩托保养,摩托车旅行,摩托车改装,到店看车,玩车,静态体验,驾驶体验,";
    //续航榜单
    private static final String OGCRangeChart = ",电动车导购,电动车技术,新能源,";
    private static final String PGCRangeChart = ",新能源特有技术,";
    private static final String UGCRangeChart = ",电价/电耗分享,新能源,";
    //上市新车
    private static final String OGCNewCar = ",新产品计划,国内谍照,新车预告,新车效果图/假想图,海外谍照,新车图解,新车官图,新车上市/价格,新车首次发布,新车现已到店,进口新车,";
    private static final String PGCNewCar = ",新车资讯,新车国内上市,新车国内发布预告,新车国内预售,新车海外上市,新车海外实拍快讯,新车海外计划,新车海外预售,新车国内计划,新车海外上市预告,新车海外谍照,新车海外发布预告,新车海外发布,新车海外到店,海外新车,新车国内发布,热点首发,国内新车上市,国内新车发布,";
    private static final String UGCNewCar = ",新车价格,新车感受,新车发布,";


    private static final List<InquiryItemModel> inquiryItemModelList1 = new ArrayList<>();
    private static final List<InquiryItemModel> inquiryItemModelList2 = new ArrayList<>();

    static{
        inquiryItemModelList1.add(new InquiryItemModel("OGC", "新车车系卡片", OGCNewCarSeries));
        inquiryItemModelList1.add(new InquiryItemModel("PGC", "新车车系卡片", PGCNewCarSeries));
        inquiryItemModelList1.add(new InquiryItemModel("UGC", "新车车系卡片", UGCNewCarSeries));

        inquiryItemModelList1.add(new InquiryItemModel("OGC", "新车查成交价卡片", OGCNewCarTransactionPrice));
        inquiryItemModelList1.add(new InquiryItemModel("PGC", "新车查成交价卡片", PGCNewCarTransactionPrice));
        inquiryItemModelList1.add(new InquiryItemModel("UGC", "新车查成交价卡片", UGCNewCarTransactionPrice));

        inquiryItemModelList1.add(new InquiryItemModel("OGC", "预约试驾", OGCBookingTestDrive));
        inquiryItemModelList1.add(new InquiryItemModel("PGC", "预约试驾", PGCBookingTestDrive));
        inquiryItemModelList1.add(new InquiryItemModel("UGC", "预约试驾", UGCBookingTestDrive));

        inquiryItemModelList1.add(new InquiryItemModel("OGC", "二手车车源卡片", OGCUsedCar));
        inquiryItemModelList1.add(new InquiryItemModel("PGC", "二手车车源卡片", PGCUsedCar));
        inquiryItemModelList1.add(new InquiryItemModel("UGC", "二手车车源卡片", UGCUsedCar));

        inquiryItemModelList1.add(new InquiryItemModel("OGC", "CPS购车福利", OGCCPS));
        inquiryItemModelList1.add(new InquiryItemModel("PGC", "CPS购车福利", PGCCPS));

        inquiryItemModelList1.add(new InquiryItemModel("OGC", "分期特惠", OGCInstallmentDiscount));
        inquiryItemModelList1.add(new InquiryItemModel("PGC", "分期特惠", PGCInstallmentDiscount));

        inquiryItemModelList1.add(new InquiryItemModel("UGC", "摩托车卡片", UGCMotorcycle));


        inquiryItemModelList2.add(new InquiryItemModel("OGC", "卖车服务", OGCSellingCars));
        inquiryItemModelList2.add(new InquiryItemModel("PGC", "卖车服务", PGCSellingCars));
        inquiryItemModelList2.add(new InquiryItemModel("UGC", "卖车服务", UGCSellingCars));

        inquiryItemModelList2.add(new InquiryItemModel("OGC", "油卡充值", OGCOilCard));
        inquiryItemModelList2.add(new InquiryItemModel("PGC", "油卡充值", PGCOilCard));
        inquiryItemModelList2.add(new InquiryItemModel("UGC", "油卡充值", UGCOilCard));

        inquiryItemModelList2.add(new InquiryItemModel("OGC", "降价榜单", OGCPrice));
        inquiryItemModelList2.add(new InquiryItemModel("PGC", "降价榜单", PGCPrice));
        inquiryItemModelList2.add(new InquiryItemModel("UGC", "降价榜单", UGCPrice));

        inquiryItemModelList2.add(new InquiryItemModel("OGC", "销量榜单", OGCSalesVolume));
        inquiryItemModelList2.add(new InquiryItemModel("PGC", "销量榜单", PGCSalesVolume));
        inquiryItemModelList2.add(new InquiryItemModel("UGC", "销量榜单", UGCSalesVolume));

        inquiryItemModelList2.add(new InquiryItemModel("OGC", "续航榜单", OGCRangeChart));
        inquiryItemModelList2.add(new InquiryItemModel("PGC", "续航榜单", PGCRangeChart));
        inquiryItemModelList2.add(new InquiryItemModel("UGC", "续航榜单", UGCRangeChart));

        inquiryItemModelList2.add(new InquiryItemModel("OGC", "上市新车", OGCNewCar));
        inquiryItemModelList2.add(new InquiryItemModel("PGC", "上市新车", PGCNewCar));
        inquiryItemModelList2.add(new InquiryItemModel("UGC", "上市新车", UGCNewCar));

    }

    public static String getNewTag (String value, InquiryTypeEnum typeEnum, int type){
        value = "," + value + ",";
        List<InquiryItemModel> tmpInquiryItemModelList = null;
        if(type == 1){
            tmpInquiryItemModelList = inquiryItemModelList1.stream().filter(o ->{
                return o.getType().equals(typeEnum.getCode());
            }).collect(Collectors.toList());
        }
        else {
            tmpInquiryItemModelList = inquiryItemModelList2.stream().filter(o ->{
                return o.getType().equals(typeEnum.getCode());
            }).collect(Collectors.toList());
        }

        for (InquiryItemModel inquiryItemModel : tmpInquiryItemModelList) {
            if(inquiryItemModel.getValue().indexOf(value) > -1)
                return inquiryItemModel.getNewTag();
        }
        return tmpInquiryItemModelList.get(0).getNewTag();
    }

    public static class InquiryItemModel{
        private String type;
        private String newTag;
        private String value;

        public InquiryItemModel(String type, String newTag, String value) {
            this.type = type;
            this.newTag = newTag;
            this.value = value;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getNewTag() {
            return newTag;
        }

        public void setNewTag(String newTag) {
            this.newTag = newTag;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public enum InquiryTypeEnum{
        OGC("OGC", "OGC覆盖标签"),
        PGC("PGC", "PGC覆盖标签"),
        UGC("UGC", "UGC覆盖标签");
        private String code;
        private String info;

        InquiryTypeEnum(String code, String info) {
            this.code = code;
            this.info = info;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

    }
}
