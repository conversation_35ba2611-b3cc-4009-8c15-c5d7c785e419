package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.model.dto.CodeMsg;
import com.autohome.applet.model.dto.PageInfo;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.dealer.SpecMinPrice;
import com.autohome.applet.model.dto.netcoreapi.spec.*;
import com.autohome.applet.service.netcoreapi.SpecService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class SpecServiceImpl implements SpecService {

    @Override
    public SpecConfigV2 carParamPkConfigV2(String specParams, String cityId, int showCps, String requestId
            , String positionId, String deviceId, String cpsPositionId, int showUsedPrice, int seriesId, int type) {
        if (StringUtils.isEmpty(specParams)) {
            return null;
        }

        // 获取基本参数
        CompletableFuture<SpecParams> paramListTask = CompletableFuture.supplyAsync(() -> {
            //根据多个车型id获取多个参数信息
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("speclist", specParams);
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_paramlistbyspeclist.ashx", param);
            if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                ReturnValue<SpecParams> deserialize = JacksonHelper.deserialize(httpGet.getBody()
                        , new TypeReference<ReturnValue<SpecParams>>() {
                        });
                if (deserialize != null && deserialize.getReturncode() == 0) {
                    return deserialize.getResult();
                }
            }
            return null;
        });

        // 获取配置参数
        CompletableFuture<SpecParams> configListTask = CompletableFuture.supplyAsync(() -> {
            //根据多个车型id获取配置(合并)列表
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("speclist", specParams);
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/carprice/Config_GetListBySpecList.ashx", param);
            if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                ReturnValue<SpecParams> deserialize = JacksonHelper.deserialize(httpGet.getBody()
                        , new TypeReference<ReturnValue<SpecParams>>() {
                        });
                if (deserialize != null && deserialize.getReturncode() == 0) {
                    return deserialize.getResult();
                }
            }
            return null;
        });

        // 获取经销商报价
        CompletableFuture<List<SpecMinPrice>> dealerPriceTask = CompletableFuture.supplyAsync(() -> {
            if ("0".equals(cityId)) {
                return null;
            }
            //车型经销商价格
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "dealer");
            param.put("cityId", cityId);
            param.put("specIds", specParams);
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/car/spec/getMinPriceSpecCity", param);
            if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                ReturnValue<PageInfo<SpecMinPrice>> deserialize = JacksonHelper.deserialize(httpGet.getBody()
                        , new TypeReference<ReturnValue<PageInfo<SpecMinPrice>>>() {
                        });
                if (deserialize != null && deserialize.getReturncode() == 0 && deserialize.getResult() != null) {
                    return deserialize.getResult().getList();
                }
            }
            return null;
        });

        // 取二手车价格，（车系页进来 and 需要展示二手车价格）
        CompletableFuture<SpecUsedPrice> usedPriceTask = CompletableFuture.supplyAsync(() -> {
            if (seriesId == 0 || showUsedPrice != 1) {
                return null;
            }
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("seriesid", seriesId);
            param.put("pid", 320000);
            param.put("cid", 320100);
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://api.assess.che168.com/trend/specseriesassess", param);
            if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                ReturnValue<SpecUsedPrice> deserialize = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<SpecUsedPrice>>() {
                });
                if (deserialize != null && deserialize.getReturncode() == 0) {
                    return deserialize.getResult();
                }
            }
            return null;
        });

        // 获取车型基础信息
        CompletableFuture<List<SpecBaseInfo>> specBaseInfoTask = CompletableFuture.supplyAsync(() -> {
            //根据多个车型id获取车型基本信息
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("specids", specParams);
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/CarPrice/Spec_BaseInfoBySpecIds.ashx", param);
            if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                ReturnValue<SpecBaseInfo.SpecItemList> deserialize = JacksonHelper.deserialize(httpGet.getBody()
                        , new TypeReference<ReturnValue<SpecBaseInfo.SpecItemList>>() {
                        });
                if (deserialize != null && deserialize.getReturncode() == 0 && deserialize.getResult() != null) {
                    return deserialize.getResult().getSpecItems();
                }
            }
            return null;
        });

        // 获取优惠信息
        CompletableFuture<List<CpsPriceItem>> carCPSListTask = specBaseInfoTask.thenApply((Function<List<SpecBaseInfo>, List<CpsPriceItem>>) specItems -> {
            if ((StringUtils.isEmpty(requestId) || StringUtils.isEmpty(positionId)) && showCps != 1) {
                return null;
            }

            if (specItems == null) {
                return null;
            }

            Set<String> seriesIdSet = specItems.stream().map(SpecBaseInfo::getSeriesId).map(String::valueOf).collect(Collectors.toSet());
            String seriesIdsParam = String.join(",", seriesIdSet);

            List<CpsPriceItem> cpsPriceList = new ArrayList<>();
            if (!StringUtils.isEmpty(requestId) && !StringUtils.isEmpty(positionId)) {
                Map<String, RecActivityInfo> map = new HashMap<>();
                for (String sId : seriesIdSet) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("requestId", requestId);
                    param.put("positionId", positionId);
                    param.put("cityId", cityId);
                    param.put("seriesId", sId);
                    HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("https://autoapi.autohome.com.cn/ypttd/yjc/api/recommend/param/config", param);
                    if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                        CodeMsg<RecActivityInfo> deserialize = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<CodeMsg<RecActivityInfo>>() {
                        });
                        if (deserialize != null && deserialize.getResult() != null) {
                            map.put(sId, deserialize.getResult());
                        }
                    }
                }
                for (SpecBaseInfo specBaseInfoItem : specItems) {
                    RecActivityInfo activityInfo = map.get(String.valueOf(specBaseInfoItem.getSeriesId()));
                    if (activityInfo != null ) {
                        cpsPriceList.add(new CpsPriceItem(
                                specBaseInfoItem.getSpecId(),
                                activityInfo.getTitle() != null ? activityInfo.getTitle() : "-",
                                activityInfo.getUrl() != null ? activityInfo.getUrl() : "",
                                activityInfo.getExtendInfo(),
                                1
                        ));
                    } else {
                        cpsPriceList.add(new CpsPriceItem(
                                specBaseInfoItem.getSpecId(),
                                "-",
                                "",
                                null,
                                1
                        ));
                    }
                }
                if (showCps == 1 && cpsPriceList.stream().allMatch(item -> "-".equals(item.getValue()))) {
                    return GetCpsInfoList2(cityId, specItems, seriesIdsParam, cpsPositionId);
                }
            } else {
                return GetCpsInfoList2(cityId, specItems, seriesIdsParam, cpsPositionId);
            }
            return cpsPriceList;
        });

        // 查二手车信息,type 0:不判断是否存在二手车，1：判断是否存在二手车
        CompletableFuture<CarSearchDto> search = CompletableFuture.supplyAsync(() -> {
            if (type == 1){
                Map<String, Object> param = new HashMap<>();
                param.put("_appid", "miniprogram");
                param.put("orderid", 0);
                param.put("seriesid", seriesId);
                param.put("pageindex", 1);
                param.put("pagesize", 5);
                param.put("cityid", cityId);
                param.put("specid", specParams.split(",")[0]);
                HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://api2scrn.lf.corpautohome.com/AutoApp/car/Search.ashx", param);
                if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                    ReturnValue<CarSearchDto> deserialize = JacksonHelper.deserialize(httpGet.getBody()
                            , new TypeReference<ReturnValue<CarSearchDto>>() {
                            });
                    if (deserialize != null && deserialize.getReturncode() == 0 && deserialize.getResult() != null) {
                        return deserialize.getResult();
                    }
                }
            }
            return null;
        });

        try {
            return CompletableFuture.allOf(paramListTask, configListTask, dealerPriceTask, usedPriceTask, carCPSListTask, search).thenApply(unused -> {
                try {
                    SpecParams tmpModelParamList = paramListTask.get();
                    SpecParams tmpModelConfigList = configListTask.get();
                    List<SpecMinPrice> tmpModelPriceList = dealerPriceTask.get();
                    SpecUsedPrice specUsedPrice = usedPriceTask.get();
                    CarSearchDto listElement = search.get();
                    if (type == 1 && listElement!= null){
                        List<CarSearchDto.ListElement> listElementList = listElement.getList();
                        List<Integer> specidList = null;
                        if (!CollectionUtils.isEmpty(listElementList)){
                            specidList = listElementList.stream().map(CarSearchDto.ListElement::getSpecid).filter(n -> n != 0).collect(Collectors.toList());
                        }
                        if (CollectionUtils.isEmpty(specidList)){
                            specUsedPrice = null;
                        }
                    }
                    List<SpecBaseInfo> specBaseInfoList = specBaseInfoTask.get();
                    List<CpsPriceItem> tmpModelCarCPSList = carCPSListTask.get();

                    if (tmpModelParamList == null) {
                        return new SpecConfigV2();
                    }

                    //车型名称
                    List<SpecParams.ValueItem> titleList = new ArrayList<>();
                    // 一级tab列表【基本参数、被动安全、主动安全...】
                    List<OneTabName> oneTabName = new ArrayList<>();
                    List<CList> cList = new ArrayList<>();

                    // 基本参数
                    for (SpecParams.ParamTypeItem item : tmpModelParamList.getParamTypeItems()) {
                        List<TwoTabName> twoTabNameList = new ArrayList<>();
                        List<TwoList> twoList = new ArrayList<>();
                        if ("基本参数".equals(item.getName())) {
                            // 给车型基础信息填充经销商报价
                            // 【参考价】
                            if (specBaseInfoList != null) {
                                List<Object> dealerPriceList = new ArrayList<>();
                                Map<Integer, SpecMinPrice> specMinPriceMap;
                                if (!"0".equals(cityId) && !CollectionUtils.isEmpty(tmpModelPriceList)) {
                                    specMinPriceMap = tmpModelPriceList.stream()
                                            .collect(Collectors.toMap(SpecMinPrice::getSpecId, it -> it));
                                } else {
                                    specMinPriceMap = Collections.emptyMap();
                                }
                                for (SpecBaseInfo specInfo : specBaseInfoList) {
                                    SpecMinPrice priceInfo = specMinPriceMap.get(specInfo.getSpecId());
                                    if (priceInfo != null) {
                                        dealerPriceList.add(SpecMinPriceDto1.by(priceInfo));
                                    } else if (specUsedPrice != null && specUsedPrice.getMinPrice() > 0) {
                                        dealerPriceList.add(SpecMinPriceDto3.by(specInfo, specUsedPrice));
                                    } else {
                                        dealerPriceList.add(SpecMinPriceDto2.by(specInfo, "——"));
                                    }
                                }

                                twoTabNameList.add(new TwoTabName("参考价(元)", false, false));
                                twoList.add(new TwoList("参考价(元)", dealerPriceList, false, false));
                            } else {
                                return null;
                            }

                            // 【购车优惠 or 超级补贴】
                            if (!CollectionUtils.isEmpty(tmpModelCarCPSList)) {
                                List<String> cpsValuesList = new ArrayList<>();

                                boolean isSuperGave = false;
                                for (CpsPriceItem carCpsItem : tmpModelCarCPSList) {
                                    if (!"-".equals(carCpsItem.getValue())) {
                                        cpsValuesList.add(carCpsItem.getValue());
                                    }
                                    if (carCpsItem.getCpsType() == 2) {
                                        isSuperGave = true;
                                    }
                                }

                                boolean isSame = new HashSet<>(cpsValuesList).size() == 1;
                                boolean isEmpty = cpsValuesList.size() == 0;

                                String cpsName = "购车优惠";
                                if (isSuperGave) {
                                    cpsName = "超级补贴";
                                }
                                String finalCpsName = cpsName;
                                twoTabNameList.add(new TwoTabName(finalCpsName, isSame, isEmpty));
                                twoList.add(new TwoList(finalCpsName, tmpModelCarCPSList, isSame, isEmpty));
                            }

                            // 车型名称
                            titleList = item.getParamItems().stream().filter(it -> "车型名称".equals(it.getName()))
                                    .flatMap(it -> it.getValueItems().stream()).collect(Collectors.toList());
                            Map<Integer, String> titleMap = titleList.stream().collect(Collectors.toMap(SpecParams.ValueItem::getSpecId, SpecParams.ValueItem::getValue));

                            // 【其他属性 for 基本参数】
                            for (SpecParams.ParamItem tabItem : item.getParamItems()) {
                                if ("车型名称".equals(tabItem.getName())) {
                                    continue;
                                }
                                boolean isSame = false; // 相同
                                boolean isEmpty = false; // 暂无

                                Map<Integer, String> tabValueMap = tabItem.getValueItems().stream()
                                        .collect(Collectors.toMap(SpecParams.ValueItem::getSpecId, SpecParams.ValueItem::getValue));
                                HashSet<String> valueSet = new HashSet<>(tabValueMap.values());
                                if (valueSet.size() == 1) {
                                    if (valueSet.stream().anyMatch("-"::equals)) {
                                        isEmpty = true;
                                    } else {
                                        isSame = true;
                                    }
                                }

                                List<SpList> spList = new ArrayList<>();
                                for (SpecParams.ValueItem valueItem : tabItem.getValueItems()) {
                                    if ("厂商指导价(元)".equals(tabItem.getName())) {
                                        String specName = titleMap.get(valueItem.getSpecId());
                                        spList.add(new SpList2(valueItem.getSpecId(), valueItem.getValue(), specName));
                                    } else {
                                        spList.add(new SpList(valueItem.getSpecId(), valueItem.getValue()));
                                    }
                                }
                                twoTabNameList.add(new TwoTabName(tabItem.getName(), isSame, isEmpty));
                                twoList.add(new TwoList(tabItem.getName(), spList, isSame, isEmpty));
                            }

                            oneTabName.add(new OneTabName(item.getName(), twoTabNameList));
                            cList.add(new CList(item.getName(), twoList));
                        }
                    }

                    // 配置类型参数
                    for (SpecParams.ConfigTypeItem item : tmpModelConfigList.getConfigTypeItems()) {
                        List<TwoTabName> twoTabNameList = new ArrayList<>();
                        List<TwoList> twoList = new ArrayList<>();

                        for (SpecParams.ParamItem tabItem : item.getConfigItems()) {

                            boolean isSame = false; // 相同
                            boolean isEmpty = false; // 暂无

                            Set<String> valueSet = tabItem.getValueItems().stream().map(SpecParams.ValueItem::getValue).collect(Collectors.toSet());
                            if (valueSet.size() == 1) { // 相同
                                if (valueSet.contains("-")) { //暂无
                                    isEmpty = true;
                                } else {
                                    isSame = true;
                                }
                            }

                            List<SpList> spList = tabItem.getValueItems().stream().map(SpList::by).collect(Collectors.toList());
                            twoTabNameList.add(new TwoTabName(tabItem.getName(), isSame, isEmpty));
                            twoList.add(new TwoList(tabItem.getName(), spList, isSame, isEmpty));
                        }

                        oneTabName.add(new OneTabName(item.getName(), twoTabNameList));
                        cList.add(new CList(item.getName(), twoList));
                    }
                    return new SpecConfigV2(titleList, oneTabName, cList);
                } catch (InterruptedException |
                        ExecutionException e) {
                    e.printStackTrace();
                }
                return null;
            }).get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<SpecBaseInfo> specBaseInfoList(String specIds) {
        //根据多个车型id获取车型基本信息
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("specids", specIds);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/CarPrice/Spec_BaseInfoBySpecIds.ashx", param);
        if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
            ReturnValue<SpecBaseInfo.SpecItemList> deserialize = JacksonHelper.deserialize(httpGet.getBody()
                    , new TypeReference<ReturnValue<SpecBaseInfo.SpecItemList>>() {
                    });
            if (deserialize != null && deserialize.getReturncode() == 0 && deserialize.getResult() != null) {
                return deserialize.getResult().getSpecItems();
            }
        }
        return null;
    }

    private static List<CpsPriceItem> GetCpsInfoList2(String cityId, List<SpecBaseInfo> specBaseInfoList, String seriesIdsParam, String cpsPositionId) {
        List<CpsPriceItem> cpsPriceList = new ArrayList<>();

        //购车优惠-CPS
        //https://openapi.autohome.com.cn/autohome/dealer/api/lq/dealerorderc/cpsProduct/getFacSkuListByShowWhere
        //http://dealer.api.terra.corpautohome.com/dealerorderc/cpsoper/listSeriesAd?_appId=dealer&cityId=110100&positionId=110&seriesIds=18,3862

        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "lightapp");
        param.put("positionId", cpsPositionId);
        param.put("seriesIds", seriesIdsParam);
        param.put("cityId", cityId);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/dealerorderc/cpsoper/listSeriesAd", param);
        if (httpGet.getStatusCode() != 200 || httpGet.getBody() == null) {
            return Collections.emptyList();
        }
        ReturnValue<List<SkuInfo>> deserialize = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<List<SkuInfo>>>() {
        });
        if (deserialize == null || deserialize.getReturncode() != 0 || CollectionUtils.isEmpty(deserialize.getResult())) {
            return Collections.emptyList();
        }
        Map<Integer, SkuInfo> skuInfoMap = deserialize.getResult().stream().collect(Collectors.toMap(SkuInfo::getSeriesId, it -> it));

        for (SpecBaseInfo specBaseInfo : specBaseInfoList) {
            SkuInfo matchCpsItem = skuInfoMap.get(specBaseInfo.getSeriesId());
            if (matchCpsItem != null) {
                String price = "-";
                if ("120".equals(cpsPositionId)) {
                    if (matchCpsItem.getAdTxtList().length >= 1) {
                        price = matchCpsItem.getAdTxtList()[0];
                    }
                }
                cpsPriceList.add(new CpsPriceItem(specBaseInfo.getSpecId(), price, matchCpsItem.getSkuId()
                        , matchCpsItem.getJumpUrl(), 2));
            } else {
                cpsPriceList.add(new CpsPriceItem(specBaseInfo.getSpecId(), "-", 0, "", 2));
            }
        }
        return cpsPriceList;
    }
}
