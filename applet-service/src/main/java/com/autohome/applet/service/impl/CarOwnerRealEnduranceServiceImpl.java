package com.autohome.applet.service.impl;

import com.autohome.applet.model.constants.RedisKeys;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfBaseInfoDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfKoubeiDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceSpecInfoDTO;
import com.autohome.applet.service.newenergy.CarOwnerRealEnduranceService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.MapBuilder;
import com.autohome.applet.util.NumberHelper;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 新能源车主续航service
 * @author: WangBoWen
 * @date: 2024-01-02
 **/
@Service
@Slf4j
public class CarOwnerRealEnduranceServiceImpl implements CarOwnerRealEnduranceService {

    @Autowired
    @Qualifier("lightapp")
    RedisClient redisTemplate;

    @Override
    public CarOwnerRealEnduranceDTO getCarOwnerRealEndurance(Integer range, Integer seriesId, Integer cityId) {

        String redisRangeKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_KEY, seriesId, cityId,range);
        String redisBestKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_BEST_KEY, seriesId, cityId);

        if (range != null) {

            //获取指定车型缓存
            String resultStr = redisTemplate.get(redisRangeKey);

            if (!StringUtils.isEmpty(resultStr)) {
                return JacksonHelper.deserialize(resultStr,CarOwnerRealEnduranceDTO.class);
            }

        }else {

            //获取最好续航缓存
            String resultStr = redisTemplate.get(redisBestKey);

            if (!StringUtils.isEmpty(resultStr)) {
                return JacksonHelper.deserialize(resultStr,CarOwnerRealEnduranceDTO.class);
            }

        }


        Supplier<CarOwnerRealEnduranceOfKoubeiDTO> getKouBeiDataFunc = () -> this.getCarOwnerRealEnduranceKouBei(range, seriesId, cityId);

        Supplier<CarOwnerRealEnduranceOfBaseInfoDTO> getBeiLiBaseInfoDataFunc = () -> this.getCarOwnerRealEnduranceOfBaseInfo(seriesId,cityId);

        CompletableFuture<CarOwnerRealEnduranceOfKoubeiDTO> kouBeiFuture = CompletableFuture.supplyAsync(
                getKouBeiDataFunc);

        CompletableFuture<CarOwnerRealEnduranceOfBaseInfoDTO> beiLiBaseInfoFunc = CompletableFuture.supplyAsync(
                getBeiLiBaseInfoDataFunc);

        CompletableFuture.allOf(kouBeiFuture,beiLiBaseInfoFunc).join();

        CarOwnerRealEnduranceDTO carOwnerRealEnduranceDTO = new CarOwnerRealEnduranceDTO();

        try {

            CarOwnerRealEnduranceOfKoubeiDTO carOwnerRealEnduranceOfKoubeiDTO = kouBeiFuture.get();
            CarOwnerRealEnduranceOfBaseInfoDTO carOwnerRealEnduranceOfBaseInfoDTO = beiLiBaseInfoFunc.get();

            CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo bestSpecInfo = this.getBestSpecInfo(range,carOwnerRealEnduranceOfBaseInfoDTO);
            log.info("getCarOwnerRealEndurance bestSpecInfo : {}",JacksonHelper.serialize(bestSpecInfo));

            CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo bestSeasonInfo = this.getBestSeasonInfo(carOwnerRealEnduranceOfKoubeiDTO);
            log.info("getCarOwnerRealEndurance bestSeasonInfo : {}",JacksonHelper.serialize(bestSeasonInfo));

            CarOwnerRealEnduranceSpecInfoDTO specInfo = null;

            //若口碑数据,和车型列表数据为null,则代表没有数据了
            if (bestSeasonInfo == null && bestSpecInfo == null) {
                return null;
            }

            if (bestSpecInfo != null) {
                carOwnerRealEnduranceDTO.setSpecId(bestSpecInfo.getSpecid());
                carOwnerRealEnduranceDTO.setOfficialbatteryLife(this.getTabNameRange(bestSpecInfo.getTabname()));
                specInfo = this.getCarOwnerRealEnduranceSpecInfoDTO(seriesId, cityId, bestSpecInfo.getSpecid());
                log.info("getCarOwnerRealEndurance specInfo : {}",JacksonHelper.serialize(specInfo));
            }

            if (specInfo != null){

//                CarOwnerRealEnduranceSpecInfoDTO.SubInfo officialEnduranceSubInfo = this.getSubInfo("官方续航", specInfo);
//                carOwnerRealEnduranceDTO.setOfficialbatteryLife(NumberHelper.parseInt(officialEnduranceSubInfo.getTitle()));

                CarOwnerRealEnduranceSpecInfoDTO.SubInfo fastChargeSubInfo = this.getSubInfo("快充时间", specInfo);
                carOwnerRealEnduranceDTO.setFactChaSpeed(NumberHelper.parseDouble(fastChargeSubInfo.getTitle(),0.0));
            }

            if (bestSeasonInfo != null && bestSeasonInfo.getFactDriveRange() != null) {
                carOwnerRealEnduranceDTO.setFactDriveRange(bestSeasonInfo.getFactDriveRange());

                //计算能耗
                if (bestSeasonInfo.getFactEnergy100() != null && bestSeasonInfo.getFactEnergy100() > 0){
                    carOwnerRealEnduranceDTO.setElectricCharge(this.calculateElectric(bestSeasonInfo));
                }else if (specInfo != null){
                    //车型信息存在则保存车型信息百公里电费
                    CarOwnerRealEnduranceSpecInfoDTO.SubInfo electricSubInfo = this.getSubInfo("百公里电费", specInfo);
                    carOwnerRealEnduranceDTO.setElectricCharge(NumberHelper.parseDouble(electricSubInfo.getTitle(),0.0));
                }

            }else if (specInfo != null){
                CarOwnerRealEnduranceSpecInfoDTO.SubInfo factSubInfo = this.getSubInfo("车主续航", specInfo);
                carOwnerRealEnduranceDTO.setFactDriveRange(NumberHelper.parseInt(factSubInfo.getTitle(),0));

                CarOwnerRealEnduranceSpecInfoDTO.SubInfo electricSubInfo = this.getSubInfo("百公里电费", specInfo);
                carOwnerRealEnduranceDTO.setElectricCharge(NumberHelper.parseDouble(electricSubInfo.getTitle(),0.0));
            }


            if (range != null) {
                redisTemplate.set(redisRangeKey,JacksonHelper.serialize(carOwnerRealEnduranceDTO),4, TimeUnit.HOURS);
            }else {
                redisTemplate.set(redisBestKey,JacksonHelper.serialize(carOwnerRealEnduranceDTO),4,TimeUnit.HOURS);
            }


            return carOwnerRealEnduranceDTO;


        } catch (Exception e) {

            log.error("getCarOwnerRealEndurance error",e);
            throw new BusinessException("源接口调用失败",1001);

        }

    }

    @Override
    public CarOwnerRealEnduranceOfKoubeiDTO getCarOwnerRealEnduranceKouBei(Integer range, Integer seriesId, Integer cityId) {


        String redisKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_KOU_BEI_DATA, range, seriesId,cityId);
        String redisJsonStr = redisTemplate.get(redisKey);

        if (!StringUtils.isEmpty(redisJsonStr)) {
            return JacksonHelper.deserialize(redisJsonStr,CarOwnerRealEnduranceOfKoubeiDTO.class);
        }

        String url = "http://koubei.api.sjz.autohome.com.cn/api/nev/car/seriesindex";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                                               .put("_appid", "koubei")
                                               .put("seriesid", seriesId)
                                               .put("range",range == null ? "" : range)
                                               .put("cityid", cityId).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("getCarOwnerRealEnduranceKouBei {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getCarOwnerRealEnduranceKouBei 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<CarOwnerRealEnduranceOfKoubeiDTO> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarOwnerRealEnduranceOfKoubeiDTO>>() {});

        if (returnValue == null){
            log.warn("getCarOwnerRealEnduranceKouBei 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0){
            log.error("getCarOwnerRealEnduranceKouBei {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null){
            log.warn("getCarOwnerRealEnduranceKouBei 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        CarOwnerRealEnduranceOfKoubeiDTO result = returnValue.getResult();

        //判断空集合防止序列化出错
        if (result.getNewSameCarList() == null) {
            result.setNewSameCarList(new ArrayList<>());
        }

        if (result.getSeasonData() == null){
            result.setSeasonData(new TreeMap<>());
        }

        if (result.getSpecInfoList() == null){
            result.setSpecInfoList(new ArrayList<>());
        }

        redisTemplate.set(redisKey, JacksonHelper.serialize(result), 4, TimeUnit.HOURS);


        return returnValue.getResult();
    }

    @Override
    public CarOwnerRealEnduranceOfBaseInfoDTO getCarOwnerRealEnduranceOfBaseInfo(Integer seriesId, Integer cityId) {

        String url = "http://cars.corpautohome.com/carext/newenergy_data/base_info";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                                               .put("seriesid", seriesId)
                                               .put("pluginversion","11.58.0")
                                               .put("cityid", cityId).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("getCarOwnerRealEnduranceOfBaseInfo {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())){
            log.warn("getCarOwnerRealEnduranceOfBaseInfo 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<CarOwnerRealEnduranceOfBaseInfoDTO> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarOwnerRealEnduranceOfBaseInfoDTO>>() {});

        if (returnValue == null) {
            log.warn("getCarOwnerRealEnduranceOfBaseInfo returnResult is null");
            return null;
        }

        if (returnValue.getReturncode() != 0 ){
            log.error("getCarOwnerRealEnduranceOfBaseInfo {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null){
            log.warn("getCarOwnerRealEnduranceOfBaseInfo 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        return returnValue.getResult();
    }

    @Override
    public CarOwnerRealEnduranceSpecInfoDTO getCarOwnerRealEnduranceSpecInfoDTO(Integer seriesId, Integer cityId,
                                                                                Integer specId) {
        String url = "http://cars.corpautohome.com/carext/newenergy_data/spec_info";

        Map<String, Object> fields = MapBuilder.<String,Object>newInstance()
                                               .put("pm",1)
                                               .put("pluginversion","11.51.0")
                                               .put("seriesid", seriesId)
                                               .put("cityid", cityId)
                                               .put("specid",specId).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200) {
            log.error("getCarOwnerRealEnduranceSpecInfoDTO {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())){
            log.warn("getCarOwnerRealEnduranceSpecInfoDTO 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<CarOwnerRealEnduranceSpecInfoDTO> returnValue= JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarOwnerRealEnduranceSpecInfoDTO>>() {});

        if (returnValue == null){
            log.warn("getCarOwnerRealEnduranceOfBaseInfo 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0){
            log.error("getCarOwnerRealEnduranceSpecInfoDTO {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败",1001);
        }

        if (returnValue.getResult() == null){
            return null;
        }

        return returnValue.getResult();
    }

    /**
     * 获取最佳车型数据
     * @param range 指定的续航里程
     * @param carOwnerRealEnduranceOfBaseInfoDTO 北里车型数据
     * @return 最佳车型数据
     */
    private CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo getBestSpecInfo (Integer range, CarOwnerRealEnduranceOfBaseInfoDTO carOwnerRealEnduranceOfBaseInfoDTO){

        if (carOwnerRealEnduranceOfBaseInfoDTO == null
            || carOwnerRealEnduranceOfBaseInfoDTO.getSeriesinfo() == null
            || carOwnerRealEnduranceOfBaseInfoDTO.getSeriesinfo().getTablist() == null
            || carOwnerRealEnduranceOfBaseInfoDTO.getSeriesinfo().getTablist().isEmpty()){

            return null;
        }

        List<CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo> tablist = carOwnerRealEnduranceOfBaseInfoDTO.getSeriesinfo().getTablist();


        //有续航里程参数则返回指定的续航里程
        if (range != null){
            CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo matchSpecInfo = null;
            for (CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo specInfo : tablist) {

                Integer tabNameRange = this.getTabNameRange(specInfo.getTabname());
                //可能存在多个车型,返回id最大的
                if (range.equals(tabNameRange)){
                    if (matchSpecInfo == null || matchSpecInfo.getSpecid() < specInfo.getSpecid()){
                        matchSpecInfo = specInfo;
                    }
                }
            }
            return matchSpecInfo;
        }


        CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo bestSpecInfo = null;

        for (CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo specInfo : tablist) {

            if (bestSpecInfo == null){
                bestSpecInfo = specInfo;
                continue;
            }

            Integer bestTabRange = this.getTabNameRange(bestSpecInfo.getTabname());
            Integer tabNameRange = this.getTabNameRange(specInfo.getTabname());

            if (bestTabRange < tabNameRange) {
                bestSpecInfo = specInfo;
            } else if (bestTabRange.equals(tabNameRange)  && bestSpecInfo.getSpecid() < specInfo.getSpecid()){

                //同续航下,取车型id最大的车型
                bestSpecInfo = specInfo;
            }

        }

        return bestSpecInfo;
    }

    /**
     * 获取口碑最佳季节的数据(最好里程,最好能耗)
     * @param koubeiDTO 口碑数据(四季续航数据)
     * @return 最佳季节续航
     */
    private CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo getBestSeasonInfo (CarOwnerRealEnduranceOfKoubeiDTO koubeiDTO){

        if (koubeiDTO == null || koubeiDTO.getSeasonData() == null || koubeiDTO.getSeasonData().isEmpty()){
            return null;
        }

        Map<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData = koubeiDTO.getSeasonData();


        //筛选出一年四季中最好续航
        CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo bestSeasonInfo = null;

        //筛出一年四季中最优能耗
        Double bestFactEnergy100 = null;

        for (Map.Entry<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> item : seasonData.entrySet()) {

            if (bestSeasonInfo == null){
                bestSeasonInfo = item.getValue();
                bestFactEnergy100 = item.getValue().getFactEnergy100();
                continue;
            }

            if (bestSeasonInfo.getFactDriveRange() < item.getValue().getFactDriveRange()) {
                bestSeasonInfo = item.getValue();
            }

            if (bestFactEnergy100 > item.getValue().getFactEnergy100()){
                bestFactEnergy100 = item.getValue().getFactEnergy100();
            }

        }

        bestSeasonInfo.setFactEnergy100(bestFactEnergy100);

        return bestSeasonInfo;
    }

    /**
     * 获取车型指定字段数据
     * @param subtitle 字段名称
     * @param carOwnerRealEnduranceSpecInfoDTO 车系数据
     * @return 指定字段的数据
     */
    private CarOwnerRealEnduranceSpecInfoDTO.SubInfo getSubInfo (String subtitle,CarOwnerRealEnduranceSpecInfoDTO carOwnerRealEnduranceSpecInfoDTO){

        if (carOwnerRealEnduranceSpecInfoDTO == null
            || carOwnerRealEnduranceSpecInfoDTO.getSpecinfo() == null
            || carOwnerRealEnduranceSpecInfoDTO.getSpecinfo().getSublist().isEmpty()) {
            return null;
        }

        List<CarOwnerRealEnduranceSpecInfoDTO.SubInfo> sublist = carOwnerRealEnduranceSpecInfoDTO.getSpecinfo().getSublist();

        ArrayList<CarOwnerRealEnduranceSpecInfoDTO.SubInfo> list = sublist.stream()
                                                                             .filter(subInfo -> subInfo.getSubtitle()
                                                                                                       .equals(subtitle))
                                                                             .collect(Collectors.toCollection(
                                                                                     ArrayList::new));

        return list.get(0);
    }

    /**
     * 计算百公里电费 (百公里耗电度数 * 0.68)
     * @param seasonInfo 最优季节续航数据
     * @return 百公里电费
     */
    private Double calculateElectric (CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo seasonInfo){

        if (seasonInfo == null || seasonInfo.getFactEnergy100() == null){
            return 0.0;
        }

        Double factEnergy100 = seasonInfo.getFactEnergy100();

        //计算百公里电费 系数0.68
        double energyDou = factEnergy100 * 0.68 ;

        BigDecimal decimal = new BigDecimal(energyDou);

        double result = decimal.setScale(BigDecimal.ROUND_DOWN, BigDecimal.ROUND_HALF_UP).doubleValue();

        return result;

    }

    private Integer getTabNameRange (String tabName){

        if (StringUtils.isEmpty(tabName)) {
            return 0;
        }

        String numStr = tabName.replace("km", "");

        return NumberHelper.parseInt(numStr,0);

    }

}
