package com.autohome.applet.service.zfb;

import com.alipay.api.FileItem;
import com.alipay.api.internal.util.AlipayUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;

/**
 * 支持 InputStream 流式处理的 FileItem 实现
 * 继承自支付宝的 FileItem，支持从 InputStream 直接流式上传，避免文件落地
 *
 * 关键技术点：
 * 1. 通过反射将父类的content字段设置为null，确保调用我们重写的writeFileContent方法
 * 2. 重写writeFileContent方法，实现从InputStream到OutputStream的流式传输
 *
 * <AUTHOR>
 */
@Slf4j
public class StreamFileItem extends FileItem {
    
    private final InputStream inputStream;
    private final String fileName;
    private final String mimeType;
    private final long contentLength;
    
    /**
     * 构造函数
     * @param fileName 文件名
     * @param inputStream 输入流
     * @param contentLength 内容长度（字节数），如果未知可传入-1
     */
    public StreamFileItem(String fileName, InputStream inputStream, long contentLength) {
        // 先调用父类构造函数，传入临时的空字节数组
        super(fileName, new byte[0]);
        this.fileName = fileName;
        this.inputStream = inputStream;
        this.contentLength = contentLength;
        this.mimeType = determineMimeType(fileName);

        // 关键修复：通过反射将父类的content字段设置为null
        // 这样当支付宝SDK调用writeFileContent时，会发现content为null，
        // 然后尝试从file读取，但file也为null，最终会调用我们重写的方法
        try {
            Field contentField = FileItem.class.getDeclaredField("content");
            contentField.setAccessible(true);
            contentField.set(this, null);
        } catch (Exception e) {
            log.warn("无法通过反射设置content字段为null，可能影响流式上传: {}", e.getMessage());
        }
    }

    /**
     * 构造函数（带MIME类型）
     * @param fileName 文件名
     * @param inputStream 输入流
     * @param contentLength 内容长度（字节数）
     * @param mimeType MIME类型
     */
    public StreamFileItem(String fileName, InputStream inputStream, long contentLength, String mimeType) {
        super(fileName, new byte[0]);
        this.fileName = fileName;
        this.inputStream = inputStream;
        this.contentLength = contentLength;
        this.mimeType = mimeType;

        // 同样通过反射设置content为null
        try {
            Field contentField = FileItem.class.getDeclaredField("content");
            contentField.setAccessible(true);
            contentField.set(this, null);
        } catch (Exception e) {
            log.warn("无法通过反射设置content字段为null，可能影响流式上传: {}", e.getMessage());
        }
    }
    
    @Override
    public String getFileName() {
        return this.fileName;
    }
    
    @Override
    public String getMimeType() throws IOException {
        return this.mimeType;
    }
    
    @Override
    public long fileContentLength() {
        return this.contentLength > 0 ? this.contentLength : 0L;
    }
    
    /**
     * 核心方法：将输入流的内容直接写入到输出流
     * 这是实现流式上传的关键方法
     */
    @Override
    public void writeFileContent(OutputStream out) throws IOException {
        if (out == null) {
            throw new IOException("输出流不能为空");
        }
        
        if (inputStream == null) {
            throw new IOException("输入流不能为空");
        }
        
        byte[] buffer = new byte[8192]; // 8KB缓冲区
        int bytesRead;
        long totalBytesRead = 0;
        
        try {
            log.debug("开始流式传输文件: {}", fileName);
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
                
                // 每传输10MB打印一次进度日志
                if (totalBytesRead % (10 * 1024 * 1024) == 0) {
                    log.debug("流式传输进度: {} MB", totalBytesRead / (1024 * 1024));
                }
            }
            
            out.flush();
            log.info("流式传输完成，总传输字节数: {} bytes", totalBytesRead);
            
        } catch (IOException e) {
            log.error("流式传输过程中发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 重写 getContent 方法
     * 注意：这个方法会将整个流读入内存，违背了流式处理的初衷
     * 建议避免调用此方法，直接使用 writeFileContent
     */
    @Override
    public byte[] getContent() throws IOException {
        log.warn("警告：getContent() 方法会将整个流读入内存，建议使用 writeFileContent() 进行流式处理");
        // 为了兼容性，这里还是实现了这个方法，但不推荐使用
        return super.getContent();
    }
    
    /**
     * 重写 getFileHeader 方法
     * 返回空数组，因为我们无法从流中预读取头部信息
     */
    @Override
    public byte[] getFileHeader() throws IOException {
        // 对于流式处理，我们无法预读取文件头，返回空数组
        return new byte[0];
    }
    
    /**
     * 根据文件名确定MIME类型
     */
    private String determineMimeType(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }
        
        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".mp4")) {
            return "video/mp4";
        } else if (lowerFileName.endsWith(".avi")) {
            return "video/x-msvideo";
        } else if (lowerFileName.endsWith(".mov")) {
            return "video/quicktime";
        } else if (lowerFileName.endsWith(".mkv")) {
            return "video/x-matroska";
        } else if (lowerFileName.endsWith(".webm")) {
            return "video/webm";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else {
            return "application/octet-stream";
        }
    }
}
