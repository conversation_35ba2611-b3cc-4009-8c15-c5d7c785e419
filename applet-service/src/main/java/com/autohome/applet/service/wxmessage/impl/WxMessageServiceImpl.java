package com.autohome.applet.service.wxmessage.impl;

import com.autohome.applet.dao.javaapi.mapper.WxMessageMapper;
import com.autohome.applet.dao.javaapi.model.wxmsg.WxMessage;
import com.autohome.applet.dao.javaapi.model.wxmsg.WxMessageQuery;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.user.DecryptUserResultDto;
import com.autohome.applet.model.dto.wsmessage.NewCarSeriesSubscriptionRequest;
import com.autohome.applet.model.dto.wsmessage.WxPostMessageRequest;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.javaapi.UserCenterService;
import com.autohome.applet.service.wxmessage.WxMessageService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;

@Service
@Slf4j
public class WxMessageServiceImpl implements WxMessageService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private WxMessageMapper wxMessageMapper;

    @Autowired
    private HttpService httpService;

    @Autowired
    UserCenterService userCenterService;

    @Value("${third.service.url.subscribe:\"\"}")
    private String subscribeUrl;

    private static final String PREFIX_LOCK_SAVE_PUSH_MESSAGE = String.join(":", "lock", "wx", "p", "msg");

    @Override
    public ReturnValue savePushMessage(WxPostMessageRequest wxPostMessageRequest) {
        //1.加锁，防止重复提交,同一车系，同一个人，只允许订阅一次，在消息发送侧面也会做出消息过滤
        String key = PREFIX_LOCK_SAVE_PUSH_MESSAGE + ":" + wxPostMessageRequest.getOpenId()+":"+wxPostMessageRequest.getSeriesId();
        boolean checkLock = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofHours(12));
        if (!checkLock) {
            //已经重复提交， 直接返回
            log.warn("savePushMessage,该车系已发送消息，今日无需再次发送 param:{}, returncode:{}, message:{}", JacksonHelper.serialize(wxPostMessageRequest), 200101, "请勿重复提交");
            return ReturnValue.buildSuccessResult(true);
        }
//        WxMessageQuery wxMessageQuery = convertDaoWxMessageQuery(wxPostMessageRequest);
//        //2.count是否存在
//        int count = wxMessageMapper.countWxMessage(wxMessageQuery);
//        if (count > 0) {
//            //已经提交过， 直接返回
//            log.warn("savePushMessage, param:{}, returncode:{}, message:{}", JacksonHelper.serialize(wxPostMessageRequest), 200102, "请勿重复提交");
//            return ReturnValue.buildErrorResult(200102, "请勿重复提交");
//        }
        //3.执行insert
        int result = wxMessageMapper.insert(convertDaoWxMessage(wxPostMessageRequest));
        if (result > 0) {
            //删除redis锁
//            stringRedisTemplate.delete(key);
            log.info("savePushMessage success, param:{}", JacksonHelper.serialize(wxPostMessageRequest));
            return ReturnValue.buildSuccessResult(true);
        } else {
            log.warn("savePushMessage, param:{}, returncode:{}, message:{}", JacksonHelper.serialize(wxPostMessageRequest), 100110, "推送失败");
            return ReturnValue.buildErrorResult(100110, "推送失败");
        }
    }

    @Override
    public ReturnValue newSeriesInfoMinpro(NewCarSeriesSubscriptionRequest newCarSeriesSubscriptionRequest) {
        log.info("newSeriesInfoMinpro request, param:{}", JacksonHelper.serialize(newCarSeriesSubscriptionRequest));

        String userId = "";
        if (!StringUtils.isEmpty(newCarSeriesSubscriptionRequest.getAuth())) {
            try {
                DecryptUserResultDto decryptUserResultDto = userCenterService.decryptPcPopClubCookie(newCarSeriesSubscriptionRequest.getAuth());
                userId = decryptUserResultDto.getUserId()+"";

            } catch (Exception e) {
                log.error("订阅接口解密失败auth={}", newCarSeriesSubscriptionRequest.getAuth(), e);
            }
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("type", newCarSeriesSubscriptionRequest.getType());
        if (!StringUtils.isEmpty(userId )) {
            param.put("userid", userId);
        }
        param.put("pm", newCarSeriesSubscriptionRequest.getPm());
        param.put("pluginversion", newCarSeriesSubscriptionRequest.getUnionid());
        param.put("deviceid", newCarSeriesSubscriptionRequest.getOpenid());
        param.put("sourceid", newCarSeriesSubscriptionRequest.getSourceid());
        param.put("biztype", newCarSeriesSubscriptionRequest.getBiztype());
        param.put("seriesid", newCarSeriesSubscriptionRequest.getSeriesid());
        param.put("cityid", newCarSeriesSubscriptionRequest.getCityid());
        ReturnValue<?> returnValue = null;
        try {
            returnValue = httpService.httpPostFor(subscribeUrl, param, new TypeReference<ReturnValue<?>>() {
            });
            log.info("newSeriesInfoMinpro 源接口调用返回:{} , url:{} , 参数:{} ", returnValue, subscribeUrl, JacksonHelper.serialize(param));
            return returnValue;
        } catch (Exception e) {
            log.error("newSeriesInfoMinpro 源接口调用报错:{} , url:{} , 参数:{}  error", returnValue, subscribeUrl, JacksonHelper.serialize(param), e);
            return ReturnValue.buildErrorResult(100110, "请重新订阅");
        }
    }

    private WxMessageQuery convertDaoWxMessageQuery(WxPostMessageRequest wxPostMessageRequest) {
        return WxMessageQuery.builder()
                .openId(wxPostMessageRequest.getOpenId())
                .bizType(wxPostMessageRequest.getBizType())
                .beginDate(DateHelper.getTodayString())
                .endDate(DateHelper.getTomorrowDateStr(DateHelper.DATEFORMAT_ONLY_DATE))
                .build();
    }

    private WxMessage convertDaoWxMessage(WxPostMessageRequest wxPostMessageRequest) {
        return WxMessage.builder()
                .openId(wxPostMessageRequest.getOpenId())
                .bizType(wxPostMessageRequest.getBizType())
                .pm(wxPostMessageRequest.getPm())
                .userId(wxPostMessageRequest.getUserId() == null ? 0 : wxPostMessageRequest.getUserId())
                .seriesId(wxPostMessageRequest.getSeriesId())
                .seriesName(wxPostMessageRequest.getSeriesName())
                .specId(wxPostMessageRequest.getSpecId())
                .cityId(wxPostMessageRequest.getCityId())
                .build();
    }
}
