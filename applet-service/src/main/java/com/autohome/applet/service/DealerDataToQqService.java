package com.autohome.applet.service;

import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.car.AllBrandInfoDto;
import com.autohome.applet.model.dto.car.SeriesInfoBrandDto;
import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsResult;
import com.autohome.applet.util.AllCitiesUtil;
import com.autohome.applet.util.BrandForQQUtil;
import com.autohome.applet.util.SeriesForQQUtil;

import java.util.List;

public interface DealerDataToQqService {
    void uploadBrandToQQ(BrandForQQUtil.BrandForQQ brandInfo, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList);
    void uploadBrandToQQV2(BrandForQQUtil.BrandForQQ brandInfo, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList);
    void uploadBrandToQQV3( AllBrandInfoDto.BrandInfoDto brandInfo, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList,  SeriesInfoBrandDto seriesInfoBrandParam, boolean isDel);
    void uploadBrandToQQ(BrandForQQUtil.BrandForQQ brandInfo, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList);
    void uploadBrandToQQ3( AllBrandInfoDto.BrandInfoDto brandInfo, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList,SeriesInfoBrandDto seriesInfoBrandParam, boolean isDel);

    void uploadSeriesToQQ(SeriesForQQUtil.SeriesForQQ seriesForQQ, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList);
    void uploadSeriesToQQV2(SeriesForQQUtil.SeriesForQQ seriesForQQ, List<AllCitiesUtil.CityItem> cityItemList, List<SeriesInfoDto> seriesInfoDtoList);

    void uploadSpecToQQV3(SeriesForQQUtil.SeriesForQQ seriesForQQ, List<AllCitiesUtil.CityItem> cityItemList, SpecParamDto specParamDto, boolean isDel);
    void uploadPicToQQV3(SeriesForQQUtil.SeriesForQQ seriesForQQ,  SpecParamDto specParamDto, boolean isDel);

    void uploadSeriesToQQ(SeriesForQQUtil.SeriesForQQ seriesForQQ, AllCitiesUtil.CityItem cityItem, List<SeriesInfoDto> seriesInfoDtoList);
    void uploadSpecToQQ(SeriesForQQUtil.SeriesForQQ seriesInfo, AllCitiesUtil.CityItem cityItem, SpecParamDto specParamDto, boolean isDel);

    void uploadPicToQQ(SeriesForQQUtil.SeriesForQQ seriesInfo, SpecParamDto specParamDto, boolean isDel);
    void uploadEvaluateCardToQQ(SeriesForQQUtil.SeriesForQQ customSeriesItem,
                                List<SeriesInfoDto> allSeriesInfoDtoList,SpecParamDto specParamDto, boolean isDel);

    /**
     * 查找全量的车系数据(在售和停产在售)
     * */
    List<SeriesInfoDto> getSeriesInfoDtoList();

    /**
     * 查找全量的车系数据(全部)
     * */
    List<SeriesInfoDto> getAllSeriesInfoDtoList();

    /**
     * 筛选品牌和城市中有报价的车系id列表
     * */
    List<Integer> getDealerFilters(String cityCode, String brandId);

    /**
     * 获取该区县,或者城市,或者省份的底价经销商, 只能传一个车系id
     * params:
     *  saleScope 0：为车商汇销售状态；；；1：产品库在售状态
     * return: 最低价格的经销商信息
     * */
    SeriesMinPriceExtendsResult getSeriesMinPriceExtends(String cityCode, Integer seriesId);

    /**
     * 获取该区县,或者城市,或者省份的底价经销商, 批量传多个车系id
     * params:
     *  saleScope 0：为车商汇销售状态；；；1：产品库在售状态
     * return: 最低价格的经销商信息
     * */
    List<SeriesMinPriceExtendsResult> getSeriesMinPriceExtends(String cityCode, List<Integer> seriesIdList);
}
