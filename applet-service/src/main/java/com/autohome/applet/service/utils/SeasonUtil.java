package com.autohome.applet.service.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.autohome.applet.model.enums.SeasonEnum;

import java.util.Date;

/**
 * 季节工具类
 * 通过当前日期计算当前季节
 */
public class SeasonUtil {
    public static SeasonEnum getSeasonByDate(Date date) {
        DateTime dateTime = new DateTime(date);
        int month = dateTime.getField(DateField.MONTH) + 1;
        if (month >= 3 && month <= 5) {
            return SeasonEnum.SPRING;
        } else if (month >= 6 && month <= 8) {
            return SeasonEnum.SUMMER;
        } else if (month >= 9 && month <= 11) {
            return SeasonEnum.AUTUMN;
        } else {
            return SeasonEnum.WINTER;
        }
    }
}