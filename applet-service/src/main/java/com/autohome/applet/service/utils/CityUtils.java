package com.autohome.applet.service.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.autohome.applet.model.dto.newenergy.RegionDto;
import com.google.common.collect.Lists;

import java.util.*;

public class CityUtils {

    private static String city_string = "[{\"code\":\"522600\",\"name\":\"黔东南\"},{\"code\":\"340800\",\"name\":\"安庆\"},{\"code\":\"340300\",\"name\":\"蚌埠\"},{\"code\":\"341600\",\"name\":\"亳州\"},{\"code\":\"341700\",\"name\":\"池州\"},{\"code\":\"341100\",\"name\":\"滁州\"},{\"code\":\"341200\",\"name\":\"阜阳\"},{\"code\":\"340400\",\"name\":\"淮南\"},{\"code\":\"340100\",\"name\":\"合肥\"},{\"code\":\"341000\",\"name\":\"黄山\"},{\"code\":\"340600\",\"name\":\"淮北\"},{\"code\":\"341500\",\"name\":\"六安\"},{\"code\":\"340500\",\"name\":\"马鞍山\"},{\"code\":\"341300\",\"name\":\"宿州\"},{\"code\":\"340700\",\"name\":\"铜陵\"},{\"code\":\"340200\",\"name\":\"芜湖\"},{\"code\":\"341800\",\"name\":\"宣城\"},{\"code\":\"820100\",\"name\":\"澳门\"},{\"code\":\"110100\",\"name\":\"北京\"},{\"code\":\"500100\",\"name\":\"重庆\"},{\"code\":\"350100\",\"name\":\"福州\"},{\"code\":\"350800\",\"name\":\"龙岩\"},{\"code\":\"350900\",\"name\":\"宁德\"},{\"code\":\"350700\",\"name\":\"南平\"},{\"code\":\"350300\",\"name\":\"莆田\"},{\"code\":\"350500\",\"name\":\"泉州\"},{\"code\":\"350400\",\"name\":\"三明\"},{\"code\":\"350200\",\"name\":\"厦门\"},{\"code\":\"350600\",\"name\":\"漳州\"},{\"code\":\"445100\",\"name\":\"潮州\"},{\"code\":\"441900\",\"name\":\"东莞\"},{\"code\":\"440600\",\"name\":\"佛山\"},{\"code\":\"440100\",\"name\":\"广州\"},{\"code\":\"441300\",\"name\":\"惠州\"},{\"code\":\"441600\",\"name\":\"河源\"},{\"code\":\"440700\",\"name\":\"江门\"},{\"code\":\"445200\",\"name\":\"揭阳\"},{\"code\":\"440900\",\"name\":\"茂名\"},{\"code\":\"441400\",\"name\":\"梅州\"},{\"code\":\"441800\",\"name\":\"清远\"},{\"code\":\"440500\",\"name\":\"汕头\"},{\"code\":\"441500\",\"name\":\"汕尾\"},{\"code\":\"440200\",\"name\":\"韶关\"},{\"code\":\"440300\",\"name\":\"深圳\"},{\"code\":\"441700\",\"name\":\"阳江\"},{\"code\":\"445300\",\"name\":\"云浮\"},{\"code\":\"442000\",\"name\":\"中山\"},{\"code\":\"440400\",\"name\":\"珠海\"},{\"code\":\"441200\",\"name\":\"肇庆\"},{\"code\":\"440800\",\"name\":\"湛江\"},{\"code\":\"450500\",\"name\":\"北海\"},{\"code\":\"451000\",\"name\":\"百色\"},{\"code\":\"451400\",\"name\":\"崇左\"},{\"code\":\"450600\",\"name\":\"防城港\"},{\"code\":\"450800\",\"name\":\"贵港\"},{\"code\":\"450300\",\"name\":\"桂林\"},{\"code\":\"451100\",\"name\":\"贺州\"},{\"code\":\"451200\",\"name\":\"河池\"},{\"code\":\"451300\",\"name\":\"来宾\"},{\"code\":\"450200\",\"name\":\"柳州\"},{\"code\":\"450100\",\"name\":\"南宁\"},{\"code\":\"450700\",\"name\":\"钦州\"},{\"code\":\"450400\",\"name\":\"梧州\"},{\"code\":\"450900\",\"name\":\"玉林\"},{\"code\":\"520400\",\"name\":\"安顺\"},{\"code\":\"520500\",\"name\":\"毕节\"},{\"code\":\"522700\",\"name\":\"黔南\"},{\"code\":\"520100\",\"name\":\"贵阳\"},{\"code\":\"520200\",\"name\":\"六盘水\"},{\"code\":\"520600\",\"name\":\"铜仁\"},{\"code\":\"522300\",\"name\":\"黔西南\"},{\"code\":\"520300\",\"name\":\"遵义\"},{\"code\":\"620400\",\"name\":\"白银\"},{\"code\":\"621100\",\"name\":\"定西\"},{\"code\":\"623000\",\"name\":\"甘南\"},{\"code\":\"620900\",\"name\":\"酒泉\"},{\"code\":\"620200\",\"name\":\"嘉峪关\"},{\"code\":\"620300\",\"name\":\"金昌\"},{\"code\":\"620100\",\"name\":\"兰州\"},{\"code\":\"621200\",\"name\":\"陇南\"},{\"code\":\"622900\",\"name\":\"临夏\"},{\"code\":\"620800\",\"name\":\"平凉\"},{\"code\":\"621000\",\"name\":\"庆阳\"},{\"code\":\"620500\",\"name\":\"天水\"},{\"code\":\"620600\",\"name\":\"武威\"},{\"code\":\"620700\",\"name\":\"张掖\"},{\"code\":\"469025\",\"name\":\"白沙\"},{\"code\":\"469029\",\"name\":\"保亭\"},{\"code\":\"469026\",\"name\":\"昌江\"},{\"code\":\"469023\",\"name\":\"澄迈\"},{\"code\":\"469007\",\"name\":\"东方\"},{\"code\":\"469021\",\"name\":\"定安\"},{\"code\":\"460400\",\"name\":\"儋州\"},{\"code\":\"460100\",\"name\":\"海口\"},{\"code\":\"469024\",\"name\":\"临高\"},{\"code\":\"469027\",\"name\":\"乐东\"},{\"code\":\"469028\",\"name\":\"陵水\"},{\"code\":\"469030\",\"name\":\"琼中\"},{\"code\":\"469002\",\"name\":\"琼海\"},{\"code\":\"460200\",\"name\":\"三亚\"},{\"code\":\"469022\",\"name\":\"屯昌\"},{\"code\":\"469001\",\"name\":\"五指山\"},{\"code\":\"469005\",\"name\":\"文昌\"},{\"code\":\"469006\",\"name\":\"万宁\"},{\"code\":\"410500\",\"name\":\"安阳\"},{\"code\":\"410600\",\"name\":\"鹤壁\"},{\"code\":\"410800\",\"name\":\"焦作\"},{\"code\":\"419001\",\"name\":\"济源市\"},{\"code\":\"410200\",\"name\":\"开封\"},{\"code\":\"410300\",\"name\":\"洛阳\"},{\"code\":\"411100\",\"name\":\"漯河\"},{\"code\":\"411300\",\"name\":\"南阳\"},{\"code\":\"410900\",\"name\":\"濮阳\"},{\"code\":\"410400\",\"name\":\"平顶山\"},{\"code\":\"411200\",\"name\":\"三门峡\"},{\"code\":\"411400\",\"name\":\"商丘\"},{\"code\":\"411500\",\"name\":\"信阳\"},{\"code\":\"411000\",\"name\":\"许昌\"},{\"code\":\"410700\",\"name\":\"新乡\"},{\"code\":\"411600\",\"name\":\"周口\"},{\"code\":\"411700\",\"name\":\"驻马店\"},{\"code\":\"410100\",\"name\":\"郑州\"},{\"code\":\"420700\",\"name\":\"鄂州\"},{\"code\":\"422800\",\"name\":\"恩施\"},{\"code\":\"421100\",\"name\":\"黄冈\"},{\"code\":\"420200\",\"name\":\"黄石\"},{\"code\":\"420800\",\"name\":\"荆门\"},{\"code\":\"421000\",\"name\":\"荆州\"},{\"code\":\"429005\",\"name\":\"潜江\"},{\"code\":\"429021\",\"name\":\"神农架\"},{\"code\":\"421300\",\"name\":\"随州\"},{\"code\":\"420300\",\"name\":\"十堰\"},{\"code\":\"429006\",\"name\":\"天门\"},{\"code\":\"420100\",\"name\":\"武汉\"},{\"code\":\"420600\",\"name\":\"襄阳\"},{\"code\":\"420900\",\"name\":\"孝感\"},{\"code\":\"421200\",\"name\":\"咸宁\"},{\"code\":\"429004\",\"name\":\"仙桃\"},{\"code\":\"420500\",\"name\":\"宜昌\"},{\"code\":\"430100\",\"name\":\"长沙\"},{\"code\":\"430700\",\"name\":\"常德\"},{\"code\":\"431000\",\"name\":\"郴州\"},{\"code\":\"431200\",\"name\":\"怀化\"},{\"code\":\"430400\",\"name\":\"衡阳\"},{\"code\":\"431300\",\"name\":\"娄底\"},{\"code\":\"430500\",\"name\":\"邵阳\"},{\"code\":\"430300\",\"name\":\"湘潭\"},{\"code\":\"433100\",\"name\":\"湘西\"},{\"code\":\"431100\",\"name\":\"永州\"},{\"code\":\"430900\",\"name\":\"益阳\"},{\"code\":\"430600\",\"name\":\"岳阳\"},{\"code\":\"430200\",\"name\":\"株洲\"},{\"code\":\"430800\",\"name\":\"张家界\"},{\"code\":\"130600\",\"name\":\"保定\"},{\"code\":\"130800\",\"name\":\"承德\"},{\"code\":\"130900\",\"name\":\"沧州\"},{\"code\":\"130400\",\"name\":\"邯郸\"},{\"code\":\"131100\",\"name\":\"衡水\"},{\"code\":\"131000\",\"name\":\"廊坊\"},{\"code\":\"130300\",\"name\":\"秦皇岛\"},{\"code\":\"130100\",\"name\":\"石家庄\"},{\"code\":\"130200\",\"name\":\"唐山\"},{\"code\":\"130500\",\"name\":\"邢台\"},{\"code\":\"130700\",\"name\":\"张家口\"},{\"code\":\"230600\",\"name\":\"大庆\"},{\"code\":\"232700\",\"name\":\"大兴安岭\"},{\"code\":\"231100\",\"name\":\"黑河\"},{\"code\":\"230100\",\"name\":\"哈尔滨\"},{\"code\":\"230400\",\"name\":\"鹤岗\"},{\"code\":\"230800\",\"name\":\"佳木斯\"},{\"code\":\"230300\",\"name\":\"鸡西\"},{\"code\":\"231000\",\"name\":\"牡丹江\"},{\"code\":\"230900\",\"name\":\"七台河\"},{\"code\":\"230200\",\"name\":\"齐齐哈尔\"},{\"code\":\"230500\",\"name\":\"双鸭山\"},{\"code\":\"231200\",\"name\":\"绥化\"},{\"code\":\"230700\",\"name\":\"伊春\"},{\"code\":\"320400\",\"name\":\"常州\"},{\"code\":\"320800\",\"name\":\"淮安\"},{\"code\":\"320700\",\"name\":\"连云港\"},{\"code\":\"320600\",\"name\":\"南通\"},{\"code\":\"320100\",\"name\":\"南京\"},{\"code\":\"320500\",\"name\":\"苏州\"},{\"code\":\"321300\",\"name\":\"宿迁\"},{\"code\":\"321200\",\"name\":\"泰州\"},{\"code\":\"320200\",\"name\":\"无锡\"},{\"code\":\"320300\",\"name\":\"徐州\"},{\"code\":\"320900\",\"name\":\"盐城\"},{\"code\":\"321000\",\"name\":\"扬州\"},{\"code\":\"321100\",\"name\":\"镇江\"},{\"code\":\"361000\",\"name\":\"抚州\"},{\"code\":\"360700\",\"name\":\"赣州\"},{\"code\":\"360800\",\"name\":\"吉安\"},{\"code\":\"360200\",\"name\":\"景德镇\"},{\"code\":\"360400\",\"name\":\"九江\"},{\"code\":\"360100\",\"name\":\"南昌\"},{\"code\":\"360300\",\"name\":\"萍乡\"},{\"code\":\"361100\",\"name\":\"上饶\"},{\"code\":\"360500\",\"name\":\"新余\"},{\"code\":\"360600\",\"name\":\"鹰潭\"},{\"code\":\"360900\",\"name\":\"宜春\"},{\"code\":\"220600\",\"name\":\"白山\"},{\"code\":\"220800\",\"name\":\"白城\"},{\"code\":\"220100\",\"name\":\"长春\"},{\"code\":\"220200\",\"name\":\"吉林\"},{\"code\":\"220400\",\"name\":\"辽源\"},{\"code\":\"220300\",\"name\":\"四平\"},{\"code\":\"220700\",\"name\":\"松原\"},{\"code\":\"220500\",\"name\":\"通化\"},{\"code\":\"222400\",\"name\":\"延边\"},{\"code\":\"210300\",\"name\":\"鞍山\"},{\"code\":\"210500\",\"name\":\"本溪\"},{\"code\":\"211300\",\"name\":\"朝阳\"},{\"code\":\"210600\",\"name\":\"丹东\"},{\"code\":\"210200\",\"name\":\"大连\"},{\"code\":\"210400\",\"name\":\"抚顺\"},{\"code\":\"210900\",\"name\":\"阜新\"},{\"code\":\"211400\",\"name\":\"葫芦岛\"},{\"code\":\"210700\",\"name\":\"锦州\"},{\"code\":\"211000\",\"name\":\"辽阳\"},{\"code\":\"211100\",\"name\":\"盘锦\"},{\"code\":\"210100\",\"name\":\"沈阳\"},{\"code\":\"211200\",\"name\":\"铁岭\"},{\"code\":\"210800\",\"name\":\"营口\"},{\"code\":\"152900\",\"name\":\"阿拉善盟\"},{\"code\":\"150200\",\"name\":\"包头\"},{\"code\":\"150800\",\"name\":\"巴彦淖尔\"},{\"code\":\"150400\",\"name\":\"赤峰\"},{\"code\":\"150600\",\"name\":\"鄂尔多斯\"},{\"code\":\"150700\",\"name\":\"呼伦贝尔\"},{\"code\":\"150100\",\"name\":\"呼和浩特\"},{\"code\":\"150500\",\"name\":\"通辽\"},{\"code\":\"150300\",\"name\":\"乌海\"},{\"code\":\"150900\",\"name\":\"乌兰察布\"},{\"code\":\"152200\",\"name\":\"兴安盟\"},{\"code\":\"152500\",\"name\":\"锡林郭勒盟\"},{\"code\":\"640400\",\"name\":\"固原\"},{\"code\":\"640200\",\"name\":\"石嘴山\"},{\"code\":\"640300\",\"name\":\"吴忠\"},{\"code\":\"640100\",\"name\":\"银川\"},{\"code\":\"640500\",\"name\":\"中卫\"},{\"code\":\"632600\",\"name\":\"果洛\"},{\"code\":\"632800\",\"name\":\"海西\"},{\"code\":\"630200\",\"name\":\"海东\"},{\"code\":\"632200\",\"name\":\"海北\"},{\"code\":\"632300\",\"name\":\"黄南\"},{\"code\":\"632500\",\"name\":\"海南\"},{\"code\":\"630100\",\"name\":\"西宁\"},{\"code\":\"632700\",\"name\":\"玉树\"},{\"code\":\"610900\",\"name\":\"安康\"},{\"code\":\"610300\",\"name\":\"宝鸡\"},{\"code\":\"610700\",\"name\":\"汉中\"},{\"code\":\"611000\",\"name\":\"商洛\"},{\"code\":\"610200\",\"name\":\"铜川\"},{\"code\":\"610500\",\"name\":\"渭南\"},{\"code\":\"610100\",\"name\":\"西安\"},{\"code\":\"610400\",\"name\":\"咸阳\"},{\"code\":\"610600\",\"name\":\"延安\"},{\"code\":\"610800\",\"name\":\"榆林\"},{\"code\":\"513200\",\"name\":\"阿坝\"},{\"code\":\"511900\",\"name\":\"巴中\"},{\"code\":\"510100\",\"name\":\"成都\"},{\"code\":\"510600\",\"name\":\"德阳\"},{\"code\":\"511700\",\"name\":\"达州\"},{\"code\":\"511600\",\"name\":\"广安\"},{\"code\":\"510800\",\"name\":\"广元\"},{\"code\":\"513300\",\"name\":\"甘孜\"},{\"code\":\"513400\",\"name\":\"凉山\"},{\"code\":\"510500\",\"name\":\"泸州\"},{\"code\":\"511100\",\"name\":\"乐山\"},{\"code\":\"511400\",\"name\":\"眉山\"},{\"code\":\"510700\",\"name\":\"绵阳\"},{\"code\":\"511300\",\"name\":\"南充\"},{\"code\":\"511000\",\"name\":\"内江\"},{\"code\":\"510400\",\"name\":\"攀枝花\"},{\"code\":\"510900\",\"name\":\"遂宁\"},{\"code\":\"511800\",\"name\":\"雅安\"},{\"code\":\"511500\",\"name\":\"宜宾\"},{\"code\":\"510300\",\"name\":\"自贡\"},{\"code\":\"512000\",\"name\":\"资阳\"},{\"code\":\"310100\",\"name\":\"上海\"},{\"code\":\"140400\",\"name\":\"长治\"},{\"code\":\"140200\",\"name\":\"大同\"},{\"code\":\"140500\",\"name\":\"晋城\"},{\"code\":\"140700\",\"name\":\"晋中\"},{\"code\":\"141000\",\"name\":\"临汾\"},{\"code\":\"141100\",\"name\":\"吕梁\"},{\"code\":\"140600\",\"name\":\"朔州\"},{\"code\":\"140100\",\"name\":\"太原\"},{\"code\":\"140900\",\"name\":\"忻州\"},{\"code\":\"140800\",\"name\":\"运城\"},{\"code\":\"140300\",\"name\":\"阳泉\"},{\"code\":\"371600\",\"name\":\"滨州\"},{\"code\":\"371400\",\"name\":\"德州\"},{\"code\":\"370500\",\"name\":\"东营\"},{\"code\":\"371700\",\"name\":\"菏泽\"},{\"code\":\"370800\",\"name\":\"济宁\"},{\"code\":\"370100\",\"name\":\"济南\"},{\"code\":\"371500\",\"name\":\"聊城\"},{\"code\":\"371300\",\"name\":\"临沂\"},{\"code\":\"370200\",\"name\":\"青岛\"},{\"code\":\"371100\",\"name\":\"日照\"},{\"code\":\"370900\",\"name\":\"泰安\"},{\"code\":\"371000\",\"name\":\"威海\"},{\"code\":\"370700\",\"name\":\"潍坊\"},{\"code\":\"370600\",\"name\":\"烟台\"},{\"code\":\"370300\",\"name\":\"淄博\"},{\"code\":\"370400\",\"name\":\"枣庄\"},{\"code\":\"120100\",\"name\":\"天津\"},{\"code\":\"710100\",\"name\":\"台湾\"},{\"code\":\"810100\",\"name\":\"香港\"},{\"code\":\"652900\",\"name\":\"阿克苏\"},{\"code\":\"654300\",\"name\":\"阿勒泰\"},{\"code\":\"659002\",\"name\":\"阿拉尔\"},{\"code\":\"652700\",\"name\":\"博尔塔拉\"},{\"code\":\"652800\",\"name\":\"巴音郭楞\"},{\"code\":\"652300\",\"name\":\"昌吉\"},{\"code\":\"653200\",\"name\":\"和田\"},{\"code\":\"650500\",\"name\":\"哈密\"},{\"code\":\"650200\",\"name\":\"克拉玛依\"},{\"code\":\"653000\",\"name\":\"克孜勒苏\"},{\"code\":\"653100\",\"name\":\"喀什\"},{\"code\":\"659001\",\"name\":\"石河子\"},{\"code\":\"659003\",\"name\":\"图木舒克\"},{\"code\":\"654200\",\"name\":\"塔城\"},{\"code\":\"650400\",\"name\":\"吐鲁番\"},{\"code\":\"650100\",\"name\":\"乌鲁木齐\"},{\"code\":\"659004\",\"name\":\"五家渠\"},{\"code\":\"654000\",\"name\":\"伊犁\"},{\"code\":\"542500\",\"name\":\"阿里\"},{\"code\":\"540300\",\"name\":\"昌都\"},{\"code\":\"540100\",\"name\":\"拉萨\"},{\"code\":\"540400\",\"name\":\"林芝\"},{\"code\":\"540600\",\"name\":\"那曲\"},{\"code\":\"540200\",\"name\":\"日喀则\"},{\"code\":\"540500\",\"name\":\"山南\"},{\"code\":\"530500\",\"name\":\"保山\"},{\"code\":\"532300\",\"name\":\"楚雄\"},{\"code\":\"532900\",\"name\":\"大理\"},{\"code\":\"533100\",\"name\":\"德宏\"},{\"code\":\"533400\",\"name\":\"迪庆\"},{\"code\":\"532500\",\"name\":\"红河\"},{\"code\":\"530100\",\"name\":\"昆明\"},{\"code\":\"530900\",\"name\":\"临沧\"},{\"code\":\"530700\",\"name\":\"丽江\"},{\"code\":\"533300\",\"name\":\"怒江\"},{\"code\":\"530800\",\"name\":\"普洱\"},{\"code\":\"530300\",\"name\":\"曲靖\"},{\"code\":\"532600\",\"name\":\"文山\"},{\"code\":\"532800\",\"name\":\"西双版纳\"},{\"code\":\"530400\",\"name\":\"玉溪\"},{\"code\":\"530600\",\"name\":\"昭通\"},{\"code\":\"330100\",\"name\":\"杭州\"},{\"code\":\"330500\",\"name\":\"湖州\"},{\"code\":\"330700\",\"name\":\"金华\"},{\"code\":\"330400\",\"name\":\"嘉兴\"},{\"code\":\"331100\",\"name\":\"丽水\"},{\"code\":\"330200\",\"name\":\"宁波\"},{\"code\":\"330800\",\"name\":\"衢州\"},{\"code\":\"330600\",\"name\":\"绍兴\"},{\"code\":\"331000\",\"name\":\"台州\"},{\"code\":\"330300\",\"name\":\"温州\"},{\"code\":\"330900\",\"name\":\"舟山\"},{\"code\":\"659007\",\"name\":\"双河市\"},{\"code\":\"659006\",\"name\":\"铁门关市\"},{\"code\":\"659008\",\"name\":\"可克达拉市\"},{\"code\":\"659009\",\"name\":\"昆玉\"},{\"code\":\"659005\",\"name\":\"北屯市\"},{\"code\":\"460300\",\"name\":\"三沙市\"}]";
    private static String province_string = "[{\"pcode\":\"340000\",\"province\":\"安徽\"},{\"pcode\":\"820000\",\"province\":\"澳门\"},{\"pcode\":\"110000\",\"province\":\"北京\"},{\"pcode\":\"500000\",\"province\":\"重庆\"},{\"pcode\":\"350000\",\"province\":\"福建\"},{\"pcode\":\"440000\",\"province\":\"广东\"},{\"pcode\":\"450000\",\"province\":\"广西\"},{\"pcode\":\"520000\",\"province\":\"贵州\"},{\"pcode\":\"620000\",\"province\":\"甘肃\"},{\"pcode\":\"460000\",\"province\":\"海南\"},{\"pcode\":\"410000\",\"province\":\"河南\"},{\"pcode\":\"420000\",\"province\":\"湖北\"},{\"pcode\":\"430000\",\"province\":\"湖南\"},{\"pcode\":\"130000\",\"province\":\"河北\"},{\"pcode\":\"230000\",\"province\":\"黑龙江\"},{\"pcode\":\"320000\",\"province\":\"江苏\"},{\"pcode\":\"360000\",\"province\":\"江西\"},{\"pcode\":\"220000\",\"province\":\"吉林\"},{\"pcode\":\"210000\",\"province\":\"辽宁\"},{\"pcode\":\"150000\",\"province\":\"内蒙古\"},{\"pcode\":\"640000\",\"province\":\"宁夏\"},{\"pcode\":\"630000\",\"province\":\"青海\"},{\"pcode\":\"610000\",\"province\":\"陕西\"},{\"pcode\":\"510000\",\"province\":\"四川\"},{\"pcode\":\"310000\",\"province\":\"上海\"},{\"pcode\":\"140000\",\"province\":\"山西\"},{\"pcode\":\"370000\",\"province\":\"山东\"},{\"pcode\":\"120000\",\"province\":\"天津\"},{\"pcode\":\"710000\",\"province\":\"台湾\"},{\"pcode\":\"810000\",\"province\":\"香港\"},{\"pcode\":\"650000\",\"province\":\"新疆\"},{\"pcode\":\"540000\",\"province\":\"西藏\"},{\"pcode\":\"530000\",\"province\":\"云南\"},{\"pcode\":\"330000\",\"province\":\"浙江\"}]";
    private static Map<String, String> city_map = new HashMap<>();

    private static final List<Integer> NORTH_REGION_ID_LIST = Arrays.asList(2, 5, 8, 9);
    private static Map<String, String> province_map = new HashMap<>();

    private static List<RegionDto> reginList = new ArrayList<>();

    static {
        initRegionData();
        JSONArray cityList = JSON.parseArray(city_string);
        cityList.forEach(i -> {
            JSONObject obj = JSON.parseObject(i.toString());
            city_map.put(obj.getString("code"), obj.getString("name"));
        });

        JSONArray pList = JSON.parseArray(province_string);
        pList.forEach(i -> {
            JSONObject obj = JSON.parseObject(i.toString());
            province_map.put(obj.getString("pcode"), obj.getString("province"));
        });
    }

    public static String getCityName(String cityid) {
        if (city_map.containsKey(cityid)) {
            return city_map.get(cityid);
        } else {
            return "";
        }
    }

    public static String getProvinceName(String cityid) {
        String pcode = StrUtil.padAfter(StrUtil.sub(cityid, 0, 2), 6, "0");
        if (province_map.containsKey(pcode)) {
            return province_map.get(pcode);
        } else {
            return "";
        }
    }

    public static RegionDto getRegionInfo(Integer cityId) {
        return reginList.stream().filter(e -> e.getCityIds().contains(cityId)).findFirst().orElse(null);
    }

    private static void initRegionData() {
        RegionDto one = new RegionDto();
        one.setReginId(1);
        one.setSpringTemperature("17℃");
        one.setSummerTemperature("22℃");
        one.setAutumnTemperature("17℃");
        one.setWinterTemperature("11℃");
        one.setCityIds(Lists.newArrayList(630100, 632200, 632500, 632600, 632700, 513300, 510400, 532800, 532900, 533100, 533300, 533400, 530500, 540100, 530700, 540200, 530800, 540300, 530900, 540400, 540500, 532300, 542400, 542500));
        reginList.add(one);
        RegionDto two = new RegionDto();
        two.setReginId(2);
        two.setSpringTemperature("12℃");
        two.setSummerTemperature("25℃");
        two.setAutumnTemperature("8℃");
        two.setWinterTemperature("-26℃");
        two.setCityIds(Lists.newArrayList(632800, 650100, 650200, 650400, 620200, 620700, 620900, 659006, 652300, 652700, 652800, 652900, 653000, 653100, 653200, 654000, 654200, 654300, 659001, 659002, 659003, 659004, 650500, 659007, 659008, 659009, 659005));
        reginList.add(two);
        RegionDto three = new RegionDto();
        three.setReginId(3);
        three.setSpringTemperature("17℃");
        three.setSummerTemperature("22℃");
        three.setAutumnTemperature("16℃");
        three.setWinterTemperature("11℃");
        three.setCityIds(Lists.newArrayList(451000, 451200, 451400, 520100, 520200, 520400, 522300, 532500, 522600, 532600, 522700, 530100, 530300, 530400));
        reginList.add(three);
        RegionDto four = new RegionDto();
        four.setReginId(4);
        four.setSpringTemperature("16℃");
        four.setSummerTemperature("25℃");
        four.setAutumnTemperature("16℃");
        four.setWinterTemperature("8℃");
        four.setCityIds(Lists.newArrayList(422800, 429021, 430800, 610900, 512000, 513200, 433100, 513400, 500100, 520300, 510100, 510300, 520500, 520600, 510500, 510600, 510700, 510800, 510900, 511000, 511100, 511300, 530600, 511400, 511500, 511600, 511700, 511800, 511900, 420300));
        reginList.add(four);
        RegionDto five = new RegionDto();
        five.setReginId(5);
        five.setSpringTemperature("14℃");
        five.setSummerTemperature("25℃");
        five.setAutumnTemperature("14℃");
        five.setWinterTemperature("4℃");
        five.setCityIds(Lists.newArrayList(140300, 621100, 152500, 621200, 140400, 152900, 622900, 140500, 623000, 140600, 630200, 140700, 140800, 150100, 632300, 150200, 140900, 150300, 141000, 141100, 150600, 130700, 640100, 150800, 640200, 150900, 640300, 640400, 140100, 640500, 140200, 611000, 620100, 620300, 620400, 620500, 620600, 620800, 621000, 411200, 419001, 610100, 610200, 610300, 610400, 610500, 610600, 610700, 410300, 610800));
        reginList.add(five);
        RegionDto six = new RegionDto();
        six.setReginId(6);
        six.setSpringTemperature("25℃");
        six.setSummerTemperature("29℃");
        six.setAutumnTemperature("25℃");
        six.setWinterTemperature("17℃");
        six.setCityIds(Lists.newArrayList(450600, 469002, 450700, 469005, 460300, 450800, 460400, 450900, 469001, 451100, 441900, 442000, 445100, 451300, 445200, 445300, 460100, 460200, 450100, 430400, 450200, 450300, 450400, 450500, 360700, 431000, 431100, 350300, 350400, 440100, 469029, 350500, 440200, 469030, 350600, 440300, 350800, 440400, 350900, 440500, 440600, 440700, 440800, 440900, 441200, 441300, 441400, 441500, 441600, 441700, 350100, 441800, 350200, 469006, 469007, 469021, 469022, 469023, 469024, 469025, 469026, 469027, 469028));
        reginList.add(six);
        RegionDto seven = new RegionDto();
        seven.setReginId(7);
        seven.setSpringTemperature("17℃");
        seven.setSummerTemperature("27℃");
        seven.setAutumnTemperature("20℃");
        seven.setWinterTemperature("8℃");
        seven.setCityIds(Lists.newArrayList(420900, 321100, 421000, 321200, 421100, 421200, 330100, 421300, 330200, 330300, 429004, 330400, 429005, 330500, 330600, 429006, 330700, 330800, 430100, 430200, 330900, 331000, 430300, 331100, 340100, 430500, 340200, 360500, 430600, 340300, 360600, 430700, 340400, 320100, 360800, 340500, 320200, 430900, 360900, 361000, 340700, 320400, 361100, 340800, 320500, 431200, 320600, 431300, 321000, 350700, 360100, 360200, 360300, 360400, 341000, 341100, 341200, 310100, 341500, 411300, 341700, 411500, 341800, 420100, 420200, 420500, 420600, 420700, 420800));
        reginList.add(seven);
        RegionDto eight = new RegionDto();
        eight.setReginId(8);
        eight.setSpringTemperature("13℃");
        eight.setSummerTemperature("24℃");
        eight.setAutumnTemperature("12℃");
        eight.setWinterTemperature("-3℃");
        eight.setCityIds(Lists.newArrayList(321300, 210200, 130600, 130800, 130900, 210600, 131000, 131100, 210800, 110100, 340600, 320300, 211100, 120100, 130100, 130200, 370100, 211400, 130300, 370200, 320700, 130400, 370300, 320800, 130500, 320900, 370400, 370500, 370600, 370700, 370800, 370900, 371000, 371100, 341300, 341600, 411400, 411600, 411700, 371200, 371300, 371400, 371500, 371600, 371700, 410100, 410200, 410400, 410500, 410600, 410700, 410800, 410900, 411000, 411100));
        reginList.add(eight);
        RegionDto nine = new RegionDto();
        nine.setReginId(9);
        nine.setSpringTemperature("11℃");
        nine.setSummerTemperature("23℃");
        nine.setAutumnTemperature("9℃");
        nine.setWinterTemperature("-7℃");
        nine.setCityIds(Lists.newArrayList(220200, 152200, 220300, 220400, 220500, 210100, 220600, 210300, 220700, 210400, 220800, 222400, 150400, 150500, 230100, 230200, 150700, 230300, 210500, 210700, 210900, 211000, 211200, 211300, 220100, 230400, 230500, 230600, 230700, 230800, 230900, 231000, 231100, 231200, 232700));
        reginList.add(nine);
    }


}