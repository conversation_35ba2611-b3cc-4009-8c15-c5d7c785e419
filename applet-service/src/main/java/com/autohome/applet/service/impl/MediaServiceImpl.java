package com.autohome.applet.service.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.MediaService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MediaServiceImpl implements MediaService {

    @Override
    public Media getMediaInfo(String mid) {
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("https://p-vp.lq.autohome.com.cn/api/gpi?mid=" + mid);
        if (httpResult.getStatusCode() == 200) {
            String body = httpResult.getBody();
            ReturnValue<Result> deserialize = JacksonHelper.deserialize(body, new TypeReference<ReturnValue<Result>>() {
            });
            if (deserialize != null && deserialize.getResult() != null) {
                return deserialize.getResult().getMedia();
            }
        }
        return null;
    }

    @Data
    public static class Result {
        private Media media;
//        private Player player;
    }

    @Data
    public static class Media {
        private Image images;
//        private List<Object> cuepoints;
        private List<Qualities> qualities;
//        private List<Object> timedtexts;
        private Info info;
    }

    @Data
    public static class Player {
        // 暂未用到，未实现
    }

    @Data
    public static class Image {
        private String cover;
        private Preview preview;
    }

    @Data
    public static class Preview {
        private int unit;
        private List<String> urls;
        private int width;
        private int height;
    }

    @Data
    public static class Qualities {
        private int isH265;
        private int wm;
        private String copy;
        private int value;
        private String desc;
    }

    @Data
    public static class Info {
        private int duration;
        private String uid;
        private long vtag;
        private int mt;
        private String title;
    }
}
