package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.*;
import com.autohome.applet.model.dto.baiduex.FaultLightDao;
import com.autohome.applet.model.dto.baiduex.WarningLightsResult;
import com.autohome.applet.service.javaapi.BaiduExService;
import com.autohome.applet.util.ExcelUtils;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BaiduExServiceImpl implements BaiduExService {

    public static final List<Plate> platesList = new ArrayList<>();

    public static final Map<String, Object> lightMap = new HashMap<>();

    private void LoadLoaclFilePlateContent() throws IOException {

        InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/Plate.xlsx");
        List<NumberPlate> _alllightLists = ExcelUtils.read(path, NumberPlate.class);

        for (int i = 0; i < _alllightLists.size(); i++) {
            NumberPlate _oplate = _alllightLists.get(i);
            int _index = i +1;
            String _shortname = _oplate.getShortname();
            String _letter = _oplate.getNumberplate().substring(1);
            String _numberplate = _oplate.getNumberplate();
            String _province = _oplate.getProvince();
            String _city = _oplate.getCity();
            int _provinceId =  _oplate.getProvinceId();
            int _cityId = _oplate.getCityId();
            int _capital = _oplate.getCapital();

            Plate _plate = new Plate(_index, _shortname, _letter, _province, _city, _numberplate, _provinceId, _cityId, _capital);
            platesList.add(_plate);
        }

//        if (shortnameList != null && shortnameList.size() > 0) {
//            // 处理csv 文件,如文件有修改，请重新修改class 对应的参数
//            for (Integer index : shortnameList.keySet()) {
//                if (index == 0) continue;
//                String s = shortnameList.get(index).replaceAll("\\uFEFF", "");
//
//                if (StringUtils.isNullOrEmpty(s))
//                    continue;
//
//                String[] arr = s.split(",");
//                if (arr == null || arr.length != 7) {
//                    continue;
//                }
//
//                String _shortname = arr[0];
//                String _letter = arr[1].substring(1);
//                String _numberplate = arr[1];
//                String _province = arr[2];
//                String _city = arr[3];
//                int _provinceId =  Integer.parseInt(arr[4].trim());
//                int _cityId = Integer.parseInt(arr[6].trim());
//
//                NumberPlate _plate = new NumberPlate(index, _shortname, _letter, _province, _city, _numberplate, _provinceId, _cityId);
//                platesList.add(_plate);
//            }
//        }
    }

    private void LoadLoaclFileFaultLightContent() throws IOException {

        InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/Faultlight.xlsx");
        List<FaultLight> _alllightLists = ExcelUtils.read(path, FaultLight.class);


        List<String> _types = new ArrayList<String>(){{
            add("红色指示");
            add("黄色指示");
            add("状态指示(蓝/绿)");
        }};

//        for (FaultLight light: _alllightLists
//             ) {
//            String _type = light.getType();
//            if (_types.contains(_type) == false) {
//                _types.add(_type);
//            }
//        }


        List<FaultLight> _commonList = _alllightLists.stream().filter(s -> s.getCommon() == 1).collect(Collectors.toList());
        List<List<FaultLight>> _typesLightList = new ArrayList<>();

        for (String _type :_types) {
            _typesLightList.add(new ArrayList<>());
        }

        for (FaultLight _light:_alllightLists) {
            int _index = _types.indexOf(_light.getType());
            if (_index >= 0 && _index < _typesLightList.size()) {
                _typesLightList.get(_index).add(_light);
            } else {
                log.warn("error light.type " + _light.getType() + " " + _light.getName());
            }
        }


        lightMap.put("all", _alllightLists);
        lightMap.put("common", _commonList);
        lightMap.put("types", _typesLightList);
    }

    @Override
    public ReturnValue<?> getNumberPlateInfo() throws IOException {


        if (platesList.size() == 0) {
            this.LoadLoaclFilePlateContent();
        }

        return new ReturnValue(0, "success", platesList);
    }


    @Override
    public ReturnValue<?> getfaultLightInfo() throws IOException {

        if(lightMap.size() ==0) {
            this.LoadLoaclFileFaultLightContent();
        }

        return new ReturnValue(0, "success", lightMap);
    }

    @Override
    public ReturnValue<?> getfaultLightInfoById(int id) throws IOException {
        if(lightMap.size() ==0) {
            this.LoadLoaclFileFaultLightContent();
        }

        List<FaultLight> _allLights = (List<FaultLight>) lightMap.get("all");

        FaultLight light = _allLights.stream()
                .filter(u -> u.getId()==id)
                .findFirst()
                .orElse(null);

        return new ReturnValue(0, "success", light);
    }

    @Override
    public ReturnValue<?> updateFaultLightInfo(FaultLightDao[] faultLights) {
        return null;
    }

    @Override
    public List<Map<String, Object>> analyzeFaultLight(String uuid, String base64Img) throws IOException {
        if(lightMap.size() ==0) {
            this.LoadLoaclFileFaultLightContent();
        }
        Map<Integer, FaultLight> faultLightMap = ((List<FaultLight>) lightMap.get("all")).stream().collect(Collectors.toMap(FaultLight::getId, v -> v, (o, n) -> o));

        HashMap<String, Object> param = new HashMap<>();
        param.put("uuid", uuid);
        param.put("in_type", "base64");
        param.put("img", new ArrayList<String>() {{
            add(base64Img);
        }});
        String body = JacksonHelper.serialize(param);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Authorization", "Basic " + Base64.getEncoder().encodeToString("applet-apiD9If4r4A:TvGIded6xhoa".getBytes(StandardCharsets.UTF_8)));

        StopWatch sw = new StopWatch();
        sw.start();
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance(1000).httpPostJson("http://imageaip.corpautohome.com/DashboardWarningLight_Baidu", body, headers);
        if (!httpResult.isSuccess()) {
            throw new BusinessException("调用识别接口失败", 10001);
        }
        sw.stop();
        log.info("识别故障灯接口用时：" + sw.getTotalTimeMillis());
        ReturnValue<WarningLightsResult> deserialize = httpResult.deserialize(new TypeReference<ReturnValue<WarningLightsResult>>() {
        });
//        if (deserialize == null) {
//            deserialize = JacksonHelper.deserialize("{\n" +
//                    "    \"returncode\": 0,\n" +
//                    "    \"message\": \"Success\",\n" +
//                    "    \"result\": {\n" +
//                    "        \"classifytime\": 11.436200857162476,\n" +
//                    "        \"img_url\": \"\",\n" +
//                    "        \"label\": 2,\n" +
//                    "        \"labelconfidence\": 0,\n" +
//                    "        \"reqid\": \"test\",\n" +
//                    "        \"runtime\": 11.436200857162476,\n" +
//                    "        \"warninglights\": [\n" +
//                    "            {\n" +
//                    "                \"pvid\": \"test\",\n" +
//                    "                \"recog_time\": 0,\n" +
//                    "                \"detect_time\": 0,\n" +
//                    "                \"resultpage_url\": \"autohome://flutter?url=flutter%3A%2F%2Fyongche%2Ffaultlampresultpage%3Freqid%3Dtest\",\n" +
//                    "                \"warningcode\": [\n" +
//                    "                    {\n" +
//                    "                        \"confidence\": \"0.91985524\",\n" +
//                    "                        \"detectconfidence\": \"0.91985524\",\n" +
//                    "                        \"positionid\": 0,\n" +
//                    "                        \"warningid\": \"7\",\n" +
//                    "                        \"warningname\": \"发动机故障灯/尾气排放故障灯\",\n" +
//                    "                        \"explain\": \"1.与发动机相关的所有故障都会亮起此灯??缺缸、混合气浓度过浓、变速箱阀体等，请尽快联系4S店或修理厂 2.表示发动机尾气排放不稳定或尾气排放超标\",\n" +
//                    "                        \"sample_url\": \"https://x.autoimg.cn/vrcar/cv/warninglights/sample/7.png\",\n" +
//                    "                        \"position\": [\n" +
//                    "                            0.27239583333333334,\n" +
//                    "                            0.5333333333333333,\n" +
//                    "                            0.39166666666666666,\n" +
//                    "                            0.6907407407407408\n" +
//                    "                        ]\n" +
//                    "                    }\n" +
//                    "                ]\n" +
//                    "            }\n" +
//                    "        ]\n" +
//                    "    }\n" +
//                    "}", new TypeReference<ReturnValue<WarningLightsResult>>() {
//            });
//        }
        if (deserialize == null || deserialize.getResult() == null) {
            ReturnValue returnValue = httpResult.deserialize(ReturnValue.class);
            if (returnValue.getReturncode() != 0) {
                throw new BusinessException(returnValue.getMessage(), returnValue.getReturncode());
            }
            throw new BusinessException("解析错误", 10002);
        }
        List<Map<String, Object>> list = new ArrayList<>();
        deserialize.getResult().getWarningLights().stream().flatMap(l -> l.getWarningCodes().stream()).forEach(code -> {
            {
                FaultLight faultLight = faultLightMap.get(Integer.parseInt(code.getWarningId()));
                if (faultLight != null) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("light", faultLight);
                    item.put("source", code);
                    list.add(item);
                }
            }
        });
        return list;
    }
}
