package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl.buycar;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecPvResult;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCar1Service;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Slf4j
@Service
public class BuyCar1ServiceImpl implements BuyCar1Service {


    /**
     * 根据车系id获取各个车型关注人数
     *
     * @param APPKEY
     * @param seriesids
     * @return
     */
    @Override
    public ResponseContent<SpecPvResult> attentionspec(String APPKEY, String seriesids) {
        try {
            HashMap<String, Object> param = new HashMap<>();
            if (!StringUtil.isNotNullAndWhiteSpace(APPKEY)) {
                //app方说是写死
                APPKEY = "B8D0049B5A6144C6CBBD4156AE3BF345";
            }
            param.put("APPKEY", APPKEY);
            param.put("seriesids", seriesids);

            //筛选车系
//            String result = carApi.get("bdp.api.attentionspec", param);
//            ResponseContent<SpecPvResult> carResponse = JSON.parseObject(result, new TypeReference<ResponseContent<SpecPvResult>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://lnglat.openapi.corpautohome.com/attention/spec", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }

            return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecPvResult>>() {
            });

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("attentionspec-WithException", e.getMessage())));
            log.error("attentionspec-WithException", e);
            return null;
        }

    }

}
