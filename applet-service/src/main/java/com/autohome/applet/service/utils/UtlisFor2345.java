package com.autohome.applet.service.utils;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.TreeMap;

public class UtlisFor2345 {
    // 签名生成函数
     public static String generateSignature(Map<String, Object> data, String secretKey) {
        // data不参与加密
        data.remove("data");

        // 对数据进行排序
        TreeMap<String, Object> sortedData = new TreeMap<>(data);

        // 拼接数据，并加上 secretKey
        StringBuilder str = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedData.entrySet()) {
            str.append(entry.getKey()).append(entry.getValue());
        }
        str.append(secretKey);

        // 使用 MD5 加密生成签名
        return DigestUtils.md5Hex(str.toString());
    }
}
