package com.autohome.applet.service.netcoreapi;

import com.autohome.applet.model.dto.netcoreapi.spec.SpecBaseInfo;
import com.autohome.applet.model.dto.netcoreapi.spec.SpecConfigV2;

import java.util.List;

public interface SpecService {

    SpecConfigV2 carParamPkConfigV2(
            String specId,
            String cityId,
            int showCps,
            String requestId,
            String positionId,
            String deviceId,
            String cpsPositionId,
            int showUsedPrice,
            int seriesId,
            int type
    );

    /**
     * 批量获取车型id
     * */
    List<SpecBaseInfo> specBaseInfoList(String specIds);
}
