package com.autohome.applet.service.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: <PERSON>Bo<PERSON><PERSON>
 * @date: 2024-01-16
 **/
@Slf4j
public class EncodesUtils {

    @SneakyThrows
    public static String urlEncode(String part) {
        try {
            return URLEncoder.encode(part, "UTF-8");
        } catch (UnsupportedEncodingException var2) {
            log.error("EncodesUtils urlEncode :" ,var2);
            throw var2;
        }
    }

}
