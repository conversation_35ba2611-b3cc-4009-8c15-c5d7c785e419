package com.autohome.applet.service.resourcefeedpush;

import com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;

import java.util.List;

public interface ResourceFeedService {
    /**
     * 获取resource_feed表数据
     */
    List<PushResourceFeedDataDTO> listPushResourceFeedDataDTO(PushResourceFeedDataQuery pushResourceFeedDataQuery);

    /**
     * 获取需要push的总数
     */
    List<PushResourceFeedCountDTO> countPushResourceFeedDataDTO(PushResourceFeedDataQuery pushResourceFeedDataQuery);
}
