package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CommonApiService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;

/**
 * Created by hanshanfeng on 2018/5/8.
 */
@Slf4j
@Service
public class BaseApiService implements CommonApiService {
    @Override
    public ResponseContent<?> GetWeatherLimitNum(int cityid) {
        try {
            //long startTime = System.currentTimeMillis();
            HashMap<String, Object> param = new HashMap<>();
            param.put("cityid", Integer.toString(cityid));
//            String result = carApi.get("wzinner.api.GetWeatherLimitNum", param);//HttpHelper.getResponse("http://wzinner.qichecdn.com/ashx/internalapi/GetWeatherLimitNum.ashx", param, null).getContent();
//            long endTime = System.currentTimeMillis();
//            long costTime = endTime - startTime;
//            if (costTime >= 100) {
//                LogHelper.logTrace("GetWeatherLimitNum1:" + costTime / 100 + "倍于100ms-", "URL:" + Integer.toString(cityid) + "实际响应时间" + Long.toString(costTime));
//            }
//            startTime = System.currentTimeMillis();
//            if (!StringUtil.isNotNullAndWhiteSpace(result)) {
//                return null;
//            }
//            ResponseContent<HashMap> carResponse = JSON.parseObject(result, new TypeReference<ResponseContent<HashMap>>() {
//            });
//            if (carResponse != null && carResponse.getResult() != null) {
//                carResponse.getResult().put("nowtime", Long.toString(new Date().getTime()));
//                endTime = System.currentTimeMillis();
//                costTime = endTime - startTime;
//                if (costTime >= 100) {
//                    LogHelper.logTrace("GetWeatherLimitNum2:" + costTime / 100 + "倍于100ms-", "URL:" + Integer.toString(cityid) + "实际响应时间" + Long.toString(costTime));
//                }
//                return carResponse;
//            }
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://wzinner.qichecdn.com/ashx/internalapi/GetWeatherLimitNum.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }

            ResponseContent<HashMap> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<HashMap>>() {
            });
            if (responseContent == null || responseContent.getResult() == null) {
                return null;
            }
            responseContent.getResult().put("nowtime", Long.toString(new Date().getTime()));
            return responseContent;

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("GetWeatherLimitNum-WithException" + Integer.toString(cityid), e.getMessage())));
            log.error("GetWeatherLimitNum error", e);
            return null;
        }

    }
}
