package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.javaapi.MotoService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MotoServiceImpl implements MotoService {

    @Override
    public Map<String, Object> seriesTabAndContent(int brandId, int seriesId, String specIds, String cityId, double lat, double lng) {
        CompletableFuture<Object> specGroupList = getSpecGroupList(seriesId);
        CompletableFuture<Object> tabCardInfo = getTabCardInfo(brandId, seriesId, "2,10"); // 2资讯、10口碑
        CompletableFuture<Object> dealerInfo = getDealerInfo(brandId, seriesId, cityId, lat, lng).thenApply(new Function<Object, Object>() {
            @Override
            public Object apply(Object o) {
                Map<Object, Object> map = new HashMap<>();
                if (o == null) {
                    CompletableFuture<Object> dealerInfoNoLocal = getDealerInfo(seriesId, cityId);
                    try {
                        Object o1 = dealerInfoNoLocal.get();
                        map.put("nonlocal", o1);
                        return dealerInfoNoLocal.get();
                    } catch (InterruptedException | ExecutionException e) {
                        e.printStackTrace();
                        return null;
                    }
                } else {
                    map.put("local", o);
                    return map;
                }
            }
        });

        CompletableFuture<Object> specParamList = getSpecParamList(seriesId, specIds);

        CompletableFuture<Map<String, Object>> thenApply = CompletableFuture.allOf(specGroupList, tabCardInfo, dealerInfo, specParamList).thenApply(v -> {
            try {
                Object tab1 = specGroupList.get();
                Object tab2 = tabCardInfo.get();
                Object tab3 = dealerInfo.get();
                Object tab4 = specParamList.get();
                Map<String, Object> map = new HashMap<>();
                map.put("tab_speclist", tab1 == null ? Collections.EMPTY_LIST : tab1);
                if (tab2 instanceof Map) {
                    Object wonderfulNewsInfo = ((Map<?, ?>) tab2).get("wonderfulnewsinfo");
                    Object koubeiInfo = ((Map<?, ?>) tab2).get("koubeiinfo");
                    map.put("tab_news", wonderfulNewsInfo == null ? new Object() : wonderfulNewsInfo);
                    map.put("tab_koubei", koubeiInfo == null ? new Object() : koubeiInfo);
                }
                map.put("tab_dealer", tab3);
                map.put("tab_specparam", tab4 == null ? Collections.EMPTY_LIST : tab4);
                return map;
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
            return null;
        });
        try {
            return thenApply.get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        return null;
    }

    private CompletableFuture<Object> getSpecGroupList(int seriesId) {
        // https://carservice.autohome.com.cn/motorapi/getspecgrouplistbyseriesid?seriesId=10006289&_appid=car
        return CompletableFuture.supplyAsync(() -> {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("https://carservice.autohome.com.cn/motorapi/getspecgrouplistbyseriesid?_appid=car&seriesId=" + seriesId);
            if (httpResult.getStatusCode() == 200) {
                ReturnValue<?> deserialize = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
                if (deserialize != null && deserialize.getResult() != null) {
                    if (deserialize.getResult() instanceof  Map) {
                        return ((Map<?, ?>) deserialize.getResult()).get("yearlist");
                    }
                }
            }
            return null;
        });
    }

    private CompletableFuture<Object> getTabCardInfo(int brandId, int seriesId, String tabIds) {
        // https://cars.app.autohome.com.cn/carext/motoseriessummary/tabcardinfo?pm=1&brandid=10000001&seriesid=10000444&tabids=2,10&pluginversion=11.40.0
        return CompletableFuture.supplyAsync(() -> {
            Map<String, Object> fields = new HashMap<>();
            fields.put("pm", 1);
            fields.put("tabids", tabIds);
            fields.put("pluginversion", "11.40.0");
            fields.put("brandid", brandId);
            fields.put("seriesid", seriesId);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("https://cars.app.autohome.com.cn/carext/motoseriessummary/tabcardinfo", fields);
            if (httpResult.getStatusCode() == 200) {
                ReturnValue<?> deserialize = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
                if (deserialize != null && deserialize.getResult() != null) {
                    return deserialize.getResult();
                }
            }
            return null;
        });
    }

    // 查询经销商列表 非本地
    private CompletableFuture<Object> getDealerInfo(int seriesId, String cityId) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, Object> fields = new HashMap<>();
            fields.put("_appid", "car");
            fields.put("seriesId", seriesId);
            fields.put("cityId", cityId);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://moto-dealerapi.corpautohome.com/dealer/moto/nonlocal/dealerInfos", fields);
            if (httpResult.getStatusCode() == 200) {
                ReturnValue<?> deserialize = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
                if (deserialize != null && deserialize.getResult() != null) {
                    return deserialize.getResult();
                }
            }
            return null;
        });
    }

    // 查询经销商列表 本地
    private CompletableFuture<Object> getDealerInfo(int brandId, int seriesId, String cityId, double lat, double lng) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, Object> fields = new HashMap<>();
            fields.put("_appid", "car");
            fields.put("brandId", brandId);
            fields.put("seriesId", seriesId);
            fields.put("cityId", cityId);
            fields.put("sortType", 2);
            fields.put("lat", lat);
            fields.put("lng", lng);
            fields.put("pageIndex", 1);
            fields.put("pageSize", 20);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://moto-dealerapi.corpautohome.com/dealer/moto/dealerInfos", fields);
            if (httpResult.getStatusCode() == 200) {
                ReturnValue deserialize = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
                if (deserialize != null && deserialize.getResult() != null) {
                    if (deserialize.getResult() instanceof Map) {
                        Object list = ((Map<?, ?>) deserialize.getResult()).get("list");
                        if (list instanceof List && ((List<?>) list).size() > 0) {
                            return list;
                        }
                    }
                }
            }
            return null;
        });
    }

    private CompletableFuture<Object> getSpecParamList(int seriesId, String specIds) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, Object> fields = new HashMap<>();
            fields.put("_appid", "car");
            fields.put("seriesId", seriesId);
            fields.put("specIds", specIds);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("https://carservice.autohome.com.cn/motorapi/spec_paramlistbyspeclist?", fields);
            if (httpResult.getStatusCode() == 200) {
                ReturnValue deserialize = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
                if (deserialize != null && deserialize.getResult() != null) {
                    if (deserialize.getResult() instanceof Map) {
                        return ((Map<?, ?>) deserialize.getResult()).get("paramtypeitems");
                    }
                }
            }
            return null;
        });
    }
}
