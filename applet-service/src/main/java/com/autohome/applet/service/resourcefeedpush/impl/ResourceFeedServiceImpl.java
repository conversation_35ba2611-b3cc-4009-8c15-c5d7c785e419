package com.autohome.applet.service.resourcefeedpush.impl;

import com.autohome.applet.dao.javaapi.mapper.ResourceFeedMapper;
import com.autohome.applet.dao.javaapi.model.ResourceFeedWithBLOBs;
import com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO;
import com.autohome.applet.dao.javaapi.model.query.ResourceFeedPushDataQuery;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ResourceFeedServiceImpl implements ResourceFeedService {
    @Autowired
    private ResourceFeedMapper resourceFeedMapper;

    @Override
    public List<PushResourceFeedDataDTO> listPushResourceFeedDataDTO(PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        ResourceFeedPushDataQuery daoResourceFeedPushDataQuery = new ResourceFeedPushDataQuery();
        //对象字段copy
        BeanUtils.copyProperties(pushResourceFeedDataQuery, daoResourceFeedPushDataQuery);

        List<ResourceFeedWithBLOBs> resourceFeedWithBLOBsList = resourceFeedMapper.listResourceFeedWithBLOBs(daoResourceFeedPushDataQuery);

        //对象转换
        if(!CollectionUtils.isEmpty(resourceFeedWithBLOBsList)){
            return resourceFeedWithBLOBsList.stream().map(r ->{
                PushResourceFeedDataDTO pushResourceFeedDataDTO = new PushResourceFeedDataDTO();
                BeanUtils.copyProperties(r, pushResourceFeedDataDTO);
                return pushResourceFeedDataDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<PushResourceFeedCountDTO> countPushResourceFeedDataDTO(PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        ResourceFeedPushDataQuery daoResourceFeedPushDataQuery = new ResourceFeedPushDataQuery();
        //对象字段copy
        BeanUtils.copyProperties(pushResourceFeedDataQuery, daoResourceFeedPushDataQuery);

        List<PushResourceFeedCountDTO> countList = resourceFeedMapper.countResourceFeedWithBLOBs(daoResourceFeedPushDataQuery);
        return countList;
    }
}
