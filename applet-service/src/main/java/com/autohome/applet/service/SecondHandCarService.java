package com.autohome.applet.service;

import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfo;
import com.autohome.applet.dao.javaapi.model.SecondHandCarOrgInfoLog;
import com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfo;
import com.autohome.applet.dao.javaapi.model.SecondHandCarProductInfoLog;

import java.util.List;

/**
 * 二手车业务接口
 * */
public interface SecondHandCarService {
    //存储消费kafka的org
    boolean saveSecondHandCarOrgInfo(SecondHandCarOrgInfo secondHandCarOrgInfo);
    //存储消费kafka的日志
    boolean saveSecondHandCarOrgInfoLog(SecondHandCarOrgInfoLog secondHandCarOrgInfoLog);
    //存储消费kafka的product
    boolean saveSecondHandCarProductInfo(SecondHandCarProductInfo secondHandCarProductInfo);
    //存储消费kafka的product的日志
    boolean saveSecondHandCarProductInfoLog(SecondHandCarProductInfoLog secondHandCarProductInfoLog);

    //更新org表的version
    boolean updateOrgVersion(SecondHandCarOrgInfo secondHandCarOrgInfo);

    //更新org表的version和状态 版本更新为下一个版本,保障下次能推送
    boolean updateOrgStatus(SecondHandCarOrgInfo secondHandCarOrgInfo);

    //更新product表的version和状态 版本更新为下一个版本,保障下次能推送
    boolean updateProductStatus(SecondHandCarProductInfo secondHandCarProductInfo);

    //拉取需要推送的二手车数据(按照最新的更新时间,拉取门店数据)
    List<SecondHandCarOrgInfo> getSecondCarToAppletOrgDtoList(long lastVersion);

    //拉取需要推送的二手车数据(按照最新的更新时间,拉取门店数据)
    List<SecondHandCarOrgInfo> listByOrgIds(List<String> orgIds);

    //更新product表的version
    boolean updateProductVersionBatch(List<String> serverIdList, Long version);

    //获取门店下所有的商品
    List<SecondHandCarProductInfo> listByVersion(String orgId, Long version);
    int countProductByVersion(String orgId, Long version);

    /**
     * params:
     * SecondHandCarOrgInfo 门店信息
     * lastVersion 上次上报版本, 本次上报 版本号 > lastVersion的数据
     * currentVersion 当前执行版本, 上传完毕后, 要更新org和product为当前版本
     * errorVersion 上传失败更新版本, 上传失败后, 要更新org和product为此版本, 方便下次继续重新执行
     * */
    void uploadToBaidu(SecondHandCarOrgInfo secondHandCarOrgInfo, Long lastVersion, Long currentVersion, Long errorVersion);

    /**
     * 获取上传百度的redis key, 存储上次同步百度的版本号
     * */
    String getUploadBaiduKey();

//    //拉取需要推送的二手车数据(按照最新的更新时间,拉取门店数据)
//    SecondCarToAppletOrgDto getSecondCarToAppletOrgDto();
}
