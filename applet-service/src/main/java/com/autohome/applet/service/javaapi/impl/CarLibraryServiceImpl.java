package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.carlibrary.*;
import com.autohome.applet.service.javaapi.CarLibraryService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CarLibraryServiceImpl implements CarLibraryService {

//    @Autowired
//    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    @Qualifier("liveplatform")
    RedisClient redisTemplate;

    @Override
    public BrandInfoDto getBrandList() {
        String key = "applet:car:brands";
        //Object o = redisTemplate.opsForValue().get(key);
        Object o = redisTemplate.get(key);
        if (o != null) {
            return JacksonHelper.deserialize(o.toString(), BrandInfoDto.class);
        }

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/javascript/brand.ashx?_appid=car&state=0X001F&typeid=0&isfilterspecimage=1");
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<BrandInfoDto> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<BrandInfoDto>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                BrandInfoDto brandInfoDto = response.getResult();
                if (brandInfoDto != null && !CollectionUtils.isEmpty(brandInfoDto.getBranditems())) {
                    //redisTemplate.opsForValue().set(key, JacksonHelper.serialize(brandInfoDto), 1, TimeUnit.HOURS);
                    redisTemplate.set(key, JacksonHelper.serialize(brandInfoDto), 1, TimeUnit.HOURS);
                    return brandInfoDto;
                }
            }
        } else {
            log.warn("request usercenter getBrandList url failed, response:{}", JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public SeriesInfoDto getSeriesByBrandId(int brandId) {
        String key = "applet:car:series:by:bid:" + brandId;
        //Object o = redisTemplate.opsForValue().get(key);
        Object o = redisTemplate.get(key);
        if (o != null) {
            return JacksonHelper.deserialize(o.toString(), SeriesInfoDto.class);
        }

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/javascript/seriesbybrand.ashx?_appid=car&brandid=" + brandId + "&typeid=0&state=0X001F&isfilterspecimage=1");
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<SeriesInfoDto> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SeriesInfoDto>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                SeriesInfoDto seriesInfoDto = response.getResult();
                if (seriesInfoDto != null && !CollectionUtils.isEmpty(seriesInfoDto.getSeriesitems())) {
                    //redisTemplate.opsForValue().set(key, JacksonHelper.serialize(seriesInfoDto), 1, TimeUnit.HOURS);
                    redisTemplate.set(key, JacksonHelper.serialize(seriesInfoDto), 1, TimeUnit.HOURS);
                    return seriesInfoDto;
                }
            }
        } else {
            log.warn("request usercenter getBrandList url failed, response:{}", JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public SeriesDetailDto getSeriesBySeriesId(int seriesId) {
        String seriesDetailUrl = "http://car.api.autohome.com.cn/v2/CarPrice/Series_ParamBySeriesId.ashx?_appid=car&seriesid=" + seriesId;
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(seriesDetailUrl);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<SeriesDetailDto> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SeriesDetailDto>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                return response.getResult();
            } else {
                log.warn("request usercenter getSeriesBySeriesId  result error, params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request usercenter getSeriesBySeriesId  url failed,  params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public SpecDto getSpecsBySeriesId(int seriesId) {
        String url = "https://car.api.autohome.com.cn/v2/carprice/spec_detailbyseriesId.ashx?_appid=car&seriesid=" + seriesId + "&state=0X001F";
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<SpecDto> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<SpecDto>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                return response.getResult();
            } else {
                log.warn("request usercenter getSpecsBySeriesId  result error, params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request usercenter getSpecsBySeriesId  url failed,  params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public ElectricSpecDto getElectricSpecsBySeriesId(int seriesId) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("seriesid", seriesId);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/App/Electric_SpecParamBySeriesId.ashx", param);
        if (httpGet.isSuccess()) {
            ReturnValue<ElectricSpecDto> deserialize = httpGet.deserialize(new TypeReference<ReturnValue<ElectricSpecDto>>() {
            });
            if (deserialize != null) {
                return deserialize.getResult();
            }
        }
        return null;
    }

    @Override
    public CarImgDto getCarImgsBySeriesId(int seriesId, int classid) {
        String url = "http://car.api.autohome.com.cn/v1/carpic/picclass_pictureitemsbyseriesId.ashx?_appid=car&seriesid=" + seriesId + "&classid=" + classid;
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<CarImgDto> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarImgDto>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                return response.getResult();
            } else {
                log.warn("request usercenter getCarImgsBySeriesId  result error, params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request usercenter getCarImgsBySeriesId  url failed,  params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public CarColorDto getCarColorDtoBySeriesId(int seriesId, String state) {
        String url = "http://car.api.autohome.com.cn/v1/carpic/piccolor_coloritemsbyseriesid.ashx?_appid=car&seriesid=" + seriesId + "&state=" + state;
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<CarColorDto> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarColorDto>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                return response.getResult();
            } else {
                log.warn("request usercenter getCarColorDtoBySeriesId  result error, params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request usercenter getCarColorDtoBySeriesId  url failed,  params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public List<NewenergyCarDto> getNewenergyCarInfo(String seriesId) {
        String url = "http://car.api.autohome.com.cn/v1/App/Electric_ParamBySeriesId.ashx?_appid=car&seriesid=" + seriesId;
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<List<NewenergyCarDto>> response = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<NewenergyCarDto>>>() {
            });
            if (response != null && response.getReturncode() == 0) {
                return response.getResult();
            } else {
                log.warn("request usercenter getCarColorDtoBySeriesId  result error, params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request usercenter getCarColorDtoBySeriesId  url failed,  params:{}, response:{}", seriesId, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

}
