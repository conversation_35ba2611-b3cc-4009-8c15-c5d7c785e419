package com.autohome.applet.service.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.newenergy.SeriesInfoNewEnergyKBResult;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: WangBoWen
 * @date: 2024-01-16
 **/
@Service
@Slf4j
public class SelectCarPortalDAL {

    private static final String SERIES_INFO_NEW_ENERGY_KOUBEI_URL="http://koubei.api.sjz.autohome.com.cn/api/nev/car/series_info";

    public SeriesInfoNewEnergyKBResult getSeriesInfoNewEnergyKBResult(int cityId, int seriesId) {
        String url = SERIES_INFO_NEW_ENERGY_KOUBEI_URL;
        Map<String, Object> param = new HashMap<>();
        param.put("_appid","koubei");
        param.put("cityId", cityId);
        param.put("seriesId", seriesId);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("getSeriesInfoNewEnergyKBResult {} is error result: {}", url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())){
            log.warn("getSeriesInfoNewEnergyKBResult 源接口调用为null url:{} param:{}",url,param);
            return null;
        }

        return JacksonHelper.deserialize(httpResult.getBody(),SeriesInfoNewEnergyKBResult.class);
    }
}
