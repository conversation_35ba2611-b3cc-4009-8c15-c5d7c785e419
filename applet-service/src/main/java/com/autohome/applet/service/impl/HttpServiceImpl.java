package com.autohome.applet.service.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.CodeMsg;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HttpServiceImpl implements HttpService {

    @Override
    public Map<String, Object> getParam(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        request.getParameterMap().forEach((k, v) -> {
            map.put(k, (v != null && v.length > 0) ? v[0] : null);
        });
        return map;
    }

    @Override
    public String httpGet(String url, Map<String, Object> param) {
        return httpGet(url, param, null);
    }

    @Override
    public String httpGet(String url, Map<String, Object> param, Map<String, Object> header) {
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet(url, param, header);
        if (httpGet.getStatusCode() != 200) {
            throw new BusinessException("http response code is not 200 (" + httpGet.getStatusCode() + ")", 1001);
        }
        if (httpGet.getBody() == null) {
            throw new BusinessException("http response body is null", 1002);
        }
        return httpGet.getBody();
    }

    @Override
    public String httpGet(String url, Map<String, Object> param, Map<String, Object> header, long timeout) {
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance(timeout).httpGet(url, param, header);
        if (httpGet.getStatusCode() != 200) {
            throw new BusinessException("http response code is not 200 (" + httpGet.getStatusCode() + ")", 1001);
        }
        if (httpGet.getBody() == null) {
            throw new BusinessException("http response body is null", 1002);
        }
        return httpGet.getBody();
    }

    @Override
    public <T> T httpGetFor(String url, Map<String, Object> param, TypeReference<T> typeReference) {
        return httpGetFor(url, param, null, typeReference);
    }

    @Override
    public <T> T httpGetFor(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<T> typeReference) {
        String body = httpGet(url, param, header);
        try {
            T deserialize = JacksonHelper.deserialize(body, typeReference);
            if (deserialize == null) {
                throw new BusinessException("http response body parse failed", 1003);
            }
            return deserialize;
        } catch (Exception e) {
            log.error("JacksonHelper parse expection for " + url, e);
            throw new BusinessException("http response body parse exception " + e.getMessage());
        }
    }

    public <T> T httpGetFor(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<T> typeReference, long timeout) {
        String body = httpGet(url, param, header, timeout);
        try {
            T deserialize = JacksonHelper.deserialize(body, typeReference);
            if (deserialize == null) {
                throw new BusinessException("http response body parse failed", 1003);
            }
            return deserialize;
        } catch (Exception e) {
            log.error("JacksonHelper parse expection for " + url, e);
            throw new BusinessException("http response body parse exception " + e.getMessage());
        }
    }

    @Override
    public <T> T httpGetForReturnValue(String url, Map<String, Object> param, TypeReference<ReturnValue<T>> typeReference) {
        return httpGetForReturnValue(url, param, null, typeReference);
    }

    @Override
    public <T> T httpGetForReturnValue(String url, Map<String, Object> param, TypeReference<ReturnValue<T>> typeReference, long timeout) {
        return httpGetForReturnValue(url, param, null, typeReference, timeout);
    }

    @Override
    public <T> T httpGetForReturnValue(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference) {
        ReturnValue<T> tReturnValue = httpGetFor(url, param, header, typeReference);
        if (tReturnValue.getReturncode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getReturncode() + ")["
                    + tReturnValue.getMessage() + "]", 1004);
        }
        if (tReturnValue.getResult() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getResult();
    }

    @Override
    public <T> T httpGetForReturnValue(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference, long timeout) {
        ReturnValue<T> tReturnValue = httpGetFor(url, param, header, typeReference, timeout);
        if (tReturnValue.getReturncode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getReturncode() + ")["
                    + tReturnValue.getMessage() + "]", 1004);
        }
        if (tReturnValue.getResult() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getResult();
    }

    @Override
    public <T> T httpGetForCodeMsg(String url, Map<String, Object> param, TypeReference<CodeMsg<T>> typeReference) {
        return httpGetForCodeMsg(url, param, null, typeReference);
    }

    @Override
    public <T> T httpGetForCodeMsg(String url, Map<String, Object> param, TypeReference<CodeMsg<T>> typeReference, long timeout) {
        return httpGetForCodeMsg(url, param, null, typeReference, timeout);
    }

    @Override
    public <T> T httpGetForCodeMsg(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<CodeMsg<T>> typeReference) {
        CodeMsg<T> tReturnValue = httpGetFor(url, param, header, typeReference);
        if (tReturnValue.getCode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getCode() + ")["
                    + tReturnValue.getMsg() + "]", 1004);
        }
        if (tReturnValue.getResult() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getResult();
    }

    @Override
    public <T> T httpGetForCodeMsg(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<CodeMsg<T>> typeReference, long timeout) {
        CodeMsg<T> tReturnValue = httpGetFor(url, param, header, typeReference, timeout);
        if (tReturnValue.getCode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getCode() + ")["
                    + tReturnValue.getMsg() + "]", 1004);
        }
        if (tReturnValue.getResult() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getResult();
    }

    @Override
    public <T> T httpGetForCodeMsgData(String url, Map<String, Object> param, TypeReference<CodeMsg<T>> typeReference) {
        return httpGetForCodeMsgData(url, param, null, typeReference);
    }

    @Override
    public <T> T httpGetForCodeMsgData(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<CodeMsg<T>> typeReference) {
        CodeMsg<T> tReturnValue = httpGetFor(url, param, header, typeReference);
        if (tReturnValue.getCode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getCode() + ")["
                    + tReturnValue.getMsg() + "]", 1004);
        }
        if (tReturnValue.getData() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getData();
    }

    @Override
    public String httpPost(String url, Map<String, Object> param) {
        return httpPost(url, param, null);
    }

    @Override
    public String httpPost(String url, Map<String, Object> param, Map<String, Object> header) {
        HttpHelper.HttpResult httpPost = HttpHelper.getInstance().httpPost(url, param, header);
        if (httpPost.getStatusCode() != 200) {
            throw new BusinessException("http response code is not 200 (" + httpPost.getStatusCode() + ")", 1001);
        }
        if (httpPost.getBody() == null) {
            throw new BusinessException("http response body is null", 1002);
        }
        return httpPost.getBody();
    }

    @Override
    public <T> T httpPostFor(String url, Map<String, Object> param, TypeReference<T> typeReference) {
        return httpPostFor(url, param, null, typeReference);
    }

    @Override
    public <T> T httpPostFor(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<T> typeReference) {
        String body = httpPost(url, param, header);
        try {
            T deserialize = JacksonHelper.deserialize(body, typeReference);
            if (deserialize == null) {
                throw new BusinessException("http response body parse failed", 1003);
            }
            return deserialize;
        } catch (Exception e) {
            log.warn("JacksonHelper parse expection for " + url, e);
            throw new BusinessException("http response body parse exception " + e.getMessage());
        }
    }

    @Override
    public <T> T httpPostForReturnValue(String url, Map<String, Object> param, TypeReference<ReturnValue<T>> typeReference) {
        return httpPostForReturnValue(url, param, null, typeReference);
    }

    @Override
    public <T> T httpPostForReturnValue(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference) {
        ReturnValue<T> tReturnValue = httpPostFor(url, param, header, typeReference);
        if (tReturnValue.getReturncode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getReturncode() + ")["
                    + tReturnValue.getMessage() + "]", 1004);
        }
        if (tReturnValue.getResult() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getResult();
    }

    @Override
    public String httpPostJson(String url, String body) {
        return httpPostJson(url, body, null);
    }

    @Override
    public String httpPostJson(String url, String body, Map<String, Object> header) {
        HttpHelper.HttpResult httpPost = HttpHelper.getInstance().httpPostJson(url, body, header);
        if (httpPost.getStatusCode() != 200) {
            throw new BusinessException("http response code is not 200 (" + httpPost.getStatusCode() + ")", 1001);
        }
        if (httpPost.getBody() == null) {
            throw new BusinessException("http response body is null", 1002);
        }
        return httpPost.getBody();
    }

    @Override
    public <T> T httpPostJsonFor(String url, String body, TypeReference<T> typeReference) {
        return httpPostJsonFor(url, body, null, typeReference);
    }

    @Override
    public <T> T httpPostJsonFor(String url, String bodyJson, Map<String, Object> header, TypeReference<T> typeReference) {
        String body = httpPostJson(url, bodyJson, header);
        try {
            T deserialize = JacksonHelper.deserialize(body, typeReference);
            if (deserialize == null) {
                throw new BusinessException("http response body parse failed", 1003);
            }
            return deserialize;
        } catch (Exception e) {
            log.warn("JacksonHelper parse expection for " + url, e);
            throw new BusinessException("http response body parse exception " + e.getMessage());
        }
    }

    @Override
    public <T> T httpPostJsonForReturnValue(String url, String body, TypeReference<ReturnValue<T>> typeReference) {
        return httpPostJsonForReturnValue(url, body, null, typeReference);
    }

    @Override
    public <T> T httpPostJsonForReturnValue(String url, String bodyJson, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference) {
        ReturnValue<T> tReturnValue = httpPostJsonFor(url, bodyJson, header, typeReference);
        if (tReturnValue.getReturncode() != 0) {
            throw new BusinessException("source interface returncode not 0 (" + tReturnValue.getReturncode() + ")["
                    + tReturnValue.getMessage() + "]", 1004);
        }
        if (tReturnValue.getResult() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        return tReturnValue.getResult();
    }
}
