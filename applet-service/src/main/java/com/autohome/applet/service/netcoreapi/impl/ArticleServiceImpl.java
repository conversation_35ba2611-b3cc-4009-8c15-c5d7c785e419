package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.dao.netcoreapi.mapper.autoopen.DealerMiniActivityMapper;
import com.autohome.applet.dao.netcoreapi.mapper.content.ExpertBlogMapper;
import com.autohome.applet.dao.netcoreapi.mapper.weixin.SweetMapper;
import com.autohome.applet.dao.netcoreapi.model.ActivityList;
import com.autohome.applet.dao.netcoreapi.model.ExpertBlog;
import com.autohome.applet.dao.netcoreapi.model.SweetClient;
import com.autohome.applet.dao.netcoreapi.model.SweetClientCombine;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.ParamBySeries;
import com.autohome.applet.model.dto.cms.ChejiahaoDetailResult;
import com.autohome.applet.model.dto.cms.JiageDetail;
import com.autohome.applet.model.dto.netcoreapi.articleservice.ActionSweetClientRank;
import com.autohome.applet.model.dto.netcoreapi.articleservice.ActionSweetClientSlist;
import com.autohome.applet.model.dto.netcoreapi.cms.CmsArticle;
import com.autohome.applet.model.dto.netcoreapi.cms.CmsArticleSerializes;
import com.autohome.applet.model.dto.netcoreapi.cms.CmsArticleTitle;
import com.autohome.applet.model.dto.netcoreapi.cms.GetFocusImgAdsDto;
import com.autohome.applet.model.dto.netcoreapi.ics.Article;
import com.autohome.applet.model.dto.netcoreapi.output.GetArticleDetailById;
import com.autohome.applet.model.dto.netcoreapi.output.GetArticleDetailByIdNew;
import com.autohome.applet.model.dto.netcoreapi.output.GetArticleDetailByIdPage;
import com.autohome.applet.model.dto.netcoreapi.reply.LikeCount;
import com.autohome.applet.model.dto.netcoreapi.series.SpecInfo;
import com.autohome.applet.model.dto.usercenter.GetUserInfoByUserIds;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.netcoreapi.ArticleService;
import com.autohome.applet.service.netcoreapi.CmsService;
import com.autohome.applet.service.netcoreapi.SeriesService;
import com.autohome.applet.util.DateFormateUtil;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ArticleServiceImpl implements ArticleService {

    @Autowired
    SweetMapper articleServiceMapper;

    @Autowired
    DealerMiniActivityMapper dealerMiniActivityMapper;

    @Autowired
    SeriesService seriesService;

    @Autowired
    ExpertBlogMapper contentReadMapper;

    @Autowired
    ExpertBlogMapper expertBlogMapper;

    //如果需要的话，可以与.net core做隔离，以防java出错不好切换，目前没有隔离，共同使用一套缓存
    //@Qualifier("apidataredis")
    @Autowired
    @Qualifier("redishosts")
    RedisClient redisClientRedisHosts;

    @Autowired
    private HttpService httpService;

    @Autowired
    private CmsService cmsService;

    @Override
    public List<ActionSweetClientSlist> GetActionSweetClient(String cid, int clientType, int status, String rid, int cityid, int province) {
        List<SweetClient> clientList;
        if (StringUtils.isAnyBlank(rid) && StringUtils.isAnyEmpty(rid)) {
            clientList = articleServiceMapper.GetActionSweetClient(cid, clientType, status, cityid, province);
        } else {
            clientList = articleServiceMapper.GetActionSweetClientWithrid(cid, clientType, status, rid, cityid, province);
        }
        List<ActionSweetClientSlist> slit = new ArrayList<>();

        for (SweetClient item : clientList) {
            ActionSweetClientSlist s = new ActionSweetClientSlist();
            s.setSweetId(item.getSweetId());
            s.setSweetName(item.getSweetName());
            s.setClientType(item.getClientType());
            s.setJumpTo(item.getJumpTo());
            s.setImg(item.getImg());
            s.setStartTime(DateHelper.serialize(item.getStartTime(), DateHelper.DATEFORMAT_STANDARD_UTC));
            s.setEndTime(DateHelper.serialize(item.getEndTime(), DateHelper.DATEFORMAT_STANDARD_UTC));
            s.setJumpType(item.getJumpType());
            s.setOtherAppid(item.getOtherAppid());
            s.setReportPoint(item.getReportPoint());
            s.setSortNum(item.getSortNum());
            s.setImglist(Arrays.asList(item.getImg().split(",")));
            s.setShareDesc(item.getShareDesc());
            s.setShareImg(item.getShareImg());
            s.setShowPoint(item.getShowPoint());
            s.setNeedLogin(item.getNeedLogin() == null ? 0 : item.getNeedLogin());

            if (clientType == 109 || clientType == 38) {
                s.setImg(s.getImglist().stream().findFirst().orElse(""));
            }
            slit.add(s);
        }
        return slit;
    }

    @Override
    public List<ActionSweetClientRank> GetActivityList(int cityId) {
        List<ActionSweetClientRank> result = new ArrayList<>();

        List<ActivityList> list = dealerMiniActivityMapper.GetActivityList(cityId);
        int row = 10;
        for (ActivityList item : list) {
            ActionSweetClientRank rank = new ActionSweetClientRank();
            rank.setAid(item.getAid());
            rank.setImg(item.getImg());
            rank.setType(item.getType());
            rank.setTitle(item.getTitle());
            rank.setStartTime(DateHelper.serialize(item.getStartTime(), DateHelper.DATEFORMAT_SHORT));
            rank.setEndTime(DateHelper.serialize(item.getEndTime(), DateHelper.DATEFORMAT_SHORT));
            rank.setVisitCount(item.getVisitCount());
            rank.setSubmitCount(item.getSubmitCount());
            rank.setDealerIcon(item.getDealerIcon());
            rank.setDealerName(item.getDealerName());
            rank.setDealerId(item.getDealerId());
            rank.setRowNum(row);
            result.add(rank);
            row += 15;
        }

        return result;
    }

    @Override
    public ReturnValue<?> GetArticleDetailById(int articleId, int firstIndex, int pagecurrt, int type) throws ExecutionException, InterruptedException {
        if (type == 4) {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "wxcar");
            param.put("infoId", articleId);

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://chejiahao.api.lq.autohome.com.cn/InfoService.svc/GetInfoDetailFromRedis", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return ReturnValue.buildErrorResult(105, "源接口错误");
            }

            ReturnValue<ChejiahaoDetailResult> tmpModel = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<ChejiahaoDetailResult>>() {
            });
            if (tmpModel != null && tmpModel.getResult() != null) {
                int z = 0;
                int start = 0;
                ChejiahaoDetailResult item = tmpModel.getResult();
                if (item.getRecycled() || !item.getIspublish() || item.getStatus() != 1) {
                    return null;
                }
                String seriesids = StringUtils.EMPTY;
                String seriesName = StringUtils.EMPTY;
                if (item.getRelationcar() != null && item.getRelationcar().size() > 0) {
                    seriesids = item.getRelationcar().stream().filter(f -> f.getSeriesid() > 0).map(car -> String.valueOf(car.getSeriesid())).collect(Collectors.joining(","));

                    ReturnValue<List<ParamBySeries>> serieslist = getSeriesListByseriesids(seriesids);
                    if (serieslist != null) {
                        seriesName = serieslist.getResult().stream().map(ParamBySeries::getName).collect(Collectors.joining(","));
                    }
                }

                String tastr = item.getTags().size() <= 0 ? StringUtils.EMPTY : item.getTags().get(0).getTagname();
                String showKeywords = tastr;
                if (seriesName.length() > 0) {
                    tastr = seriesName + "," + tastr;
                }

                GetArticleDetailById detail = new GetArticleDetailById();
                detail.setStart(start);
                detail.setTitletype(1);
                detail.setId(item.getInfoid());
                detail.setTitle(item.getTitle());
                detail.setImg(item.getImage());
                detail.setAuthorid(item.getAuthor() == null ? 0 : item.getAuthor().getUserid());
                detail.setAuthorname(item.getAuthor() == null ? StringUtils.EMPTY : item.getAuthor().getNickname());
                detail.setAvatar(item.getAuthor() == null ? StringUtils.EMPTY : item.getAuthor().getAvatar());
                detail.setPublishtime(item.getPublishtime());
                detail.setContent(item.getArticle() != null ? GetArticleContent(pagecurrt, item.getArticle().getContent()) : StringUtils.EMPTY);
                detail.setClass1(StringUtils.EMPTY);
                detail.setClass1name(StringUtils.EMPTY);
                detail.setLookcount(item.getInfostatistics() == null ? 0 : item.getInfostatistics().getDetailpv());
                detail.setReplycount(item.getInfostatistics() == null ? 0 : item.getInfostatistics().getReplycount());
                detail.setLikecount(item.getInfostatistics() == null ? 0 : item.getInfostatistics().getPraisecount());
                detail.setViews(item.getInfostatistics() == null ? 0 : item.getInfostatistics().getPv());
                detail.setSeriesids(seriesids);
                detail.setIspublish(item.getIspublish());
                detail.setRecycled(item.getRecycled());
                detail.setInfotype(item.getInfotype());
                detail.setThumbnailimg(item.getImage().replace("autohomecar", "500x500_0_c40_autohomecar"));
                detail.setNewkeywords(tastr);
                detail.setDescription(item.getDescription());
                detail.setPcurl(item.getPcurl());
                detail.setShowKeywords(showKeywords);
                detail.setIpprovince(item.getIpprovince() == null ? StringUtils.EMPTY : item.getIpprovince());

                return ReturnValue.buildSuccessResult(detail);
            }
            return tmpModel;
        } else {

            if (articleId > 100000000) {
                return GetNewspriceArticles(articleId);
            }

            CompletableFuture<ReturnValue<CmsArticle>> getArticleDetailbyId = getArticleDetailbyId(articleId);
            CompletableFuture<ReturnValue<LikeCount>> likeCount = likeCount(articleId);
            CompletableFuture.allOf(getArticleDetailbyId, likeCount).join();

            int z = -1;
            int start = 0;
            ReturnValue<CmsArticle> tmpModel = getArticleDetailbyId.get();
            ReturnValue<LikeCount> tmpModel1 = likeCount.get();

            if (tmpModel1 != null && tmpModel.getResult() != null) {
                CmsArticle item = tmpModel.getResult();
                int voteid = ArticleVotePKTagProcess_Edit(item.getContent());
                List<Integer> voids = ArticleVotePKTagProcess_EditNew(item.getContent());

                int pkid = ArticlePKTagProcess_Edit(item.getContent());
                List<Integer> pkids = ArticlePKTagProcess_EditNew(item.getContent());
                String seriesName = StringUtils.EMPTY;
//                if (item.getSeriesId() != null && item.getSeriesId() != "")
//                {
                String seriesNamelist = String.valueOf(item.getSeriesId());

                ReturnValue<List<ParamBySeries>> serieslist = getSeriesListByseriesids(seriesNamelist);
                if (serieslist != null && serieslist.getResult() != null) {
                    seriesName = serieslist.getResult().stream().map(ParamBySeries::getName).collect(Collectors.joining(","));
                }
//                }
                //多页
                if (item.getTotalCount() > 0) {
                    if (item.getArticleSerializes() != null && item.getArticleSerializes().size() >= 2 && firstIndex == 1) {
                        //获取连载文章Id
                        int serializeOrders = item.getSerializeOrders();
                        for (CmsArticleSerializes itemArticle : item.getArticleSerializes()) {
                            int startIndex = 0;
                            if (serializeOrders == itemArticle.getOrders()) {
                                startIndex = itemArticle.getStartPage() - 1;
                            }
                            List<ExpertBlog> users = expertBlogMapper.GetExperBlogListByEditorIds(Arrays.asList(item.getAuthorId()));
                            if (users != null && users.size() > 0) {
                                item.setAvator(users.get(0).getEmemberpic());
                                item.setEditorPosition(users.get(0).getEposition());
                            }

                            GetArticleDetailByIdPage page = new GetArticleDetailByIdPage();
                            page.setEndPage(startIndex + 1);
                            page.setStart(start);
                            page.setTitletype(1);
                            page.setId(item.getId());
                            page.setTitle(item.getShortTitle() == null || item.getShortTitle() == StringUtils.EMPTY ? item.getTitle() : item.getShortTitle());
                            page.setImg(item.getImg());
                            page.setAuthorid(item.getAuthorId());
                            page.setAuthorname(item.getAuthor());
                            page.setAvatar(item.getAvator());
                            page.setPublishtime(item.getPublishTime());
                            page.setContent(GetArticleContent(startIndex, item.getContent()));
                            page.setClass1(item.getClass1());
                            page.setClass1name(item.getClass1Name());
                            page.setReplycount(item.getReplyCount());
                            page.setClickcount(item.getClickCount());
                            page.setLikecount(tmpModel1.getResult() == null ? 0 : tmpModel1.getResult().getCount());
                            page.setImages(item.getImages());
                            page.setTitleList(UptitleCurrtNew(item.getPageTitles()));
                            page.setContentCount(item.getTotalCount());
                            page.setLookcount(0);
                            page.setSeriesIds(item.getSeriesIds());
                            page.setVotePkid(voteid);
                            page.setPkid(pkid);
                            page.setVoids(voids);
                            page.setPkids(pkids);
                            page.setIspublish(item.getIsPublish());
                            page.setArticleSerializes(item.getArticleSerializes());
                            page.setThumbnailimg(item.getImg().replace("400x300_0", "500x500_0_c40_"));
                            page.setNewkeywords(seriesName);
                            page.setDescription(item.getDescription() == null ? StringUtils.EMPTY : item.getDescription());
                            page.setWebUrl(item.getWebUrl());
                            page.setMemberId(item.getMemberId());
                            page.setShowKeywords(item.getContentKeywords());
                            page.setEditProvinceName(item.getEditProvinceName() == null ? StringUtils.EMPTY : item.getEditProvinceName());

                            return ReturnValue.buildSuccessResult(page);
                        }
                    } else {
                        List<ExpertBlog> users = expertBlogMapper.GetExperBlogListByEditorIds(Arrays.asList(item.getAuthorId()));
                        if (users != null && users.size() > 0) {
                            item.setAvator(users.get(0).getEmemberpic());
                            item.setEditorPosition(users.get(0).getEposition());
                        }
                        GetArticleDetailByIdPage page = new GetArticleDetailByIdPage();
                        //page.setEndPage(startIndex + 1);
                        page.setStart(start);
                        page.setTitletype(1);
                        page.setId(item.getId());
                        page.setTitle(item.getShortTitle() == null || item.getShortTitle() == StringUtils.EMPTY ? item.getTitle() : item.getShortTitle());
                        page.setImg(item.getImg());
                        page.setAuthorid(item.getAuthorId());
                        page.setAuthorname(item.getAuthor());
                        page.setAvatar(item.getAvator());
                        page.setPublishtime(item.getPublishTime());
                        page.setContent(GetArticleContent(pagecurrt, item.getContent()));
                        page.setClass1(item.getClass1());
                        page.setClass1name(item.getClass1Name());
                        page.setReplycount(item.getReplyCount());
                        page.setClickcount(item.getClickCount());
                        page.setLikecount(tmpModel1.getResult() == null ? 0 : tmpModel1.getResult().getCount());
                        page.setImages(item.getImages());
                        page.setTitleList(UptitleCurrtNew(item.getPageTitles()));
                        page.setContentCount(item.getTotalCount());
                        page.setLookcount(0);
                        page.setSeriesIds(item.getSeriesIds());
                        page.setVotePkid(voteid);
                        page.setPkid(pkid);
                        page.setVoids(voids);
                        page.setPkids(pkids);
                        page.setIspublish(item.getIsPublish());
                        page.setArticleSerializes(item.getArticleSerializes());
                        page.setThumbnailimg(item.getImg().replace("400x300_0", "500x500_0_c40_"));
                        page.setNewkeywords(seriesName);
                        page.setDescription(item.getDescription() == null ? StringUtils.EMPTY : item.getDescription());
                        page.setWebUrl(item.getWebUrl());
                        page.setMemberId(item.getMemberId());
                        page.setShowKeywords(item.getContentKeywords());
                        page.setEditProvinceName(item.getEditProvinceName() == null ? StringUtils.EMPTY : item.getEditProvinceName());

                        return ReturnValue.buildSuccessResult(page);
                    }
                } else {
                    GetArticleDetailByIdPage page = new GetArticleDetailByIdPage();
                    //page.setEndPage(startIndex + 1);
                    page.setStart(start);
                    page.setTitletype(0);
                    page.setId(item.getId());
                    page.setTitle(item.getShortTitle() == null || item.getShortTitle() == StringUtils.EMPTY ? item.getTitle() : item.getShortTitle());
                    page.setImg(item.getImg());
                    page.setAuthorid(item.getAuthorId());
                    page.setAuthorname(item.getAuthor());
                    //page.setAvatar(item.getAvator());
                    page.setPublishtime(item.getPublishTime());
                    page.setContent(GetArticleContent(item.getContent()));
                    page.setClass1(item.getClass1());
                    page.setClass1name(item.getClass1Name());
                    page.setReplycount(item.getReplyCount());
                    page.setClickcount(item.getClickCount());
                    page.setLikecount(tmpModel1.getResult() == null ? 0 : tmpModel1.getResult().getCount());
                    page.setImages(item.getImages());
                    //page.setTitleList(UptitleCurrtNew(item.getPageTitles()));
                    //page.setContentCount(item.getTotalCount());
                    //page.setLookcount(0);
                    page.setSeriesIds(item.getSeriesIds());
                    page.setVotePkid(voteid);
                    page.setPkid(pkid);
                    page.setVoids(voids);
                    page.setPkids(pkids);
                    page.setIspublish(item.getIsPublish());
                    //page.setArticleSerializes(item.getArticleSerializes());
                    page.setThumbnailimg(item.getImg().replace("400x300_0", "500x500_0_c40_"));
                    page.setNewkeywords(seriesName);
                    page.setDescription(item.getDescription() == null ? StringUtils.EMPTY : item.getDescription());
                    page.setWebUrl(item.getWebUrl());
                    page.setMemberId(item.getMemberId());
                    page.setShowKeywords(item.getContentKeywords());
                    page.setEditProvinceName(item.getEditProvinceName() == null ? StringUtils.EMPTY : item.getEditProvinceName());

                    return ReturnValue.buildSuccessResult(page);
                }
            }
            return null;
        }
    }

    public ReturnValue<List<ParamBySeries>> getSeriesListByseriesids(String seriesids) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("serieslist", seriesids);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/carprice/series_parambyserieslist.ashx", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("getSeriesListByseriesids error");
            return null;
        }

        ReturnValue<List<ParamBySeries>> tmpModelseries = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<ParamBySeries>>>() {
        });
        if (tmpModelseries != null && tmpModelseries.getReturncode() == 0) {
            return tmpModelseries;
        }
        return null;


    }

    public String GetArticleContent(String content) throws ExecutionException, InterruptedException {
        String[] contentList = content.split("㊣");
        int totalpage = contentList.length;

        StringBuilder sb = new StringBuilder();
        for (String s : contentList) {
            sb.append(s);
        }
        return ReplaceContent(sb.toString());
    }

    //多页文章返回处理
    public String GetArticleContent(int currt, String content) throws ExecutionException, InterruptedException {

        String[] arr = content.split("㊣");
        for (String s : arr) {
            if (s.indexOf("≮") != -1) {
                //z = i;
            }

        }
        if (currt > arr.length - 1) {
            currt = arr.length - 1;
        }
        return ReplaceContent(arr[currt]);
    }

    public List<String> UptitleCurrtNew(List<CmsArticleTitle> titlelist) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < titlelist.size(); i++) {

            list.add("第" + (i + 1) + "页 " + titlelist.get(i).getTitleContent());
        }

        return list;
    }

    /// <summary>
    /// 文章内容处理
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    public String ReplaceContent(String content) throws ExecutionException, InterruptedException {
        //            var reg =/\<\!\-\-[\s\S] *?\-\-\>/ g
        //document.body.innerHTML = str.replace(reg, "");
        //  content = Regex.Replace(content, "(?<!--((.*)|(\n))*?-->)", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
        //降价排行榜，跳到www
        content = content.replace("http://m.autohome.com.cn/pricerank/", "http://www.autohome.com.cn/pricerank/");
        //车展替换，跳到m上
        content = content.replace("http://www.autohome.com.cn/chezhan/", "http://m.autohome.com.cn/chezhan/");
        //视频替换，跳到m上
        content = content.replace("http://v.autohome.com.cn/", "http://v.m.autohome.com.cn/");
        if (content.indexOf("≮") != -1) {
            content = ReplaceArticelConfig(content);
        }
        //剔除行情问文章中table/tr/td/th/tbody内的width及sytle里的width样式。
        content = content.replaceAll("(?i)(?s)(?<=<table[^>]\\*width.{1,2})(\\\\d{3}px|\\\\d{3})", "100%");
        content = content.replaceAll("(?i)(?s)(?<=<(tr|td|th|tbody).[^>]\\*)width.\\d+(px)?;?(.[^>]*>)", "$3");

        //过滤“读图模式”
        content = content.replaceAll("(?i)(?m)<P align=\"?center\"?><FONT style=\"?FONT-SIZE: 24px\"? color=#0f0cbf><STRONG>温馨提示：点击下面图片可开启游记读图模式</STRONG></FONT></P>", StringUtils.EMPTY);
        content = content.replaceAll("(?i)(?s)<P align=\"?center\"?><FONT color=#0f0cbf><STRONG>↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓</STRONG></FONT></P>", StringUtils.EMPTY);

        //剔除横线及中间内容。
        content = content.replaceAll("(?i)(?s)<DIV[^>]*title=\"Print Filter Label\".*?<DIV[^>]*title=\"Print Filter Label\".*?</DIV>", StringUtils.EMPTY);

        //行情文章中促销p标签width
        content = content.replaceAll("(?i)(?s)<p style=\"border:1px solid #FFCC99; background-color:#FFFFCC; color:#CC0000; width:\\d+px; text-align:center; margin-left:42px\">", "<p style=\"border:1px solid #FFCC99; background-color:#FFFFCC; color:#CC0000; display:inline-block;\">");

        ////文章详情页链接
        //Regex reg1 = new Regex(@"(?<=http://)www.autohome.com.cn(?=/news|market|shopping|advice|drive|accident|life|user|olympic|info|industry|use|sport|culture|tech|tuning/\d{6}/313150.html)", RegexOptions.IgnoreCase);
        //Match match = reg1.Match(content);
        //while (match.Success)
        //{

        //    content = content.Replace("www.autohome.com.cn", "m.autohome.com.cn");
        //    match = match.NextMatch();
        //}
        //content = ReplaceVideoTags(content, 2);
        //文章频道改成m链接
        content = content.replaceAll("(?i)(?s)(<\\s*a\\shref=\"\")http://www.autohome.com.cn/(news|channel|market|advice|drive|use|culture|tech|tuning)/\"\"", "$1http://m.autohome.com.cn/$2/\"\"");
        //屏蔽文章关联词链接
        content = content.replaceAll("(?i)(?s)(?<=汽车之家\\D\\*)<\\s*a[^>]*\\shref=\"http://www.autohome.com.cn/*\"*[^>]*>(([^<]|<(?!/a))*)<\\s*/a\\s*>", "$1");
        //图片懒加载，设定默认图片
        content = content.replaceAll("(?i)(?s)(?<=<img[^>]\\*)src=", "src=\"http://x.autoimg.cn/m/images/640x480_picloading.gif\" data-src=");
        //编辑博客换成m地址
        content = content.replaceAll("(?i)(?s)(?<=http://)www.autohome.com.cn(?=/ExpertBlog/Editor_\\d+.html)", "m.autohome.com.cn");

        return content;
    }

    /// <summary>
    /// 获取车型配置信息
    /// </summary>
    /// <param name="OldContent">文章内容替换前</param>
    /// <returns>包含车型配置的文章内容</returns>
    public String ReplaceArticelConfig(String OldContent) throws ExecutionException, InterruptedException {
        String result = StringUtils.EMPTY;

        result = OldContent.replaceAll("≮#[\\d|,]*≯", StringUtils.EMPTY);

        Pattern mRegex = Pattern.compile("≮\\$(([\\d|,]*)|(\\|[\\d|,]*))≯");
        Matcher mMactchCol = mRegex.matcher(result);
        String sReplaceOld = StringUtils.EMPTY;
        StringBuilder sReplaceNew = new StringBuilder();
        int i = 0;
        while (mMactchCol.find()) {
            sReplaceOld = mMactchCol.group(0);
            String ModelIDs = sReplaceOld.substring(2, sReplaceOld.length() - 3);
            if (ModelIDs.indexOf("|") != -1) {
                ModelIDs = ModelIDs.substring(0, ModelIDs.indexOf("|"));
            }
            String[] arrIDs = ModelIDs.split(",");

            SpecInfo nav;
            if (i == 0) sReplaceNew.append("<dl class=\"paramlist\"><dt>点击下列车型名称查看参数配置</dt>");
            sReplaceNew.append("<dd>");
            for (String id : arrIDs) {
                int specid = Integer.parseInt(id);
                nav = seriesService.spec_infobyspecid(specid).get().getResult();
                sReplaceNew.append("<a href=\"http://car.m.autohome.com.cn/config/spec/").append(nav.getId()).append(".html\">").append(nav.getName()).append(" ").append(nav.getSeriesname()).append("</a><br />");
            }
            sReplaceNew.append("</dd>");

            result = result.replace(sReplaceOld, sReplaceNew.toString());
            sReplaceNew.delete(0, sReplaceNew.length());
            i++;
        }
        result = result + "</dl>";

        return result;
    }

    /// <summary>
    /// 取投票id
    /// </summary>
    /// <param name="Content"></param>
    /// <returns></returns>
    public int ArticleVotePKTagProcess_Edit(String Content) {
        String strReturn = Content;
        try {
            //投票标题文字标记
            Pattern regex = Pattern.compile("(?i)(?s)<div vote-id=\"(.*?)\" class=\"card—vote\".*?></div>");
            Matcher mMactchCol = regex.matcher(Content);
            while (mMactchCol.find()) {
                int voteId = Integer.parseInt(mMactchCol.group(1));

                strReturn.replaceAll(mMactchCol.pattern().pattern(), StringUtils.EMPTY);
                return voteId;
            }
            return 0;
        } catch (Exception ex) {
            log.error("ArticleVotePKTagProcess_Edit error", ex);
            return 0;
        }
    }

    public List<Integer> ArticleVotePKTagProcess_EditNew(String Content) {
        String strReturn = Content;
        try {
            //投票标题文字标记
            Pattern regex = Pattern.compile("(?i)(?s)<div vote-id=\"(.*?)\" class=\"card—vote\".*?></div>");
            List<Integer> voids = new ArrayList<>();
            Matcher mMactchCol = regex.matcher(Content);
            while (mMactchCol.find()) {
                int voteId = Integer.parseInt(mMactchCol.group(1));
                voids.add(voteId);
                strReturn.replaceAll(mMactchCol.pattern().pattern(), StringUtils.EMPTY);
                //return voteId;
            }
            return voids;
        } catch (Exception ex) {
            log.error("ArticleVotePKTagProcess_Edit error", ex);
            return null;
        }
    }

    /// <summary>
    /// 取PKid
    /// </summary>
    /// <param name="Content"></param>
    /// <returns></returns>
    public int ArticlePKTagProcess_Edit(String Content) {
        String strReturn = Content;
        try {
            //投票标题文字标记
            Pattern regex = Pattern.compile("(?i)(?s)<div pk-id=\"(.*?)\" class=\"card—pk\".*?></div>");
            Matcher mMactchCol = regex.matcher(Content);
            while (mMactchCol.find()) {
                int pkId = Integer.parseInt(mMactchCol.group(1));

                strReturn.replaceAll(mMactchCol.pattern().pattern(), StringUtils.EMPTY);
                return pkId;
            }
            return 0;
        } catch (Exception ex) {
            log.error("ArticleVotePKTagProcess_Edit error", ex);
            return 0;
        }

    }

    /// <summary>
    /// 取PKid
    /// </summary>
    /// <param name="Content"></param>
    /// <returns></returns>
    public List<Integer> ArticlePKTagProcess_EditNew(String Content) {
        String strReturn = Content;
        try {
            List<Integer> pkids = new ArrayList<>();
            //投票标题文字标记
            Pattern regex = Pattern.compile("(?i)(?s)<div pk-id=\"(.*?)\" class=\"card—pk\".*?></div>");
            Matcher mMactchCol = regex.matcher(Content);
            while (mMactchCol.find()) {
                int pkId = Integer.parseInt(mMactchCol.group(1));
                pkids.add(pkId);
                strReturn.replaceAll(mMactchCol.pattern().pattern(), StringUtils.EMPTY);

            }
            return pkids;
        } catch (Exception ex) {
            log.error("ArticleVotePKTagProcess_Edit error", ex);
            return null;
        }

    }

    /// <summary>
    /// 获取经销商文章
    /// </summary>
    /// <returns></returns>
    public ReturnValue<?> GetNewspriceArticles(int articleId) throws ExecutionException, InterruptedException {
        CompletableFuture<ReturnValue<Article>> icsArticles = icsArticles(articleId);
        CompletableFuture<ReturnValue<LikeCount>> likeCount = likeCount(articleId);
        CompletableFuture.allOf(icsArticles, likeCount).join();

        ReturnValue<Article> tmpModelNewsprice = icsArticles.get();
        ReturnValue<LikeCount> tmpModel1 = likeCount.get();

        if (tmpModelNewsprice != null && tmpModelNewsprice.getResult() != null) {
            Article item = tmpModelNewsprice.getResult();
            if (!item.getIsPublish()) {
                return null;
            }

            List<ExpertBlog> users = contentReadMapper.GetExperBlogListByEditorIds(Collections.singletonList(item.getEditor()));
            String userAvator = StringUtils.EMPTY;
            String ePosition = StringUtils.EMPTY;
            if (users.size() > 0) {
                ExpertBlog blog = users.get(0);
                userAvator = blog.getEmemberpic();
                ePosition = blog.getEposition();
            }
            GetArticleDetailByIdNew detail = new GetArticleDetailByIdNew();
            detail.setStart(item.getSerializeStartPage());
            detail.setTitletype(1);
            detail.setId(item.getId());
            detail.setTitle(item.getTitle());
            detail.setImg(item.getImg());
            detail.setAuthorid(item.getEditor());
            detail.setAuthorname(userAvator);
            //detail.setAvatar(userAvator);
            detail.setPublishtime(item.getPublishtime());
            detail.setContent(GetArticleContent(item.getContent()));
            detail.setClass1(item.getClass1());
            detail.setClass1name(item.getClass1name());
            detail.setReplycount(item.getReplyCount());
            detail.setArticleNavigation(item.getArticleNavigation());
            detail.setLikecount(tmpModel1 == null ? 0 : tmpModel1.getResult().getCount());
            detail.setLookcount(0);
            detail.setIspublish(item.getIsPublish());
            detail.setNewkeywords(item.getContent());
            detail.setDescription(item.getShorttitle());

            return ReturnValue.buildSuccessResult(detail);
        }

        return null;
    }


    private CompletableFuture<ReturnValue<Article>> icsArticles(int articleId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<Article>>() {
            @Override
            public ReturnValue<Article> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "wxcar");
                param.put("articleId", articleId);

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://newsprice.ics.api.lq.autohome.com.cn/articles/get", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Article>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<LikeCount>> likeCount(int articleId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<LikeCount>>() {
            @Override
            public ReturnValue<LikeCount> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "wxcar");
                param.put("appid", "1");
                param.put("liketype", "1");
                param.put("objid", articleId);
                param.put("secobj", StringUtils.EMPTY);
                param.put("datatype", "json");


                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://reply.autohome.com.cn/api/like/getcount.json", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<LikeCount>>() {
                });
            }
        });
    }

    private CompletableFuture<ReturnValue<CmsArticle>> getArticleDetailbyId(int articleId) {
        return CompletableFuture.supplyAsync(new Supplier<ReturnValue<CmsArticle>>() {
            @Override
            public ReturnValue<CmsArticle> get() {
                HashMap<String, Object> param = new HashMap<>();
                param.put("_appid", "m11");
                param.put("newid", articleId);
                param.put("pageindex", "0");
                param.put("returnvideoinfo", "0");

                HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/baseapi/articles/articledetails.ashx", param);
                if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                    return null;
                }
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CmsArticle>>() {
                });
            }
        });
    }

    @Override
    public ReturnValue<?> GetJiageDeitle(int priceid) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "app");
        param.put("priceid", priceid);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://jiageapi.in.autohome.com.cn/api/carprice/GetCarPrice", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }
        return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<JiageDetail>>() {
        });
    }

    public Map<Integer, String> GetAutoHomeUserInfo(List<Integer> userIds) {
        userIds = userIds.stream().distinct().collect(Collectors.toList());
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "user");
        param.put("userIds", userIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://user.api.autohome.com.cn/api/userInfo/GetUserInfoByUserIds", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return null;
        }

        ReturnValue<List<GetUserInfoByUserIds>> tmpModel = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<GetUserInfoByUserIds>>>() {
        });

        if (tmpModel != null && tmpModel.getReturncode() == 0) {
            Map<Integer, String> map = new HashMap<>();
            for (GetUserInfoByUserIds item : tmpModel.getResult()) {
                if (!map.containsKey(item.getUserId())) {
                    map.put(item.getUserId(), GetHeadImageUrl(item.getHeadImage(), item.getSex()));
                }
            }
            return map;
        }
        return null;
    }

    public String GetHeadImageUrl(String headImageUrl, int sex) {
        //头像为空返回默认头像
        if (headImageUrl == null && headImageUrl.trim().length() == 0) {
            return sex == 1 ? "http://x.autoimg.cn/pckuaibao/img/avtar_man_34dp.png" : "http://x.autoimg.cn/pckuaibao/img/avtar_woman_34dp.png";
        }
        //完整路径头像直接返回
        if (headImageUrl.startsWith("http://")) {
            return headImageUrl;
        }
        //论坛头像数据带有 "userheaders" ，需要把"userheaders"处理掉
        headImageUrl = headImageUrl.replace("userheaders", "");
        String domain = "";
        //Filename filename = new Filename();
        return "";
    }

    public List<FocusLightForList> ActionSweetClientCombineNews0518(String cid, int clientType, int status, String sourcetype, String opuserid, String Userip, String version, int province, int city) {
        try{
            LocalDate localDate = LocalDate.now();
            //.net core里DateTime.Today 是类似这样的格式：2024/5/6 0:00:00，java这里我是这样的思路，先拿到yyyy/MM/dd，然后把/0这样的字符串替换成/，再加上 0:00:00
            String yyyyMmDd = DateFormateUtil.formatLocalDate("MM/dd/yyyy 00:00:00", localDate);//2024/5/6 0:00:00
            //yyyyMmDd = yyyyMmDd + " 0:00:00";
            String rediskey = "wxcarapi:artictlservice:ActionSweetClientCombineNewsTest:v12:%s:%d:%d:%s:%s:%d:%d" + yyyyMmDd;
            rediskey = String.format(rediskey, cid,clientType,status,sourcetype,version,province,city);
            List<FocusLightForList> focusLightForIndex = getActionSweetClientCombineNewsTestFromCache(rediskey);
            log.info("ActionSweetClientCombineNews0518 rediskey:{},focusLightForIndex:{}",rediskey,focusLightForIndex);
            if (CollectionUtils.isEmpty(focusLightForIndex)) {
                focusLightForIndex = getAppCombineNewsT(sourcetype, cid, province, city);

                boolean setCache = true;
                if (CollectionUtils.isEmpty(focusLightForIndex)) {
                    setCache = false;
                }
                List<FocusLightForList> sweets = GetActionSweetClientCombine(cid, clientType, status, province, city);

                if (!CollectionUtils.isEmpty(sweets)) {
                    sweets = sweets.stream().sorted(Comparator.comparing(FocusLightForList::getOrder1)).collect(Collectors.toList());
                    for (FocusLightForList s : sweets) {
                        List<FocusLightForList> filters = focusLightForIndex.stream().filter(l -> l.order1 >= s.order1).collect(Collectors.toList());
                        for (FocusLightForList item : filters) {
                            item.order1 = item.order1 + 1;
                        }
                    }
                    focusLightForIndex.addAll(sweets);
                }
                focusLightForIndex = focusLightForIndex.stream().sorted(Comparator.comparing(FocusLightForList::getOrder1)).limit(10).collect(Collectors.toList());
                if (focusLightForIndex.size() > 0 && setCache) {
                    redisClientRedisHosts.set(rediskey,JacksonHelper.serialize(focusLightForIndex),30, TimeUnit.MINUTES);
                }
            }

            //获取广告
            List<FocusLightForList> focusAds = new ArrayList<>();
            //支付宝小程序和QB小程序不获取广告
            if (clientType != 300 && clientType != 82) {
                focusAds = getFocusImgAds(sourcetype, opuserid, Userip, cid);
            }

            List<FocusLightForList> resutlAds = new ArrayList<>();
            List<FocusLightForList> focusAdsFS = new ArrayList<>();
            if (focusLightForIndex != null && focusLightForIndex.size() > 0) {
                resutlAds.addAll(focusLightForIndex.stream().limit(3).collect(Collectors.toList()));
                focusAdsFS = focusLightForIndex.stream().skip(3).limit(3).collect(Collectors.toList());
            }
            if (focusAds.size() > 0 && focusAdsFS.size() > 0) {
                for (int i = 0; i <= focusAds.size() - 1; i++) {
                    //广告为空
                    if (focusAds.get(i).isAsIsnull() && focusAdsFS.size() > 0) {
                        FocusLightForList firstOrDefault = focusAdsFS.get(0);
                        focusAds.get(i).setId(firstOrDefault.id);
                        focusAds.get(i).setImg(firstOrDefault.img);
                        focusAds.get(i).setTitle(firstOrDefault.title);
                        focusAds.get(i).setTime(firstOrDefault.time);
                        focusAds.get(i).setType(firstOrDefault.type);
                        focusAds.get(i).setVersion(firstOrDefault.version);
                        focusAds.get(i).setProtocol(firstOrDefault.protocol);
                        focusAds.get(i).setNewsId(firstOrDefault.newsId);
                        focusAds.get(i).setUrl(firstOrDefault.url);
                        focusAds.get(i).setJumpType(firstOrDefault.jumpType);
                        focusAds.get(i).setOtherAppid(firstOrDefault.otherAppid);
                        focusAds.get(i).setReportPoint(firstOrDefault.reportPoint);
                        focusAds.get(i).setFrom(firstOrDefault.from);
                        focusAds.get(i).setAppImg(firstOrDefault.appImg);
                        focusAds.get(i).setOrder1((i + 1) + 3);
                        focusAds.get(i).setOrder2(firstOrDefault.order2);
                        focusAdsFS.remove(firstOrDefault);
                    }
                    if (StringUtils.isEmpty(focusAds.get(i).url)) {
                        log.info("URL为null url:" + focusAds.get(i).url + ",form:" + focusAds.get(i).from);
                        continue;
                    }
                    resutlAds.add(focusAds.get(i));
                }
            }
            else {
                log.warn("获取广告接口为空focusAds,cid:{},clientType:{},status:{},sourcetype:{},opuserid:{},Userip:{}",cid,clientType,status,sourcetype,opuserid,Userip);
            }

            resutlAds.addAll(focusAdsFS);
            resutlAds.addAll(focusLightForIndex.stream().skip(6).collect(Collectors.toList()));
            resutlAds = resutlAds.stream().limit(10).collect(Collectors.toList());
            List<FocusLightForList> forceOrderList = resutlAds.stream().filter(l -> l.order2 > 0).collect(Collectors.toList());
            List<FocusLightForList> normalOrderList = resutlAds.stream().filter(l -> l.order2 == 0).collect(Collectors.toList());
            for (FocusLightForList f : forceOrderList) {
                if (f.order2 - 1 < normalOrderList.size()) {
                    normalOrderList.add(f.order2 - 1, f);
                } else {
                    normalOrderList.add(f);
                }
            }
            return resutlAds;
        }
        catch (Exception ex) {
            log.error("焦点图异常:{}",ex.getMessage(),ex);
            throw ex;
        }
    }

    private List<FocusLightForList> getActionSweetClientCombineNewsTestFromCache(String rediskey){
        String cache = redisClientRedisHosts.get(rediskey);
        if (cache != null) {
            return JacksonHelper.deserialize(cache, new TypeReference<List<FocusLightForList>>() {
            });
        }
        return null;
    }

    private List<FocusLightForList> getAppCombineNewsT(String sourcetype, String cid, int province, int city) {
        Map<String, String> urlkeys = new HashMap<>();

        urlkeys.put("1quickapp", "/PageArticle?objid=");
        urlkeys.put("1wxcarapp", "/view/article/show?type=0&articleid=");
        urlkeys.put("1bdcarapp", "/view/article/show?type=0&articleid=");
        urlkeys.put("1zhifubao", "/pages/detail-article/detail-article?objid=");
        urlkeys.put("1pingan", "/detailpackage/pages/original/article/index?objectid=");

        urlkeys.put("2quickapp", "/PagePost?topicid=");
        urlkeys.put("2wxcarapp", "/view/club/detail/detail?type=0&id=");
        urlkeys.put("2bdcarapp", "/view/club/detail/detail?type=0&id=");
        urlkeys.put("2zhifubao", "/pages/detail-post/detail-post?topicid=");
        urlkeys.put("2pingan", "/detailpackage/pages/post-detail/index?objectid=");

        urlkeys.put("4quickapp", "/PageVideo?type=1&videoid=");
        urlkeys.put("4wxcarapp", "/view/home/<USER>/show?cjhtype=0&id=");
        urlkeys.put("4bdcarapp", "/view/home/<USER>/show?cjhtype=0&id=");
        urlkeys.put("4zhifubao", "/pages/original-video-details/original-video-details?cjhtype=0&id=");
        urlkeys.put("4pingan", "/detailpackage/pages/original/video/index?objectid=");

        urlkeys.put("20wxcarapp", "/view/home/<USER>/show?id=");
        urlkeys.put("20bdcarapp", "/view/home/<USER>/show?id=");

        urlkeys.put("25quickapp", "/PageTravelDetail?type=4&detailsId=");
        urlkeys.put("25wxcarapp", "/package2/pages/travel/index?id=");
        urlkeys.put("25bdcarapp", "/package/pages/travel/index?id=");
        urlkeys.put("25zhifubao", "/pages/detail-travel/detail-travel?id=");
        urlkeys.put("25pingan", "/detailpackage/pages/travel/detail/index?objectid=");


        urlkeys.put("31quickapp", "/PageChejiahao?objid=");
        urlkeys.put("31wxcarapp", "/view/article/show?type=4&articleid=");
        urlkeys.put("31bdcarapp", "/view/article/show?type=4&articleid=");
        urlkeys.put("31zhifubao", "/pages/detail-chejiahao/detail-chejiahao?type=4&objid=");
        urlkeys.put("31pingan", "/detailpackage/pages/chejiahao/long/index?objectid=");


        urlkeys.put("32quickapp", "/PageChejiahao?objid=");
        urlkeys.put("32wxcarapp", "/detailpackage/pages/lighttext/lighttext?id=");
        urlkeys.put("32bdcarapp", "/detailpackage/pages/lighttext/lighttext?id=");

        urlkeys.put("33quickapp", "/PageVideo?type=4&videoid=");
        urlkeys.put("33wxcarapp", "/view/home/<USER>/show?cjhtype=4&id=");
        urlkeys.put("33bdcarapp", "/view/home/<USER>/show?cjhtype=4&id=");
        urlkeys.put("33zhifubao", "/pages/original-video-details/original-video-details?cjhtype=4&id=");
        urlkeys.put("33pingan", "/detailpackage/pages/chejiahao/video/index?objectid=");

        urlkeys.put("35wxcarapp", "/view/home/<USER>/show?id=");
        urlkeys.put("35bdcarapp", "/view/home/<USER>/show?id=");

        urlkeys.put("36quickapp", "/PageChejiahao?objid=");
        urlkeys.put("36wxcarapp", "/detailpackage/pages/carshow/carshow?id=");
        urlkeys.put("36bdcarapp", "/detailpackage/pages/carshow/carshow?id=");
        province = 90400012;
        String url = "http://data.in.corpautohome.com/oneapi/v2?requestid=lightappapi&uuid=0&pid="+province+"&devicetype=android&userid=0&source=m&ext=%7b%22resourceids%22%3a%22ZXB-014%22%7d";
        Map<String, Object> headers = new HashMap<>();
        headers.put("Authorization", "Basic " + Base64Utils.encodeToString("wxcarnetcore:pDpQBsU^b^".getBytes(StandardCharsets.UTF_8)));

        ReturnValue<FocusImgResultDto> model = httpService.httpGetFor(url,null,headers, new TypeReference<ReturnValue<FocusImgResultDto>>() {
        });

        List<FocusLightForList> list = new ArrayList<>();
        if (model != null && model.getResult() != null && model.getResult().itemlist != null && model.getResult().itemlist.size() > 0) {
            int order = 1;

            List<FocusImgResultItemDto> focusImgResultItemDtos10 = model.getResult().itemlist.stream().limit(10).collect(Collectors.toList());
            for (FocusImgResultItemDto m : focusImgResultItemDtos10)
            {
                String keys = "";
                if (StringUtils.isEmpty(m.bizid)) continue;
                FocusLightForList htsitem = new FocusLightForList();

                //文章 - 1
                //视频 - 4
                //论坛 - 2
                //车家号 - 3
                //直播 - 20
                //VR - 35
                //话题 - 26
                //H5 - 999
                //文章1、直播20、重播21、资讯视频3
                switch (m.biztype)
                {
                    case "1": //文章
                        keys = "1";
                        break;
                    case "20": //直播
                    case "21": //重播
                    case "610050": //车家号直播
                    case "610066": //论坛直播
                        keys = "20";
                        break;
                    case "3": //资讯视频
                        keys = "4";
                        break;
                    case "12": //车家号长文
                        keys = "31";
                        break;
                    case "14": //车家号视频
                        keys = "33";
                        break;
                    case "5": //帖子
                    case "66": //论坛视频
                    case "63": //帖子加精-青少年精华
                    case "73": //青少年论坛视频
                        keys = "2";
                        break;
                    case "25": //游记
                        keys = "25";
                        break;
                    default:
                        continue;
                }
                keys = keys + sourcetype;

                if (urlkeys.containsKey(keys)) {
                    htsitem.url = urlkeys.get(keys) + m.bizid;
                }
                else {
                    continue;
                    //htsitem.Url = m.landurl;
                }
                htsitem.id = NumberUtils.toInt(m.bizid);
                htsitem.img = m.pic.replace("http://", "https://");
                htsitem.appImg = m.pic.replace("http://", "https://");
                htsitem.title = m.longtitle;
                htsitem.time = "";
                htsitem.order1 = order;
                htsitem.type = NumberUtils.toInt(m.biztype);
                htsitem.version = "";
                htsitem.protocol = "";
                htsitem.newsId = NumberUtils.toInt(m.bizid);
                if ("33quickapp".equals(keys))
                    htsitem.from = 1;
                else
                    htsitem.from = 1;
                htsitem.jumpType = 2;
                htsitem.exposeext = m.exposeext;
                list.add(htsitem);

                order++;
            }
        }
        return list;
    }

    public List<FocusLightForList> GetActionSweetClientCombine(String cid, int clientType, int status, int province, int city) {
        List<SweetClientCombine> dt = articleServiceMapper.GetActionSweetClientCombine(cid,clientType,status,city,province);
        List<FocusLightForList> slist = new ArrayList<>();
        if (dt != null && dt.size() != 0) {
            for (SweetClientCombine item : dt) {
                Integer forceSort = item.getForceSort();
                FocusLightForList itemModel = new FocusLightForList(){{
                    setFrom(2);
                    setNewsId(0);
                    setId(0);
                    setOrder1(item.getSortNum()==null?0:item.getSortNum());
                    setTitle(item.getSweetName());
                    setUrl(item.getJumpTo());
                    setType(item.getClientType()==null?0:item.getClientType());
                    setImg(item.getImg()==null?"":item.getImg().replace("http://", "https://"));
                    setJumpType(item.getJumpType()==null?0:item.getJumpType());
                    setOtherAppid(item.getOtherAppid());
                    setReportPoint(item.getReportPoint());
                    setShareDesc(item.getShareDesc());
                    setShareImg(item.getShareImg());
                    setShowPoint(item.getShowPoint());
                    setNeedLogin(item.getNeedLogin() ==null?0:item.getNeedLogin());
                    setAppImg(item.getImg()==null?"":item.getImg().replace("http://", "https://"));
                    setOrder2((forceSort != null && forceSort == 1) ? (item.getSortNum()==null?0:item.getSortNum()) : 0);
                }};
                slist.add(itemModel);
            }
        }
        return slist;
    }

    /**
     * 增加cid，临时方案，更改第四帧广告焦点图地址
     * @param sourcetype
     * @param userId
     * @param userip
     * @param cid
     * @return
     */
    private List<FocusLightForList> getFocusImgAds(String sourcetype, String userId, String userip, String cid) {
        if (StringUtils.isEmpty(sourcetype)) {
            sourcetype = "wxcarapp";
        }
        List<FocusLightForList> list = new ArrayList<>();
        String psids = "";
        //todo下掉除微信的其他广告焦点图
        if (sourcetype == "bdcarapp") {
            return list;
        }
        switch (sourcetype) {
            case "bdcarapp": //百度
                psids = "7220,7223,7226";
                break;
            case "wxcarapp": //微信
                psids = "7221,7224,7227";
                break;
            case "quickapp": //快应用
                psids = "7222,7225,7228";
                break;
        }
        List<GetFocusImgAdsDto> tmpModel = cmsService.getFocusImgAds(psids,userip,userId);
        if (tmpModel != null) {
            int position = 4;
            for (GetFocusImgAdsDto itemeda : tmpModel) {
                FocusLightForList htsitem = new FocusLightForList();
                if (CollectionUtils.isEmpty(itemeda.getData())) {
                    htsitem.from = 4;
                    if (itemeda.getMeta() != null && itemeda.getMeta().getReportInfo() != null) {
                        htsitem.rdpvurls = CollectionUtils.isEmpty(itemeda.getMeta().getReportInfo().getRdpvurls()) ? "" : itemeda.getMeta().getReportInfo().getRdpvurls().get(0);
                        htsitem.rdrqurls = CollectionUtils.isEmpty(itemeda.getMeta().getReportInfo().getRdrqurls()) ? "" : itemeda.getMeta().getReportInfo().getRdrqurls().get(0);
                    }
                    htsitem.type = -1;
                    htsitem.asIsnull = true;
                    htsitem.position = position;
                }
                else
                {
                    htsitem.url = itemeda.getData().get(0).getLink().getSrc();
                    htsitem.id = 0;
                    htsitem.img = itemeda.getData().get(0).getMaterial().getImg().get(0).getSrc();
                    htsitem.title = itemeda.getData().get(0).getLink().getDesc();
                    htsitem.order1 = 0;
                    htsitem.from = 4;
                    htsitem.reports = itemeda.getData().get(0).getLink().getReports().get(0);
                    htsitem.rdpvurls = itemeda.getMeta().getReportInfo().getRdpvurls().get(0);
                    htsitem.rdrqurls = itemeda.getMeta().getReportInfo().getRdrqurls().get(0);
                    htsitem.type = 10;
                    htsitem.asIsnull = false;
                    htsitem.position = position;
                    htsitem.appImg = itemeda.getData().get(0).getMaterial().getImg().get(0).getSrc();
                }
                position++;
                list.add(htsitem);
            }
        }
        return list;
    }

    /**
     * 轻应用焦点图
     */
    @Data
    public static class FocusLightForList {
        @JsonProperty("NeedLogin")
        private int needLogin ;
        @JsonProperty("ShowPoint")
        private String showPoint ;
        @JsonProperty("reportPoint")
        private String reportPoint ;
        @JsonProperty("JumpType")
        private int jumpType ;
        @JsonProperty("OtherAppid")
        private String otherAppid ;
        @JsonProperty("Id")
        private int id ;
        @JsonProperty("Img")
        private String img ; //{mater img}
        @JsonProperty("Title")
        private String title ;
        @JsonProperty("Time")
        ///link desc
        private String time ;
        @JsonProperty("Order2")
        //强制序号
        private int order2 ;
        @JsonProperty("Order1")
        //序号
        private int order1 ;
        @JsonProperty("Type")
        //数据类型
        private int type ;
        @JsonProperty("Url")
        //VR地址
        private String url ;
        @JsonProperty("Version")
        //版本号
        private String version ;
        @JsonProperty("Protocol")
        //协议
        private String protocol ;
        @JsonProperty("NewsId")
        //内容id
        private int newsId ;
        @JsonProperty("From")
        //焦点图来源:1资讯后台,2资源位管理，4广告
        private int from ;
        @JsonProperty("reports")
        //广告
        private String reports ; //{link reports}
        @JsonProperty("rdpvurls")
        private String rdpvurls ; //{link reports}
        @JsonProperty("rdrqurls")
        private String rdrqurls ; //{link rdrqurls}
        @JsonProperty("asIsnull")
        private boolean asIsnull ;
        @JsonProperty("position")
        private int position ;
        @JsonProperty("AppImg")
        private String appImg ;
        @JsonProperty("exposeext")
        private String exposeext ;
        @JsonProperty("ShareDesc")
        private String shareDesc ;
        @JsonProperty("ShareImg")
        private String shareImg ;
    }

    @Data
    public static class FocusImgResultDto {
        public List<FocusImgResultItemDto> itemlist ;
    }

    @Data
    public static class FocusImgResultItemDto {
        private String biztype ;
        private String bizid ;
        private String longtitle ;
        private String shorttitle ;
        private String pic ;
        private String landurl ;
        private int position ;
        private String exposeext ;
    }
}
