package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.netcoreapi.sou.City;
import com.autohome.applet.model.dto.netcoreapi.sou.GpsJw;
import com.autohome.applet.service.netcoreapi.SouService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SouServiceImpl implements SouService {

    @Autowired
    @Qualifier("apidataredis")
    RedisClient redisClientApiDataRedis;

    private static final HashMap<String, String> unExpInfoArr = new HashMap<String, String>() {{
        put("1102", "1101");
        put("1202", "1201");
        put("3102", "3101");
        put("5002", "5001");
    }};

    @Override
    public ReturnValue<City> GetCityByLatitude(BigDecimal longitude, BigDecimal Latitude) {
        int cityId;
        HashMap<String, Object> param = new HashMap<>();

        if (longitude.compareTo(BigDecimal.ZERO) != 0 || Latitude.compareTo(BigDecimal.ZERO) != 0) {
            param.clear();
            param.put("_appid", "lapp");
            param.put("j", longitude);
            param.put("w", Latitude);

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://gps.lq.autohome.com.cn/jw/", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return ReturnValue.buildSuccessResult(null);
            }
            ReturnValue<GpsJw> resp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<GpsJw>>() {
            });
            if (resp == null || resp.getResult() == null) {
                return ReturnValue.buildSuccessResult(null);
            }

            cityId = resp.getResult().getAreajw();

            cityId = GetCityId(cityId);

        } else {
            //鉴于个人信息安全，禁止调用IP获取定位，一律默认北京
            cityId = 110100;
        }

        String key = "wxcar:city:info:v6:" + cityId;
        String cache = redisClientApiDataRedis.get(key);
        if (cache != null) {
            ReturnValue<City> resp = JacksonHelper.deserialize(cache, new TypeReference<ReturnValue<City>>() {
            });
            if (resp == null || resp.getResult() == null) {
                return ReturnValue.buildSuccessResult(null);
            }

            return resp;
        }
        param.clear();
        param.put("_appid", "wxcar");
        param.put("id", cityId);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/baseapi/AreaCity/areacityinfo.ashx", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildSuccessResult(null);
        }
        ReturnValue<City> resp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<City>>() {
        });
        if (resp == null || resp.getResult() == null) {
            return ReturnValue.buildSuccessResult(null);
        }
        redisClientApiDataRedis.set(key, resp, 2, TimeUnit.HOURS);
        return resp;
    }

    int GetCityId(int cityId) {
        String strId = String.valueOf(cityId);
        if (strId.length() != 6) {
            return 0;
        }
        String pId = strId.substring(0, 2);
        String cId = strId.substring(2, 2);
        if (pId == "99" || cId == "99" || cId == "00") {
            return 0;
        }
        String tmpId = strId.substring(0, 4);
        if (unExpInfoArr.containsKey(tmpId)) {
            tmpId = unExpInfoArr.get(tmpId);
        }
        return Integer.parseInt(tmpId + "00");
    }
}
