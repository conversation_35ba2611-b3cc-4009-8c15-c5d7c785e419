package com.autohome.applet.service.wxmessage;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.wsmessage.NewCarSeriesSubscriptionRequest;
import com.autohome.applet.model.dto.wsmessage.WxPostMessageRequest;

public interface WxMessageService {
    ReturnValue savePushMessage(WxPostMessageRequest wxPostMessageRequest);

    ReturnValue newSeriesInfoMinpro(NewCarSeriesSubscriptionRequest newCarSeriesSubscriptionRequest);
}
