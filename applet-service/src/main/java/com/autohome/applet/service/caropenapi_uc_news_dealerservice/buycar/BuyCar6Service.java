package com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.CarCalculator;

public interface BuyCar6Service {
    ResponseContent<CarCalculator> calBuyCarCostBySpecid(Integer specid, Integer price, String _appid, int cityid, int provinceid);
}
