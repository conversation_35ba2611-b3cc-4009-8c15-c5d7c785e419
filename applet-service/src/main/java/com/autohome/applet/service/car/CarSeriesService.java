package com.autohome.applet.service.car;

import com.autohome.applet.model.dto.car.*;

import java.util.List;

/**
 * @description: 车系service接口
 * @author: WangBoWen
 * @date: 2023-11-27
 **/
public interface CarSeriesService {

    /**
     * 再task中缓存全量车系信息
     * */
    public String CACHE_SERIES_INFO_KEY = "applet:basecar:series:%s";

    List<EnquiryCardDTO> getEnquiryCardList(int userId,String uuid, String appkey);

    List<Integer> getUserLikeCarSeriesIds (int userId,String uuid,String appkey);

    List<Integer> getHotCarSeriesIds ();

    List<CarSeriesInfoDTO> getCarSeriesInfo (List<Integer> seriesIds);

    /**
     * 查询全量车系信息
     * */
    List<SeriesInfoDto> getAllSeriesInfos();
    /**
     * 查询全量厂商信息
     * */
    List<FctItemListDto.FctItem> getAllFctInfos();
    /**
     * 从缓存中查询全量车系信息
     * */
    List<SeriesInfoDto> getAllSeriesInfosFormCache(List<Integer> seriesIds);
    /**
     * 查询全量品牌信息
     * */
    List<AllBrandInfoDto.BrandInfoDto> getAllBrandInfos();
    /**
     * 刷新全量车系信息缓存
     * */
    List<SeriesInfoDto> refreshAllSeriesInfos();
    /**
     * 刷新全量厂商信息缓存
     * */
    List<FctItemListDto.FctItem> refreshAllFctItems();
    /**
     * 刷新全量车系信息缓存
     * */
    List<SeriesInfoDto> refreshAllSeriesInfos(List<SeriesInfoDto> seriesInfoDtoList);
    /**
     * 刷新全量品牌信息缓存
     * */
    List<AllBrandInfoDto.BrandInfoDto> refreshAllBrandInfos();
    /**
     * 无缓存
     * */
    List<SeriesInfoDto> getAllSeriesInfosFromUrlNoCache();
}
