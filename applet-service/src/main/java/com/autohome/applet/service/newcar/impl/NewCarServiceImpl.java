package com.autohome.applet.service.newcar.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.hash.Hash;
import com.autohome.applet.dao.javaapi.mapper.NewCarEventMapper;
import com.autohome.applet.dao.javaapi.model.NewCarEvent;
import com.autohome.applet.dao.javaapi.model.query.NewCarEventQuery;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.CarSeriesInfoDTO;
import com.autohome.applet.model.dto.car.SeriesInfoDto;
import com.autohome.applet.model.dto.carlibrary.carapi.out.RankListpageResult;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.AllNewBrands;
import com.autohome.applet.model.dto.newcar.LaOriginItemProtocol;
import com.autohome.applet.model.dto.newcar.NewCarFocusCardDTO;
import com.autohome.applet.model.enums.NewCarEventObjTypeEnum;
import com.autohome.applet.service.car.CarSeriesService;
import com.autohome.applet.service.javaapi.CarService;
import com.autohome.applet.service.javaapi.NewCarLaunchService;
import com.autohome.applet.service.newcar.NewCarService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.comm.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class NewCarServiceImpl implements NewCarService {

    @Autowired
    private CarSeriesService carSeriesService;

    @Autowired
    private CarService carService;

    @Autowired
    private NewCarEventMapper newCarEventMapper;

    @Qualifier("newCarLaunchServiceV2Impl")
    @Autowired
    private NewCarLaunchService newCarLaunchService;

    @Value("${newcar.foucs-url:\"\"}")
    private String newCarFocusUrl;

    @Override
    public ReturnValue<List<NewCarFocusCardDTO>> getFocusCard() {
        //1.查询焦点图接口
        CompletableFuture<List<NewCarFocusCardDTO>> newCarFocusCardDTOListFuture = CompletableFuture.supplyAsync(() -> getNewCarFocusCardDTOList());
        //2.查询上市信息
        CompletableFuture<List<AllNewBrands>> allNewBrandsFuture = CompletableFuture.supplyAsync(() -> newCarLaunchService.allNewBrands());
        //3.查询新车关注排名榜
        CompletableFuture<RankListpageResult> rankListpageResultFuture = CompletableFuture.supplyAsync(() -> {
            return carService.getrecranklistpageresult2V2("0", 20);//热门排行榜
        });

        CompletableFuture.allOf(newCarFocusCardDTOListFuture, allNewBrandsFuture, rankListpageResultFuture).join();

        try {
            List<NewCarFocusCardDTO> newCarFocusCardDTOList = newCarFocusCardDTOListFuture.get();
            List<AllNewBrands> allNewBrandsList = allNewBrandsFuture.get();
            RankListpageResult rankListpageResult = rankListpageResultFuture.get();
            if(!CollectionUtils.isEmpty(newCarFocusCardDTOList)){

                List<Integer> seriesIds = newCarFocusCardDTOList.stream().map(NewCarFocusCardDTO :: getSeriesId).collect(Collectors.toList());

                newCarFocusCardDTOList.forEach(o -> {
                    //填充上市日期
                    AllNewBrands allNewBrands = allNewBrandsList.stream().filter(t -> t.getSeriesid() == o.getSeriesId()).findFirst().orElse(null);
                    if(allNewBrands != null){
                        o.setOntime(allNewBrands.getOntimeDateV3Str());
                        o.setLightpoint(allNewBrands.getLightpoint());
                    }
                    //填充排名
                    if(rankListpageResult != null && rankListpageResult.getResult() != null && !CollectionUtils.isEmpty(rankListpageResult.getResult().getList())){
                        RankListpageResult.ListItem tmpItem = rankListpageResult.getResult().getList().stream().filter(r -> r.getSeriesid().equals(String.valueOf(o.getSeriesId()))).findFirst().orElse(null);
                        if(tmpItem != null){
                            o.setRank(StringUtils.isEmpty(tmpItem.getRank()) ? 0 : Integer.parseInt(tmpItem.getRank()));
                        }
                    }
                    //补齐车系基本信息（车系名称，车系无底图）
                    List<CarSeriesInfoDTO> carSeriesInfoDTOList = carSeriesService.getCarSeriesInfo(seriesIds);
                    List<SeriesInfoDto> seriesInfoDtoList = carSeriesService.getAllSeriesInfosFormCache(seriesIds);
                    if(!CollectionUtils.isEmpty(carSeriesInfoDTOList)){
                        CarSeriesInfoDTO carSeriesInfoDTO = carSeriesInfoDTOList.stream().filter(k -> k.getSeriesid().equals(o.getSeriesId())).findFirst().orElse(null);
                        if(carSeriesInfoDTO != null){
                            o.setSeriesImg(carSeriesInfoDTO.getSeriespnglogo());
                            o.setSeriesName(carSeriesInfoDTO.getName());
                        }
                    }
                });
            }
            return ReturnValue.buildSuccessResult(newCarFocusCardDTOList);
        }
        catch (Exception ex){

        }
        
        return ReturnValue.buildSuccessResult(new ArrayList<>());
    }

    @Override
    public HashMap<String, Integer> newcareventDataSync() {
    /**
     * 第1块：存数据库，混合车系维度，每个车系取Top30条内容
     * */
        HashMap<String, Integer> result = new HashMap<>();
        //Top20车系
        RankListpageResult rcmRankListResult = carService.getrecranklistpageresult2_V2(1, 20, 2, "2002");
        val rankSeriesList = Optional.ofNullable(rcmRankListResult).map(RankListpageResult::getResult)
                .map(RankListpageResult.Result::getList)
                .orElse(null);
        if (CollectionUtil.isNotEmpty(rankSeriesList)) {
            System.out.println("list ====> top20车系遍历取资讯");
            rankSeriesList.forEach(series -> {
                int seriesId = Integer.parseInt(series.getSeriesid());
                val newsList = getCmsVideoListBySeriesid(seriesId, 30);

                if(CollectionUtils.isEmpty(newsList)){
                    return ;
                }

                val dbOldRecords = newCarEventMapper.getByQuery(NewCarEventQuery.builder().seriesid("%," + seriesId + ",%").build());
                if (CollectionUtil.isNotEmpty(dbOldRecords)) {
                    List<Integer> deleteList = new ArrayList<>();

                    /**
                     * 查询到最新50条news中不包含已经入库的对应车系的记录，则将库中该记录进行删除操作
                     * */
                    dbOldRecords.forEach(oldRec -> {
                        val sameNewsDto = Optional.ofNullable(newsList).map(List::stream)
                                .map(nl -> nl.filter(
                                        newsDto -> newsDto.getBizId().intValue() == oldRec.getObjid()
                                )).map(Stream::findFirst).flatMap(o -> o)
                                .orElse(null);
                        if (sameNewsDto == null) {
                            deleteList.add(oldRec.getId());
                        }
                    });

                    if (CollectionUtil.isNotEmpty(deleteList)) {
                        newCarEventMapper.deleteBatch(NewCarEventQuery.builder().idList(deleteList).build());
                    }

                    result.put("delete", deleteList.size());
                }

                if (CollectionUtil.isNotEmpty(newsList)) {
                    List<NewCarEvent> insertList = new ArrayList<>();
                    List<NewCarEvent> updateList = new ArrayList<>();

                    System.out.println("list ====> 遍历车系资讯");
                    /**
                     * dbid字段数值规则：
                     * String idString = (100000000 + objid ) +"";
                     * dbid = Long.valueOf((publishTime.getTime() /1000) + "" + (1000 - objtype) + "" + idString.substring(idString.length()-6) + "");
                     * */
                    newsList.forEach(news -> {
                        int cmsCreatesource = Optional.of(news).map(LaOriginItemProtocol.LaOriginItemProtocolItem::getCmsCreatesource).orElse(-1);
                        int objtype = NewCarEventObjTypeEnum.getByMainDataType(news.getMainDataType()).getObjType();
                        //特殊objtype处理
                        objtype = objtype == 1 && cmsCreatesource == 2 ? 2 : objtype;
                        //无法识别的 MainDataType 不进行处理
                        if (objtype > 0) {
                            String idString = (100000000 + news.getBizId()) + "";
                            long dbid = Long.parseLong((news.getPublishTime().getTime() / 1000) + "" + (1000 - objtype) + "" + idString.substring(idString.length() - 6) + "");

                            NewCarEvent newCarEventModel = new NewCarEvent();
                            newCarEventModel.setDbid(dbid);
                            newCarEventModel.setObjid(news.getBizId().intValue());
                            newCarEventModel.setObjtype(objtype);
                            newCarEventModel.setTitle(news.getTitle());
                            String imgUrl = objtype == 3 ? news.getImgUrl69() : news.getImgUrl43();
                            newCarEventModel.setImgurl(StringUtils.isNullOrEmpty(imgUrl) ? "" : imgUrl);
                            newCarEventModel.setPublishtime(news.getPublishTime());
                            newCarEventModel.setUpdatetime(news.getBizUpdateTime());
                            newCarEventModel.setAuthorid(news.getAuthorId());
                            newCarEventModel.setAuthorname(news.getAuthorName());
                            newCarEventModel.setHeadimg(news.getAuthorImg());
                            newCarEventModel.setIndexdetail(news.getSummary());
                            newCarEventModel.setReplycount(news.getReplyCount() == null ? 0 : news.getReplyCount());
                            newCarEventModel.setPlaycount(news.getVv() == null ? 0 : news.getVv());
                            newCarEventModel.setLikecount(news.getLikeCount() == null ? 0 : news.getLikeCount());
                            newCarEventModel.setViewcount(news.getPv() == null ? 0 : news.getPv());
                            newCarEventModel.setJumpurl("");
                            newCarEventModel.setImglist(news.getMultiImages() == null ? "" : String.join(",", news.getMultiImages()));
                            newCarEventModel.setClosecomment(news.getIsCloseComment() == null ? 0 : news.getIsCloseComment().byteValue());
                            newCarEventModel.setVideosource(StringUtils.isNullOrEmpty(news.getVideoSource()) ? "" : news.getVideoSource());
                            if (CollectionUtil.isNotEmpty(news.getSeriesIds())) {
                                if (news.getSeriesIds().size() > 50) {
                                    news.setSeriesIds(news.getSeriesIds().subList(0, 50));
                                }
                                newCarEventModel.setSeriesids("," + news.getSeriesIds().stream().map(String::valueOf).collect(Collectors.joining(",")) + ",");
                                //补齐seriesnames，与 SeriesIds对应
                                newCarEventModel.setSeriesnames(getSeriesNames(news.getSeriesIds()));
                            } else {
                                newCarEventModel.setSeriesids("");
                            }
                            newCarEventModel.setExtdata("");
                            newCarEventModel.setDirection(news.getVideoDirection() == null ? 0 : news.getVideoDirection().byteValue());
                            newCarEventModel.setCmsrefine(news.getCmsRefine() == null ? 0 : news.getCmsRefine().byteValue());
                            newCarEventModel.setIsDel((byte) 0);
                            newCarEventModel.setDuration(objtype == 3 ? news.getDuration().intValue() : 0);

                            /**
                             * 根据 dbid 对照表数据，存在即更新，不存在即插入
                             * 用 dbid 有漏洞，当编辑更新已经发布过的内容后，dbid发生变更，就会出现重复数据
                             * objid+objtype是唯一的
                             * */
//                            val dbEventRecord = newCarEventService.getByDbId(dbid);
                            List<NewCarEvent> newCarEventList = newCarEventMapper.getByQuery(NewCarEventQuery.builder().objid(newCarEventModel.getObjid()).objtype(newCarEventModel.getObjtype()).build());
                            if (!CollectionUtils.isEmpty(newCarEventList)) {
                                NewCarEvent newCarEvent = newCarEventList.get(0);
                                newCarEventModel.setId(newCarEvent.getId());
                                newCarEventModel.setModifiedStime(DateHelper.getNow());
                                updateList.add(newCarEventModel);
                            } else {
                                insertList.add(newCarEventModel);
                            }
                        }
                    });

                    //执行 insert && update
                    if (CollectionUtil.isNotEmpty(insertList)) {
                        result.put("insert", insertList.size());
                        newCarEventMapper.insertBatch(insertList);
                    }
                    if (CollectionUtil.isNotEmpty(updateList)) {
                        result.put("update", updateList.size());
                        newCarEventMapper.updateBatch(updateList);
                    }
                }
            });
        }
        return result;
    }

    @Override
    public void newcareventObjectsCacheSync() {

    }

    /**
     * 获取运营配置的焦点图
     * */
    private List<NewCarFocusCardDTO> getNewCarFocusCardDTOList() {
        Map<String, Object> param = new HashMap<>();
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(newCarFocusUrl, param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("getNewCarFocusCardDTOList error url : {}", newCarFocusUrl);
            return null;
        }
        ResponseContent<List<NewCarFocusCardDTO>> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<List<NewCarFocusCardDTO>>>() {
        });
        if (responseSpecContent == null || responseSpecContent.getResult() == null) {
            log.error("NewCarService getNewCarFocusCardDTOList error, url： {}, param: {}", newCarFocusUrl, JacksonHelper.serialize(param));
            return null;
        }
        return responseSpecContent.getResult();
    }

    /**
     * 新车大事件
     * 从物理表中查询， 表里的数据 由task存储
     * */
    private List<LaOriginItemProtocol.LaOriginItemProtocolItem> getCmsVideoListBySeriesid(int seriesId, int pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "app");
        params.put("series_id", seriesId);
        params.put("page_size", pageSize);
        String url = "http://la.corpautohome.com/video/cms_video_list_by_seriesid";
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, params);

        if (httpResult == null || httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("getNewCarNewss error url : {} ， parms: {}", url, JacksonHelper.serialize(params));
            return null;
        }

        ReturnValue<LaOriginItemProtocol> newCarNewsDtoResult = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<LaOriginItemProtocol>>() {
        });

        if (newCarNewsDtoResult.getResult() == null) {
            log.error("getNewCarNewss  error, url： {}, param: {}, response: {}", url, JacksonHelper.serialize(params), JacksonHelper.serialize(newCarNewsDtoResult));
            return null;
        }
        return newCarNewsDtoResult.getResult().getItems();
    }

    private String getSeriesNames(List<Integer> seriesIds){
        List<SeriesInfoDto> seriesInfoDtoList = carSeriesService.getAllSeriesInfos();
        String seriesNames = ",";
        for (Integer seriesId : seriesIds) {
            SeriesInfoDto tmpSeriesInfoDto = seriesInfoDtoList.stream().filter(s -> s.getId().equals(seriesId)).findFirst().orElse(null);
            if (tmpSeriesInfoDto == null || StringUtils.isEmpty(tmpSeriesInfoDto.getName())) {
                seriesNames += ",";
            } else {
                seriesNames += tmpSeriesInfoDto.getName() + ",";
            }
        }
        return seriesNames;
    }

}
