package com.autohome.applet.service.wxapi;

import com.autohome.applet.dao.javaapi.model.wxapp.topic.ContentCard;
import com.autohome.applet.dao.javaapi.model.wxapp.topic.ImageOtherAttr;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by pengyi on 2016/12/12.
 */
public class RichTextParser {

    private static String filterHtml(String content) {
        content = HtmlUtils.htmlUnescape(content);//.replaceAll("㊣","");
        //content = content.replaceAll("<br\\s+/>|</p>", "\n").replaceAll("&nbsp;", " ");//.replaceAll("&.{2,6}?;", " ");.replaceAll("<IMG.+?(http.+?(!jpg)).+?>", "")
        content = content.replaceAll("<(\\s*?)", "<").replaceAll("(\\s*?)/>", "/>").replaceAll("<br/>|<BR/>|</p>|</P>", "\n")
                .replaceAll("\n{2,}", "\n");
        content = content.replaceAll("<script.*?>(.*?)</script>", " ");
        content = content.replaceAll("<(?!img|IMG|Img)[^>]*>|㊣|<(img|IMG).*\\.gif.*>|≮[^≯]*≯", "");//.replaceAll("<(img|IMG).*\\.gif.*>","");//过滤非图片标签
        return content;
    }

    private static String filterHtmlRetainA(String content) {
        content = HtmlUtils.htmlUnescape(content);//.replaceAll("㊣","");
        //content = content.replaceAll("<br\\s+/>|</p>", "\n").replaceAll("&nbsp;", " ");//.replaceAll("&.{2,6}?;", " ");.replaceAll("<IMG.+?(http.+?(!jpg)).+?>", "")
        content = content.replaceAll("<(\\s*?)", "<").replaceAll("(\\s*?)/>", "/>").replaceAll("<br/>|<BR/>|</p>|</P>", "\n")
                .replaceAll("\n{2,}", "\n");
        content = content.replaceAll("<script.*?>(.*?)</script>", " ");
        content = content.replaceAll("<(?!img|IMG|Img|a|/a)[^>]*>", "");//.replaceAll("<(img|IMG).*\\.gif.*>","");//过滤非图片标签
        return content;
    }

    /**
     * 转换最终页富文本(不包含回复)
     *
     * @param content 最终页内容信息
     * @return 结构化的json数据
     */
    public static List<ContentCard> parseDetail(String content) {
        if (content == null || content.isEmpty()) {
            return null;
        }
        content = filterHtml(content);
        List<ContentCard> cards = parse2CardList(content);
        return cards;
    }

    /**
     * 转换最终页富文本(不包含回复)
     *
     * @param content 最终页内容信息
     * @return 结构化的json数据
     */
    public static List<ContentCard> parseDetailRetainA(String content) {
        if (content == null || content.isEmpty()) {
            return null;
        }
        content = filterHtmlRetainA(content);
        List<ContentCard> cards = parse2CardList(content);
        return cards;
    }

    /**
     * 将富文本内容转化成卡片集合
     *
     * @param content 富文本
     * @return
     */
    private static List<ContentCard> parse2CardList(String content) {
        List<ContentCard> cards = new ArrayList<>();
        Pattern pattern = Pattern.compile("<img(.*?)src=\"(.*?)\"(.*?)>", Pattern.CASE_INSENSITIVE);
        //Pattern imgOtherAttrPattern = Pattern.compile("width(.*?)(\\d+)(.*?)height(.*?)(\\d+)", Pattern.CASE_INSENSITIVE);
        Pattern imgWidthPattern = Pattern.compile("width(.*?)(\\d+)", Pattern.CASE_INSENSITIVE);
        Pattern imgHeightPattern = Pattern.compile("height(.*?)(\\d+)", Pattern.CASE_INSENSITIVE);
        Matcher imgMather = pattern.matcher(content);
        List<String> imgs = new ArrayList<String>();
        List<ImageOtherAttr> imgOtherAttrs = new ArrayList<>();
        while (imgMather.find()) {
            imgs.add(imgMather.group(2));
            //Matcher m = imgOtherAttrPattern.matcher(imgMather.group());
            Matcher widthM = imgWidthPattern.matcher(imgMather.group());
            Matcher heightM = imgHeightPattern.matcher(imgMather.group());
            int width = 0;
            int height = 0;
            if (widthM.find()) {
                width = Integer.parseInt(widthM.group(2));
            }
            if (heightM.find()) {
                height = Integer.parseInt(heightM.group(2));
            }
            imgOtherAttrs.add(new ImageOtherAttr(width, height));
        }
        String[] strings = pattern.split(content); //content.split("<img(.*?)src=\"(.*?)\"(.*?)>");
        for (int i = 0; i < strings.length; i++) {
            //if (Pattern.matches("\\S+",strings[i])){//过滤掉只包含空格或制表符
            if (!strings[i].replaceAll("\r|\n|\t", "").trim().isEmpty()) {
                cards.add(new ContentCard(2, StringUtils.trimWhitespace(strings[i]), "", null));
            }
            if (i < imgs.size()) {//去掉gif图片
                String img = imgs.get(i);
                ImageOtherAttr imageOtherAttr = imgOtherAttrs.get(i);
                if (!img.contains("gif")) {cards.add(new ContentCard(1, "", img, imageOtherAttr));}
            }
        }
        if (strings.length == 0 && imgs.size() > 0) {
            for (int i = 0; i < imgs.size(); i++) {
                cards.add(new ContentCard(1, "", imgs.get(i), imgOtherAttrs.get(i)));
            }
        }
        return cards;
    }
}
