package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat.BusinessInfo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat.BusinessTags;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat.GroupSeriesBusinessInfo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat.RescuePhoneAndDealer;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.FctSeries;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.Seriesitems;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.ChatService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.logger.LogHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    @Autowired
    private CarApiService carApiService;

    @Override
    public List<BusinessInfo> getHomeChatsByFctId(int fctid, String state, String _appid) {
        try {
            FctSeries fctSeries = carApiService.getSeriesByFctId(_appid, null, fctid, 50);
            if (fctSeries == null || fctSeries.getSeriesitems() == null || fctSeries.getSeriesitems().size() == 0) {
                return null;
            }
            List<BusinessInfo> businessinfolist = new ArrayList<>();
            Map<String, List<Seriesitems>> fctGroup = fctSeries.getSeriesitems().stream().collect(Collectors.groupingBy(Seriesitems::getSeriesStateGroup));
            int onsellsize = fctGroup.get("在售") != null ? fctGroup.get("在售").size() : 0;
            int presellsize = fctGroup.get("预售") != null ? fctGroup.get("预售").size() : 0;
            if (onsellsize >= 3) {
                businessinfolist = getBuinessInfo(fctGroup.get("在售"));
            } else if (presellsize >= 3) {
                businessinfolist = getBuinessInfo(fctGroup.get("预售"));
            }

            return businessinfolist;
        } catch (Exception e) {
            //LogHelper.logError("getHomeChatsByFctId-WithException", e, "厂商id：" + Integer.toString(fctid));
            log.error("getHomeChatsByFctId error 厂商id " + fctid, e);
        }
        return null;
    }

    public List<BusinessInfo> getBuinessInfo(List<Seriesitems> seriesitemsList) {
        if (seriesitemsList == null || seriesitemsList.size() == 0) {
            return null;
        }
        List<BusinessInfo> businessinfolist = new ArrayList<>();
        List<String> titlelist = new ArrayList<>();
        for (Seriesitems item : seriesitemsList) {
            try {
                BusinessInfo buisnessinfo = GetBusinessInfo(Integer.toString(item.getId()), "1");
                if (buisnessinfo != null) {
                    if (!titlelist.contains(buisnessinfo.getTitle()) && businessinfolist.size() < 3) {
                        BusinessTags businessTags = getBusinessTags(Integer.toString(item.getId()), "1");
                        buisnessinfo.setLabel(businessTags.getLabel());
                        businessinfolist.add(buisnessinfo);
                        titlelist.add(buisnessinfo.getTitle());
                    }
                }
            } catch (Exception e) {
                log.error("getBuinessInfo error ", e);
                continue;
            }
        }
        return businessinfolist;
    }

    /**
     * 判断聊天是否开放(获取圈子列表)
     *
     * @param targetId
     * @return
     */
    public BusinessInfo GetBusinessInfo(String targetId, String targetType) {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("targetId", targetId);
            param.put("targetType", targetType);
            param.put("_appid", "car");
            //筛选车系
//            String result = carApi.get("chat.api.GetBusinessInfo", param);
//            ResponseContent<BusinessInfo> responseSpecContent = JSON.parseObject(result, new TypeReference<ResponseContent<BusinessInfo>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://chat.api.autohome.com.cn/c1/s1/api/GetBusinessInfo", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<BusinessInfo> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<BusinessInfo>>() {
            });
            if (responseSpecContent == null || responseSpecContent.getResult() == null) {
                return null;
            }

            return responseSpecContent.getResult();
        } catch (Exception e) {
            //LogHelper.logError("GetBusinessInfo-WithException", e, e.getMessage());
            log.error("GetBusinessInfo error ", e);
            return null;
        }

    }

    /**
     * 获取标签列表
     *
     * @param targetId
     * @return
     */
    public BusinessTags getBusinessTags(String targetId, String targetType) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("targetId", targetId);
            param.put("targetType", targetType);
            param.put("_appid", "car");
            //筛选车系
//            String result = carApi.get("chat.api.getBusinessTags", param);
//            ResponseContent<BusinessTags> responseSpecContent = JSON.parseObject(result, new TypeReference<ResponseContent<BusinessTags>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://chat.api.autohome.com.cn/c1/s1/api/getBusinessTags", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<BusinessTags> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<BusinessTags>>() {
            });
            if (responseSpecContent == null || responseSpecContent.getResult() == null) {
                return null;
            }

            return responseSpecContent.getResult();
        } catch (Exception e) {
//            LogHelper.logError("getBusinessTags-WithException", e, e.getMessage());
            log.error("getBusinessTags error ", e);
            return null;
        }

    }


    /**
     * 获取厂商救援售后电话
     *
     * @param specId
     * @specId
     */
    @Override
    public ResponseContent<?> getmaintainbyspecid(String specId) {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("specId", specId);
            //筛选车系
//            String result = carApi.get("yczj.api.getmaintainbyspecid", param);
//            ResponseContent<HashMap> responseSpecContent = JSON.parseObject(result, new TypeReference<ResponseContent<HashMap>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://yczj.m.autohome.com.cn/cus/v1_0_0/api/maintain/advice/byid", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<HashMap> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<HashMap>>() {
            });
            if (responseSpecContent == null || responseSpecContent.getResult() == null) {
                return null;
            }

            return responseSpecContent;
        } catch (Exception e) {
            LogHelper.logError("getmaintainbyspecid-WithException", e, e.getMessage());
            return null;
        }

    }

    /**
     * 根据厂商id获取所有车系的车友圈
     *
     * @param fctid
     * @param state
     * @param _appid
     * @return
     */
    @Override
    public ResponseContent<?> getAllSeriesChatsByFctId(int fctid, String state, String _appid) {
        try {
            FctSeries fctSeries = carApiService.getSeriesByFctId(_appid, null, fctid, 50);
            if (fctSeries == null || fctSeries.getSeriesitems() == null || fctSeries.getSeriesitems().size() == 0) {
                return null;
            }
            Map<String, List<Seriesitems>> fctGroup = fctSeries.getSeriesitems().stream().collect(Collectors.groupingBy(Seriesitems::getSeriesStateGroup));
            List<GroupSeriesBusinessInfo> groupSeriesBusinessInfos = new ArrayList<>();
            for (Map.Entry<String, List<Seriesitems>> entry : fctGroup.entrySet()) {
                if (entry.getKey().equals("停售")) {
                    continue;
                }
                String groupname = "车友都在聊";
                if (entry.getKey().equals("预售")) {
                    groupname = "新车抢先聊";
                }
                GroupSeriesBusinessInfo groupSeriesBusinessInfo = new GroupSeriesBusinessInfo();
                groupSeriesBusinessInfo.setGroupname(groupname);
                List<BusinessInfo> businessinfolist = new ArrayList<>();
                List<String> titlelist = new ArrayList<>();
                entry.getValue().forEach(s -> {
                    BusinessInfo buisnessinfo = GetBusinessInfo(Integer.toString(s.getId()), "1");
                    if (buisnessinfo != null) {
                        if (!titlelist.contains(buisnessinfo.getTitle())) {
                            BusinessTags businessTags = getBusinessTags(Integer.toString(s.getId()), "1");
                            buisnessinfo.setLabel(businessTags.getLabel());
                            businessinfolist.add(buisnessinfo);
                            titlelist.add(buisnessinfo.getTitle());
                        }
                    }
                });
                groupSeriesBusinessInfo.setBusinessInfoList(businessinfolist);
                groupSeriesBusinessInfos.add(groupSeriesBusinessInfo);
            }
            if (fctid == 190) {
                GroupSeriesBusinessInfo groupSeriesBusinessInfo = new GroupSeriesBusinessInfo();
                groupSeriesBusinessInfo.setGroupname("定制圈子");
                List<BusinessInfo> businessinfolist = new ArrayList<>();

                BusinessInfo buisnessinfo = GetBusinessInfo("155706", "5");
                BusinessTags businessTags = getBusinessTags("155706", "5");
                buisnessinfo.setLabel(businessTags.getLabel());
                businessinfolist.add(buisnessinfo);

                groupSeriesBusinessInfo.setBusinessInfoList(businessinfolist);
                groupSeriesBusinessInfos.add(groupSeriesBusinessInfo);
            }
//            List<BusinessInfo> businessinfolist = new ArrayList<>();
//            fctSeriesResponseContent.getResult().getSeriesitems().forEach(s -> {
//                BusinessInfo buisnessinfo = GetBusinessInfo(Integer.toString(s.getId()));
//                BusinessTags businessTags = getBusinessTags(Integer.toString(s.getId()));
//                buisnessinfo.setLabel(businessTags.getLabel());
//                businessinfolist.add(buisnessinfo);
//            });
            if (groupSeriesBusinessInfos.size() == 0) {
                return null;
            }
            return new ResponseContent<>(groupSeriesBusinessInfos);
        } catch (Exception e) {
            //LogHelper.logError("getAllSeriesChatsByFctId-WithException", e, e.getMessage());
            log.error("getAllSeriesChatsByFctId-WithException", e);
        }
        return null;
    }

    /**
     * 获取厂商救援售后电话
     *
     * @param fctid
     * @return
     */
    @Override
    public ResponseContent<?> getRescuePhoneAndDealer(int fctid) {
        try {
            //获取品牌id
//            int brandid = fctRepository.getBrandIdByFctId(fctid);
//            if (brandid == 0) {
//                return null;
//            }
//            //随机获取一个厂商下车系
//            FctSeries fctSeries = carApiService.getSeriesByFctId("m", null, fctid);
//            if (fctSeries == null || fctSeries.getSeriesitems() == null || fctSeries.getSeriesitems().size() == 0) {
//                return null;
//            }
            HashMap<String, Object> param = new HashMap<>();
            param.put("fctId", Integer.toString(fctid));
            param.put("carBrandId", "1");
            //筛选车系
//            String result = carApi.get("yczj.api.getRescuePhoneAndDealer", param);
//            ResponseContent<RescuePhoneAndDealer> responseSpecContent = JSON.parseObject(result, new TypeReference<ResponseContent<RescuePhoneAndDealer>>() {
//            });

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://yczj.api.autohome.com.cn/cus/v1_0_0/api/review/mina/getRescuePhoneAndDealer", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<RescuePhoneAndDealer> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<RescuePhoneAndDealer>>() {
            });
            if (responseSpecContent == null || responseSpecContent.getResult() == null) {
                return null;
            }

            return responseSpecContent;
        } catch (Exception e) {
            //LogHelper.logError("getRescuePhoneAndDealer-WithException", e, e.getMessage());
            log.error("getRescuePhoneAndDealer-WithException", e);
            return null;
        }

    }
}
