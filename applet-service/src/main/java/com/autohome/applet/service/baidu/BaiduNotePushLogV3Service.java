package com.autohome.applet.service.baidu;

import com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.util.BaiduNoteUtil;

import java.util.List;

public interface BaiduNotePushLogV3Service {
    int handleBaiduNoteExcel(List<BaiduNoteUtil.BaiduNoteExcel> baiduNoteExcelList);

    List<BaiduNotePushLogV3> getNoPushList();
    Integer getNoPushCount();

    /**
     * 获取未推送的数据
     * @return
     */
    List<PushResourceFeedDataDTO> getPushResourceFeedDataDTOList();

    int updateByExId(BaiduNotePushLogV3 baiduNotePushLogV3);
}
