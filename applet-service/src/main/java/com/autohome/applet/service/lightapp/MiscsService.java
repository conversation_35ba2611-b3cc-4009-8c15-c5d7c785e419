package com.autohome.applet.service.lightapp;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.lightapp.*;

public interface MiscsService {

    PageOf<Dealer4SpecDto> getDealers4SpecV2(Integer specId, int orderType, int pageindex, int pagesize, int cityId, double lat, double lon);
    PageKBOf<WordOfMouthDto, DimSeriesPRCTypesDto> wordOfMouth(Integer seriesId, int pageindex, int pagesize, int isgood, int purpose, int photosize, int isappend, int year, int grade, int provinceId, int order , int cityid);

    ReturnValue<?> getSeriesList(int pm, int seriesid, int cityid, int grade, int pageindex, int pagesize, int order);

    ReturnValue<?> getBrandSeariesYYClub(String appid);
}
