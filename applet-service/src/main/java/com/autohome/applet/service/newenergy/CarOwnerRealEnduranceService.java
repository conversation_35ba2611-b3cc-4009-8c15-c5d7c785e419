package com.autohome.applet.service.newenergy;

import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfBaseInfoDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfKoubeiDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceSpecInfoDTO;

/**
 * @description: 新能源车主真实续航
 * @author: WangBoWen
 * @date: 2024-01-02
 **/
public interface CarOwnerRealEnduranceService {

    /**
     * 获取车主真实续航
     * @param seriesId 车系id
     * @param cityId 城市id
     * @return 车主续航数据
     */
    CarOwnerRealEnduranceDTO getCarOwnerRealEndurance (Integer range ,Integer seriesId,Integer cityId);

    /**
     * 获取口碑车主真实续航
     *
     * @param range
     * @param seriesId 车系id
     * @param cityId   城市id
     * @return 口碑车主真实续航
     */
    CarOwnerRealEnduranceOfKoubeiDTO getCarOwnerRealEnduranceKouBei (Integer range, Integer seriesId,Integer cityId);

    /**
     * 获取续航最长的车型
     * @param seriesId 车系id
     * @param cityId 城市id
     * @return CarOwnerRealEnduranceOfBaseInfoDTO
     */
    CarOwnerRealEnduranceOfBaseInfoDTO getCarOwnerRealEnduranceOfBaseInfo(Integer seriesId,Integer cityId);

    /**
     * 获取车型续航数据
     * @param seriesId 车系id
     * @param cityId 城市id
     * @param specId 车型id
     * @return 车型续航数据
     */
    CarOwnerRealEnduranceSpecInfoDTO getCarOwnerRealEnduranceSpecInfoDTO(Integer seriesId,Integer cityId,Integer specId);


}
