package com.autohome.applet.service.wxmessage;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.wsmessage.SubscriptionInfo;
import com.autohome.applet.model.dto.wsmessage.SubscriptionRequest;
import com.autohome.applet.model.dto.wsmessage.WxMiniMessageRequest;

public interface WxServiceMessageService {
    ReturnValue saveAccountInfo(WxMiniMessageRequest wxMiniMessageRequest);
    ReturnValue subService(SubscriptionRequest subscriptionRequest);
    ReturnValue cancelSubService(SubscriptionInfo subscriptionInfo);
    ReturnValue getSubInfo(SubscriptionInfo subscriptionInfo);

}
