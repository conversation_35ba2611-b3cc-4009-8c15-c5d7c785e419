package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.ReturnValue;

import javax.servlet.http.HttpServletRequest;

/**
 * 论坛接口转发
 */
public interface ForumForwardService {

    /**
     * 首页信息流分类 接口转发
     * http://la.corpautohome.com/club/app_custom_query_type_list
     * @param request
     * @return
     */
    ReturnValue<?> getAppCustomQueryTypeList(HttpServletRequest request);



    /**
     * 首页信息流分类 接口转发
     * http://la.corpautohome.com/club/app_feed_by_custom_query
     * @param request
     * @return
     */
    ReturnValue<?> getAppFeedByCustomQuery(HttpServletRequest request);


    /**
     * 获取大数据推荐
     * @param request
     * @return
     */
    ReturnValue<?> getRcmRcmland(HttpServletRequest request);


    ReturnValue<?> getTopicsList(String club_bbs_id, String club_order_type, int page_num, int page_size);

    ReturnValue<?> getTopicListByContentType(int pageindex, int pagesize, int tagInfoId, String orderby, int bbsid, String fields, int containTag, int containImg, int refine);
}
