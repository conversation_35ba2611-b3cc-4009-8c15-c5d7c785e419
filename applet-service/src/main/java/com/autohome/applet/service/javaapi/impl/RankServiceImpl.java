package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.StatusMsg;
import com.autohome.applet.model.dto.car.AllSeriesResultDto;
import com.autohome.applet.model.dto.car.CarListDto;
import com.autohome.applet.model.dto.car.SeriesBaseDto;
import com.autohome.applet.model.dto.rank.*;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.RedisWapper;
import com.autohome.applet.service.javaapi.RankService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autohome.applet.util.Md5;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.comm.utils.JsonUtils;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RankServiceImpl implements RankService {

    public static final String CLH_SERIES_SALE_RANK_USE_MIX = "applet:api:clhSeriesSaleRank:useMix";
    @Autowired
    @Qualifier("lightapp")
    RedisClient redisClient;

    @Autowired
    RedisWapper redisWapper;

    @Autowired
    HttpService httpService;

    // 销量榜
    @Override
    public SeriesSaleRank seriesSaleRank(int brandId, int maxPrice, int minPrice, String menuTypes
            , String month, String levels, boolean flag, int isNewEnergy, int pageIndex, int pageSize) {
        try {
            return clhSeriesSaleRank(brandId, flag, isNewEnergy, levels, menuTypes, maxPrice, minPrice, month, pageIndex, pageSize);
        } catch (BusinessException e) {
            if (e.getCode() == 1004 || e.getCode() == 1005) {
                // 似乎已经没有数据了，先保留这个接口逻辑吧
                return zhqxSeriesSaleRank(brandId, flag, isNewEnergy, levels, menuTypes, maxPrice, minPrice, month, pageIndex, pageSize);
            }
        }
        SeriesSaleRank result  = new SeriesSaleRank();
        result.setData(Collections.emptyList());
        result.setContent("销量榜数据更新中");
        return result;
    }

    // 摩托车榜
    @Override
    public Map<?, ?> motoRank(String levelId, Integer sortId, String price, int pageIndex, int pageSize) {
        String url = "http://uc-car-findcar.msapi.autohome.com.cn/motorbike/searchrank";
        String key = "applet:api:motoRank:" + Md5.md5(url + levelId + sortId + price + pageIndex + pageSize);
        String value = redisClient.getValue(key);
        if (value != null) {
            return JacksonHelper.deserialize(value, Map.class);
        }
        Map<String, Object> param = new HashMap<>();
        if (levelId != null) {
            param.put("levelid", levelId);
        }
        if (sortId != null) {
            param.put("sortId", sortId);
        }
        if (price != null) {
            param.put("price", price);
        }
        param.put("pageindex", pageIndex);
        param.put("pagesize", pageSize);
        Map<?, ?> map = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<Map<?, ?>>>() {});
        Map<String, Object> result = new HashMap<>();
        result.put("content", "热度值是摩托车在全国的浏览数据");
        result.put("data", map);
        redisClient.setValue(key, JacksonHelper.serialize(result), 10, TimeUnit.MINUTES);
        return result;
    }

    // 关注榜、热门榜
    @Override
    public List<RankHotSeriesLevelInfo.RankHotSeriesInfo> seriesHotRank(Integer areaType, Integer cityId
            , String factoryType, Integer minPrice, Integer maxPrice, Integer isnewenergy, String level, int pageIndex, int pageSize) {
        // http://wiki.corpautohome.com/pages/viewpage.action?pageId=100489875
        String url = "http://dsj.dataservice.autohome.com.cn/api/app/getSeriesHotRankNew?level=0&areaType=" + areaType +
                "&areaId=" + cityId + "&fctTypeId=" + factoryType + "&minPrice=" + minPrice + "&maxPrice=" + maxPrice + "&isNewEnergy=" + isnewenergy + "&topN=0";
        String key = "applet:api:seriesHotRank:V2:" + Md5.md5(url);
        if (!redisWapper.hasKey(key)) {
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet(url);
            if (httpGet.getStatusCode() != 200 || httpGet.getBody() == null) {
                throw new BusinessException("http code is " + httpGet.getStatusCode() + " or body is null", 10001);
            }
            ReturnValue<RankHotSeriesResult> result = JsonUtils.toObject(httpGet.getBody(),
                    new TypeReference<ReturnValue<RankHotSeriesResult>>() { });
            if (result == null) {
                throw new BusinessException("parse failed ", 10002);
            }
            if (result.getReturncode() != 0 && result.getResult() == null || CollectionUtils.isEmpty(result.getResult().getSeriesData())) {
                throw new BusinessException("source return code is " + result.getReturncode() + " or result is null or seriesData is empty", 10003);
            }
            RankHotSeriesLevelInfo rankHotSeriesLevelInfo = result.getResult().getSeriesData().get(0);// 热门
            List<String> collect = rankHotSeriesLevelInfo.getSeries().stream().map(JacksonHelper::serialize).collect(Collectors.toList());
            redisWapper.rPush(key, collect, 60, TimeUnit.MINUTES);
        }

        List<String> rangeFromList = redisWapper.range(key, (pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + pageSize - 1);
        if (CollectionUtils.isEmpty(rangeFromList)) {
            return Collections.emptyList();
        }
        List<RankHotSeriesLevelInfo.RankHotSeriesInfo> collect = rangeFromList.stream()
                .map(item -> JacksonHelper.deserialize(item, RankHotSeriesLevelInfo.RankHotSeriesInfo.class))
                .collect(Collectors.toList());
        // 添加透明底车系图
        addSeriesPngLogoAndPrice(collect);
        // 添加口碑评分
        addKoubeiScoreList(collect);
        return collect;
    }

    // 降价榜
    @Override
    public List<PromotionSeriesInfo> seriesPromotionRank(Integer cityId, Integer minPrice, Integer maxPrice
            , String seriesLevel, String fuelTypeDetail, int pageIndex, int pageSize) {
        String url = "http://dealer.api.lq.autohome.com.cn/car/series/getSeriesPromotionList";
        String key = "applet:api:seriesPromotionRank:" + Md5.md5(url + cityId + minPrice + maxPrice + seriesLevel + fuelTypeDetail + pageIndex + pageSize);
        String value = redisClient.getValue(key);
        if (value != null) {
            return JacksonHelper.deserialize(value, new TypeReference<List<PromotionSeriesInfo>>() {
            });
        }
        Map<String, Object> param = new HashMap<>();
        param.put("_appId", "cms");
        param.put("pageindex", pageIndex);
        param.put("pagesize", pageSize);
        if (cityId != null && cityId > 0) {
            param.put("cityId", cityId);
        } else {
            param.put("cityId", "");
        }
        if (minPrice != null && minPrice > 0) {
            param.put("minPrice", minPrice);
        } else {
            param.put("minPrice", "");
        }
        if (maxPrice != null && maxPrice > 0) {
            param.put("maxPrice", maxPrice);
        } else {
            param.put("maxPrice", "");
        }
        if (seriesLevel != null) {
            param.put("seriesLevel", seriesLevel);
        } else {
            param.put("seriesLevel", "");
        }
        if (fuelTypeDetail != null) {
            param.put("fuelTypeDetail", fuelTypeDetail);
        } else {
            param.put("fuelTypeDetail", "");
        }
        CarListDto<PromotionSeriesInfo> carListDto = httpService.httpGetForReturnValue(url, param,
                new TypeReference<ReturnValue<CarListDto<PromotionSeriesInfo>>>() {});
        if (CollectionUtils.isEmpty(carListDto.getList())) {
            return Collections.emptyList();
        }
        // 添加透明底车系图、刷新最低价最高价
        addSeriesPngLogoAndPrice(carListDto.getList());
        // 添加口碑评分
        addKoubeiScoreList(carListDto.getList());
        redisClient.setValue(key, JacksonHelper.serialize(carListDto.getList()), 10, TimeUnit.MINUTES);
        return carListDto.getList();
    }

    // 口碑榜
    @Override
    public List<SeriesScoreRank> seriesScoreRank(Integer minPrice, Integer maxPrice, String levelIds, String place
            , Integer orderBy, Integer isNewEnergy, int pageIndex, int pageSize) {
        String url = "http://koubei.api.sjz.autohome.com.cn/api/seriesLevelScore/loadLevelSeriesScoreRank";
        String key = "applet:api:seriesScoreRankV2:" + Md5.md5(url + minPrice + maxPrice + levelIds + place + orderBy + isNewEnergy);
        if (!redisWapper.hasKey(key)) {

            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "cms");
            if (levelIds == null) {
                param.put("levelids", "");
            } else {
                param.put("levelids", levelIds);
            }
            if (orderBy == null) {
                param.put("orderBy", "");
            } else {
                param.put("orderBy", orderBy);
            }
            if (place == null) {
                param.put("place", "");
            } else {
                param.put("place", place);
            }
            if (minPrice == null || minPrice == 0) {
                param.put("minPrice", "");
            } else {
                param.put("minPrice", minPrice);
            }
            if (maxPrice == null || maxPrice == 0) {
                param.put("maxPrice", "");
            } else {
                param.put("maxPrice", maxPrice);
            }
            param.put("isnewenergy", isNewEnergy);
            List<SeriesScoreRank> seriesScoreRanks = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<List<SeriesScoreRank>>>() {
            });
            redisWapper.rPush(key, seriesScoreRanks.stream().map(JacksonHelper::serialize).collect(Collectors.toList()),
                    60, TimeUnit.MINUTES);
        }

        List<String> rangeFromList = redisWapper.range(key, (pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + pageSize - 1);
        if (CollectionUtils.isEmpty(rangeFromList)) {
            return Collections.emptyList();
        }
        List<SeriesScoreRank> collect = rangeFromList.stream()
                .map(item -> JacksonHelper.deserialize(item, SeriesScoreRank.class))
                .collect(Collectors.toList());
        // 添加透明底车系图
        addSeriesPngLogoAndPrice(collect);
        // 添加口碑评分
        addKoubeiScoreList(collect);
        return collect;
    }

    @Override
    public SmartData.Result smartRank() {
        String key = "applet:api:smartRank";
        String value = redisClient.getValue(key);
        if (value != null) {
            return JacksonHelper.deserialize(value, SmartData.Result.class);
        }
//        StatusMsg<SmartData.Result> statusMsg = httpService.httpPostFor("http://testbigdata.lf.test.autohome.com.cn/science/api/loudIndex/rankData"
        StatusMsg<SmartData.Result> statusMsg = httpService.httpPostFor("http://scienceapi.autohome.com.cn/science/api/loudIndex/rankData"
                , null, new TypeReference<StatusMsg<SmartData.Result>>() {
                });
        if (statusMsg.getStatus() != 0) {
            throw new BusinessException("source interface status not 0 (" + statusMsg.getStatus() + ")["
                    + statusMsg.getStatus() + "]", 1004);
        }
        if (statusMsg.getData() == null) {
            throw new BusinessException("source interface result is null", 1005);
        }
        List<SmartData> data = statusMsg.getData().getData();
        if (!CollectionUtils.isEmpty(data)) {
            addSeriesPngLogoAndPrice(data);
        }
        redisClient.setValue(key, JacksonHelper.serialize(statusMsg.getData()), 1, TimeUnit.HOURS);
        return statusMsg.getData();
    }

    @Override
    public SmartDataDetail smartRankDetail(int index5Id, int pageIndex, int pageSize) {
        SmartDataDetail detail = null;
        String key = "applet:api:smartRankDetail:" + index5Id;
        String keyList = "applet:api:smartRankDetail:SeriesListV2:" + index5Id;
        String value = redisClient.getValue(key);
        if (value == null || !redisWapper.hasKey(keyList)) {
            Map<String, Object> param = new HashMap<>();
            param.put("id", index5Id);
//            StatusMsg<SmartDataDetail> statusMsg = httpService.httpGetFor("http://testbigdata.lf.test.autohome.com.cn/science/api/loudIndex/rankSeriesData"
            StatusMsg<SmartDataDetail> statusMsg = httpService.httpGetFor("http://scienceapi.autohome.com.cn/science/api/loudIndex/rankSeriesData"
                    , param, new TypeReference<StatusMsg<SmartDataDetail>>() {
                    });
            if (statusMsg.getStatus() != 0) {
                throw new BusinessException("source interface status not 0 (" + statusMsg.getStatus() + ")["
                        + statusMsg.getStatus() + "]", 1004);
            }
            if (statusMsg.getData() == null) {
                throw new BusinessException("source interface result is null", 1005);
            }
            detail = statusMsg.getData();
            redisWapper.rPush(keyList, detail.getSeriesInfos().stream().map(JacksonHelper::serialize).collect(Collectors.toList())
                    , 60, TimeUnit.MINUTES);
            detail.setSeriesInfos(Collections.emptyList());
            redisClient.setValue(key, JacksonHelper.serialize(statusMsg.getData()), 1, TimeUnit.HOURS);
        }

        if (detail == null) {
            value = redisClient.getValue(key);
            if (value != null) {
                detail = JacksonHelper.deserialize(value, SmartDataDetail.class);
            }
        }
        if (detail == null) {
            throw new BusinessException("获取详情失败", 10001);
        }

        List<String> rangeFromList = redisWapper.range(keyList, (pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + pageSize - 1);
        if (CollectionUtils.isEmpty(rangeFromList)) {
            detail.setSeriesInfos(Collections.emptyList());
            return detail;
        }
        List<SmartDataDetail.SeriesInfo> collect = rangeFromList.stream()
                .map(item -> JacksonHelper.deserialize(item, SmartDataDetail.SeriesInfo.class))
                .collect(Collectors.toList());
        // 添加透明底车系图
        addSeriesPngLogoAndPrice(collect);
        detail.setSeriesInfos(collect);
        return detail;
    }

    @Override
    public List<SeriesMileageInfo> seriesMileageRank(int fuelType, int pageIndex, int pageSize) {
        String url = "http://car.api.autohome.com.cn/NewEnergy/Series_SeriesRankByMileage.ashx";
        String key = "applet:api:seriesMileageRank:" + Md5.md5(url + fuelType);
        if (!redisClient.containsKey(key)) {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("fueltype", fuelType);
            List<SeriesMileageInfo> seriesScoreRanks = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<List<SeriesMileageInfo>>>() {
            });
            if (CollectionUtils.isEmpty(seriesScoreRanks)) {
                return Collections.emptyList();
            }
            for (SeriesMileageInfo seriesInfo : seriesScoreRanks) {
                if (seriesInfo == null) {
                    continue;
                }
                redisClient.addItemToList(key, JacksonHelper.serialize(seriesInfo));
            }
            redisClient.expireEntryIn(key, 1, TimeUnit.HOURS);
        }

        List<String> rangeFromList = redisClient.getRangeFromList(key, (pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + pageSize - 1);
        if (CollectionUtils.isEmpty(rangeFromList)) {
            return Collections.emptyList();
        }
        return rangeFromList.stream()
                .map(item -> JacksonHelper.deserialize(item, SeriesMileageInfo.class))
                .collect(Collectors.toList());
    }

    // 取中汽协-汽车销量
    private SeriesSaleRank zhqxSeriesSaleRank(int brandId, boolean flag, int isNewEnergy, String levels
            , String menuTypes, int maxPrice, int minPrice, String month, int pageIndex, int pageSize) {
        String url = "http://common-output.openapi.corpautohome.com/v1/ol/carseriesSaleRank";
        String key = "applet:api:clhSeriesSaleRank:" + Md5.md5(url + brandId + minPrice + maxPrice + levels + menuTypes + month + flag + isNewEnergy);
        if (!redisClient.containsKey(key)) {
            HashMap<String, Object> header = new HashMap<>();
            //测试环境秘钥
            header.put("Authorization", "Basic" + Base64.getUrlEncoder().encodeToString("lightappapi:p3rEQ$hI0R".getBytes()));
            SeriesZhqxSaleRankParam seriesSaleRankParam = new SeriesZhqxSaleRankParam();
            seriesSaleRankParam.setFlag(String.valueOf(flag));
            if (isNewEnergy > 0)
                seriesSaleRankParam.setIsNewenergy(String.valueOf(isNewEnergy));
            seriesSaleRankParam.setLevels(Arrays.asList(levels.split(",")));
            if (!StringUtils.isEmpty(String.valueOf(menuTypes))) {
                seriesSaleRankParam.setManutypes(Arrays.asList(menuTypes.split(",")));
            } else {
                seriesSaleRankParam.setManutypes(new ArrayList<>());
            }
            seriesSaleRankParam.setMaxPrice(String.valueOf(maxPrice));
            seriesSaleRankParam.setMinPrice(String.valueOf(minPrice));
            seriesSaleRankParam.setMonth(month);
            String bodyJson = JacksonHelper.serialize(seriesSaleRankParam);
            SeriesSaleRank seriesSaleRank = httpService.httpPostJsonForReturnValue(url, bodyJson, header, new TypeReference<ReturnValue<SeriesSaleRank>>() {
            });
            for (SeriesSaleRank.DataBean seriesInfo : seriesSaleRank.getData()) {
                if (seriesInfo == null) {
                    continue;
                }
                if (brandId > 0) {// 由于要查询品牌id，顺带把其他信息也补充了
                    SeriesBaseDto sbd = getSeriesBaseDto(seriesInfo.getSeriesId());
                    if (sbd != null && brandId == sbd.getBrandid()) {
                        seriesInfo.setBrandid(sbd.getBrandid());
                        seriesInfo.setSeriespnglogo(sbd.getSeriespnglogo());
                        seriesInfo.setMinPrice(sbd.getMinprice());
                        seriesInfo.setMaxPrice(sbd.getMaxprice());
                        seriesInfo.setSeriesName(sbd.getName());
                        redisClient.addItemToList(key, JacksonHelper.serialize(seriesInfo));
                    }
                } else {
                    redisClient.addItemToList(key, JacksonHelper.serialize(seriesInfo));
                }
            }
            redisClient.expireEntryIn(key, 10, TimeUnit.MINUTES);
        }

        SeriesSaleRank result  = new SeriesSaleRank();
        result.setContent("数据为中国汽车工业协会官方发布的厂商批发量，每月18号左右更新");

        List<String> rangeFromList = redisClient.getRangeFromList(key, (pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + pageSize - 1);
        if (CollectionUtils.isEmpty(rangeFromList)) {
            result.setData(Collections.emptyList());
            return result;
        } else {
            List<SeriesSaleRank.DataBean> collect = rangeFromList.stream()
                    .map(item -> JacksonHelper.deserialize(item, SeriesSaleRank.DataBean.class))
                    .collect(Collectors.toList());
            if (brandId == 0) {
                // 添加透明底车系图
                addSeriesPngLogoAndPrice(collect);
            }
            // 添加口碑评分
            addKoubeiScoreList(collect);
            for (SeriesSaleRank.DataBean dataBean : collect) {
                dataBean.setSeriesprice(CarPriceUtils.getStrPrice(dataBean.getMinprice(), dataBean.getMaxprice()));
            }
            result.setData(collect);
            return result;
        }
    }

    // 取乘联会-汽车销量
    private SeriesSaleRank clhSeriesSaleRank(int brandId, boolean flag, int isNewEnergy, String levels
            , String menuTypes, int maxPrice, int minPrice, String month, int pageIndex, int pageSize) {
        String value = redisClient.getValue(CLH_SERIES_SALE_RANK_USE_MIX);
        // redis查得到 且 redis=0时不用mix
        boolean useMix = !"0".equals(value);
        String url = useMix ? "http://common-output.corpautohome.com/ol/carseriesCLHSaleMixRank"
                : "http://common-output.corpautohome.com/ol/carseriesCLHSaleRank";
        String key = "applet:api:clhSeriesSaleRank:" + Md5.md5(url + useMix + brandId + minPrice + maxPrice + levels + menuTypes + month + flag + isNewEnergy);
        if (!redisClient.containsKey(key)) {
            HashMap<String, Object> header = new HashMap<>();
            //测试环境秘钥
            header.put("Authorization", "Basic Y2FyZXh0OmhVWDB0aW5FJGY=");
            SeriesClhSaleRankParam seriesSaleRankParam = new SeriesClhSaleRankParam();
            seriesSaleRankParam.setFlag(String.valueOf(flag));
            if (isNewEnergy > 0)
                seriesSaleRankParam.setIsNewenergy(String.valueOf(isNewEnergy));
            seriesSaleRankParam.setLevels(Arrays.asList(levels.split(",")));
            if (!StringUtils.isEmpty(String.valueOf(menuTypes))) {
                seriesSaleRankParam.setManutypes(Arrays.asList(menuTypes.split(",")));
            } else {
                seriesSaleRankParam.setManutypes(new ArrayList<>());
            }
            seriesSaleRankParam.setMaxPrice(String.valueOf(maxPrice));
            seriesSaleRankParam.setMinPrice(String.valueOf(minPrice));
            seriesSaleRankParam.setBeginMonth(month);
            seriesSaleRankParam.setEndMonth(month);
            String bodyJson = JacksonHelper.serialize(seriesSaleRankParam);
            ClhSaleRank clhSaleRank = httpService.httpPostJsonFor(url, bodyJson, header, new TypeReference<ClhSaleRank>() {
            });
            if (clhSaleRank.getCode() != 0) {
                throw new BusinessException("source interface returncode not 0 (" + clhSaleRank.getCode() + ")["
                        + clhSaleRank.getMessage() + "]", 1004);
            }
            if (clhSaleRank.getData() == null) {
                throw new BusinessException("source interface result is null", 1005);
            }
            for (SeriesSaleRank.DataBean seriesInfo : clhSaleRank.getData()) {
                if (seriesInfo == null) {
                    continue;
                }
                if (brandId > 0) {// 由于要查询品牌id，顺带把其他信息也补充了
                    SeriesBaseDto sbd = getSeriesBaseDto(seriesInfo.getSeriesId());
                    if (sbd != null && brandId == sbd.getBrandid()) {
                        seriesInfo.setBrandid(sbd.getBrandid());
                        seriesInfo.setSeriespnglogo(sbd.getSeriespnglogo());
                        seriesInfo.setMinPrice(sbd.getMinprice());
                        seriesInfo.setMaxPrice(sbd.getMaxprice());
                        seriesInfo.setSeriesName(sbd.getName());
                        redisClient.addItemToList(key, JacksonHelper.serialize(seriesInfo));
                    }
                } else {
                    redisClient.addItemToList(key, JacksonHelper.serialize(seriesInfo));
                }
            }
            redisClient.expireEntryIn(key, 10, TimeUnit.MINUTES);
        }
        SeriesSaleRank result  = new SeriesSaleRank();
        result.setContent(useMix ? "数据源于行业综合销量，每月10日左右更新" : "数据源于乘联会发布的零售量，每月12号左右更新");


        List<String> rangeFromList = redisClient.getRangeFromList(key, (pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + pageSize - 1);
        if (CollectionUtils.isEmpty(rangeFromList)) {
            result.setData(Collections.emptyList());
            return result;
        } else {
            List<SeriesSaleRank.DataBean> collect = rangeFromList.stream()
                    .map(item -> JacksonHelper.deserialize(item, SeriesSaleRank.DataBean.class))
                    .collect(Collectors.toList());
            if (brandId == 0) {
                // 添加透明底车系图
                addSeriesPngLogoAndPrice(collect);
            }
            // 添加口碑评分
            addKoubeiScoreList(collect);
            for (SeriesSaleRank.DataBean dataBean : collect) {
                dataBean.setSeriesprice(CarPriceUtils.getStrPrice(dataBean.getMinprice(), dataBean.getMaxprice()));
            }
            result.setData(collect);
            return result;
        }
    }

    // 查询缓存中的车系基础信息
    private SeriesBaseDto getSeriesBaseDto(int seriesId) {
        String value = redisClient.getValue(seriesBaseDtoKey(seriesId));
        if (value != null) {
            return JacksonHelper.deserialize(value, SeriesBaseDto.class);
        }
        return null;
    }

    // 增加口碑评分列表
    private void addKoubeiScoreList(List<? extends KoubeiScoreListable> seriesList) {
        List<String> scoreKeys = seriesList.stream().map(KoubeiScoreListable::getSeriesId).map(this::seriesScoreKey).collect(Collectors.toList());
        Map<String, List<SeriesScoreList.Score>> scoreMap = redisClient.getAll(scoreKeys, new TypeReference<List<SeriesScoreList.Score>>() {
        });
        for (KoubeiScoreListable seriesInfo : seriesList) {
            List<SeriesScoreList.Score> scoreList = scoreMap.get(seriesScoreKey(seriesInfo.getSeriesId()));
            if (scoreList != null) {
                seriesInfo.setScoreList(scoreList);
            } else {
                seriesInfo.setScoreList(Collections.emptyList());
            }
        }
    }

    // 增加车系透明底图、最高价最低价
    private void addSeriesPngLogoAndPrice(List<? extends SeriesInfoSetable> seriesList) {
        List<String> seriesIdKeys = seriesList.stream()
                .map(SeriesInfoSetable::getSeriesId)
                .map(this::seriesBaseDtoKey)
                .collect(Collectors.toList());
        Map<String, SeriesBaseDto> allSeriesMap = redisClient.getAll(seriesIdKeys, SeriesBaseDto.class);
        if (CollectionUtils.isEmpty(allSeriesMap)) {
            return;
        }
        for (SeriesInfoSetable seriesInfo : seriesList) {
            SeriesBaseDto series = allSeriesMap.get(seriesBaseDtoKey(seriesInfo.getSeriesId()));
            if (series != null) {
                seriesInfo.setSeriespnglogo(series.getSeriespnglogo());
                seriesInfo.setMinPrice(series.getMinprice());
                seriesInfo.setMaxPrice(series.getMaxprice());
                seriesInfo.setSeriesName(series.getName());
                seriesInfo.setBrandId(series.getBrandid());
            }
        }
    }

    // 缓存车系信息 -> 缓存车系口碑评分
    @Override
    public void cacheSeriesInfo() {
        log.info("cacheSeriesInfo Scheduled");
        int totalNum = 0;
        String url = "http://car.api.autohome.com.cn/v2/Base/Series_GetAllSeries.ashx?_appid=car";
        ReturnValue<AllSeriesResultDto> result = HttpClientUtils.get(url, new TypeReference<ReturnValue<AllSeriesResultDto>>() {
        });
        if (result == null || result.getReturncode() != 0) {
            return;
        }
        if (result.getResult() == null || result.getResult().getSeriesitems() == null || result.getResult().getSeriesitems().size() == 0) {
            return;
        }
        List<String> seriesIds = new ArrayList<>();
        for (AllSeriesResultDto.SeriesDto seriesItem : result.getResult().getSeriesitems()) {
            seriesIds.add(String.valueOf(seriesItem.getId()));
            totalNum += seriesIds.size();
            if (seriesIds.size() >= 20) {
                cacheSeriesBaseDto(seriesIds);
                cacheKoubeiScore(seriesIds);
                seriesIds.clear();
            }
        }
        if (seriesIds.size() > 0) {
            cacheSeriesBaseDto(seriesIds);
            cacheKoubeiScore(seriesIds);
        }
        JobLog.info("SeriesBaseAndKoubeiScoreJob end 本次处理数据量为："+totalNum );
    }

    @Override
    public List<SeriesBaseDto> getSeriesBaseDtoCache(List<String> seriesIds) {
        List<SeriesBaseDto> seriesBaseDtoList = new ArrayList<>();
        List<String> tempSeriesIds = new ArrayList<>();
        for (String seriesId: seriesIds) {
            String seriesCache = redisClient.getValue(seriesBaseDtoKey(Integer.valueOf(seriesId)));
            if(!StringUtils.isEmpty(seriesCache)){
                seriesBaseDtoList.add(JacksonHelper.deserialize(seriesCache, SeriesBaseDto.class));
            }
            else{
                tempSeriesIds.add(seriesId);
            }
        }
        //补充redis
        if(!CollectionUtils.isEmpty(tempSeriesIds)){
            String url = "http://car.api.autohome.com.cn/v1/CarPrice/Series_BaseInfoBySeriesList.ashx";
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "car");
            param.put("serieslist", String.join(",", tempSeriesIds));
            try {
                CarListDto<SeriesBaseDto> carListDto = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<CarListDto<SeriesBaseDto>>>() {
                });
                if (!CollectionUtils.isEmpty(carListDto.getList())) {
                    List<SeriesBaseDto> collect = carListDto.getList().stream().peek(item -> {
                        item.setSeriespnglogo(item.getSeriespnglogo()
                                .replace("/autohomecar__", "/184x140_autohomecar__")
                                .replace(".png", ".png.png"));
                    }).collect(Collectors.toList());
                    for (SeriesBaseDto seriesBaseDto : collect) {
                        redisClient.setValue(seriesBaseDtoKey(seriesBaseDto.getSeriesid()), JacksonHelper.serialize(seriesBaseDto), 365, TimeUnit.DAYS);
                        seriesBaseDtoList.add(seriesBaseDto);
                    }
                }
            } catch (BusinessException e) {
                JobLog.warn("getSeriesBaseDtoCache exception " + e.getCode() + " " + e.getMessage() + " " + String.join(",", seriesIds));
            }
        }
        return seriesBaseDtoList;
    }

    private void cacheSeriesBaseDto(List<String> seriesIds) {
        String url = "http://car.api.autohome.com.cn/v1/CarPrice/Series_BaseInfoBySeriesList.ashx";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("serieslist", String.join(",", seriesIds));
        try {
            CarListDto<SeriesBaseDto> carListDto = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<CarListDto<SeriesBaseDto>>>() {
            });
            if (CollectionUtils.isEmpty(carListDto.getList())) {
                return;
            }
            List<SeriesBaseDto> collect = carListDto.getList().stream().peek(item -> {
                item.setSeriespnglogo(item.getSeriespnglogo()
                        .replace("/autohomecar__", "/184x140_autohomecar__")
                        .replace(".png", ".png.png"));
            }).collect(Collectors.toList());
            for (SeriesBaseDto seriesBaseDto : collect) {
                redisClient.setValue(seriesBaseDtoKey(seriesBaseDto.getSeriesid()), JacksonHelper.serialize(seriesBaseDto), 365, TimeUnit.DAYS);
            }
        } catch (BusinessException e) {
            JobLog.warn("cacheSeriesBaseDto exception " + e.getCode() + " " + e.getMessage() + " " + String.join(",", seriesIds));
        }
    }

    private void cacheKoubeiScore(List<String> seriesIds) {
        String url = "http://koubei.api.sjz.autohome.com.cn/api/series/getseriesscorerankbatch";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("seriesids", String.join(",", seriesIds));
        try {
            SeriesScoreList.SeriesListScoreList scoreList = httpService.httpGetForReturnValue(url, param, new TypeReference<ReturnValue<SeriesScoreList.SeriesListScoreList>>() {
            });
            for (SeriesScoreList seriesScoreList : scoreList.getList()) {
                redisClient.setValue(seriesScoreKey(seriesScoreList.getSeriesId()), JacksonHelper.serialize(seriesScoreList.getScoreList()), 365, TimeUnit.DAYS);
            }
        } catch (BusinessException e) {
            JobLog.warn("cacheKoubeiScore exception " + e.getCode() + " " + e.getMessage() + " " + String.join(",", seriesIds));
        }
    }

    private String seriesBaseDtoKey(int seriesId) {
        return "applet:task:seriesbasedto:seriesId:" + seriesId;
    }

    private String seriesScoreKey(int seriesId) {
        return "applet:task:series:score:seriesId:" + seriesId;
    }
}
