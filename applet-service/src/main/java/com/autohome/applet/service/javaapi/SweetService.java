package com.autohome.applet.service.javaapi;

import com.autohome.applet.dao.javaapi.model.Sweet;

import java.util.Date;

public interface SweetService {

    Sweet createNew(int clientType, int sweetId, String sweetName, String img, String jumpTo,
                    Date startTime, Date endTime, String clientString, String openScreen,
                    Integer status, Integer jumpType, String otherAppid, String reportPoint,
                    String shareDesc, String shareImg, Integer contentType, String showPoint,
                    Integer copySortNum, Integer forceSort, String skinBottomColor, String searchBottomColor,
                    String indexOrSearchOrRecomColor, String sweetBottomLineColor,
                    Integer needLogin, Integer categoryPart,
                    int contry, String choiceprovinceids, String choiceprovinceNames, String choiceCityids, String choiceCityNames);

    void setHomeFeed7(String title, String img, String jumpTo, int jumpType, String reportPoint, String showPoint, Date startTime, Date endTime);

    void setHomeFocus2(String title, String img, String jumpTo, int jumpType, String reportPoint, String showPoint, Date startTime, Date endTime);

    void setDetailAd(String cid, int detailType, String title, String img, String jumpTo, int jumpType, String reportPoint, String showPoint, Date startTime, Date endTime);
}
