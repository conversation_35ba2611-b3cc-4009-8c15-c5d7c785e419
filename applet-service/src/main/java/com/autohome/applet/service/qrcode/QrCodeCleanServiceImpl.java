package com.autohome.applet.service.qrcode;

import com.autohome.applet.dao.javaapi.mapper.QrCodeMapper;
import com.autohome.applet.dao.javaapi.model.qrcode.QrCodeModel;
import com.autohome.applet.service.tool.ImageUploader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class QrCodeCleanServiceImpl implements QrCodeCleanService{
    @Autowired
    QrCodeMapper qrCodeMapper;

    @Autowired
    private ImageUploader imageUploader;

    public List<QrCodeModel> getByPage(long lsatId,long maxId,int pageSize, int shardingIndex, int shardingTotal,boolean isDel){
        return isDel ?
                qrCodeMapper.getDelByPage(lsatId,maxId,pageSize,shardingIndex,shardingTotal):
                qrCodeMapper.getUnDelByPage(lsatId,maxId,pageSize,shardingIndex,shardingTotal);
    }

    public boolean cleanQrCodeImg(QrCodeModel qrCodeModel,boolean logInfo){
        String img = qrCodeModel.getImgUrl();
        boolean isSuccess = false;
        try {
            img = img.replace("/100x100_autohomecar__",
                    "/autohomecar__");
            //删除图片
            //type :  0：不删除CDN缓存，1：异步删除CDN缓存
            isSuccess = imageUploader.del(img,1);
            if(isSuccess){
                if(logInfo){
                    log.info("cleanQrCodeImg成功:{},{}",qrCodeModel.getQrCodeId(),isSuccess);
                }
            }else{
                log.warn("cleanQrCodeImg失败:{}",qrCodeModel.getQrCodeId());
            }
        } catch (Exception e) {
            log.warn("cleanQrCodeImg error:{}",qrCodeModel,e);
        }
        return isSuccess;
    }

    public boolean updateQrCodeStatus(long qrCodeId,int status){
        return qrCodeMapper.updateQrCodeStatus(qrCodeId,status);
    }

    public int updateQrCodeStatus(List<Long> qRCodeIds, int status){
        return qrCodeMapper.updateQrCodeStatusByIds(qRCodeIds,status);
    }

    public boolean cleanQrCodeAndImg(QrCodeModel qrCodeModel,boolean logInfo){
        String img = qrCodeModel.getImgUrl();
        boolean isSuccess = false;
        try {
            img = img.replace("/100x100_autohomecar__",
                    "/autohomecar__");
            //删除图片
            //type :  0：不删除CDN缓存，1：异步删除CDN缓存
            isSuccess = imageUploader.del(img,0);
            if(isSuccess){
                //删除数据
                isSuccess = qrCodeMapper.updateQrCodeStatus(qrCodeModel.getQrCodeId(),QrCodeModel.DEL_DATA_AND_IMG_STATUS);
                if(logInfo){
                    log.info("cleanQrCodeModel成功:{},{}",qrCodeModel,isSuccess);
                }
            }else{
                log.warn("delImg失败:{}",qrCodeModel);
            }
        } catch (Exception e) {
            log.warn("cleanQrCodeModel error:{}",qrCodeModel,e);
        }
        return isSuccess;
    }
}
