package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.model.dto.netcoreapi.qrcode.QRcode;
import com.autohome.applet.model.dto.netcoreapi.qrcode.Qrcode4Redis;
import com.autohome.applet.service.netcoreapi.QRcodeService;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QRcodeServiceImpl implements QRcodeService {

    @Autowired
    @Qualifier("lightapp")
    RedisClient redisClientLightapp;

    @Override
    public QRcode GetWxQRcode2(int objid, int type, int specid, int channel) {
        String wxurl;
        String sence;

        switch (type) {
            case 1:
                wxurl = "view/article/show";  ///原创文章详情页
                sence = "articleid=" + objid + "&type=1&m=1";
                break;
            case 2:
                wxurl = "view/series/index"; ///车系综述页
                sence = "seriesid=" + objid + "&m=1";
                break;
            case 3:
                wxurl = "view/spec/index"; //车型综述页
                sence = "seriesid=" + objid + "&specid=" + specid + "&m=1";
                break;
            case 4:
                wxurl = "view/article/show"; //车家号文章详情页
                sence = "articleid=" + objid + "&type=4&m=1";
                break;
            case 5:
                wxurl = "view/home/<USER>/show";  //车家号视频详情页
                sence = "id=" + objid + "&cjhtype=4&m=1";
                break;
            case 6:
                wxurl = "view/home/<USER>/show"; //原创视频详情页
                sence = "id=" + objid + "&cjhtype=0&m=1";
                break;
            case 7:
                wxurl = "view/club/detail/detail"; //论坛文章详情页
                sence = "id=" + objid + "&type=0&m=1";
                break;
            case 8:
                wxurl = "package2/pages/travel/index"; //旅行家详情页 游记
                sence = "id=" + objid + "&m=1";
                break;
            case 9:
                wxurl = "view/koubei/detail/detail"; //口碑详情
                sence = "koubeiid=" + objid + "&m=1";
                break;
            case 10:
                wxurl = "view/series/index"; //口碑列表
                sence = "seriesid=" + objid + "&ptype=kb&m=1";
                break;
            case 11:
                wxurl = "detailpackage/pages/littlevideo/index"; //小视频
                sence = "id=" + objid + "&m=1";
                break;
            //case 12:
            //    wxurl = "testpackage/pages/share/share"; //小视频
            //    sence = "user_id=" + objid;
            //    break;
            case 12:
                wxurl = "package2/pages/travel/strategy/index"; //旅行家详情页 攻略
                sence = "id=" + objid + "&m=1";
                break;

            case 21:
                wxurl = "view/h5/quicknews/index"; //每日快讯
                sence = "id=" + objid;
                break;
            case 22:
                wxurl = "webview/vr/index"; //主软全景看车VR分享微信唤起之家小程序-内饰
                sence = "type=pano&id=" + objid;
                break;
            case 23:
                wxurl = "webview/vr/index"; //主软全景看车VR分享微信唤起之家小程序-外观
                sence = "type=ext&id=" + objid;
                break;
            case 24:
                wxurl = "detailpackage/pages/lighttext/lighttext"; //轻文
                sence = "id=" + objid;
                break;
            case 25:
                wxurl = "detailpackage/pages/carshow/carshow"; //车单
                sence = "id=" + objid;
                break;
            case 26:
                wxurl = "cyq/view/activity/detail/detail"; //车友圈
                sence = "id=" + objid + "&m=1";
                break;
            case 27:
                wxurl = "view/calculator/index"; //购车计算器-贷款
                sence = "objid=" + objid + "&sendtype=1";
                break;
            case 28:
                wxurl = "view/calculator/index"; //购车计算器-全款
                sence = "objid=" + objid + "&sendtype=2";
                break;
            case 29:
                wxurl = "detailpackage/pages/today/index"; //早报的落地页
                sence = "newsid=" + objid;
                break;
            case 60:
                wxurl = "view/home/<USER>/index"; //图说落地页
                sence = "id=" + objid + "&type=1";
                break;
            case 61:
                wxurl = "view/home/<USER>/index"; //新车图解落地页
                sence = "id=" + objid + "&type=1&pagetype=tujie";
                break;
            case 62:
                wxurl = "package/pages/topic/index"; //话题页
                sence = "tid=" + objid;
                break;
            case 63:
                wxurl = "package/pages/topic/topic"; //话题详情页落地页
                sence = "id=" + objid;
                break;

            case 70:
                wxurl = "detailpackage/pages/xiaoshipin/index"; //小视频
                sence = "videoid=" + objid;
                break;
            case 71:
                wxurl = "package2/pages/xiaoshipin/index"; //车系小视频
                sence = "seriesid=" + objid;
                break;
            case 75:
                wxurl = "view/series/image"; //图片列表
                sence = "type=" + channel + "&seriesid=" + objid;
                break;
            case 76:
                wxurl = "view/series/image"; //图片列表
                sence = "seriesid=" + objid + "&type=" + channel + "&specid=" + specid;
                break;
            case 77:
                wxurl = "detailpackage/pages/travel-article/index"; //旅摄-图文类型
                sence = "storyId=" + objid;
                break;
            case 78:
                wxurl = "detailpackage/pages/travel-video/index"; //旅摄-小视频类型
                sence = "storyId=" + objid;
                break;

            case 79:
                wxurl = "detailpackage/pages/commentDetail/index"; //汽车点评
                sence = "id=" + objid;
                break;

            case 80:
                wxurl = "detailpackage/pages/short-article/index"; //原创快讯
                sence = "objectid=" + objid;
                break;

            case 81:
                wxurl = "detailpackage/pages/day-news/index"; //日报
                sence = StringUtils.EMPTY;
                break;

            case 91:
                wxurl = "car-package/pages/moto-series/index"; //摩托车车系详情
                sence = "seriesid=" + objid;
                break;

            case 92:
                wxurl = "car-package/pages/pic/list/index"; //摩托车车系详情
                sence = "moto=1&seriesid=" + objid + "&type=" + channel;
                break;

            case 93:
                wxurl = "activity-package/business/index/index"; //抽奖详情
                sence = "activityId=" + objid + "&shareId=" + channel;
                break;

            case 94:
                wxurl = "activity-package/business/list/index"; //抽奖列表
                sence = "shareId=" + objid;
                break;

            default:
                wxurl = "view/home/<USER>"; //首页
                sence = "m=1";
                break;
        }

        QRcode code = new QRcode();
        code.setSence(sence);
        code.setWxurl(wxurl);

        return code;
    }

    @Override
    public String GetQRCode3(QRcode code) {
        String key4img = "lightapp:table:qrcode:pageurl:" + code.getWxurl() + ":sence:" + code.getSence();
        log.info(key4img);
        String img = redisClientLightapp.get(key4img);
        log.info(img);

        if (!StringUtils.isEmpty(img)) {
            //return img;
            img = img.replaceAll("\\\"", ""); //netcore 存进去的会多一层引号
            return img;
        }

        String qrCodePrepareList = "lightapp:weixinservice:qrcode:prepare:list";
        String qrCodeDbList = "lightapp:weixinservice:qrcode:DB:list";
        String qrCodeJson = redisClientLightapp.dequeueItemFromList(qrCodePrepareList);

        log.info(qrCodeJson);

        if (!StringUtils.isEmpty(qrCodeJson)) {

            Qrcode4Redis qrCode = JacksonHelper.deserialize(qrCodeJson, Qrcode4Redis.class);
            if (qrCode != null && !StringUtils.isEmpty(qrCode.getImgUrl())) {
                String key4id = "lightapp:table:qrcode:qrcodeid:" + qrCode.getQrCodeId();
                log.info("key4id " + key4id);
                img = qrCode.getImgUrl();
                qrCode.setPageUrl(code.getWxurl());
                qrCode.setSence(code.getSence());
                String qrcodeJson = JacksonHelper.serialize(qrCode);
                redisClientLightapp.enqueueItemOnList(qrCodeDbList, qrcodeJson);
                redisClientLightapp.add(key4id, qrCode, 2, TimeUnit.HOURS);

                redisClientLightapp.add(key4img, img, 2, TimeUnit.HOURS);
                return img;
            }
        }

        if (StringUtils.isEmpty(img)) {
            return defaultQrcodeImg;//"https://car3.autoimg.cn/cardfs/activity/g29/M0A/F0/95/100x100_autohomecar__ChsEn1wgUjyAEX6yAAC57BUfG1o773.jpg";
        }
        return img;
    }

    /**
     * 根据页面类型和参数获取库中预存的二维码图片
     */
    @Override
    public QRcode GetWxQRcodeSpecPk(int type, List<Integer> specs) {
        List newList = specs.stream().distinct().collect(Collectors.toList());
        ;
        String pageurl;
        String sence = StringUtils.EMPTY;

        if (specs.size() > 5) {
            newList = specs.subList(0, 4);
            sence = "&s=1";
        }
        Collections.sort(newList);
        //优先通过数据库查询-数据库中数据通过定时服务跑日志等方式插入记录

        switch (type) {
            case 11:
                pageurl = "view/carcontrast/detail"; //车系PK(最多5条)
                sence += "id=" + newList.stream().map(String::valueOf).collect(Collectors.joining(",")) + "&m=1&v=1";
                break;

            default:
                pageurl = "view/home/<USER>"; //首页
                sence = "m=1";
                break;
        }
        QRcode code = new QRcode();
        code.setSence(sence);
        code.setWxurl(pageurl);

        return code;
    }

    private static final String defaultQrcodeImg = "https://car3.autoimg.cn/cardfs/activity/g29/M0A/F0/95/100x100_autohomecar__ChsEn1wgUjyAEX6yAAC57BUfG1o773.jpg";

    @Override
    public String getDefaultQrcodeImg() {
        return defaultQrcodeImg;
    }
}
