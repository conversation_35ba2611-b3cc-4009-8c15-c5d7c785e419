package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.ParamBySeries;
import com.autohome.applet.service.javaapi.BaseCarService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 主要负责调用产品库源接口的封装，源接口的数据缓存也在此处理
 * <AUTHOR>
 */
@Slf4j
@Service
public class BaseCarServiceImpl implements BaseCarService {

    @Override
    public List<ParamBySeries> seriesParamBySeriesList(List<Integer> seriesIds) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("serieslist", seriesIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v2/carprice/series_parambyserieslist.ashx", param);
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("getSeriesListByseriesids error");
            return null;
        }

        ReturnValue<List<ParamBySeries>> tmpModelSeries = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<ParamBySeries>>>() {
        });
        if (tmpModelSeries != null && tmpModelSeries.getReturncode() == 0) {
            return tmpModelSeries.getResult();
        }
        return null;
    }
}
