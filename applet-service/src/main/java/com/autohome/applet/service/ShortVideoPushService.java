package com.autohome.applet.service;

import com.autohome.applet.model.dto.ShortVideo.VideoAppletDto;

import java.util.List;

public interface ShortVideoPushService {
    final static String VIDEO = "video";
    final static String SECRETKEY = "c813618dd3cb629747d3a178f0f22279";
    final static String SOURCE = "qczj";
    final static Integer SIZE = 100;
    final static int PAGESIZE = 100;

    /**
     * 查询短视频数据
     * */
    List<VideoAppletDto> getVideoApplet(String startTime , String endTime);

    void uploadToShortVideoPush(List<VideoAppletDto> videoAppletDtoList , String startTime , String endTime);


}
