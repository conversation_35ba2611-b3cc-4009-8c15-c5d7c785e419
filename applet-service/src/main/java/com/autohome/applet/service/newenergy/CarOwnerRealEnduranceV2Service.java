package com.autohome.applet.service.newenergy;

import java.util.List;

import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfBaseInfoDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceOfKoubeiDTO;
import com.autohome.applet.model.dto.newenergy.CarOwnerRealEnduranceSpecInfoDTO;
import com.autohome.applet.model.dto.newenergy.SeriesBaseInfoDto;
import com.autohome.applet.model.dto.newenergy.SeriesBaseInfoV2;
import com.autohome.applet.model.dto.newenergy.SeriesConfigInfoResult;
import com.autohome.applet.model.dto.newenergy.SameCarSeriesResult;

/**
 * @description: 新能源车主真实续航V2
 * @author: WangBoWen
 * @date: 2024-01-11
 **/
public interface CarOwnerRealEnduranceV2Service {

    CarOwnerRealEnduranceOfBaseInfoDTO baseInfoV2OfCache (Integer cityId, Integer seriesId);



    CarOwnerRealEnduranceSpecInfoDTO specDataV2OfCache (Integer cityId, Integer seriesId, Integer specId, Integer officialDriveRange, Integer dataType);

    CarOwnerRealEnduranceSpecInfoDTO specDataOfCache(Integer cityId, Integer seriesId, Integer specId);

    SeriesBaseInfoV2.Energyconfigbeans getEnergyconfigbeansOfCache(int seriesid, int cityid);

    /**
     * 获取车系落地页数据
     * @param cityId 城市id
     * @param seriesId 车系id
     * @return
     */
    CarOwnerRealEnduranceOfBaseInfoDTO baseInfoV2 (Integer cityId, Integer seriesId);

    /**
     * 获取北里数据
     * @param cityId 城市id
     * @param seriesId 车系id
     * @param specId 车系id
     * @return
     */
    CarOwnerRealEnduranceOfKoubeiDTO getBeiLiData(Integer cityId, Integer seriesId, Integer specId);

    /**
     * 获取车系参配
     * @param seriesId
     * @return
     */
    SeriesConfigInfoResult getConfigFromCarServicesBySeriesId(Integer seriesId);

    /**
     * 获取车型数据接口
     * @param cityId 城市id
     * @param seriesId 车系id
     * @param specId 车型id
     * @param officialDriveRange 官方续航
     * @param dataType           1:北理，2:口碑
     * @return
     */
    CarOwnerRealEnduranceSpecInfoDTO specDataV2 (Integer cityId, Integer seriesId, Integer specId, Integer officialDriveRange, Integer dataType);

    CarOwnerRealEnduranceSpecInfoDTO specData(Integer cityId, Integer seriesId, Integer specId);

    SeriesBaseInfoV2.Energyconfigbeans getEnergyconfigbeans(int seriesid, int cityid, SeriesBaseInfoDto seriesDetailTask);

    SameCarSeriesResult seriesListCompareInfos(Integer cityId, List<String> seriesIds);
}
