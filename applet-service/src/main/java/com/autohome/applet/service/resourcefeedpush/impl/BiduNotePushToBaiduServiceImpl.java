package com.autohome.applet.service.resourcefeedpush.impl;

import com.alibaba.fastjson.JSON;
import com.autohome.applet.dao.javaapi.model.BaiduNotePushLogV3;
import com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.maindata.HotDataByBizType;
import com.autohome.applet.model.dto.resourcefeedpush.PushAtomicResultDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BadiduDTO_Data;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BadiduDTO_Display;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BadiduDTO_Poi_list;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BaiduDTO;
import com.autohome.applet.model.enums.MainDataTypeToBizTypeEnum;
import com.autohome.applet.service.baidu.BaiduNotePushLogV3Service;
import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.ImageSizeUtil;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BiduNotePushToBaiduServiceImpl implements ResourceFeedPushService {
    @Autowired
    private DouYinService douYinService;
    @Autowired
    private BaiduNotePushLogV3Service baiduNotePushLogV3Service;

    private int bizType = 999001;


    //    private static List<String> TAGS = Arrays.asList("单车评测", "双车评测", "多车评测", "单车试驾", "双车试驾", "多车试驾", "单车体验", "双车体验", "多车体验", "实拍解析", "到店实拍", "评测体验(其他)", "二手车评测", "二手车导购", "二手车检测", "二手车修整", "二手车买卖攻略", "二手车收车日记", "二手车行业动态", "二手车(其他)", "买车攻略", "提车报告", "买车/提车(其他)", "车闻趣事", "明星座驾", "美女模特", "汽车影视", "广告/宣传片", "玩车", "用车攻略", "车务办理", "用车成本", "交通违章及法规", "车辆故障", "汽车保养", "美容装饰", "汽车用品", "出行信息", "驾驶技巧", "汽车维修", "用车养车(其他)", "车型口碑", "自驾游", "越野", "汽车生活", "改装车", "摩托车", "房车", "特种车");
    private static List<String> TAGS = new ArrayList<>();

    private static Map<String, String>  TOKENMAP = new HashMap<String, String>() {{
        put("club.m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("chejiahao.m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("v.m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("qzuouickapp-h5.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("quickapp-h5.autohome.com.cn", "27k95LOmoQqpJ44u");
    }};

    /**
     * 计数器
     */
    private PushAtomicResultDTO atomicPushAtomicResultDTO;
//    private HashMap<String, PushAtomicResultDTO> atomicResultDTOHashMap;

    @Override
    public void doPush(PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        //todo 查询新插入的表 总数
        List<BaiduNotePushLogV3> baiduNotePushLogV3List = baiduNotePushLogV3Service.getNoPushList();//查询未推送的数据
        atomicPushAtomicResultDTO = new PushAtomicResultDTO(baiduNotePushLogV3List.size());

        sendLog(String.format("BiduNotePushToBaiduServiceImpl 需要推送总数：%s", atomicPushAtomicResultDTO.getTotal()));
        //执行push操作
        if (atomicPushAtomicResultDTO.getTotal() > 0) {
            push(baiduNotePushLogV3List);
        }

        sendLog();
    }

    private void push(List<BaiduNotePushLogV3> baiduNotePushLogV3List) {
        Set<String> urlSet = new HashSet<>();
        List<BaiduDTO> baiduDTOList = new ArrayList<>();
        baiduNotePushLogV3List.forEach(ext -> {
//            String bizUrl = getBizH5Url(bizType);
            String bizUrl = getBizH5UrlV3(ext.getExId());
            urlSet.add(bizUrl);
            //构建推送到百度DTO
            BaiduDTO item = buildBaiduDTO(ext, bizUrl);
            baiduDTOList.add(item);
            log.info("BiduNotePushToBaiduServiceImpl push item " + ext.getId() + " " + bizType + " " + ext.getModifiedSTime() + " " + bizUrl);
        });
        sendToBaidu(baiduDTOList);

    }

    private BaiduDTO buildBaiduDTO(BaiduNotePushLogV3 ext, String url) {
        BadiduDTO_Display display = buildBaiduDOTDisplay(ext, url);

        BadiduDTO_Data data = new BadiduDTO_Data();
        data.setDisplay(display);

        BaiduDTO baiduDTO = new BaiduDTO();
        baiduDTO.setLoc(url); //必选 落地页
        baiduDTO.setLastmod(DateHelper.serialize(ext.getModifiedSTime(), DateHelper.DATEFORMAT_STANDARD));//可选
        baiduDTO.setChangefreq("always");//更新频率
        baiduDTO.setPriority(1);//优先级
        baiduDTO.setData(data);
        baiduDTO.setBiz("999001");
        baiduDTO.setExtid(ext.getExId());
        return baiduDTO;
    }

    private BadiduDTO_Display buildBaiduDOTDisplay(BaiduNotePushLogV3 b, String url) {
        BadiduDTO_Display display = new BadiduDTO_Display();
        display.setUrl("");//落地页 必选
        display.setTitle(b.getTitle());//标题 必选
        display.setContent(b.getContent());
        List<String> imgList = new ArrayList<>();
        if (!StringUtils.isEmpty(b.getImgs())) {
            imgList = Arrays.asList(b.getImgs().split(","));
            int i = 0;
            for (String s : imgList) {
                imgList.set(i, "\"" + s + "\"");
                i++;
            }
        }

        display.setThumbnail_img_list("[" + String.join(",", imgList) + "]");
        display.setVideo_url("");//没有视频填空 必选
        display.setVideo_thumbnail_url("");//视频封面 必选
        display.setAuthor_name(b.getAuthorName());//作者姓名 必选
        //display.setAuthor_url("");//作者主页url 可选
        //display.setAuthor_tag("");//作者认证,无填空 可选
        display.setAuthor_img(!StringUtils.isEmpty(b.getAuthorImg()) ? b.getAuthorImg() : "https://x.autoimg.cn/news/miniprogram/touxiang2.png");//作者头像 必选
        //display.setAuthor_level("");//作这等级 可选
        display.setAuthor_fans(0);//作者粉丝 必选
        //display.setAuthor_fields("");//作者领域 可选
        //display.setAuthor_docs(0);//历史发文总数 可选
        display.setDate_published(b.getPushDate().split(" ")[0]);//发布是时间 必选 YYYY-MM-DD
        //display.setQuality_score(0);//优质程度 可选
        display.setContent_quality_label("精选");//质量等级标签 必选
//        if (!org.apache.commons.lang3.StringUtils.isEmpty(b.getCmsTags())) {
//            display.setContent_tag(b.getCmsTags().replace("/", ",").replace(" ", ","));//内容标签 半角逗号分隔 可选
//        }
        display.setLike_count(0);//赞数 必选
        //display.setUnlike_count(0);//踩  可选
        display.setComment_count(0);//评论数 必选
        display.setComment_content("");//评论 必选
        display.setCollect_count(0);//收藏  必选
        display.setRepost_count(0);//转发数 必选
        display.setImage_count(imgList.size());//图片数  必选
        display.setVideo_list("");//视频列表,必选 没有清空
        display.setIs_video(1);//内容类型 1图文,2视频 必选
        display.setStatus(1);//状态 1正常 2删除 必选
        display.setSrc_type("汽车之家");//来源渠道 必选
        display.setSub_src("汽车之家");//来源子分类 必选
        display.setThird_id(String.valueOf(b.getExId()));//第三方内容id 可选
        display.setIs_abroad(1);//1国内 2国外 必选
        display.setCountry("中国");//国家地区 必选
        display.setPoi_type(2);//1 poi坐标,2城市
        display.setUrl(url);
//        display.setContent(display.getTitle());
        display.setContent(display.getContent());
        BadiduDTO_Poi_list poi_list = new BadiduDTO_Poi_list();
        poi_list.setCity_name(b.getCityName()); //城市名
        poi_list.setPoi_name("");//位置名
        display.setPoi_list(poi_list);
        return display;
    }

    private void sendToBaidu(List<BaiduDTO> list) {
        int success = 0;
        int error = 0;
        int baiduError = 0;
        int ignore = 0;
        int tagIgnore = 0;
        for (BaiduDTO baidu3GC : list) {
            String traceid = UUID.randomUUID().toString().replaceAll("-", "");
            String body = JSON.toJSONString(baidu3GC);


            try {
                if (!StringUtils.isEmpty(baidu3GC.getLoc())) {
                    URL url = new URL(baidu3GC.getLoc());
                    String host = url.getHost();
                    String postUrl = "http://ziyuan-data.baidu.com/json?site=https://" + host + "&token=" + TOKENMAP.get(host) + "&type=ugc&traceid=" + traceid;
                    String baiduResp = postbaidu(postUrl, body, 5000, "application/json", "utf-8", "utf-8");
                    log.info("baidu3GC host={}, token:{}",host, TOKENMAP.get(host));
                    log.info("baidu3GC url ={},body={},baiduResp = {}" ,postUrl, body,baiduResp);
                    if (baiduResp.contains("success")) {
                        log.info("baidu3GC success " + traceid + " " + baidu3GC.getLoc() + " " + baiduResp + " " + body);
                        success += 1;
                        atomicPushAtomicResultDTO.getSuccess().addAndGet(1);
                        //update BaiduNotePushLogV3 的状态位
                        BaiduNotePushLogV3 baiduNotePushLogV3 = new BaiduNotePushLogV3();
                        baiduNotePushLogV3.setExId(baidu3GC.getExtid());
                        baiduNotePushLogV3.setPushState(1);
                        baiduNotePushLogV3Service.updateByExId(baiduNotePushLogV3);

                    } else {
                        log.warn("baidu3GC warn  traceid:{},url:{}",traceid,postUrl);
                        log.warn("baidu3GC warn traceid:{},body:{}",traceid,body);
                        log.warn("baidu3GC warn traceid:{},baiduResp:{}",traceid,baiduResp);
                        log.warn("baidu3GC warn traceid:{},traceid" + traceid + " " + baidu3GC.getLoc() + " " + baiduResp + " " + body);
                        baiduError += 1;
                        atomicPushAtomicResultDTO.getPushError().addAndGet(1);
                    }
                }

            } catch (Exception e) {
                error += 1;
                atomicPushAtomicResultDTO.getError().addAndGet(1);
                log.error("sendToBaidu err " + baidu3GC.getLoc() + " " + body, e);
            }
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("sendToBaidu error:", e);
            }

        }
    }

    private String postbaidu(String url, String params, Integer timeout, String contentType, String sendEncoding, String receiveEncoding) {

        PrintWriter printWriter = null;
        HttpURLConnection con = null;
        BufferedReader in = null;

        try {
            URL obj = new URL(url);
            con = (HttpURLConnection) obj.openConnection();
            con.setRequestMethod("POST");

            con.setRequestProperty("Content-Type", contentType);
            con.setRequestProperty("connection", "Keep-Alive");
            con.setRequestProperty("Content-Length", String.valueOf(params.length()));
//            con.setRequestProperty("APPKEY", "88A51C6D0A060EA44E0C73D9CED5D2FA");
            //con.setRequestProperty("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString(("uc-news-quickappservice:8sOG24^gr^").getBytes()));
            // 设置连接主机超时
            con.setConnectTimeout(timeout);
            // 设置从主机读取数据超时
            con.setReadTimeout(timeout);

            // 发送 post 必须
            con.setDoOutput(true);
            con.setDoInput(true);


            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(con.getOutputStream(), sendEncoding));
            bw.write(params);
            bw.flush();
            bw.close();


            // 根据ResponseCode判断连接是否成功
            int responseCode = con.getResponseCode();

            in = new BufferedReader(

                    new InputStreamReader(responseCode == 200 ? con.getInputStream() : con.getErrorStream(), receiveEncoding));


            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
                //response.append("\r\n");
            }
            in.close();

            return response.toString();


        } catch (Exception e) {
            log.error("postbaidu note error param:%s", JacksonHelper.serialize(params), e);
            return e.getMessage();
        } finally {
            if (con != null) {
                con.disconnect();
            }

            if (printWriter != null) {
                printWriter.close();
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("postbaidu note BufferedReader error param:%s", JacksonHelper.serialize(params), e);
                }
            }
        }
    }

    private String getBizH5Url(int bizType) {
        String url = "";
        switch (bizType) {
            case 999001: {
                //百度笔记 自定义excel内容
//                url = "https://qzuouickapp-h5.autohome.com.cn/note?objectid=" + bizType;
                url = "https://quickapp-h5.autohome.com.cn/note?extid=" + bizType;

                break;
            }

        }
        return url;
    }

    private String getBizH5UrlV3(int extid) {
        String url = "https://quickapp-h5.autohome.com.cn/note?extid="+extid;
        return url;
    }

    private void sendLog() {
        String logContent = "";


        logContent = String.format("BiduNotePushToBaiduServiceImpl, 需推送: %s, 实际推送: %s, 有效推送: %s, 忽略: %s, tag 忽略: %s, 推送报错: %s, 报错: %s"
                , atomicPushAtomicResultDTO.getTotal(), atomicPushAtomicResultDTO.getPushDone().get(), atomicPushAtomicResultDTO.getSuccess().get(), atomicPushAtomicResultDTO.getIgnore().get()
                , atomicPushAtomicResultDTO.getTagIgnore().get(), atomicPushAtomicResultDTO.getPushError().get(), atomicPushAtomicResultDTO.getError().get());

        douYinService.addBaiduReportRedis(BAIDU_3GC_KEY, DateHelper.serialize(DateHelper.getNow(), DateHelper.DATEFORMAT_ONLY_DATE), 1, 1, 0, atomicPushAtomicResultDTO.getPushDone().get());
        douYinService.addBaiduReportRedis(BAIDU_3GC_KEY, DateHelper.serialize(DateHelper.getNow(), DateHelper.DATEFORMAT_ONLY_DATE), 1, 1, 1, atomicPushAtomicResultDTO.getSuccess().get());
        douYinService.addBaiduReportRedis(BAIDU_3GC_KEY, DateHelper.serialize(DateHelper.getNow(), DateHelper.DATEFORMAT_ONLY_DATE), 1, 1, 2, atomicPushAtomicResultDTO.getPushError().get());
        XxlJobHelper.log(logContent);
        log.info(logContent);
    }

    private void sendLog(String logContent) {
        XxlJobHelper.log(logContent);
        log.info(logContent);
    }

    private int getIntegerValue(String value) {
        try {
            int result = Integer.valueOf(value);
            return result < 0 ? 0 : result;
        } catch (Exception ex) {
            return 0;
        }
    }
}
