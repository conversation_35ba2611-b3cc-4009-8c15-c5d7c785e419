package com.autohome.applet.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.autohome.applet.model.constants.RedisKeys;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecParam;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarSeriesList;
import com.autohome.applet.model.dto.newenergy.*;
import com.autohome.applet.model.enums.ConfigInfoEnum;
import com.autohome.applet.model.enums.SeasonEnum;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCarCommonService;
import com.autohome.applet.service.newenergy.CarOwnerRealEnduranceService;
import com.autohome.applet.service.newenergy.CarOwnerRealEnduranceV2Service;
import com.autohome.applet.service.utils.CityUtils;
import com.autohome.applet.service.utils.SchemeUtils;
import com.autohome.applet.service.utils.SeasonUtil;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.MapBuilder;
import com.autonews.comm.BaseModel;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 新能源车主真实续航v2service
 * @author: WangBoWen
 * @date: 2024-01-11
 **/
@Service
@Slf4j
public class CarOwnerRealEnduranceServiceV2Impl implements CarOwnerRealEnduranceV2Service {

    @Autowired
    private CarOwnerRealEnduranceService carOwnerRealEnduranceService;

    @Autowired
    private CarApiService carApiService;

    @Autowired
    @Qualifier("lightapp")
    private RedisClient stringRedisTemplate;

    private static final String DEFAULT_CONFIG_VALUE = "--";

    //获取北方城市
    @Autowired
    private NorthCityServiceImpl northCityServiceImpl;

    @Autowired
    private KouBeiListDal kouBeiListDal;

    @Autowired
    private BuyCarCommonService buyCarCommonService;

    @Autowired
    private SelectCarPortalDAL selectCarPortalDAL;

    private static String CAR_OWNER_KOUBEI_COMPARE_URL="http://koubei.api.sjz.autohome.com.cn/api/nev/car/serieslist";

    @Override
    public CarOwnerRealEnduranceOfBaseInfoDTO baseInfoV2OfCache(Integer cityId, Integer seriesId) {

        String redisBestKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_BASE_INFO_KEY, seriesId, cityId);
        String baseInfoJsonStr = stringRedisTemplate.get(redisBestKey);

        if (!StringUtils.isEmpty(baseInfoJsonStr)) {
            return JacksonHelper.deserialize(baseInfoJsonStr, CarOwnerRealEnduranceOfBaseInfoDTO.class);
        }

        CarOwnerRealEnduranceOfBaseInfoDTO baseInfoDTO = this.baseInfoV2(cityId, seriesId);

        stringRedisTemplate.set(redisBestKey, JacksonHelper.serialize(baseInfoDTO), 4, TimeUnit.HOURS);

        return baseInfoDTO;
    }

    @Override
    public CarOwnerRealEnduranceSpecInfoDTO specDataV2OfCache(Integer cityId, Integer seriesId, Integer specId,
                                                              Integer officialDriveRange, Integer dataType) {

        String redisBestKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_SPEC_INFO_V2_KEY, cityId, seriesId,
                specId, officialDriveRange, dataType);
        String specInfoJsonStr = stringRedisTemplate.get(redisBestKey);

//        if (!StringUtils.isEmpty(specInfoJsonStr)) {
//            return JacksonHelper.deserialize(specInfoJsonStr,CarOwnerRealEnduranceSpecInfoDTO.class);
//        }

        CarOwnerRealEnduranceSpecInfoDTO specInfo = this.specDataV2(cityId, seriesId, specId, officialDriveRange,
                dataType);

        stringRedisTemplate.set(redisBestKey, JacksonHelper.serialize(specInfo), 4, TimeUnit.HOURS);

        return specInfo;
    }

    @Override
    public CarOwnerRealEnduranceSpecInfoDTO specDataOfCache(Integer cityId, Integer seriesId, Integer specId) {

        String redisBestKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_SPEC_INFO_KEY, cityId, seriesId, specId);
        String specInfoJsonStr = stringRedisTemplate.get(redisBestKey);

        if (!StringUtils.isEmpty(specInfoJsonStr)) {
            return JacksonHelper.deserialize(specInfoJsonStr,CarOwnerRealEnduranceSpecInfoDTO.class);
        }

        CarOwnerRealEnduranceSpecInfoDTO specInfo = this.specData(cityId, seriesId, specId);

        stringRedisTemplate.set(redisBestKey, JacksonHelper.serialize(specInfo), 4, TimeUnit.HOURS);

        return specInfo;
    }

    @Override
    public SeriesBaseInfoV2.Energyconfigbeans getEnergyconfigbeansOfCache(int seriesid, int cityid) {

        String redisBestKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_ENERGY_CONFIG_BEANS, seriesid, cityid);
        String jsonStr = stringRedisTemplate.get(redisBestKey);

        if (!StringUtils.isEmpty(jsonStr)){
            return JacksonHelper.deserialize(jsonStr,SeriesBaseInfoV2.Energyconfigbeans.class);
        }

        SeriesBaseInfoDto seriesBaseInfoTask = this.getSeriesBaseInfoDto(cityid, seriesid);

        SeriesBaseInfoV2.Energyconfigbeans energyconfigbeans = this.getEnergyconfigbeans(seriesid, cityid,
                seriesBaseInfoTask);

        stringRedisTemplate.set(redisBestKey, JacksonHelper.serialize(energyconfigbeans), 4, TimeUnit.HOURS);

        return energyconfigbeans;
    }

    @Override
    public CarOwnerRealEnduranceOfBaseInfoDTO baseInfoV2(Integer cityId, Integer seriesId) {

        CarOwnerRealEnduranceOfBaseInfoDTO baseInfoDTO = new CarOwnerRealEnduranceOfBaseInfoDTO();
        CarOwnerRealEnduranceOfBaseInfoDTO.SeriesInfo seriesInfo = new CarOwnerRealEnduranceOfBaseInfoDTO.SeriesInfo();

        CarOwnerRealEnduranceOfKoubeiDTO beiLiData = this.getBeiLiData(cityId, seriesId, null);
        CarOwnerRealEnduranceOfKoubeiDTO kouBeiData = carOwnerRealEnduranceService.getCarOwnerRealEnduranceKouBei(null,
                seriesId, cityId);

        List<StatisSpecInfo> specInfoList = new ArrayList<>();
        List<StatisSpecInfo> specInfoListKB = new ArrayList<>();

        if (beiLiData != null && !CollectionUtils.isEmpty(beiLiData.getSpecInfoList())) {
            specInfoList = beiLiData.getStatisSpecInfo();
        }
        if (kouBeiData != null && !CollectionUtils.isEmpty(kouBeiData.getSpecInfoList())) {
            specInfoListKB = kouBeiData.getStatisSpecInfo();
        }


        StatisSpecInfo first = null;
        if (!specInfoList.isEmpty()) {
            first = specInfoList.get(0);
        }
        if (first == null && !specInfoListKB.isEmpty()) {
            first = specInfoListKB.get(0);
        }

        if (first == null) {

            SeriesConfigParamDto configConfig = getMaxRangeSpecConfig(seriesId, 0);
            if (Objects.nonNull(configConfig) && configConfig.getSpecId() != 0) {
                //SeriesFullInfo seriesFullInfo = redisLocalOps.getSeriesFullInfo(seriesId);
                CarSeriesList baseInfoBySeriesList = carApiService.getBaseInfoBySeriesList(seriesId.toString());
                seriesInfo.setSeriesid(seriesId);
                seriesInfo.setSeriesname(baseInfoBySeriesList.getList().get(0).getName());
                seriesInfo.setSerieslogo(baseInfoBySeriesList.getList().get(0).getSeriespnglogo());
                seriesInfo.setFcttext("指导价：");
                seriesInfo.setFctprice(getFormatPrice(baseInfoBySeriesList.getList().get(0).getMinprice(),
                        baseInfoBySeriesList.getList().get(0).getMaxprice()));
                seriesInfo.setImgrighttitle("和车友聊聊");
                seriesInfo.setImglist(getAutohomeHeaderRange());
                seriesInfo.setImglinkurl("autohome://club/topiclist?bbsid=&bbsname=&bbstype=c&seriesid=" + seriesId);
                seriesInfo.setMileagetitle("共1款续航版本");
                CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo tab = new CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo();
                tab.setDatatype(3);
                tab.setSpecid(configConfig.getSpecId());
                tab.setOfficialdriverange(configConfig.getMaxRange());
                tab.setTabname(configConfig.getMaxRange() + "km");
                seriesInfo.setTablist(Collections.singletonList(tab));
                baseInfoDTO.setSeriesinfo(seriesInfo);
                return baseInfoDTO;
            } else {
                return null;
            }

        }

        Integer maxPrice = specInfoList.stream().map(StatisSpecInfo::getMaxprice).max(Integer::compareTo).orElse(0);
        Integer minPrice = specInfoList.stream().map(StatisSpecInfo::getMinprice).min(Integer::compareTo)
                                       .orElse(Integer.MAX_VALUE);
        Integer maxPriceKB = specInfoListKB.stream().map(StatisSpecInfo::getMaxprice).max(Integer::compareTo).orElse(0);
        Integer minPriceKB = specInfoListKB.stream().map(StatisSpecInfo::getMinprice).min(Integer::compareTo)
                                           .orElse(Integer.MAX_VALUE);

        seriesInfo.setSeriesid(first.getSeriesId());
        seriesInfo.setSeriesname(first.getSeriesName());
        seriesInfo.setSerieslogo(first.getSeriesPngLogo());
        seriesInfo.setFcttext("指导价：");

        minPrice = minPrice == 0 ? Integer.MAX_VALUE : minPrice;
        maxPrice = maxPrice == 0 ? Integer.MIN_VALUE : maxPrice;
        minPriceKB = minPriceKB == 0 ? Integer.MAX_VALUE : minPriceKB;
        maxPriceKB = maxPriceKB == 0 ? Integer.MIN_VALUE : maxPriceKB;

        minPrice = Math.min(minPrice, minPriceKB);
        maxPrice = Math.max(maxPrice, maxPriceKB);

        seriesInfo.setFctprice(getFormatPrice(minPrice, maxPrice));
        seriesInfo.setImgrighttitle("和车友聊聊");
        seriesInfo.setImglist(getAutohomeHeaderRange());
        seriesInfo.setImglinkurl("autohome://club/topiclist?bbsid=&bbsname=&bbstype=c&seriesid=" + first.getSeriesId());

        Map<Integer, List<StatisSpecInfo>> officeDriveRangeGroup = specInfoList.stream().collect(
                Collectors.groupingBy(StatisSpecInfo::getOfficialDriveRange));
        Map<Integer, List<StatisSpecInfo>> officeDriveRangeGroupKB = specInfoListKB.stream().collect(
                Collectors.groupingBy(StatisSpecInfo::getOfficialDriveRange));
        List<CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo> tabs = new ArrayList<>();

        TreeMap<Integer, StatisSpecInfo> sortMap = new TreeMap<>();
        TreeMap<Integer, StatisSpecInfo> sortMapKB = new TreeMap<>();
        officeDriveRangeGroupKB.forEach((k, v) -> {
            StatisSpecInfo max = v.stream()
                                  .max(Comparator.comparing(StatisSpecInfo::getFactDriveRange)
                                                 .thenComparing(StatisSpecInfo::getSpecId)
                                      ).orElse(null);
            sortMapKB.put(k, max);
        });
        officeDriveRangeGroup.forEach((k, v) -> {
            //排除口碑已有的数据。
            if (!sortMapKB.containsKey(k)) {
                StatisSpecInfo max = v.stream()
                                      .max(Comparator.comparing(StatisSpecInfo::getFactDriveRange)
                                                     .thenComparing(StatisSpecInfo::getSpecId)
                                          ).orElse(null);
                sortMap.put(k, max);
            }
        });

        log.info("车主真实续航北理数据{}", JacksonHelper.serialize(sortMap));
        log.info("车主真实续航口碑数据{}", JacksonHelper.serialize(sortMapKB));

        sortMap.forEach((k, v) -> {
            if (k != null && v != null && v.getSpecId() != null && v.getSpecId() > 0) {
                CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo tab = new CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo();
                tab.setDatatype(1);
                tab.setOfficialdriverange(k);
                tab.setSpecid(v.getSpecId());
                tab.setTabname(k + "km");
                tabs.add(tab);
            } else {
                log.error("车主真实续航构建北理数据异常{}", JacksonHelper.serialize(sortMapKB));
            }
        });
        //口碑数据
        sortMapKB.forEach((k, v) -> {
            if (k != null && v != null && v.getSpecId() != null && v.getSpecId() > 0) {
                CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo tab = new CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo();
                tab.setDatatype(2);
                tab.setOfficialdriverange(k);
                tab.setSpecid(v.getSpecId());
                tab.setTabname(k + "km");
                tabs.add(tab);
            } else {
                log.error("车主真实续航构建口碑数据异常{}", JacksonHelper.serialize(sortMapKB));
            }
        });

        tabs.sort(
                Comparator.comparingInt(CarOwnerRealEnduranceOfBaseInfoDTO.SpecInfo::getOfficialdriverange).reversed());
        seriesInfo.setMileagetitle("共" + tabs.size() + "款续航版本");
        seriesInfo.setTablist(tabs);

        baseInfoDTO.setSeriesinfo(seriesInfo);

        return baseInfoDTO;
    }

    @Override
    public CarOwnerRealEnduranceOfKoubeiDTO getBeiLiData(Integer cityId, Integer seriesId, Integer specId) {

        String redisKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_BEI_LI_DATA, seriesId, cityId, specId);
        String redisJsonStr = stringRedisTemplate.get(redisKey);

        if (!StringUtils.isEmpty(redisJsonStr)) {
            return JacksonHelper.deserialize(redisJsonStr, CarOwnerRealEnduranceOfKoubeiDTO.class);
        }

        String url = "http://newvideo.autohome.com.cn/openapi/aggregationapi/battery/spec_fact_data";

        Map<String, Object> fields = MapBuilder.<String, Object>newInstance()
                                               .put("seriesId", seriesId)
                                               .put("cityId", cityId)
                                               .put("specId", specId == null ? "" : specId).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200) {
            log.error("getBeiLiData {} is error result: {}", url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getBeiLiData 源接口查询为null url:{}, 参数:{}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<JsonNode> returnJsonNode = JacksonHelper.deserialize(httpResult.getBody(),
                new TypeReference<ReturnValue<JsonNode>>() {
                });

        if (returnJsonNode == null
            || returnJsonNode.getResult() == null
            || !returnJsonNode.getResult().get("returncode").asText().equals("0")) {

            log.error("getBeiLiData {} is error result: {}", url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);

        }

        String jsonStr = JacksonHelper.serialize(returnJsonNode);
        ReturnValue returnValue = JacksonHelper.deserialize(jsonStr, ReturnValue.class);

        if (returnValue == null){
            log.warn("getBeiLiData 源接口查询为null url:{}, 参数:{}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0) {
            log.error("getBeiLiData {} is error result: {}", url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (returnValue.getResult() == null) {
            return null;
        }

        JsonNode jsonNode = returnJsonNode.getResult().get("result");

        String sonResultJson = JacksonHelper.serialize(jsonNode);
        CarOwnerRealEnduranceOfKoubeiDTO result = JacksonHelper.deserialize(sonResultJson,
                CarOwnerRealEnduranceOfKoubeiDTO.class);

        stringRedisTemplate.set(redisKey, JacksonHelper.serialize(result), 4, TimeUnit.HOURS);

        return result;
    }

    @Override
    public SeriesConfigInfoResult getConfigFromCarServicesBySeriesId(Integer seriesId) {

        String url = "http://carservice.autohome.com.cn/v3/carprice/spec_paramlistbyseriesid";

        Map<String, Object> fields = MapBuilder.<String, Object>newInstance()
                                               .put("seriesid", seriesId)
                                               .put("_appid", "app").build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200  ) {
            log.error("getBeiLiData {} is error result: {}", url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())){
            log.warn("getBeiLiData 源接口返回为null url:{}, param:{}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<SeriesConfigInfoResult> returnValue = JacksonHelper.deserialize(httpResult.getBody(),
                new TypeReference<ReturnValue<SeriesConfigInfoResult>>() {
                });

        if (returnValue == null){
            log.warn("getBeiLiData 源接口返回为null url:{}, param:{}",url,JacksonHelper.serialize(fields));
            return null;
        }

        if (returnValue.getReturncode() != 0) {
            log.error("getBeiLiData {} is error result: {}", url, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("getBeiLiData 源接口返回为null url:{}, param:{}",url,JacksonHelper.serialize(fields));
            return null;
        }

        return returnValue.getResult();
    }

    @Override
    public SameCarSeriesResult seriesListCompareInfos(Integer cityId, List<String> seriesIds) {

        String seriesIdsString = seriesIds.toString();
        String seriesIdsParamStr = seriesIdsString.replace("[", "").replace("]", "").replace(" ","");

        //?_appid=koubei&seriesids={seriesIds}&cityid={cityId}
        Map<String, Object> fields = MapBuilder.<String, Object>newInstance()
                                               .put("_appid", "koubei")
                                               .put("seriesIds", seriesIdsParamStr)
                                               .put("cityId", cityId).build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(CAR_OWNER_KOUBEI_COMPARE_URL, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200 ) {
            log.error("seriesListCompareInfos {} is error result: {}", CAR_OWNER_KOUBEI_COMPARE_URL, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("seriesListCompareInfos 源接口调用为null , url:{} , param:{}",CAR_OWNER_KOUBEI_COMPARE_URL,fields);
            return null;
        }

        ReturnValue<SameCarSeriesResult> returnValue = JacksonHelper.deserialize(httpResult.getBody(),
                new TypeReference<ReturnValue<SameCarSeriesResult>>() {
                });

        if (returnValue == null) {
            log.warn("seriesListCompareInfos 源接口调用为null , url:{} , param:{}",CAR_OWNER_KOUBEI_COMPARE_URL,fields);
            return null;
        }

        if (returnValue.getReturncode() != 0) {
            log.error("seriesListCompareInfos {} is error result: {}", CAR_OWNER_KOUBEI_COMPARE_URL, JacksonHelper.serialize(httpResult));
            throw new BusinessException("源接口调用失败", 1001);
        }

        if (returnValue.getResult() == null) {
            log.warn("seriesListCompareInfos 源接口调用为null , url:{} , param:{}",CAR_OWNER_KOUBEI_COMPARE_URL,fields);
            return null;
        }

        return returnValue.getResult();


    }

    @Override
    public CarOwnerRealEnduranceSpecInfoDTO specData(Integer cityId, Integer seriesId, Integer specId) {
        CarOwnerRealEnduranceSpecInfoDTO view = new CarOwnerRealEnduranceSpecInfoDTO();
        //车主基本信息
        CarOwnerRealEnduranceOfKoubeiDTO factBasicData = this.getBeiLiData(cityId, seriesId, specId);

        if (null == factBasicData) {
            throw new BusinessException("当前车主续航数据不存在", 0);
        }

        //车型列表,匹配当前车型
        CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo current = factBasicData.getSpecInfoList()
                                                                         .stream()
                                                                         .filter(e -> e.getSpecId().equals(specId))
                                                                         .findFirst().orElse(null);
        if (null == current) {
            throw new BusinessException("当前车型车主续航数据不存在", 0);
        }

        ResponseContent<SpecParam> specParam = buyCarCommonService.spec_parambyspecid("car", specId.toString());

        StatisSpecInfoDto currentSpecInfoDto = new StatisSpecInfoDto();
        currentSpecInfoDto.setSeriesId(current.getSeriesId());
        currentSpecInfoDto.setSeriesName(current.getSeriesName());
        currentSpecInfoDto.setSpecId(current.getSpecId());
        currentSpecInfoDto.setSpecName(current.getSpecName());
        currentSpecInfoDto.setOfficialDriveRange(current.getOfficialDriveRange());

        //四季数据获取
        TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData = factBasicData.getSeasonData();

        view.setSpecinfo(assembleSpecInfo(currentSpecInfoDto, seasonData, specParam, 1));

        //对比数据
        Pair<List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>, List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>> discuss = getDiscussList(
                seriesId);
        CarOwnerRealEnduranceSpecInfoDTO.Nav batteryLifeNav = assembleBatteryLifeNav(cityId, currentSpecInfoDto,
                seasonData,
                factBasicData.getNewSameCarList(), discuss, 1, true);

        //费用计算+车友讨论
        CarOwnerRealEnduranceSpecInfoDTO.Nav chargeNav = assembleChargeNav(currentSpecInfoDto, seasonData, discuss,
                specParam);
        view.setNavlist(Lists.newArrayList(batteryLifeNav, chargeNav));

        return view;
    }

    @Override
    public CarOwnerRealEnduranceSpecInfoDTO specDataV2(Integer cityId, Integer seriesId, Integer specId,
                                                       Integer officialDriveRange, Integer dataType) {

        CarOwnerRealEnduranceSpecInfoDTO view = new CarOwnerRealEnduranceSpecInfoDTO();
        //车主基本信息
        CarOwnerRealEnduranceOfKoubeiDTO factBasicData = null;

        if (dataType == 2) {
            factBasicData = carOwnerRealEnduranceService.getCarOwnerRealEnduranceKouBei(officialDriveRange, seriesId,
                    cityId);
        } else {
            factBasicData = this.getBeiLiData(cityId, seriesId, specId);
        }

        if (null == factBasicData) {
            throw new BusinessException("当前车主续航数据不存在", 0);
        }

        //ResponseContent<SpecParam> specParam = buyCarCommonService.spec_parambyspecid("car", specId.toString());
        ResponseContent<SpecParam> specParam =specParamBySpecIdOfCache(specId);

        if (specParam == null || specParam.getReturncode() != 0 || specParam.getResult() == null) {
            throw new BusinessException("获取当前车型参数信息失败", -1);
        }

        if (officialDriveRange == null || officialDriveRange == 0) {
            throw new BusinessException("参数错误", -1);
        }

        StatisSpecInfoDto currentSpecInfoDto = new StatisSpecInfoDto();
        currentSpecInfoDto.setSeriesId(specParam.getResult().getSeriesid());
        currentSpecInfoDto.setSeriesName(specParam.getResult().getSeriesname());
        currentSpecInfoDto.setSpecId(specParam.getResult().getSpecid());
        currentSpecInfoDto.setSpecName(specParam.getResult().getSpecname());
        currentSpecInfoDto.setOfficialDriveRange(officialDriveRange);
        boolean batteryLifeShowFlag = true;
        //四季数据获取
        TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData = factBasicData.getSeasonData();
//        if (CommonHelper.isTakeEffectVersion(pluginVersion, "11.57.0")) {
            // 查询是否可以展示车主续航
            batteryLifeShowFlag = getShowBatteryFlag(cityId, seriesId, officialDriveRange, seasonData);
//        }
        // 处理续航数据
        List<CarOwnerRealEnduranceOfKoubeiDTO.SameCar> newSameCarList = factBasicData.getNewSameCarList();
        if (isNorthCity(cityId)) {
            processFactDriveRange(currentSpecInfoDto, seasonData, newSameCarList, officialDriveRange, dataType, cityId);
        }

        view.setSpecinfo(assembleSpecInfo(currentSpecInfoDto, seasonData, specParam, dataType));
//
        //对比数据
        Pair<List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>, List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>> discuss = getDiscussList(
                cityId, seriesId, officialDriveRange);
        CarOwnerRealEnduranceSpecInfoDTO.Nav batteryLifeNav;
        batteryLifeNav = assembleBatteryLifeNav(cityId, currentSpecInfoDto, seasonData, newSameCarList, discuss,
                dataType, batteryLifeShowFlag);

        //费用计算+车友讨论
        CarOwnerRealEnduranceSpecInfoDTO.Nav chargeNav = assembleChargeNav(currentSpecInfoDto, seasonData, discuss,
                specParam);
        view.setNavlist(Lists.newArrayList(batteryLifeNav, chargeNav));

        return view;
    }

    @Override
    public SeriesBaseInfoV2.Energyconfigbeans getEnergyconfigbeans(int seriesid, int cityid, SeriesBaseInfoDto seriesDetailTask) {
        int factDriveRange = 0;
        double electricCharge = 0;
        double factChaSpeed = 0;
        String driveRangeLink = "";

        List<CompletableFuture> tasks = new ArrayList<>();

        SeriesInfoNewEnergyResult newEnergyResult = null;

        if (seriesDetailTask != null && seriesDetailTask.getSeriesInfoNewEnergy() != null) {
            newEnergyResult = seriesDetailTask.getSeriesInfoNewEnergy().join();
        }

        if (newEnergyResult != null
            && newEnergyResult.getResult() != null
            &&  newEnergyResult.getResult().getFactDriveRange() != null
            && 0 != newEnergyResult.getResult().getFactDriveRange()) {

            factDriveRange = newEnergyResult.getResult().getFactDriveRange();
            electricCharge = newEnergyResult.getResult().getElectricCharge();
            factChaSpeed = newEnergyResult.getResult().getFactChaSpeed();
            driveRangeLink = newEnergyResult.getResult().getDriveRangeLink();
        }

        //当前版本11.51.0不上了，源接口暂时无法提供数据。
        SeriesInfoNewEnergyKBResult newEnergyKBResult = null;
        if (seriesDetailTask != null && seriesDetailTask.getSeriesInfoNewEnergyKB() != null) {
            newEnergyKBResult = seriesDetailTask.getSeriesInfoNewEnergyKB().join();
        }
        if (newEnergyKBResult != null && newEnergyKBResult.getReturncode() == 0 && newEnergyKBResult.getResult() != null) {
            if (newEnergyKBResult.getResult().getFactDriveRange() > factDriveRange) {
                factDriveRange = newEnergyKBResult.getResult().getFactDriveRange();
                BigDecimal cost = BigDecimal.valueOf(newEnergyKBResult.getResult().getFactEnergy100())
                                            .multiply(BigDecimal.valueOf(0.68)).setScale(1, RoundingMode.HALF_DOWN);
                electricCharge = cost.doubleValue();
                driveRangeLink = newEnergyKBResult.getResult().getDriveRangeLink();
            }
        }

        SeriesBaseInfoV2.Energyconfigbeans energyconfigbeans = new SeriesBaseInfoV2.Energyconfigbeans();

        if (factDriveRange != 0 || electricCharge != 0 || factChaSpeed != 0) {
            String xh_title = factDriveRange > 0 ? factDriveRange + "km" : "暂无";
            String df_title = electricCharge > 0 ? electricCharge + "元" : "暂无";
            String kc_title = factChaSpeed > 0 ? factChaSpeed + "小时" : "暂无";

            energyconfigbeans.setTitle("车主真实数据");
            energyconfigbeans.setSubtitle("大家都在看");
            energyconfigbeans.setImglist(Arrays.asList(
                    "http://nfiles3.autohome.com.cn/zrjcpk10/<EMAIL>",
                    "http://nfiles3.autohome.com.cn/zrjcpk10/<EMAIL>",
                    "http://nfiles3.autohome.com.cn/zrjcpk10/<EMAIL>"));
            energyconfigbeans.setList(new ArrayList<>());

            SeriesBaseInfoV2.Configbean xh = new SeriesBaseInfoV2.Configbean();
            xh.setTitle(xh_title);
            xh.setSubtitle("车主续航");
            xh.setPointcolor("#25C9FF");
            energyconfigbeans.getList().add(xh);

            SeriesBaseInfoV2.Configbean kc = new SeriesBaseInfoV2.Configbean();
            kc.setTitle(kc_title);
            kc.setSubtitle("快充时间");
            kc.setPointcolor("#0096FF");
            energyconfigbeans.getList().add(kc);

            SeriesBaseInfoV2.Configbean df = new SeriesBaseInfoV2.Configbean();
            df.setTitle(df_title);
            df.setSubtitle("百公里电费");
            df.setPointcolor("#85F8FF");
            energyconfigbeans.getList().add(df);

            SeriesBaseInfoV2.PvItem pvItem = new SeriesBaseInfoV2.PvItem();
            HashMap<String, String> argvs = new HashMap<>();
            pvItem.setArgvs(argvs);
            pvItem.getArgvs().put("seriesid", seriesid + "");
            pvItem.getArgvs().put("cityid", cityid + "");

            SeriesBaseInfoV2.PvItem.Click click = new SeriesBaseInfoV2.PvItem.Click();
            pvItem.setClick(click);
            pvItem.getClick().setEventid("car_series_energy_new_config_click");

            SeriesBaseInfoV2.PvItem.Show show = new SeriesBaseInfoV2.PvItem.Show();
            pvItem.setShow(show);
            pvItem.getShow().setEventid("car_series_energy_new_config_show");
            energyconfigbeans.setPvitem(pvItem);

            energyconfigbeans.setLinkurl(driveRangeLink);
        }

        return energyconfigbeans;
    }

    private SeriesBaseInfoDto getSeriesBaseInfoDto(Integer cityid, Integer seriesid) {

        SeriesBaseInfoDto result = new SeriesBaseInfoDto();

        CompletableFuture<SeriesInfoNewEnergyKBResult> seriesInfoNewEnergyKB = CompletableFuture.supplyAsync(
                                                                                                        () -> selectCarPortalDAL.getSeriesInfoNewEnergyKBResult(cityid, seriesid))
                                                                                                .exceptionally(e -> {
                                                                                                    log.error(
                                                                                                            "getSeriesInfoNewEnergyResult error",
                                                                                                            e);
                                                                                                    return null;
                                                                                                });
        result.setSeriesInfoNewEnergyKB(seriesInfoNewEnergyKB);

        CompletableFuture seriesInfoNewEnergy = CompletableFuture.supplyAsync(() -> seriesFactData(cityid, seriesid))
                                                                 .exceptionally(e -> {
                                                                     log.error("getSeriesInfoNewEnergyResult error", e);
                                                                     return null;
                                                                 });
        result.setSeriesInfoNewEnergy(seriesInfoNewEnergy);

        return result;
    }


    public SeriesConfigParamDto getMaxRangeSpecConfig(int seriesId, int specId) {

        String redisKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_MAX_RANGE_SPEC_DATA, seriesId, specId);
        String redisJsonStr = stringRedisTemplate.get(redisKey);

        if (!StringUtils.isEmpty(redisJsonStr)) {
            return JacksonHelper.deserialize(redisJsonStr, SeriesConfigParamDto.class);
        }

        SeriesConfigParamDto dto = new SeriesConfigParamDto();
        // 获取参配信息
        SeriesConfigInfoResult config = this.getConfigFromCarServicesBySeriesId(seriesId);
        List<SeriesConfigInfoResult.ParamtypeitemsDTO> paramTypeItems = config.getParamtypeitems();
        Optional<SeriesConfigInfoResult.ParamtypeitemsDTO> basicParamOptional = paramTypeItems.stream()
                                                                                              .filter(x -> x.getName()
                                                                                                            .equals("基本参数"))
                                                                                              .findFirst();
        Optional<SeriesConfigInfoResult.ParamtypeitemsDTO> electricMotorOptional = paramTypeItems.stream()
                                                                                                 .filter(x -> x.getName()
                                                                                                               .equals("电动机"))
                                                                                                 .findFirst();
        Map<Integer, List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO>> electricMotorParamMap;
        electricMotorParamMap = electricMotorOptional.map(
                                                             paramtypeitemsDTO -> paramtypeitemsDTO.getParamitems().stream().collect(
                                                                     Collectors.toMap(SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO::getId,
                                                                             SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO::getValueitems)))
                                                     .orElse(Collections.emptyMap());
        if (basicParamOptional.isPresent()) {
            Map<Integer, List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO>> paramMap = basicParamOptional.get()
                                                                                                                                  .getParamitems()
                                                                                                                                  .stream()
                                                                                                                                  .collect(
                                                                                                                                          Collectors.toMap(
                                                                                                                                                  SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO::getId,
                                                                                                                                                  SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO::getValueitems));
            if (CollUtil.isNotEmpty(paramMap)) {
                // 快充时间
                // 百公里电耗
                SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO specIdAndRange = getSpecIdAndRange(
                        paramMap, specId);
                if (Objects.nonNull(specIdAndRange)) {
                    specId = specIdAndRange.getSpecid();
                    // 车型名称
                    dto.setSpecName(getSpecConfigValue(specId, paramMap.get(ConfigInfoEnum.SPEC_NAME.getId()),
                            CharSequenceUtil.EMPTY));
                    // 获取快充时间
                    dto.setFastChargeTime(
                            getSpecConfigValue(specId, paramMap.get(ConfigInfoEnum.FAST_CHARGE_TIME.getId()),
                                    DEFAULT_CONFIG_VALUE));
                    // 慢充时间
                    dto.setSlowChargeTime(
                            getSpecConfigValue(specId, paramMap.get(ConfigInfoEnum.SLOW_CHARGE_TIME.getId()),
                                    DEFAULT_CONFIG_VALUE));
                    // 车型级别
                    dto.setLevelName(getSpecConfigValue(specId, paramMap.get(ConfigInfoEnum.LEVEL.getId()),
                            DEFAULT_CONFIG_VALUE));
                    // 计算百公里电费
                    String electricityUse = getSpecConfigValue(specId,
                            electricMotorParamMap.get(ConfigInfoEnum.HUNDRED_KM_POWER_CONSUMPTION.getId()),
                            CharSequenceUtil.EMPTY);
                    if (CharSequenceUtil.isNotEmpty(electricityUse) && !electricityUse.equals("-")) {
                        dto.setHundredsElectricityPriceUse(BigDecimal.valueOf(Double.parseDouble(electricityUse))
                                                                     .multiply(BigDecimal.valueOf(0.68))
                                                                     .setScale(1, RoundingMode.HALF_DOWN).toString());
                        dto.setHundredsElectricityUse(Double.parseDouble(electricityUse));
                    } else {
                        dto.setHundredsElectricityPriceUse(DEFAULT_CONFIG_VALUE);
                        dto.setHundredsElectricityUse(0d);
                    }
                    dto.setSpecId(specIdAndRange.getSpecid());
                    dto.setMaxRange(Integer.parseInt(specIdAndRange.getValue()));
                }
            }

        }

        stringRedisTemplate.set(redisKey, JacksonHelper.serialize(dto), 4, TimeUnit.HOURS);

        return dto;
    }

    private String getFormatPrice(Integer minPrice, Integer maxPrice) {
        String minText = "--";
        String maxText = "--";
        if (0 != minPrice) {
            BigDecimal min = BigDecimal.valueOf(minPrice).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_DOWN);
            minText = min.toString();
        }
        if (0 != maxPrice) {
            BigDecimal max = BigDecimal.valueOf(maxPrice).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_DOWN);
            maxText = max.toString();
        }
        return minText + "-" + maxText + "万";
    }

    private static List<String> getAutohomeHeaderRange() {
        List<String> allHeaderInfos = new ArrayList<>();
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/1.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/1101.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/2.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/3.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/5.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/6.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/7.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/8.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/10.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/11.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/12.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/13.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/14.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/15.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/16.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/17.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/18.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/19.jpg");
        allHeaderInfos.add("http://nfiles3.autohome.com.cn/zrjcpk10/autoheaer/22.jpg");
        Collections.shuffle(allHeaderInfos);
        return allHeaderInfos.subList(0, 3);
    }

    /**
     * 根据优先级选出最大续航的车型和续航数
     *
     * @param paramMap 参配Map
     * @return 最大续航的车型和续航数
     */
    private SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO getSpecIdAndRange(
            Map<Integer, List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO>> paramMap,
            int specId) {
        List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO> cltc = paramMap.get(
                ConfigInfoEnum.CLTC.getId());
        List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO> wltc = paramMap.get(
                ConfigInfoEnum.WLTC.getId());
        List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO> nedc = paramMap.get(
                ConfigInfoEnum.NEDC.getId());
        SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO result = null;
        if (CollUtil.isNotEmpty(cltc) && CollUtil.isNotEmpty(cltc = (cltc.stream().filter(x -> !x.getValue().equals(
                StrPool.DASHED)).collect(Collectors.toList())))) {
            result = getMaxRangeSpecInfo(cltc, specId);
            if (Objects.nonNull(result)) {
                return result;
            }
        }
        if (CollUtil.isNotEmpty(wltc) && CollUtil.isNotEmpty(
                wltc = (wltc.stream().filter(x -> !x.getValue().equals(StrPool.DASHED))
                            .collect(Collectors.toList())))) {
            result = getMaxRangeSpecInfo(wltc, specId);
            if (Objects.nonNull(result)) {
                return result;
            }
        }
        if (CollUtil.isNotEmpty(nedc) && CollUtil.isNotEmpty(
                nedc = (nedc.stream().filter(x -> !x.getValue().equals(StrPool.DASHED))
                            .collect(Collectors.toList())))) {
            result = getMaxRangeSpecInfo(nedc, specId);
            if (Objects.nonNull(result)) {
                return result;
            }
        }
        return result;
    }

    private SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO getMaxRangeSpecInfo(
            List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO> list, int specId) {
        if (specId != 0) {
            return list.stream().filter(x -> x.getSpecid().equals(specId)).findFirst().orElse(null);
        } else {
            list.sort(Comparator.comparingInt(x -> Integer.parseInt(x.getValue())));
            return list.get(list.size() - 1);
        }
    }

    /**
     * 获取特定车型的
     *
     * @param specId    车型ID
     * @param paramList 参数List
     * @return 当前车型的参数值
     */
    private String getSpecConfigValue(int specId,
                                      List<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO> paramList,
                                      String defaultValue) {
        if (CollUtil.isNotEmpty(paramList)) {
            Optional<SeriesConfigInfoResult.ParamtypeitemsDTO.ParamitemsDTO.ValueitemsDTO> dtoOptional = paramList.stream()
                                                                                                                  .filter(x -> x.getSpecid()
                                                                                                                                .equals(specId))
                                                                                                                  .findFirst();
            return dtoOptional.isPresent() ? dtoOptional.get().getValue() : defaultValue;
        }
        return defaultValue;
    }

    /**
     * 判断是否为北方城市
     *
     * @param cityId 城市id
     * @return 是否为北方城市
     */
    public boolean isNorthCity(Integer cityId) {
        return northCityServiceImpl.getNorthCityMap().containsKey(cityId);
    }

    /**
     * 处理续航里程
     *
     * @param current            当前车系信息
     * @param seasonData         四季数据
     * @param newSameCarList     新能源对比车list
     * @param officialDriveRange 官方续航
     * @param dataType           数据类型
     * @param cityId             城市id
     */
    private void processFactDriveRange(StatisSpecInfoDto current,
                                       TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData,
                                       List<CarOwnerRealEnduranceOfKoubeiDTO.SameCar> newSameCarList,
                                       int officialDriveRange, int dataType, int cityId) {
        if (isNorthCity(cityId)) {
            Map<Integer, CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> beiLiMap = Collections.emptyMap();
            Integer curSummerVal = Integer.MAX_VALUE;
            Integer curWinterVal = 0;
            if (CollUtil.isNotEmpty(seasonData)) {
                if (seasonData.containsKey(SeasonEnum.SUMMER.getCnName())) {
                    curSummerVal = seasonData.get(SeasonEnum.SUMMER.getCnName()).getFactDriveRange();
                }
                if (seasonData.containsKey(SeasonEnum.WINTER.getCnName())) {
                    curWinterVal = seasonData.get(SeasonEnum.WINTER.getCnName()).getFactDriveRange();
                }
            }
            if (dataType == 2) {
                List<CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> beiLiCollect = new ArrayList<>(
                        newSameCarList.size());
                List<List<Integer>> seriesSpecIdList = newSameCarList.stream().map(x -> Arrays.asList(x.getSeriesId(),
                        x.getSpecId())).collect(Collectors.toList());
                seriesSpecIdList.add(Arrays.asList(current.getSeriesId(), current.getSpecId()));
                seriesSpecIdList.parallelStream().forEach(list -> {
                    // 获取北理数据
                    BaseModel<CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> res = compareInfo(cityId, list.get(0),
                            list.get(1));
                    if (Objects.nonNull(res) && Objects.nonNull(res.getResult())) {
                        beiLiCollect.add(res.getResult());
                    }
                });
                beiLiMap = beiLiCollect.stream().collect(
                        Collectors.toMap(CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol::getSpecid, x -> x));
                CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol currentBeiLiDto = beiLiMap.get(current.getSpecId());
                // 处理当前车型续航数据
                if (CollUtil.isNotEmpty(seasonData)) {
                    if (curSummerVal != 0 && curWinterVal > curSummerVal) {
                        if (Objects.nonNull(currentBeiLiDto) && CollUtil.isNotEmpty(
                                currentBeiLiDto.getList()) && Objects.nonNull(currentBeiLiDto.getList().get(3))) {
                            // 获取北理数据
                            CarOwnerRealEnduranceSpecInfoDTO.TableColValue beiLiWinterDto = currentBeiLiDto.getList()
                                                                                                           .get(3);
                            if (!"——".equals(beiLiWinterDto.getName())) {
                                int curBeiLiWinterVal = Integer.parseInt(
                                        beiLiWinterDto.getName().replaceAll("km", CharSequenceUtil.EMPTY));
                                if (curBeiLiWinterVal < curSummerVal) {
                                    curWinterVal = curBeiLiWinterVal;
                                }
                            }
                        }
                        if (curWinterVal <= curSummerVal) {
                            // 将冬季续航数据替换为北理数据
                            seasonData.get(SeasonEnum.WINTER.getCnName()).setFactDriveRange(curWinterVal);
                        } else {
                            // 移除冬季数据
                            seasonData.get(SeasonEnum.WINTER.getCnName()).setFactDriveRange(0);
                        }
                    }
                }
            }
            if (curSummerVal != 0 && curWinterVal > curSummerVal) {
                // 移除冬季数据
                seasonData.get(SeasonEnum.WINTER.getCnName()).setFactDriveRange(0);
            }
            // 过滤真实续航 > 1500 || < 10  || 真实续航>官方续航 的数据
            seasonData.forEach((season, dataDto) -> {
                if (Objects.nonNull(
                        dataDto) && (dataDto.getFactDriveRange() > 1500 || dataDto.getFactDriveRange() < 10 || dataDto.getFactDriveRange() > officialDriveRange)) {
                    seasonData.get(season).setFactDriveRange(0);
                }
            });


            // 处理同级车续航数据
            for (CarOwnerRealEnduranceOfKoubeiDTO.SameCar newSameCarDto : newSameCarList) {
                List<CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason> sameCarSeasonList = newSameCarDto.getSameCarSeasonList();
                if (CollUtil.isNotEmpty(sameCarSeasonList)) {
                    Map<String, CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason> seasonDataMap = sameCarSeasonList.stream()
                                                                                                                 .collect(
                                                                                                                         Collectors.toMap(
                                                                                                                                 CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason::getSeason,
                                                                                                                                 x -> x));
                    Integer summerVal = Integer.MAX_VALUE;
                    if (seasonDataMap.containsKey(SeasonEnum.SUMMER.getCnName())) {
                        summerVal = seasonDataMap.get(SeasonEnum.SUMMER.getCnName()).getFactDriveRange();
                    }
                    Integer winterVal = 0;
                    if (seasonDataMap.containsKey(SeasonEnum.WINTER.getCnName())) {
                        winterVal = seasonDataMap.get(SeasonEnum.WINTER.getCnName()).getFactDriveRange();
                    }
                    if (winterVal != 0 && winterVal > summerVal) {
                        if (beiLiMap.containsKey(newSameCarDto.getSpecId())) {
                            CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol beiLiDto = beiLiMap.get(
                                    newSameCarDto.getSpecId());
                            if (Objects.nonNull(beiLiDto) && CollUtil.isNotEmpty(beiLiDto.getList()) && Objects.nonNull(
                                    beiLiDto.getList().get(3))) {
                                // 获取北理数据
                                CarOwnerRealEnduranceSpecInfoDTO.TableColValue beiLiWinterDto = beiLiDto.getList()
                                                                                                        .get(3);
                                if (!"——".equals(beiLiWinterDto.getName())) {
                                    Integer beiLiWinterVal = Integer.parseInt(
                                            beiLiWinterDto.getName().replaceAll("——", CharSequenceUtil.EMPTY));
                                    if (beiLiWinterVal <= summerVal) {
                                        // 将冬季续航数据替换为北理数据
                                        sameCarSeasonList.get(sameCarSeasonList.size() - 1)
                                                         .setFactDriveRange(beiLiWinterVal);
                                    } else {
                                        // 移除冬季数据
                                        sameCarSeasonList.remove(sameCarSeasonList.size() - 1);
                                    }
                                }
                            }
                        } else {
                            // 移除冬季数据
                            sameCarSeasonList.remove(sameCarSeasonList.size() - 1);
                        }
                    }
                    sameCarSeasonList = sameCarSeasonList.stream()
                                                         .filter(x -> x.getFactDriveRange() < newSameCarDto.getOfficialDriveRange() && x.getFactDriveRange() >= 10 && x.getFactDriveRange() <= 1500)
                                                         .collect(Collectors.toList());
                    newSameCarDto.setSameCarSeasonList(sameCarSeasonList);
                }
            }

        }
    }

    /**
     * 组装规格信息
     *
     * @param current
     * @param seasonData
     * @param info
     * @param dataType   1:北理，2：口碑
     * @return
     */
    private CarOwnerRealEnduranceSpecInfoDTO.SpecInfo assembleSpecInfo(StatisSpecInfoDto current,
                                                                       TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData,
                                                                       ResponseContent<SpecParam> info,
                                                                       Integer dataType
                                                                      ) {
        CarOwnerRealEnduranceSpecInfoDTO.SpecInfo specInfo = new CarOwnerRealEnduranceSpecInfoDTO.SpecInfo();
        specInfo.setSpecid(current.getSpecId());
        specInfo.setSubname(current.getOfficialDriveRange() + "km");
        String bussinessName = dataType == 1 ? "的车主真实反馈" : dataType == 2 ? "的车主续航口碑分享" : "的车主真实反馈";
        specInfo.setTabtips("本数据均来自当前城市为");
        double factChaSpeed = Double.MAX_VALUE;
        double factEnergy100 = Double.MAX_VALUE;
        int factDriveRange = 0;

        if (seasonData == null || seasonData.isEmpty()) {
            specInfo.setTabtipssuffix(bussinessName);
            return specInfo;
        }

        if (seasonData != null) {
            for (CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo dto : seasonData.values()) {
                factEnergy100 = Math.min(factEnergy100, dto.getFactEnergy100());
                factDriveRange = Math.max(factDriveRange, dto.getFactDriveRange());
            }
        }

        if (seasonData != null && !seasonData.isEmpty()) {
            for (CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo dto : seasonData.values()) {
                if (dto.getFactChaSpeed() != 0.0D) {
                    factChaSpeed = Math.min(factChaSpeed, dto.getFactChaSpeed());
                }
            }
        }

        specInfo.setTabtipssuffix(bussinessName);

        String fastChargeTime = "--";

        if (factChaSpeed == Double.MAX_VALUE) {
            if (null != info && null != info.getResult()
                && !"0".equals(info.getResult().getFastchargetime())) {
                fastChargeTime = info.getResult().getFastchargetime();
            }
        } else if (factChaSpeed != 0.0D) {
            fastChargeTime = factChaSpeed + "";
        }
        String driveRange = 0 == factDriveRange ? DEFAULT_CONFIG_VALUE : factDriveRange + "";
        CarOwnerRealEnduranceSpecInfoDTO.SubInfo factDriveRangeTab =
                new CarOwnerRealEnduranceSpecInfoDTO.SubInfo(driveRange, "km", "车主续航",
                        DEFAULT_CONFIG_VALUE.equals(driveRange) ? CharSequenceUtil.EMPTY : "#05D1D1");
        CarOwnerRealEnduranceSpecInfoDTO.SubInfo officeDriveRangeTab =
                new CarOwnerRealEnduranceSpecInfoDTO.SubInfo(current.getOfficialDriveRange() + "", "km", "官方续航",
                        "");
        CarOwnerRealEnduranceSpecInfoDTO.SubInfo factChaSpeedTab =
                new CarOwnerRealEnduranceSpecInfoDTO.SubInfo(fastChargeTime,
                        fastChargeTime.equals("--") ? "" : "h", "快充时间", "");

        BigDecimal cost = BigDecimal.valueOf(factEnergy100).multiply(BigDecimal.valueOf(0.68))
                                    .setScale(1, RoundingMode.HALF_DOWN);
        CarOwnerRealEnduranceSpecInfoDTO.SubInfo factCost100Tab =
                new CarOwnerRealEnduranceSpecInfoDTO.SubInfo(
                        cost.equals(BigDecimal.valueOf(0.0)) ? "--" : cost.toString(),
                        cost.equals(BigDecimal.valueOf(0.0)) ? "" : "元", "百公里电费", "");
        specInfo.setSublist(
                Lists.newArrayList(factDriveRangeTab, officeDriveRangeTab, factChaSpeedTab, factCost100Tab));

        return specInfo;
    }

    private Pair<List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>, List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>> getDiscussList(
            Integer seriesId) {
        List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem> batteryLifeDiscuss = null;
        List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem> satisfiedDiscuss = null;
        KoubeiInfoRes batteryLife;
        KoubeiInfoRes satisfied;

        Supplier<KoubeiInfoRes> getSatisfiedFuc = () -> kouBeiListDal.list(seriesId, 1, 4, 1);
        Supplier<KoubeiInfoRes> getBatteryLifeFuc = () -> kouBeiListDal.list(seriesId, 6, 2, 1);


        CompletableFuture<KoubeiInfoRes> satisfiedFuture = CompletableFuture.supplyAsync(getSatisfiedFuc);
        //口碑车主分享
        CompletableFuture<KoubeiInfoRes> batteryLifeFuture = CompletableFuture.supplyAsync(getBatteryLifeFuc);
        try {
            batteryLife = batteryLifeFuture.get(5000, TimeUnit.MILLISECONDS);
            if (null != batteryLife.getResult()) {
                batteryLifeDiscuss = convertDiscussModel(batteryLife.getResult().getList());
            }
        } catch (Exception e) {
            log.error("获取续航口碑列表失败，seriesId:{}", seriesId, e);
        }
        try {
            satisfied = satisfiedFuture.get(5000, TimeUnit.MILLISECONDS);
            if (null != satisfied.getResult()) {
                satisfiedDiscuss = convertDiscussModel(satisfied.getResult().getList());
            }
        } catch (Exception e) {
            log.error("获取满意口碑列表失败，seriesId:{}", seriesId, e);
        }
        return Pair.of(batteryLifeDiscuss, satisfiedDiscuss);
    }

    private Pair<List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>, List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>> getDiscussList(
            Integer cityId, Integer seriesId, Integer range) {
        List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem> batteryLifeDiscuss = null;
        List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem> satisfiedDiscuss = null;
        KoubeiInfoShareRes batteryLife;
        KoubeiInfoShareRes satisfied;

        Supplier<KoubeiInfoShareRes> satisfiedFuc = () -> kouBeiListDal.list_series_nev_list(cityId, seriesId, range, 1,
                4);
        Supplier<KoubeiInfoShareRes> batteryLifeFuc = () -> kouBeiListDal.list_series_nev_list(cityId, seriesId, range,
                6, 2);

        CompletableFuture<KoubeiInfoShareRes> satisfiedFuture = CompletableFuture.supplyAsync(satisfiedFuc);
        //口碑车主分享
        CompletableFuture<KoubeiInfoShareRes> batteryLifeFuture = CompletableFuture.supplyAsync(batteryLifeFuc);
        try {
            batteryLife = batteryLifeFuture.get(5000, TimeUnit.MILLISECONDS);
            if (null != batteryLife && null != batteryLife.getResult()) {
                batteryLifeDiscuss = convertDiscussShareModel(batteryLife.getResult().getList());
            }
        } catch (Exception e) {
            log.error("获取续航口碑列表失败，seriesId:{}", seriesId, e);
        }
        try {
            satisfied = satisfiedFuture.get(5000, TimeUnit.MILLISECONDS);
            if (null != satisfied && null != satisfied.getResult()) {
                satisfiedDiscuss = convertDiscussShareModel(satisfied.getResult().getList());
            }
        } catch (Exception e) {
            log.error("获取满意口碑列表失败，seriesId:{}", seriesId, e);
        }
        return Pair.of(batteryLifeDiscuss, satisfiedDiscuss);
    }

    /**
     * 车主内容
     *
     * @param cityId
     * @param current
     * @param seasonData
     * @param discuss
     * @param batteryLifeShowFlag
     * @return
     */
    private CarOwnerRealEnduranceSpecInfoDTO.Nav assembleBatteryLifeNav(Integer cityId, StatisSpecInfoDto current,
                                                                        TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData,
                                                                        List<CarOwnerRealEnduranceOfKoubeiDTO.SameCar> newSameCarList,
                                                                        Pair<List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>, List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>> discuss,
                                                                        Integer dataType, boolean batteryLifeShowFlag) {
        CarOwnerRealEnduranceSpecInfoDTO.Nav nav = new CarOwnerRealEnduranceSpecInfoDTO.Nav();
        nav.setNavkey("mileage");
        nav.setNavid(0);
        nav.setNavtitle("续航");
        //东、夏数据
        CarOwnerRealEnduranceSpecInfoDTO.SameSpecBase sameSpecInfo;

        RegionDto regionInfo = CityUtils.getRegionInfo(cityId);
        if (batteryLifeShowFlag && Objects.nonNull(regionInfo)) {
//            if (CommonHelper.isTakeEffectVersion(pluginVersion, "11.57.0")) {
                sameSpecInfo = assembleSameSpecInfoNew(current, seasonData, cityId);
//            } else {
//            sameSpecInfo = assembleSameSpecInfo(current, seasonData);
//            }
            nav.setSamespecinfo(sameSpecInfo);
            //同级车型head
            CarOwnerRealEnduranceSpecInfoDTO.CompareInfo compareInfo;
            if (dataType == 3) {
                compareInfo = new CarOwnerRealEnduranceSpecInfoDTO.CompareInfo();
            } else {
                compareInfo = createBaseCompareInfo(cityId);
            }
            //组装对比车型
            assembleCompareInfo(compareInfo, current, seasonData, newSameCarList);
            nav.setCompareinfo(compareInfo);
        }
        //续航衰减趋势(11.52.0之后就不要续航衰减趋势节点了，包括11.52.0版本)
//        if (StringUtils.isEmpty(pluginVersion) || !CommonHelper.isTakeEffectVersion(pluginVersion, "11.52.0")) {
//            SpecFactDataView.ChartInfo chartInfo = assembleChartInfo(current, seasonData);
//            nav.setChartInfo(chartInfo);
//        }

        CarOwnerRealEnduranceSpecInfoDTO.DiscussInfo discussInfo = new CarOwnerRealEnduranceSpecInfoDTO.DiscussInfo();
        discussInfo.setBigtitle("口碑车主续航分享");
        discussInfo.setList(discuss.getLeft());
        nav.setDiscussinfo(discussInfo);
        CarOwnerRealEnduranceSpecInfoDTO.MoreInfo moreInfo = new CarOwnerRealEnduranceSpecInfoDTO.MoreInfo();
        moreInfo.setMoretitle("查看更多车主口碑");
        moreInfo.setMorelinkurl(
                SchemeUtils.getKouBeiListScheme(current.getSeriesId(), current.getSeriesName(), null, null, 12, 48));
        nav.setMoreinfo(moreInfo);
        return nav;
    }



    /**
     * 费用计算+车友讨论
     *
     * @param current
     * @param seasonData
     * @param discuss
     * @param res
     * @return
     */
    private CarOwnerRealEnduranceSpecInfoDTO.Nav assembleChargeNav(StatisSpecInfoDto current,
                                                                   TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData,
                                                                   Pair<List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>, List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem>> discuss,
                                                                   ResponseContent<SpecParam> res) {
        CarOwnerRealEnduranceSpecInfoDTO.Nav nav = new CarOwnerRealEnduranceSpecInfoDTO.Nav();
        nav.setNavkey("charge");
        nav.setNavtitle("充电");
        nav.setNavid(1);
        CarOwnerRealEnduranceSpecInfoDTO.ChargeInfo chargeInfo = new CarOwnerRealEnduranceSpecInfoDTO.ChargeInfo();
        chargeInfo.setBigtitle(current.getSpecName() + "充电时长");
//        if (CommonHelper.isTakeEffectVersion(pluginVersion, "11.58.0")) {
            chargeInfo.setBigtitlelinkurl("autohome://car/specmain?specid=" + current.getSpecId());
//        } else {
//        chargeInfo.setPoptitle("季节选择");
//        }
        String slowChargeTime;
        String fastChargeTime;
        int isSuv = 0;
        if (null != res && null != res.getResult()) {
            if (!res.getResult().getSlowchargetime().equals("0")) {
                slowChargeTime = res.getResult().getSlowchargetime();
            } else {
                slowChargeTime = "--";
            }
            if (!res.getResult().getFastchargetime().equals("0")) {
                fastChargeTime = res.getResult().getFastchargetime();
            } else {
                fastChargeTime = "--";
            }
        } else {
            slowChargeTime = "--";
            fastChargeTime = "--";
        }
        if (null != res && null != res.getResult() && null != res.getResult().getLevelname()) {
            isSuv = res.getResult().getLevelname().contains("SUV") ? 1 : 0;
        }
        List<CarOwnerRealEnduranceSpecInfoDTO.SeasonItem> seasonList = new ArrayList<>();
        List<CarOwnerRealEnduranceSpecInfoDTO.EnergyCost> energyCostList = new ArrayList<>();
        if (Objects.nonNull(seasonData)) {
            LinkedHashMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> sortSeasonMap = convertSortMap(
                    seasonData);
            sortSeasonMap.forEach((k, v) -> {
                String seasonName = k + "季";
                CarOwnerRealEnduranceSpecInfoDTO.SeasonItem item = new CarOwnerRealEnduranceSpecInfoDTO.SeasonItem();
                item.setSeasonname(seasonName);
                CarOwnerRealEnduranceSpecInfoDTO.Charge slow = new CarOwnerRealEnduranceSpecInfoDTO.
                        Charge(slowChargeTime, slowChargeTime.equals("--") ? "" : "h", "慢充", "0%-100%");
                CarOwnerRealEnduranceSpecInfoDTO.Charge quick = new CarOwnerRealEnduranceSpecInfoDTO.
                        Charge(fastChargeTime, fastChargeTime.equals("--") ? "" : "h", "快充", "20%-100%");
                if (null != v && null != v.getFactChaSpeed() && !v.getFactChaSpeed().equals(0.0D)) {
                    quick.setTime(v.getFactChaSpeed() + "");
                    quick.setSuffix("h");
                }
                item.setChargeList(Lists.newArrayList(slow, quick));
                seasonList.add(item);
                CarOwnerRealEnduranceSpecInfoDTO.EnergyCost energyCost = new CarOwnerRealEnduranceSpecInfoDTO.EnergyCost();
                energyCost.setSeasonname(seasonName);
                if (null != v && null != v.getFactEnergy100() && 0 != v.getFactDriveRange()) {
                    energyCost.setNenghao100(v.getFactEnergy100() > 0 ? v.getFactEnergy100() : null);
                    energyCost.setNormalmileage(v.getFactDriveRange());
                    energyCostList.add(energyCost);
                }
            });
//            if (CommonHelper.isTakeEffectVersion(pluginVersion, "11.58.0")) {
            SeasonEnum currentSeason = SeasonUtil.getSeasonByDate(new Date());
            if (Objects.nonNull(currentSeason)) {
                CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo currentSeasonData = sortSeasonMap.get(
                        currentSeason.getCnName());
                List<CarOwnerRealEnduranceSpecInfoDTO.Charge> chargeTimeNormal = getChargeTimeNormal(currentSeasonData,
                        slowChargeTime, fastChargeTime);
                chargeInfo.setChargetimes(chargeTimeNormal);
            }
//            }
        } else {
            SeriesConfigParamDto configParamDto = getMaxRangeSpecConfig(current.getSeriesId(), current.getSpecId());
            if (CollUtil.isEmpty(energyCostList) && configParamDto.getHundredsElectricityUse() > 0) {
                SeasonEnum seasonByDate = SeasonUtil.getSeasonByDate(new Date());
                CarOwnerRealEnduranceSpecInfoDTO.EnergyCost energyCost = new CarOwnerRealEnduranceSpecInfoDTO.EnergyCost();
                energyCost.setNenghao100(configParamDto.getHundredsElectricityUse());
                energyCost.setSeasonname(seasonByDate.getCnName());
                // 使用官方续航打底
                energyCost.setNormalmileage(current.getOfficialDriveRange());
                energyCostList.add(energyCost);
            }
        }
        if (CollUtil.isNotEmpty(energyCostList)) {
            double chargePrice;
//            if (CommonHelper.isTakeEffectVersion(pluginVersion, "11.58.0")) {
            chargePrice = 0.68;
//            } else {
//                chargePrice = 0.58;
//                chargeInfo.setSeasonList(seasonList);
//            }
            CarOwnerRealEnduranceSpecInfoDTO.CalculatorInfo calculatorInfo = new CarOwnerRealEnduranceSpecInfoDTO.CalculatorInfo();
            calculatorInfo.setBigtitle("费用计算器");
            calculatorInfo.setSeasonlist(energyCostList);
            calculatorInfo.setDefmileage(100);
            calculatorInfo.setDefchargeprice(chargePrice);
            calculatorInfo.setOilprice(7.8);
            calculatorInfo.setAvgfuel(1 == isSuv ? 8.5 : 7.5);
            calculatorInfo.setIssuv(isSuv);
            calculatorInfo.setTipstitle("充电费用计算");
            calculatorInfo.setTipslist(Lists.newArrayList(
                    new CarOwnerRealEnduranceSpecInfoDTO.Tip("每度电可行使里程：", "100/百公里能耗（结果保留两位小数）"),
                    new CarOwnerRealEnduranceSpecInfoDTO.Tip("出行费用：",
                            "实际百公里能耗/100*充电单价*行驶里程（结果保留两位小数，默认充电单价为" + chargePrice + "元）"),
                    new CarOwnerRealEnduranceSpecInfoDTO.Tip("预计节约油费：",
                            "suv：8.5/100*行驶里程*7.8（结果保留两位小数）" +
                            "-行驶每公里成本*行驶里程\n其他：7.5/100*行驶里程*7.8（结果保留两位小数）-行驶每公里成本*行驶里程"),
                    new CarOwnerRealEnduranceSpecInfoDTO.Tip("充电费用：",
                            "充电一次成本：实际百公里能耗/100*充电单价*季节续航数（结果保留两位小数，默认充电单价为" + chargePrice + "元）")));
            chargeInfo.setCalculatorinfo(calculatorInfo);
        }
        nav.setChargeinfo(chargeInfo);
        CarOwnerRealEnduranceSpecInfoDTO.DiscussInfo discussInfo = new CarOwnerRealEnduranceSpecInfoDTO.DiscussInfo();
        discussInfo.setBigtitle("口碑车主用车讨论");
        discussInfo.setList(discuss.getRight());
        nav.setDiscussinfo(discussInfo);
        CarOwnerRealEnduranceSpecInfoDTO.MoreInfo moreInfo = new CarOwnerRealEnduranceSpecInfoDTO.MoreInfo();
        moreInfo.setMoretitle("查看更多车主口碑");
        moreInfo.setMorelinkurl(SchemeUtils.getKouBeiListScheme(current.getSeriesId(), current.getSeriesName(),
                null, null, 1, 48));
        nav.setMoreinfo(moreInfo);
        return nav;
    }

    public BaseModel<CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> compareInfo(Integer cityId, Integer seriesId,
                                                                                   Integer specId) {
        CarOwnerRealEnduranceOfKoubeiDTO factData = this.getBeiLiData(cityId, seriesId, specId);
        if (null == factData) {
            return new BaseModel<>(0, "当前车型车主续航数据不存在");
        }
        CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo current = factData.getSpecInfoList().stream()
                                                                    .filter(e -> e.getSpecId().equals(specId))
                                                                    .findFirst().orElse(null);
        if (null == current) {
            return new BaseModel<>(0, "当前车型车主续航数据不存在");
        }
        CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol currentCar = new CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol();
        currentCar.setTitle(current.getSeriesName());
        currentCar.setTitlecolor("");
        currentCar.setSpecid(specId);
        currentCar.setSeriesid(seriesId);
        currentCar.setSubtitle("官方续航" + current.getOfficialDriveRange() + "km");
        List<CarOwnerRealEnduranceSpecInfoDTO.TableColValue> currentCarValues = new ArrayList<>();
        LinkedHashMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> sortSeasonMap = convertSortMap(
                factData.getSeasonData());
        sortSeasonMap.forEach((k, v) -> {
            CarOwnerRealEnduranceSpecInfoDTO.TableColValue colValue = new CarOwnerRealEnduranceSpecInfoDTO.TableColValue(
                    "——", "", "");
            if (null != v) {
                colValue.setName(v.getFactDriveRange() + "km");
            }
            currentCarValues.add(colValue);
        });
        currentCar.setList(currentCarValues);
        currentCar.setLinkurl("autohome://rninsidebrowser?url=rn%3A%2F%2FCarNewRankRN%2FOwnerRealDataPage%3Fseriesid%3D"
                              + current.getSeriesId());
        return new BaseModel<>(0, "success", currentCar);
    }

    private LinkedHashMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> convertSortMap(
            TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData) {
        LinkedHashMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> sortMap = new LinkedHashMap<>();
        sortMap.put("春", seasonData.get("春"));
        sortMap.put("夏", seasonData.get("夏"));
        sortMap.put("秋", seasonData.get("秋"));
        sortMap.put("冬", seasonData.get("冬"));
        return sortMap;
    }

    private List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem> convertDiscussModel(List<KouBeiInfoDto> list) {
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().map(e -> {
                CarOwnerRealEnduranceSpecInfoDTO.DiscussItem discussItem = new CarOwnerRealEnduranceSpecInfoDTO.DiscussItem();
                discussItem.setUsername(e.getNickName());
                discussItem.setUserpic(KouBeiInfoDto.getHeadImageUrl(e.getHeadImage()));
                discussItem.setUserdesc(e.getSeriesName() + "车主");
                discussItem.setReplycount(e.getCommentCount());
                discussItem.setTitle(e.getFeeling());
                discussItem.setPraisecount(e.getHelpfulCount());
                discussItem.setViewcount(e.getVisitCount() + "浏览");
                discussItem.setLinkurl(SchemeUtils.getKouBeiScheme(e.getId(), e.getSeriesId(), e.getSeriesName(),
                        e.getSpecid(), e.getSpecName(), e.getNickName(), "", "", 1));
                if (!CollectionUtils.isEmpty(e.getPhotos())) {
                    discussItem.setImglist(e.getPhotos().size() > 3 ? e.getPhotos().subList(0, 3) : e.getPhotos());
                }
                return discussItem;
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 转换口碑车主分享model
     *
     * @param list
     * @return
     */
    private List<CarOwnerRealEnduranceSpecInfoDTO.DiscussItem> convertDiscussShareModel(
            List<KouBeiShareContentDto> list) {

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.stream().map(e -> {
            CarOwnerRealEnduranceSpecInfoDTO.DiscussItem discussShareItem = new CarOwnerRealEnduranceSpecInfoDTO.DiscussItem();
            discussShareItem.setUsername(e.getNickName());
            discussShareItem.setUserpic(KouBeiInfoDto.getHeadImageUrl(e.getHeadImage()));
            discussShareItem.setUserdesc(e.getSeriesName() + "车主");
            discussShareItem.setReplycount(e.getCommentCount());
            discussShareItem.setTitle(e.getFeeling());
            discussShareItem.setPraisecount(e.getHelpfulCount());
            discussShareItem.setViewcount(e.getVisitCount() + "浏览");
            discussShareItem.setLinkurl(SchemeUtils.getKouBeiScheme(e.getId(), e.getSeriesId(), e.getSeriesName(),
                    e.getSpecid(), e.getSpecName(), e.getNickName(), "", "", 1));
            if (!CollectionUtils.isEmpty(e.getMultiImages())) {
                final List<String> imageCollect = e.getMultiImages().stream()
                                                   .map(KouBeiShareContentDto.ImageData::getImg)
                                                   .collect(Collectors.toList());
                discussShareItem.setImglist(imageCollect.size() > 3 ? imageCollect.subList(0, 3) : imageCollect);
            }

            discussShareItem.setScore(e.getAverage() != null ? String.format("%.1f", e.getAverage()) : "0.0");
            List<CarOwnerRealEnduranceSpecInfoDTO.DiscussShareTitle> discussShareTitleList = new ArrayList<>();
            if (CollUtil.isNotEmpty(e.getTabInfoList())) {
                e.getTabInfoList().forEach(tabInfo -> discussShareTitleList.add(
                        new CarOwnerRealEnduranceSpecInfoDTO.DiscussShareTitle(tabInfo.getValue(), tabInfo.getName())));
            } else {
                discussShareTitleList.add(createDiscussShareTitle(e.getSpringrange(), "春秋满电续航"));
                discussShareTitleList.add(createDiscussShareTitle(e.getSummerrange(), "夏季满电续航"));
                discussShareTitleList.add(createDiscussShareTitle(e.getWinterCruisingRange(), "冬季满电续航"));
            }
            discussShareItem.setTestlist(discussShareTitleList);

            return discussShareItem;
        }).collect(Collectors.toList());

    }

    /**
     * 创建口碑车主分享标题
     *
     * @param range
     * @param subTitle
     * @return
     */
    private CarOwnerRealEnduranceSpecInfoDTO.DiscussShareTitle createDiscussShareTitle(Integer range, String subTitle) {
        CarOwnerRealEnduranceSpecInfoDTO.DiscussShareTitle discussShareTitle = new CarOwnerRealEnduranceSpecInfoDTO.DiscussShareTitle();
        discussShareTitle.setTitle(range != null && range.intValue() > 0 ? range + "km" : "--");
        discussShareTitle.setSubtitle(subTitle);
        return discussShareTitle;
    }

    private CarOwnerRealEnduranceSpecInfoDTO.SameSpecBase assembleSameSpecInfo(StatisSpecInfoDto current,
                                                                               TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData) {
        CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfo sameSpecInfo = new CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfo();
        sameSpecInfo.setBigtitle(current.getSpecName() + "冬夏续航");
        sameSpecInfo.setWinterTitle("冬季");
        sameSpecInfo.setWinterSubTitle("12月-2月");
        sameSpecInfo.setWinterSuffix("km");
        CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo winter = seasonData.get("冬");
        if (null != winter) {
            sameSpecInfo.setWinterValue(winter.getFactDriveRange());
        }
        sameSpecInfo.setSummerTitle("夏季");
        sameSpecInfo.setSummerSubtitle("6月-8月");
        sameSpecInfo.setSummerSuffix("km");
        CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo summer = seasonData.get("夏");
        if (null != summer) {
            sameSpecInfo.setSummerValue(summer.getFactDriveRange());
        }
        return sameSpecInfo;
    }

    private CarOwnerRealEnduranceSpecInfoDTO.CompareInfo createBaseCompareInfo(Integer cityId) {
        CarOwnerRealEnduranceSpecInfoDTO.CompareInfo compareInfo = new CarOwnerRealEnduranceSpecInfoDTO.CompareInfo();
        compareInfo.setBigtitle("同级车四季续航对比");
        List<CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> tableList = new ArrayList<>();
        CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol head = new CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol();
        head.setTitle("对比车系");
        head.setSubtitle("");
        head.setTitlecolor("");
        RegionDto regionInfo = CityUtils.getRegionInfo(cityId);
        List<CarOwnerRealEnduranceSpecInfoDTO.TableColValue> headValues = Lists.newArrayList(
                new CarOwnerRealEnduranceSpecInfoDTO.TableColValue("春季", regionInfo.getSpringTemperature(), ""),
                new CarOwnerRealEnduranceSpecInfoDTO.TableColValue("夏季", regionInfo.getSummerTemperature(), ""),
                new CarOwnerRealEnduranceSpecInfoDTO.TableColValue("秋季", regionInfo.getAutumnTemperature(), ""),
                new CarOwnerRealEnduranceSpecInfoDTO.TableColValue("冬季", regionInfo.getWinterTemperature(), "")
                                                                                            );
        head.setList(headValues);
        head.setLinkurl("");
        tableList.add(head);
        compareInfo.setTablelist(tableList);
        return compareInfo;
    }

    private void assembleCompareInfo(CarOwnerRealEnduranceSpecInfoDTO.CompareInfo compareInfo,
                                     StatisSpecInfoDto current,
                                     TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData,
                                     List<CarOwnerRealEnduranceOfKoubeiDTO.SameCar> newSameCarList) {
        List<Integer> specIds = Lists.newArrayList(current.getSpecId());
        List<CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> tableList = compareInfo.getTablelist();
        CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol currentCar = new CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol();
        currentCar.setTitle(current.getSeriesName());
        currentCar.setTitlecolor("#05D1D1");
        currentCar.setSpecid(current.getSpecId());
        currentCar.setSeriesid(current.getSeriesId());
        currentCar.setSubtitle("官方续航" + current.getOfficialDriveRange() + "km");
        List<CarOwnerRealEnduranceSpecInfoDTO.TableColValue> currentCarValues = new ArrayList<>();
        if (Objects.nonNull(seasonData)) {
            LinkedHashMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> sortSeasonMap = convertSortMap(
                    seasonData);
            for (String k : sortSeasonMap.keySet()) {
                CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo v = sortSeasonMap.get(k);
                CarOwnerRealEnduranceSpecInfoDTO.TableColValue colValue = new CarOwnerRealEnduranceSpecInfoDTO.TableColValue(
                        "——", "", "#05D1D1");
                if (null != v && v.getFactDriveRange() != 0) {
                    colValue.setName(v.getFactDriveRange() + "km");
                }
                currentCarValues.add(colValue);
            }
            currentCar.setList(currentCarValues);
            currentCar.setLinkurl("");
            tableList.add(currentCar);
        }
        if (newSameCarList != null && !newSameCarList.isEmpty()) {
            assembleSameCarTable(tableList, specIds, newSameCarList);
        }
        String specIdsStr = specIds.stream().map(Object::toString).collect(Collectors.joining(","));
        compareInfo.setSpecids(specIdsStr);
        compareInfo.setTablelist(tableList);
    }

    private void assembleSameCarTable(List<CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol> tableList,
                                      List<Integer> specIds,
                                      List<CarOwnerRealEnduranceOfKoubeiDTO.SameCar> newSameCarList) {
        List<CarOwnerRealEnduranceOfKoubeiDTO.SameCar> list = newSameCarList.size() > 2 ? newSameCarList.subList(0,
                2) : newSameCarList;
        specIds.addAll(
                list.stream().map(CarOwnerRealEnduranceOfKoubeiDTO.SameCar::getSpecId).collect(Collectors.toList()));
        list.forEach(e -> {
            CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol sameCarCol = new CarOwnerRealEnduranceSpecInfoDTO.CompareTableCol();
            sameCarCol.setTitle(e.getSeriesName());
            sameCarCol.setSpecid(e.getSpecId());
            sameCarCol.setSeriesid(e.getSeriesId());
            sameCarCol.setTitlecolor("");
            sameCarCol.setSubtitle("官方续航" + e.getOfficialDriveRange() + "km");
            sameCarCol.setLinkurl(
                    "autohome://rninsidebrowser?url=rn%3A%2F%2FCarNewRankRN%2FOwnerRealDataPage%3Fseriesid%3D"
                    + e.getSeriesId() + "%26tabid%3D2");
            List<CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason> sameCarSeasonList = e.getSameCarSeasonList();
            Map<String, CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason> seasonDataMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(sameCarSeasonList)) {
                seasonDataMap.putAll(sameCarSeasonList.stream()
                                                      .collect(Collectors.toMap(
                                                              CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason::getSeason,
                                                              o -> o, (k1, k2) -> k1)));
            }
            List<CarOwnerRealEnduranceSpecInfoDTO.TableColValue> data = Lists.newArrayList("春", "夏", "秋", "冬")
                                                                             .stream()
                                                                             .map(season -> {
                                                                                 CarOwnerRealEnduranceSpecInfoDTO.TableColValue colValue = new CarOwnerRealEnduranceSpecInfoDTO.TableColValue(
                                                                                         "——", "", "");
                                                                                 CarOwnerRealEnduranceOfKoubeiDTO.SameCarSeason seasonData = seasonDataMap.get(
                                                                                         season);
                                                                                 if (null != seasonData) {
                                                                                     colValue.setName(
                                                                                             seasonData.getFactDriveRange() + "km");
                                                                                 }
                                                                                 return colValue;
                                                                             }).collect(Collectors.toList());
            sameCarCol.setList(data);
            tableList.add(sameCarCol);
        });
    }

    /**
     * 填充充电打底数据
     */
    private List<CarOwnerRealEnduranceSpecInfoDTO.Charge> getChargeTimeNormal(
            CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo currentSeasonData, String slowChargeTime,
            String fastChargeTime) {
        CarOwnerRealEnduranceSpecInfoDTO.Charge slow = new CarOwnerRealEnduranceSpecInfoDTO.
                Charge(slowChargeTime, slowChargeTime.equals("--") ? "" : "h", "慢充", "0%-100%");
        CarOwnerRealEnduranceSpecInfoDTO.Charge fast = new CarOwnerRealEnduranceSpecInfoDTO.
                Charge(fastChargeTime, fastChargeTime.equals("--") ? "" : "h", "快充", "20%-100%");
        if (null != currentSeasonData && null != currentSeasonData.getFactChaSpeed() && !currentSeasonData.getFactChaSpeed()
                                                                                                          .equals(0.0D)) {
            fast.setTime(currentSeasonData.getFactChaSpeed() + "");
            fast.setSuffix("h");
        }
        return Arrays.asList(slow, fast);
    }

    //从ext中复制
    public SeriesInfoNewEnergyResult seriesFactData(Integer cityId, Integer seriesId) {
        SeriesInfoNewEnergyResult result = new SeriesInfoNewEnergyResult();
        SeriesInfoNewEnergyResult.Result seriesInfo = new SeriesInfoNewEnergyResult.Result();
        CarOwnerRealEnduranceOfKoubeiDTO factData = null;
        try {
            factData = this.getBeiLiData(cityId, seriesId, null);
        } catch (Exception e) {
            throw new BusinessException("源接口调用失败", -1);
        }
        if (null != factData  && !CollectionUtils.isEmpty(factData.getSpecInfoList())) {
            List<CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo> specInfoList = factData.getSpecInfoList();

            CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo current = Objects.requireNonNull(specInfoList.stream()
                                                                        .collect(Collectors.groupingBy(
                                                                                CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo::getOfficialDriveRange))
                                                                        .entrySet()
                                                                        .stream().max(Map.Entry.comparingByKey())
                                                                        .orElse(null)).getValue()
                                            .stream().max(Comparator.comparing(CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo::getFactDriveRange)
                                                                    .thenComparing(CarOwnerRealEnduranceOfKoubeiDTO.SpecInfo::getSpecId))
                                            .orElse(null);
            if (null != current) {
                CarOwnerRealEnduranceOfKoubeiDTO specFactData = factData;
                List<CompletableFuture> tasks = new ArrayList<>();
                AtomicReference<CarOwnerRealEnduranceOfKoubeiDTO> beiliStatistics = new AtomicReference<>();
                AtomicReference<ResponseContent<SpecParam>> specParamDetailInfoResult = new AtomicReference<>();
                tasks.add(CompletableFuture.supplyAsync(() -> this.getBeiLiData(cityId, seriesId, current.getSpecId()))
                                           .exceptionally((e -> {
                                               log.error("carOwnerFactDataDal.specFactData error", e);
                                               return null;
                                           })).thenAccept(beiliStatistics::set));
                tasks.add(CompletableFuture.supplyAsync(
                                                   () -> buyCarCommonService.spec_parambyspecid("car", current.getSpecId().toString()))
                                           .exceptionally(e -> {
                                               log.error("microBizDaL.getSpecParamDetailInfoByApi error", e);
                                               return null;
                                           }).thenAccept(specParamDetailInfoResult::set));
                CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
                if (!Objects.equals(factData.getCurrentSpecId(), current.getSpecId())) {
                    specFactData = beiliStatistics.get();
                }
                if (specFactData != null) {
                    Triple<Integer, Double, Double> specFactCoreData = getSpecFactCoreData(specParamDetailInfoResult.get().getResult().getSpecid(), specFactData.getSeasonData());
                    seriesInfo.setSpecId(current.getSpecId());
                    seriesInfo.setOfficialDriveRange(current.getOfficialDriveRange());
                    seriesInfo.setFactDriveRange(specFactCoreData.getLeft());
                    seriesInfo.setElectricCharge(specFactCoreData.getMiddle());
                    seriesInfo.setFactChaSpeed(specFactCoreData.getRight());
                    seriesInfo.setDriveRangeLink(
                            "autohome://rninsidebrowser?url=rn%3A%2F%2FCarNewRankRN%2FOwnerRealDataPage%3Fseriesid%3D"
                            + seriesId);
                    seriesInfo.setChargeLink(
                            "autohome://rninsidebrowser?url=rn%3A%2F%2FCarNewRankRN%2FOwnerRealDataPage%3Fseriesid%3D" + seriesId);
                }
            }

        }
        result.setResult(seriesInfo);
        return result;
    }

    private Triple<Integer, Double, Double> getSpecFactCoreData(Integer specId,TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData) {
        int factDriveRange = 0;
        double charge = 0.0D;
        double factChaSpeed = Double.MAX_VALUE;
        if (null != seasonData && !seasonData.isEmpty()) {
            double factEnergy100 = Double.MAX_VALUE;
            for (CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo dto : seasonData.values()) {
                factEnergy100 = Math.min(factEnergy100, dto.getFactEnergy100());
                factDriveRange = Math.max(factDriveRange, dto.getFactDriveRange());
                if (dto.getFactChaSpeed() != 0.0D) {
                    factChaSpeed = Math.min(factChaSpeed, dto.getFactChaSpeed());
                }
            }
            if (factEnergy100 == Double.MAX_VALUE) {
                factEnergy100 = 0D;
            }
            if (factChaSpeed == Double.MAX_VALUE) {
                ResponseContent<SpecParam> specParam = buyCarCommonService.spec_parambyspecid("car", specId.toString());
                if (null != specParam && null != specParam.getResult()
                    && !"0".equals(specParam.getResult().getFastchargetime())) {
                    factChaSpeed = Double.parseDouble(specParam.getResult().getFastchargetime());
                } else {
                    factChaSpeed = 0D;
                }
            }
            BigDecimal cost = BigDecimal.valueOf(factEnergy100)
                                        .multiply(BigDecimal.valueOf(0.68)).setScale(1, RoundingMode.HALF_DOWN);
            charge = cost.doubleValue();
        }
        return Triple.of(factDriveRange, charge, factChaSpeed);
    }

    private ResponseContent<SpecParam> specParamBySpecIdOfCache (Integer specId){

        String redisKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_SPEC_PARAM_DATA,specId);
        String redisJsonStr = stringRedisTemplate.get(redisKey);

        if (!StringUtils.isEmpty(redisJsonStr)) {
            return new ResponseContent(JacksonHelper.deserialize(redisJsonStr,SpecParam.class));
        }

        ResponseContent<SpecParam> specParam = buyCarCommonService.spec_parambyspecid("car", specId.toString());

        stringRedisTemplate.set(redisKey, JacksonHelper.serialize(specParam.getResult()), 4, TimeUnit.HOURS);

        return specParam;
    }

    private CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew assembleSameSpecInfoNew(StatisSpecInfoDto current,
                                                                                     TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData,
                                                                                     Integer cityId) {
        CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew sameSpecInfo = new CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew();
        if (Objects.isNull(seasonData)) {
            return sameSpecInfo;
        }
        sameSpecInfo.setBigtitle(current.getSpecName() + "冬夏续航");
        sameSpecInfo.setBigtitlelinkurl("autohome://car/specmain?specid=" + current.getSpecId());

        CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo summer = seasonData.get("夏");
        CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew.SeasonDto summerDto = new CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew.SeasonDto();
        if (null != summer && Objects.nonNull(summer.getFactDriveRange()) && summer.getFactDriveRange() != 0) {
            summerDto.setValue(summer.getFactDriveRange());
        } else {
            summerDto.setValue(0);
        }
        summerDto.setTitle("夏季");
        summerDto.setSubtitle("6月-8月");
        summerDto.setSuffix("km");
        summerDto.setStartcolor("rgba(255, 177, 21, 0)");
        summerDto.setEndcolor("rgba(255, 177, 21, 0.2)");
        List<CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew.SeasonDto> list = new ArrayList<>();
        sameSpecInfo.setList(list);
        sameSpecInfo.getList().add(summerDto);

        CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew.SeasonDto winterDto = new CarOwnerRealEnduranceSpecInfoDTO.SameSpecInfoNew.SeasonDto();
        CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo winter = seasonData.get("冬");
        winterDto.setValue(0);
        if (null != winter && Objects.nonNull(winter.getFactDriveRange()) && winter.getFactDriveRange() != 0) {
            // 版本>= 11.57.0 若为北方, 且冬季续航小于等于夏季续航时才展示
            if (!isNorthCity(cityId)
                || Objects.isNull(summerDto.getValue())
                || summerDto.getValue() == 0
                || winter.getFactDriveRange() <= summerDto.getValue()) {
                winterDto.setValue(winter.getFactDriveRange());

            }
        }
        winterDto.setTitle("冬季");
        winterDto.setSubtitle("12月-2月");
        winterDto.setSuffix("km");
        winterDto.setStartcolor("rgba(9, 217, 223, 0)");
        winterDto.setEndcolor("rgba(9, 217, 223, 0.2)");
        sameSpecInfo.getList().add(winterDto);
        if (CollUtil.isNotEmpty(sameSpecInfo.getList()) && sameSpecInfo.getList().size() == 2) {
            sameSpecInfo.setList(CollUtil.reverse(sameSpecInfo.getList()));
        }

        return sameSpecInfo;
    }

    /**
     * 查询是否可以显示车主续航并处理季节信息
     * @param cityId 城市ID
     * @param seriesId 车系ID
     * @param officialDriveRange 官方续航
     * @param seasonData 季节信息
     * @return 是否可以展示车主续航
     */
    private boolean getShowBatteryFlag(int cityId, int seriesId, int officialDriveRange, TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData) {
        int lastLevelFactDriveRange = getLastLevelFactDriveRange(cityId, seriesId, officialDriveRange);
        if (lastLevelFactDriveRange != 0) {
            int zeroCount = 0;
            for (Map.Entry<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> entry : seasonData.entrySet()) {
                CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo dataDto = entry.getValue();
                if (dataDto.getFactDriveRange() > lastLevelFactDriveRange) {
                    dataDto.setFactDriveRange(0);
                    zeroCount++;
                }
            }
            return zeroCount != seasonData.size();
        }
        return true;
    }

    private int getLastLevelFactDriveRange(Integer cityId, Integer seriesId, Integer officialDriveRange) {
        Map<Integer, SpecDriveRangeInfoDto> factDriveMap = getFactDriveProcess(cityId, seriesId);
        if (CollUtil.isNotEmpty(factDriveMap)) {
            return factDriveMap.get(officialDriveRange).getLastLevelFactRange();
        }
        return 0;
    }

    public Map<Integer, SpecDriveRangeInfoDto> getFactDriveProcess(Integer cityId, Integer seriesId) {

        String redisKey = String.format(RedisKeys.CAR_OWNER_REAL_ENDURANCE_FACT_DRIVE_PROCESS_DATA, seriesId, cityId);

        String jsonStr = stringRedisTemplate.get(redisKey);

        if (!StringUtils.isEmpty(jsonStr)) {
            return JacksonHelper.deserialize(jsonStr, new TypeReference<Map<Integer, SpecDriveRangeInfoDto>>() {});
        }

        CompletableFuture<CarOwnerRealEnduranceOfKoubeiDTO> beiLiFuture = CompletableFuture.supplyAsync(() -> this.getBeiLiData(cityId, seriesId, null))
                                                                             .exceptionally(e -> {
                                                                                 log.error("carOwnerFactDataDal.specFactData error");
                                                                                 return null;
                                                                             });
        CompletableFuture<CarOwnerRealEnduranceOfKoubeiDTO> kouBeiFuture = CompletableFuture.supplyAsync(() ->carOwnerRealEnduranceService.getCarOwnerRealEnduranceKouBei(null, seriesId, cityId))
                                                                              .exceptionally(e -> {
                                                                                  log.error("kouBeiRepositoryDal.specFactDataByKouBei error");
                                                                                  return null;
                                                                              });
        CarOwnerRealEnduranceOfKoubeiDTO beiLiFactData = null;
        if (Objects.nonNull(beiLiFuture)) {
            beiLiFactData = beiLiFuture.join();
        }
        List<SpecDriveRangeInfoDto> allSpecInfoList = new ArrayList<>();

        CarOwnerRealEnduranceOfKoubeiDTO kouBeiFactData = null;
        if (Objects.nonNull(kouBeiFuture)) {
            kouBeiFactData = kouBeiFuture.join();
        }
        if (Objects.nonNull(kouBeiFactData) && CollUtil.isNotEmpty(kouBeiFactData.getStatisSpecInfo())) {
            List<StatisSpecInfo> specInfoList = kouBeiFactData.getStatisSpecInfo();
            List<String> seriesIdOfficalRangeList = specInfoList.stream().sorted(Comparator.comparingInt(StatisSpecInfo::getOfficialDriveRange).reversed()).map(x -> x.getSeriesId() + StrPool.UNDERLINE + x.getOfficialDriveRange()).collect(Collectors.toList());
            SameCarSeriesResult kouBeiBatchData = null;
            try {
                kouBeiBatchData = this.seriesListCompareInfos(cityId, seriesIdOfficalRangeList);
            } catch (Exception e) {
               log.error("getFactDriveProcess",e);
            }

            if (Objects.nonNull(kouBeiBatchData) && CollUtil.isNotEmpty(kouBeiBatchData.getNewSameCarList())) {
                List<SameCarSeriesResult.SameCarSeriesInfo> kouBeiCarList = kouBeiBatchData.getNewSameCarList();
                Map<Integer, List<SameCarSeriesResult.SameCarSeriesInfo.Season>> carSeasonMap = kouBeiCarList.stream().collect(Collectors.toMap(SameCarSeriesResult.SameCarSeriesInfo::getOfficialDriveRange, SameCarSeriesResult.SameCarSeriesInfo::getSameCarSeasonList));
                for (StatisSpecInfo current : specInfoList) {
                    if (carSeasonMap.containsKey(current.getOfficialDriveRange())) {
                        List<SameCarSeriesResult.SameCarSeriesInfo.Season> seasonList = carSeasonMap.get(current.getOfficialDriveRange());
                        SpecDriveRangeInfoDto instance = SpecDriveRangeInfoDto.getInstance(current);
                        instance.setSeasonDataList(new ArrayList<>());
                        seasonList.forEach(season -> instance.getSeasonDataList().add(new SpecDriveRangeInfoDto.SpecSeasonData(season.getSeason(), season.getFactDriveRange())));
                        allSpecInfoList.add(instance);
                    }

                }
            }
        }
        if (Objects.nonNull(beiLiFactData) && CollUtil.isNotEmpty(beiLiFactData.getStatisSpecInfo())) {
            List<Integer> kouBeiOfficalRangeList = allSpecInfoList.stream().map(SpecDriveRangeInfoDto::getOfficialDriveRange).collect(Collectors.toList());
            Map<Integer, List<StatisSpecInfo>> beiLiDataMap = beiLiFactData.getStatisSpecInfo().stream().filter(x -> !kouBeiOfficalRangeList.contains(x.getOfficialDriveRange())).collect(Collectors.groupingBy(StatisSpecInfo::getOfficialDriveRange));
            if (CollUtil.isNotEmpty(beiLiDataMap)) {
                List<StatisSpecInfo> beiLiDisDataList = new ArrayList<>();
                beiLiDataMap.forEach((official, list) -> {
                    //List<StatisSpecInfo> collect = list.stream().sorted(Comparator.comparing(StatisSpecInfo::getSpecId).reversed()).collect(Collectors.toList());
                    Optional<StatisSpecInfo> max = list.stream().max(Comparator.comparingInt(StatisSpecInfo::getFactDriveRange));
                    max.ifPresent(beiLiDisDataList::add);
                });
                beiLiDisDataList.parallelStream().forEach(specInfo -> {
                    try {
                        CarOwnerRealEnduranceOfKoubeiDTO beiLiResult =  this.getBeiLiData(cityId, seriesId, specInfo.getSpecId());
                        if (Objects.nonNull(beiLiResult) && Objects.nonNull(beiLiResult.getSeasonData())) {
                            TreeMap<String, CarOwnerRealEnduranceOfKoubeiDTO.SeasonInfo> seasonData = beiLiResult.getSeasonData();
                            SpecDriveRangeInfoDto instance = SpecDriveRangeInfoDto.getInstance(specInfo);
                            instance.setSeasonDataList(new ArrayList<>());
                            seasonData.forEach((name, dto) -> instance.getSeasonDataList().add(new SpecDriveRangeInfoDto.SpecSeasonData(name, dto.getFactDriveRange())));
                            allSpecInfoList.add(instance);
                        }
                    } catch (Exception e) {
//                        CacheHolder.error();
                        log.error("getFactDriveProcess", e);
                    }
                });
            }
        }
        if (CollUtil.isNotEmpty(allSpecInfoList)) {
            int maxRange = Integer.MAX_VALUE;
            allSpecInfoList.sort(Comparator.comparingInt(SpecDriveRangeInfoDto::getOfficialDriveRange).reversed());

            for (SpecDriveRangeInfoDto specDriveRangeInfoDto : allSpecInfoList) {
                List<SpecDriveRangeInfoDto.SpecSeasonData> seasonList = specDriveRangeInfoDto.getSeasonDataList();
                int finalMaxRange = maxRange;
                if (isNorthCity(cityId)) {
                    Map<String, SpecDriveRangeInfoDto.SpecSeasonData> seasonMap = seasonList.stream().collect(Collectors.toMap(SpecDriveRangeInfoDto.SpecSeasonData::getName, x -> x));
                    SpecDriveRangeInfoDto.SpecSeasonData winter = seasonMap.get(SeasonEnum.WINTER.getCnName());
                    SpecDriveRangeInfoDto.SpecSeasonData summer = seasonMap.get(SeasonEnum.SUMMER.getCnName());
                    if (Objects.nonNull(winter) && Objects.nonNull(summer) && summer.getFactDriveRange() > 0 && winter.getFactDriveRange() > summer.getFactDriveRange()) {
                        winter.setFactDriveRange(0);
                    }
                }
                int maxSeasonFactRange = seasonList.stream().map(SpecDriveRangeInfoDto.SpecSeasonData::getFactDriveRange).filter(factDriveRange -> factDriveRange <= finalMaxRange).max(Comparator.comparingInt(x -> x)).orElse(0);
                if (maxSeasonFactRange != 0) {
                    maxRange = maxSeasonFactRange;
                }
                specDriveRangeInfoDto.setFactDriveRange(maxSeasonFactRange);

            }


            int maxFactRange = allSpecInfoList.get(0).getFactDriveRange();
            allSpecInfoList.get(0).setLastLevelFactRange(Integer.MAX_VALUE);
            for (int i = 1; i < allSpecInfoList.size(); i++) {
                SpecDriveRangeInfoDto currentSpecInfo = allSpecInfoList.get(i);
                if (maxFactRange < currentSpecInfo.getFactDriveRange()) {
                    currentSpecInfo.setFactDriveRange(0);
                } else {
                    if (currentSpecInfo.getFactDriveRange() != 0) {
                        maxFactRange = currentSpecInfo.getFactDriveRange();
                    }
                }
                currentSpecInfo.setLastLevelFactRange(maxFactRange);
            }

            Map<Integer, SpecDriveRangeInfoDto> result = allSpecInfoList.stream().collect(
                    Collectors.toMap(SpecDriveRangeInfoDto::getOfficialDriveRange, x -> x));

            stringRedisTemplate.set(redisKey, JacksonHelper.serialize(result), 2, TimeUnit.HOURS);

            return result;
        }



        return Collections.emptyMap();
    }




}
