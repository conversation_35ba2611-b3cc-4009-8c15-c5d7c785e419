package com.autohome.applet.service.openapi;

import com.autohome.applet.model.dto.carlibrary.carapi.out.CarApiBrandListOut;
import com.autohome.applet.model.dto.carlibrary.carapi.out.CarApiFctListOut;
import com.autohome.applet.model.dto.carlibrary.carapi.out.CarApiSpecDetailOut;
import com.autohome.applet.model.dto.carlibrary.carapi.out.CarApiSpecListOut;

public interface CarLibService {

    // 三端统一的品牌、车系、车型接口 start

    CarApiBrandListOut getCarApiBrandList();

    CarApiFctListOut getCarApiSeriesList(String secretBrandId);

    CarApiSpecListOut getCarApiSpecList(String secretSeriesId);

    // 三端统一的品牌、车系、车型接口 end

    CarApiSpecDetailOut getCarApiSpecDetail(String secretSpecId);

}
