package com.autohome.applet.service.utils;

import org.springframework.util.StringUtils;

public class SchemeUtils {
    public SchemeUtils() {
    }

    public static String getKouBeiScheme(Integer kouBeiId, Integer seriesId, String seriesName, Integer specId, String specName, String userName, String reportDate, String pvid, Integer from) {
        return String.format("autohome://reputation/reputationdetail?koubeiid=%s&seriesid=%s&seriesname=%s&specid=%s&specname=%s&username=%s&reportdate=%s&pvid=%s&from=%s",
                kouBeiId, seriesId, !StringUtils.isEmpty(seriesName) ? EncodesUtils.urlEncode(seriesName) : "",
                specId, !StringUtils.isEmpty(specName) ? EncodesUtils.urlEncode(specName) : "",
                !StringUtils.isEmpty(userName) ? EncodesUtils.urlEncode(userName) : "", reportDate, pvid, from);
    }

    public static String getKouBeiListScheme(Integer seriesId, String seriesName,
                                             Integer specId, String specName, Integer categoryId, Integer koubeiFromKey) {
        return String.format("autohome://reputation/reputationlist?brandid=&seriesid=%d&seriesname=%s&specid=%s&specname=%s&categoryid=%d&summarykey=&koubeifromkey=%d",
                seriesId, !StringUtils.isEmpty(seriesName) ? EncodesUtils.urlEncode(seriesName) : "",
                null==specId?"":specId+"", !StringUtils.isEmpty(specName) ? EncodesUtils.urlEncode(specName) : "", categoryId, koubeiFromKey);
    }


}
