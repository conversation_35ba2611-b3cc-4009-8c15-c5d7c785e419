package com.autohome.applet.service.impl;

import com.autohome.applet.dao.javaapi.mapper.AppletDoTaskLogMapper;
import com.autohome.applet.dao.javaapi.model.AppletDoTaskLog;
import com.autohome.applet.service.AppletDoTaskLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AppletDoTaskLogServiceImpl implements AppletDoTaskLogService {

    @Autowired
    private AppletDoTaskLogMapper appletDoTaskLogMapper;


    @Override
    public List<AppletDoTaskLog> getUnexecutedList(String startDate , String endDate,Integer pageSize,Long  lastId) {
        List<AppletDoTaskLog> list = appletDoTaskLogMapper.getUnexecutedList(startDate, endDate,pageSize,lastId);

        return list;
    }

    @Override
    public void updateByPrimaryKey(AppletDoTaskLog doTaskLog) {
        appletDoTaskLogMapper.updateByPrimaryKey(doTaskLog);
    }
}
