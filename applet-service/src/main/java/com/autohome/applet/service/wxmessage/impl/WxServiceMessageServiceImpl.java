package com.autohome.applet.service.wxmessage.impl;

import com.autohome.applet.dao.javaapi.mapper.WxMiniAccountMapper;
import com.autohome.applet.dao.javaapi.mapper.WxServiceAccountMapper;
import com.autohome.applet.dao.javaapi.model.WxMiniAccount;
import com.autohome.applet.dao.javaapi.model.WxServiceAccount;
import com.autohome.applet.dao.javaapi.model.wxmsg.SubscriptionInfoDTO;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.wsmessage.SubscriptionInfo;
import com.autohome.applet.model.dto.wsmessage.SubscriptionRequest;
import com.autohome.applet.model.dto.wsmessage.WxMiniMessageRequest;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.javaapi.UserCenterService;
import com.autohome.applet.service.wxmessage.WxMessageService;
import com.autohome.applet.service.wxmessage.WxServiceMessageService;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class WxServiceMessageServiceImpl implements WxServiceMessageService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WxMiniAccountMapper wxMiniAccountMapper;
    @Autowired
    private WxServiceMessageAsync wxServiceMessageAsync;

    @Autowired
    private WxServiceAccountMapper wxServiceAccountMapper;

    @Autowired
    private HttpService httpService;

    @Autowired
    UserCenterService userCenterService;

    @Autowired
    WxMessageService wxMessageService;

    @Value("${third.service.url.subscribe:\"\"}")
    private String subscribeUrl;

    private static final String PREFIX_LOCK_SAVE_PUSH_MESSAGE = String.join(":", "lock", "wx", "p", "msg");

    /**
     * 保存小程序openid、设备号、userid、unionid 等信息
     *
     * @param wxMiniMessageRequest
     * @return
     */
    @Override
    public ReturnValue saveAccountInfo(WxMiniMessageRequest wxMiniMessageRequest) {

        WxMiniAccount exWxMiniAccount = wxMiniAccountMapper.getWxMiniAccountByDeviceId(wxMiniMessageRequest.getDeviceId(), wxMiniMessageRequest.getBizType());
        if (exWxMiniAccount != null) {
            return ReturnValue.buildErrorResult(-1, "已存在对应设备id，无需再次请求");
        }
        //1.保存数据到表中
        WxMiniAccount wxMiniAccount = new WxMiniAccount();
        wxMiniAccount.setSourceId(wxMiniMessageRequest.getSourceId());
        wxMiniAccount.setSeriesId(wxMiniMessageRequest.getSeriesId());
        wxMiniAccount.setCityId(wxMiniMessageRequest.getCityId() == null ? 0 : wxMiniMessageRequest.getCityId());
        wxMiniAccount.setSpecId("");
        wxMiniAccount.setPluginversion(wxMiniMessageRequest.getPluginversion());
        wxMiniAccount.setUserId(wxMiniMessageRequest.getUserId());
        wxMiniAccount.setDeviceId(wxMiniMessageRequest.getDeviceId());
        wxMiniAccount.setXcxOpenId(wxMiniMessageRequest.getXcxopenId());
        wxMiniAccount.setUnionId(wxMiniMessageRequest.getUnionId());
        wxMiniAccount.setBizType(wxMiniMessageRequest.getBizType());
        wxMiniAccount.setPm(wxMiniMessageRequest.getPm());
        wxMiniAccount.setCreatedStime(new Date());
        wxMiniAccount.setModifiedStime(new Date());
        wxMiniAccount.setStatus(0);
        wxMiniAccount.setIsDel(0);

        int result = wxMiniAccountMapper.insert(wxMiniAccount);

        if (result > 0) {
            log.info("saveAccountInfo success, param:{}", JacksonHelper.serialize(wxMiniMessageRequest));
            return ReturnValue.buildSuccessResult(true);
        } else {
            log.warn("saveAccountInfo, param:{}, returncode:{}, message:{}", JacksonHelper.serialize(wxMiniMessageRequest), 100110, "保存信息失败");
            return ReturnValue.buildErrorResult(100110, "保存信息失败");
        }
    }

    @Override
    public ReturnValue subService(SubscriptionRequest subscriptionRequest) {

        WxMiniAccount exWxMiniAccount = wxMiniAccountMapper.getWxMiniAccountByUnionId(subscriptionRequest.getUnionId(), subscriptionRequest.getBiztype());
        if (exWxMiniAccount == null) {
            log.info("subService,未找到对应设备id，无法订阅服务号,subscriptionRequest={}", subscriptionRequest);
            return ReturnValue.buildErrorResult(-1, "未找到对应设备id，无法订阅服务号");
        }
        if (exWxMiniAccount.getStatus() == 1) {
            return ReturnValue.buildSuccessResult(true);
        }

        //1.保存数据表
        convertDaoWxServiceAccount(exWxMiniAccount, subscriptionRequest);
        //2.发送http请求到品库进行订阅操作
        httpSubscribe(exWxMiniAccount, 1);

        //3.给用户发一条服务号消息
        wxServiceMessageAsync.sendSubscribeMsg(subscriptionRequest, exWxMiniAccount);
        log.info("subService success, param:{}", JacksonHelper.serialize(subscriptionRequest));
        return ReturnValue.buildSuccessResult(true);

    }

    /**
     * 通过deviceid进行数据查询
     *
     * @param subscriptionInfo
     * @return
     */
    @Override
    public ReturnValue cancelSubService(SubscriptionInfo subscriptionInfo) {
        WxMiniAccount wxMiniAccountByDeviceId = wxMiniAccountMapper.getWxMiniAccountByDeviceId(subscriptionInfo.getDeviceId(), subscriptionInfo.getBiztype());
        if (wxMiniAccountByDeviceId == null) {
            return ReturnValue.buildErrorResult(-1, "该用户未订阅对应类型");
        }
        if (wxMiniAccountByDeviceId.getStatus() == 0) {
            return ReturnValue.buildErrorResult(-1, "该用户已经是取消订阅用户");
        }
        wxMiniAccountMapper.updateById(Long.parseLong(wxMiniAccountByDeviceId.getId() + ""));

        ReturnValue<?> returnValue = httpSubscribe(wxMiniAccountByDeviceId, 2);

        return returnValue;
    }

    @Override
    public ReturnValue getSubInfo(SubscriptionInfo subscriptionInfo) {
        Map<String, Integer> map = new HashMap<>();
        List<SubscriptionInfoDTO> subInfo = wxMiniAccountMapper.getSubInfo(subscriptionInfo.getDeviceId(), subscriptionInfo.getBiztype());
        //如果查询跟服务号绑定关系，只要有记录即可
        if (subscriptionInfo.getBiztype() == -1) {
            if (subInfo != null && subInfo.size() >= 1) {
                map.put("status", 1);
            } else {
                map.put("status", -1);
            }
        } else {
            if (subInfo != null && subInfo.size() >= 1) {
                SubscriptionInfoDTO subscriptionInfoDTO = subInfo.get(0);
                map.put("status", subscriptionInfoDTO.getStatus());
            } else {
                map.put("status", -1);
            }
        }

        return ReturnValue.buildSuccessResult(map);
    }

    public WxMiniAccount convertDaoWxServiceAccount(WxMiniAccount subscriptionInfoDTO, SubscriptionRequest subscriptionRequest) {
        WxMiniAccount wxMiniAccount = null;
        if (subscriptionInfoDTO.getStatus() == 0) {
            subscriptionInfoDTO.setStatus(1);
            wxMiniAccountMapper.updateByPrimaryKey(subscriptionInfoDTO);

        }
        WxServiceAccount wxServiceAccountByUnionId = wxServiceAccountMapper.getWxServiceAccountByUnionId(subscriptionInfoDTO.getUnionId());
        if (wxServiceAccountByUnionId == null) {
            WxServiceAccount wxServiceAccount = WxServiceAccount.builder().fwhOpenId(subscriptionRequest.getFwhOpenId()).unionId(subscriptionRequest.getUnionId()).createdStime(new Date()).modifiedStime(new Date()).isDel(0).build();
            wxServiceAccountMapper.insert(wxServiceAccount);
        }

        return subscriptionInfoDTO;
    }

    /**
     * @param exWxMiniAccount
     * @param type            1订阅 0取消订阅
     * @return
     */
    public ReturnValue<?> httpSubscribe(WxMiniAccount exWxMiniAccount, int type) {

        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        if (exWxMiniAccount.getUserId() > 0) {
            param.put("userid", exWxMiniAccount.getUserId());
        }
        param.put("pm", exWxMiniAccount.getPm());
        param.put("pluginversion", exWxMiniAccount.getPluginversion());
        param.put("deviceid", exWxMiniAccount.getDeviceId());
        param.put("sourceid", exWxMiniAccount.getSourceId());
        param.put("biztype", exWxMiniAccount.getBizType());
        param.put("seriesid", exWxMiniAccount.getSeriesId());

        ReturnValue<?> returnValue = null;
        try {
            returnValue = httpService.httpPostFor(subscribeUrl, param, new TypeReference<ReturnValue<?>>() {
            });
            log.info("httpSubscribe 源接口调用返回:{} , url:{} , 参数:{} ", returnValue, subscribeUrl, JacksonHelper.serialize(param));
            return returnValue;
        } catch (Exception e) {
            log.error("httpSubscribe 源接口调用报错:{} , url:{} , 参数:{}  error", returnValue, subscribeUrl, JacksonHelper.serialize(param), e);
            return ReturnValue.buildErrorResult(100110, "请重新订阅");
        }
    }

}
