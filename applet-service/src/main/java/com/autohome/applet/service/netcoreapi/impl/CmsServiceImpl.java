package com.autohome.applet.service.netcoreapi.impl;

import com.autohome.applet.model.dto.netcoreapi.cms.GetFocusImgAdsDto;
import com.autohome.applet.service.netcoreapi.CmsService;
import com.autohome.applet.util.HttpHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class CmsServiceImpl implements CmsService {
    //获取广告
    @Value("${netcore.cms.getFocusImgAdsUrl:http://eda.autohome.com.cn/applet/deliver?series=}")
    private String getFocusImgAdsUrl;

    @Override
    public List<GetFocusImgAdsDto> getFocusImgAds(String psids, String userip, String userId) {
        String uuidN = UUID.randomUUID().toString().replaceAll("-", "");
        String param = "&psids=" + psids + "&platform=5&os=&pageid=" + uuidN + "&brand=&level=&city=&province=&ip=" + userip + "&wd=&wdt=&wdc=&sou=&fuel_type=&userid=" + userId;
        List<GetFocusImgAdsDto> result = HttpHelper.getInstance().httpGet(getFocusImgAdsUrl + param, null, null, new TypeReference<List<GetFocusImgAdsDto>>() {
        });
        return result;
    }
}
