package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl.buycar;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.CarCalculator;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCar4Service;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Slf4j
@Service
public class BuyCar4ServiceImpl implements BuyCar4Service {

    /**
     * 购车计算器接口
     *
     * @param carPrice
     * @param seatCount
     * @param specistaxexemption
     * @return
     */
    @Override
    public CarCalculator getCalculatorByCarInfo(String carPrice, String seatCount, String specistaxexemption) {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("specistaxexemption", specistaxexemption);
            param.put("carPrice", carPrice);
            param.put("seatCount", seatCount);
            param.put("_appid", "car");
            //筛选车系
//            String result = carApi.get("j.api.getCalculatorByCarInfo", param);
//            ResponseContent<CarCalculator> calculatorResponseContent = JSON.parseObject(result, new TypeReference<ResponseContent<CarCalculator>>() {
//            });

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://j.api.autohome.com.cn/api/loancar/getCalculatorByCarInfo", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }

            ResponseContent<CarCalculator> calculatorResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<CarCalculator>>() {
            });
            if (calculatorResponseContent == null || calculatorResponseContent.getResult() == null) {
                return null;
            }

            return calculatorResponseContent.getResult();
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("getCalculatorByCarInfo-WithException", e.getMessage())));
            log.error("getCalculatorByCarInfo-WithException", e);
            return null;
        }

    }
}
