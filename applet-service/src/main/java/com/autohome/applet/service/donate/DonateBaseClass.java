package com.autohome.applet.service.donate;

import com.autohome.applet.dao.javaapi.mapper.AppletUserbehaviorhistoryHashMapper;
import com.autohome.applet.dao.javaapi.mapper.AppletUserbehaviorhistorySeriesMapper;
import com.autohome.applet.dao.javaapi.mapper.DonateLogMapper;
import com.autohome.applet.dao.javaapi.model.AppletUserbehaviorhistoryHash;
import com.autohome.applet.dao.javaapi.model.AppletUserbehaviorhistorySeries;
import com.autohome.applet.dao.javaapi.model.DonateLog;
import com.autohome.applet.dao.javaapi.model.donate.DonateDaoQuery;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.donate.DonateCacheModel;
import com.autohome.applet.model.dto.donate.DonateQueryModel;
import com.autohome.applet.model.dto.maindata.*;
import com.autohome.applet.model.enums.DonateDataSourceEnum;
import com.autohome.applet.model.enums.DonateTerminalEnum;
import com.autohome.applet.model.enums.DonateViewMarkEnum;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.autohome.applet.util.DateHelper.convertFormatDate;

/**
 * ios spotlight及android donation捐献业务
 * */
@Slf4j
public abstract class DonateBaseClass {
    @Autowired
    private AppletUserbehaviorhistorySeriesMapper appletUserbehaviorhistorySeriesMapper;
    @Autowired
    private AppletUserbehaviorhistoryHashMapper appletUserbehaviorhistoryHashMapper;
    @Autowired
    private DonateLogMapper donateLogMapper;
    @Autowired
    private HttpService httpService;

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;
    //设置保存的总数据量
    protected final static int SAVETOTAL = 2000;
    //设置保存的分片数量
    protected final static int SAVETOTALTODB = 100;
    /**
     *
     * */
    private static List<DonateLog> IOSOPGCDONATELOGLIST = new CopyOnWriteArrayList<>();
    private static List<DonateLog> HUAWEIOPGCDONATELOGLIST = new CopyOnWriteArrayList<>();

    /**
     * 捐赠数据,第二部分,原创 + 车家号
     * */
    public abstract void doBusiness(DonateQueryModel donateQuery);

    /**
     * 删除30天前的数据
     * */
    protected int deleteData30(Integer donateType, String endDate){
       return donateLogMapper.deleteBatch(null, donateType, endDate);
    }

    /**
     * 填充捐赠数据
     * */
    protected List<DonateLog> fillDonateData(DonateQueryModel donateQuery){
        try{
            //0. 查询redis, 获取当前进度
            DonateCacheModel donateCacheModel = getDonateCacheModel(donateQuery.getDeviceHash());
            donateQuery.setDonateDataSourceEnum(donateCacheModel.getDonateDataSourceEnum());
            //判断从哪个数据源出数据
            //1. 查询行为-原创、车家号数据
            if(DonateDataSourceEnum.BEHAVIORHISTORY == donateCacheModel.getDonateDataSourceEnum()){
                List<AppletUserbehaviorhistoryHash> appletUserbehaviorhistoryHashList = getDonateData(donateQuery, donateCacheModel);
                if(!CollectionUtils.isEmpty(appletUserbehaviorhistoryHashList)){
                    //设置DonateCacheModel
                    donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.BEHAVIORHISTORY);
                    donateCacheModel.setSearchAfter(appletUserbehaviorhistoryHashList.get(appletUserbehaviorhistoryHashList.size() - 1).getId());
                    setDonateRedisInfo(donateCacheModel);
                    //设置缓存的total
                    setDonateRedisCount(donateCacheModel, appletUserbehaviorhistoryHashList.size());
                    return convertDonateLogDto(appletUserbehaviorhistoryHashList, donateQuery);
                }
                else{
                    //设置DonateCacheModel
                    donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.BEHAVIORHISTORYSERIES);
                    donateCacheModel.setSearchAfter(0);
                    setDonateRedisInfo(donateCacheModel);
                    return null;
                }

            }
            //2. 查询行为-车系数据
            else if(DonateDataSourceEnum.BEHAVIORHISTORYSERIES == donateCacheModel.getDonateDataSourceEnum()){
                List<AppletUserbehaviorhistorySeries> appletUserbehaviorhistorySeriesList = getDonateSeriesData(donateQuery, donateCacheModel);
                if(!CollectionUtils.isEmpty(appletUserbehaviorhistorySeriesList)){
                    //设置DonateCacheModel
                    donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.BEHAVIORHISTORYSERIES);
                    donateCacheModel.setSearchAfter(appletUserbehaviorhistorySeriesList.get(appletUserbehaviorhistorySeriesList.size() - 1).getId());
                    setDonateRedisInfo(donateCacheModel);
                    //设置缓存的total
                    setDonateRedisCount(donateCacheModel, appletUserbehaviorhistorySeriesList.size());
                    return convertDonateLogDtoForSeries(appletUserbehaviorhistorySeriesList, donateQuery);
                }
                else{
                    //设置DonateCacheModel
                    donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.OPGC);
                    donateCacheModel.setSearchAfter(0);
                    setDonateRedisInfo(donateCacheModel);
                    return null;
                }
            }
            //3. 查询原创+车家号
            else if(DonateDataSourceEnum.OPGC == donateCacheModel.getDonateDataSourceEnum()){
//            //todo 此处是测试代码
//            donateQuery.setPageSize(500);
//            donateCacheModel.setSearchAfter(0l);
                List<DonateLog> donateLogList = getDonateDataForOPGC(donateQuery, donateCacheModel);
                if(!CollectionUtils.isEmpty(donateLogList)){
                    //设置DonateCacheModel
                    donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.OPGC);
                    //此处为分析上线后的error日志，需要打印数据
                    try{
                        donateCacheModel.setSearchAfter(donateLogList.get(donateLogList.size() - 1).getId());
                    }
                    catch (Exception ex){
                        donateCacheModel.setSearchAfter(0);

                        DonateLog tmp = donateLogList.get(donateLogList.size() - 2);
                        log.warn("get SearchAfter, size: {}, obj:{}", donateLogList.size(), tmp == null? "null" : JacksonHelper.serialize(tmp));
                    }
                    setDonateRedisInfo(donateCacheModel);
                    //设置缓存的total
                    setDonateRedisCount(donateCacheModel, donateLogList.size());

//                //todo 此处是测试代码
//                donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.DEFAULT);
//                donateCacheModel.setSearchAfter(0);
                    setDonateRedisInfo(donateCacheModel);
                    return donateLogList;
                }
                else{
                    //设置DonateCacheModel
                    donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.DEFAULT);
                    donateCacheModel.setSearchAfter(0);
                    setDonateRedisInfo(donateCacheModel);
                    return null;
                }
            }
            //4. 查询固定数据
            else if(DonateDataSourceEnum.DEFAULT == donateCacheModel.getDonateDataSourceEnum()){
                List<DonateLog> donateLogList = fillDataForPart3(donateQuery);
                if(!CollectionUtils.isEmpty(donateLogList)){
                    //设置缓存的total
                    setDonateRedisCount(donateCacheModel, donateLogList.size());
                }
                //设置DonateCacheModel
                donateCacheModel.setDonateDataSourceEnum(DonateDataSourceEnum.OVER);
                donateCacheModel.setSearchAfter(0);
                setDonateRedisInfo(donateCacheModel);
                return donateLogList;
            }
        }
        catch (Exception ex){
            log.warn("fillDonateData no data, query:{}", JacksonHelper.serialize(donateQuery));
        }
        return null;
    }

    /**
     * 获取用户行为数据--原创+车家号
     * */
    protected List<AppletUserbehaviorhistoryHash> getDonateData(DonateQueryModel donateQuery, DonateCacheModel donateCacheModel){
        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().pageSize(donateQuery.getPageSize())
                .deviceId(donateQuery.getDeviceId())
                .deviceHashId(donateCacheModel.getDeviceHashCode())
                .searchAfter(donateCacheModel.getSearchAfter()).build();
        return appletUserbehaviorhistoryHashMapper.listByQuery(donateDaoQuery);
    }
    /**
     * 获取用户行为数据--车系
     * */
    protected List<AppletUserbehaviorhistorySeries> getDonateSeriesData(DonateQueryModel donateQuery, DonateCacheModel getDonateCacheModel){
        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().pageSize(donateQuery.getPageSize())
                .deviceId(donateQuery.getDeviceId())
                .deviceHashId(getDonateCacheModel.getDeviceHashCode())
                .searchAfter(getDonateCacheModel.getSearchAfter()).build();
        return appletUserbehaviorhistorySeriesMapper.listByQuery(donateDaoQuery);
    }
    /**
     * 获取原创+车家号
     * */
    protected List<DonateLog> getDonateDataForOPGC(DonateQueryModel donateQuery, DonateCacheModel donateCacheModel){
        List<DonateLog> donateLogList = getDonateDataForOPGCList(donateQuery);
        if(!CollectionUtils.isEmpty(donateLogList)){
            return donateLogList;
        }
        log.warn("getDonateDataForOPGC from db, query:{}", JacksonHelper.serialize(donateQuery));
        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().pageSize(donateQuery.getPageSize())
                .deviceId(donateQuery.getDeviceId())
                .deviceHashId(donateCacheModel.getDeviceHashCode())
                .donateType(donateQuery.getDonateTerminalEnum().getCode())
                .searchAfter(donateCacheModel.getSearchAfter()).build();
        donateLogList = donateLogMapper.selectByQuery(donateDaoQuery);
        if(!CollectionUtils.isEmpty(donateLogList)){
            donateLogList.stream().forEach(o -> o.setDataSource(donateCacheModel.getDonateDataSourceEnum().getCode()));
        }
        return donateLogList;
    }

    /**
     * 捐赠数据,第三部分,固定数据
     * */
    protected List<DonateLog> fillDataForPart3(DonateQueryModel donateQuery){
        List<DonateLog> donateLogList = new ArrayList<>();
        donateLogList.add(fillSalesListDataForPart3(donateQuery));
        donateLogList.add(fillSelectCarDataForPart3(donateQuery));
        donateLogList.add(fillBuycarDataForPart3(donateQuery));
        return donateLogList;
    }

    protected int saveLog(List<DonateLog> donateLogList){
        return donateLogMapper.insertBatch(donateLogList);
    }

//    /**
//     * 获取用户行为数据总数
//     * */
//    protected int countUserBehaviorHistory(DonateQueryModel donateQuery){
//        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().userId(donateQuery.getUserId()).page(donateQuery.getPage())
//                .beginDate(donateQuery.getBeginDate()).endDate(donateQuery.getEndDate()).daily(donateQuery.getDaily())
//                .deviceId(donateQuery.getDeviceId()).pageSize(donateQuery.getPageSize()).build();
//        return userBehaviorHistoryHashLogMapper.countByQuery(donateDaoQuery);
//    }
    /**
     * 获取车家号信息(包括视频、文章和图文)
     * */
    protected List<MainDataSourceDto<MainDataCheJiaHaoSourceDto>> getChejiahaoInfos(DonateQueryModel donateQuery){
        try {
            List<MainDataSourceDto<MainDataCheJiaHaoSourceDto>> mainDataSourceDtoList = getMainDataSourceItemDto(MainDataCheJiaHaoSourceDto.class, donateQuery);
            if(!CollectionUtils.isEmpty(mainDataSourceDtoList)){
                mainDataSourceDtoList.forEach(o -> {
                    String json = JacksonHelper.serialize(o.getSource());
                    o.setSource(JacksonHelper.deserialize(json, new TypeReference<MainDataCheJiaHaoSourceDto>() {
                    }));
                });
            }
            return mainDataSourceDtoList;
        } catch (BusinessException e) {
            log.error("getChejiahaoInfos ", e);
        }
        return null;
    }
    /**
     * 获取CMS信息(原创)
     * */
    protected List<MainDataSourceDto<MainDataCMSSourceDto>> getCmsInfos(DonateQueryModel donateQuery){
        try {
            List<MainDataSourceDto<MainDataCMSSourceDto>> mainDataSourceDtoList = getMainDataSourceItemDto(MainDataCMSSourceDto.class, donateQuery);
            if(!CollectionUtils.isEmpty(mainDataSourceDtoList)){
                mainDataSourceDtoList.forEach(o -> {
                    String json = JacksonHelper.serialize(o.getSource());
                    o.setSource(JacksonHelper.deserialize(json, new TypeReference<MainDataCMSSourceDto>() {
                    }));
                });
            }
            return mainDataSourceDtoList;
        } catch (BusinessException e) {
            log.error("getCmsInfos ", e);
        }
        return null;
    }
    /**
     * 获取原创视频信息(原创)
     * */
    protected List<MainDataSourceDto<MainDataVideoSourceDto>> getVideosInfos(DonateQueryModel donateQuery){
        try {
            List<MainDataSourceDto<MainDataVideoSourceDto>> mainDataSourceDtoList = getMainDataSourceItemDto(MainDataVideoSourceDto.class, donateQuery);
            if(!CollectionUtils.isEmpty(mainDataSourceDtoList)){
                mainDataSourceDtoList.forEach(o -> {
                    String json = JacksonHelper.serialize(o.getSource());
                    o.setSource(JacksonHelper.deserialize(json, new TypeReference<MainDataVideoSourceDto>() {
                    }));
                });
            }
            return mainDataSourceDtoList;
        } catch (BusinessException e) {
            log.error("getVideosInfos ", e);
        }
        return null;
    }

    protected void fillDonateLogFromCMS(DonateQueryModel donateQuery, List<DonateLog> donateLogList){
        if(donateLogList.size() >= donateQuery.getTotal()){
            return;
        }
        int total = countMainDataSourceItemDto(MainDataCMSSourceDto.class, donateQuery);
        donateQuery.setDonateViewMarkEnum(DonateViewMarkEnum.CMS);
        //存储到缓存中
        fillDonateLogList(total, donateQuery, donateLogList);
    }

    protected void fillDonateLogFromVideo(DonateQueryModel donateQuery, List<DonateLog> donateLogList){
        if(donateLogList.size() >= donateQuery.getTotal()){
            return;
        }
        int total = countMainDataSourceItemDto(MainDataVideoSourceDto.class, donateQuery);
        donateQuery.setDonateViewMarkEnum(DonateViewMarkEnum.VIDEO);
        //存储到缓存中
        fillDonateLogList(total, donateQuery, donateLogList);
    }

    protected void fillDonateLogFromChejiahao(DonateQueryModel donateQuery, List<DonateLog> donateLogList){
        if(donateLogList.size() >= donateQuery.getTotal()){
            return;
        }
        int total = countMainDataSourceItemDto(MainDataCheJiaHaoSourceDto.class, donateQuery);
        donateQuery.setDonateViewMarkEnum(DonateViewMarkEnum.CHEJIAHAO);
        //存储到缓存中
        fillDonateLogList(total, donateQuery, donateLogList);
    }

    private void fillDonateLogList(int total, DonateQueryModel donateQuery, List<DonateLog> donateLogList){
        try {
            for (int i = 1; i <= total / donateQuery.getPageSize() + 1; i++) {
                donateQuery.setPage(i);
                //转换对象
                List<DonateLog> tmpDonateLogList = convertDonateLogs(donateQuery);
                //存入日志表缓存
                donateLogList.addAll(tmpDonateLogList);
//                saveLog(tmpDonateLogList);
                //查询存入数量, 如果大于total, 退出
                if(donateLogList.size() >= donateQuery.getTotal()){
                    //已经满足数量要求, 不需要存储
                    break;
                }
            }
        } catch (BusinessException e) {
            log.error("saveDonateLogFromCMS ", e);
        }
    }

    protected void saveDonateLog(List<DonateLog> donateLogList){
        // 倒序排列, 目的是为了保证pv最大的数据, id最大, 为了查询时pv倒序排列
        Collections.reverse(donateLogList);
        try {
            for (int i = 1; i <= donateLogList.size() / SAVETOTALTODB + 1; i++) {
                // 分段取值
                List<DonateLog> tmpDonateLogList = donateLogList.stream()
                        .skip((i - 1) * SAVETOTALTODB)
                        .limit(SAVETOTALTODB)
                        .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(tmpDonateLogList)){
                    //存入日志表
                    saveLog(tmpDonateLogList);
                }
            }
        } catch (BusinessException e) {
            log.error("saveDonateLogFromCMS ", e);
        }
    }

    private List<DonateLog> convertDonateLogs(DonateQueryModel donateQuery){
        List<DonateLog> donateLogList = new ArrayList<>();
        if(DonateViewMarkEnum.CMS == donateQuery.getDonateViewMarkEnum()){
            List<MainDataSourceDto<MainDataCMSSourceDto>> objList = getCmsInfos(donateQuery);
            if(!CollectionUtils.isEmpty(objList)){
                objList.forEach(o -> {
                    DonateLog tmpDonateLog = convertCmsDonateLog(o, donateQuery);
                    if(tmpDonateLog != null){
                        donateLogList.add(tmpDonateLog);
                    }
                });
            }
        }
        else if(DonateViewMarkEnum.VIDEO == donateQuery.getDonateViewMarkEnum()){
            List<MainDataSourceDto<MainDataVideoSourceDto>> objList = getVideosInfos(donateQuery);
            if(!CollectionUtils.isEmpty(objList)){
                objList.forEach(o -> {
                    DonateLog tmpDonateLog = convertVideoDonateLog(o, donateQuery);
                    if(tmpDonateLog != null){
                        donateLogList.add(tmpDonateLog);
                    }
                });
            }
        }
        else if(DonateViewMarkEnum.CHEJIAHAO == donateQuery.getDonateViewMarkEnum()){
            List<MainDataSourceDto<MainDataCheJiaHaoSourceDto>> objList = getChejiahaoInfos(donateQuery);
            if(!CollectionUtils.isEmpty(objList)){
                objList.forEach(o -> {
                    DonateLog tmpDonateLog = convertChejiahaoDonateLog(o, donateQuery);
                    if(tmpDonateLog != null){
                        donateLogList.add(tmpDonateLog);
                    }
                });
            }
        }
        return donateLogList;
    }

    private DonateLog convertCmsDonateLog(MainDataSourceDto<MainDataCMSSourceDto> mainDataSourceDto, DonateQueryModel donateQuery){
        if(mainDataSourceDto != null){
//            String imgUrl4x3 = mainDataSourceDto.getSource().getImgUrl4x3();
//            if(StringUtils.isEmpty(imgUrl4x3)){
//                return null;
//            }
            String scheme = getMainDataSourceDtoSchemeCMS(mainDataSourceDto.getSource());
            if(StringUtils.isEmpty(scheme)){
                return null;
            }
            DonateLog donateLog = new DonateLog();
            donateLog.setDataUrl(scheme);
            donateLog.setUniqueldentifier(donateQuery.getDonateTerminalEnum().getCode() + "_" + DonateViewMarkEnum.CMS.getCode() + "_" + mainDataSourceDto.getSource().getBizId());
            donateLog.setTitle(mainDataSourceDto.getSource().getTitle());
            donateLog.setDonateType(donateQuery.getDonateTerminalEnum().getCode());
            donateLog.setMetadataModificationDate(DateHelper.deserialize(mainDataSourceDto.getSource().getBizUpdateTime(), DateHelper.DATEFORMAT_NEW));
            BigDecimal bd1 = new BigDecimal(mainDataSourceDto.getSource().getPv());
            BigDecimal bd2 = new BigDecimal(100000);
            donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
            donateLog.setKeyWords(mainDataSourceDto.getSource().getCmsTags());
            donateLog.setDescribe(mainDataSourceDto.getSource().getSmallTitle());
            donateLog.setDaily(donateQuery.getDaily());
            donateLog.setViewMark(donateQuery.getDonateViewMarkEnum().getCode());
            //缩略图地址（尺寸：48*48),20k以下
            donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");
            donateLog.setDataSource(donateQuery.getDonateDataSourceEnum().getCode());
            return donateLog;
        }
        return null;
    }

    private DonateLog convertVideoDonateLog(MainDataSourceDto<MainDataVideoSourceDto> mainDataSourceDto, DonateQueryModel donateQuery){
        if(mainDataSourceDto != null){
//            String imgUrl4x3 = mainDataSourceDto.getSource().getImgUrl4x3();
//            if(StringUtils.isEmpty(imgUrl4x3)){
//                return null;
//            }
            //scheme
            String scheme = getMainDataSourceDtoSchemeVideo(mainDataSourceDto.getSource());
            if(StringUtils.isEmpty(scheme)){
                return null;
            }
            DonateLog donateLog = new DonateLog();
            donateLog.setDataUrl(scheme);
            donateLog.setUniqueldentifier(donateQuery.getDonateTerminalEnum().getCode() + "_" + DonateViewMarkEnum.VIDEO.getCode() + "_" + mainDataSourceDto.getSource().getBizId());
            donateLog.setTitle(mainDataSourceDto.getSource().getTitle());
            donateLog.setDonateType(donateQuery.getDonateTerminalEnum().getCode());
            donateLog.setMetadataModificationDate(DateHelper.deserialize(mainDataSourceDto.getSource().getBizUpdateTime(), DateHelper.DATEFORMAT_NEW));
            BigDecimal bd1 = new BigDecimal(mainDataSourceDto.getSource().getPv());
            BigDecimal bd2 = new BigDecimal(100000);
            donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
            if(!CollectionUtils.isEmpty(mainDataSourceDto.getSource().getVideoOriginalTagNames())){
                donateLog.setKeyWords(String.join(",", mainDataSourceDto.getSource().getVideoOriginalTagNames()));
            }
            donateLog.setDescribe(mainDataSourceDto.getSource().getSummary());
            donateLog.setDaily(donateQuery.getDaily());
            donateLog.setViewMark(donateQuery.getDonateViewMarkEnum().getCode());
            //缩略图地址（尺寸：48*48),20k以下
            donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");
            donateLog.setDataSource(donateQuery.getDonateDataSourceEnum().getCode());
            return donateLog;
        }
        return null;
    }

    private DonateLog convertChejiahaoDonateLog(MainDataSourceDto<MainDataCheJiaHaoSourceDto> mainDataSourceDto, DonateQueryModel donateQuery){
        if(mainDataSourceDto != null){
//            String imgUrl = mainDataSourceDto.getSource().getImgUrl();
//            if(StringUtils.isEmpty(imgUrl)){
//                return null;
//            }
            //scheme
            String scheme = getMainDataSourceDtoSchemeChejiahao(mainDataSourceDto.getSource());
            if(StringUtils.isEmpty(scheme)){
                return null;
            }
            DonateLog donateLog = new DonateLog();
            donateLog.setDataUrl(scheme);
            donateLog.setUniqueldentifier(donateQuery.getDonateDataSourceEnum().getCode() + "_" + DonateViewMarkEnum.CHEJIAHAO.getCode() + "_" + mainDataSourceDto.getSource().getBizId());
            donateLog.setTitle(mainDataSourceDto.getSource().getTitle());
            donateLog.setDonateType(donateQuery.getDonateTerminalEnum().getCode());
            donateLog.setMetadataModificationDate(DateHelper.deserialize(mainDataSourceDto.getSource().getBizUpdateTime(), DateHelper.DATEFORMAT_NEW));
            BigDecimal bd1 = new BigDecimal(mainDataSourceDto.getSource().getPv()==null?0:mainDataSourceDto.getSource().getPv());
            BigDecimal bd2 = new BigDecimal(100000);
            donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
            if(!CollectionUtils.isEmpty(mainDataSourceDto.getSource().getChejiahaoTagNames())){
                donateLog.setKeyWords(String.join(",", mainDataSourceDto.getSource().getChejiahaoTagNames()));
            }
            donateLog.setDescribe(mainDataSourceDto.getSource().getSummary());
            donateLog.setDaily(donateQuery.getDaily());
            //缩略图地址（尺寸：48*48),20k以下
            donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");

            donateLog.setDataSource(donateQuery.getDonateDataSourceEnum().getCode());
            //存储数据类型
            if(mainDataSourceDto.getSource().getPoolBizTypes().contains(14)){
                donateLog.setViewMark("14");
            }
            else if(mainDataSourceDto.getSource().getPoolBizTypes().contains(12)){
                donateLog.setViewMark("12");
            }
            else if(mainDataSourceDto.getSource().getPoolBizTypes().contains(13)){
                donateLog.setViewMark("13");
            }
            return donateLog;
        }
        return null;
    }


    private <T> int countMainDataSourceItemDto(Class<T> clazz, DonateQueryModel donateQuery){
        //查询总数需要把page传1
        donateQuery.setPage(1);
        MainDataResultHitsDto mainDataResultHitsDto = getMainDataResultHitsDto(clazz, donateQuery);
        if(mainDataResultHitsDto != null){
            return mainDataResultHitsDto.getHits().getTotal();
        }
        return 0;
    }

    private <T> List<MainDataSourceDto<T>> getMainDataSourceItemDto(Class<T> clazz, DonateQueryModel donateQuery){
        MainDataResultHitsDto mainDataResultHitsDto = getMainDataResultHitsDto(clazz, donateQuery);
        if(mainDataResultHitsDto != null){
            return mainDataResultHitsDto.getHits().getHits();
        }
        return null;
    }
    private <T> MainDataResultHitsDto<T> getMainDataResultHitsDto(Class<T> clazz, DonateQueryModel donateQuery) {
        String url = "";
        if (clazz.equals(MainDataCheJiaHaoSourceDto.class)) {
            url = "http://la.corpautohome.com/userincr/chejiahao_info_applet?";
        } else if (clazz.equals(MainDataCMSSourceDto.class)) {
            url = "http://la.corpautohome.com/skynet/cms_info_applet?";
        } else if (clazz.equals(MainDataVideoSourceDto.class)) {
            url = "http://la.corpautohome.com/userincr/video_info_applet?";
        }
        Map<String, Object> param = new HashMap<>();
        url = url +
                "_appid=*";


        if (StringUtils.isNotBlank(donateQuery.getBizIds())) {
            url = url + "&biz_ids=" + donateQuery.getBizIds();
        } else {
            url = url + "end_time=" + donateQuery.getEndDate()
                    + "&begin_time=" + donateQuery.getBeginDate()
                    + "&page_size=" + donateQuery.getPageSize()
                    + "&page_num=" + donateQuery.getPage();
        }
        MainDataResultHitsDto<T> mainDataResultHitsDto = httpService.httpGetForReturnValue(url, param
                , new TypeReference<ReturnValue<MainDataResultHitsDto<T>>>() {
                });
        if (mainDataResultHitsDto == null) {
            return null;
        }
        log.info("request getMainDataResultHitsDtoFor url:{},response:{},total:{}", url, mainDataResultHitsDto, mainDataResultHitsDto.getHits().getTotal());
        return mainDataResultHitsDto;
    }

//    /**
//     * 判断是否数量已经满足需求
//     * result: true-满足,不需要再存储; false-不满足, 还需要继续组装数据
//     * */
//    protected boolean checkTotal(DonateQueryModel donateQuery){
//        //查询存入数量, 如果大于total, 退出
//        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().daily(donateQuery.getDaily())
//                .donateType(donateQuery.getDonateTerminalEnum().getCode()).build();
//        int totalCount = donateLogMapper.countByQuery(donateDaoQuery);
//        if(totalCount >= donateQuery.getTotal()){
//            //已经满足数量要求
//            return true;
//        }
//        return false;
//    }
    /**
     * 删除当天的捐赠数据
     * */
    protected void deleteDonate(String daily, DonateTerminalEnum donateTerminalEnum, String endDate){
        donateLogMapper.deleteBatch(daily, donateTerminalEnum.getCode(), endDate);
    }

    /**
     * 删除当天的捐赠数据
     * */
    protected void deleteDonate(String daily, DonateTerminalEnum donateTerminalEnum){
        donateLogMapper.deleteBatch(daily, donateTerminalEnum.getCode(), null);
    }

    /**
     * wiki: https://zhishi.autohome.com.cn/home/<USER>/file?targetId=106309219
     * */

    private String getMainDataSourceDtoSchemeCMS(MainDataCMSSourceDto mainDataCMSSourceDto){
        String scheme = "autohome://article/articledetailcolumn?newsid="+ mainDataCMSSourceDto.getBizId()
                +"&newstype=0&lastupdatetime="+ convertFormatDate(mainDataCMSSourceDto.getBizUpdateTime(), DateHelper.DATEFORMAT_NEW, DateHelper.DATEFORMAT_STANDARD_2)
                +"&pvid=X&mediatype=0";
        return scheme;
    }
    private String getMainDataSourceDtoSchemeVideo(MainDataVideoSourceDto mainDataVideoSourceDto){
        String scheme = "autohome://article/videodetail?newsid="+ mainDataVideoSourceDto.getBizId() +"&mediatype=3";
        return scheme;
    }
    private String getMainDataSourceDtoSchemeChejiahao(MainDataCheJiaHaoSourceDto mainDataCheJiaHaoSourceDto){
        String scheme = null;
        if(CollectionUtils.isEmpty(mainDataCheJiaHaoSourceDto.getPoolBizTypes())){
            return null;
        }
        if(mainDataCheJiaHaoSourceDto.getPoolBizTypes().contains(14)){
            //车家号-视频
            scheme = "autohome://article/videodetail?newsid="+ mainDataCheJiaHaoSourceDto.getBizId() +"&mediatype=14";
        }
        else if(mainDataCheJiaHaoSourceDto.getPoolBizTypes().contains(12)){
            //车家号-文章/长文
            scheme = "autohome://article/articledetailcolumn?newsid="+ mainDataCheJiaHaoSourceDto.getBizId()
                    +"&newstype=12&lastupdatetime="+ convertFormatDate(mainDataCheJiaHaoSourceDto.getBizUpdateTime(), DateHelper.DATEFORMAT_NEW, DateHelper.DATEFORMAT_STANDARD_2)
                    +"&pvid=X&mediatype=12";
        }
        else if(mainDataCheJiaHaoSourceDto.getPoolBizTypes().contains(13)){
            //车家号-图文/轻文
            scheme = "autohome://article/articledetailcolumn?newsid="+ mainDataCheJiaHaoSourceDto.getBizId()
                    +"&newstype=13&lastupdatetime="+ convertFormatDate(mainDataCheJiaHaoSourceDto.getBizUpdateTime(), DateHelper.DATEFORMAT_NEW, DateHelper.DATEFORMAT_STANDARD_2)
                    +"&pvid=X&mediatype=13";
        }
        return scheme;
    }
    private String getMainDataSourceDtoSchemeSeries(Integer seriesId, String seriesName){
        String scheme = null;
        //series 用户行为——车系
        try{
            scheme = "autohome://car/seriesmain?seriesid="+ seriesId;
        }
        catch (Exception ex){
            log.error("getMainDataSourceDtoScheme scheme error, sid:{}, sname:{}", seriesId, seriesName, ex);
        }
        return scheme;
    }

    /**
     * 第三部分-销量榜
     * */
    private DonateLog fillSalesListDataForPart3(DonateQueryModel donateQuery){
        DonateLog donateLog = new DonateLog();
        donateLog.setDataUrl("autohome://car/recmainrank?from=0&typeid=1");
        donateLog.setUniqueldentifier(donateQuery.getDonateTerminalEnum().getCode() + "_paihangbang");
        donateLog.setTitle("汽车之家销量榜");
        donateLog.setDonateType(donateQuery.getDonateTerminalEnum().getCode());
        donateLog.setMetadataModificationDate(DateHelper.getNow());
        BigDecimal bd1 = new BigDecimal(1);
        BigDecimal bd2 = new BigDecimal(100000);
        donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
        donateLog.setKeyWords("汽车之家,之家,销量,汽车,qiche,懂车帝,易车");
        donateLog.setDescribe("最新汽车销量排行，热门车型引领潮流");
        donateLog.setDaily(donateQuery.getDaily());
        //缩略图地址（尺寸：48*48),20k以下
        donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");
        donateLog.setDataSource("02");
        donateLog.setViewMark("20");
        return donateLog;
    }

    /**
     * 第三部分-选车
     * */
    private DonateLog fillSelectCarDataForPart3(DonateQueryModel donateQuery){
        DonateLog donateLog = new DonateLog();
        donateLog.setDataUrl("autohome://apptabswitch?tab=car&tabsecret=2EF828C525E84597D27A509E43BC5728");
        donateLog.setUniqueldentifier(donateQuery.getDonateTerminalEnum().getCode() + "_xuanche");
        donateLog.setTitle("汽车之家车型大全");
        donateLog.setDonateType(donateQuery.getDonateTerminalEnum().getCode());
        donateLog.setMetadataModificationDate(DateHelper.getNow());
        BigDecimal bd1 = new BigDecimal(2);
        BigDecimal bd2 = new BigDecimal(100000);
        donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
        donateLog.setKeyWords("汽车之家,之家,车型,汽车,qiche,懂车帝,易车");
        donateLog.setDescribe("全量车型，价格、配置、图片一览无余");
        donateLog.setDaily(donateQuery.getDaily());
        //缩略图地址（尺寸：48*48),20k以下
        donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");
        donateLog.setDataSource("02");
        donateLog.setViewMark("21");
        return donateLog;
    }

    /**
     * 第三部分-购车
     * */
    private DonateLog fillBuycarDataForPart3(DonateQueryModel donateQuery){
        DonateLog donateLog = new DonateLog();
        donateLog.setDataUrl("autohome://insidebrowser?url=https%3A%2F%2Fdealer.m.autohome.com.cn%2Fjiajiago%2Fbrandlist%3Fpvareaid%3D6861515");
        donateLog.setUniqueldentifier(donateQuery.getDonateTerminalEnum().getCode() + "_buycar");
        donateLog.setTitle("汽车之家购车优惠");
        donateLog.setDonateType(donateQuery.getDonateTerminalEnum().getCode());
        donateLog.setMetadataModificationDate(DateHelper.getNow());
        BigDecimal bd1 = new BigDecimal(3);
        BigDecimal bd2 = new BigDecimal(100000);
        donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
        donateLog.setKeyWords("汽车之家,之家,优惠,购车优惠,汽车,qiche,懂车帝,易车");
        donateLog.setDescribe("您买车，我补贴，优惠多多！");
        donateLog.setDaily(donateQuery.getDaily());
        //缩略图地址（尺寸：48*48),20k以下
        donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");
        donateLog.setDataSource("02");
        donateLog.setViewMark("22");
        return donateLog;
    }

    private DonateCacheModel getDonateCacheModel(int deviceIdHashCode){
        String key = getDonateRedisKey(deviceIdHashCode, 1);
        String value = redisClient.get(key);
        if(StringUtils.isEmpty(value)){
            return DonateCacheModel.buildDefaultResult(deviceIdHashCode);
        }
        String[] tmp = value.split("_");
        if(tmp != null && tmp.length != 3){
            return DonateCacheModel.buildDefaultResult(deviceIdHashCode);
        }
        return DonateCacheModel.builder()
                .deviceHashCode(Integer.valueOf(tmp[0]))
                .donateDataSourceEnum(DonateDataSourceEnum.fromCode(tmp[1]))
                .searchAfter(Long.valueOf(tmp[2]))
                .build();
    }

    /**
     * key: deviceidhash_daily
     * keyType: 1,捐赠标志信息; 2,捐赠总数
     * */
    protected String getDonateRedisKey(int deviceIdHashCode, int keyType){
        if(keyType == 1){
            return deviceIdHashCode + ":info:" + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE);
        }
        else if(keyType == 2){
            return deviceIdHashCode + ":total:" + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE);
        }
        return null;
    }
    /**
     * 将当前状态存入redis,
     * key: deviceidhash_info_daily
     * value: devicehashcode_type_searchafter
     *      type: DonateDataSourceEnum
     * expire: 1天过期
     * */
    private void setDonateRedisInfo(DonateCacheModel donateCacheModel){
        String key = getDonateRedisKey(donateCacheModel.getDeviceHashCode(), 1);
        redisClient.set(key, donateCacheModel.getDeviceHashCode()+"_"+donateCacheModel.getDonateDataSourceEnum().getCode()+"_"+donateCacheModel.getSearchAfter(), 1, TimeUnit.DAYS);
    }

    /**
     * 将当前捐赠总数存入redis,
     * key: deviceidhash_total_daily
     * value: 总数
     * expire: 1天过期
     * */
    private void setDonateRedisCount(DonateCacheModel donateRedisInfo, int count){
        String key = getDonateRedisKey(donateRedisInfo.getDeviceHashCode(), 2);
        redisClient.incrementValueBy(key, count);
        redisClient.expireEntryIn(key, 1, TimeUnit.DAYS);
    }

    /**
     * 用户行为转donatelog, 需要调用主数据接口
     * */
    private List<DonateLog> convertDonateLogDto(List<AppletUserbehaviorhistoryHash> appletUserbehaviorhistoryHashList, DonateQueryModel donateQuery){
        if(CollectionUtils.isEmpty(appletUserbehaviorhistoryHashList)){
            return new ArrayList<>();
        }
        List<DonateLog> donateLogList = new ArrayList<>();
        if(CollectionUtils.isEmpty(appletUserbehaviorhistoryHashList)){
            return donateLogList;
        }
        //查询原创文章
        donateLogList.addAll(convertDonateLogDto(appletUserbehaviorhistoryHashList, DonateViewMarkEnum.CMS, donateQuery));
        //查询原创视频
        donateLogList.addAll(convertDonateLogDto(appletUserbehaviorhistoryHashList, DonateViewMarkEnum.VIDEO, donateQuery));
        //查询车家号
        donateLogList.addAll(convertDonateLogDto(appletUserbehaviorhistoryHashList, DonateViewMarkEnum.CHEJIAHAO, donateQuery));
        return donateLogList;
    }
    /**
     * 用户行为(车系)转donatelog
     * */
    private List<DonateLog> convertDonateLogDtoForSeries(List<AppletUserbehaviorhistorySeries> appletUserbehaviorhistorySeries, DonateQueryModel donateQuery){
        if(CollectionUtils.isEmpty(appletUserbehaviorhistorySeries)){
            return new ArrayList<>();
        }
        List<DonateLog> donateLogList = new ArrayList<>();
        if(CollectionUtils.isEmpty(appletUserbehaviorhistorySeries)){
            return donateLogList;
        }
        //
        return appletUserbehaviorhistorySeries.stream().map(o -> {
            DonateLog donateLog = new DonateLog();
            donateLog.setDataUrl(getMainDataSourceDtoSchemeSeries(o.getSeriesId(), o.getSeriesName()));
            donateLog.setUniqueldentifier(o.getSeriesId() + "_series");
            donateLog.setTitle(o.getSeriesName());
            donateLog.setMetadataModificationDate(DateHelper.getNow());
            BigDecimal bd1 = new BigDecimal(2);
            BigDecimal bd2 = new BigDecimal(100000);
            donateLog.setRankingHint(bd1.divide(bd2, 6, RoundingMode.HALF_UP));
            donateLog.setKeyWords(o.getSeriesName() + "," + o.getSeriesName()+"新能源");
            donateLog.setDescribe("最近成交价、购车优惠、口碑尽在汽车之家");
            //缩略图地址（尺寸：48*48),20k以下
            donateLog.setLogoUrl("https://m1.autoimg.cn/clubdfs/g31/M0B/01/E8/48x48_f41_autohomecar__ChxoHmXUEE6AO3GtAAEGXAiUNrI424.jpg");
            donateLog.setViewMark(DonateViewMarkEnum.SERIES.getCode());
            donateLog.setDataSource(donateQuery.getDonateDataSourceEnum().getCode());
            return donateLog;
        }).collect(Collectors.toList());
    }


    private List<DonateLog> convertDonateLogDto(List<AppletUserbehaviorhistoryHash> appletUserbehaviorhistoryHashList, DonateViewMarkEnum donateViewMarkEnum, DonateQueryModel donateQuery){
        List<DonateLog> donateLogList = new ArrayList<>();
        String[] tmpArray;
        if(donateViewMarkEnum == DonateViewMarkEnum.CHEJIAHAO){
            tmpArray = new String[] { "08","09","10" };
        } else {
            tmpArray = new String[]{donateViewMarkEnum.getCode()};
        }
        //查询原创文章
        List<Integer> objectIds = appletUserbehaviorhistoryHashList.stream().filter(o -> Arrays.asList(tmpArray).contains(o.getViewMark())).map(o -> o.getObjectId()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(objectIds)){
            List<DonateLog> tmpDonateLogList = convertDonateLogs(DonateQueryModel.builder().page(1). pageSize(SAVETOTAL).bizIds(objectIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                    .beginDate(DateHelper.serialize(DateHelper.beforeNDaysDate(30))).endDate(DateHelper.getNowString(DateHelper.DATEFORMAT_STANDARD)).donateViewMarkEnum(donateViewMarkEnum)
                    .donateDataSourceEnum(DonateDataSourceEnum.BEHAVIORHISTORY).donateTerminalEnum(donateQuery.getDonateTerminalEnum()).build());
            if(!CollectionUtils.isEmpty(tmpDonateLogList)){
                donateLogList.addAll(tmpDonateLogList);
            }
        }
        return donateLogList;
    }

    protected String convertDataSource(String donateLogDataSource) {
        switch (donateLogDataSource) {
            case "0":
            case "1": {
                return "01";
            }
            default: {
                return "02";
            }
        }
    }

    private List<DonateLog> getDonateDataForOPGCList(DonateQueryModel donateQuery){
        if(DonateTerminalEnum.HUAWEI.equals(donateQuery.getDonateTerminalEnum())){
            if(CollectionUtils.isEmpty(HUAWEIOPGCDONATELOGLIST)){
                //初始化
                initHuaWeiDonateDataForOPGC();
            }
            if(!CollectionUtils.isEmpty(HUAWEIOPGCDONATELOGLIST)){
                int size = donateQuery.getPageSize() >= HUAWEIOPGCDONATELOGLIST.size() ? donateQuery.getPageSize() : HUAWEIOPGCDONATELOGLIST.size();
                return HUAWEIOPGCDONATELOGLIST.stream().skip(0).limit(size).collect(Collectors.toList());
            }
        }
        else{
            if(CollectionUtils.isEmpty(IOSOPGCDONATELOGLIST)){
                //初始化
                initIosDonateDataForOPGC();
            }
            if(!CollectionUtils.isEmpty(IOSOPGCDONATELOGLIST)){
                int size = donateQuery.getPageSize() >= IOSOPGCDONATELOGLIST.size() ? donateQuery.getPageSize() : IOSOPGCDONATELOGLIST.size();
                return IOSOPGCDONATELOGLIST.stream().skip(0).limit(size).collect(Collectors.toList());
            }
        }
        return null;
    }

    /**
     * 初始划捐赠接口， 每小时执行一次， 把原创和车家号数据存储到内存中
     * */
    @Scheduled(cron = "10 10 * * * ?")
    private void initHuaWeiDonateDataForOPGC(){
        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().pageSize(SAVETOTAL)
                .donateType(DonateTerminalEnum.HUAWEI.getCode()).build();
        List<DonateLog> donateLogList = donateLogMapper.selectByQuery(donateDaoQuery);
        if(!CollectionUtils.isEmpty(donateLogList)){
            donateLogList.stream().forEach(o -> o.setDataSource(DonateDataSourceEnum.OPGC.getCode()));
            HUAWEIOPGCDONATELOGLIST = donateLogList;
            log.info("initHuaWeiDonateDataForOPGC, size:{}", HUAWEIOPGCDONATELOGLIST.size());
        }
        else{
            log.warn("initHuaWeiDonateDataForOPGC, no data");
        }
    }
    @Scheduled(cron = "20 20 * * * ?")
    private void initIosDonateDataForOPGC(){
        DonateDaoQuery donateDaoQuery = DonateDaoQuery.builder().pageSize(SAVETOTAL)
                .donateType(DonateTerminalEnum.APPLE.getCode()).build();
        List<DonateLog> donateLogList = donateLogMapper.selectByQuery(donateDaoQuery);
        if(!CollectionUtils.isEmpty(donateLogList)){
            donateLogList.stream().forEach(o -> o.setDataSource(DonateDataSourceEnum.OPGC.getCode()));
            IOSOPGCDONATELOGLIST = donateLogList;
            log.info("initIosDonateDataForOPGC, size:{}", IOSOPGCDONATELOGLIST.size());
        }
        else{
            log.warn("initIosDonateDataForOPGC, no data");
        }
    }
}
