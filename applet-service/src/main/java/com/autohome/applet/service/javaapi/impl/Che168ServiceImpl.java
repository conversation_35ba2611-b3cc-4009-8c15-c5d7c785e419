package com.autohome.applet.service.javaapi.impl;


import com.autohome.applet.service.javaapi.Che168Service;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.comm.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class Che168ServiceImpl implements Che168Service {

    public boolean check_validcode(String phone_num, String validcode) {
        String url = "http://api2scauto.lq.che168.com/autom/checkvalidcode.ashx";
        Map<String, String> params = new HashMap<>();
        params.put("_appid", "m");
        params.put("mobile", phone_num);
        params.put("validcode", validcode);
        String resultStr = HttpClientUtils.postForm(url, params, null, 1000, 1000);

        if (StringUtils.isNullOrEmpty(resultStr)) {
            return false;
        }

        JSONObject result = new JSONObject(resultStr);
        if (result == null || result.getInt("returncode") != 0 || result.getJSONObject("result") == null || result.getJSONObject("result").getInt("flag") != 1) {
            return false;
        }
        return true;
    }



}
