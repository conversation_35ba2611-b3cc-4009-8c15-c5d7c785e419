package com.autohome.applet.service.wxapi.impl;

import com.autohome.applet.dao.javaapi.model.wxapp.*;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.wxapi.NewsService;
import com.autohome.applet.service.wxapi.RichTextParser;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * Created by pengyi on 2016/12/9.
 */
@Slf4j
@Service
public class NewsServiceImpl implements NewsService {
    private static final String ARTICLE_DETAILS_URL = "http://cms.api.autohome.com.cn/baseapi/articles/articledetails.ashx";
    private static final String LIKE_LIST_URL = "http://reply.autohome.com.cn/api/like/listcount.ashx";
    private static final String VIDEO_CONTENT_URL = "http://cms.api.autohome.com.cn/baseapi/video/videocontent.ashx";
    private static final String VIDEO_ADDRESS_URL = "http://p-vp.lq.autohome.com.cn/api/mpi";
    private static final String APPID = "wxapp";

    @Autowired
    private HttpService httpService;

    @Override
    public ReturnValue<DetailModel> getArticleDetails(int newsid) {
        HashMap<String, Object> queryString = new HashMap<>(4);
        queryString.put("newid", newsid + "");
        queryString.put("pageindex", "0");//获取本文章全部内容,pageindex传0
        queryString.put("_appid", APPID);
        queryString.put("returnvideoinfo", "1");
        ReturnValue<ArticleDetailsResult> response =
                httpService.httpGetFor(ARTICLE_DETAILS_URL, queryString, new TypeReference<ReturnValue<ArticleDetailsResult>>() {});
        ReturnValue<DetailModel> result = null;
        if (response != null) {
            result = new ReturnValue<>();
            result.setReturncode(response.getReturncode());
            result.setMessage(response.getMessage());
            ArticleDetailsResult articleDetailsResult = response.getResult();
            if (articleDetailsResult != null) {
                DetailModel detailModel = new DetailModel();
                detailModel.setName(articleDetailsResult.getAuthor());
                detailModel.setTitle(articleDetailsResult.getTitle());
                detailModel.setPublishTime(articleDetailsResult.getPublishTime());
                detailModel.setId(articleDetailsResult.getId());
                detailModel.setContentCards(RichTextParser.parseDetail(response.getResult().getContent()));
                detailModel.setImg(articleDetailsResult.getImg());
                detailModel.setReplyCount(articleDetailsResult.getReplyCount());
                ReturnValue<LikeDetail[]> likeDetails = getLikeList(1, 1, "[{\"objid\":" + newsid + ",\"secobj\":\"\"}]");
                if (likeDetails != null && likeDetails.getResult() != null && likeDetails.getResult().length > 0 &&
                        likeDetails.getResult()[0].getObjid() == newsid) {
                    detailModel.setLikeCount(likeDetails.getResult()[0].getCount());
                }
                result.setResult(detailModel);
            }
        }
        return result;
    }

    @Override
    public ReturnValue<DetailModel> getVideoDetails(int videoid) {
        ReturnValue<VideoContentResult> response = getVideoContent(videoid);
        if (response == null || response.getResult() == null) {
            return ReturnValue.buildErrorResult(500, "获得视频详情失败");
        }
        ReturnValue<DetailModel> detailModelReturnValue = new ReturnValue<>();
        DetailModel detailModel = new DetailModel();
        detailModel.setId(videoid);
        detailModel.setPublishTime(response.getResult().getInputtime());
        detailModel.setName(response.getResult().getNickName());
        detailModel.setReplyCount(response.getResult().getCommentnum());
        detailModel.setTitle(response.getResult().getTitle());
        DetailModel.Video video = new DetailModel.Video();
        video.setImg(response.getResult().getPicurl());
        detailModel.setVideo(video);
        detailModel.setContentCards(RichTextParser.parseDetail(response.getResult().getDescription()));
        String videoId = response.getResult().getVideoid();
        if (videoId == null || videoId.isEmpty()) {
            return ReturnValue.buildErrorResult(501, "视频不存在");
        }
        ReturnValue<VideoAddressResult> result = getVideoAddress(videoId);
        if (result != null && result.getResult() != null && result.getResult().getMediainfos() != null &&
                result.getResult().getMediainfos().length > 0) {
            if (result.getResult().getMediainfos()[0].getCopies() != null && result.getResult().getMediainfos()[0].getCopies().length > 0) {
                detailModel.getVideo().setPlayUrl(result.getResult().getMediainfos()[0].getCopies()[0].getPlayurl());
                detailModel.getVideo().setImg(result.getResult().getMediainfos()[0].getImg());
            }
        }
        ReturnValue<LikeDetail[]> likeDetails = getLikeList(4, 1, "[{\"objid\":" + videoid + ",\"secobj\":\"\"}]");
        if (likeDetails != null && likeDetails.getResult() != null && likeDetails.getResult().length > 0 &&
                likeDetails.getResult()[0].getObjid() == videoid) {
            detailModel.setLikeCount(likeDetails.getResult()[0].getCount());
        }
        detailModelReturnValue.setResult(detailModel);
        return detailModelReturnValue;
    }

    public ReturnValue<LikeDetail[]> getLikeList(int appid, int liketype, String ids) {
        HashMap<String, Object> queryString = new HashMap<>(4);
        queryString.put("appid", appid + "");
        queryString.put("liketype", liketype + "");
        queryString.put("ids", ids);// [{"objid":1,"secobj":""},{"objid":2,"secobj":""},{"objid":3,"secobj":""}]
        queryString.put("datatype", "json");
        return httpService.httpGetFor(LIKE_LIST_URL, queryString, new TypeReference<ReturnValue<LikeDetail[]>>() {});
    }

    public ReturnValue<VideoContentResult> getVideoContent(int videoid) {
        HashMap<String, Object> queryString = new HashMap<>(2);
        queryString.put("id", videoid + "");
        queryString.put("_appid", APPID);
        return httpService.httpGetFor(VIDEO_CONTENT_URL, queryString, new TypeReference<ReturnValue<VideoContentResult>>() {});
    }

    public ReturnValue<VideoAddressResult> getVideoAddress(String videoid) {
        HashMap<String, Object> queryString = new HashMap<>(1);
        queryString.put("mid", videoid);
        VideoAddressResult response = httpService.httpGetFor(VIDEO_ADDRESS_URL, queryString, new TypeReference<VideoAddressResult>() {});
        ReturnValue<VideoAddressResult> result = new ReturnValue<>();
        result.setResult(response);
        return result;
    }
}
