package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.dao.javaapi.mapper.EnterpriseGroupMapper;
import com.autohome.applet.dao.javaapi.model.EnterpriseGroup;
import com.autohome.applet.model.constants.RedisKeys;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.CodeMsg;
import com.autohome.applet.model.dto.OemMsg;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.SeriesSpecInfo;
import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.car.portrayal.*;
import com.autohome.applet.model.dto.carlibrary.BrandNameDTO;
import com.autohome.applet.model.dto.carlibrary.carapi.out.*;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarMainresult;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarSeries;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarSeriesList;
import com.autohome.applet.model.dto.cms.ChejiahaoDetailResult;
import com.autohome.applet.model.dto.cms.ChejiahaoDetailResultTagsnew;
import com.autohome.applet.model.dto.dealer.CustomDealer;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsDto;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsResult;
import com.autohome.applet.model.dto.dealer.SpecDealPrice;
import com.autohome.applet.model.dto.netcoreapi.cms.CmsArticle;
import com.autohome.applet.model.dto.netcoreapi.dealer.Dealer;
import com.autohome.applet.model.dto.netcoreapi.series.*;
import com.autohome.applet.model.enums.DealerTypeEnum;
import com.autohome.applet.model.enums.LeadFromEnum;
import com.autohome.applet.model.lightapp.RecommendSeriesInfoDto;
import com.autohome.applet.service.DealerDataToQqService;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.constant.InquiryModel;
import com.autohome.applet.service.javaapi.BaseCarService;
import com.autohome.applet.service.javaapi.CarService;
import com.autohome.applet.service.javaapi.RankService;
import com.autohome.applet.service.netcoreapi.SeriesService;
import com.autohome.applet.service.openapi.CarLibService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.SecurityKit;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.springboot.util.RedisClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CarServiceImpl implements CarService {

//    @Autowired
//    CarLibraryService carLibraryService;

    @Autowired
    CarLibService carLibService;

    @Autowired
    EnterpriseGroupMapper enterpriseGroupMapper;

    @Autowired
    HttpService httpService;

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    BaseCarService baseCarService;

    @Autowired
    RankService rankService;

    @Autowired
    DealerDataToQqService dealerDataToQqService;

    @Override
    public Upb2Result getUpb2(String uuid, String appKey, String userid) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "8a1aebddbaab9cd59eec077d7563bca0");
        params.put("appkey", appKey);
        params.put("userid", userid);
        params.put("uuid", uuid);
        // https://doc.autohome.com.cn/docapi/page/share/share_oGQPQXNZfk
        return httpService.httpGetForReturnValue("http://portrayal-app-personas.autohome.com.cn/w/upb2", params, new TypeReference<ReturnValue<Upb2Result>>() {
        }, 200);
    }

    @Override
    public List<Integer> getHotCar(String type, Integer limit, String cityId, Boolean newenergy) {
        if (limit == 0) {
            return new ArrayList<>();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "8a1aebddbaab9cd59eec077d7563bca0");
        params.put("type", type);
        if (limit != null) {
            params.put("limit", limit);
        }
        if (cityId != null) {
            params.put("cityid", cityId);
        }
        if (newenergy != null) {
            params.put("newenergy", newenergy);
        }
        // https://doc.autohome.com.cn/docapi/page/share/share_oGQQIzF0pE
        return httpService.httpGetForReturnValue("http://portrayal-app-personas.autohome.com.cn/cpc/getHotCar", params, new TypeReference<ReturnValue<List<Integer>>>() {
        }, 200);
    }

    @Override
    public List<CarApiBrandOut> getBrandInfos(List<Integer> brandIds) {
        return getBrands(brandIds);
    }

    @Override
    public void cacheSeriesAllInfo() {
        log.info("cacheSeriesAllInfo Scheduled");
        List<SeriesInfoDto> seriesInfoDtoList = dealerDataToQqService.getSeriesInfoDtoList();
        if (CollectionUtils.isEmpty(seriesInfoDtoList)) {
            return;
        }
        redisClient.setValue(allSeriesDtoKey(), JacksonHelper.serialize(seriesInfoDtoList), 365, TimeUnit.DAYS);
    }

    /**
     * type=series --获取热门车系； type=brand --获取热门品牌
     * */
    @Override
    public List<SeriesMinPriceExtendsResult> getCarSelectionCardMinPriceExtendsResult(String uuid, String appkey, Integer userid, String type, Integer cityid) {
        List<Integer> carSelectionCardList = new LinkedList<>();
        CompletableFuture<List<Integer>> hotCarResultItem = CompletableFuture.supplyAsync(() -> {
            try {
                return getHotCarResult(appkey, type, cityid);

            } catch (BusinessException e) {
                log.error("hotCarResultItem hotCarResultItem exception ", e);
            }
            return new ArrayList<>();
        });

        CompletableFuture<Upb2Result> upb2ResultItem = CompletableFuture.supplyAsync(() -> {
            try {
                return getUpb2Result(uuid, appkey, userid);

            } catch (BusinessException e) {
                log.error("upb2ResultItem upb2ResultItem exception ", e);
            }
            return new Upb2Result();
        });

        //获取偏好车、热门车
        CompletableFuture<RecordDto> thenApply = CompletableFuture.allOf(hotCarResultItem, upb2ResultItem).thenApply(unused -> {
            RecordDto recordDto = new RecordDto();
            try {
                List<Integer> hotCarResult = hotCarResultItem.get();
                recordDto.setHotCarResult(hotCarResult);
            } catch (InterruptedException | ExecutionException e) {
                log.error("getCarSelectionCcard hotCarResult exception ", e);
            }

            try {
                Upb2Result upb2Result = upb2ResultItem.get();
                List<Upb2Item> userViewCarSeriesList = upb2Result.getUserViewCarSeriesList();
                recordDto.setUserViewCarSeriesList(userViewCarSeriesList);
            } catch (InterruptedException | ExecutionException e) {
                log.error("getCarSelectionCcard upb2Result exception ", e);
            }
            return recordDto;
        });
        RecordDto recordDto = null;
        try {
            recordDto = thenApply.get();
        } catch (InterruptedException | ExecutionException e) {
            log.warn("getCarSelectionCard exception ", e);
        }

        List<Integer> userViewCarSeriesIdList = new ArrayList<>();//偏爱车型
        if(!CollectionUtils.isEmpty(recordDto.getUserViewCarSeriesList())){
            userViewCarSeriesIdList.addAll(recordDto.getUserViewCarSeriesList().stream().map(Upb2Item :: getCode).collect(Collectors.toList()));
        }
        List<Integer> hotCarResult = recordDto.getHotCarResult(); //热门车型

        //合并，得到去重的seriesId
        List<Integer> seriesIdList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(hotCarResult)){
            seriesIdList = Stream.of(userViewCarSeriesIdList, hotCarResult).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(seriesIdList)){
            return null;
        }

        //最多取前15条
        if(seriesIdList.size() > 15){
            seriesIdList = seriesIdList.subList(0,15);
        }

        //返回可询价的车系
        List<SeriesMinPriceExtendsResult> seriesMinPriceExtendsResultList = dealerDataToQqService.getSeriesMinPriceExtends(String.valueOf(cityid), seriesIdList);
        //排序
        List<Integer> finalSeriesIdList = seriesIdList;

        if(CollectionUtils.isEmpty(seriesMinPriceExtendsResultList)){
            return null;
        }

        seriesMinPriceExtendsResultList.sort((o1, o2) -> {
            int io1 = finalSeriesIdList.indexOf(o1.getSeriesId());
            int io2 = finalSeriesIdList.indexOf(o2.getSeriesId());
            if(io1 != -1){
                io1 = seriesMinPriceExtendsResultList.size() - io1;
            }
            if(io2 != -1){
                io2 = seriesMinPriceExtendsResultList.size() - io2;
            }
            return io2-io1;
        });
        return seriesMinPriceExtendsResultList;
    }

    /**
     * 获取车系销量热度数据
     * @return
     */
    @Override
    public RankListpageResult getrecranklistpageresult2() {
        Map<String, Object> params = new HashMap<>();
        //获取上个月的时间,一次性全部取出，一个月分大概400条左右
        LocalDate now = LocalDate.now();
        LocalDate lastMonth = now.minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String nowString = lastMonth.format(formatter);

        String url = "http://cars.app.autohome.com.cn/carext/recrank/all/getrecranklistpageresult2?pm=2&pluginversion=11.58.0&issale=1&model=1&channel=0&from=10&pageindex=1&pagesize=2000&typeid=1&subranktypeid=1&levelid=0&date="+nowString+"&price=0-9000&_appid=wxapp&_v=v9.1.12";
        RankListpageResult rankListpageResult = httpService.httpGetFor(url, params, new TypeReference<RankListpageResult>() {
        });

        return rankListpageResult;
    }

    /**
     * 获取车系销量热度数据
     * 微型车: 1
     * 小型车: 2
     * 紧凑型车: 3
     * 中型车: 4
     * 中大型车: 5
     * 大型车: 6
     * 小型SUV: 16
     * 紧凑型SUV: 17
     * 中型SUV: 18
     * 中大型SUV: 19
     * 大型SUV: 20
     * 紧凑型MPV: 21
     * 中型MPV: 22
     * 中大型MPV: 23
     * 大型MPV: 24
     * 跑车: 7
     * 微面: 11
     * 轻客: 13
     * 皮卡: 14,15
     * 微卡: 12
     * @return
     */
    @Override
    public RankListpageResult getrecranklistpageresult2V2(String levelIds, int pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("pm", 2);
        params.put("penetrate_version", "pre28_1725892247000_1722940496000");
//        params.put("pluginversion", "11.69.3");
        params.put("channel", 0);
        params.put("pageindex", 1);
        params.put("pagesize", 20);
        params.put("model", 1);
        params.put("typeid", 2);
        params.put("subranktypeid", "2002");
        params.put("levelid", levelIds);
//https://cars.app.autohome.com.cn/carext/recrank/all/getrecranklistpageresult2_v2?pm=2&penetrate_version=pre28_1725892247000_1722940496000&pluginversion=11.69.3&channel=0&pageindex=1&pagesize=20&model=1&typeid=2&subranktypeid=2002&levelid=0
//        String url = "http://car.app.corpautohome.com/carext/recrank/all/getrecranklistpageresult2_v2";
        String url = "https://cars.app.autohome.com.cn/carext/recrank/all/getrecranklistpageresult2_v2";
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, params);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("getrecranklistpageresult2V2 error url : {} ， parms: {}", url, JacksonHelper.serialize(params));
            return null;
        }

        RankListpageResult rankListpageResult = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<RankListpageResult>() {
        });

        if (httpResult.getBody() == null || rankListpageResult.getResult() == null) {
            log.error("getrecranklistpageresult2V2 error, url： {}, param: {}", url, JacksonHelper.serialize(params));
            return null;
        }
        return rankListpageResult;
    }

    @Override
    public RankListpageResult getrecranklistpageresult2_V2(int pageIndex, int pageSize, int typeid, String subranktypeid) {
        StringBuffer sb = new StringBuffer();
        String url = "cars.app.autohome.com.cn/carext/recrank/all/getrecranklistpageresult2_v2";
        sb.append("http://").append(url)
                .append("?_appid=app&pageindex=").append(pageIndex)
                .append("&pagesize=").append(pageSize)
                .append("&typeid=").append(typeid)
                .append("&subranktypeid=").append(subranktypeid);
//https://cars.app.autohome.com.cn/carext/recrank/all/getrecranklistpageresult2_v2?pm=2&penetrate_version=pre28_1725892247000_1722940496000&pluginversion=11.69.3&channel=0&pageindex=1&pagesize=20&model=1&typeid=2&subranktypeid=2002&levelid=0
//        String url = "http://car.app.corpautohome.com/carext/recrank/all/getrecranklistpageresult2_v2";
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(sb.toString(), new HashMap<>());

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            log.error("getrecranklistpageresult2V2 error url : {}", url);
            return null;
        }

        RankListpageResult rankListpageResult = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<RankListpageResult>() {
        });

        if (httpResult.getBody() == null || rankListpageResult.getResult() == null) {
            log.error("getrecranklistpageresult2V2 error, url： {}", url);
            return null;
        }
        return rankListpageResult;
    }

    @Override
    public BrandNameDTO getBrandNameDTO(int brandId){
        //1.优先走缓存
        String key = "applet:base:car:brandname:getBrandName:" + brandId;
        String tempBrandName = redisClient.get(key);
        if (!StringUtils.isEmpty(tempBrandName)) {
            BrandNameDTO result = JacksonHelper.deserialize(tempBrandName, BrandNameDTO.class);
            if (result != null) {
                return result;
            }
        }
        String url = "http://car.api.autohome.com.cn/v1/CarPrice/Brand_GetBrandNameByBrandId.ashx?brandid=" + brandId + "&_appid=car";
        try {
            BrandNameDTO result = httpService.httpGetForReturnValue(url, new HashMap<>(), new TypeReference<ReturnValue<BrandNameDTO>>() {});
            if(result != null && !StringUtils.isEmpty(result.getBrandname())){
                //存储redis
                redisClient.set(key, JacksonHelper.serialize(result), 1, TimeUnit.DAYS);
            }
            return result;
        } catch (Exception e) {
            log.error("getBrandNameDTO brandId:{}, ex:", brandId, e);
        }
        return null;
    }


    //获取偏爱车
    private Upb2Result getUpb2Result(String uuid, String appkey, Integer userid) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "8a1aebddbaab9cd59eec077d7563bca0");
        params.put("appkey", appkey);
        params.put("userid", userid);
        params.put("uuid", uuid);
        // https://doc.autohome.com.cn/docapi/page/share/share_oGQPQXNZfk
        Upb2Result upb2Result = httpService.httpGetForReturnValue("http://portrayal-app-personas.autohome.com.cn/w/upb2", params, new TypeReference<ReturnValue<Upb2Result>>() {
        }, 200);
        return upb2Result;
    }

    /**
     * 获取热门车
     * type=series --获取热门车系； type=brand --获取热门品牌
     * */
    private List<Integer> getHotCarResult(String appkey, String type, Integer cityid) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "8a1aebddbaab9cd59eec077d7563bca0");
        params.put("appkey", appkey);
        //获取热门车
        params.put("type", type);
        if (cityid != null) {
            params.put("cityid", cityid);
            params.put("limit", "");
        }
        // https://doc.autohome.com.cn/docapi/page/share/share_oGQQIzF0pE
        return httpService.httpGetForReturnValue("http://portrayal-app-personas.autohome.com.cn/cpc/getHotCar", params, new TypeReference<ReturnValue<List<Integer>>>() {
        }, 200);
    }

    private List<CarApiBrandOut> getBrands(List<Integer> brandIds) {
        String brandDtoKey = "applet:api:car:allbrands";
        String brandListOutStr = redisClient.get(brandDtoKey);
        CarApiBrandListOut brandListOut;
        if (brandListOutStr == null) {// 如果品牌缓存标记过期就刷新品牌数据
            brandListOut = carLibService.getCarApiBrandList();
            brandListOut.getBrandList().forEach(i -> {
                i.setId(String.valueOf(SecurityKit.decrypt(i.getId())));
                redisClient.set(brandDtoKey + ":item:" + i.getId(), JacksonHelper.serialize(i), 2, TimeUnit.DAYS);
            });
            redisClient.set(brandDtoKey, "set", 1, TimeUnit.DAYS);
        }
        List<CarApiBrandOut> result = new ArrayList<>();
        for (Integer brandId : brandIds) {
            String brandDto = redisClient.get(brandDtoKey + ":item:" + brandId);
            if (brandDto != null) {
                CarApiBrandOut deserialize = JacksonHelper.deserialize(brandDto, CarApiBrandOut.class);
                if (deserialize != null) {
                    result.add(deserialize);
                }
            }
        }
        return result;
    }

    private String allSeriesDtoKey() {
        return "applet:task:series:all";
    }


}
