package com.autohome.applet.service.lightapp.impl;

import cn.hutool.core.codec.Base64;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.BrandSeariesDTO;
import com.autohome.applet.model.dto.car.BrandmenulistDTO;
import com.autohome.applet.model.dto.car.SpecsMinPriceExtendDTO;
import com.autohome.applet.model.lightapp.*;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.lightapp.MiscsService;
import com.autohome.applet.util.HttpHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MiscsServiceImpl implements MiscsService {

    @Autowired
    HttpService httpService;

    @Override
    public PageOf<Dealer4SpecDto> getDealers4SpecV2(Integer specId, int orderType, int pageindex, int pagesize, int cityId, double lat, double lon) {
        String url = "http://apigateway.corpautohome.com/jxsjs/ics/yhz/dealerlq/v1/dealerlist/list/GetDealerListSpecNew?" +
                "_appId=lightapp&cityId=" + cityId + "&orderType=" + orderType + "&specId=" + specId + "&pageIndex=" + pageindex + "&pageSize=" + pagesize + "&lat=" + lat + "&lon=" + lon;
        Map<String, Object> headers = new HashMap<>();
        headers.put("Authorization", String.format("Basic %s", "bGlnaHRhcHBhcGktbWFuYWdlbWVudGkyc1lnSFd5Olp4aXYxOGV1REtGNw=="));

        Dealer4SpecBaseDto dealer4SpecBaseDto = httpService.httpGetForReturnValue(url, null, headers, new TypeReference<ReturnValue<Dealer4SpecBaseDto>>() {
        });

        PageOf<Dealer4SpecDto> ds = new PageOf<>();
//        ds.setItems(new ArrayList<>());
//        ds.setTotalCount(0L);
        ds.setTotalCount((long) dealer4SpecBaseDto.getRowcount());
        ds.setItems(dealer4SpecBaseDto.getList());
        if (ds.getItems() != null && ds.getItems().size() > 0) {
            List<String> dealerIdList = ds.getItems().stream()
                    .map(Book -> String.valueOf(Book.getDealerId())).collect(Collectors.toList());
            if (dealerIdList.size() > 0) {
                String dealerIdStr = String.join(",", dealerIdList);

                CompletableFuture<List<zxdpItem>> listZxdpItem = CompletableFuture.supplyAsync(() -> {
                    try {
                        return httpService.httpGetForReturnValue("http://dealercloud.arvr.api.lq.autohome.com.cn/api/v1/zxdp/getList?_appId=cms&dealerIds=" + dealerIdStr + "&pvAreaId=6851513", null, new TypeReference<ReturnValue<List<zxdpItem>>>() {
                        });
                    } catch (BusinessException e) {
                        log.warn("get zxdp exception ", e);
                    }
                    return null;
                });
                CompletableFuture<List<Dealer4SpecActivity>> listActivityItem = CompletableFuture.supplyAsync(() -> {
                    try {
                        return httpService.httpGetForReturnValue("http://zhiyutong.api.corpautohome.com/open/entrance/activitys" +
                                        "?_appid=m&cityId=" + cityId + "&dealerIds=" + dealerIdStr
                                , null, new TypeReference<ReturnValue<List<Dealer4SpecActivity>>>() {
                        });
                    } catch (BusinessException e) {
                        log.warn("get activity exception ", e);
                    }
                    return null;
                });

                CompletableFuture<?> thenApply = CompletableFuture.allOf(listZxdpItem, listActivityItem).thenApply(unused -> {
                    Map<Integer, zxdpItem> zxdpItemMap = null;
                    try {
                        List<zxdpItem> zxdpItems = listZxdpItem.get();
                        if (!CollectionUtils.isEmpty(zxdpItems)) {
                            zxdpItemMap = zxdpItems.stream().collect(Collectors.toMap(zxdpItem::getDealerId, item -> item));
                        }
                    } catch (InterruptedException | ExecutionException e) {
                        e.printStackTrace();
                    }
                    Map<Integer, Dealer4SpecActivity> activityItemMap = null;
                    try {
                        List<Dealer4SpecActivity> activityList = listActivityItem.get();
                        if (!CollectionUtils.isEmpty(activityList)) {
                            activityItemMap = activityList.stream().collect(Collectors.toMap(Dealer4SpecActivity::getDealerId, item -> item));
                        }
                    } catch (InterruptedException | ExecutionException e) {
                        e.printStackTrace();
                    }
                    for (Dealer4SpecDto t : ds.getItems()) {
                        if (zxdpItemMap != null) {
                            zxdpItem first = zxdpItemMap.get(t.getDealerId());
                            if (first != null) {
                                t.setLandUrl(first.getLandUrl());
                                t.setHavePermission(first.isHavePermission());
                                if (first.getExhibition() != null) {
                                    t.setLinkUrl(first.getExhibition().getLandUrl());
                                    t.setVrState(first.getExhibition().isHavePermission() ? 1 : 0);
                                }
                            }
                        }
                        if (activityItemMap != null) {
                            Dealer4SpecActivity specActivity = activityItemMap.get(t.getDealerId());
                            t.setActivity(specActivity);
                        }
                    }
                    return null;
                });
                try {
                    thenApply.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.warn("getDealers4SpecV2 thenApply exception ", e);
                }
            }
        }
        return ds;
    }

    @Override
    public PageKBOf<WordOfMouthDto,DimSeriesPRCTypesDto> wordOfMouth(Integer seriesId, int pageindex, int pagesize, int isgood, int purpose, int photosize, int isappend, int year, int grade, int provinceId, int order ,int cityid) {
        //获取车系列表页
        String url1 = "http://koubei.api.sjz.autohome.com.cn/api/masterdata/series_evaluation_list_v1?" +
                "_appId=lightapp&id=" + seriesId +"&pageindex=" + pageindex + "&pagesize=" + pagesize + "&isgood=" + isgood + "&purpose=" + purpose + "&photosize=" + photosize + "&isappend=" + isappend + "&year=" + year + "&grade=" + grade + "&provinceId=" + provinceId + "&order=" + order ;
        Map<String, Object> headers = new HashMap<>();
        headers.put("Authorization", String.format("Basic %s", "bGlnaHRhcHBhcGktbWFuYWdlbWVudGkyc1lnSFd5Olp4aXYxOGV1REtGNw=="));
        //获得车系口碑平均评分
        String url2 = "http://koubei.api.sjz.autohome.com.cn/api/Series/GetSeriesEvalScore?" + "_appId=lightapp&seriesIds=" + seriesId ;
        //口碑获取用户购车价
        String url3 = "http://koubei.api.sjz.autohome.com.cn/api/spec/boughtprice?" + "_appId=lightapp&seriesid=" + seriesId + "&cityid=" + cityid  +"&pageindex=" + pageindex + "&pagesize=" + pagesize;

        //获取车系语义标签数据
        String url4 = "http://koubei.api.sjz.autohome.com.cn/api/Semantic/LoadSeriesPRCType?" + "_appId=lightapp&seriesid=" + seriesId + "&typekey=-1&year=1";

        //PageOf<WordOfMouthDto> wom = new PageOf<>();
        PageKBOf<WordOfMouthDto,DimSeriesPRCTypesDto> wom = new PageKBOf<>();

        CompletableFuture<SeriesEvaluationDto> seriesEvaluationItem = CompletableFuture.supplyAsync(() -> {
            try {
                return httpService.httpGetForReturnValue(url1, null, new TypeReference<ReturnValue<SeriesEvaluationDto>>() {
                });
            } catch (BusinessException e) {
                log.error("wordOfMouth seriesEvaluationItem exception ", e);
            }
            return null;
        });

        CompletableFuture<List<SeriesEvalScoreDto>> seriesEvalScoreItem = CompletableFuture.supplyAsync(() -> {
            try {
                return httpService.httpGetForReturnValue(url2, null, new TypeReference<ReturnValue<List<SeriesEvalScoreDto>>>() {
                });
            } catch (BusinessException e) {
                log.error("wordOfMouth seriesEvalScoreItem exception ", e);
            }
            return null;
        });

        CompletableFuture<BoughtpriceBaseDto> boughtpriceBaseItem = CompletableFuture.supplyAsync(() -> {
            try {
                return httpService.httpGetForReturnValue(url3, null, new TypeReference<ReturnValue<BoughtpriceBaseDto>>() {
                });
            } catch (BusinessException e) {
                log.error("wordOfMouth boughtpriceBaseItem exception ", e);
            }
            return null;
        });

        CompletableFuture<DimSeriesPRCTypesBaseDto> dimSeriesPRCTypesBaseDtoItem = CompletableFuture.supplyAsync(() -> {
            try {
                return httpService.httpGetForReturnValue(url4, null, new TypeReference<ReturnValue<DimSeriesPRCTypesBaseDto>>() {
                });
            } catch (BusinessException e) {
                log.error("wordOfMouth dimSeriesPRCTypesBaseDtoItem exception ", e);
            }
            return null;
        });


        CompletableFuture<?> thenApply = CompletableFuture.allOf(seriesEvaluationItem, seriesEvalScoreItem , boughtpriceBaseItem ,dimSeriesPRCTypesBaseDtoItem).thenApply(unused -> {
            try {
                DimSeriesPRCTypesBaseDto dimSeriesPRCTypesBaseItems = dimSeriesPRCTypesBaseDtoItem.get();
                wom.setDimSeriesPRCTypes(dimSeriesPRCTypesBaseItems.getDimSeriesPRCTypes());

            } catch (InterruptedException | ExecutionException e) {
                log.error("wordOfMouth dimSeriesPRCTypesBaseItems exception ", e);
            }

            try {
                SeriesEvaluationDto seriesEvaluationItems = seriesEvaluationItem.get();
                wom.setTotalCount((long) seriesEvaluationItems.getRowcount());
                wom.setItems(seriesEvaluationItems.getList());
            } catch (InterruptedException | ExecutionException e) {
                log.error("wordOfMouth seriesEvaluationItems exception ", e);
            }

            Map<Integer, SeriesEvalScoreDto> seriesEvalScoreMap = null;
            try {
                List<SeriesEvalScoreDto> seriesEvalScoreList = seriesEvalScoreItem.get();
                if (!CollectionUtils.isEmpty(seriesEvalScoreList)) {
                    seriesEvalScoreMap = seriesEvalScoreList.stream().collect(Collectors.toMap(SeriesEvalScoreDto::getSeriesid, item -> item));
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("wordOfMouth seriesEvalScoreList exception ", e);
            }
            List<BoughtpriceDto> boughtpriceDtoList = null ;
            try {
                BoughtpriceBaseDto boughtpriceBaseItems = boughtpriceBaseItem.get();
                boughtpriceDtoList = boughtpriceBaseItems.getList();
            } catch (InterruptedException | ExecutionException e) {
                log.error("wordOfMouth boughtpriceBaseItems exception ", e);
            }
            Double kbscore = null ;
            for (Integer integer : seriesEvalScoreMap.keySet()) {
                SeriesEvalScoreDto seriesEvalScoreDto = seriesEvalScoreMap.get(integer);
                kbscore = seriesEvalScoreDto.getKbscore();
            }

            for (WordOfMouthDto item : wom.getItems()) {
                item.setKbscore(kbscore);
                for (BoughtpriceDto boughtpriceDto : boughtpriceDtoList) {
                    if (item.getSpecid().equals(boughtpriceDto.getSpecId()))
                        item.setBoughtprice(boughtpriceDto);
                }
            }
            return null;
        });
        try {
            thenApply.get();
        } catch (InterruptedException | ExecutionException e) {
            log.warn("wordOfMouth exception ", e);
        }
        return wom;
    }

    @Override
    public ReturnValue<?> getSeriesList(int pm, int seriesid, int cityid, int grade, int pageindex, int pagesize, int order) {
        List<String> specidsList = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("pm", pm);
        param.put("seriesid", seriesid);
        param.put("grade", grade);
        param.put("pageindex", pageindex);
        param.put("pagesize", pagesize);
        param.put("order", order);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("https://koubeiipv6.app.autohome.com.cn/pc/series/list", param);
        if (httpGet.isSuccess()) {
            ReturnValue<Map> result = httpGet.deserialize(new TypeReference<ReturnValue<Map>>() {
            });
            if (result != null && !CollectionUtils.isEmpty(result.getResult())) {
                Map resultMap = result.getResult();
                Object specgroup = resultMap.get("specgroup");
                List<Map> specgroupList = (List<Map>)specgroup;
                for (Map specgroupMap : specgroupList) {
                    Object speclist = specgroupMap.get("speclist");
                    List<Map> speclistList = (List<Map>)speclist;
                    for (Map speclistMap : speclistList) {
                        String specid = speclistMap.get("specid").toString();
                        specidsList.add(specid);
                        speclistMap.put("minOriginalPrice",null);
                    }
                }
                //获取newsPrice（最低价 单位：元）
                try {
                    ArrayList<CompletableFuture<SpecsMinPriceExtendDTO>> specsMinPriceExtendList = new ArrayList<>();
                    for (String specid : specidsList) {
                        CompletableFuture<SpecsMinPriceExtendDTO> completable =CompletableFuture.supplyAsync(()->
                                this.getSpecsMinPriceExtend(specid,cityid)
                        );
                        specsMinPriceExtendList.add(completable);
                    }
                    CompletableFuture[] specsMinPriceExtendarray = specsMinPriceExtendList.toArray(new CompletableFuture[specsMinPriceExtendList.size()]);
                    CompletableFuture.allOf(specsMinPriceExtendarray).join();
                    //填充数据
                    for (Map specgroupMap : specgroupList) {
                        Object speclist = specgroupMap.get("speclist");
                        List<Map> speclistList = (List<Map>)speclist;
                        for (Map speclistMap : speclistList) {
                            String pcsSecid = speclistMap.get("specid").toString();
                            for (CompletableFuture<SpecsMinPriceExtendDTO> specsMinPriceExtendDTOCompletableFuture : specsMinPriceExtendList) {
                                SpecsMinPriceExtendDTO specsMinPriceExtend = specsMinPriceExtendDTOCompletableFuture.get();
                                if (specsMinPriceExtend != null && !CollectionUtils.isEmpty(specsMinPriceExtend.getList())){
                                    String specspecId = specsMinPriceExtend.getList().get(0).getSpecId().toString();
                                    if (specspecId.equals(pcsSecid)){
                                        speclistMap.put("minOriginalPrice",specsMinPriceExtend.getList().get(0).getMinOriginalPrice());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("getSeriesList getSpecsMinPriceExtend exception ", e);
                }
                return result;
            }
        }


        return null;
    }

    @Override
    public ReturnValue<?> getBrandSeariesYYClub(String appid) {
        List<BrandSeariesDTO> brandSearies = new ArrayList<>();
        CompletableFuture<BrandmenulistDTO> brandmenulist = CompletableFuture.supplyAsync(() -> {
            try {
                return getbrandmenulist();
            } catch (BusinessException e) {
                log.error("getBrandSeariesYYClub brandmenulist exception ", e);
            }
            return null;
        });

        CompletableFuture<List<BrandSeariesDTO>> brandSeariesDTOList = CompletableFuture.supplyAsync(() -> {
            try {
                return getBrandSeariesDTO(appid);
            } catch (BusinessException e) {
                log.error("getBrandSeariesYYClub brandmenulist exception ", e);
            }
            return null;
        });

        CompletableFuture<?> thenApply = CompletableFuture.allOf(brandmenulist, brandSeariesDTOList ).thenApply(unused -> {
            try {
                //获取车型全量数据
                BrandmenulistDTO brandmenulistDTO = brandmenulist.get();
                //获取论坛车型数据
                List<BrandSeariesDTO> brandSeariesList = brandSeariesDTOList.get();
                List<BrandmenulistDTO.brandlist> brandlist = brandmenulistDTO.getBrandlist();
                //通过全量数据进行论坛车型数据排序
                for (BrandmenulistDTO.brandlist brand : brandlist) {
                    String brandId = brand.getId().toString();
                    for (BrandSeariesDTO brandSeariesDTO : brandSeariesList) {
                        String brandSeariesid = brandSeariesDTO.getBrandid().toString();
                        if (brandId.equals(brandSeariesid)){
                            //添加结果集
                            brandSearies.add(brandSeariesDTO);
                            break;
                        }
                    }
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("getBrandSeariesYYClub brandmenulist|brandSeariesDTOList exception ", e);
            }
            return null;
        });
        try {
            thenApply.get();
        } catch (InterruptedException | ExecutionException e) {
            log.warn("getBrandSeariesYYClub exception ", e);
        }
        return ReturnValue.buildSuccessResult(brandSearies);
    }


    private List<BrandSeariesDTO> getBrandSeariesDTO(String appid) {
        Map<String, Object> params = new HashMap<>();
        params.put("_appid",appid);
        List<BrandSeariesDTO> brandSeariesDTOList = httpService.httpGetForReturnValue("http://clubapi.in.autohome.com.cn/api/club/GetBrand_Searies_YYClub", params, new TypeReference<ReturnValue<List<BrandSeariesDTO>>>() {
        });
        return brandSeariesDTOList;
    }


    private BrandmenulistDTO getbrandmenulist() {
        Map<String, Object> params = new HashMap<>();
        BrandmenulistDTO brandmenulistDTO = httpService.httpGetForReturnValue("https://carservice.autohome.com.cn/carapi/getbrandmenulist", params, new TypeReference<ReturnValue<BrandmenulistDTO>>() {
        });
        return brandmenulistDTO;
    }


    private SpecsMinPriceExtendDTO getSpecsMinPriceExtend(String specid , int cityid) {
        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "dealer");
        params.put("cityid",cityid);
        params.put("specids",specid);
        //HttpHeaders headers = new HttpHeaders();
        //headers.set("Authorization", String.join(" ","Basic", Base64.encode(String.join(":","applet-apiD9If4r4A", "TvGIded6xhoa").getBytes())));
        HashMap<String, Object> header = new HashMap<>();
        header.put("Authorization",String.join(" ","Basic", Base64.encode(String.join(":","applet-apiD9If4r4A", "TvGIded6xhoa").getBytes())));
        SpecsMinPriceExtendDTO specsMinPriceExtendDTO = httpService.httpGetForReturnValue("http://apigateway.corpautohome.com/jxsjs/ics/yhz/dealerlq/v1/statistics/spec/getSpecsMinPriceExtend", params,header, new TypeReference<ReturnValue<SpecsMinPriceExtendDTO>>() {
        });
        return specsMinPriceExtendDTO;
    }


}
