package com.autohome.applet.service.openapi.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.carlibrary.ElectricSpecDto;
import com.autohome.applet.model.dto.carlibrary.ElectricSpecItem;
import com.autohome.applet.model.dto.carlibrary.SeriesDetailDto;
import com.autohome.applet.model.dto.honor.AnalyzeCar;
import com.autohome.applet.model.dto.honor.AnalyzeCarResult;
import com.autohome.applet.model.dto.honor.AnalyzeSeriesCardInfo;
import com.autohome.applet.model.dto.honor.ArticleCard;
import com.autohome.applet.model.dto.maindata.Article;
import com.autohome.applet.model.dto.maindata.DataMore;
import com.autohome.applet.model.dto.netcoreapi.spec.SpecUsedPrice;
import com.autohome.applet.model.dto.rank.RankElectricCar;
import com.autohome.applet.model.dto.rank.RankElectricCarList;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.MediaService;
import com.autohome.applet.service.impl.MediaServiceImpl;
import com.autohome.applet.service.javaapi.CarLibraryService;
import com.autohome.applet.service.javaapi.RankService;
import com.autohome.applet.service.openapi.HonorService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.JobLog;
import com.autohome.applet.util.SecurityKit;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HonorServiceImpl implements HonorService {

    @Autowired
    private MediaService mediaService = new MediaServiceImpl();

    @Autowired
    private CarLibraryService carLibraryService;

    @Autowired
    private RankService rankService;

    @Autowired
    HttpService httpService;

    @Override
    public void syncArticle(LocalDateTime breakData) {
        String searchAfter = ""; // "1679478678000,1281261"

        DataMore<Article> articleList = null;
        while (articleList == null || articleList.isHasMore()) {
            articleList = getArticleList(searchAfter);
            if (articleList != null) {
                JobLog.info("honorArticleSyncJob getArticleListresult"+ JacksonHelper.serialize(articleList) );
                searchAfter = articleList.getSearchAfter();
                List<Article> items = articleList.getItems();

                List<ArticleCard> list = new ArrayList<>();

                for (Article item : items) {

                    LocalDateTime publishDateTime = LocalDateTime.parse(item.getPublish_time(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"));
                    if (publishDateTime.isBefore(breakData)) {
                        JobLog.info("syncArticle finish to time " + item.getPublish_time());
                        return;
                    }

                    ArticleCard card = new ArticleCard();
                    card.setAccessCode("16be35e555663000");
                    card.setContentId(String.valueOf(item.getBiz_id()));
                    card.setOp(1);
                    card.setTitle(item.getTitle());
                    card.setDescription(item.getSummary().replaceAll("&nbsp;", ""));
                    if (!CollectionUtils.isEmpty(item.getSeries_ids())) {

                        Set<String> keySet = new HashSet<>();
                        for (Integer seriesId : item.getSeries_ids()) {
                            SeriesDetailDto series = carLibraryService.getSeriesBySeriesId(seriesId);
                            if (series != null) {
                                keySet.add(series.getName());
                                keySet.add(series.getLevelname());
                            }
                        }
                        // 获取车系名称，级别名称等 https://caraddl.api.autohome.com.cn/v2/Base/Series_GetAllSeries.ashx?_appid=car
                        card.setKeywords(String.join(",", keySet));
                    } else {
                        card.setKeywords("汽车,汽车之家,新能源");
                    }
                    card.setLanguage("zh_cn");
                    card.setContentScore(item.getPv());
                    card.setCpName("汽车之家极速版");
                    card.setCpIconUrl("https://www3.autoimg.cn/svcover/g24/M01/A1/24/Chxky2PiC4GAaBTFAAAXQq07C3s883.jpg");

                    if (!StringUtils.isEmpty(item.getVideo_source())) {
                        MediaServiceImpl.Media mediaInfo = mediaService.getMediaInfo(item.getVideo_source());
                        if (mediaInfo != null) {
                            MediaServiceImpl.Preview preview = mediaInfo.getImages().getPreview();
                            if (preview.getWidth() > preview.getHeight()) {
                                card.setVideoSpecs("16:9");
                            } else {
                                card.setVideoSpecs("9:16");
                            }
                            card.setVideoType(1);
                            Optional<MediaServiceImpl.Qualities> max = mediaInfo.getQualities().stream().filter(qualities -> qualities.getValue() <= 300)
                                    .max(Comparator.comparingInt(MediaServiceImpl.Qualities::getValue));
                            max.ifPresent(qualities -> card.setVideoUrl(qualities.getCopy()));
                        }
                    }

                    card.setImgType(1);
                    card.setImgUrl(item.getImg_url_4x3()
                            .replace("http://", "https://")
                            .replaceAll("\\d{2,3}x\\d{2,3}_0_autohomecar__", "400x300_0_autohomecar__"));
                    card.setImgSpecs("4:3");

                    // 内容详情页
                    if (!StringUtils.isEmpty(item.getVideo_source())) {
                        // 原创视频
                        card.setWebLink("https://mina.autohome.com.cn/pa-bank/#/detailpackage/pages/original/video/index?channel=harmonyos&auto_open_from=honor_search&objectid=" + item.getBiz_id());
                        card.setQuickAppLink("hnquickapp://app/com.autohome.quickapp/PageVideo?type=1&videoid=" +
                                item.getBiz_id() + "&auto_open_from=hr_search_230324");
                    } else {
                        // 原创文章
                        card.setWebLink("https://mina.autohome.com.cn/pa-bank/#/detailpackage/pages/original/article/index?channel=harmonyos&auto_open_from=honor_search&objectid=" + item.getBiz_id());
                        card.setQuickAppLink("hnquickapp://app/com.autohome.quickapp/PageArticle?objid=" +
                                item.getBiz_id() + "&auto_open_from=hr_search_230324");
                    }
                    card.setQuickAppAppName("汽车之家极速版");
                    card.setQuickAppPackageName("com.autohome.quickapp");
                    card.setQuickAppMinVersionCode(8001);
                    card.setMinPlatformVersion("1070");

                    // 快应用主页
                    card.setCpWebLink("https://mina.autohome.com.cn/pa-bank/#/pages/home/<USER>");
                    card.setCpQuickAppLink("hnquickapp://app/com.autohome.quickapp/PageIndex?auto_open_from=hr_search_230324");
                    card.setCpQuickAppAppName("汽车之家极速版");
                    card.setCpQuickAppMinVersionCode(8001);
                    card.setCpQuickAppPackageName("com.autohome.quickapp");
                    card.setCpMinPlatformVersion(1070);

                    list.add(card);
                }
                pushArticleCard(list);
            } else {
                JobLog.warn("getArticleList return null for searchAfter " + searchAfter);
                break;
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    public AnalyzeCarResult.Car analyzeCar(AnalyzeCar analyzeCar) {
        String analyzeCarUrl = "http://imageaip.corpautohome.com/car_series_recog_honor";

        AnalyzeCar.Real real = analyzeCar.getContentData().getValues().get(0);

        Map<String, Object> param = new HashMap<>();
        param.put("image_base64", real.getReal());
        param.put("pvid", UUID.randomUUID());
        param.put("device_id", analyzeCar.getTraceId());
        String body = JacksonHelper.serialize(param);
        log.info("analyzeCar http://imageaip.corpautohome.com/car_series_recog_honor > " + body);

        Map<String, Object> header = new HashMap<>();
        header.put("Authorization", "Basic YXBwbGV0LWFwaUQ5SWY0cjRBOlR2R0lkZWQ2eGhvYQ==");

        AnalyzeCarResult analyzeCarResult = httpService.httpPostJsonForReturnValue(analyzeCarUrl, body, header, new TypeReference<ReturnValue<AnalyzeCarResult>>() {
        });
        if (CollectionUtils.isEmpty(analyzeCarResult.getCars())) {
            return null;
        }
        AnalyzeCarResult.Cars firstCars = analyzeCarResult.getCars().get(0);
        if (CollectionUtils.isEmpty(firstCars.getCar())) {
            return null;
        }
        return firstCars.getCar().get(0);
    }

    @Override
    public AnalyzeSeriesCardInfo getSeriesCardInfo(AnalyzeCarResult.Car car) {
        SeriesDetailDto series = carLibraryService.getSeriesBySeriesId(car.getSeriesId());
        String encryptSeriesId = SecurityKit.encrypt(String.valueOf(car.getSeriesId()));
        if (series == null) {
            throw new BusinessException("未获取到车系信息(" + encryptSeriesId + ")", 1);
        }
        int state = series.getState(); // 0 未售,10 待售,20 在售,30 停产在售,40 停售

        AnalyzeSeriesCardInfo cardInfo = new AnalyzeSeriesCardInfo();
        cardInfo.setSeriesName(car.getSeriesName());
        cardInfo.setSeriesId(car.getSeriesId());
        switch (state) {
            case 10:
                cardInfo.setSaleStatus("即将上市");
                break;
            case 20:
            case 30:
                cardInfo.setSaleStatus("售卖中");
                break;
            case 0:
                cardInfo.setSaleStatus("未售");
                break;
            case 40:
                cardInfo.setSaleStatus("已停售");
                break;
            default:
                cardInfo.setSaleStatus("未知");
                break;
        } // 设置售卖状态
        cardInfo.setBrandName(car.getBrandName());
        cardInfo.setBrandLogo(getBrandLogo(car.getBrandId()));
        cardInfo.setSeriesImg(series.getPnglogo());
        cardInfo.setSeriesLevel(series.getLevelname());
//        AnalyzeSeriesCardInfo.ExtendInfo extendInfo = new AnalyzeSeriesCardInfo.ExtendInfo();
//        cardInfo.setExtendInfo(extendInfo);


        String autoOpenFrom = getAutoOpenFrom(state, series.getNewenergy() == 1);

        // 配置卡片跳转车系页deeplink
        String pageCarSeriesUrl = "PageCarSeries?seriesid=${" + encryptSeriesId + "}&auto_open_from=" + autoOpenFrom;
//        extendInfo.setCardDeeplink(getDeeplink(pageCarSeriesUrl));
        AnalyzeSeriesCardInfo.DeepLink homePage = new AnalyzeSeriesCardInfo.DeepLink();
        AnalyzeSeriesCardInfo.Quick quickApp = new AnalyzeSeriesCardInfo.Quick();
        quickApp.setAppName("汽车之家");
        quickApp.setUrl(getDeeplink(pageCarSeriesUrl));
        homePage.setQuickApp(quickApp);
        cardInfo.setHomePage(homePage);

        // 配置服务按钮
        List<AnalyzeSeriesCardInfo.OperateIcon> operateIconInfo = new ArrayList<>();
        cardInfo.setOperateIconInfo(operateIconInfo);
//        List<AnalyzeSeriesCardInfo.ServiceLink> recommendServiceList = new ArrayList<>();
//        extendInfo.setRecommendServiceList(recommendServiceList);

        // 服务按钮-左侧
        String pageCarPicurl = "PageCarPicSeries?seriesid=${" + encryptSeriesId + "}&seriesname=${" + car.getSeriesName() + "}" +
                "&current=guantu&auto_open_from=" + autoOpenFrom;
//        recommendServiceList.add(new AnalyzeSeriesCardInfo.ServiceLink("实拍图片", getDeeplink(pageCarPicurl)));
        AnalyzeSeriesCardInfo.DeepLink deeplinkLeft = new AnalyzeSeriesCardInfo.DeepLink();
        AnalyzeSeriesCardInfo.Quick quickAppLeft = new AnalyzeSeriesCardInfo.Quick();
        quickAppLeft.setAppName("汽车之家");
        quickAppLeft.setUrl(getDeeplink(pageCarPicurl));
        deeplinkLeft.setQuickApp(quickAppLeft);
        operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("实拍图片", deeplinkLeft));

//        List<AnalyzeSeriesCardInfo.AtomItem> dynamicFields = new ArrayList<>();
//        extendInfo.setDynamicFields(dynamicFields);
        List<AnalyzeSeriesCardInfo.NameValue> extraContent = new ArrayList<>();
        cardInfo.setExtraContent(extraContent);

        // 厂商指导价
        String factoryPrice = String.format("%.2f万", series.getMinprice() / 10000f) + " ~ "
                + String.format("%.2f万", series.getMaxprice() / 10000f);
        extraContent.add(new AnalyzeSeriesCardInfo.NameValue("厂商指导价", factoryPrice));
//        dynamicFields.add(new AnalyzeSeriesCardInfo.AtomItem("厂商指导价", factoryPrice));


        if (series.getNewenergy() == 1) { // 新能源车
            // 纯电续航里程
            Optional<Integer> max = series.getElectricmotormileage().stream().map(Integer::parseInt).max(Comparator.comparingInt(o -> o));
            String value2 = "暂无";
            if (max.isPresent()) {
                value2 = max.get() + "KM";
            }
            extraContent.add(new AnalyzeSeriesCardInfo.NameValue("纯电续航里程", value2));

            // 快充时间、销量排行、二手车价
            if (state == 10) { // 待售
                // 快充时间
                double maxFastChargeTimeInt = 0;
                ElectricSpecDto electricSpecs = carLibraryService.getElectricSpecsBySeriesId(car.getSeriesId());
                if (electricSpecs != null) {
                    Optional<ElectricSpecItem> maxFastChargeTime = electricSpecs.getSpecItems().stream().max(Comparator.comparingDouble(ElectricSpecItem::getOfficialFastChargeTime));
                    if (maxFastChargeTime.isPresent()) {
                        maxFastChargeTimeInt = maxFastChargeTime.get().getOfficialFastChargeTime();
                    }
                }
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("快充时间", maxFastChargeTimeInt > 0 ? String.format("%.2f h", maxFastChargeTimeInt) : "暂无"));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageCarSeries?seriesid=${" + encryptSeriesId + "}&auto_open_from="
                        + autoOpenFrom + "&openinquiry=1");
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("预约咨询",deeplinkRight));
            } else if (state == 20 || state == 30) { // 在售、停产在售
                // 销量排行
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("快充时间",
                        getRankInfo(car.getSeriesId(), "201908", "新能源", series.getMinprice(), series.getMaxprice())));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageBrowser?webUrl=https://quickapp-h5.autohome.com.cn/final-price?keyid=85&linkid=4|127|1783|21306|205912|305202&auto_open_from=hr_ident_car");
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("最新成交价",deeplinkRight));
            } else if (state == 40 || state == 0) { // 停售、未售
                // 二手车价
                SpecUsedPrice erShouCar = getErShouCar(car.getSeriesId());
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("二手车价",
                        erShouCar != null ? String.format("%.2f万", erShouCar.getMinPrice()) : "暂无"));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageUsedListWeb?webUrl=https://m.che168.com/app/beijing/aodi/aodis3/?pvareaid=104765&sourcename=ahxiaochengxu&safe=0&auto_open_from=" + autoOpenFrom);
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("二手车咨询",deeplinkRight));
            } else { // 其他
                // 打底 二手车价
                SpecUsedPrice erShouCar = getErShouCar(car.getSeriesId());
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("二手车价",
                        erShouCar != null ? String.format("%.2f万", erShouCar.getMinPrice()) : "暂无"));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageUsedListWeb?webUrl=https://m.che168.com/app/beijing/aodi/aodis3/?pvareaid=104765&sourcename=ahxiaochengxu&safe=0&auto_open_from=" + autoOpenFrom);
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("二手车咨询",deeplinkRight));
            }
        } else { // 燃油车
            // 综合油耗
            String value2 = "暂无";
            if (series.getMinoilwear() == series.getMaxoilwear()) {
                if (series.getMinoilwear() != 0) {
                    value2 = series.getMinoilwear() + "L/100km";
                }
            } else {
                value2 = series.getMinoilwear() + "~" + series.getMaxoilwear() + "L/100km";
            }
            extraContent.add(new AnalyzeSeriesCardInfo.NameValue("综合油耗", value2));

            if (state == 10) { // 待售
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("上市时间", "敬请期待"));
                // 服务按钮-右侧
                String deeplink = getDeeplink("PageCarSeries?seriesid=${" + encryptSeriesId + "}&auto_open_from="
                        + autoOpenFrom + "&openinquiry=1");
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("预约咨询",deeplinkRight));
            } else if (state == 20 || state == 30) { // 在售、停产在售
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("销量排行",
                        getRankInfo(car.getSeriesId(), String.valueOf(series.getLevelid())
                                , series.getLevelname(), series.getMinprice(), series.getMaxprice())));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageBrowser?webUrl=https://quickapp-h5.autohome.com.cn/final-price?keyid=85&linkid=4|127|1783|21306|205911|305202&auto_open_from=hr_ident_car");
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("最新成交价",deeplinkRight));
            } else if (state == 40 || state == 0) { // 停售、未售
                SpecUsedPrice erShouCar = getErShouCar(car.getSeriesId());
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("二手车价",
                        erShouCar != null ? String.format("%.2f万", erShouCar.getMinPrice()) : "暂无"));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageUsedListWeb?webUrl=https://m.che168.com/app/beijing/aodi/aodis3/?pvareaid=104765&sourcename=ahxiaochengxu&safe=0&auto_open_from=" + autoOpenFrom);
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("二手车咨询",deeplinkRight));
            } else { // 其他
                SpecUsedPrice erShouCar = getErShouCar(car.getSeriesId());
                extraContent.add(new AnalyzeSeriesCardInfo.NameValue("二手车价",
                        erShouCar != null ? String.format("%.2f万", erShouCar.getMinPrice()) : "暂无"));

                // 服务按钮-右侧
                String deeplink = getDeeplink("PageUsedListWeb?webUrl=https://m.che168.com/app/beijing/aodi/aodis3/?pvareaid=104765&sourcename=ahxiaochengxu&safe=0&auto_open_from=" + autoOpenFrom);
                AnalyzeSeriesCardInfo.DeepLink deeplinkRight = new AnalyzeSeriesCardInfo.DeepLink();
                AnalyzeSeriesCardInfo.Quick quickAppRight = new AnalyzeSeriesCardInfo.Quick();
                quickAppRight.setAppName("汽车之家");
                quickAppRight.setUrl(deeplink);
                deeplinkRight.setQuickApp(quickAppRight);
                operateIconInfo.add(new AnalyzeSeriesCardInfo.OperateIcon("二手车咨询",deeplinkRight));
            }
        }
        return cardInfo;
    }

    private String getAutoOpenFrom(int state, boolean isNewenergy) {
        // 0 未售,10 待售,20 在售,30 停产在售,40 停售
        switch (state) {
            case 10: // 待售
                return isNewenergy ? "hr_ident_ev_presale" : "hr_ident_oil_presale";
            case 20: // 在售
            case 30: // 停产在售
                return isNewenergy ? "hr_ident_ev_now" : "hr_ident_oil_now";
            case 0: // 未售
                return isNewenergy ? "hr_ident_ev_not" : "hr_ident_oil_not";
            case 40: // 停售
                return isNewenergy ? "hr_ident_ev_halt" : "hr_ident_oil_halt";
            default: //
                return "hr_ident_other_state";
        }
    }

    private String getDeeplink(String url) {
        try {
            return String.format("hnquickapp://app/com.autohome.quickapp/PageTransfer?url=%s", URLEncoder.encode(url, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            log.warn("getDeeplink url encode exception", e);
        }
        return String.format("hnquickapp://app/com.autohome.quickapp/PageTransfer?url=%s", url);
    }
    /**
     * 销量榜查询
     *
     * @param seriesId 车系id
     * @param levelId  级别(中型车：4)（新能源-全部: 201908）
     * @param minPrice 车系最低价
     * @param maxPrice 车系最高价
     * @return 返回榜单文本
     */
    private String getRankInfo(int seriesId, String levelId, String levelName, int minPrice, int maxPrice) {
        int[] priceRange = getPriceRange((int) (minPrice / 10000f), (int) (maxPrice / 10000f));
        minPrice = priceRange[0];
        maxPrice = priceRange[1];

        String month = "2023-08";
        Map<String, Object> param = new HashMap<>();
        param.put("pm", 2);
        param.put("channel", 0);
        param.put("from", 10);
        param.put("date", month);
        param.put("levelid", levelId);
        param.put("pageindex", 1);
        param.put("pagesize", 50);
        param.put("price", minPrice + "-" + maxPrice);
        param.put("pluginversion", "11.36.5");
        param.put("typeid", "1");
        param.put("_appid", "wxapp");
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("https://lapp.api.autohome.com.cn/leaderboard/landing/page/scene/unite", param);
        if (httpGet.isSuccess()) {
            ReturnValue<RankElectricCarList> deserialize = httpGet.deserialize(new TypeReference<ReturnValue<RankElectricCarList>>() {
            });
            if (deserialize != null && deserialize.getResult() != null && !CollectionUtils.isEmpty(deserialize.getResult().getCarList())) {
                List<RankElectricCar> carList = deserialize.getResult().getCarList();
                Optional<RankElectricCar> first = carList.stream().filter(it -> String.valueOf(seriesId).equals(it.getSeriesId())).findFirst();
                if (first.isPresent()) {
                    String rank = first.get().getRank();
                    try {
                        rank = String.valueOf(Integer.parseInt(rank));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return minPrice + "-" + maxPrice + "万" + levelName + "销量排行第" + rank + "名";
                }
            }
        }
        return "暂无";
    }

    private String getBrandLogo(int brandId) {
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("https://car.api.autohome.com.cn/v2/CarPrice/Brand_GetBrandLogo.ashx?_appid=car&brandid=" + brandId);
        if (httpGet.getBody() != null) {
            Map deserialize = JacksonHelper.deserialize(httpGet.getBody(), Map.class);
            if (deserialize != null) {
                Object result = deserialize.get("result");
                if (result != null && result instanceof Map) {
                    return (String) ((Map<?, ?>) result).get("brandlogo");
                }
            }
        }
        return "";
    }

    private SpecUsedPrice getErShouCar(int seriesId) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "car");
        param.put("seriesid", seriesId);
//        param.put("pid", 320000);
        param.put("cid", 110100);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://api.assess.che168.com/trend/specseriesassess", param);
        if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
            ReturnValue<SpecUsedPrice> deserialize = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<SpecUsedPrice>>() {
            });
            if (deserialize != null && deserialize.getReturncode() == 0) {
                return deserialize.getResult();
            }
        }
        return null;
    }

    private void pushArticleCard(List<ArticleCard> cardList) {
        Map<String, Object> map = new HashMap<>();
        map.put("cpId", "16806d5c13135000");
        map.put("data", cardList);
        String json = JacksonHelper.serialize(map);
        JobLog.info("request " + json);

        Map<String, Object> headers = new HashMap<>();
        long timestamp = System.currentTimeMillis();
        headers.put("timestamp", String.valueOf(timestamp));
        headers.put("sign", sign(timestamp));
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson("https://honorsearch-drcn.hiboard.hihonorcloud.com/honorsearch-openplat/v1/dataPush/batchPush/feeds", json, headers);
        int statusCode = httpResult.getStatusCode();
        if (statusCode == 200) {
            String body = httpResult.getBody();
            JobLog.info("result " + body);
        }
    }

    private DataMore<Article> getArticleList(String searchAfter) {
        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "light_api");
        params.put("page_size", 20);
        params.put("search_after", searchAfter);
        params.put("class_ids", "1,3,60,82,97,102,107");
        params.put("main_data_types", "cms,video");
        params.put("express_message", 1);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://maindata.api.autohome.com.cn/data/more/contentplat_query_videos_and_articles_by_classIds", params);
        JobLog.info("honorArticleSyncJob getArticleList http://maindata.api.autohome.com.cn/data/more/contentplat_query_videos_and_articles_by_classIds param="+params.toString() );
        if (httpResult.getStatusCode() == 200) {
            String body = httpResult.getBody();
            ReturnValue<DataMore<Article>> deserialize = JacksonHelper.deserialize(body, new TypeReference<ReturnValue<DataMore<Article>>>() {
            });
            if (deserialize != null && deserialize.getResult() != null) {
                return deserialize.getResult();
            }
        }
        return null;
    }

    public String sign(long timestamp) {
        String cpId = "16806d5c13135000";
        String secret = "374f44057890319f2722065ad305e19e6bfd311b890cceba780871820cb79a1d";
        StringBuilder stringBuilder = new StringBuilder().append(cpId).append(timestamp);
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKey);
            byte[] bytes = mac.doFinal(stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(bytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            JobLog.error("Failed to generate pushDataSign", e);
        }
        return null;
    }

    @Override
    public String honorSign(String secret, String content) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKey);
            byte[] bytes = mac.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(bytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            JobLog.error("Failed to generate pushDataSign", e);
        }
        return null;
    }

    public String honorSign2(String secret, String content) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKey);
            byte[] bytes = mac.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return new String(Base64.getEncoder().encode(bytes), StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            JobLog.error("Failed to generate pushDataSign", e);
        }
        return null;
    }

    /**
     * 根据车系的 最高价、最低价，计算查榜单的价格区间
     * @param minPrice
     * @param maxPrice
     * @return
     */
    private int[] getPriceRange(int minPrice, int maxPrice) {
        int[][] priceRange = new int[][]{
                new int[]{0, 5},
                new int[]{5, 10},
                new int[]{10, 15},
                new int[]{15, 20},
                new int[]{20, 25},
                new int[]{25, 35},
                new int[]{35, 50},
                new int[]{50, 100},
                new int[]{100, 9000},
        };
        for (int[] range : priceRange) {
            if (maxPrice < range[0] || minPrice > range[1]) {
                continue;
            }
            return range;
        }
        return new int[]{0, 9000};
    }
}
