package com.autohome.applet.service.zfb.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class Mp4DirectTransferService {
    // 限速500K (500 * 1024字节/秒)
    private static final int SPEED_LIMIT_BYTES = 500 * 1024;
    // 时间窗口，用于限速计算 (1秒)
    private static final int TIME_WINDOW_MS = 1000;

    /**
     * 直接流式传输MP4文件（不落地）
     * @param sourceUrl MP4源地址
     * @param uploadUrl 上传目标地址
     * @return 是否传输成功
     */
    public boolean transferMp4Directly(String sourceUrl, String uploadUrl) {
        HttpURLConnection downloadConnection = null;
        HttpURLConnection uploadConnection = null;
        InputStream downloadIn = null;
        OutputStream uploadOut = null;
        PrintWriter writer = null;

        try {
            // 1. 建立下载连接
            URL downloadUrl = new URL(sourceUrl);
            downloadConnection = (HttpURLConnection) downloadUrl.openConnection();
            downloadConnection.setRequestMethod("GET");
            downloadConnection.setConnectTimeout(5000);
            downloadConnection.setReadTimeout(10000);

            int downloadResponseCode = downloadConnection.getResponseCode();
            if (downloadResponseCode != HttpURLConnection.HTTP_OK) {
                System.err.println("下载请求失败，响应码: " + downloadResponseCode);
                return false;
            }

            // 获取文件名（从Content-Disposition或URL中提取）
            String fileName = getFileName(downloadConnection, sourceUrl);

            // 2. 建立上传连接
            URL uploadUrlObj = new URL(uploadUrl);
            uploadConnection = (HttpURLConnection) uploadUrlObj.openConnection();
            uploadConnection.setRequestMethod("POST");
            uploadConnection.setDoOutput(true);
            uploadConnection.setConnectTimeout(5000);
            uploadConnection.setReadTimeout(0); // 上传可能耗时较长，不设置读取超时

            String boundary = "====" + System.currentTimeMillis() + "====";
            uploadConnection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            // 3. 准备上传流
            uploadOut = uploadConnection.getOutputStream();
            writer = new PrintWriter(new OutputStreamWriter(uploadOut, "UTF-8"), true);

            // 写入multipart头部
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + fileName + "\"").append("\r\n");
            writer.append("Content-Type: video/mp4").append("\r\n");
            writer.append("\r\n").flush();

            // 4. 流式传输（边下载边上传）
            downloadIn = downloadConnection.getInputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            long totalBytesTransferred = 0;
            long startTime = System.currentTimeMillis();

            System.out.println("开始流式传输文件: " + fileName);

            while ((bytesRead = downloadIn.read(buffer)) != -1) {
                uploadOut.write(buffer, 0, bytesRead);
                totalBytesTransferred += bytesRead;

                // 控制传输速度（同时限制下载和上传速度）
                enforceSpeedLimit(totalBytesTransferred, startTime);

                // 定期刷新输出流
                if (totalBytesTransferred % (SPEED_LIMIT_BYTES / 10) == 0) {
                    uploadOut.flush();
                }
            }

            // 完成上传
            uploadOut.flush();
            writer.append("\r\n").flush();
            writer.append("--" + boundary + "--").append("\r\n").flush();

            // 5. 检查上传结果
            int uploadResponseCode = uploadConnection.getResponseCode();
            if (uploadResponseCode >= 200 && uploadResponseCode < 300) {
                System.out.println("文件流式传输成功，总传输字节数: " + totalBytesTransferred);
                return true;
            } else {
                System.err.println("文件上传失败，响应码: " + uploadResponseCode);
                return false;
            }

        } catch (Exception e) {
            System.err.println("文件传输过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            // 关闭所有资源
            try {
                if (writer != null) writer.close();
                if (uploadOut != null) uploadOut.close();
                if (downloadIn != null) downloadIn.close();
                if (uploadConnection != null) uploadConnection.disconnect();
                if (downloadConnection != null) downloadConnection.disconnect();
            } catch (IOException e) {
                System.err.println("关闭资源时发生错误: " + e.getMessage());
            }
        }
    }

    /**
     * 从响应头或URL中提取文件名
     */
    private String getFileName(HttpURLConnection connection, String url) {
        // 尝试从Content-Disposition获取
        String disposition = connection.getHeaderField("Content-Disposition");
        if (disposition != null && disposition.contains("filename=")) {
            String fileName = disposition.substring(disposition.indexOf("filename=") + 9);
            if (fileName.startsWith("\"") && fileName.endsWith("\"")) {
                fileName = fileName.substring(1, fileName.length() - 1);
            }
            return fileName;
        }

        // 从URL中提取
        String urlFileName = new File(url).getName();
        return urlFileName.isEmpty() ? "streamed.mp4" : urlFileName;
    }

    /**
     * 限速控制实现
     */
    private void enforceSpeedLimit(long totalBytes, long startTime) throws InterruptedException {
        long elapsedTime = System.currentTimeMillis() - startTime;

        // 当传输时间超过1秒时检查速度限制
        if (elapsedTime > TIME_WINDOW_MS) {
            // 计算当前速度 (字节/秒)
            double currentSpeed = (totalBytes * 1000.0) / elapsedTime;

            // 如果超过限速，计算需要休眠的时间
            if (currentSpeed > SPEED_LIMIT_BYTES) {
                // 计算理论上应该花费的时间
                long expectedTime = (long)(totalBytes * 1000.0 / SPEED_LIMIT_BYTES);
                long sleepTime = expectedTime - elapsedTime;

                if (sleepTime > 0) {
                    TimeUnit.MILLISECONDS.sleep(sleepTime);
                }
            }
        }
    }

    public static void main(String[] args) {
        // 示例用法
        Mp4DirectTransferService service = new Mp4DirectTransferService();
        String sourceUrl = "https://vc13-pa1-pl-agv.autohome.com.cn/video-10/E3BD4E39114FD258/2023-11-08/2552EC5900C643FE6F15C4841F4F2CE2-100-wm.mp4?key=63A2653770B39FEAC96B22AE160F4D07&time=1754393625";  // 源MP4地址
        String uploadUrl = "http://openapi.alipay.com/gateway.do";  // 目标上传地址

        boolean b = service.transferMp4Directly(sourceUrl, uploadUrl);
        System.out.println(b);
    }

}
