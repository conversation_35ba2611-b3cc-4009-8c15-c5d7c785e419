package com.autohome.applet.service.article.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.maindata.MainDataArticleDataDto;
import com.autohome.applet.service.article.MaindataArticleService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.HashMap;

@Slf4j
@Service
public class MaindataArticleServiceImpl implements MaindataArticleService {

    @Override
    public MainDataArticleDataDto getCmsArticleInfo(int pageNum, int pageSize, int datatype, String beginTime, String endTime) {
        String url =  "http://maindata.api.autohome.com.cn/data/page/cms_video_datasyn_by_updatetime";
        HashMap<String, Object> param = new HashMap<>();
        param.put("_appid", "m");
        param.put("page_num", pageNum);
        param.put("page_size", pageSize);
        param.put("datatype",datatype);
        param.put("syn_begin_time", beginTime);
        param.put("syn_end_time", endTime);
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet(url, param);
        if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
            ReturnValue<MainDataArticleDataDto> returnValue = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<MainDataArticleDataDto>>() {
            });
            if (returnValue != null && returnValue.getReturncode() == 0 && returnValue.getResult() != null) {
                return returnValue.getResult();
            }
        }

        return null;
    }
}
