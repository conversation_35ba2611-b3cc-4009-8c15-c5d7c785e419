package com.autohome.applet.service;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.dealer.SpecDealPrice;
import com.autohome.applet.model.dto.netcoreapi.output.GetSpecInfoquick;
import com.autohome.applet.model.dto.netcoreapi.series.SeriesInfo;
import com.autohome.applet.model.dto.netcoreapi.series.SeriesRate;
import com.autohome.applet.model.dto.netcoreapi.series.SpecInfo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public interface SeriesService {
    ReturnValue<SeriesInfo> GetSeriesInfo(int seriesId) throws ExecutionException, InterruptedException;

    ReturnValue<SeriesInfo> GetSeriesInfo(int seriesId, int cityid) throws ExecutionException, InterruptedException;

    ReturnValue<GetSpecInfoquick> GetSpecInfoquick(int seriesId, int cityid) throws ExecutionException, InterruptedException;

    ReturnValue<?> GetAttentionBySeries(String seriesid) throws ExecutionException, InterruptedException;

    ReturnValue<?> GetAttentionBySeries(String seriesid, int quicktype) throws ExecutionException, InterruptedException;

    CompletableFuture<ReturnValue<SpecInfo>> spec_infobyspecid(int specId);

    ReturnValue<?> getBannerAttentionBySeries(String seriesIds, int cityid);

    /**
     * 批量获取车型+城市销售价格
     * */
    List<SpecDealPrice> getDealPriceListBySpecIds(String specIds, int cityId);
    /**
     * 批量获取车型+城市销售价格+车系+车型基本信息
     * */
    List<SpecDealPrice> getDealPriceSpecBaseInfoListBySpecIds(String specIds, int cityId);
    Integer getSpecid(String seriesIds);

    ReturnValue<List<SeriesRate>> getHotSpecidList(String seriesIds);
    /**
     * 车系亮点
     * @param request
     * @return
     */
    ReturnValue<?>  getbrightpointsconfigbyseriesid(HttpServletRequest request);
}
