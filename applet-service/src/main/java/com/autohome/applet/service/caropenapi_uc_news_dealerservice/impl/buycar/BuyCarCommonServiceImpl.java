package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl.buycar;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.buycar.SpecParam;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.buycar.BuyCarCommonService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Slf4j
@Service
public class BuyCarCommonServiceImpl implements BuyCarCommonService {

    /**
     * 根据车型id获取车型信息[单个获取是否免稅信息]
     *
     * @param _appid
     * @param specid
     * @return
     */
    @Override
    public ResponseContent<SpecParam> spec_parambyspecid(String _appid, String specid) {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("specid", specid);
            //筛选车系
//            String result = carApi.get("car.api.spec_parambyspecid", param);
//            ResponseContent<SpecParam> responseSpecContent = JSON.parseObject(result, new TypeReference<ResponseContent<SpecParam>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://car.api.autohome.com.cn/v1/carprice/spec_parambyspecid.ashx", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }

            ResponseContent<SpecParam> responseSpecContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecParam>>() {
            });
            if (responseSpecContent == null || responseSpecContent.getResult() == null) {
                return null;
            }

            return responseSpecContent;
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("spec_detailbyspeclist-WithException", e.getMessage())));
            log.error("spec_detailbyspeclist-WithException", e);
            return null;
        }

    }
}
