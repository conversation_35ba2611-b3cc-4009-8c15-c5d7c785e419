package com.autohome.applet.service.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.newenergy.KoubeiInfoRes;
import com.autohome.applet.model.dto.newenergy.KoubeiInfoShareRes;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class KouBeiListDal {
    private final String koubeiUrl = "http://koubei.api.sjz.autohome.com.cn/api/masterdata/series_evaluation_list_v1";
    //_appid=app&id={seriesId}&pageindex={pageIndex}&pagesize={pageSize}&photosize=180&grade={grade}&year=0&isappend=0&order=0&provinceid="
    public KoubeiInfoRes list(Integer seriesId, Integer grade, Integer pageSize, Integer pageIndex) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "app");
            param.put("id", seriesId);
            param.put("pageIndex", pageIndex);
            param.put("pageSize", pageSize);
            param.put("photosize", 180);
            param.put("grade", grade);
            param.put("year", 0);
            param.put("isappend", 0);
            param.put("order", 0);
            param.put("provinceid", "");

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(koubeiUrl, param);

            if (httpResult == null || httpResult.getStatusCode() != 200 ) {
                log.error("KouBeiListDal {} is error result: {}",koubeiUrl, JacksonHelper.serialize(httpResult));
                throw new BusinessException("源接口调用失败",1001);
            }

            if (StringUtils.isEmpty(httpResult.getBody())){
                log.warn("KouBeiListDal 源接口调用为null url:{} , param: {}",koubeiUrl,JacksonHelper.serialize(param));
                return null;
            }

            KoubeiInfoRes returnValue= JacksonHelper.deserialize(httpResult.getBody(), KoubeiInfoRes.class);

            if (returnValue == null){
                log.warn("KouBeiListDal 源接口调用为null url:{} , param: {}",koubeiUrl,JacksonHelper.serialize(param));
                return null;
            }

            if (returnValue.getReturncode() != 0){
                log.error("KouBeiListDal {} is error result: {}",koubeiUrl, JacksonHelper.serialize(httpResult));
                throw new BusinessException("源接口调用失败",1001);
            }

            if (returnValue.getResult() == null){
                log.warn("KouBeiListDal 源接口调用为null url:{} , param: {}",koubeiUrl,JacksonHelper.serialize(param));
                return null;
            }

            return returnValue;
        } catch (Exception e) {
            log.error("获取口碑列表失败，seriesId:{},grade:{}", seriesId, grade, e);
        }
        return null;
    }


    /**
     * 获取口碑车主分享列表
     * @param cityId
     * @param seriesId
     * @param range
     * @return
     */
    public KoubeiInfoShareRes list_series_nev_list(Integer cityId, Integer seriesId, Integer range, Integer grade, Integer pagesize) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "app");
            param.put("id", seriesId);
            param.put("city", cityId);
            param.put("range", range);
            param.put("grade", grade);
            param.put("pagesize", pagesize);
            String url = "http://koubei.api.sjz.autohome.com.cn/api/masterdata/series_nev_list";

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);

            if (httpResult == null || httpResult.getStatusCode() != 200 ) {
                log.error("KouBeiListDal list_series_nev_list {} is error result: {}",url, JacksonHelper.serialize(httpResult));
                throw new BusinessException("源接口调用失败",1001);
            }

            if (StringUtils.isEmpty(httpResult.getBody())){
                log.warn("KouBeiListDal 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(param));
                return null;
            }

            KoubeiInfoShareRes returnValue= JacksonHelper.deserialize(httpResult.getBody(),KoubeiInfoShareRes.class);

            if (returnValue == null){
                log.warn("KouBeiListDal 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(param));
                return null;
            }

            if (returnValue.getReturncode() != 0 ){
                log.error("KouBeiListDal list_series_nev_list {} is error result: {}",url, JacksonHelper.serialize(httpResult));
                throw new BusinessException("源接口调用失败",1001);
            }

            if (returnValue.getResult() == null){
                log.warn("KouBeiListDal 源接口调用为null url:{} , param: {}",url,JacksonHelper.serialize(param));
                return null;
            }

            return returnValue;
        } catch (Exception e) {
            log.error("获取口碑车主分享列表失败，seriesId:{},cityId:{},range:{}", seriesId, cityId, range, e);
        }
        return null;
    }


}
