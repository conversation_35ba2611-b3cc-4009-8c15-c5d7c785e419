package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Service
public class PortRayalServiceimpl implements PortRayalService {

    /**
     *
     * @param seriesid
     * @return
     */
    @Override
    public ReturnValue<?> getCpcInfo(String seriesid) {

        Map<String, Object> params = new HashMap<>();
        if (seriesid != null) {
            params.put("id",seriesid);
        }
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance(350).httpGet("http://portrayal-app-personas.autohome.com.cn/pp/api/competing/getSeriCompetingInfoById", params);
        if (httpResult.getStatusCode() == 200) {
            return JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        } else {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }
    }

}
