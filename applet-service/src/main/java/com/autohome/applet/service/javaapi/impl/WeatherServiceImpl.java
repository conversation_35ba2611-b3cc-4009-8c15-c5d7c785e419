package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.weather.Day;
import com.autohome.applet.model.dto.weather.Forecasts;
import com.autohome.applet.service.javaapi.WeatherService;
import com.autohome.applet.util.FileUtil;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.comm.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class WeatherServiceImpl implements WeatherService {

    private final String weatherUrl = "http://ownermp.corpautohome.com/ownerserviceapi/weather/getWeatherForeCast";

    public static final Map weatherIconsTypeMap = new HashMap<>();

    private final String picUrl = "https://zt.autohome.com.cn/liveportal/images/weather/weather_";

    private void LoadLoaclFileContent() {
        HashMap<Integer, String> shortnameList = FileUtil.readFileContentMap("static/weatherIconsType.csv");

        if (shortnameList != null && shortnameList.size() > 0) {
            // 处理csv 文件,如文件有修改，请重新修改class 对应的参数
            for (Integer index : shortnameList.keySet()) {
                if (index == 0) continue;
                String s = shortnameList.get(index).replaceAll("\\uFEFF", "");

                if (StringUtils.isNullOrEmpty(s))
                    continue;

                String[] arr = s.split(",");
                if (arr == null || arr.length != 2) {
                    continue;
                }

                String _weather = arr[0].trim();
                Integer _type = Integer.parseInt(arr[1].trim());

                weatherIconsTypeMap.put(  _weather, _type);
            }
        }
    }

    @Override
    public ReturnValue<Day> getWeatherInfo(Integer cityid) {
        if (weatherIconsTypeMap.size() == 0) {
            this.LoadLoaclFileContent();
        }

        Map<String, Object> param = new HashMap<>();
        param.put("cityid", cityid);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(weatherUrl, param);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<Forecasts> resp =  JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Forecasts>>() {});

        if (resp != null && resp.getReturncode() == 0 && resp.getResult() != null) {
            // 做天气类型图片替换
            List<Day> days = resp.getResult().getForecasts();

            if (days.size() == 0) {
                return ReturnValue.buildErrorResult(1002, "源接口查询失败");
            }

//            for (Day day: days
//            ) {
//                String _dw = day.getDayweather();
//                String _nw = day.getNightweather();
//
//                Integer dayType = (Integer) (weatherIconsTypeMap.containsKey(_dw) ? weatherIconsTypeMap.get(_dw) : weatherIconsTypeMap.get("未知"));
//                Integer nightType = (Integer) (weatherIconsTypeMap.containsKey(_nw) ? weatherIconsTypeMap.get(_nw) : weatherIconsTypeMap.get("未知"));;
//
//                day.setDayweathericon(picUrl + dayType + ".png");
//                day.setNightweathericon(picUrl + nightType + ".png");
//            }

            Day day = days.get(0);
            String _dw = day.getDayweather();
            String _nw = day.getNightweather();

            Integer dayType = (Integer) (weatherIconsTypeMap.containsKey(_dw) ? weatherIconsTypeMap.get(_dw) : weatherIconsTypeMap.get("未知"));
            Integer nightType = (Integer) (weatherIconsTypeMap.containsKey(_nw) ? weatherIconsTypeMap.get(_nw) : weatherIconsTypeMap.get("未知"));;

            day.setDayweathericon(picUrl + dayType + ".png");
            day.setNightweathericon(picUrl + nightType + ".png");

            return ReturnValue.buildSuccessResult(day);
        } else {
            return ReturnValue.buildErrorResult(1002, "源接口查询失败");
        }

    }
}
