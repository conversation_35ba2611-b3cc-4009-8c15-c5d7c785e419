package com.autohome.applet.service.resourcefeedpush.impl;

import com.alibaba.fastjson.JSON;
import com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.maindata.HotDataByBizType;
import com.autohome.applet.model.dto.resourcefeedpush.PushAtomicResultDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BadiduDTO_Data;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BadiduDTO_Display;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BadiduDTO_Poi_list;
import com.autohome.applet.model.dto.resourcefeedpush.baidu.BaiduDTO;
import com.autohome.applet.model.enums.MainDataTypeToBizTypeEnum;
import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.service.douyin.impl.DouYinServiceImpl;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedService;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.ImageSizeUtil;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.protocol.ResponseContent;
import org.omg.CORBA.PRIVATE_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ResourceFeedPushToBaiduServiceImpl implements ResourceFeedPushService {
    @Autowired
    private ResourceFeedService resourceFeedService;
    @Autowired
    private DouYinService douYinService;


//    private static List<String> TAGS = Arrays.asList("单车评测", "双车评测", "多车评测", "单车试驾", "双车试驾", "多车试驾", "单车体验", "双车体验", "多车体验", "实拍解析", "到店实拍", "评测体验(其他)", "二手车评测", "二手车导购", "二手车检测", "二手车修整", "二手车买卖攻略", "二手车收车日记", "二手车行业动态", "二手车(其他)", "买车攻略", "提车报告", "买车/提车(其他)", "车闻趣事", "明星座驾", "美女模特", "汽车影视", "广告/宣传片", "玩车", "用车攻略", "车务办理", "用车成本", "交通违章及法规", "车辆故障", "汽车保养", "美容装饰", "汽车用品", "出行信息", "驾驶技巧", "汽车维修", "用车养车(其他)", "车型口碑", "自驾游", "越野", "汽车生活", "改装车", "摩托车", "房车", "特种车");
    private static List<String> TAGS = new ArrayList<>();

    private static Map<String, String> TOKENMAP = new HashMap<String, String>() {{
        put("club.m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("chejiahao.m.autohome.com.cn", "VjJzlZf24oiw1Lud");
        put("v.m.autohome.com.cn", "VjJzlZf24oiw1Lud");
    }};

    /**
     * 计数器
     */
    private PushAtomicResultDTO atomicPushAtomicResultDTO;
    private HashMap<String, PushAtomicResultDTO> atomicResultDTOHashMap;

    @Override
    public void doPush(PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        atomicResultDTOHashMap = new HashMap<>();
        atomicPushAtomicResultDTO = new PushAtomicResultDTO(0);
        List<PushResourceFeedCountDTO> pushResourceFeedCountDTOList = resourceFeedService.countPushResourceFeedDataDTO(pushResourceFeedDataQuery);
        pushResourceFeedCountDTOList.forEach(obj -> {
            atomicResultDTOHashMap.put(obj.getBizType(), new PushAtomicResultDTO(obj.getCount()));
            atomicPushAtomicResultDTO.setTotal(atomicPushAtomicResultDTO.getTotal() + obj.getCount());
        });

        sendLog(String.format("ResourceFeedPushService 推送时间：%s %s, 推送类型：%s, 推送步长：%s, 需要推送总数：%s", pushResourceFeedDataQuery.getStarttime(), pushResourceFeedDataQuery.getEndtime(), pushResourceFeedDataQuery.getBizTypeList(), pushResourceFeedDataQuery.getCount(), atomicPushAtomicResultDTO.getTotal()));
        sendLog(2, pushResourceFeedDataQuery.getBizTypeList(), pushResourceFeedDataQuery);
        //执行push操作
        if(atomicPushAtomicResultDTO.getTotal() > 0){
            push(pushResourceFeedDataQuery);
        }

        sendLog(1, pushResourceFeedDataQuery.getBizTypeList(), pushResourceFeedDataQuery);
    }

    private void push(PushResourceFeedDataQuery pushResourceFeedDataQuery){
        List<PushResourceFeedDataDTO> pushResourceFeedDataDTOList;
        Set<String> urlSet = new HashSet<>();
        do {
            List<BaiduDTO> baiduDTOList = new ArrayList<>();
            //获取feed数据流
            pushResourceFeedDataDTOList = resourceFeedService.listPushResourceFeedDataDTO(pushResourceFeedDataQuery);
            if(!CollectionUtils.isEmpty(pushResourceFeedDataDTOList)){

                //填充点赞数
                fillHotDataByBizTypeList(pushResourceFeedDataDTOList);

                for (PushResourceFeedDataDTO ext : pushResourceFeedDataDTOList) {
                    //更新计数器
                    PushAtomicResultDTO pushAtomicResultDTO = atomicResultDTOHashMap.get(ext.getBizType());

                    pushAtomicResultDTO.getPushDone().addAndGet(1);
                    atomicPushAtomicResultDTO.getPushDone().addAndGet(1);

                    if(checkClubBiz(ext)){
                        String clubUrl = getClubUrl(Integer.valueOf(ext.getBizId()));
                        if(StringUtils.isEmpty(clubUrl)){
                            log.warn("push no loc biztype:{}, id:{}, bizid:{}, info:{}", ext.getBizType(), ext.getId(), ext.getBizId(), JacksonHelper.serialize(ext));
                            pushAtomicResultDTO.getIgnore().addAndGet(1);
                            continue;
                        }
                        if (clubUrl != null) {
                            clubUrl = clubUrl.replace("club.", "club.m.");
                            if (!urlSet.contains(clubUrl)) {
                                urlSet.add(clubUrl);
                                BaiduDTO item = buildBaiduDTO(ext, clubUrl);
                                baiduDTOList.add(item);
                                log.info("ResourceFeedPushService push item " + ext.getId() + " " + ext.getBizId() + " " + ext.getPublishTime() + " " + clubUrl);
                            }
                            else{
                                //去重，存入忽略数据中
                                pushAtomicResultDTO.getIgnore().addAndGet(1);
                            }
                        }
                    }
                    else{
                        String bizUrl = getBizH5Url(ext);
                        if(StringUtils.isEmpty(bizUrl)){
                            log.warn("push no loc biztype:{}, id:{}, bizid:{}, info:{}", ext.getBizType(), ext.getId(), ext.getBizId(), JacksonHelper.serialize(ext));
                            pushAtomicResultDTO.getIgnore().addAndGet(1);
                            continue;
                        }
                        if (!urlSet.contains(bizUrl)) {
                            urlSet.add(bizUrl);
                            BaiduDTO item = buildBaiduDTO(ext, bizUrl);
                            baiduDTOList.add(item);
                            log.info("ResourceFeedPushService push item " + ext.getId() + " " + ext.getBizId() + " " + ext.getPublishTime() + " " + bizUrl);
                        }
                        else{
                            //去重，存入忽略数据中
                            pushAtomicResultDTO.getIgnore().addAndGet(1);
                        }
                    }
                }
                sendToBaidu(baiduDTOList);
                //设置offset
                if (pushResourceFeedDataDTOList.size() > 0) {
                    pushResourceFeedDataQuery.setOffset(pushResourceFeedDataQuery.getOffset() + pushResourceFeedDataDTOList.size());
                }
                sendLog(String.format("ResourceFeedPushService 已推送: %s, 当前偏移量: %s, 最后一条id: %s", atomicPushAtomicResultDTO.getPushDone().get(), pushResourceFeedDataQuery.getOffset(), pushResourceFeedDataDTOList.get(pushResourceFeedDataDTOList.size()-1).getId()));
            }
        } while (!CollectionUtils.isEmpty(pushResourceFeedDataDTOList));
    }

    private BaiduDTO buildBaiduDTO(PushResourceFeedDataDTO ext, String url) {
        BadiduDTO_Display display = buildBaiduDOTDisplay(ext, url);

        BadiduDTO_Data data = new BadiduDTO_Data();
        data.setDisplay(display);

        BaiduDTO baiduDTO = new BaiduDTO();
        baiduDTO.setLoc(url); //必选 落地页
        baiduDTO.setLastmod(ext.getModifyTime());//可选
        baiduDTO.setChangefreq("always");//更新频率
        baiduDTO.setPriority(1);//优先级
        baiduDTO.setData(data);
        baiduDTO.setBiz(ext.getBizType());
        return baiduDTO;
    }
    private BadiduDTO_Display buildBaiduDOTDisplay(PushResourceFeedDataDTO b, String url) {
        BadiduDTO_Display display = new BadiduDTO_Display();
        display.setUrl("");//落地页 必选
        display.setTitle(b.getTitle());//标题 必选
        display.setContent(b.getContent());
        List<String> imgList;
        if (!StringUtils.isEmpty(b.getGraphicImgList())) {
            imgList = Lists.newArrayList(b.getGraphicImgList().split("㊣"));
        } else if (!StringUtils.isEmpty(b.getGraphicImgList3())) {
            imgList = Lists.newArrayList(b.getGraphicImgList3().split("㊣"));
        } else {
            imgList = Lists.newArrayList(ImageSizeUtil.changeSize(b.getImgUrl()));
        }
        int i = 0;
        for (String s : imgList) {
            imgList.set(i, "\"" + s + "\"");
        }

        display.setThumbnail_img_list("[" + String.join(",", imgList) + "]");
        display.setVideo_url("");//没有视频填空 必选
        display.setVideo_thumbnail_url("");//视频封面 必选
        display.setAuthor_name(b.getAuthor());//作者姓名 必选
        //display.setAuthor_url("");//作者主页url 可选
        //display.setAuthor_tag("");//作者认证,无填空 可选
        display.setAuthor_img(!StringUtils.isEmpty(b.getAuthorIcon()) ? b.getAuthorIcon() : "https://x.autoimg.cn/news/miniprogram/touxiang2.png");//作者头像 必选
        //display.setAuthor_level("");//作这等级 可选
        display.setAuthor_fans(0);//作者粉丝 必选
        //display.setAuthor_fields("");//作者领域 可选
        //display.setAuthor_docs(0);//历史发文总数 可选
        display.setDate_published(b.getPublishTime().split(" ")[0]);//发布是时间 必选 YYYY-MM-DD
        //display.setQuality_score(0);//优质程度 可选
        display.setContent_quality_label("优质");//质量等级标签 必选
        if (!org.apache.commons.lang3.StringUtils.isEmpty(b.getCmsTags())) {
            display.setContent_tag(b.getCmsTags().replace("/", ",").replace(" ", ","));//内容标签 半角逗号分隔 可选
        }
        display.setLike_count(getIntegerValue(b.getLikeCount()));//赞数 必选
        //display.setUnlike_count(0);//踩  可选
        display.setComment_count(StringUtils.isEmpty(b.getReplyCount()) ? 0 : Integer.valueOf(b.getReplyCount()));//评论数 必选
        display.setComment_content("");//评论 必选
        display.setCollect_count(0);//收藏  必选
        display.setRepost_count(0);//转发数 必选
        display.setImage_count(imgList.size());//图片数  必选
        display.setVideo_list("");//视频列表,必选 没有清空
        display.setIs_video(1);//内容类型 1图文,2视频 必选
        display.setStatus("0".equals(b.getIsDelete())? 1 : 2);//状态 1正常 2删除 必选
        display.setSrc_type("汽车之家");//来源渠道 必选
        display.setSub_src("");//来源子分类 必选
        //display.setThird_id(1111);//第三方内容id 可选
        display.setIs_abroad(1);//1国内 2国外 必选
        display.setCountry("中国");//国家地区 必选
        display.setPoi_type(2);//1 poi坐标,2城市
        display.setUrl(url);
        if (org.apache.commons.lang3.StringUtils.isEmpty(display.getContent())) {
            if (!StringUtils.isEmpty(b.getSummary())) {
                display.setContent(b.getSummary());
            } else {
                display.setContent(display.getTitle());
            }
        }
        BadiduDTO_Poi_list poi_list = new BadiduDTO_Poi_list();
        poi_list.setCity_name(""); //城市名
        poi_list.setPoi_name("");//位置名
        display.setPoi_list(poi_list);
        return display;
    }
    private void sendToBaidu(List<BaiduDTO> list) {
        int success = 0;
        int error = 0;
        int baiduError = 0;
        int ignore = 0;
        int tagIgnore = 0;
        for (BaiduDTO baidu3GC : list) {
            String traceid = UUID.randomUUID().toString().replaceAll("-", "");
            String body = JSON.toJSONString(baidu3GC);

            PushAtomicResultDTO pushAtomicResultDTO = atomicResultDTOHashMap.get(baidu3GC.getBiz());

            try {
                if (!StringUtils.isEmpty(baidu3GC.getLoc())) {
                    if (baidu3GC.getLoc().contains("chejiahao")) {
                        boolean can = true;
                        if (StringUtils.isEmpty(baidu3GC.getData().getDisplay().getContent_tag())) {
                            can = false;
                        }
//                        else {
//
//                            int notContains = 0;
//                            for (String t : TAGS) {
//                                if (!baidu3GC.getData().getDisplay().getContent_tag().contains(t)) {
//                                    notContains += 1;
//                                }
//                            }
//                            if (notContains == TAGS.size()) {
//                                can = false;
//                            }
//                        }

                        if (can) {
                            log.info("baidu3GC tag " + baidu3GC.getLoc() + " " + baidu3GC.getData().getDisplay().getContent_tag());

                            URL url = new URL(baidu3GC.getLoc());
                            String host = url.getHost();
                            String baiduResp = postbaidu("http://ziyuan-data.baidu.com/json?site=https://" + host + "&token=" + TOKENMAP.get(host) + "&type=ugc&traceid=" + traceid, body, 5000, "application/json", "utf-8", "utf-8");

                            if (baiduResp.contains("success")) {
                                log.info("baidu3GC success " + traceid + " " + baidu3GC.getLoc() + " " + baiduResp + " " + body);
                                success += 1;
                                pushAtomicResultDTO.getSuccess().addAndGet(1);

                            } else {
                                log.warn("baidu3GC warn " + traceid + " " +baidu3GC.getLoc()  + " " + baiduResp + " " + body);
                                baiduError += 1;
                                pushAtomicResultDTO.getPushError().addAndGet(1);
                            }

                        } else {
                            tagIgnore += 1;
                            pushAtomicResultDTO.getTagIgnore().addAndGet(1);
                            log.info("baidu3GC tagIgnore info: " + body);
                        }
                    } else {
                        URL url = new URL(baidu3GC.getLoc());
                        String host = url.getHost();
                        String baiduResp = postbaidu("http://ziyuan-data.baidu.com/json?site=https://" + host + "&token=" + TOKENMAP.get(host) + "&type=ugc&traceid=" + traceid, body, 5000, "application/json", "utf-8", "utf-8");

                        if (baiduResp.contains("success")) {
                            log.info("baidu3GC success " + traceid + " " + baidu3GC.getLoc() + " " + baiduResp + " " + body);
                            success += 1;
                            pushAtomicResultDTO.getSuccess().addAndGet(1);
                        } else {
                            log.warn("baidu3GC warn " + traceid + " " + baidu3GC.getLoc() + " " + baiduResp + " " + body);
                            baiduError += 1;
                            pushAtomicResultDTO.getPushError().addAndGet(1);
                        }
                    }
                } else {
                    ignore += 1;
                    pushAtomicResultDTO.getIgnore().addAndGet(1);
                    log.warn("push no loc biztype :{}, loc:{}, info:{}", baidu3GC.getBiz(), baidu3GC.getLoc(), body);
                }

            } catch (Exception e) {
                error += 1;
                pushAtomicResultDTO.getError().addAndGet(1);
                log.error("sendToBaidu err " + baidu3GC.getLoc() + " " + body, e);
            }
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("sendToBaidu error:",  e);
            }

        }
        sendLog("ResourceFeedPushService baidudto total: " + list.size() + ", 有效推送:" + success + ", 忽略:" + ignore + ", tag 忽略:" + tagIgnore + ", 推送报错:" + baiduError + ", 报错:" + error);
    }
    private String getClubUrl(int id) {
        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://clubapi.in.autohome.com.cn/japi/topic/gettopicbyid?_appid=wxapp&topicid=" + id + "&fields=url,bbsid");
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ReturnValue<List<HashMap<String, String>>> clubResult = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<HashMap<String, String>>>>() {});

            if (clubResult != null && clubResult.getResult() != null && !CollectionUtils.isEmpty(clubResult.getResult())) {
                for (HashMap<String, String> map : clubResult.getResult()) {
                    Object bbsid = map.get("bbsid");
                    Object url = map.get("url");
                    if ("200053".equals(bbsid) || "2015625".equals(bbsid) || "2015626".equals(bbsid)) {
                        continue;
                    }
                    return String.valueOf(url);
                }
            }
        } catch (Exception ex) {
            log.error("ResourceFeedPushService getClubUrl error, id:%s", id, ex);
        }
        return null;
    }

    /**
     * 填充点赞数量
     * 每50个查一次
     * */
    private void fillHotDataByBizTypeList(List<PushResourceFeedDataDTO> pushResourceFeedDataDTOList) {
        int size = 50;

        int step = pushResourceFeedDataDTOList.size() / size + 1;

        for(int i=0; i<step; i++){
            StringBuilder sb = new StringBuilder();
            List<PushResourceFeedDataDTO> tempPushResourceFeedDataDTOList = pushResourceFeedDataDTOList.stream().skip(i * size)
                    .limit(size)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tempPushResourceFeedDataDTOList)){
                continue;
            }
            tempPushResourceFeedDataDTOList.forEach(obj -> {
                String mainDataType = MainDataTypeToBizTypeEnum.fromCode(obj.getBizType()).getValue();
                if(!"1".equals(obj.getIsDelete()) && !StringUtils.isEmpty(mainDataType)){
                    String tmp = mainDataType + "-like_count-" + obj.getBizId() + ",";
                    if(sb.indexOf(tmp) == -1){
                        sb.append(tmp);
                    }
                }
            });
            if(sb.length() == 0){
                continue;
            }
            List<HotDataByBizType> hotDataByBizTypeList = getHotDataByBizTypeList(sb.toString());
            if(!CollectionUtils.isEmpty(hotDataByBizTypeList)){
                pushResourceFeedDataDTOList.forEach(obj -> {
                    String mainDataType = MainDataTypeToBizTypeEnum.fromCode(obj.getBizType()).getValue();
                    if(StringUtils.isEmpty(mainDataType)){
                        return;
                    }
                    HotDataByBizType tmpHotDataByBizType = hotDataByBizTypeList.stream().filter(o->String.valueOf(o.getBizId()).equals(obj.getBizId()) && o.getMainDataType().equals(mainDataType)).findFirst().orElse(null);
                    if(tmpHotDataByBizType != null){
                        obj.setLikeCount(String.valueOf(tmpHotDataByBizType.getCount()));
                    }
                    else{
                        obj.setLikeCount("0");
                    }
                });
            }
        }
    }

    /**
     * 获取点赞数量
     * wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=209799341
     * 示例：http://maindata.api.autohome.com.cn/hotdata/get_hot_data_by_biz_type?_appid=applet&keys=5-like_count-108554207,33-like_count-46510367
     * */
    private List<HotDataByBizType> getHotDataByBizTypeList(String param) {
        try {
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://maindata.api.autohome.com.cn/hotdata/get_hot_data?_appid=cms&value=" + param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ReturnValue<List<HotDataByBizType>> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<HotDataByBizType>>>() {});

            if (result != null && result.getResult() != null && !CollectionUtils.isEmpty(result.getResult())) {
                return result.getResult();
            }
        } catch (Exception ex) {
            log.error("ResourceFeedPushService getHotDataByBizTypeList error, id:%s", param, ex);
        }
        return null;
    }

    private String postbaidu(String url, String params, Integer timeout, String contentType, String sendEncoding, String receiveEncoding) {

        PrintWriter printWriter = null;
        HttpURLConnection con = null;
        BufferedReader in = null;

        try {
            URL obj = new URL(url);
            con = (HttpURLConnection) obj.openConnection();
            con.setRequestMethod("POST");

            con.setRequestProperty("Content-Type", contentType);
            con.setRequestProperty("connection", "Keep-Alive");
            con.setRequestProperty("Content-Length", String.valueOf(params.length()));
//            con.setRequestProperty("APPKEY", "88A51C6D0A060EA44E0C73D9CED5D2FA");
            //con.setRequestProperty("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString(("uc-news-quickappservice:8sOG24^gr^").getBytes()));
            // 设置连接主机超时
            con.setConnectTimeout(timeout);
            // 设置从主机读取数据超时
            con.setReadTimeout(timeout);

            // 发送 post 必须
            con.setDoOutput(true);
            con.setDoInput(true);


            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(con.getOutputStream(), sendEncoding));
            bw.write(params);
            bw.flush();
            bw.close();


            // 根据ResponseCode判断连接是否成功
            int responseCode = con.getResponseCode();

            in = new BufferedReader(

                    new InputStreamReader(responseCode == 200 ? con.getInputStream() : con.getErrorStream(), receiveEncoding));


            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
                //response.append("\r\n");
            }
            in.close();

            return response.toString();


        } catch (Exception e) {
            log.error("postbaidu error param:%s", JacksonHelper.serialize(params), e);
            return e.getMessage();
        } finally {
            if (con != null) {
                con.disconnect();
            }

            if (printWriter != null) {
                printWriter.close();
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("postbaidu BufferedReader error param:%s", JacksonHelper.serialize(params), e);
                }
            }
        }
    }

    private boolean checkClubBiz(PushResourceFeedDataDTO dto){
        if("5".equals(dto.getBizType()) || "33".equals(dto.getBizType()) || "66".equals(dto.getBizType()) || "74".equals(dto.getBizType())){
            return true;
        }
        return false;
    }

    private String getBizH5Url(PushResourceFeedDataDTO dto){
        String url = "";
        switch (dto.getBizType()){
            case "1":{
                url = "https://m.autohome.com.cn/news/" + DateHelper.serialize(DateHelper.deserialize(dto.getPublishTime()), DateHelper.DATEFORMAT_YEARMONTH) + "/" + dto.getBizId() + ".html";
                break;
            }
            case "12":{
                url = "https://chejiahao.m.autohome.com.cn/info/" + dto.getBizId();
                break;
            }
            case "13":{
                url = "https://chejiahao.m.autohome.com.cn/info/" + dto.getBizId();
                break;
            }
            case "11":{
                break;
            }
            case "25":{
                break;
            }
            case "36":{
                break;
            }
            case "63":{
                break;
            }
            case "74":{

                break;
            }
            case "212":{
                break;
            }
            case "201":{
                url = "https://yczj.m.autohome.com.cn/app/carcasedetail?carCaseId=" + dto.getBizId();
                break;
            }
            case "700112":{
                url = "https://m.autohome.com.cn/fastmessage/detail/" + dto.getBizId() + ".html";
                break;
            }

        }
        return url;
    }

    private void sendLog(int type, List<Integer> bizList, PushResourceFeedDataQuery pushResourceFeedDataQuery){
        for (Integer i : bizList) {
            String key = String.valueOf(i);
            String des = MainDataTypeToBizTypeEnum.fromCode(key).getDes();
            PushAtomicResultDTO pushAtomicResultDTO = atomicResultDTOHashMap.get(key);
            if(pushAtomicResultDTO == null){
                pushAtomicResultDTO = new PushAtomicResultDTO(0);
            }
            String logContent = "";
            switch (type){
                case 1:{
                    logContent = String.format("ResourceFeedPushService 推送时间：%s %s, 数据类别: %s-%s, 需推送: %s, 实际推送: %s, 有效推送: %s, 忽略: %s, tag 忽略: %s, 推送报错: %s, 报错: %s"
                            , pushResourceFeedDataQuery.getStarttime(), pushResourceFeedDataQuery.getEndtime(), key, des, pushAtomicResultDTO.getTotal(), pushAtomicResultDTO.getPushDone().get(), pushAtomicResultDTO.getSuccess().get(), pushAtomicResultDTO.getIgnore().get()
                            , pushAtomicResultDTO.getTagIgnore().get(), pushAtomicResultDTO.getPushError().get(), pushAtomicResultDTO.getError().get());

                    douYinService.addBaiduReportRedis(BAIDU_3GC_KEY, pushResourceFeedDataQuery.getDaily(),Integer.parseInt(key),1,0,pushAtomicResultDTO.getPushDone().get());
                    douYinService.addBaiduReportRedis(BAIDU_3GC_KEY, pushResourceFeedDataQuery.getDaily(),Integer.parseInt(key),1,1, pushAtomicResultDTO.getSuccess().get());
                    douYinService.addBaiduReportRedis(BAIDU_3GC_KEY, pushResourceFeedDataQuery.getDaily(),Integer.parseInt(key),1,2,pushAtomicResultDTO.getPushError().get());

                    break;
                }
                case 2:{
                    logContent = String.format("ResourceFeedPushService 推送时间：%s %s, 数据类别: %s, 需推送: %s", pushResourceFeedDataQuery.getStarttime(), pushResourceFeedDataQuery.getEndtime(), key, pushAtomicResultDTO.getTotal());
                    break;
                }
            }
            XxlJobHelper.log(logContent);
            log.info(logContent);
        }
    }
    private void sendLog(String logContent){
        XxlJobHelper.log(logContent);
        log.info(logContent);
    }
    private int getIntegerValue(String value){
        try{
            int result = Integer.valueOf(value);
            return result < 0 ? 0 : result;
        }
        catch (Exception ex){
            return 0;
        }
    }
}
