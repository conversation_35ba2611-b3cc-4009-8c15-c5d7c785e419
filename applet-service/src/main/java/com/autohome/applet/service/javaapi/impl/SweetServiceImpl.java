package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.dao.javaapi.mapper.SweetCityMapper;
import com.autohome.applet.dao.javaapi.mapper.SweetClientRealtionMapper;
import com.autohome.applet.dao.javaapi.mapper.SweetMapper;
import com.autohome.applet.dao.javaapi.model.Sweet;
import com.autohome.applet.dao.javaapi.model.SweetClientRealtion;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.service.javaapi.SweetService;
import com.autohome.applet.util.JacksonHelper;
import com.autonews.comm.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SweetServiceImpl implements SweetService {

    @Qualifier("managerSweetMapper")
    @Autowired
    SweetMapper sweetMapper;

    @Autowired
    SweetClientRealtionMapper realtionMapper;

    @Autowired
    SweetCityMapper sweetCityMapper;

    @Override
    public Sweet createNew(int clientType, int sweetId, String sweetName, String img, String jumpTo, Date startTime,
                           Date endTime, String clientString, String openScreen, Integer status, Integer jumpType,
                           String otherAppid, String reportPoint, String shareDesc, String shareImg,
                           Integer contentType, String showPoint, Integer copySortNum, Integer forceSort,
                           String skinBottomColor, String searchBottomColor, String indexOrSearchOrRecomColor,
                           String sweetBottomLineColor, Integer needLogin, Integer categoryPart, int contry,
                           String choiceprovinceids, String choiceprovinceNames, String choiceCityids,
                           String choiceCityNames) {
        List<String> clientArr = new ArrayList<>();

        if (status == 0) {
            if (!StringUtils.isBlank(clientString)) {
                clientArr = Arrays.asList(clientString.split(","));
                for (String c : clientArr) {
//                    if (!checkSweetAdd(clientType, sweetId, c)) {
//                        return new BaseModel<>(101, "线上最多配置15个");
//                    }
                }
            } else {
//                if (!checkSweetAdd(clientType, sweetId)) {
//                    return new BaseModel<>(101, "线上最多配置15个");
//                }
            }
        }


        Sweet sweet = new Sweet();
//        List<String> contentTypeArr = new ArrayList<>();
        if (!StringUtils.isBlank(clientString)) {
            clientArr = Arrays.asList(clientString.split(","));
        }

        List<String> openArr = new ArrayList<>();
        if (!StringUtils.isBlank(openScreen)) {
            openArr = Arrays.asList(openScreen.split(","));
        }
//        if (sweetId > 0) {
//            sweet = sweetDBMapper.get(sweetId);
//            if (Objects.isNull(sweet)) {
//                return new BaseModel<>(1, "保存失败！没找到id");
//            }
//            realtionDBMapper.trueDelRelationBySid(sweetId);
//            for (String d : clientArr) {
//                SweetClientRealtion realtion = new SweetClientRealtion();
//                realtion.setCid(d);
//                realtion.setSid(sweetId);
//                realtionDBMapper.add(realtion);
//            }
//            secdRelationDBMapper.delRelationBySid(sweetId);
//            for (String d : openArr) {
//                SweetSecdRelation realtion = new SweetSecdRelation();
//                realtion.setRid(d);
//                realtion.setSid(sweetId);
//                secdRelationDBMapper.add(realtion);
//            }
//            if (Objects.nonNull(contentType)) {
//                sweet.setContentType(contentType);
//            } else {
//                sweet.setContentType(0);
//            }
//            sweet.setSweetName(sweetName);
//            sweet.setImg(img);
//            sweet.setJumpTo(jumpTo);
//            sweet.setStartTime(startTime);
//            sweet.setEndTime(endTime);
//            sweet.setStatus(status);
//            sweet.setJumpType(jumpType);
//            sweet.setOtherAppid(otherAppid);
//            sweet.setReportPoint(reportPoint);
//            sweet.setShowPoint(showPoint);
//            sweet.setShareDesc(shareDesc);
//            sweet.setShareImg(shareImg);
//            sweet.setCategoryPart(categoryPart);
//            sweet.setNeedLogin(Objects.nonNull(needLogin) ? needLogin : 0);
//            setSwetExt(sweet, skinBottomColor, searchBottomColor, indexOrSearchOrRecomColor, sweetBottomLineColor);
//            for (String c1 : clientArr) {
//                redisClient.remove(LightDbRedisKeyEnum.appendKey(LightDbRedisKeyEnum.SWEET_LIST_BY_TYPE.val(), sweet.getClientType(), 0, c1));
//                redisClient.remove(LightDbRedisKeyEnum.appendKey(LightDbRedisKeyEnum.SWEET_LIST_BY_TYPE.val(), sweet.getClientType(), 2, c1));
//            }
//            //保存推广城市
//            saveSweetCity(sweetId, contry, choiceprovinceids, choiceprovinceNames, choiceCityids, choiceCityNames, true);
//            return update(sweet);
//        }

        int sortNum = 1;
        Sweet last = sweetMapper.getLastForClientType(clientType);
        if (last != null) {
            sortNum = last.getSortnum() + 1;
        }
//        if (SweetClientTypeEnum.FORUMFEED_BD.val().equals(clientType)
//                || SweetClientTypeEnum.FORUMFEED_WX.val().equals(clientType)
//                || SweetClientTypeEnum.FORUMFEED_QUICK.val().equals(clientType)
//        ) {
//            sortNum = 4;
//        }
//        if (SweetClientTypeEnum.INDEXRECOMFEED_BD.val().equals(clientType)
//                || SweetClientTypeEnum.INDEXRECOMFEED_WX.val().equals(clientType)
//                || SweetClientTypeEnum.INDEXRECOMFEED_QUICK.val().equals(clientType)
//        ) {
//            sortNum = 6;
//        }
        if (Objects.nonNull(contentType)) {
            sweet.setContenttype(contentType);
        } else {
            sweet.setContenttype(0);
        }
        sweet.setSortnum(copySortNum == 0 ? sortNum : copySortNum);
        sweet.setClienttype(clientType);
        sweet.setSweetname(sweetName);
        sweet.setImg(img);
        sweet.setJumpto(jumpTo);
        sweet.setStarttime(startTime);
        sweet.setEndtime(endTime);
        sweet.setStatus(status);
        sweet.setOtherappid(otherAppid);
        sweet.setJumptype(jumpType);
        sweet.setReportpoint(reportPoint);
        sweet.setShowpoint(showPoint);
        sweet.setSharedesc(shareDesc);
        sweet.setShareimg(shareImg);
        sweet.setNeedlogin(Objects.nonNull(needLogin) ? needLogin : 0);
        //categoryPart
        sweet.setCategorypart(categoryPart);
        sweet.setRecycled(false);
        sweet.setExt("");

//        setSwetExt(sweet, skinBottomColor, searchBottomColor, indexOrSearchOrRecomColor, sweetBottomLineColor);
        int newOne = sweetMapper.insert(sweet);
        sweet.setSweetid(newOne);
        System.out.println(newOne);
//        for (String d : clientArr) {
//            SweetClientRealtion realtion = new SweetClientRealtion();
//            realtion.setCid(d);
//            realtion.setSid(newOne);
//            realtionDBMapper.add(realtion);
//        }
//        for (String d : openArr) {
//            SweetSecdRelation realtion = new SweetSecdRelation();
//            realtion.setRid(d);
//            realtion.setSid(newOne);
//            secdRelationDBMapper.add(realtion);
//        }
//        for (String c1 : clientArr) {
//            redisClient.remove(LightDbRedisKeyEnum.appendKey(LightDbRedisKeyEnum.SWEET_LIST_BY_TYPE.val(), sweet.getClientType(), 0, c1));
//            redisClient.remove(LightDbRedisKeyEnum.appendKey(LightDbRedisKeyEnum.SWEET_LIST_BY_TYPE.val(), sweet.getClientType(), 2, c1));
//        }
//        //保存推广城市
//        saveSweetCity(newOne, contry, choiceprovinceids, choiceprovinceNames, choiceCityids, choiceCityNames, false);
        return sweet;
    }

    @Override
    public void setHomeFeed7(String title, String img, String jumpTo, int jumpType, String reportPoint, String showPoint, Date startTime, Date endTime) {
        Sweet sweet = new Sweet();
        List<Sweet> homeFeed7 = sweetMapper.getHomeFeed7();
        log.info("setHomeFeed7 获取所有上线状态的sweet homeFeed7:{}",JacksonHelper.serialize(homeFeed7));
        if (homeFeed7 != null && homeFeed7.size() > 0) {
            /*Sweet updateSweet = homeFeed7.remove(0);
            for (Sweet feed : homeFeed7) {
                sweetMapper.del(feed.getSweetid());
            }
            updateSweet.setSweetname(title);
            updateSweet.setImg(img);
            updateSweet.setJumpto(jumpTo);
            updateSweet.setJumptype(jumpType);
            updateSweet.setReportpoint(reportPoint);
            updateSweet.setShowpoint(showPoint);
            updateSweet.setStarttime(startTime);
            updateSweet.setEndtime(endTime);
            Date now = new Date();
            updateSweet.setCreatedStime(now);
            updateSweet.setModifiedStime(now);
            sweetMapper.resetSweet(updateSweet);*/
            sweetMapper.resetSweet7All();
            log.info("setHomeFeed7 修改所有上线状态的sweet为下线状态 homeFeed7:{}",JacksonHelper.serialize(homeFeed7));
        }

        sweet.setContenttype(0);
        sweet.setSortnum(7);
        sweet.setClienttype(107);
        sweet.setSweetname(title);
        sweet.setImg(img);
        sweet.setJumpto(jumpTo);
        sweet.setStarttime(startTime);
        sweet.setEndtime(endTime);
        sweet.setStatus(0);
        sweet.setOtherappid("");
        sweet.setJumptype(jumpType);
        sweet.setReportpoint(reportPoint);
        sweet.setShowpoint(showPoint);
        sweet.setSharedesc("");
        sweet.setShareimg("");
        sweet.setNeedlogin(0);
        //categoryPart
        sweet.setCategorypart(0);
        sweet.setRecycled(false);
        sweet.setExt("");
        sweet.setForcesort(1);

        log.info("setHomeFeed7 新的sweet:{}",JacksonHelper.serialize(sweet));
//        setSwetExt(sweet, skinBottomColor, searchBottomColor, indexOrSearchOrRecomColor, sweetBottomLineColor);
        int newOne = sweetMapper.insert(sweet);
        log.info("setHomeFeed7 插入新的sweet result:{}",newOne);

        if (newOne > 0){
            sweet.setSweetid(newOne);
            System.out.println(newOne);

            SweetClientRealtion realtion = new SweetClientRealtion();
            realtion.setCid("wxcar");
            realtion.setSid(newOne);
            realtionMapper.insert(realtion);
            sweetCityMapper.insertCountry(newOne);
        }else {
            log.info("setHomeFeed7 新sweet保存失败,恢复原有的homeFeed7:{}",JacksonHelper.serialize(homeFeed7));
            if (homeFeed7 != null && homeFeed7.size() > 0) {
                for (Sweet feed : homeFeed7) {
                    sweetMapper.updateById(feed.getSweetid());
                    log.info("setHomeFeed7 恢复原有sweet成功:{}",JacksonHelper.serialize(feed));
                }
            }
        }

//        }

//        return newOne;
    }

    @Override
    public void setHomeFocus2(String title, String img, String jumpTo, int jumpType, String reportPoint, String showPoint, Date startTime, Date endTime) {
        Sweet sweet = new Sweet();
        List<Sweet> homeFocus = sweetMapper.getHomeFocus2();
        log.info("setHomeFocus2 获取所有的上线状态的sweet setHomeFocus2:{}",JacksonHelper.serialize(homeFocus));
        if (!CollectionUtils.isEmpty(homeFocus)) {
            /*// 取出第一个
            Sweet updateSweet = homeFocus.remove(0);
            // 多余的删除
            for (Sweet feed : homeFocus) {
                sweetMapper.del(feed.getSweetid());
            }
            updateSweet.setSweetname(title);
            updateSweet.setImg(img);
            updateSweet.setJumpto(jumpTo);
            updateSweet.setJumptype(jumpType);
            updateSweet.setReportpoint(reportPoint);
            updateSweet.setShowpoint(showPoint);
            updateSweet.setStarttime(startTime);
            updateSweet.setEndtime(endTime);
            Date now = new Date();
            updateSweet.setCreatedStime(now);
            updateSweet.setModifiedStime(now);
            sweetMapper.resetSweet(updateSweet);*/
            sweetMapper.resetSweet2All();
            log.info("setHomeFocus2 将所有的上线状态的sweet改为下线状态 setHomeFocus2:{}",JacksonHelper.serialize(homeFocus));
        }

        sweet.setContenttype(0);
        sweet.setSortnum(2);
        sweet.setClienttype(11);
        sweet.setSweetname(title);
        sweet.setImg(img);
        sweet.setJumpto(jumpTo);
        sweet.setStarttime(startTime);
        sweet.setEndtime(endTime);
        sweet.setStatus(0);
        sweet.setOtherappid("");
        sweet.setJumptype(jumpType);
        sweet.setReportpoint(reportPoint);
        sweet.setShowpoint(showPoint);
        sweet.setSharedesc("");
        sweet.setShareimg("");
        sweet.setNeedlogin(0);
        //categoryPart
        sweet.setCategorypart(0);
        sweet.setRecycled(false);
        sweet.setExt("");
        sweet.setForcesort(1);

        log.info("setHomeFocus2 新的sweet:{}",JacksonHelper.serialize(sweet));
//        setSwetExt(sweet, skinBottomColor, searchBottomColor, indexOrSearchOrRecomColor, sweetBottomLineColor);
        int newOne = sweetMapper.insert(sweet);

        log.info("setHomeFocus2 新sweet保存成功 result:{}",newOne);

        if (newOne > 0){
            sweet.setSweetid(newOne);
//                System.out.println(newOne);

            SweetClientRealtion realtion = new SweetClientRealtion();
            realtion.setCid("wxcar");
            realtion.setSid(newOne);
            realtionMapper.insert(realtion);
            sweetCityMapper.insertCountry(newOne);
        }else {
            log.info("setHomeFocus2 保存失败,恢复已有sweet状态");
            if (homeFocus != null && homeFocus.size() > 0) {
                for (Sweet feed : homeFocus) {
                    sweetMapper.updateById(feed.getSweetid());
                    log.info("setHomeFocus2 恢复sweet:{}",JacksonHelper.serialize(sweet));
                }
            }
        }
//        }

//        return newOne;
    }

    @Override
    public void setDetailAd(String cid, int detailType, String title, String img, String jumpTo, int jumpType, String reportPoint, String showPoint, Date startTime, Date endTime) {
        Sweet sweet = new Sweet();
        int clientType = 0;
        if ("wxcar".equals(cid)) {
            if (detailType == 1) {
                clientType = 342;
            } else if (detailType == 2) {
                clientType = 346;
            } else if (detailType == 3) {
                clientType = 347;
            } else {
                throw new BusinessException("cid mast be 1、2、3, your's " + detailType, -1);
            }
        } else if ("bdcar".equals(cid)) {
            if (detailType == 1) {
                clientType = 343;
            } else if (detailType == 2) {
                clientType = 348;
            } else if (detailType == 3) {
                clientType = 349;
            } else {
                throw new BusinessException("cid mast be 1、2、3, your's " + detailType, -1);
            }
        } else {
            throw new BusinessException("cid mast be wxcar or bdcar, your's " + cid, -1);
        }
        List<Sweet> detailAds = sweetMapper.getDetailAd(clientType);

        log.info("setDetailAd 获取所有上线状态的 detailAds:{}", JacksonHelper.serialize(detailAds));

        if (!CollectionUtils.isEmpty(detailAds)) {
            // 取出第一个
            Sweet updateSweet = detailAds.remove(0);
            log.info("setDetailAd 获取第一个sweet updateSweet:{}",updateSweet);

            log.info("setDetailAd 要删除的 detailAds:{}",JacksonHelper.serialize(detailAds));
            // 多余的删除
            for (Sweet feed : detailAds) {
                sweetMapper.del(feed.getSweetid());
            }
            updateSweet.setSweetname(title);
            updateSweet.setImg(img);
            updateSweet.setJumpto(jumpTo);
            updateSweet.setJumptype(jumpType);
            updateSweet.setReportpoint(reportPoint);
            updateSweet.setShowpoint(showPoint);
            updateSweet.setStarttime(startTime);
            updateSweet.setEndtime(endTime);
            Date now = new Date();
            updateSweet.setCreatedStime(now);
            updateSweet.setModifiedStime(now);
            sweetMapper.resetSweet(updateSweet);
            log.info("setDetailAd 更新sweet updateSweet:{}",updateSweet);
        } else {
            sweet.setContenttype(0);
            sweet.setSortnum(1);
            sweet.setClienttype(clientType);
            sweet.setSweetname(title);
            sweet.setImg(img);
            sweet.setJumpto(jumpTo);
            sweet.setStarttime(startTime);
            sweet.setEndtime(endTime);
            sweet.setStatus(0);
            sweet.setOtherappid("");
            sweet.setJumptype(jumpType);
            sweet.setReportpoint(reportPoint);
            sweet.setShowpoint(showPoint);
            sweet.setSharedesc("");
            sweet.setShareimg("");
            sweet.setNeedlogin(0);
            //categoryPart
            sweet.setCategorypart(0);
            sweet.setRecycled(false);
            sweet.setExt("");
            sweet.setForcesort(1);

//        setSwetExt(sweet, skinBottomColor, searchBottomColor, indexOrSearchOrRecomColor, sweetBottomLineColor);
            log.info("setDetailAd 数据库插入新的 sweet:{}",JacksonHelper.serialize(sweet));
            int newOne = sweetMapper.insert(sweet);
            log.info("setDetailAd 数据库插入新的 sweet:{},result:{}",JacksonHelper.serialize(sweet),newOne);
            sweet.setSweetid(newOne);
//            System.out.println(newOne);

            SweetClientRealtion realtion = new SweetClientRealtion();
            realtion.setCid(cid);
            realtion.setSid(newOne);
            realtionMapper.insert(realtion);
            sweetCityMapper.insertCountry(newOne);
//        }
        }
    }
}
