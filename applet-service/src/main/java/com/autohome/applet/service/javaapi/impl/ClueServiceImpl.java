package com.autohome.applet.service.javaapi.impl;

import com.alibaba.fastjson.JSON;
import com.autohome.applet.dao.javaapi.mapper.Clue4Che168Mapper;
import com.autohome.applet.dao.javaapi.model.Clue4Che168;
import com.autohome.applet.dao.javaapi.model.Clue4Che168Dto;
import com.autohome.applet.dao.javaapi.model.PushClueResultDto;
import com.autohome.applet.service.javaapi.ClueService;
import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.comm.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ClueServiceImpl implements ClueService {

    @Value("${clue.pushclues:http://handler.yz.test.autohome.com.cn/clues-handler/api/pushclues}")
    String push_clue_url;

    @Autowired
    private Clue4Che168Mapper clue4Che168Mapper;


    @Override
    public BaseModel<String> clue4che168_buy(Clue4Che168Dto clue) {
        Clue4Che168 model = new Clue4Che168();
        model.setIsDel(false);
        model.setKeyClientOrderIp(clue.getKeyClientOrderIp());
        model.setKeyInsideLinkidId(clue.getKeyInsideLinkidId());
        model.setKeyName(clue.getKeyName());
        model.setKeyOutsidePvareaidId("");
        model.setKeyPhone(clue.getKeyPhone());
        model.setKeyUsedCarBusinessId(clue.getKeyUsedCarBusinessId());
        model.setKeyUserId(clue.getKeyUserId());
        model.setPushData("");
        model.setPushId("");
        model.setPushResult("");
        model.setPushStatus(0);
        model.setRuleengineResult("");
        model.setRuleengineStatus(0);
        model.setPushAppKey(clue.getPushAppKey());
        model.setKeyUsedCarBusinessId(clue.getKeyUsedCarBusinessId());
        model.setKeyUsedCarSourceId(clue.getKeyUsedCarSourceId());
        model.setKeySupplyBusinessId(clue.getKeySupplyBusinessId());
        model.setKeyOrderCityid(clue.getKeyOrderCityId());
        JSONObject k7 = new JSONObject(clue.getKeyExt7());
        model.setSourceId(k7.getInt("sourceid"));

        clue4Che168Mapper.add(model);
        log.debug("ClueServiceImpl.clue4che168_buy 新增数据id ：{}", JSON.toJSONString(model));

//        RuleengineResultDto ruleengineResult = invokeRuleengine(k7.getString("deviceid"),clue.getKeyUserId(),clue.getKeyPhone(),clue.getKeyClientOrderIp(),clue.getKeyName(),clue.getKeyCardCityId(),clue.getKeyCarAudiId(),clue.getKeyCarTypeId());
//        model.setRuleengineStatus(ruleengineResult.getRuleResultCode().equals("100")?1:2);
//        model.setRuleengineResult(ruleengineResult.getReturnData());
//        clue4Che168Mapper.update(model);
//        if(ruleengineResult.getRuleResultCode().equals("100")){
        String pushData = JsonUtils.toString(clue);
        PushClueResultDto pushResult = pushclues(pushData);
        model.setPushStatus(pushResult.getStatus() == 0 ? 1 : 2);
        model.setPushId(pushResult.getStatus() == 0 ? Long.toString(pushResult.getResult().getPushId()) : "");
        model.setPushResult(JsonUtils.toString(pushResult));
        model.setPushData("");
        model.setKeyName("");
        model.setKeyPhone("");
        model.setKeyUsedCarSourceId(0);
        model.setKeyUsedCarBusinessId("");
        clue4Che168Mapper.update(model);
        log.debug("ClueServiceImpl.clue4che168_buy 修改数据 ：{}", JSON.toJSONString(model));
//        }
        return new BaseModel<>(0, "success");
    }


    public PushClueResultDto pushclues(String body) {
        try {
            return HttpClientUtils.post(push_clue_url, body, "application/json", "UTF-8", 5000, 5000, PushClueResultDto.class);
        } catch (Exception ex) {
            PushClueResultDto result = new PushClueResultDto();
            result.setStatus(-1);
            result.setMsg("推送失败了" + ex.getMessage() + ex.toString());
            result.setResult(null);
            return result;
        }
    }
}
