package com.autohome.applet.service.openapi;

import com.autohome.applet.model.dto.honor.AnalyzeCar;
import com.autohome.applet.model.dto.honor.AnalyzeCarResult;
import com.autohome.applet.model.dto.honor.AnalyzeSeriesCardInfo;

import java.time.LocalDateTime;

public interface HonorService {

    void syncArticle(LocalDateTime breakData);

    AnalyzeCarResult.Car analyzeCar(AnalyzeCar analyzeCar);

    AnalyzeSeriesCardInfo getSeriesCardInfo(AnalyzeCarResult.Car car);

    String honorSign(String secret, String content);

    String honorSign2(String secret, String content);
}
