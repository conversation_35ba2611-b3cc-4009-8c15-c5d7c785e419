package com.autohome.applet.service.donate;

import com.autohome.applet.dao.javaapi.model.DonateLog;
import com.autohome.applet.model.dto.donate.DonateHuaweiModel;
import com.autohome.applet.model.dto.donate.DonateQueryModel;
import com.autohome.applet.model.enums.DonateDataSourceEnum;
import com.autohome.applet.model.enums.DonateTerminalEnum;
import com.autohome.applet.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static java.lang.Math.abs;

@Service
@Slf4j
public class HuaweiDonateClass extends DonateBaseClass {

    private final static Integer TOTALDONATE = 2000;
    //设置分页数据量
    private final static int PAGESIZE = 2000;

    /**
     * 提前将原创和车家号数据存储到物理表中
     * */
    @Override
    public void doBusiness(DonateQueryModel donateQuery) {
        donateQuery.setPageSize(100);
        donateQuery.setDonateTerminalEnum(DonateTerminalEnum.HUAWEI);
        donateQuery.setTotal(TOTALDONATE);
        donateQuery.setDonateDataSourceEnum(DonateDataSourceEnum.OPGC);
        //先删除当天的捐赠数据, 然后重新查询保存(方便反复执行)
        deleteDonate(donateQuery.getDaily(), donateQuery.getDonateTerminalEnum());
        List<DonateLog> donateLogList = new ArrayList<>();
        //保存原创视频
        fillDonateLogFromCMS(donateQuery, donateLogList);
        //保存原创文章
        fillDonateLogFromVideo(donateQuery, donateLogList);
        //保存车家号
        fillDonateLogFromChejiahao(donateQuery, donateLogList);
        //保存到数据库
        saveDonateLog(donateLogList);
        System.out.println("HuaweiDonateClass doBusiness log");
    }

    public int deleteData30(String endDate){
       return  deleteData30(DonateTerminalEnum.HUAWEI.getCode(), endDate);
    }

    public DonateHuaweiModel getDonateData(String deviceId) {
        DonateHuaweiModel donateHuaweiModel = new DonateHuaweiModel();
        DonateQueryModel donateQuery = new DonateQueryModel();
        donateQuery.setDeviceId(deviceId);
        donateQuery.setDonateTerminalEnum(DonateTerminalEnum.HUAWEI);
        donateQuery.setTotal(TOTALDONATE);
        int deviceHash = abs(donateQuery.getDeviceId().hashCode());
        donateQuery.setDeviceHash(deviceHash);
        String totalTmp = redisClient.get(getDonateRedisKey(deviceHash, 2));
        int total = StringUtils.isEmpty(totalTmp) ? 0 : Integer.valueOf(totalTmp);
        //判断是否到上限
        if(total >= TOTALDONATE){
            return DonateHuaweiModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
        }
        //设置剩余的需要请求的条数
        donateQuery.setPageSize(PAGESIZE - total);
        //4个数据源,调用4次
        for(int i = 0; i< DonateDataSourceEnum.count() - 1; i++) {
            //填充捐赠数据
            List<DonateLog> donateLogList = fillDonateData(donateQuery);
            if(!CollectionUtils.isEmpty(donateLogList)){
                donateHuaweiModel.setHasMore(1);//还有数据
                donateHuaweiModel.setList(convertDonateHuaweiItem(donateLogList));
                return donateHuaweiModel;
            }
            if(donateQuery.getDonateDataSourceEnum() == DonateDataSourceEnum.OVER){
                break;
            }
        }
        return DonateHuaweiModel.builder().hasMore(0).list(Collections.EMPTY_LIST).build();
    }

    private List<DonateHuaweiModel.DonateHuaweiItem> convertDonateHuaweiItem(List<DonateLog> donateLogList){
        if(!CollectionUtils.isEmpty(donateLogList)){
            return donateLogList.stream().map(o ->{
                DonateHuaweiModel.DonateHuaweiItem donateHuaweiItem = new DonateHuaweiModel.DonateHuaweiItem();
                donateHuaweiItem.setContentType("application");
                donateHuaweiItem.setDisplayname(o.getTitle());
                donateHuaweiItem.setDescription(o.getDescribe());
                donateHuaweiItem.setMetadataModificationDate(DateHelper.serialize(o.getMetadataModificationDate(), DateHelper.DATEFORMAT_STANDARD));
                donateHuaweiItem.setKeyWords(StringUtils.isEmpty(o.getKeyWords()) ? Collections.EMPTY_LIST : Arrays.asList(o.getKeyWords().split(",")));
                donateHuaweiItem.setLogoURL(o.getLogoUrl());
                donateHuaweiItem.setRankingHint(o.getRankingHint());
                donateHuaweiItem.setUniqueldentifier(o.getUniqueldentifier());
//                donateHuaweiItem.setDataURI(o.getDataUrl());
                donateHuaweiItem.setDataURI(o.getDataUrl() + "&sourceid=donation&dataSource="+ convertDataSource(o.getDataSource()) +"&viewMark=" + o.getViewMark());
//                donateHuaweiItem.setDataSource(convertDataSource(o.getDataSource()));
//                donateHuaweiItem.setViewMark(o.getViewMark());
                return donateHuaweiItem;
            }).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }


}
