package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.mapper.autoopen.OpenModuleRepository;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.OpenModule;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer.*;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.DealerService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarSettings;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DealerServiceImpl implements DealerService {

    @Autowired
    private OpenModuleRepository openModuleRepository;


    /**
     * 经销商完整信息+营业执照+轮播图
     *
     * @param dealerid
     * @return
     */
    @Override
    public ResponseContent<?> GetCompleteDealerInfo(String dealerid, String classIds, String platform, int isNeedVirtual) {

        try {
            CompletableFuture<ResponseContent<CompleteDealerInfo>> completableFuture = CompletableFuture.supplyAsync(new Supplier<ResponseContent<CompleteDealerInfo>>() {
                @Override
                public ResponseContent<CompleteDealerInfo> get() {
                    HashMap<String, Object> param = new HashMap<>();
                    param.put("DealerId", dealerid);
                    param.put("_encoding", "utf8");
                    param.put("_appid", "car");
                    param.put("isNeedVirtual", String.valueOf(isNeedVirtual));
//
//                    String result = carApi.get("dealer.api.GetCompleteDealerInfo", params);//HttpHelper.getResponse("https://dealer.api.autohome.com.cn/dealerrest/Profile/GetCompleteDealerInfo", params,ENCODING_GB2312, 3000).getContent();
//                    if (StringUtil.isBlank(result)) {
//                        return null;
//                    }
//                    ResponseContent<CompleteDealerInfo> respCnt = JSON.parseObject(result, new TypeReference<ResponseContent<CompleteDealerInfo>>() {
//                    });
//                    if (respCnt == null || respCnt.getResult() == null || respCnt.getResult().getList() == null || respCnt.getResult().getList().size() == 0) {
//                        return null;
//                    }
//                    return respCnt;

//                    https://dealer.api.autohome.com.cn/dealerrest/Profile/GetCompleteDealerInfo?DealerId=133&_appId=dealer&_encoding=utf8
//                    http://wiki.corpautohome.com/pages/viewpage.action?pageId=18514298
                    HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/Profile/GetCompleteDealerInfo", param);
                    if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                        //if (respCnt == null || respCnt.getResult() == null) {
                        return null;
                    } else {
                        ResponseContent<CompleteDealerInfo> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<CompleteDealerInfo>>() {
                        });
                        if (responseContent == null || responseContent.getResult() == null || responseContent.getResult().getList() == null || responseContent.getResult().getList().size() == 0) {
                            return null;
                        }
                        return responseContent;
                    }
                }
            });

            //营业执照
            CompletableFuture<DealerLicense> dealerLicenseCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerLicense>() {
                @Override
                public DealerLicense get() {
                    return GetDealerLicense(dealerid);
                }
            });

            //M版商家店铺，公司介绍页轮播图
            CompletableFuture<DealerImages> dealerImagesCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerImages>() {
                @Override
                public DealerImages get() {
                    return GetDealerImagesByPartOfClasses(dealerid, classIds);
                }
            });

            //获取经销商是否有VR权限
            CompletableFuture<DealerBusiness> dealerBusinessCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerBusiness>() {
                @Override
                public DealerBusiness get() {
                    return getDealerBusiness(dealerid, platform);
                }
            });

            CompletableFuture.allOf(completableFuture, dealerLicenseCompletableFuture, dealerImagesCompletableFuture, dealerBusinessCompletableFuture).join();

            ResponseContent<CompleteDealerInfo> respCntNew = null;
            try {
                respCntNew = completableFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
            if (respCntNew == null || respCntNew.getResult() == null) {
                respCntNew = new ResponseContent<CompleteDealerInfo>();
            }
            DealerBusiness dealerBusiness = null;
            try {
                dealerBusiness = dealerBusinessCompletableFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
            if (dealerBusiness != null) {
                respCntNew.getResult().setDealerBusiness(dealerBusiness);
            }
            DealerLicense dealerLicense = null;
            try {
                dealerLicense = dealerLicenseCompletableFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
            if (dealerLicense != null) {
                String licenseurl = dealerLicense.getList().get(0).getLicenseurl();
                if (licenseurl == null) {
                    respCntNew.getResult().getList().get(0).setLicenseUrl("");
                    respCntNew.setMessage("licenseurl is null");
                } else {
                    licenseurl = licenseurl.indexOf("https://") >= 0 ? licenseurl : "https://" + licenseurl;
                    respCntNew.getResult().getList().get(0).setLicenseUrl(licenseurl);
                }
            }
            DealerImages dealerImages = dealerImagesCompletableFuture.get();
            if (dealerImages != null) {

                respCntNew.getResult().setDealerImages(dealerImages);
            } else {
                respCntNew.getResult().setDealerImages(new DealerImages());
            }
            return respCntNew;
        } catch (Exception e) {
            log.error("GetCompleteDealerInfo-withexception " + e.getMessage(), e);
//            LogHelper.logError("获取经销商信息异常", e, e.getMessage());
            return new ResponseContent<>(500, "获取经销商完整信息异常");
        }
    }

    /**
     * M版商家店铺，公司介绍页轮播图
     *
     * @param dealerid
     * @return
     */
    public DealerLicense GetDealerLicense(String dealerid) {

        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("dealerID", dealerid);
            param.put("_encoding", "utf8");
            param.put("_appid", "m");
            //ResponseContent<DealerLicense> respCnt = carApi.get("dealer.api.GetDealerLicense", paramsl, DealerLicense.class);

//          http://dealer.api.autohome.com.cn/dealerrest/dealer/GetDealerLicense?_appId=m&dealerID=1
//          http://wiki.corpautohome.com/pages/viewpage.action?pageId=75541865
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/dealer/GetDealerLicense", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                //if (respCnt == null || respCnt.getResult() == null) {
                return null;
            } else {
                ResponseContent<DealerLicense> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerLicense>>() {
                });

                return responseContent.getResult();
            }

        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("GetDealerImagesByPartOfClasses-withexception", e.getMessage())));
//            LogHelper.logError("获取经销商信息异常", e, e.getMessage());
            log.error("GetDealerLicense error", e);
            return null;
        }
    }

    /**
     * M版商家店铺，公司介绍页轮播图
     *
     * @param dealerid
     * @param classIds
     * @return
     */
    public DealerImages GetDealerImagesByPartOfClasses(String dealerid, String classIds) {

        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("dealerId", dealerid);
            param.put("classIds", classIds);//14,7,8,9,10
            param.put("_encoding", "utf8");
            param.put("_appid", "m");
//            ResponseContent<DealerImages> respCnt = carApi.get("dealer.api.GetDealerImagesByPartOfClasses", dealerimg, DealerImages.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();

//          http://dealer.api.autohome.com.cn/dealers/profile/GetDealerImagesByPartOfClasses?DealerId=121&classIds=14,7,8,9,10&_appId=m
//          http://wiki.corpautohome.com/pages/viewpage.action?pageId=71641919
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealers/profile/GetDealerImagesByPartOfClasses", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                //if (respCnt == null || respCnt.getResult() == null) {
                return null;
            } else {
                ResponseContent<DealerImages> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerImages>>() {
                });
                return responseContent.getResult();
            }

        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("GetDealerImagesByPartOfClasses-withexception", e.getMessage())));
//            LogHelper.logError("获取经销商信息异常", e, e.getMessage());
            log.error("GetDealerImagesByPartOfClasses error", e);
            return null;
        }
    }

    /**
     * 获取经销商是否有VR权限
     *
     * @param dealerids
     * @param platform
     * @return
     */
    public DealerBusiness getDealerBusiness(String dealerids, String platform) {

        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("dealerIds", dealerids);
            param.put("platform", platform);
            param.put("_encoding", "utf8");
            param.put("_appid", "m");
//            ResponseContent<DealerBusiness> respCnt = carApi.get("dealer.api.getDealerBusiness", params2, DealerBusiness.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/vr/getDealerBusiness", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                //if (respCnt == null || respCnt.getResult() == null) {
                return null;
            } else {
                ResponseContent<DealerBusiness> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerBusiness>>() {
                });
                return responseContent.getResult();
            }
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getDealerBusiness-withexception", e.getMessage())));
//            LogHelper.logError("获取经销商信息异常", e, e.getMessage());
            log.error("getDealerBusiness error", e);
            return null;
        }
    }

    /**
     * 获取经销商首页部分模块
     *
     * @param dealerid
     * @return
     */
    @Override
    public ResponseContent<?> getSubOpenModuleByDealerId(int dealerid, int cityid, int pageSize, int orderType, int bdstatus) {
        try {
//            long startTime = System.currentTimeMillis();
            List<OpenModule> openModulelist;
            List<OpenModule> openModuleSublist;
            if (bdstatus == 1) {
                openModulelist = openModuleRepository.getOpenMouduleByDealerIdAndBdStatus(dealerid, 2);
            } else {
                openModulelist = openModuleRepository.getOpenMouduleByDealerId(dealerid, 2);
            }

            if (openModulelist == null || openModulelist.size() == 0) {
                openModulelist = getDefaultOpenModule();
            }
            openModuleSublist = openModulelist.stream().filter(n -> (n.getModuleName().equals("主推车型") || n.getModuleName().equals("限时促销"))).collect(Collectors.toList());
            CompletableFuture<HotSpecList> hotSpecListCompletableFuture = CompletableFuture.supplyAsync(new Supplier<HotSpecList>() {
                @Override
                public HotSpecList get() {
                    return GetHotSpecListByDealer(Integer.toString(dealerid), pageSize);
                }
            });

            CompletableFuture<DealerPromotionList> dealerPromotionListCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerPromotionList>() {
                @Override
                public DealerPromotionList get() {
                    return GetPromotionListByDealer(Integer.toString(dealerid), 5, orderType);
                }
            });
            for (OpenModule s : openModuleSublist) {
                if (s.getModuleName().equals("主推车型")) {
                    HotSpecList hotSpecList = null;
                    try {
                        hotSpecList = hotSpecListCompletableFuture.get();
                    } catch (InterruptedException | ExecutionException e) {
                        e.printStackTrace();
                    }
                    if (hotSpecList != null) {
                        s.setModuleData(hotSpecList.getList());
                        if (hotSpecList.getRowcount() > 0) {
                            s.setMore("更多");
                        }
                    }
                } else if (s.getModuleName().equals("限时促销")) {
                    DealerPromotionList dealerPromotionList = null;
                    try {
                        dealerPromotionList = dealerPromotionListCompletableFuture.get();
                    } catch (InterruptedException | ExecutionException e) {
                        e.printStackTrace();
                    }
                    if (dealerPromotionList != null) {
                        s.setModuleData(dealerPromotionList.getList());
                        if (dealerPromotionList.getRowcount() > 3) {
                            s.setMore("更多");
                        }
                    }
                }
            }
//            long endTime = System.currentTimeMillis();
//            long costTime = endTime - startTime;
//            if (costTime >= 100) {
//                System.out.println("getSubOpenModuleByDealerId" + costTime / 100 + "倍于100ms-" + "实际响应时间" + Long.toString(costTime));
//                LogHelper.logTrace("getSubOpenModuleByDealerId" + costTime / 100 + "倍于100ms-", "实际响应时间" + Long.toString(costTime));
//            }
            return new ResponseContent<>(openModuleSublist);
        } catch (Exception e) {
//            LogHelper.logError("getSubOpenModuleByDealerId-withexception", e, e.getMessage());
            log.error("getSubOpenModuleByDealerId error", e);
            return new ResponseContent(500, "getSubOpenModuleByDealerId-withexception");
        }
    }

    private List<OpenModule> getDefaultOpenModule() {
        List<OpenModule> openModulelist = new ArrayList<>();
        openModulelist.add(new OpenModule(1, "主推车型", 1, 1));
        openModulelist.add(new OpenModule(2, "销售顾问", 1, 2));
        openModulelist.add(new OpenModule(3, "热门资讯", 1, 3));
        openModulelist.add(new OpenModule(4, "限时促销", 1, 4));
        openModulelist.add(new OpenModule(5, "小视频", 1, 5));
        return openModulelist;
    }


    //    主推车型
    public HotSpecList GetHotSpecListByDealer(String dealerid, int pagesize) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("PageSize", Integer.toString(pagesize));
            paramsl.put("DealerId", dealerid);
            paramsl.put("isWhiteImage", "1");
            paramsl.put("_encoding", "utf8");
            paramsl.put("_appid", "m");
//            ResponseContent<HotSpecList> respCnt = carApi.get("dealer.api.GetHotSpecListByDealer", paramsl, HotSpecList.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/profile/GetHotSpecListByDealer", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                //if (respCnt == null || respCnt.getResult() == null) {
                return null;
            } else {
                ResponseContent<HotSpecList> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<HotSpecList>>() {
                });
                return responseContent.getResult();
            }

        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("GetHotSpecListByDealer-withexception", e.getMessage())));
//            LogHelper.logError("获取经销商信息异常", e, e.getMessage());
            log.error("GetHotSpecListByDealer error", e);
            return null;
        }
    }

    //限时促销
    public DealerPromotionList GetPromotionListByDealer(String dealerid, int pageSize, int orderType) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("dealerid", dealerid);
            paramsl.put("pageSize", Integer.toString(pageSize));
            paramsl.put("pageIndex", "1");
            paramsl.put("isWhiteImage", "true");
            paramsl.put("orderType", Integer.toString(orderType));
            paramsl.put("_encoding", "utf8");
            paramsl.put("noNeedImageList", "");
            paramsl.put("_appid", "m");
//            ResponseContent<DealerPromotionList> respCnt = carApi.get("dealer.api.GetPromotionListByDealer", paramsl, DealerPromotionList.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/news/GetPromotionListByDealer", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                //if (respCnt == null || respCnt.getResult() == null) {
                return null;
            } else {
                ResponseContent<DealerPromotionList> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerPromotionList>>() {
                });
                return responseContent.getResult();
            }
        } catch (Exception e) {
//            LogHelper.logError("GetPromotionListByDealer-withexception", e, e.getMessage());
            log.error("GetPromotionListByDealer error", e);
            return null;
        }
    }

    //金牌销售
    @Override
    public DealerSales getUserSales(String dealerid, int cityId, int pageIndex, int pageSize) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("pageIndex", Integer.toString(pageIndex));
            paramsl.put("pageSize", Integer.toString(pageSize));
            paramsl.put("DealerId", dealerid);
            paramsl.put("sortType", "1");
            paramsl.put("sourceName", "wechat-weapp-usersales");
            paramsl.put("_encoding", "utf8");
            paramsl.put("cityId", Integer.toString(cityId));
            paramsl.put("_appid", "m");
//            ResponseContent<DealerSales> respCnt = carApi.get("baojia.api.getUserSales", paramsl, DealerSales.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://api.baojia.lq.autohome.com.cn/user/getUserSales", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<DealerSales> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerSales>>() {
            });
            return responseContent.getResult();

        } catch (Exception e) {
//            LogHelper.logError("getUserSales-withexception", e, e.getMessage());
            log.error("getUserSales error", e);
            return null;
        }
    }

    /**
     * 根据经销商id获取最热车型或随机车型
     *
     * @param dealerid
     * @return
     */
    @Override
    public HotSpecList GetHotSpecListByDealerId(String dealerid) {
        CompletableFuture<HotSpecList> hotSpecListCompletableFuture = CompletableFuture.supplyAsync(new Supplier<HotSpecList>() {
            @Override
            public HotSpecList get() {
                return GetHotSpecListByDealer(dealerid, 1);
            }
        });
        HotSpecList hotSpecList = null;
        try {
            hotSpecList = hotSpecListCompletableFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        if (hotSpecList != null && hotSpecList.getList() != null && hotSpecList.getList().size() > 0) {
            return hotSpecList;
        }
        CompletableFuture<DealerSeries> dealerSeriesCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerSeries>() {
            @Override
            public DealerSeries get() {
                return GetFacSeriesInfoByDealerId(dealerid);
            }
        });
        DealerSeries dealerSeries = null;
        try {
            dealerSeries = dealerSeriesCompletableFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        if (dealerSeries == null || dealerSeries.getList() == null || dealerSeries.getList().size() == 0) {
            return null;
        }
        String seriesid = "";
        a:
        for (DealerSeries.FctList fct : dealerSeries.getList()) {
            if (fct == null || fct.getSerieslist() == null || fct.getSerieslist().size() == 0) {
                continue;
            }
            for (DealerSeries.Serieslist series : fct.getSerieslist()) {
                if (series != null) {
                    seriesid = Integer.toString(series.getSeriesid());
                    break a;
                }
            }
        }
        DealerSpec dealerSpec = GetDealerSpecListBySeries(dealerid, seriesid);
        if (dealerSpec == null || dealerSpec.getList() == null || dealerSpec.getList().size() == 0) {
            return null;
        }
        HotSpecList newHotSpec = new HotSpecList();
        b:
        for (DealerSpec.SpecGroupList group : dealerSpec.getList()) {
            if (group == null || group.getSpeclist() == null || group.getSpeclist().size() == 0) {
                continue;
            }
            for (DealerSpec.Speclist spec : group.getSpeclist()) {
                if (spec == null) {
                    continue;
                }
                List<HotSpecList.SpecList> specLists = new ArrayList<>();
                HotSpecList.SpecList newspec = new HotSpecList.SpecList();
                newspec.setSpecid(spec.getSpecid());
                newspec.setSeriesid(Integer.parseInt(seriesid));
                String shareurl = spec.getImageurl();
                if (StringUtil.isNotNullAndWhiteSpace(shareurl)) {
                    shareurl = CarSettings.ConvertImgNew(shareurl, "400x320_").replace("//", "https://");
                }
                newspec.setShareimgurl(shareurl);
                specLists.add(newspec);
                newHotSpec.setList(specLists);
                break b;
            }

        }
        return newHotSpec;
    }

    //    经销商车系列表
    public DealerSeries GetFacSeriesInfoByDealerId(String dealerid) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("DealerId", dealerid);
            paramsl.put("isWhiteImage", "1");
            paramsl.put("_encoding", "utf8");
            paramsl.put("_appid", "m");
//            ResponseContent<DealerSeries> respCnt = carApi.get("dealer.api.GetFacSeriesInfoByDealerId", paramsl, DealerSeries.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/List/GetFacSeriesInfoByDealerId", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            } else {
                ResponseContent<DealerSeries> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerSeries>>() {
                });
                return responseContent.getResult();
            }
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("GetHotSpecListByDealer-withexception", e.getMessage())));
//            LogHelper.logError("获取经销商信息异常", e, e.getMessage());
            log.error("GetFacSeriesInfoByDealerId error", e);
            return null;
        }
    }

    //    获取指定车系车型信息
    public DealerSpec GetDealerSpecListBySeries(String dealerid, String seriesId) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("dealerId", dealerid);
            paramsl.put("seriesId", seriesId);
            paramsl.put("_encoding", "utf8");
            paramsl.put("_appid", "m");
//            ResponseContent<DealerSpec> respCnt = carApi.get("dealer.api.GetDealerSpecListBySeries", paramsl, DealerSpec.class);
//            if (respCnt == null || respCnt.getResult() == null) {
//                return null;
//            }
//            return respCnt.getResult();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealerrest/dealer/GetDealerSpecListBySeries", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            } else {
                ResponseContent<DealerSpec> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerSpec>>() {
                });
                return responseContent.getResult();
            }
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("GetDealerSpecListBySeries-withexception", e.getMessage())));
//            LogHelper.logError("GetDealerSpecListBySeriesWithException", e, e.getMessage());
            log.error("GetDealerSpecListBySeries error", e);
            return null;
        }
    }

    @Override
    public ResponseContent<?> getDealers(String seriesId, String cityid, String _appid, String orderType, String pageIndex, String pageSize, String myLat, String myLon) {
        try {
            Map<String, Object> param = new HashMap<>();

            param.put("seriesId", seriesId);
            param.put("cityid", cityid);
            param.put("orderType", orderType);
            param.put("_appid", _appid);
            param.put("pageIndex", pageIndex);
            param.put("pageSize", pageSize);
            param.put("myLat", myLat);
            param.put("myLon", myLon);

//            String result = HttpHelper.getResponse(DealerListURL, param, "GBK", 3000).getContent();
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(0, "暂无数据");
//            }
//            TypeUtils.compatibleWithJavaBean = true;
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealers/DealerList/GetDealers", param, null, "GBK");
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(101, "暂无数据");
            }
//            ResponseContent<DealerResult> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<DealerResult>>() {
            ResponseContent<DealerResult> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerResult>>() {
            });
            if (responseContent == null || responseContent.getResult() == null ||
                    responseContent.getResult().getRowcount() == 0 || responseContent.getResult().getList().size() == 0) {
                return new ResponseContent<>(101, "暂无数据");
            }
            responseContent.getResult().setLatitude(myLat);
            responseContent.getResult().setLongitude(myLon);
//            if (orderType.equals("2")) {
//                responseContent.getResult().getList().sort(new Comparator<Dealer>() {
//                    @Override
//                    public int compare(Dealer o1, Dealer o2) {
//                        if (Double.parseDouble(o1.getDistanceitem()) > Double.parseDouble(o2.getDistanceitem())) {
//                            return 1;
//                        } else {
//                            return -1;
//                        }
//                    }
//                });
//            }
            return responseContent;
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("getDealers-WithException", e.getMessage())));
            log.error("getDealers error ", e);
            return new ResponseContent<>(500, "getDealers-WithException" + e.getMessage());
        }
    }

    /**
     * 获取经销商首页
     *
     * @param dealerid
     * @return
     */
    @Override
    public ResponseContent<?> getOpenModuleByDealerId(int dealerid, int cityid, int pageSize, int orderType, int bdstatus) {
        try {

            CompletableFuture<List<OpenModule>> openFuture = CompletableFuture.supplyAsync(new Supplier<List<OpenModule>>() {
                @Override
                public List<OpenModule> get() {
                    if (bdstatus == 1) {
                        return openModuleRepository.getOpenMouduleByDealerIdAndBdStatus(dealerid, 2);
                    } else {
                        return openModuleRepository.getOpenMouduleByDealerId(dealerid, 2);
                    }
                }
            });
            //主推车型
            CompletableFuture<HotSpecList> hotSpecListCompletableFuture = CompletableFuture.supplyAsync(new Supplier<HotSpecList>() {
                @Override
                public HotSpecList get() {
                    return GetHotSpecListByDealer(Integer.toString(dealerid), pageSize);
                }
            });
            //销售顾问
            CompletableFuture<DealerSales> dealerSalesCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerSales>() {
                @Override
                public DealerSales get() {
                    return getUserSales(Integer.toString(dealerid), cityid, 1, 3);
                }
            });
            //热门资讯
            CompletableFuture<UnTemlateNews> unTemlateNewsCompletableFuture = CompletableFuture.supplyAsync(new Supplier<UnTemlateNews>() {
                @Override
                public UnTemlateNews get() {
                    return getUnTemplateNews(Integer.toString(dealerid), 1, 3, 7);
                }
            });
            //限时促销
            CompletableFuture<DealerPromotionList> dealerPromotionListCompletableFuture = CompletableFuture.supplyAsync(new Supplier<DealerPromotionList>() {
                @Override
                public DealerPromotionList get() {
                    return GetPromotionListByDealer(Integer.toString(dealerid), 4, orderType);
                }
            });
            //小视频
            CompletableFuture<List<DealerVideo>> dealervideoFuture = CompletableFuture.supplyAsync(new Supplier<List<DealerVideo>>() {
                @Override
                public List<DealerVideo> get() {
                    return GetDealerVideos(Integer.toString(dealerid));
                }
            });

            CompletableFuture.allOf(openFuture, hotSpecListCompletableFuture, dealerSalesCompletableFuture, unTemlateNewsCompletableFuture, dealerPromotionListCompletableFuture, dealervideoFuture).join();

            List<OpenModule> openModulelist = new ArrayList<>();

            try {
                openModulelist = openFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }

            if (openModulelist == null || openModulelist.size() == 0) {
                openModulelist = getDefaultOpenModule();
            }

            openModulelist.forEach(s -> {
                switch (s.getModuleName()) {
                    case "主推车型":
                        HotSpecList hotSpecList = null;
                        try {
                            hotSpecList = hotSpecListCompletableFuture.get();
                        } catch (InterruptedException | ExecutionException e) {
                            e.printStackTrace();
                        }
                        if (hotSpecList != null) {
                            s.setModuleData(hotSpecList.getList());
                            if (hotSpecList.getRowcount() > 0) {
                                s.setMore("更多");
                            }
                        }
                        break;
                    case "销售顾问":
                        DealerSales dealerSales = null;
                        try {
                            dealerSales = dealerSalesCompletableFuture.get();
                        } catch (InterruptedException | ExecutionException e) {
                            e.printStackTrace();
                        }
                        if (dealerSales != null && dealerSales.getList() != null && dealerSales.getList().size() > 0) {

//                        Iterator<DealerSales.SaleList> it = dealerSales.getList().iterator();
//                        while (it.hasNext()){
//                            DealerSales.SaleList sale=it.next();
//                            if(sale.getSaleslevel()!=1){
//                                it.remove();
//                            }
//                        }
                            if (dealerSales.getList().size() > 3) {
                                dealerSales.setList(dealerSales.getList().subList(0, 3));
                                s.setMore("更多");
                            }
                            s.setModuleData(dealerSales.getList());
                        }
                        break;
                    case "热门资讯":
                        UnTemlateNews unTemlateNews = null;
                        try {
                            unTemlateNews = unTemlateNewsCompletableFuture.get();
                        } catch (InterruptedException | ExecutionException e) {
                            e.printStackTrace();
                        }
                        if (unTemlateNews != null) {
                            s.setModuleData(unTemlateNews.getList());
                            if (unTemlateNews.getPagecount() > 3)
                                s.setMore("更多");
                        }
                        break;
                    case "限时促销":
                        DealerPromotionList dealerPromotionList = null;
                        try {
                            dealerPromotionList = dealerPromotionListCompletableFuture.get();
                        } catch (InterruptedException | ExecutionException e) {
                            e.printStackTrace();
                        }
                        if (dealerPromotionList != null) {
                            s.setModuleData(dealerPromotionList.getList());
                            if (dealerPromotionList.getRowcount() > 3) {
                                s.setMore("更多");
                            }
                        }
                        break;
                    case "小视频":

                        //小视频
                        List<DealerVideo> dealerVideos = null;
                        try {
                            dealerVideos = dealervideoFuture.get();
                        } catch (InterruptedException | ExecutionException e) {
                            e.printStackTrace();
                        }
                        if (dealerVideos != null) {
                            List<DealerVideo> dealerVideosNew = new ArrayList<DealerVideo>();
                            for (DealerVideo video : dealerVideos) {
                                if (dealerVideosNew.size() < 2) {
                                    dealerVideosNew.add(video);
                                }
                            }
                            s.setModuleData(dealerVideosNew);
                            if (dealerVideos.size() > 2) {
                                s.setMore("更多");
                            }
                        }
                        break;
                }
            });
//            long endTime = System.currentTimeMillis();
//            long costTime = endTime - startTime;
//            if (costTime >= 100) {
//                System.out.println("getOpenModuleByDealerId" + costTime / 100 + "倍于100ms-" + "实际响应时间" + Long.toString(costTime));
//                LogHelper.logTrace("getOpenModuleByDealerId" + costTime / 100 + "倍于100ms-", "实际响应时间" + Long.toString(costTime));
//            }
            return new ResponseContent<>(openModulelist);
        } catch (Exception e) {
//            LogHelper.logError("getOpenModuleByDealerId-withexception", e, e.getMessage());
            log.error("getOpenModuleByDealerId error ", e);
            return new ResponseContent<>(500, "getOpenModuleByDealerId-withexception");
        }
    }

    public UnTemlateNews getUnTemplateNews(String dealerid, int pageIndex, int pagesize, int classIds) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("dealerId", dealerid);
            paramsl.put("pageIndex", Integer.toString(pageIndex));
            paramsl.put("pagesize", Integer.toString(pagesize));
//            paramsl.put("classIds", Integer.toString(classIds));
            paramsl.put("_encoding", "utf8");
            paramsl.put("_appid", "m");
            //ResponseContent<UnTemlateNews> respCnt = carApi.get("dealer.api.getUnTemplateNews", paramsl, UnTemlateNews.class);
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/dealer/news/getUnTemplateNews", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<UnTemlateNews> respCnt = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<UnTemlateNews>>() {
            });

            if (respCnt == null || respCnt.getResult() == null) {
                return null;
            }
            return respCnt.getResult();
        } catch (Exception e) {
//            LogHelper.logError("getUnTemplateNews-withexception", e, e.getMessage());
            log.error("getUnTemplateNews error ", e);
            return null;
        }
    }

    //小视频列表
    public List<DealerVideo> GetDealerVideos(String dealerid) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("dealerId", dealerid);
            paramsl.put("size", "3");
            paramsl.put("page", "1");
            paramsl.put("_encoding", "utf8");
            paramsl.put("_appid", "m");
//            String result = carApi.get("dealer.api.GetDealerVideos", paramsl);
//            if (StringUtils.isBlank(result)) {
//                return null;
//            }
//            ResponseContent<List<DealerVideo>> respCnt = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<List<DealerVideo>>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/dealer/dealervideo/videos", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<List<DealerVideo>> respCnt = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<List<DealerVideo>>>() {
            });
            if (respCnt == null || respCnt.getResult() == null) {
                return null;
            }
            return respCnt.getResult();
        } catch (Exception e) {
//            LogHelper.logError("GetDealerVideos-withexception", e, e.getMessage());
            log.error("GetDealerVideos error", e);
            return null;
        }
    }

    /**
     * 根据厂商id获取经销商
     *
     * @param fctid
     * @param cityid
     * @param _appid
     * @param orderType
     * @param pageIndex
     * @param pageSize
     * @param myLat
     * @param myLon
     * @return
     */
    @Override
    public ResponseContent<?> GetDealersByFctid(int brandId, int fctid, int cityid, String _appid, String orderType, int pageIndex, int pageSize, String myLat, String myLon, String _encoding) {
        try {

            Map<String, Object> param = new HashMap<>();
//            if (brandId > 0) {
            param.put("brandId", Integer.toString(brandId));
//            }
            param.put("factoryId", Integer.toString(fctid));
            param.put("factoryId", Integer.toString(fctid));
            param.put("cityId", Integer.toString(cityid));
            param.put("orderType", orderType);
            param.put("_appid", _appid);
            param.put("pageIndex", Integer.toString(pageIndex));
            param.put("_encoding", _encoding);
            param.put("pageSize", Integer.toString(pageSize));
            if (StringUtil.isNotNullAndWhiteSpace(myLat))
                param.put("myLat", myLat);
            if (StringUtil.isNotNullAndWhiteSpace(myLon))
                param.put("myLon", myLon);

//            String result = carApi.get("dealer.api.GetDealersByFctid", param);
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(0, "暂无数据");
//            }
////            TypeUtils.compatibleWithJavaBean = true;
//            ResponseContent<DealerResult> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<DealerResult>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.autohome.com.cn/dealers/DealerList/GetDealers", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(101, "暂无数据");
            }
            ResponseContent<DealerResult> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerResult>>() {
            });
            if (responseContent == null || responseContent.getResult() == null ||
                    responseContent.getResult().getRowcount() == 0 || responseContent.getResult().getList().size() == 0) {
                return new ResponseContent<>(101, "暂无数据");
            }
            if (StringUtil.isNotNullAndWhiteSpace(myLat))
                responseContent.getResult().setLatitude(myLat);
            if (StringUtil.isNotNullAndWhiteSpace(myLon))
                responseContent.getResult().setLongitude(myLon);
//            if (orderType.equals("2")) {
//                responseContent.getResult().getList().sort(new Comparator<Dealer>() {
//                    @Override
//                    public int compare(Dealer o1, Dealer o2) {
//                        if (Double.parseDouble(o1.getDistanceitem()) > Double.parseDouble(o2.getDistanceitem())) {
//                            return 1;
//                        } else {
//                            return -1;
//                        }
//                    }
//                });
//            }
            return responseContent;
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getDealers-WithException", e.getMessage())));
            log.error("GetDealersByFctid error ", e);
            return new ResponseContent<>(500, "getDealers-WithException" + e.getMessage());
        }
    }

    //经销商VR列表
    @Override
    public DealerVR getSeriesVRList(String dealerid) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("dealerid", dealerid);
            paramsl.put("_encoding", "utf8");
            paramsl.put("_appid", "m");
//            String result = carApi.get("dealer.api.getSeriesVRList", paramsl);
//            if (StringUtil.isBlank(result)) {
//                return null;
//            }
//            ResponseContent<DealerVR> respCnt = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<DealerVR>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/dealer/wapper/getSeriesVRList", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<DealerVR> respCnt = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<DealerVR>>() {
            });
            if (respCnt == null || respCnt.getResult() == null) {
                return null;
            }

            return respCnt.getResult();
        } catch (Exception e) {
//            LogHelper.logError("getSeriesVRList-withexception", e, e.getMessage());
            log.error("getSeriesVRList error", e);
            return null;
        }
    }

    /**
     * 获取试驾订单
     *
     * @param mobile
     * @param dealerid
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @Override
    public TestDriveOrder getUserTestDriveOrderList(String mobile, String dealerid, int pageIndex, int pageSize) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("mobile", mobile);
            paramsl.put("dealerid", dealerid);
            paramsl.put("pageIndex", Integer.toString(pageIndex));
            paramsl.put("pageSize", Integer.toString(pageSize));
//            String reuslt = HttpHelper.postResponse("http://api.sjlive.yz.autohome.com.cn/clue/list", paramsl, null).getContent();
//            if (StringUtils.isBlank(reuslt)) {
//                return null;
//            }
//            ResponseContent<TestDriveOrder> respCnt = JSON.parseObject(reuslt, new com.alibaba.fastjson.TypeReference<ResponseContent<TestDriveOrder>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://api.sjlive.yz.autohome.com.cn/clue/list", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<TestDriveOrder> respCnt = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<TestDriveOrder>>() {
            });

            if (respCnt == null || respCnt.getResult() == null || respCnt.getResult().getList() == null || respCnt.getResult().getList().size() == 0) {
                return null;
            }
//            HashSet<String> seriesids = new HashSet<>();
//            for (TestDriveOrder.OderList order : respCnt.getResult().getList()) {
//                if (order.getSeriesid() > 0)
//                    seriesids.add(Integer.toString(order.getSeriesid()));
//            }
//            if (seriesids == null && seriesids.size() == 0) {
//                return respCnt.getResult();
//            }
//            CarSeriesList seriesList = carApiService.getBaseInfoBySeriesList(String.join(",", seriesids));
//            if (seriesList == null || seriesList.getList() == null || seriesList.getList().size() == 0) {
//                return respCnt.getResult();
//            }
//            seriesList.getList().forEach(s -> {
//                respCnt.getResult().getList().forEach(r -> {
//                    if (s.getSeriesid() == r.getSeriesid()) {
//                        if (StringUtils.isNotNullAndWhiteSpace(s.getSeriespnglogo())) {
//                            r.setSerieslogo(s.getSeriespnglogo());
//                        }
//                    }
//                });
//            });
            return respCnt.getResult();
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getUserOrderList-withexception", e.getMessage())));
//            LogHelper.logError("获取试驾订单异常", e, e.getMessage());
            log.error("getUserTestDriveOrderList error", e);
            return null;
        }
    }

    /**
     * 获取保养订单
     *
     * @param userid
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @Override
    public MaintainOrder maintainorderlist(String userid, String dealerid, int pageIndex, int pageSize) {

        try {
            HashMap<String, Object> paramsl = new HashMap<>();
            paramsl.put("userid", userid);
            paramsl.put("dealerid", dealerid);
            paramsl.put("pageIndex", Integer.toString(pageIndex));
            paramsl.put("pageSize", Integer.toString(pageSize));
//            String reuslt = carApi.get("youche.api.myorderlist", paramsl);
//            if (StringUtils.isBlank(reuslt)) {
//                return null;
//            }
//            ResponseContent<MaintainOrder> respCnt = JSON.parseObject(reuslt, new com.alibaba.fastjson.TypeReference<ResponseContent<MaintainOrder>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://api.youche.in.autohome.com.cn/as/order/listbydealer", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<MaintainOrder> respCnt = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<MaintainOrder>>() {
            });
            if (respCnt == null || respCnt.getResult() == null || respCnt.getResult().getList() == null) {
                return null;
            }
//            HashSet<String> specids = new HashSet<>();
//            for (MaintainOrder.Orderlist order : respCnt.getResult().getList()) {
//                if (order.getSpecid() > 0)
//                    specids.add(Integer.toString(order.getSpecid()));
//            }
//            if (specids == null || specids.size() == 0) {
//                return respCnt.getResult();
//            }
//            CarSpecResult carSpecResult = carApiService.getCarSpec_BaseInfbySpecList("m", String.join(",", specids));
//            if (carSpecResult == null || carSpecResult.getList() == null || carSpecResult.getList().size() == 0) {
//                return respCnt.getResult();
//            }
//            carSpecResult.getList().forEach(c -> {
//                respCnt.getResult().getList().forEach(r -> {
//                    if (c.getSpecid() == r.getSpecid()) {
//                        if (StringUtils.isNotNullAndWhiteSpace(c.getPic())) {
//                            r.setSpeclogo(c.getPic());
//                        }
//                    }
//                });
//            });
            return respCnt.getResult();
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("maintainorderlist-withexception", e.getMessage())));
//            LogHelper.logError("获取保养订单异常", e, e.getMessage());
            log.error("maintainorderlist error", e);
            return null;
        }
    }

    @Override
    public ResponseContent<SpecDealerPrice> getSpecsMinPrice(String _appid, int cityId, String specids, int dealerid) {

        try {
            Map<String, Object> param = new HashMap<>();
            param.put("dealerid", Integer.toString(dealerid));
            param.put("cityid", Integer.toString(cityId));
            param.put("_appid", _appid);
            param.put("specids", specids);

//            String result = HttpHelper.getResponse(getSpecsMinPrice, param, null, 3000).getContent();
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(0, "暂无数据");
//            }
////            TypeUtils.compatibleWithJavaBean = true;
//            ResponseContent<SpecDealerPrice> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<SpecDealerPrice>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/statistics/spec/getSpecsMinPrice", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<SpecDealerPrice> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<SpecDealerPrice>>() {
            });
            if (responseContent == null || responseContent.getResult() == null) {
                return new ResponseContent<>(0, "暂无数据");
            }

            return responseContent;
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getSpecsMinPrice-WithException", e.getMessage())));
            log.error("getSpecsMinPrice error", e);

            return new ResponseContent<>(500, "getSpecsMinPrice-WithException" + e.getMessage());
        }

    }


    @Override
    public ResponseContent<List<SeriesDealerPrice>> getSeriesMinOriginalPriceSpec(String _appid, int cityId, String seriesIds) {

        try {
            Map<String, Object> param = new HashMap<>();

            param.put("cityid", Integer.toString(cityId));
            param.put("_appid", _appid);
            param.put("seriesIds", seriesIds);

//            String result = HttpHelper.getResponse(getSeriesMinOriginalPriceSpec, param, null, 3000).getContent();
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(0, "暂无数据");
//            }
////            TypeUtils.compatibleWithJavaBean = true;
//            ResponseContent<List<SeriesDealerPrice>> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<List<SeriesDealerPrice>>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://dealer.api.lq.autohome.com.cn/statistics/seriesprice/getSeriesMinOriginalPriceSpec", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(0, "暂无数据");
            }
            ResponseContent<List<SeriesDealerPrice>> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<List<SeriesDealerPrice>>>() {
            });
            if (responseContent == null || responseContent.getResult() == null) {
                return new ResponseContent<>(0, "暂无数据");
            }

            return responseContent;
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getSeriesMinOriginalPriceSpec-WithException", e.getMessage())));
            log.error("getSeriesMinOriginalPriceSpec error", e);
            return new ResponseContent<>(500, "getSeriesMinOriginalPriceSpec-WithException" + e.getMessage());
        }

    }


    /**
     * 车系小视频
     *
     * @return
     */
    @Override
    public ResponseContent<?> getseriesvideolatest(String imei, String series_id, int startindex, int count) {

        try {
            Map<String, Object> paramsl = new HashMap<>();
            paramsl.put("imei", imei);
            paramsl.put("series_id", series_id);
            paramsl.put("startindex", Integer.toString(startindex));
            paramsl.put("count", Integer.toString(count));
//            String reuslt = carApi.get("intranet.api.getseriesvideolatest", paramsl);
//            if (StringUtils.isBlank(reuslt)) {
//                return null;
//            }
//            ResponseContent<Videolatest> respCnt = JSON.parseObject(reuslt, new com.alibaba.fastjson.TypeReference<ResponseContent<Videolatest>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://intranet.sv.api.autohome.com.cn/100/video/getseriesvideolatest", paramsl);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(0, "暂无数据");
            }
            ResponseContent<Videolatest> respCnt = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<Videolatest>>() {
            });
            if (respCnt == null || respCnt.getResult() == null) {
                return new ResponseContent<>(0, "暂无数据");
            }
            return respCnt;
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("getseriesvideolatest-withexception", e.getMessage())));
//            LogHelper.logError("车系小视频异常", e, e.getMessage());
            log.error("getseriesvideolatest-withexception", e);
            return null;
        }
    }


}