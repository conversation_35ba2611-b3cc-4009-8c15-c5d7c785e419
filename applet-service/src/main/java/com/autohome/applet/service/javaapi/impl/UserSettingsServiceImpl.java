package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.dao.javaapi.mapper.UserSettingsMapper;
import com.autohome.applet.dao.javaapi.model.UserSettings;
import com.autohome.applet.model.constants.RedisKeys;
import com.autohome.applet.model.dto.UserSettingsDto;
import com.autohome.applet.service.javaapi.UserSettingsService;
import com.autonews.springboot.util.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class UserSettingsServiceImpl implements UserSettingsService {

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    UserSettingsMapper userSettingsMapper;

    @Override
    public UserSettingsDto getUserSettings(String openId) {
        UserSettings userSettings = userSettingsMapper.getUserSettings(openId);
        if (userSettings == null) {
            return null;
        }
        UserSettingsDto dto = new UserSettingsDto();
        dto.setOpenId(userSettings.getOpenid());
        dto.setPersonalizedRec(userSettings.getPersonalizedrec());
        return dto;
    }

    @Override
    public boolean getPersonalizedRec(String openId) {
        String value = redisClient.getValue(String.format(RedisKeys.USER_SETTINGS_PERSONALIZED_REC, openId));
        if (value != null) {
            return "1".equals(value);
        }
        Integer personalizedRec = userSettingsMapper.getPersonalizedRec(openId);
        // 没有记录的默认为开启 || 有记录的 == 1 为开启
        return personalizedRec == null || personalizedRec == 1;
    }

    @Override
    public void setPersonalizedRec(String openId, boolean enable) {
        redisClient.setValue(String.format(RedisKeys.USER_SETTINGS_PERSONALIZED_REC, openId)
                , String.valueOf(enable ? 1 : 0), 356, TimeUnit.DAYS);
        UserSettings userSettings = userSettingsMapper.getUserSettings(openId);
        if (userSettings == null) {
            UserSettings row = new UserSettings();
            row.setOpenid(openId);
            row.setPersonalizedrec(enable ? 1 : 0);
            userSettingsMapper.insertNew(row);
        } else {
            userSettings.setPersonalizedrec(enable ? 1 : 0);
            userSettingsMapper.updateByPrimaryKey(userSettings);
        }
    }

//    @PostConstruct
    @Scheduled(cron = "0 0 5 * * ?")
    public void init() {
        List<UserSettings> userSettings = userSettingsMapper.selectAll();
        for (UserSettings userSetting : userSettings) {
            redisClient.setValue(String.format(RedisKeys.USER_SETTINGS_PERSONALIZED_REC, userSetting.getOpenid())
                    , String.valueOf(userSetting.getPersonalizedrec()), 356, TimeUnit.DAYS);
        }

    }
}
