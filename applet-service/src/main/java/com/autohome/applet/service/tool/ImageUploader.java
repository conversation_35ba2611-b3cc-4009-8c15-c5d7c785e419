package com.autohome.applet.service.tool;

import com.autonews.comm.fileUpload.JsonResult;
import com.autonews.comm.fileUpload.UploadFileInfo;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.comm.utils.JsonUtils;
import com.autonews.comm.utils.StringUtils;
import com.autonews.springboot.util.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ImageUploader {
    private String GetTokenUrl = "http://baseup.afs.bj.autohome.com.cn/service/get/token";
    private String SaveFileUrl = "http://baseup.afs.bj.autohome.com.cn/service/fastUp";
    private String CheckTokenUrl = "http://baseup.sjz.autohome.com.cn/service/search/token";
    private String DelFileUrl = "http://baseup.afs.bj.autohome.com.cn/service/DelImgNew";
    private int minutes = 100;
    @Value("${fastdfs.host:www3.autoimg.cn}")
    private String host;
    @Value("${fastdfs.path:lightapp}")
    private String path;
    @Value("${fastdfs.source:zxptdfs}")
    private String source;
    @Value("${fastdfs.key:305c300d06092a864886f70d0101010500034b003048024100deeb1cbf49995174c14e4fb698dda9c88c09133654b349ee57996904c1e3a48fc10610991bae24f75218de48357283a85b4436f3788f39ca4c58380446e047770203010001}")
    private String key;

    @Autowired
    @Qualifier("lightapp")
    RedisClient redisClientLightapp;

    private String getToken() throws Exception {
        //查询redis
        String key = "fastdfs:token:"+source;
        String redisValue = redisClientLightapp.getValue(key);
        if(!org.apache.commons.lang3.StringUtils.isEmpty(redisValue)){
            return redisValue;
        }

        String jsonResult = HttpClientUtils.post(this.GetTokenUrl, "source=" + this.source + "&key=" + this.key + "&minutes=" + this.minutes + "", "application/x-www-form-urlencoded", "UTF-8", 1000, 3000);
        JsonResult result = (JsonResult) JsonUtils.toObject(jsonResult, JsonResult.class);

        if (result.getCode() != 0 || result.getResult().length() <= 0) {
            log.error("fastdfs getToken 失败:{}",jsonResult);
            throw new Exception("获取token失败");
        }

        log.info("fastdfs getToken:{}",jsonResult);
        redisClientLightapp.setValue(key,result.getResult(),minutes, TimeUnit.MINUTES);
        return result.getResult();
    }

    public boolean del(String img) throws Exception {
        return this.del(img, 1);
    }

    public boolean del(String img, int type) throws Exception {
        JsonResult jsonResult = this.delImg(img, type);
        return jsonResult.getCode() == 0;
    }

    public JsonResult delImg(String img, int type) throws Exception {
        String jsonResult = HttpClientUtils.post(this.DelFileUrl, "source=" + this.source + "&type=" + type + "&token=" + this.getToken() + "&url=" + img, "application/x-www-form-urlencoded", "UTF-8", 1000, 3000);
        return (JsonResult)JsonUtils.toObject(jsonResult, JsonResult.class);
    }

    public UploadFileInfo save(String img) throws Exception {
        String[] parts = img.split(",");
        String imageString = parts[1];
        String exName = "jpg";
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] imageByte = decoder.decode(imageString);
        ByteArrayInputStream bis = new ByteArrayInputStream(imageByte);
        UploadFileInfo fileInfo = this.Save((InputStream)bis, exName);
        bis.close();
        return fileInfo;
    }

    public UploadFileInfo Save(InputStream stream, String exName) throws Exception {
        ContentBody cd = new InputStreamBody(stream, "1.jpg");
        return this.Save((ContentBody)cd, exName);
    }

    public UploadFileInfo Save(ContentBody file, String exName) throws Exception {
        UploadFileInfo result = new UploadFileInfo();
        HttpPost httpRequest = new HttpPost(this.SaveFileUrl);
        MultipartEntity multipartEntity = new MultipartEntity();
        multipartEntity.addPart("token", new StringBody(this.getToken()));
        multipartEntity.addPart("exName", new StringBody(exName));
        multipartEntity.addPart("source", new StringBody(this.source));
        multipartEntity.addPart("file", file);

        try {
            httpRequest.setEntity(multipartEntity);
            HttpResponse httpResponse = (new DefaultHttpClient()).execute(httpRequest);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                String jsonResult = EntityUtils.toString(httpResponse.getEntity());
                JsonResult obj = (JsonResult)JsonUtils.toObject(jsonResult, JsonResult.class);
                if (obj.getCode() == 0 && obj.getResult().length() > 0) {
                    String result1 = this.toHttpImageUrl(obj.getResult());
                    result.setImgUrl(result1);
                } else {
                    result.setReturnCode(obj.getCode());
                    result.setMessage("上传图片失败");
                }
            } else {
                result.setReturnCode(-10000);
                result.setMessage("图片服务器返回异常");
            }
        } catch (Exception var10) {
            result.setReturnCode(-10000);
            result.setMessage("图片服务器返回异常");
        }

        return result;
    }

    private String toHttpImageUrl(String result1) {
        Path p = Paths.get(result1);
        String imageName = p.getFileName().toString();
        if (StringUtils.isNullOrEmpty(this.path)) {
            this.path = "newsdfs";
        }

        String newPath = this.path + "/" + result1.replace(imageName, "autohomecar__" + imageName);
        String url;
        if (StringUtils.isNullOrEmpty(this.host)) {
            url = ImageHelper.AddImageDomain(newPath);
        } else {
            url = ImageHelper.AddImageDomain(newPath, this.host);
        }

        return url;
    }

    public static class ImageHelper {
        ImageHelper() {
        }

        public static String AddImageDomain(String imagepath) {
            return AddImageDomain(imagepath, "www%1$s.autoimg.cn");
        }

        public static String AddImageDomain(String imagepath, String host) {
            if (host == null || host.isEmpty()) {
                host = "www%1$s.autoimg.cn";
            }

            if (imagepath != null && !imagepath.isEmpty()) {
                if (imagepath.indexOf("http://") > -1) {
                    return "";
                } else {
                    imagepath = imagepath.trim();
                    host = host.trim();
                    int no = 0;
                    if (imagepath.indexOf("autohomecar__") != -1) {
                        no = 2;
                    }

                    no += GetImgDomainNum(imagepath);
                    return String.format("https://" + host + "/" + imagepath, no);
                }
            } else {
                return "";
            }
        }

        public static int GetImgDomainNum(String name) {
            int r = 0;
            int b = 0;

            while(true) {
                r += 4;
                if (r >= name.length()) {
                    b %= 2;
                    return b;
                }

                b ^= name.toCharArray()[r];
            }
        }
    }
}
