package com.autohome.applet.service.douyin.impl;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import com.autohome.applet.dao.javaapi.mapper.*;
import com.autohome.applet.dao.javaapi.model.DouyinSitemapHistory;
import com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap;
import com.autohome.applet.dao.javaapi.model.MiniTokenValue;
import com.autohome.applet.dao.javaapi.model.SweetCity;
import com.autohome.applet.dao.javaapi.model.douyin.DouYinSiteMapHistoryDto;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.douyin.DouYinBaseReturn;
import com.autohome.applet.model.dto.douyin.HangQingDto;
import com.autohome.applet.model.dto.douyin.WenDaDto;
import com.autohome.applet.model.enums.MainDataTypeToBizTypeEnum;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.service.javaapi.SweetService;
import com.autohome.applet.service.resourcefeedpush.impl.ResourceFeedPushToBaiduServiceImpl;
import com.autohome.applet.util.*;
import com.autonews.springboot.util.RedisClient;
import com.dingtalk.api.DingTalkClient;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 抖音小程序相关业务
 */
@Service
@Slf4j
public class DouYinServiceImpl implements DouYinService {

    @Autowired
    MiniTokenValueMapper miniTokenValueMapper;

    //抖音小程序appid
    final String douYinAppId = "tt0190772d9eba4876";
    public static String DOUYIN_SITEMAP_KEY = "douyin:sitemap:";

    @Autowired
    HttpService httpService;

    @Autowired
    @Resource(name = "lightapp")
    private RedisClient redisClient;
    @Value("${dingding.douyin.accesstoken}")
    private String douyinaccesstoken;
    @Value("${dingding.douyin.secret}")

    private String douyinsecret;
    @Value("${dingding.baidu.accesstoken}")
    private String baiduaccesstoken;
    @Value("${dingding.baidu.secret}")
    private String baidusecret;

    @Autowired
    private DouyinSitemapHistoryMapper douyinSitemapHistoryMapper;
    @Autowired
    private HistoryDouyinSitemapMapper historyDouyinSitemapMapper;

    /**
     * 根据小程序类型获取最新accesstoken
     *
     * @param minitype 100:抖音小程序
     * @return
     */
    @Override
    public Map<String, String> selectAccessTokenByMiniType(Integer minitype) {

        Map<String, String> map = new HashMap<>();
        String top1 = miniTokenValueMapper.getTop1(minitype);
        map.put("accessToken", top1);
        return map;
    }


    /**
     * 抖音web化，提交sitemap
     * https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/web-microapp/submit-paths
     *
     * @param pagePaths
     * @param pushFlag  推送类型 11:问答新增  21：行情新增
     * @return
     */
    public DouYinBaseReturn uploadSiteMap(List<String> pagePaths, int pushFlag) {
        if (pagePaths.size() > 100000 || pagePaths.size() < 1) {
            return DouYinBaseReturn.builder().err_no(-1).err_msg("单次提交数必须大在1~100000条之间").build();
        }
        final String url = "https://open.douyin.com/api/apps/v1/search/upload_sitemap/";
        JSONObject param = new JSONObject();
        param.put("app_id", douYinAppId);
        param.put("page_paths", pagePaths);

        //设置accesstoken
        Map<String, String> stringStringMap = selectAccessTokenByMiniType(100);
        Map<String, Object> headers = new HashMap<>();
        headers.put("access-token", stringStringMap.get("accessToken"));

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(url, param.toJSONString(), headers);

        log.info("douyin uploadSitemap 推送类型:{} ,url={} , param={} ,headers={},httpreturn={}", pushFlag, url, param, headers, httpResult.getBody());
        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return DouYinBaseReturn.builder().err_no(-2).err_msg("sitemap数据提交失败").build();
        }
        DouYinBaseReturn douYinBaseReturn = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<DouYinBaseReturn>() {
        });
        if (douYinBaseReturn.getErr_no() != 0) {
            //此处需要判断推送类型，行情数据推送失败不回流，其他类型均需回流
            if(pushFlag!=100&&pushFlag!=110){
                addErrorDataToHistory(pagePaths,pushFlag);
                log.error("抖音sitemap数据推送失败，请查及时关注,接口返回信息={},请求地址={},接口请求参数={},header={} 数据回流到历史表", httpResult.getBody(), url, param, headers);
            }
        }

        return douYinBaseReturn;
    }

    /**
     * 抖音web化，删除sitemap
     * https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/web-microapp/submit-paths
     * https://open.douyin.com/api/apps/v1/search/delete_index/
     *
     * @param pagePaths
     * @param pushFlag  12：问答删除  22:行情删除
     * @return
     */
    public DouYinBaseReturn delSiteMap(List<String> pagePaths, int pushFlag) {
        if (pagePaths.size() > 5000 || pagePaths.size() < 1) {
            return DouYinBaseReturn.builder().err_no(-1).err_msg("单次提交数必须大在1~5000条之间").build();
        }
        final String url = "https://open.douyin.com/api/apps/v1/search/delete_index/";
        JSONObject param = new JSONObject();
        param.put("app_id", douYinAppId);
        param.put("name", "index");
        param.put("path_list", pagePaths);

        //设置accesstoken
        Map<String, String> stringStringMap = selectAccessTokenByMiniType(100);
        Map<String, Object> headers = new HashMap<>();
        headers.put("access-token", stringStringMap.get("accessToken"));

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(url, param.toJSONString(), headers);
        log.info("douyin delSitemap 推送类型:{} ,url={} , param={} ,headers={},httpreturn={}", pushFlag, url, param, headers, httpResult.getBody());

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return DouYinBaseReturn.builder().err_no(-2).err_msg("sitemap数据提交失败").build();
        }
        DouYinBaseReturn douYinBaseReturn = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<DouYinBaseReturn>() {
        });

        return douYinBaseReturn;
    }

    /**
     * 问答
     * https://zhishi.autohome.com.cn/home/<USER>/file?targetId=plj8BCSr4q
     *
     * @param param
     * @param url
     * @param flag  1新增，-1删除
     * @return
     */

    public Object getWenDa(Map<String, Object> param, String url, int flag) {


        Boolean hasmore = true;//是否有下一页
        if(flag==1){
            param.put("pagesize", 50);
            param.put("pageid", "");
        }else{
            param.put("pageSize", 50);
            param.put("pageId", "");
        }

        int totalNum = 0;
        int totalFailNum = 0;
        int totalSuccessNum = 0;
        while (hasmore) {
            List<String> list = new ArrayList<>();
            try {
                ReturnValue<WenDaDto> wenDaDtoReturnValue = httpService.httpGetFor(url, param, new TypeReference<ReturnValue<WenDaDto>>() {
                });
                hasmore = wenDaDtoReturnValue.getResult().isHasmore();

                if(flag==1){
                    param.put("pageid", wenDaDtoReturnValue.getResult().getPageid());
                }else{
                    param.put("pageId", wenDaDtoReturnValue.getResult().getPageid());
                }

                for (WenDaDto.ItemData item : wenDaDtoReturnValue.getResult().getItems()) {
                    list.add("detailpackage/pages/aigc-qa/index?id=" + item.getId());
                }
                if (wenDaDtoReturnValue.getResult().getItems().size() == 0) {
                    continue;
                }

                totalNum += wenDaDtoReturnValue.getResult().getItems().size();
                DouYinBaseReturn douYinBaseReturn = null;
                if (flag == 1) {
                    douYinBaseReturn = uploadSiteMap(list, 101);
                    log.info("本次执行问答-新增sitemap,url:{},param：{},推送条数:{},当前执行到{},推送结果：{}", url, param, list.size(), totalNum, JacksonHelper.serialize(douYinBaseReturn));
                } else {
                    douYinBaseReturn = delSiteMap(list, 12);
                    log.info("本次执行问答-删除sitemap,url:{},param：{},推送条数:{},当前执行到{},推送结果：{}", url, param, list.size(), totalNum, JacksonHelper.serialize(douYinBaseReturn));
                }
                if (douYinBaseReturn.getErr_no() == 0) {
                    totalSuccessNum += wenDaDtoReturnValue.getResult().getItems().size();
                } else {
                    totalFailNum += wenDaDtoReturnValue.getResult().getItems().size();
                }


            } catch (Exception e) {
                if (!e.getMessage().equals("1001")) {
                    log.error("问答出现异常，url:{},请求参数：{}", url, param);
                }
            }
        }

        if (flag == 1) {
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,101, 1, 0, totalNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,101, 1, 1, totalSuccessNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,101, 1, 2, totalFailNum);
        } else {
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,101, 2, 0, totalNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,101, 2, 1, totalSuccessNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,101, 2, 2, totalFailNum);
        }

        log.info("本次执行【{}】问答完成，汇总数据如下：开始时间：{},结束时间：{}，推送总条数：{}，成功推送：{}，推送失败：{}", (flag == 1 ? "新增" : "删除"), param.get("startTime"), param.get("endTime"), totalNum, totalSuccessNum, totalFailNum);
        JobLog.info("本次执行【" + (flag == 1 ? "新增" : "删除") + "】问答完成，汇总数据如下：开始时间：" + param.get("startTime") + ",结束时间：" + param.get("endTime") + "，推送总条数：" + totalNum + "，成功推送：" + totalSuccessNum + "，推送失败：" + totalFailNum);


        return "success";
    }

    /**
     * 行情
     * https://zhishi.autohome.com.cn/home/<USER>/file?targetId=rS0vOPTF7g
     *
     * @param
     * @return
     */
    @Override
    public Object getHangQing(Map<String, Object> param, String url, int flag) {

        Boolean hasmore = true;//是否有下一页

        if(flag==1){
            param.put("pagesize", 50);
            param.put("pageid", "");
        }else{
            param.put("pageSize", 50);
            param.put("pageId", "");
        }

        int totalNum = 0;
        int totalFailNum = 0;
        int totalSuccessNum = 0;
        while (hasmore) {
            List<String> list = new ArrayList<>();
            try {
                ReturnValue<HangQingDto> hangQingDtoReturnValue = httpService.httpGetFor(url, param, new TypeReference<ReturnValue<HangQingDto>>() {
                });
                hasmore = hangQingDtoReturnValue.getResult().getHasmore();

                if(flag==1){
                    param.put("pageid", hangQingDtoReturnValue.getResult().getPageid());
                }else{
                    param.put("pageId", hangQingDtoReturnValue.getResult().getPageid());
                }

                for (HangQingDto.Items item : hangQingDtoReturnValue.getResult().getItems()) {
                    list.add("detailpackage/pages/aigc-hq/index?id=" + item.getId());
                }
                if (hangQingDtoReturnValue.getResult().getItems().size() == 0) {
                    continue;
                }
                DouYinBaseReturn douYinBaseReturn = null;
                totalNum += hangQingDtoReturnValue.getResult().getItems().size();
                if (flag == 1) {
                    douYinBaseReturn = uploadSiteMap(list, 100);

                    log.info("本次执行行情-新增sitemap,url:{},param：{},推送条数:{},当前执行到{},推送结果：{}", url, param, list.size(), totalNum, JacksonHelper.serialize(douYinBaseReturn));
                } else {
                    douYinBaseReturn = delSiteMap(list, 22);

                    log.info("本次执行行情-删除sitemap,url:{},param：{},推送条数:{},当前执行到{},推送结果：{}", url, param, list.size(), totalNum, JacksonHelper.serialize(douYinBaseReturn));
                }
                if (douYinBaseReturn.getErr_no() == 0) {
                    totalSuccessNum += hangQingDtoReturnValue.getResult().getItems().size();
                } else {
                    totalFailNum += hangQingDtoReturnValue.getResult().getItems().size();
                }


            } catch (Exception e) {
                if (!e.getMessage().equals("1001")) {
                    log.error("行情出现异常，url:{},请求参数：{}", url, param);
                }
            }
        }
        if (flag == 1) {
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,100, 1, 0, totalNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,100, 1, 1, totalSuccessNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,100, 1, 2, totalFailNum);
        } else {
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,100, 2, 0, totalNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,100, 2, 1, totalSuccessNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,100, 2, 2, totalFailNum);
        }

        log.info("本次执行【{}】行情完成，汇总数据如下：开始时间：{},结束时间：{}，推送总条数：{}，成功推送：{}，推送失败：{}", (flag == 1 ? "新增" : "删除"), param.get("startTime"), param.get("endTime"), totalNum, totalSuccessNum, totalFailNum);
        JobLog.info("本次执行【" + (flag == 1 ? "新增" : "删除") + "】行情完成，汇总数据如下：开始时间：" + param.get("startTime") + ",结束时间：" + param.get("endTime") + "，推送总条数：" + totalNum + "，成功推送：" + totalSuccessNum + "，推送失败：" + totalFailNum);
        return "success";
    }

    /**
     * 设置统计信息
     *
     * @param source
     * @param flag   1：新增 2：删除
     * @param type   0:总条数 1:推送成功 2:推送失败
     * @param num
     */
    public void addDouYinSiteMapRedis(String keyHeader,int source, int flag, int type, int num) {

        try {
            //例子  2024-09-10:100:1:1:100
            String key = keyHeader + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" + source + ":" + flag + ":" + type;

            String value = redisClient.get(key);
            if (value == null || value.length() == 0) {
                redisClient.setValue(key, num + "", 5 * 365, TimeUnit.DAYS);
            } else {
                redisClient.setValue(key, (Integer.parseInt(value) + num) + "", 5 * 365, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            log.error("addDouYinSiteMapRedis error,source:{} flag:{} type:{} num:{},error:{}", source, flag, type, num, e);
        }

    }

    /**
     * 设置统计信息
     *
     * @param source
     * @param flag   1：新增 2：删除
     * @param type   0:总条数 1:推送成功 2:推送失败
     * @param num
     */
    public void addBaiduReportRedis(String keyHeader, String date,int source,int flag,int type,int num) {

        try {
            //例子  2024-09-10:100:1:1:100
            String key = keyHeader + date + ":" + source + ":" + flag + ":" + type;

            String value = redisClient.get(key);
            if (value == null || value.length() == 0) {
                redisClient.setValue(key, num + "", 5 * 365, TimeUnit.DAYS);
            } else {
                redisClient.setValue(key, (Integer.parseInt(value) + num) + "", 5 * 365, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            log.error("addDouYinSiteMapRedis error,source:{} flag:{} type:{} num:{},error:{}", source, flag, type, num, e);
        }

    }

    /**
     * 设置统计信息
     *
     * @param source
     * @param flag   1：新增 2：删除
     * @param type   0:总条数 1:推送成功 2:推送失败
     */
    @Override
    public void removeBaiduReportRedis(String keyHeader, String date, int source, int flag, int type) {
        try {
            //例子  2024-09-10:100:1:1:100
            String key = keyHeader + date + ":" + source + ":" + flag + ":" + type;

            redisClient.remove(key);

        } catch (Exception e) {
            log.error("removeDouYinSiteMapRedis error,source:{} flag:{} type:{} num:{},error:{}", source, flag, type, e);
        }
    }

    /**
     * 设置统计信息
     *
     * @param
     */
    public Object report() {

        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder stringBuilderBody = new StringBuilder();
        //表头
        stringBuilder.append("抖音小程序昨日数据日报\n");
        stringBuilder.append("|名称|提交数量|提交成功数|提交失败数|\n")
                .append("|--|--|--|--|\n");

        int totaladd = 0;
        int totaladdsuccess = 0;
        int totaladderror = 0;
        int deltotal = 0;
        int deltotalsuccess = 0;
        int deltotalerror = 0;
        Map<String, MainDataTypeToBizTypeEnum> map =new TreeMap<>(
                Arrays.stream(MainDataTypeToBizTypeEnum.values()).collect(Collectors.toMap(e -> e.getCode(), e -> e)));
        for (Map.Entry<String, MainDataTypeToBizTypeEnum> stringMainDataTypeToBizTypeEnumEntry : map.entrySet()) {
            if (stringMainDataTypeToBizTypeEnumEntry.getKey().equals("0")) {
                continue;
            }
            String addtotal = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 0;
            String addsuccess = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 1;
            String addfalse = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 2;

            String deladdtotal = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 2 + ":" + 0;
            String deladdsuccess = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 2 + ":" + 1;
            String deladdfalse = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 2 + ":" + 2;

            String addtotalnum = redisClient.get(addtotal);
            String addsuccessnum = redisClient.get(addsuccess);
            String addfalsenum = redisClient.get(addfalse);
            String deladdtotalnum = redisClient.get(deladdtotal);
            String deladdsuccessnum = redisClient.get(deladdsuccess);
            String deladdfalsenum = redisClient.get(deladdfalse);

            stringBuilderBody.append("|").append(stringMainDataTypeToBizTypeEnumEntry.getValue().getDes() + "新增").
                    append("|").append(addtotalnum == null ? "0" : addtotalnum).
                    append("|").append(addsuccessnum == null ? "0" : addsuccessnum).
                    append("|").append(addfalsenum == null ? "0" : addfalsenum).append("\n");
            if(stringMainDataTypeToBizTypeEnumEntry.getKey().equals("900")){
                continue;
            }
            stringBuilderBody.append("|").append(stringMainDataTypeToBizTypeEnumEntry.getValue().getDes() + "删除").
                    append("|").append(deladdtotalnum == null ? "0" : deladdtotalnum).
                    append("|").append(deladdsuccessnum == null ? "0" : deladdsuccessnum).
                    append("|").append(deladdfalsenum == null ? "0" : deladdfalsenum).append("\n");
            //总量
            totaladd += addtotalnum == null ? 0 : Integer.parseInt(addtotalnum);
            //总删除量
            deltotal += deladdtotalnum == null ? 0 : Integer.parseInt(deladdtotalnum);
            //总新增成功量
            totaladdsuccess += addsuccessnum == null ? 0 : Integer.parseInt(addsuccessnum);
            totaladderror += addfalsenum == null ? 0 : Integer.parseInt(addfalsenum);
            deltotalsuccess += deladdsuccessnum == null ? 0 : Integer.parseInt(deladdsuccessnum);
            deltotalerror += deladdfalsenum == null ? 0 : Integer.parseInt(deladdfalsenum);

        }
        stringBuilder.append("|").append("新增总推送量").append("|").append(totaladd).append("|").append(totaladdsuccess).append("|").append(totaladderror).append("\n");


        stringBuilder.append("|").append("删除总推送量").append("|").append(deltotal).append("|").append(deltotalsuccess).append("|").append(deltotalerror).append("\n");

        stringBuilder.append(stringBuilderBody);
        DingTalkClient client = DingTalkUtils.getClient(douyinaccesstoken, douyinsecret);
        DingTalkUtils.sendMessageByMarkdown("抖音sitemap推送提醒", stringBuilder.toString(), new ArrayList<>(), true,client);

        return "success";
    }

    /**
     * 百度设置统计信息
     *
     * @param
     */
    public Object baiduReport(){
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder stringBuilderBody = new StringBuilder();
        //表头
        stringBuilder.append("百度数据推送日报\n");
//        stringBuilder.append("|名称|提交成功数|提交失败数|\n")
//                .append("|--|--|--|\n");

        int totaladd = 0;
        int totaladdsuccess = 0;
        int totaladderror = 0;
        Map<String, MainDataTypeToBizTypeEnum> map =new TreeMap<>(
                Arrays.stream(MainDataTypeToBizTypeEnum.values()).collect(Collectors.toMap(e -> e.getCode(), e -> e)));
        for (Map.Entry<String, MainDataTypeToBizTypeEnum> stringMainDataTypeToBizTypeEnumEntry : map.entrySet()) {
            if (stringMainDataTypeToBizTypeEnumEntry.getKey().equals("0")|| Integer.parseInt(stringMainDataTypeToBizTypeEnumEntry.getKey())>=100) {
                continue;
            }
            String addtotal = ResourceFeedPushToBaiduServiceImpl.BAIDU_3GC_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 0;
            String addsuccess = ResourceFeedPushToBaiduServiceImpl.BAIDU_3GC_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 1;
            String addfalse = ResourceFeedPushToBaiduServiceImpl.BAIDU_3GC_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 2;

//            String addtotalnum = redisClient.get(addtotal);
            String addsuccessnum = redisClient.get(addsuccess);
            String addfalsenum = redisClient.get(addfalse);

            stringBuilderBody.append(stringMainDataTypeToBizTypeEnumEntry.getValue().getDes() ).
//                    append("|").append(addtotalnum == null ? "0" : addtotalnum).
                    append(" 成功:【").append(addsuccessnum == null ? "0" : addsuccessnum).append("】").
                    append(" 失败:【").append(addfalsenum == null ? "0" : addfalsenum).append("】\n");

            //总量
//            totaladd += addtotalnum == null ? 0 : Integer.parseInt(addtotalnum);
            //总新增成功量
            totaladdsuccess += addsuccessnum == null ? 0 : Integer.parseInt(addsuccessnum);
            totaladderror += addfalsenum == null ? 0 : Integer.parseInt(addfalsenum);
        }
        stringBuilder.append("总推送量 成功:【").append(totaladdsuccess).append("】 失败:【").append(totaladderror).append("】\n");

        stringBuilder.append(stringBuilderBody);
        DingTalkClient client = DingTalkUtils.getClient(baiduaccesstoken, baidusecret);
//        DingTalkUtils.sendMessageByMarkdown("百度数据推送日报提醒", stringBuilder.toString(), new ArrayList<>(), true,client);
        DingTalkUtils.sendMessageByText(  stringBuilder.toString(), new ArrayList<>(), true,client);


        return "success";
    }

    /**
     * 百度设置统计信息
     *
     * @param
     */
    public Object baiduReport(List<String> dateList){
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder stringBuilderBody = new StringBuilder();
        //表头
        stringBuilder.append("百度数据推送日报 【").append(dateList.get(0)).append("—").append(dateList.get(dateList.size()-1)).append("】\n");
//        stringBuilder.append("|名称|提交成功数|提交失败数|\n")
//                .append("|--|--|--|\n");

        int totaladd = 0;
        int totaladdsuccess = 0;
        int totaladderror = 0;
        if(CollectionUtils.isEmpty(dateList)){
            return "success";
        }
        Map<String, Map<String, Integer>> mapResult = new HashMap();
        for(int i =0; i< dateList.size(); i++){
            Map<String, MainDataTypeToBizTypeEnum> map =new TreeMap<>(
                    Arrays.stream(MainDataTypeToBizTypeEnum.values()).collect(Collectors.toMap(e -> e.getCode(), e -> e)));
            for (Map.Entry<String, MainDataTypeToBizTypeEnum> stringMainDataTypeToBizTypeEnumEntry : map.entrySet()) {
                if (stringMainDataTypeToBizTypeEnumEntry.getKey().equals("0")) {
                    continue;
                }
                if(mapResult == null){
                    mapResult = new HashMap<>();
                }
                String addsuccess = ResourceFeedPushToBaiduServiceImpl.BAIDU_3GC_KEY + dateList.get(i) + ":" +
                        stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 1;
                String addfalse = ResourceFeedPushToBaiduServiceImpl.BAIDU_3GC_KEY + dateList.get(i) + ":" +
                        stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 2;

                String addsuccessnum = redisClient.get(addsuccess);
                String addfalsenum = redisClient.get(addfalse);
                int successNum = addsuccessnum == null ? 0 : Integer.parseInt(addsuccessnum);
                int falseNum = addfalsenum == null ? 0 : Integer.parseInt(addfalsenum);
                //总新增成功量
                totaladdsuccess += successNum;
                totaladderror += falseNum;

                Map<String, Integer> tmpResult = mapResult.get(stringMainDataTypeToBizTypeEnumEntry.getValue().getDes());
                if(tmpResult == null){
                    tmpResult = new HashMap<>();
                    tmpResult.put("success", successNum);
                    tmpResult.put("false", falseNum);
                    mapResult.put(stringMainDataTypeToBizTypeEnumEntry.getValue().getDes(), tmpResult);
                }
                else{
                    Integer tmpSuccessNum = tmpResult.get("success");
                    Integer tmpFalseNum = tmpResult.get("false");
                    successNum += (tmpSuccessNum == null ? 0 : tmpSuccessNum.intValue());
                    falseNum += (tmpFalseNum == null ? 0 : tmpFalseNum.intValue());
                    tmpResult.put("success", successNum);
                    tmpResult.put("false", falseNum);
                }
            }
        }
        for (Map.Entry<String, Map<String, Integer>> tmpAppendBodyMap : mapResult.entrySet()) {
            stringBuilderBody.append(tmpAppendBodyMap.getKey() )
                    .append(" 成功:【").append(tmpAppendBodyMap.getValue().get("success") == null ? "0" : tmpAppendBodyMap.getValue().get("success")).append("】").
                    append(" 失败:【").append(tmpAppendBodyMap.getValue().get("false") == null ? "0" : tmpAppendBodyMap.getValue().get("false")).append("】\n");
        }

        stringBuilder.append("总推送量 成功:【").append(totaladdsuccess).append("】 失败:【").append(totaladderror).append("】\n");

        stringBuilder.append(stringBuilderBody);
        DingTalkClient client = DingTalkUtils.getClient(baiduaccesstoken, baidusecret);
//        DingTalkUtils.sendMessageByMarkdown("百度数据推送日报提醒", stringBuilder.toString(), new ArrayList<>(), true,client);
        DingTalkUtils.sendMessageByText(  stringBuilder.toString(), new ArrayList<>(), true,client);


        return "success";
    }

    /**
     * 抖音每日8万sitemap，补足则补量
     *
     * @param
     */
    public Object douYinSiteMapFillIn() {
        int totaladd = 0;
        int totaladdsuccess = 0;
        int totaladderror = 0;
        List<String> list = new ArrayList<>();
        DouYinBaseReturn douYinBaseReturn = null;
        Map<String, MainDataTypeToBizTypeEnum> map =
                Arrays.stream(MainDataTypeToBizTypeEnum.values()).collect(Collectors.toMap(e -> e.getCode(), e -> e));
        for (Map.Entry<String, MainDataTypeToBizTypeEnum> stringMainDataTypeToBizTypeEnumEntry : map.entrySet()) {
            if (stringMainDataTypeToBizTypeEnumEntry.getKey().equals("0")) {
                continue;
            }
            String addtotal = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 0;
            String addsuccess = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 1;
            String addfalse = DOUYIN_SITEMAP_KEY + DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE) + ":" +
                    stringMainDataTypeToBizTypeEnumEntry.getKey() + ":" + 1 + ":" + 2;
            String addtotalnum = redisClient.get(addtotal);
            String addsuccessnum = redisClient.get(addsuccess);
            String addfalsenum = redisClient.get(addfalse);
            //总量
            totaladd += addtotalnum == null ? 0 : Integer.parseInt(addtotalnum);
            //总新增成功量
            totaladdsuccess += addsuccessnum == null ? 0 : Integer.parseInt(addsuccessnum);
            totaladderror += addfalsenum == null ? 0 : Integer.parseInt(addfalsenum);
        }

        int pushflag = 110;
        int diffNum = 80000 - totaladd;
        log.info("douYinSiteMapReport 当前已经推送情况 应提交数: {},提交成功数：{},提交失败数：{},当日sitemap差额:{}", totaladd, totaladdsuccess, totaladderror,diffNum);
        JobLog.info("当前已经推送情况 应提交数: "+totaladd+"提交成功数："+totaladdsuccess+",提交失败数："+totaladderror+",当日sitemap差额:"+diffNum);
        int totalNum = 0;
        int totalSuccessNum = 0;
        int totalFailNum = 0;
        // 当日sitemap差额大于0 ，则去数据库中获取指定数据进行推送
        if (diffNum > 0) {
//            List<DouYinSiteMapHistoryDto> douyinSitemapHistoryList = historyDouyinSitemapMapper.getDouyinSitemapHistoryList(diffNum);
            List<DouYinSiteMapHistoryDto> douyinSitemapHistoryList = historyDouyinSitemapMapper.getDouyinSitemapHistoryList(diffNum);
            if (douyinSitemapHistoryList != null && douyinSitemapHistoryList.size() > 0) {

                list.addAll(douyinSitemapHistoryList.stream()
                        .map(DouYinSiteMapHistoryDto::getPagepath)
                        .collect(Collectors.toList()));
            }
            //https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/web-microapp/submit-paths 抖音要求单次提交不能超过5MB,条数不超过10万
            douYinBaseReturn = uploadSiteMap(list, pushflag);
            log.info("本次执行历史补足-新增sitemap,推送条数:{},推送结果：{}", list.size(), JacksonHelper.serialize(douYinBaseReturn));
            JobLog.info("本次执行历史补足-新增sitemap,推送条数:"+list.size()+",推送结果："+JacksonHelper.serialize(douYinBaseReturn));

            if (douYinBaseReturn.getErr_no() == 0) {
                totalSuccessNum += list.size();
                //推送成功需要将推送成功的数据状态设置成1（已推送）
                for (DouYinSiteMapHistoryDto douYinSiteMapHistoryDto : douyinSitemapHistoryList) {
                    historyDouyinSitemapMapper.updateById(douYinSiteMapHistoryDto.getId());
                }
            } else {
                totalFailNum += list.size();
            }

            totalNum = totalSuccessNum + totalFailNum;
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,pushflag, 1, 0, totalNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,pushflag, 1, 1, totalSuccessNum);
            addDouYinSiteMapRedis(DOUYIN_SITEMAP_KEY,pushflag, 1, 2, totalFailNum);
        }
        JobLog.info("本次执行历史补足-新增sitemap,推送条数:"+totalNum+",成功："+totalSuccessNum+",失败："+totalFailNum);
        return "success";
    }

    /** 批量保存错误数据,回溯
     *
     * @param pagePaths
     * @param pushFlag
     */
    private void addErrorDataToHistory(List<String> pagePaths, int pushFlag) {
        try {
            if(pagePaths==null||pagePaths.size()==0){
                return;
            }

            List<HistoryDouyinSitemap> list = new ArrayList<>();
            for (String pagePath : pagePaths) {
                HistoryDouyinSitemap historyDouyinSitemap = new HistoryDouyinSitemap();
                historyDouyinSitemap.setCreatedStime(new Date());
                historyDouyinSitemap.setModifiedStime(new Date());
                historyDouyinSitemap.setPagepath(pagePath);
                historyDouyinSitemap.setIsDelete(0);
                historyDouyinSitemap.setStatus(0);
                historyDouyinSitemap.setSource(pushFlag);
                list.add(historyDouyinSitemap);
            }
            historyDouyinSitemapMapper.insertBatch(list);
            log.info("批量历史数据保存成功，本次保存 {} 条",list.size());
        }catch (Exception e){
            log.error("addErrorDataToHistory,批量保存历史数据发生错误,pushFlag={},pagepaths={},error={}",pushFlag,pagePaths,e);
        }
    }
}
