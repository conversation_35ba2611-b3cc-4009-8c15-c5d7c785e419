package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.quickpass.AccessTokenResponse;
import com.autohome.applet.model.dto.quickpass.MobileInfoResponse;

public interface CloudQuickPassService {

    /**
     * 获取BackendToken
     *
     * @return
     */
    String getBackendToken();

    /**
     * 获取AccessToken
     *
     * @param code
     * @param backendToken
     * @return
     */
    AccessTokenResponse.Params getAccessToken(String code, String backendToken);

    /**
     * 获取用户手机号
     *
     * @param accessToken
     * @param backendToken
     * @param openId
     * @return
     */
    MobileInfoResponse.Params getUserPhoneNum(String accessToken, String backendToken, String openId);

    /**
     * 云闪付解密API文档：https://opentools.95516.com/applet/#/docs/develop/demo?id=_020808
     *
     * @param mobile
     * @return
     */
    String decryptMobile(String mobile);

}
