package com.autohome.applet.service.wxapi.impl;

import com.autohome.applet.dao.javaapi.mapper.WxUserViewMapper;
import com.autohome.applet.dao.javaapi.model.wxapp.DetailModel;
import com.autohome.applet.dao.javaapi.model.wxapp.KoubeiDetail;
import com.autohome.applet.dao.javaapi.model.wxapp.KoubeiWxAppDetail;
import com.autohome.applet.dao.javaapi.model.wxapp.UserView;
import com.autohome.applet.dao.javaapi.model.wxapp.topic.CardType;
import com.autohome.applet.dao.javaapi.model.wxapp.topic.ContentCard;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.wxapi.DetailService;
import com.autohome.applet.service.wxapi.NewsService;
import com.autohome.applet.service.wxapi.TopicService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Detail Service
 *
 * <AUTHOR>
 * @date 2024/05/06
 */
@Service
public class DetailServiceImpl implements DetailService {
    private static final String KOUBEI_DETAIL_MAIN_URL = "http://koubei.app.autohome.com.cn/autov7.7.0/alibi/alibiinfobase-pm1-k%d.json";
    private static final String KOUBEI_DETAIL_WXAPP_URL = "http://k.api.autohome.com.cn/api/Evaluation/GetWeixinEvaluation";
    private static final String APPID = "wxapp";
    @Autowired
    private NewsService newsService;
    @Autowired
    private TopicService topicService;
    @Autowired
    private HttpService httpService;

    @Override
    public ReturnValue<DetailModel> detail(int id, int type, int nocache, int isRetainA) {
        //Topic = 0 , Koubei = 1, Article = 2, Video =3
        switch (type) {
            case 0:
                return topicService.topicDetail(id, nocache, isRetainA);
            case 1:
                return getKoubeiDetails(id);
            case 2:
                return newsService.getArticleDetails(id);
            case 3:
                return newsService.getVideoDetails(id);
            default:
                return ReturnValue.buildErrorResult(600, "不支持此最终页类型");
        }
    }

    private ReturnValue<DetailModel> getKoubeiDetails(int kid) {
        List<ContentCard> contentCards = new ArrayList<>();
        List<DetailModel.KoubeiInfo> koubeiInfos = new ArrayList<>();
        KoubeiDetail koubeiDetail = getKoubeiMainInfo(kid, contentCards, koubeiInfos);
        if (koubeiDetail == null) {
            return ReturnValue.buildErrorResult(102, "未能获得到口碑数据");
        }
        DetailModel detail = new DetailModel();
        detail.setContentCards(contentCards);
        detail.setReplyCount(koubeiDetail.getCommentcount());
        detail.setId(kid);
        detail.setHeaderImg(koubeiDetail.getMembericon());
        detail.setName(koubeiDetail.getMembername());
        detail.setMemberId(koubeiDetail.getMemberid());
        detail.setPublishTime(koubeiDetail.getReportdate());
        detail.setLikeCount(koubeiDetail.getHelpfulcount());
        KoubeiWxAppDetail koubeiWxAppDetail = getKoubeiOtherInfo(kid);
        if (koubeiWxAppDetail != null) {
            detail.setTitle(koubeiWxAppDetail.getFeelingsummary());
            koubeiInfos.add(new DetailModel.KoubeiInfo("购买目的:", koubeiWxAppDetail.getPurposeStr()));
        }
        detail.setKoubei(koubeiInfos);

        //return ReturnValue.buildSuccessResult(detail);
        ReturnValue<DetailModel> result = new ReturnValue<>();
        result.setResult(detail);
        return result;
    }

    private KoubeiDetail getKoubeiMainInfo(int kid, List<ContentCard> contentCards, List<DetailModel.KoubeiInfo> koubeiInfos) {
        ReturnValue<KoubeiDetail> response =
                httpService.httpGetFor(String.format(KOUBEI_DETAIL_MAIN_URL, kid), null, new TypeReference<ReturnValue<KoubeiDetail>>() {});
        if (response == null || response.getResult() == null || response.getReturncode() != 0) {
            return null;
        }
        KoubeiDetail kDetail = response.getResult();
        String content = kDetail.getContent();
        //1.组织卡片部分
        if (!Strings.isNullOrEmpty(content)) {
            contentCards.add(new ContentCard(CardType.TEXT.getIndex(), content, "", null));
        }
        KoubeiDetail.PicInfo[] picInfos = kDetail.getPiclist();
        if (picInfos != null && picInfos.length > 0) {
            for (KoubeiDetail.PicInfo picInfo : picInfos) {
                String url = picInfo.getBigurl();
                if (!Strings.isNullOrEmpty(url)) {
                    contentCards.add(new ContentCard(CardType.IMAGE.getIndex(), "", picInfo.getBigurl(), null));
                }
            }
        }
        //2.组织口碑打分(评价)部分
        koubeiInfos.add(new DetailModel.KoubeiInfo("评价车型:",
                String.format("%s%s%s", kDetail.getBrandname(), kDetail.getSeriesname(), kDetail.getSpecname())));
        KoubeiDetail.CarInfo carInfo = kDetail.getCarinfo();
        if (carInfo != null) {
            boolean isbattery = carInfo.getIsbattery() == 1;
            koubeiInfos.add(new DetailModel.KoubeiInfo("裸车购买价格:", String.format("%.2f万元", carInfo.getBoughtprice())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("购买时间:", carInfo.getBoughtdate()));
            koubeiInfos.add(new DetailModel.KoubeiInfo("购买地点:", carInfo.getBoughtaddress()));
            koubeiInfos.add(new DetailModel.KoubeiInfo("外观:", String.format("%s分", carInfo.getApperance())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("内饰:", String.format("%s分", carInfo.getInternals())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("舒适性:", String.format("%s分", carInfo.getComfortabelness())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("空间:", String.format("%s分", carInfo.getSpace())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("动力:", String.format("%s分", carInfo.getPower())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("操控:", String.format("%s分", carInfo.getManeuverability())));
            koubeiInfos.add(new DetailModel.KoubeiInfo(isbattery ? "电耗:" : "油耗:", String.format("%s分", carInfo.getConsumptionscore())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("性价比:", String.format("%s分", carInfo.getCostefficient())));
            //koubeiInfos.add(new DetailModel.KoubeiInfo("车型满意度(平均分):",String.format("%s分",carInfo.getInternals())));//无此项
            koubeiInfos.add(new DetailModel.KoubeiInfo(isbattery ? "使用电耗:" : "使用油耗:",
                    String.format(isbattery ? "%s千瓦时/百公里" : "%s升/百公里", carInfo.getActualoilcomsumption())));
            koubeiInfos.add(new DetailModel.KoubeiInfo("目前行驶:", String.format("%s公里", carInfo.getDrivenkiloms())));
            //koubeiInfos.add(new DetailModel.KoubeiInfo("购买目的:",String.format("")));//购买目的在另外一个接口取
        }
        return kDetail;
    }

    private KoubeiWxAppDetail getKoubeiOtherInfo(int kid) {
        Map<String, Object> params = new HashMap<>(2);
        //http://k.api.autohome.com.cn/api/Evaluation/GetWeixinEvaluation?_appid=koubei&eid=230758
        params.put("_appid", APPID);
        params.put("eid", kid + "");
        ReturnValue<KoubeiWxAppDetail> response =
                httpService.httpGetFor(KOUBEI_DETAIL_WXAPP_URL, params, new TypeReference<ReturnValue<KoubeiWxAppDetail>>() {});
        if (response == null) {
            return null;
        }
        return response.getResult();
    }
}
