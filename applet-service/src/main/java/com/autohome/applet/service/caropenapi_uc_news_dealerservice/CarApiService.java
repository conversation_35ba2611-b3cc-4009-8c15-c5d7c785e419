package com.autohome.applet.service.caropenapi_uc_news_dealerservice;

import com.autohome.applet.model.dto.car.SpecBaseInfoBySpecListDTO;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.autoopen.SpecWhiteLogo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarSeriesList;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.FctSeries;

import java.util.List;

public interface CarApiService {
    CarSeriesList getBaseInfoBySeriesList(String seriesids);

    ResponseContent<CarSeriesList> getBaseInfoBySeriesListResponse(String seriesids);

    ResponseContent<?> series_classpicturebyseriesId(String _appid, int seriesid);

    FctSeries getSeriesByFctId(String _appid, String state, int fctid, int size);

    FctSeries getSeriesByBrandIdAndFctId(String _appid, String state, int fctid, int size, int brandid);

    ResponseContent<?> getPictureListsByCondition(String _appid, int seriesid, int classid, int pageindex, int imageid, int size);

    //HashMap inner_series_menubysearch(String _appid, String state, int fctid, int brandId);

    ResponseContent<?> series_menubysearch(String _appid, String state, int fctid, int brandid);

    ResponseContent<?> getSpecParamAndConfigByspeclist(String _appid, String speclist);

    ResponseContent<?> getHotsSpecByFctId(String _appid, String state, int fctid);

    SpecWhiteLogo getSpecWhiteLogoBySpeclist(String speclist);

    /**
     * 根据多个车型id列表获取车型相关基本信息
     * wiki: https://zhishi.autohome.com.cn/home/<USER>/file?targetId=27231572
     * @param speclist 车型id，逗号分隔 最多50个车型id
     * @return
     */
    List<SpecBaseInfoBySpecListDTO> SpecBaseInfbySpecList(String speclist);

    ResponseContent<?> series_classpicturebyseriesIdRetAll(String _appid, int seriesid);

}
