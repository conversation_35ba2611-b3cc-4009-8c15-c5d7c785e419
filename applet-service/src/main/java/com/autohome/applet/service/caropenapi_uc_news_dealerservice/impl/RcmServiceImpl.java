package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.alibaba.fastjson.JSON;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.AutoSmallVideo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.FctSeries;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.rcm.RcmDataOpenResponse;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.RcmService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.IPUtils;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RcmServiceImpl implements RcmService {

    @Autowired
    private CarApiService carApiService;

    /**
     * 根据车系id推荐小视频
     *
     * @param _appid
     * @param deviceid
     * @param operation
     * @param netstate
     * @param gps
     * @param userip
     * @param version
     * @param outmedia
     * @param fctid
     * @return
     */
    @Override
    public List<HashMap> autosmallvideoByFctIdNew(String _appid, String deviceid, String operation, String netstate, String gps,
                                                  String userip, String version, String outmedia, int number, int fctid, int seriesid, HttpServletRequest request) {
        try {
            String seriesids = "";
            if (seriesid == 0) {
                FctSeries fctSeries = carApiService.getSeriesByFctId(_appid, null, fctid, 50);
                if (fctSeries != null) {
                    seriesids = fctSeries.getSeriesids();
                }
            } else {
                seriesids = Integer.toString(seriesid);
            }
            ResponseContent<RcmDataOpenResponse> rcmContent = smallvideorcmland("400025", null, deviceid, version, null, null, "0", netstate,
                    null, null, null, Integer.toString(number), null, null, null, outmedia, seriesids, request);
            if (rcmContent == null || rcmContent.getResult() == null) {
                return null;
            }
            return rcmContent.getResult().getItemlist();
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("autosmallvideo-WithException", e.getMessage())));
            log.error("autosmallvideoByFctIdNew error", e);
            return null;
        }
    }

    public ResponseContent<RcmDataOpenResponse> smallvideorcmland(String pid, String userid, String uuid, String version, String devicetype, String source, String operation,
                                                                  String netstate, String bsdata, String appversion, String requestid, String number, String ext,
                                                                  String debug, String include, String outmedia, String seriesids, HttpServletRequest request) {
        try {
//            String gongyao = "uc-news-quickappservice";
//            String siyao = "8sOG24^gr^";
//            Base64 base64 = new Base64();
//            StopWatch watch = new StopWatch();
//            watch.start("smallvideorcmlandparam");
            HashMap<String, Object> headparam = new HashMap<>();
            //测试环境秘钥
            headparam.put("Authorization", "Basic" + Base64.getUrlEncoder().encodeToString("uc-news-dealerservice:L7m**r@mJY".getBytes()));
//            headparam.put("Authorization", "Basic" + Base64.getUrlEncoder().encodeToString("uc-news-dealerservice:L7m**r@mJY".getBytes()));

            HashMap<String, Object> param = new HashMap<>();
            param.put("pid", pid);
            if (StringUtil.isNotNullAndWhiteSpace(userid))
                param.put("userid", userid);

            param.put("uuid", uuid);
            param.put("version", version);
            if (StringUtil.isNotNullAndWhiteSpace(devicetype))
                param.put("devicetype", devicetype);
            if (StringUtil.isNotNullAndWhiteSpace(source))
                param.put("source", source);
            param.put("operation", operation);
            if (StringUtil.isNotNullAndWhiteSpace(netstate))
                param.put("netstate", netstate);
            if (StringUtil.isNotNullAndWhiteSpace(bsdata))
                param.put("bsdata", bsdata);
            if (StringUtil.isNotNullAndWhiteSpace(appversion))
                param.put("appversion", appversion);
            if (StringUtil.isNotNullAndWhiteSpace(requestid))
                param.put("requestid", requestid);
            if (StringUtil.isNotNullAndWhiteSpace(number))
                param.put("number", number);

            Map<String, Object> extparam = new HashMap<>();
            if (StringUtil.isNotNullAndWhiteSpace(outmedia))
                extparam.put("outmedia", outmedia);
            if (StringUtil.isNotNullAndWhiteSpace(debug))
                extparam.put("debug", debug);
            if (StringUtil.isNotNullAndWhiteSpace(include))
                extparam.put("include", include);
            if (StringUtil.isNotNullAndWhiteSpace(seriesids))
                extparam.put("seriesids", seriesids);

            String userip = IPUtils.getRemoteIp(request);

//            extparam.put("include", Integer.parseInt(include));
//            if (StringUtils.isNotNullAndWhiteSpace(ext))
//                param.put("ext", JSON.toJSONString(extparam).replace("\"","%22").replace(":","%3a"));
            param.put("ext", URLEncoder.encode(JSON.toJSONString(extparam)));


            //首页智能推荐
            //String result = HttpHelper.getResponseForRcm("http://data.in.corpautohome.com/oneapi/v2", headparam, param, null, 1000, 0).getContent();
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://data.in.corpautohome.com/oneapi/v2", param, headparam);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            } else {
                //ResponseContent<RcmDataOpenResponse> responseSpecContent = JSON.parseObject(result, new TypeReference<ResponseContent<RcmDataOpenResponse>>() {
                return JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<RcmDataOpenResponse>>() {
                });
            }

        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("rcmland-WithException", e.getMessage())));
            log.error("smallvideorcmland error", e);
            return null;
        }
    }

    /**
     * 根据车系id推荐小视频
     *
     * @param _appid
     * @param deviceid
     * @param operation
     * @param netstate
     * @param gps
     * @param userip
     * @param version
     * @param outmedia
     * @param seriesids
     * @return
     */
    @Override
    public ResponseContent<?> autosmallvideo(String _appid, String deviceid, String operation, String netstate, String gps,
                                             String userip, String version, String outmedia, String seriesids, int number) {
        try {

            Map<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("deviceid", deviceid);
            param.put("operation", operation);
            if (StringUtil.isNotNullAndWhiteSpace(netstate)) {
                param.put("netstate", netstate);
            }
            if (StringUtil.isNotNullAndWhiteSpace(gps)) {
                param.put("gps", gps);
            }
            param.put("userip", userip);
            param.put("version", version);
            param.put("outmedia", outmedia);
            param.put("seriesids", seriesids);
            param.put("number", Integer.toString(number));
//            String result = HttpHelper.getResponse(autosmallvideoURL, param, null, 3000).getContent();
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(0, "暂无数据", new HashMap<>());
//            }
//            ResponseContent<HashMap> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<HashMap>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://rcm.autohome.com.cn/autosmallvideo/lands", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(0, "暂无数据", new HashMap());
            }
            ResponseContent<HashMap> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<HashMap>>() {
            });

            if (responseContent == null || responseContent.getResult() == null) {
                return new ResponseContent<>(0, "暂无数据", new HashMap());
            }
            if (responseContent.getResult().get("list") == null) {
                responseContent.setMessage("暂无更多小视频");
            }
            return responseContent;
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("autosmallvideo-WithException", e.getMessage())));
            log.error("autosmallvideo error", e);
            return new ResponseContent<>(500, "autosmallvideo-WithException" + e.getMessage(), new HashMap());
        }
    }

    /**
     * 根据车系id推荐小视频
     *
     * @param _appid
     * @param deviceid
     * @param operation
     * @param netstate
     * @param gps
     * @param userip
     * @param version
     * @param outmedia
     * @param fctid
     * @return
     */
    public AutoSmallVideo autosmallvideoByFctId(String _appid, String deviceid, String operation, String netstate, String gps,
                                                String userip, String version, String outmedia, int number, int fctid, int seriesid) {
        try {
            String seriesids = "";
            if (seriesid == 0) {
                FctSeries fctSeries = carApiService.getSeriesByFctId(_appid, null, fctid, 50);
                if (fctSeries != null) {
                    seriesids = fctSeries.getSeriesids();
                }
            } else {
                seriesids = Integer.toString(seriesid);
            }
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("deviceid", deviceid);
            param.put("operation", operation);
            if (StringUtil.isNotNullAndWhiteSpace(netstate)) {
                param.put("netstate", netstate);
            }
            if (StringUtil.isNotNullAndWhiteSpace(gps)) {
                param.put("gps", gps);
            }
            param.put("userip", userip);
            param.put("version", version);
            param.put("outmedia", outmedia);
            param.put("seriesids", seriesids);
            param.put("number", Integer.toString(number));
//            String result = HttpHelper.getResponse(autosmallvideoURL, param, null, 3000).getContent();
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return null;
//            }
//            ResponseContent<AutoSmallVideo> responseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<AutoSmallVideo>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://rcm.autohome.com.cn/autosmallvideo/lands", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<AutoSmallVideo> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<AutoSmallVideo>>() {
            });
            if (responseContent == null || responseContent.getResult() == null) {
                return null;
            }
            return responseContent.getResult();
        } catch (Exception e) {
//            logger.error(JSON.toJSONString(new LogFormat("autosmallvideo-WithException", e.getMessage())));
            log.error("autosmallvideoByFctId error", e);
            return null;
        }
    }


    /**
     * 根据车系id推荐小视频
     *
     * @param _appid
     * @param deviceid
     * @param operation
     * @param netstate
     * @param gps
     * @param userip
     * @param version
     * @param outmedia
     * @param seriesids
     * @return
     */
    @Override
    public ResponseContent<RcmDataOpenResponse> autosmallvideoNew(String _appid, String deviceid, String operation, String netstate, String gps,
                                                                  String userip, String version, String outmedia, String seriesids, int number, HttpServletRequest request) {
        try {

            return smallvideorcmland("400025", null, deviceid, version, null, null, "0", netstate,
                    null, null, null, Integer.toString(number), null, null, null, outmedia, seriesids, request);
        } catch (Exception e) {
            //logger.error(JSON.toJSONString(new LogFormat("autosmallvideo-WithException", e.getMessage())));
            log.error("autosmallvideo-WithException", e);
            return new ResponseContent<>(500, "根据车系id推荐小视频异常" + e.getMessage());
        }
    }
}
