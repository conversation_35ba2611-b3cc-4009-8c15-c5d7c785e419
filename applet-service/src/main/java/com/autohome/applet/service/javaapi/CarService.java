package com.autohome.applet.service.javaapi;

import com.autohome.applet.dao.javaapi.model.EnterpriseGroup;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.car.*;
import com.autohome.applet.model.dto.car.portrayal.CompetingSeries;
import com.autohome.applet.model.dto.car.portrayal.Upb2Result;
import com.autohome.applet.model.dto.carlibrary.BrandNameDTO;
import com.autohome.applet.model.dto.carlibrary.carapi.out.BrandOut;
import com.autohome.applet.model.dto.carlibrary.carapi.out.CarApiBrandOut;
import com.autohome.applet.model.dto.carlibrary.carapi.out.RankListpageResult;
import com.autohome.applet.model.dto.dealer.CustomDealer;
import com.autohome.applet.model.dto.dealer.SeriesMinPriceExtendsResult;
import com.autohome.applet.model.lightapp.RecommendSeriesInfoDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CarService {

    Upb2Result getUpb2(String uuid, String appKey, String userid);

    List<Integer> getHotCar(String type, Integer limit, String  cityId, Boolean newenergy);

    List<CarApiBrandOut> getBrandInfos(List<Integer> brandIds);

    void cacheSeriesAllInfo();

    /**
     * wiki: https://doc.autohome.com.cn/docapi/page/share/share_veKXQXLTk0
     * 获取选车卡 可询价车系信息
     * 范围：选车页-推荐车接口
     * 具体逻辑：
     *  车系个数：2
     * 取车系逻辑：
     * 排序：用户偏好车系下（在售车）>热门车系（在售车）
     * 车系状态：
     *  可询价
     *  有成交价(车型)
     *  有降价（车型）
     *  车系其余需展示字段
     *  车系图片
     *  车系名称
     * 车型成交价：需透传至询价弹窗数值一致
     * 车型降价：需透传至询价弹窗，数值一致
     * 车型ID：需透传至询价弹窗，数值一致
     *
     *
     * type=series --获取热门车系； type=brand --获取热门品牌
     * */
    List<SeriesMinPriceExtendsResult> getCarSelectionCardMinPriceExtendsResult(String uuid, String appkey, Integer userid, String type, Integer cityid);


    RankListpageResult getrecranklistpageresult2();

    RankListpageResult getrecranklistpageresult2V2(String levelIds, int pageSize);

    RankListpageResult getrecranklistpageresult2_V2(int pageIndex, int pageSize, int typeid, String subranktypeid);
    /**
     * 通过品牌id查询品牌名称
     * wiki:https://zhishi.autohome.com.cn/home/<USER>/file?targetId=28743941
     * @param brandId
     * @return
     */
    BrandNameDTO getBrandNameDTO(int brandId);
}
