package com.autohome.applet.service.dealer.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.dealer.SpecMinPrice;
import com.autohome.applet.service.dealer.DealerNewService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.MapBuilder;
import com.autohome.applet.util.UUIDUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class DealerNewServiceImpl implements DealerNewService {
    @Override
    public ReturnValue<?> getDealers(HttpServletRequest request) {
        final String url = "https://dealer.m.autohome.com.cn/handler/v1/GetDataForApp?__action=dealerlq.quoteprice-getdealers";
        return getReturnValue(request, url);
    }

    @Override
    public ReturnValue<?> getQuoteDealer(HttpServletRequest request) {
        final String url = "https://dealer.m.autohome.com.cn/handler/v1/GetDataForApp?__action=dealerlq.quoteprice-getquotedealer";

        return postReturnValue(request, url);
    }

    @Override
    public ReturnValue<?> getMinSeriesDealPriceFormat(HttpServletRequest request) {
        final String url = "https://dealer.m.autohome.com.cn/handler/v1/GetDataForApp?__action=dealerlq.seriesprice-getminseriesdealpriceformat";        return getReturnValue(request, url);
    }

    @Override
    public ReturnValue<?> getMinPrice(Integer seriesId, Integer cityId) {
        String seriesMinPriceUrl = "http://apigateway.corpautohome.com/jxsjs/ics/yhz/dealerlq/v1/statistics/seriesprice/getSeriesMinPrice";
        Map<String, Object> fields = MapBuilder.<String, Object>newInstance().put("seriesid", seriesId)
                .put("_appid", "app")
                .put("cityid", cityId)
                .build();
        Map<String, Object> header = MapBuilder.<String, Object>newInstance()
                .put("Authorization", "Basic bHRhbXVHTVUySFF0OllRODNVaHFOZVVOUg==")
                .build();
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(seriesMinPriceUrl, fields, header);
        ReturnValue<JsonNode> returnValue = JacksonHelper.deserialize(httpResult.getBody(),
                new TypeReference<ReturnValue<JsonNode>>() {
                });
        if (returnValue.getReturncode() != 0 || returnValue.getResult() == null) {
            log.error(String.format("getMinPrice %s is error result: %s", seriesMinPriceUrl, JacksonHelper.serialize(httpResult)));
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }
        return returnValue;
    }

    @Override
    public SpecMinPrice getMinPriceDTO(Integer seriesId, Integer cityId) {
        String seriesMinPriceUrl = "http://dealer.api.lq.autohome.com.cn/statistics/seriesprice/getSeriesMinPrice";
        Map<String, Object> fields = MapBuilder.<String, Object>newInstance().put("seriesid", seriesId)
                .put("_appid", "app")
                .put("cityid", cityId)
                .build();
        Map<String, Object> header = MapBuilder.<String, Object>newInstance()
                .put("Authorization", "Basic bHRhbXVHTVUySFF0OllRODNVaHFOZVVOUg==")
                .build();
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(seriesMinPriceUrl, fields, header);
        ReturnValue<SpecMinPrice> returnValue = JacksonHelper.deserialize(httpResult.getBody(),
                new TypeReference<ReturnValue<SpecMinPrice>>() {
                });
        if (httpResult == null || returnValue.getReturncode() != 0 || returnValue.getResult() == null) {
            log.error(String.format("getMinPrice %s is error result: %s", seriesMinPriceUrl, JacksonHelper.serialize(httpResult)));
            return null;
        }
        return returnValue.getResult();
    }

    @Override
    public ReturnValue<?> getListSmartAreaButton(Integer cityId, String specId, Integer seriesId, Integer areaId,
                                                 String deviceId, String deviceType, String appVersion,
                                                 String traceType) {
        String url = "http://dealer.api.lq.autohome.com.cn/statistics/text/listSmartAreaButton";
        String uuid = UUIDUtil.getUUID();

        Map<String, Object> fields = MapBuilder.<String, Object>newInstance()
                .put("specId", specId)
                .put("_appid", "lightapp")
                .put("cityid", cityId)
                .put("areaId",areaId)
                .put("pvid",uuid)
                .put("appVersion",appVersion)
                .put("deviceType",deviceType)
                .put("deviceId",deviceId)
                .put("traceType",traceType)
                .put("seriesId",seriesId)
                .build();

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, fields);

        if (httpResult == null || httpResult.getStatusCode() != 200) {
            log.error("getListSmartAreaButton {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            return ReturnValue.buildErrorResult(1001,"源接口请求失败");
        }

        if (StringUtils.isEmpty(httpResult.getBody())) {
            log.warn("getListSmartAreaButton 源接口返回为null , url:{} , param:{}",url,JacksonHelper.serialize(fields));
            return null;
        }

        ReturnValue<JsonNode> returnValue = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<JsonNode>>() {});

        if (returnValue.getReturncode() != 0){
            log.error("getListSmartAreaButton {} is error result: {}",url, JacksonHelper.serialize(httpResult));
            return ReturnValue.buildErrorResult(1001,"源接口请求失败");
        }

        return returnValue;
    }

    private ReturnValue<?> getReturnValue(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, params);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<?> resp = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        return resp;
    }

    private ReturnValue<?> postReturnValue(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }


        String paramJson = JacksonHelper.serialize(params);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(url, paramJson);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<?> resp = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        return resp;
    }
}
