package com.autohome.applet.service;

import java.util.Map;

import com.autohome.applet.model.dto.CodeMsg;
import com.autohome.applet.model.dto.ReturnValue;
import com.fasterxml.jackson.core.type.TypeReference;

import javax.servlet.http.HttpServletRequest;

public interface HttpService {

    Map<String, Object> getParam(HttpServletRequest request);

    String httpGet(String url, Map<String, Object> param);

    String httpGet(String url, Map<String, Object> param, Map<String, Object> header);

    String httpGet(String url, Map<String, Object> param, Map<String, Object> header, long timeout);

    <T> T httpGetFor(String url, Map<String, Object> param, TypeReference<T> typeReference);

    <T> T httpGetFor(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<T> typeReference);

    <T> T httpGetForReturnValue(String url, Map<String, Object> param, TypeReference<ReturnValue<T>> typeReference);

    <T> T httpGetForReturnValue(String url, Map<String, Object> param, TypeReference<ReturnValue<T>> typeReference, long timeout);

    <T> T httpGetForReturnValue(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference);

    <T> T httpGetForReturnValue(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference, long timeout);

    <T> T httpGetForCodeMsg(String url, Map<String, Object> param, TypeReference<CodeMsg<T>> typeReference);
    <T> T httpGetForCodeMsg(String url, Map<String, Object> param, TypeReference<CodeMsg<T>> typeReference, long timeout);

    <T> T httpGetForCodeMsg(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<CodeMsg<T>> typeReference);

    <T> T httpGetForCodeMsg(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<CodeMsg<T>> typeReference, long timeout);

    <T> T httpGetForCodeMsgData(String url, Map<String, Object> param, TypeReference<CodeMsg<T>> typeReference);

    <T> T httpGetForCodeMsgData(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<CodeMsg<T>> typeReference);

    String httpPost(String url, Map<String, Object> param);

    String httpPost(String url, Map<String, Object> param, Map<String, Object> header);

    <T> T httpPostFor(String url, Map<String, Object> param, TypeReference<T> typeReference);

    <T> T httpPostFor(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<T> typeReference);

    <T> T httpPostForReturnValue(String url, Map<String, Object> param, TypeReference<ReturnValue<T>> typeReference);

    <T> T httpPostForReturnValue(String url, Map<String, Object> param, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference);

    String httpPostJson(String url, String body);

    String httpPostJson(String url, String body, Map<String, Object> header);

    <T> T httpPostJsonFor(String url, String body, TypeReference<T> typeReference);

    <T> T httpPostJsonFor(String url, String body, Map<String, Object> header, TypeReference<T> typeReference);

    <T> T httpPostJsonForReturnValue(String url, String body, TypeReference<ReturnValue<T>> typeReference);

    <T> T httpPostJsonForReturnValue(String url, String body, Map<String, Object> header, TypeReference<ReturnValue<T>> typeReference);
}