package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.config.QuickPassProperties;
import com.autohome.applet.model.dto.quickpass.AccessTokenResponse;
import com.autohome.applet.model.dto.quickpass.BackendTokenResponse;
import com.autohome.applet.model.dto.quickpass.MobileInfoResponse;
import com.autohome.applet.service.javaapi.CloudQuickPassService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.SignUtil;
import com.autonews.springboot.util.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CloudQuickPassServiceImpl implements CloudQuickPassService {

    private static final String GET_BACKEND_TOKEN_URL = "https://open.95516.com/open/access/1.0/backendToken";
    private static final String GET_ACCESS_TOKEN_URL = "https://open.95516.com/open/access/1.0/token";
    private static final String GET_PHONE_NUM_URL = "https://open.95516.com/open/access/1.0/user.mobile";

    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;

    @Autowired
    private QuickPassProperties quickPassProperties;

    @Override
    public String getBackendToken() {
        Map param = new HashMap<>();
        param.put("appId", quickPassProperties.getAppid());
        param.put("nonceStr", SignUtil.createNonceStr());
        param.put("timestamp", Long.toString(System.currentTimeMillis() / 1000L));
        param.put("secret", quickPassProperties.getSecret());
        param.put("signature", SignUtil.createQuickPassSign(param));

        String paramJson = JacksonHelper.serialize(param);
        log.info("request BackendToken url params:{}", paramJson);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(GET_BACKEND_TOKEN_URL, paramJson);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            log.info("request BackendToken url response:{}", httpResult.getBody());
            BackendTokenResponse returnValue = JacksonHelper.deserialize(httpResult.getBody(), BackendTokenResponse.class);
            if (returnValue != null && returnValue.getParams() != null) {
                return returnValue.getParams().getBackendToken();
            } else {
                log.info("request BackendToken url failed, result is null, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.info("request BackendToken url failed, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public AccessTokenResponse.Params getAccessToken(String code, String backendToken) {
        Map param = new HashMap<>();
        param.put("appId", quickPassProperties.getAppid());
        param.put("code", code);
        param.put("backendToken", backendToken);
        param.put("grantType", "authorization_code");

        String paramJson = JacksonHelper.serialize(param);
        log.info("request AccessToken url params:{}", paramJson);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(GET_ACCESS_TOKEN_URL, paramJson);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            log.info("request AccessToken url response:{}", httpResult.getBody());
            AccessTokenResponse returnValue = JacksonHelper.deserialize(httpResult.getBody(), AccessTokenResponse.class);
            if (returnValue.getParams() != null && "成功".equalsIgnoreCase(returnValue.getMsg()) && "scope.mobile".equalsIgnoreCase(returnValue.getParams().getScope())) {
                return returnValue.getParams();
            } else {
                if ("不合法的授权code，或已过期".equals(returnValue.getMsg())) {
                    redisClient.setValue("applet:api:getAccessToken:code:" + code, "-1", 30, TimeUnit.DAYS);
                }
                log.warn("request AccessToken url failed, result is null, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request AccessToken url failed, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public MobileInfoResponse.Params getUserPhoneNum(String accessToken, String backendToken, String openId) {
        Map param = new HashMap<>();
        param.put("appId", quickPassProperties.getAppid());
        param.put("accessToken", accessToken);
        param.put("backendToken", backendToken);
        param.put("openId", openId);

        String paramJson = JacksonHelper.serialize(param);
        log.info("request UserPhoneNum url params:{}", paramJson);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(GET_PHONE_NUM_URL, paramJson);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            log.info("request UserPhoneNum url response:{}", httpResult.getBody());
            MobileInfoResponse returnValue = JacksonHelper.deserialize(httpResult.getBody(), MobileInfoResponse.class);
            if (returnValue.getParams() != null && "成功".equalsIgnoreCase(returnValue.getMsg())) {
                return returnValue.getParams();
            } else {
                log.warn("request UserPhoneNum url failed, result is null, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request UserPhoneNum url failed, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
        }
        return null;
    }


    @Override
    public String decryptMobile(String mobile) {
        String decodeSecret = quickPassProperties.getDecodeSecret();
        try {
            return getDecryptedValue(mobile, decodeSecret);
        } catch (Exception e) {
            log.error("云闪付获取用户手机号解密失败", e);
        }
        return "";
    }

    /**
     * 解密
     *
     * @param value 待解密的字符串
     * @param key   解密秘钥
     * @return 解密后字符串
     * @throws Exception
     */
    public static String getDecryptedValue(String value, String key) throws Exception {
        if (null == value || "".equals(value)) {
            return "";
        }
        byte[] valueByte = Base64.decodeBase64(value);
        byte[] sl = decrypt3DES(valueByte, hexStr2Bytes(key));
        String result = new String(sl);
        return result;
    }

    public static byte[] hexStr2Bytes(String src) {
        /*对输入值进行规范化整理*/
        src = src.trim().replace(" ", "").toUpperCase(Locale.US);
        //处理值初始化
        int m = 0, n = 0;
        int iLen = src.length() / 2; //计算长度
        byte[] ret = new byte[iLen]; //分配存储空间

        for (int i = 0; i < iLen; i++) {
            m = i * 2 + 1;
            n = m + 1;
            ret[i] = (byte) (Integer.decode("0x" + src.substring(i * 2, m) + src.substring(m, n)) & 0xFF);
        }
        return ret;
    }

    /**
     * 3DES解密
     *
     * @param input
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decrypt3DES(byte[] input, byte[] key) throws Exception {
        Cipher c = Cipher.getInstance("DESede/ECB/PKCS5Padding");
        c.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "DESede"));
        return c.doFinal(input);
    }

}
