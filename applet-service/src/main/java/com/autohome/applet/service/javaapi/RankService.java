package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.car.SeriesBaseDto;
import com.autohome.applet.model.dto.rank.*;

import java.util.List;
import java.util.Map;

public interface RankService {

    Map<?, ?> motoRank(
            String levelId,
            Integer sortId,
            String price,
            int pageIndex,
            int pageSize
    );

    List<RankHotSeriesLevelInfo.RankHotSeriesInfo> seriesHotRank(
            Integer areaType,
            Integer cityId,
            String factoryType,
            Integer minPrice,
            Integer maxPrice,
            Integer isnewenergy,
            String level,
            int pageIndex,
            int pageSize
    );

    List<PromotionSeriesInfo> seriesPromotionRank(
            Integer cityId,
            Integer minPrice,
            Integer maxPrice,
            String seriesLevel,
            String fuelTypeDetail,
            int pageindex,
            int pageSize
    );

    List<SeriesScoreRank> seriesScoreRank(
            Integer minPrice,
            Integer maxPrice,
            String levelIds,
            String place,
            Integer orderBy,
            Integer isNewEnergy,
            int pageIndex,
            int pageSize
    );

    SeriesSaleRank seriesSaleRank(
            int brandId, int maxPrice,
            int minPrice,
            String menuTypes,
            String month,
            String levels,
            boolean flag,
            int isNewEnergy,
            int pageIndex,
            int pageSize
    );

    SmartData.Result smartRank();

    SmartDataDetail smartRankDetail(
            int index5Id,
            int pageIndex,
            int pageSize);

    List<SeriesMileageInfo> seriesMileageRank(int fuelType, int pageIndex, int pageSize);

    void cacheSeriesInfo();

    List<SeriesBaseDto> getSeriesBaseDtoCache(List<String> seriesIds);
}
