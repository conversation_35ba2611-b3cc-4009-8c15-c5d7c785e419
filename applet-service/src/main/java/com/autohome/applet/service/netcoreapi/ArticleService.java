package com.autohome.applet.service.netcoreapi;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.netcoreapi.articleservice.ActionSweetClientRank;
import com.autohome.applet.model.dto.netcoreapi.articleservice.ActionSweetClientSlist;
import com.autohome.applet.service.netcoreapi.impl.ArticleServiceImpl;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface ArticleService {

    List<ActionSweetClientSlist> GetActionSweetClient(String cid, int clientType, int status, String rid, int cityid, int province);

    List<ActionSweetClientRank> GetActivityList(int cityId);

    ReturnValue<?> GetArticleDetailById(int articleId, int firstIndex, int pagecurrt, int type) throws ExecutionException, InterruptedException;

    ReturnValue<?> GetJiageDeitle(int priceid);

    List<ArticleServiceImpl.FocusLightForList> ActionSweetClientCombineNews0518(String cid, int clientType, int status,
                                                                                String sourcetype, String opuserid, String Userip,
                                                                                String version, int province, int city);

}
