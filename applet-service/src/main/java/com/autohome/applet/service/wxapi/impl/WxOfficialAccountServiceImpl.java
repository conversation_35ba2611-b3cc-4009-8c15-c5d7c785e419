package com.autohome.applet.service.wxapi.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.autohome.applet.model.dto.wechat.WeixinOfficialAccountToken;
import com.autohome.applet.service.wxapi.WxOfficialAccountService;
import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.HttpClientUtils;
import com.autonews.comm.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * crated by biyukun 2024/6/3 16:42
 */
@Service
public class WxOfficialAccountServiceImpl implements WxOfficialAccountService {

    @Value("${wx.officialAccountTokenUrl:http://x.api.corpautohome.com/ltam/mp/getAccessToken}")
    private String getTokenUrl;

    public String getToken() {
        String result = HttpClientUtils.get(getTokenUrl);
        if (StringUtils.isNullOrEmpty(result)) {
            return null;
        }
        BaseModel<WeixinOfficialAccountToken> accountTokenBaseModel = JSON.parseObject(result, new TypeReference<BaseModel<WeixinOfficialAccountToken>>() {});
        if (accountTokenBaseModel == null || accountTokenBaseModel.getResult() == null || StringUtils.isNullOrEmpty(accountTokenBaseModel.getResult().getAccessToken())) {
            return null;
        }
        return accountTokenBaseModel.getResult().getAccessToken();
    }

}
