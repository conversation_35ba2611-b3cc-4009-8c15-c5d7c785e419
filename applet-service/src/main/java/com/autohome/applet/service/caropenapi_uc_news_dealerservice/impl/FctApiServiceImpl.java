package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.mapper.autoopen.OpenModuleRepository;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.FctActivity;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.OpenInfo;
import com.autohome.applet.dao.caropenapi_uc_news_dealerservice.model.OpenModule;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarVrInfo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat.BusinessInfo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.*;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.*;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.FormatDate;
import com.autohome.applet.util.netcoreapi.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by hanshanfeng on 2019/4/26.
 */
@Slf4j
@Service
public class FctApiServiceImpl implements FctApiService {

    @Autowired
    private OpenModuleRepository openModuleRepository;

    @Autowired
    private CarApiService carApiService;

    @Autowired
    private PanoApiService panoApiService;

    @Autowired
    private RcmService rcmService;

    @Autowired
    private ChatService chatService;

    /**
     * 获取厂商首页模块
     *
     * @param fctid
     * @return
     */

    @Override
    public ResponseContent<?> getOpenModuleByFctIdNew(String _appid, int fctid, String userip, String deviceid, String gps, String netstate, int brandid, HttpServletRequest request) {
        try {
            CompletableFuture<List<OpenModule>> openModuleFuture = CompletableFuture.supplyAsync(new Supplier<List<OpenModule>>() {
                @Override
                public List<OpenModule> get() {
                    return openModuleRepository.getOpenMouduleByDealerId(fctid, 1);
                }
            });

            //热门车系
            CompletableFuture<List<Seriesitems>> seriesFuture = CompletableFuture.supplyAsync(new Supplier<List<Seriesitems>>() {
                @Override
                public List<Seriesitems> get() {
                    return getRecommendSeries(_appid, fctid, brandid);
                }
            });
            //车主小视频
            CompletableFuture<List<HashMap>> autoSmallVideoCompletableFuture = CompletableFuture.supplyAsync(new Supplier<List<HashMap>>() {
                @Override
                public List<HashMap> get() {
                    return rcmService.autosmallvideoByFctIdNew(_appid, deviceid, "0", netstate, gps, userip, "1", "134217728", 5, fctid, 0, request);
                }
            });
            //精彩活动
            CompletableFuture<List<FctActivity>> fctActivityFuture = CompletableFuture.supplyAsync(new Supplier<List<FctActivity>>() {
                @Override
                public List<FctActivity> get() {
                    return getFctActivity(fctid);
                }
            });
            //车友圈
            CompletableFuture<List<BusinessInfo>> businessFuture = CompletableFuture.supplyAsync(new Supplier<List<BusinessInfo>>() {
                @Override
                public List<BusinessInfo> get() {
                    return chatService.getHomeChatsByFctId(fctid, null, _appid);
                }
            });
            //新闻资讯
            CompletableFuture<List<FctContents.ContentList>> fctContentFuture = CompletableFuture.supplyAsync(new Supplier<List<FctContents.ContentList>>() {
                @Override
                public List<FctContents.ContentList> get() {
                    return getFctOpeninfo(fctid);
                }
            });

            CompletableFuture.allOf(openModuleFuture, seriesFuture, autoSmallVideoCompletableFuture, fctActivityFuture, businessFuture, fctContentFuture).join();

            List<OpenModule> openModulelist = openModuleFuture.get();//openModuleRepository.getOpenMouduleByDealerId(fctid, 1);
            if (openModulelist == null || openModulelist.size() == 0) {
                openModulelist = getDefaultOpenModule();
            }

            List<Seriesitems> seriesitemsList = new ArrayList<>();
            for (OpenModule s : openModulelist) {
                try {
                    if (s == null) {
                        continue;
                    }
                    if (s.getModuleName().equals("热门车系")) {

                        seriesitemsList = seriesFuture.get();
                        s.setModuleData(seriesitemsList);

                    } else if (s.getModuleName().equals("车主小视频")) {

                        if (seriesitemsList != null || seriesitemsList.size() != 0) {
                            s.setModuleData(autoSmallVideoCompletableFuture.get());
                        }
                    } else if (s.getModuleName().equals("精彩活动")) {

                        List<FctActivity> fctActivitys = fctActivityFuture.get();
                        s.setModuleData(fctActivitys);

                    } else if (s.getModuleName().equals("车友圈")) {

                        List<BusinessInfo> businessinfolist = businessFuture.get();
                        s.setModuleData(businessinfolist);
                    }
                    //新闻资讯
                    else if (s.getModuleName().equals("新闻资讯")) {

                        List<FctContents.ContentList> contentLists = fctContentFuture.get();
                        s.setModuleData(contentLists);
                    }
                } catch (Exception e) {
                    log.error("getOpenModuleByFctIdNew inner error ", e);
                    continue;
                }
            }
            return new ResponseContent<>(openModulelist);
        } catch (Exception e) {
//            LogHelper.logError("getOpenModuleByFctId-withexception", e, e.getMessage());
            log.error("getOpenModuleByFctIdNew error ", e);
        }


        return null;
    }

    /**
     * 热门车系
     *
     * @param _appid
     * @param fctid
     * @return
     */
    public List<Seriesitems> getRecommendSeries(String _appid, int fctid, int brandid) {
        try {

            FctSeries fctSeries = carApiService.getSeriesByBrandIdAndFctId(_appid, null, fctid, 50, brandid);
            if (fctSeries == null || fctSeries.getSeriesitems() == null) {
                return null;
            }
            boolean ishaveRecommandSeries = false;
            for (Seriesitems seriesitems : fctSeries.getSeriesitems()) {
                if (seriesitems.getRecommendStatus() == 1) {
                    ishaveRecommandSeries = true;
                    break;
                }
            }

            if (ishaveRecommandSeries) {
                fctSeries.getSeriesitems().removeIf(item -> item.getRecommendStatus() != 1);

                fctSeries.getSeriesitems().sort(new Comparator<Seriesitems>() {
                    @Override
                    public int compare(Seriesitems o1, Seriesitems o2) {
                        if (StringUtil.isNotNullAndWhiteSpace(o1.getRecommendTime()) && StringUtil.isNotNullAndWhiteSpace(o2.getRecommendTime())) {
                            boolean comparedate = FormatDate.dateCompare(FormatDate.strToTime(o1.getRecommendTime()), FormatDate.strToTime(o2.getRecommendTime()));
                            if (comparedate) {
                                return 1;
                            } else {
                                return -1;
                            }
                        }
                        return 0;
                    }
                });
            } else {
                fctSeries.getSeriesitems().removeIf(item -> item.getStatus() != 1);
//                fctSeries.getSeriesitems().sort(new Comparator<FctSeries.Seriesitems>() {
//                    @Override
//                    public int compare(FctSeries.Seriesitems o1, FctSeries.Seriesitems o2) {
//
//                        if (o1.getSortNum() > o2.getSortNum()) {
//                            return 1;
//                        } else if (o1.getSortNum() == o2.getSortNum()) {
//                            return 0;
//                        } else {
//                            return -1;
//                        }
//                    }
//                });
            }

            if (fctSeries.getSeriesitems().size() > 0) {
                if (fctSeries.getSeriesitems().size() > 5) {
                    fctSeries.setSeriesitems(fctSeries.getSeriesitems().subList(0, 5));
                }
                fctSeries.getSeriesitems().forEach(s -> {
//                    if (StringUtil.isNotNullAndWhiteSpace(s.getPnglogo())) {
//                        s.setLogo(s.getPnglogo());
//                    }
                    ResponseContent<CarVrInfo> carVrInfoResponseContent = panoApiService.getcarvrinfobyseriesid(_appid, "car", s.getId());
                    if (carVrInfoResponseContent != null && carVrInfoResponseContent.getResult() != null) {
                        if (carVrInfoResponseContent.getResult().getHasexterior()) {
                            s.setHaveVr(true);
                        }
                    }
                });
            }
            return fctSeries.getSeriesitems();
        } catch (Exception e) {
            //LogHelper.logError("getRecommendSeries", e, e.getMessage());
            log.error("getRecommendSeries error ", e);
            return null;
        }
    }

    /**
     * 厂商活动
     *
     * @param fctid
     * @return
     */
    public List<FctActivity> getFctActivity(int fctid) {
        try {
            List<FctActivity> fctActivities = openModuleRepository.getFctActivitys(fctid, 1);
            if (fctActivities == null || fctActivities.size() == 0) {
                return fctActivities;
            }
            boolean ishaveRecommend = false;
            for (FctActivity fctActivity : fctActivities) {
                if (fctActivity.getRecommendStatus() == 1) {
                    ishaveRecommend = true;
                    break;
                }
            }
            if (ishaveRecommend) {
                fctActivities.removeIf(activity -> activity.getRecommendStatus() != 1);
            }

            fctActivities.sort(new Comparator<FctActivity>() {
                @Override
                public int compare(FctActivity o1, FctActivity o2) {
                    if (o1.getRecommendStatus() == 1 && o2.getRecommendStatus() == 0) {
                        return -1;
                    }
                    if (o1.getRecommendStatus() == 0 && o2.getRecommendStatus() == 1) {
                        return 1;
                    }
                    if (FormatDate.dateCompare(FormatDate.strToTime(o1.getStartTime()), FormatDate.strToTime(o2.getStartTime()))) {
                        return 1;
                    } else {
                        return -1;
                    }

                }
            });
            if (fctActivities.size() > 3) {
                fctActivities = fctActivities.subList(0, 3);
            }
            return fctActivities;
        } catch (Exception e) {
            //LogHelper.logError("getFctActivity", e, e.getMessage());
            log.error("getFctActivity error ", e);
            return null;
        }
    }

    /**
     * 厂商资讯
     *
     * @param fctid
     * @return
     */
    public List<FctContents.ContentList> getFctOpeninfo(int fctid) {
        try {
            List<OpenInfo> openInfoList = openModuleRepository.getFctOpenInfo(fctid, 1);
            if (openInfoList == null || openInfoList.size() == 0) {
                return null;
            }
            boolean ishasRecommend = false;
            for (OpenInfo openInfo : openInfoList) {
                if (openInfo.getRecommendStatus() == 1) {
                    ishasRecommend = true;
                    break;
                }
            }
            if (openInfoList.size() <= 1) {
                return null;
            }
            if (ishasRecommend) {
                openInfoList.removeIf(openInfo -> openInfo.getRecommendStatus() != 1);
                if (openInfoList.size() <= 1) {
                    return null;
                }
            }

            Map<String, List<OpenInfo>> groupopeninfo = openInfoList.stream().collect(Collectors.groupingBy(OpenInfo::getInfoType));
            List<String> cmsids = new ArrayList<>();
            List<String> chejiahaoids = new ArrayList<>();
            for (Map.Entry<String, List<OpenInfo>> entry : groupopeninfo.entrySet()) {
//                        --1车家号长文，2车家号图文，3车家号视频，11原创
                if (entry.getKey().equals("11")) {
                    if (entry.getValue() == null || entry.getValue().size() == 0) {
                        continue;
                    }
                    for (OpenInfo openInfo : entry.getValue()) {
                        cmsids.add(openInfo.getSourceInfoId());
                    }
                } else {
                    if (entry.getValue() == null || entry.getValue().size() == 0) {
                        continue;
                    }
                    for (OpenInfo openInfo : entry.getValue()) {
                        chejiahaoids.add(openInfo.getSourceInfoId());
                    }
                }
            }
            List<FctContents.ContentList> contentLists = new ArrayList<>();
            if (chejiahaoids.size() > 0) {
                //车家号文章
                List<ChejiahaoArticle> chejiahaoArticles = GetInfoDetails("m", String.join(",", chejiahaoids));
                if (chejiahaoArticles != null && chejiahaoArticles.size() > 0) {
                    for (ChejiahaoArticle chejiahaoArticle : chejiahaoArticles) {
                        FctContents.ContentList contentList = new FctContents.ContentList();
                        contentList.setTitle(chejiahaoArticle.getTitle());
                        contentList.setAuthorname(chejiahaoArticle.getAuthor() == null ? "" : chejiahaoArticle.getAuthor().getNickname());
                        contentList.setLanVicon("//chejiahao.autohome.com.cn/images/v/<EMAIL>");
                        String img = chejiahaoArticle.getImage() != null ? chejiahaoArticle.getImage().replace("http://", "//").replace("https://", "//") : "";
                        contentList.setImg(img);
                        int replycount = 0;
                        if (chejiahaoArticle.getInfostatistics() != null) {
                            replycount = chejiahaoArticle.getInfostatistics().getReplycount();
                        }
                        contentList.setCmsAppimages(chejiahaoArticle.getShowimagelist() ? chejiahaoArticle.getImages() : null);
                        contentList.setBizid(chejiahaoArticle.getInfoid());
                        contentList.setBiztype(chejiahaoArticle.getInfotype() + 30);
                        contentList.setPublishtime(chejiahaoArticle.getPublishtime());
                        contentList.setReplycount(replycount);

                        contentLists.add(contentList);
                    }
                }
            }
            if (cmsids.size() > 0) {
                //原创文章
                List<CmsArticle> cmsArticles = GetArticlesBatch("m", "0", "0", String.join(",", cmsids));
                if (cmsArticles != null && cmsArticles.size() > 0) {
                    for (CmsArticle cmsArticle : cmsArticles) {
                        FctContents.ContentList contentList = new FctContents.ContentList();
                        contentList.setReplycount(cmsArticle.getReplycount());
                        contentList.setPublishtime(cmsArticle.getPublishtime());
                        contentList.setBiztype(11);
                        contentList.setBizid(cmsArticle.getId());
                        contentList.setAuthorname(cmsArticle.getEditorname());
                        List<String> cmsimg = new ArrayList<>();
                        if (StringUtil.isNotNullAndWhiteSpace(cmsArticle.getFirstappcoverimg()) &&
                                StringUtil.isNotNullAndWhiteSpace(cmsArticle.getSecondappcoverimg()) &&
                                StringUtil.isNotNullAndWhiteSpace(cmsArticle.getThirdappcoverimg())) {

                            cmsimg.add(cmsArticle.getFirstappcoverimg().replace("http://", "//").replace("https://", "//"));
                            cmsimg.add(cmsArticle.getSecondappcoverimg().replace("http://", "//").replace("https://", "//"));
                            cmsimg.add(cmsArticle.getThirdappcoverimg().replace("http://", "//").replace("https://", "//"));
                        }
                        contentList.setImg(cmsArticle.getImg().replace("http://", "//").replace("https://", "//"));
                        contentList.setCmsAppimages(cmsimg);
                        contentList.setTitle(cmsArticle.getTitle());
                        contentLists.add(contentList);
                    }
                }
            }
            if (contentLists.size() > 0) {
                contentLists.sort(new Comparator<FctContents.ContentList>() {
                    @Override
                    public int compare(FctContents.ContentList o1, FctContents.ContentList o2) {
//                        if (o1.getReplycount() > o2.getReplycount()) {
//                            return -1;
//                        }
//                        if (o1.getReplycount() < o2.getReplycount()) {
//                            return 1;
//                        }
                        if (StringUtil.isNotNullAndWhiteSpace(o1.getPublishtime()) && StringUtil.isNotNullAndWhiteSpace(o2.getPublishtime())) {
                            if (FormatDate.dateCompare(FormatDate.strToTime(o1.getPublishtime()), FormatDate.strToTime(o2.getPublishtime()))) {
                                return 1;
                            } else {
                                return -1;
                            }
                        }
                        return 0;
                    }
                });
            }
            if (contentLists.size() > 3) {
                contentLists = contentLists.subList(0, 3);
            }
            return contentLists;

//            FctContents fctContents = queryContentsByFactoryId("m", "31,32,33", "1", "11", fctid, 3);
//            if (fctContents == null) {
//                return null;
//            }
//            return fctContents.getList();
        } catch (Exception e) {
            log.error("getFctOpeninfo error ", e);
            return null;
        }
    }

    public List<OpenModule> getDefaultOpenModule() {
        List<OpenModule> openModulelist = new ArrayList<>();
        openModulelist.add(new OpenModule(1, "热门车系", 1, 1));
        openModulelist.add(new OpenModule(2, "车主小视频", 1, 2));
        openModulelist.add(new OpenModule(3, "精彩活动", 1, 3));
        openModulelist.add(new OpenModule(4, "车友圈", 1, 4));
        openModulelist.add(new OpenModule(5, "新闻资讯", 1, 5));
        return openModulelist;
    }

    public List<ChejiahaoArticle> GetInfoDetails(String _appid, String infoIds) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("infoIds", infoIds);
//            String result = carApi.get("chejiahao.api.GetInfoDetails", param);
//
//            if (StringUtil.isBlank(result)) {
//                return null;
//            }
//            ResponseContent<List<ChejiahaoArticle>> detailResponseContent = JSON.parseObject(result, new TypeReference<ResponseContent<List<ChejiahaoArticle>>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://chejiahao.api.lq.autohome.com.cn/InfoFromCache.svc/GetInfoDetails", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<List<ChejiahaoArticle>> detailResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<List<ChejiahaoArticle>>>() {
            });

            if (detailResponseContent == null || detailResponseContent.getResult() == null) {
                return null;
            }
            return detailResponseContent.getResult();
        } catch (Exception e) {
//            LogHelper.logError("GetInfoDetails", e, e.getMessage());
            log.error("GetInfoDetails error ", e);
            return null;
        }
    }

    /**
     * @param _appid
     * @param withContent
     * @param withEvalGood
     * @param Ids
     * @return
     */
    public List<CmsArticle> GetArticlesBatch(String _appid, String withContent, String withEvalGood, String Ids) {
        try {
//            ?_appid=app&chejiahaoBizTypes=31,32,33&chejiahaoUserType=1&ycBizTypes=11&factoryId=58&takeCount=3
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("withContent", withContent);
            param.put("withEvalGood", withEvalGood);
            param.put("Ids", Ids);
//            String result = carApi.get("cms.api.GetArticlesBatch", param);
//
//            if (StringUtil.isBlank(result)) {
//                return null;
//            }
//            ResponseContent<List<CmsArticle>> detailResponseContent = JSON.parseObject(result, new TypeReference<ResponseContent<List<CmsArticle>>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://cms.api.autohome.com.cn/Wcf/ArticleSerivce.svc/GetArticlesBatch", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            ResponseContent<List<CmsArticle>> detailResponseContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<List<CmsArticle>>>() {
            });

            if (detailResponseContent == null || detailResponseContent.getResult() == null) {
                return null;
            }
            return detailResponseContent.getResult();
        } catch (Exception e) {
//            LogHelper.logError("queryContentsByFactoryId", e, e.getMessage());
            log.error("GetInfoDetails error ", e);
            return null;
        }
    }


    /**
     * 根据车系Id查询图片
     * @param seriesId
     * @return
     */
    public List<Api8Dto.Picitems> getApi_8(String seriesId ) {
        try {
//            seriesid=528&specid=0&classid=1&pageindex=1&pagesize=3&_appid=car&_v=v4.1.0
            Map<String, Object> param = new HashMap<>();
            param.put("seriesid", seriesId);
            param.put("specid", 0);
            param.put("classid", 1);
            param.put("pageindex", 1);
            param.put("pagesize", 10);
            param.put("_appid", "car");
            param.put("_v", "v4.1.0");

            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://lapp.corpautohome.com/cloudapi/car/api_8?", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return null;
            }
            Api8Dto api8Dto = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<Api8Dto>() {
            });

            if (api8Dto == null || api8Dto.getResult() == null) {
                return null;
            }
            return api8Dto.getResult().getPicitems();
        } catch (Exception e) {

            log.error("getApi_8 seriesId={} error  ",seriesId, e);
            return null;
        }
    }
}
