package com.autohome.applet.service.caropenapi_uc_news_dealerservice;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.fct.AutoSmallVideo;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.rcm.RcmDataOpenResponse;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

public interface RcmService {

    List<HashMap> autosmallvideoByFctIdNew(String _appid, String deviceid, String operation, String netstate, String gps,
                                           String userip, String version, String outmedia, int number, int fctid, int seriesid, HttpServletRequest request);

    ResponseContent<?> autosmallvideo(String _appid, String deviceid, String operation, String netstate, String gps,
                                      String userip, String version, String outmedia, String seriesids, int number);

    AutoSmallVideo autosmallvideoByFctId(String _appid, String deviceid, String operation, String netstate, String gps,
                                         String userip, String version, String outmedia, int number, int fctid, int seriesid);

    ResponseContent<RcmDataOpenResponse> autosmallvideoNew(String _appid, String deviceid, String operation, String netstate, String gps,
                                                           String userip, String version, String outmedia, String seriesids, int number, HttpServletRequest request);
}
