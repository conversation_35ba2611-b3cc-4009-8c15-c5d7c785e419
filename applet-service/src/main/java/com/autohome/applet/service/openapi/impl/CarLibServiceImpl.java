package com.autohome.applet.service.openapi.impl;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.carlibrary.carapi.*;
import com.autohome.applet.model.dto.carlibrary.carapi.out.*;
import com.autohome.applet.service.openapi.CarLibService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.SecurityKit;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CarLibServiceImpl implements CarLibService {

    @Override
    public CarApiBrandListOut getCarApiBrandList() {
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("https://carservice.autohome.com.cn/carapi/getbrandmenulist?_appid=lightapp");
        CarApiBrandListOut out = new CarApiBrandListOut();
        if (httpResult.getStatusCode() == 200) {
            ReturnValue<CarApiBrandList> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarApiBrandList>>() {
            });
            if (result != null && result.getReturncode() == 0 && result.getResult() != null) {
                CarApiBrandList carApiBrandList = result.getResult();
                out.setTotal(carApiBrandList.getTotal());
                out.setBrandList(carApiBrandList.getBrandList().stream().map(CarApiBrandOut::by).collect(Collectors.toList()));
                return out;
            }
        }
        out.setTotal(0);
        out.setBrandList(Collections.emptyList());
        return out;
    }

    @Override
    public CarApiFctListOut getCarApiSeriesList(String secretBrandId) {
        int brandId = SecurityKit.decrypt(secretBrandId);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(
                "http://carservice.autohome.com.cn/carapi/getserieslistbybrand?_appid=lightapp&brandid="
                        + brandId);
        CarApiFctListOut fctListOut = new CarApiFctListOut();
        if (httpResult.getStatusCode() == 200) {
            ReturnValue<CarApiFctList> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarApiFctList>>() {
            });
            if (result != null && result.getReturncode() == 0 && result.getResult() != null) {
                CarApiFctList carApiFctList = result.getResult();
                fctListOut.setBrandId(SecurityKit.encrypt(carApiFctList.getBrandId()));
                fctListOut.setBrandName(carApiFctList.getBrandName());
                fctListOut.setBrandLogo(carApiFctList.getBrandLogo());
                fctListOut.setFctList(carApiFctList.getFctList().stream().map(item -> {
                    CarApiFctOut fctOut = new CarApiFctOut();
                    fctOut.setFctId(SecurityKit.encrypt(item.getFctId()));
                    fctOut.setFctName(item.getFctName());
                    String seriesIds = item.getSeriesList().stream().map(CarApiSeries::getSeriesId).map(String::valueOf)
                            .collect(Collectors.joining(","));

                    Map<String, String> vrUrlMap = new HashMap<>();
                    try {
                        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://pano.api.lq.autohome.com.cn/v1/vr/getcarvrinfobyseriesids?_appid=car&category=car&vrtype=0&seriesids=" + seriesIds);
                        Map deserialize = JacksonHelper.deserialize(httpGet.getBody(), Map.class);
                        if (deserialize != null && deserialize.get("Result") != null) {
                            Map theResult = (Map) deserialize.get("Result");
                            if (theResult != null && theResult.get("seriesitems") != null) {
                                Map seriesitems = (Map) theResult.get("seriesitems");
                                seriesitems.forEach((k, v) -> {
                                    Object extInfo = ((Map) v).get("ExtInfo");
                                    if (extInfo != null && extInfo instanceof List && ((List<?>) extInfo).size() > 0) {
                                        Map specItem = (Map) ((List<?>) extInfo).get(0);
                                        Object showUrl = specItem.get("ShowUrl");
                                        if (showUrl != null && showUrl instanceof String) {
                                            vrUrlMap.put((String)k, (String) showUrl);
                                        }
                                    }
                                });
                            }
                        }
                    } catch (Exception e){
                        log.warn("get vr url ", e);
                    }

                    List<CarApiSeriesOut> seriesList = item.getSeriesList().stream().map(series -> {
                        CarApiSeriesOut seriesOut = new CarApiSeriesOut();
                        seriesOut.setSeriesId(SecurityKit.encrypt(series.getSeriesId()));
                        seriesOut.setSeriesName(series.getSeriesName());
                        seriesOut.setSeriesState(series.getSeriesState());
                        seriesOut.setSeriesImg(series.getSeriesImg());
                        seriesOut.setPngLogo(series.getPngLogo());
                        seriesOut.setSeriesPlace(series.getSeriesPlace());
                        seriesOut.setLevelName(series.getLevelName());
                        seriesOut.setNewEnergy(series.getNewEnergy());
                        seriesOut.setRank(series.getRank());
                        String vrUrl = vrUrlMap.get(series.getSeriesId() + "");
                        if (vrUrl != null) {
                            seriesOut.setVrH5Url(vrUrl);
                        } else {
                            seriesOut.setVrH5Url("");
                        }
                        return seriesOut;
                    }).collect(Collectors.toList());

                    fctOut.setSeriesList(seriesList);
                    return fctOut;
                }).collect(Collectors.toList()));
                return fctListOut;
            }
        }
        fctListOut.setBrandId(secretBrandId);
        fctListOut.setBrandName("");
        fctListOut.setBrandLogo("");
        fctListOut.setFctList(Collections.emptyList());

        return fctListOut;
    }

    @Override
    public CarApiSpecListOut getCarApiSpecList(String secretSeriesId) {
        int seriesId = SecurityKit.decrypt(secretSeriesId);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(
                "https://carservice.autohome.com.cn/carapi/getSpecGroupListBySeriesId?seriesid="
                        + seriesId);
        CarApiSpecListOut specListOut = new CarApiSpecListOut();
        specListOut.setSeriesId(secretSeriesId);
        specListOut.setSpecList(Collections.emptyList());
        if (httpResult.getStatusCode() == 200) {
            ReturnValue<CarApiYearList> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarApiYearList>>() {
            });
            if (result != null && result.getReturncode() == 0 && result.getResult() != null) {
                Set<CarApiSpecOut> collect = result.getResult().getYearList().stream()
                        .flatMap(year -> year.getYearListBeans().stream())
                        .flatMap(year -> year.getSpecGourpList().stream())
                        .flatMap(specGroup -> specGroup.getSpecList().stream())
                        .map(CarApiSpecOut::by)
                        .collect(Collectors.toSet());
                specListOut.setSpecList(new ArrayList<>(collect));
                return specListOut;
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(SecurityKit.encrypt("27436"));
        System.out.println(SecurityKit.decrypt("67356A42FD05FD80A2558AC7F79070ED"));
    }
    @Override
    public CarApiSpecDetailOut getCarApiSpecDetail(String secretSpecId) {
        int specId = SecurityKit.decrypt(secretSpecId);
        // http://zhishi.autohome.com.cn/home/<USER>/file?targetId=5177544
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(
                "http://car.api.autohome.com.cn/v1/carprice/spec_parambyspecid.ashx?specid=" + specId + "&_appid=car");
        if (httpResult.getStatusCode() == 200) {
            ReturnValue<CarApiSpecDetail> deserialize = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<CarApiSpecDetail>>() {
            });
            if (deserialize != null && deserialize.getReturncode() == 0 && deserialize.getResult() != null) {
                CarApiSpecDetail result = deserialize.getResult();
                CarApiSpecDetailOut out = new CarApiSpecDetailOut();
                out.setSpecId(secretSpecId);
                out.setSpecName(result.getSpecName());
                out.setSpecFlowModeId(result.getSpecFlowModeId());
                if (result.getSpecFlowModeId() == 1) {
                    out.setSpecDispWithFlowMode(result.getSpecDisplacement() + "L");
                } else if (result.getSpecFlowModeId() >= 2 && result.getSpecFlowModeId() <= 9){
                    out.setSpecDispWithFlowMode(result.getSpecDisplacement() + "T");
                } else {
                    out.setSpecDispWithFlowMode(result.getSpecDisplacement() + "");
                }
                out.setSpecFlowModeName(result.getSpecFlowModeName());
                out.setSpecDisplacement(result.getSpecDisplacement());
                out.setSpecStructureDoor(result.getSpecStructureDoor());
                out.setSpecStructureSeat(result.getSpecStructureSeat());
                out.setSpecTransmission(result.getSpecTransmission());
                out.setFuelType(result.getFuelTypeDetail());
                out.setSpecLength(result.getSpecLength());
                out.setSpecWidth(result.getSpecWidth());
                out.setSpecHeight(result.getSpecHeight());
                out.setSpecWeight(result.getSpecWeight());
                out.setEngingKw(result.getEngingKw());
                out.setSpecEnginePower(result.getSpecEnginePower());
                out.setOilLabel(result.getOilLabel());
                out.setSpecDrivingModeName(result.getSpecDrivingModeName());
                out.setLevelName(result.getLevelName());
                out.setOilBoxVolume(result.getOilBoxVolume());
                out.setSpecOilOfficial(result.getSpecOilOfficial());
                out.setMile(result.getMile());
                out.setSpecStructureTypeName(result.getSpecStructureTypeName());
                out.setFastChargeTime(result.getFastChargeTime());
                out.setSlowChargeTime(result.getSlowChargeTime());

                HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("https://car.api.autohome.com.cn/v1/carpic/Spec_25PictureBySpecList.ashx?_appid=car&&speclist=" + specId);
                if (httpGet.getStatusCode() == 200) {
                    ReturnValue<CarApiSpecPicList> picResult = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<CarApiSpecPicList>>() {
                    });
                    if (picResult != null && picResult.getReturncode() == 0 && picResult.getResult() != null) {
                        List<CarApiSpecPicDetail> specItems = picResult.getResult().getSpecItems();
                        if (specItems.size() > 0) {
                            CarApiSpecPicDetail detail = specItems.get(0);
                            if (detail.getSpecId() == specId) {
                                List<CarApiSpecPicDetail.PicItem> picItems = detail.getPicItems();
                                out.setPicGroups(picItems.stream().filter(item -> {
                                                    if ("外观".equals(item.getTypeName())
                                                            || "中控类".equals(item.getTypeName())
                                                            || "车厢座椅".equals(item.getTypeName())) {
                                                        if (item.getPicItems() != null && item.getPicItems().size() > 3) {
                                                            item.setPicItems(item.getPicItems().subList(0, 3));
                                                        }
                                                        return true;
                                                    } else {
                                                        return false;
                                                    }
                                                }
                                        ).map(CarApiSpecDetailOut.PicItemOut::by)
                                        .collect(Collectors.toList()));

                            }
                        }
                    }
                }
                if (out.getPicGroups() == null) {
                    out.setPicGroups(Collections.emptyList());
                }

                return out;
            }
        }
        return null;
    }
}
