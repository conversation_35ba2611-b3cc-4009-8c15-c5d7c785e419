package com.autohome.applet.service.javaapi;


import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.AllNewBrands;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.BrandsTimeModel;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.NewCarLaunchHead;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface NewCarLaunchService {

    ReturnValue<?> getNewCarLaunchHead();

    List<NewCarLaunchHead> getNewCarLaunchHeadNoCache();

    ReturnValue<?> getNewCarLaunchList(String startTime, String endTime, int levelid,String month);

    /**
     * tabType: 1 即将上市; 2 已上市
     * startTime： 开始时间 2024-05-01:5月数据， 2024-04-01:4月数据
     * levelid: 级别
     * */
    List<BrandsTimeModel> getNewCarLaunchListNoCache( String startTime, String endTime, int levelid, String month);

    /**
     * 获取上市车信息
     * */
    List<AllNewBrands> allNewBrands();
}
