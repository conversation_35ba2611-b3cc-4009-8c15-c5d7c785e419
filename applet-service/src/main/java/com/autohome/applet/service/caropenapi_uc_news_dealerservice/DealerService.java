package com.autohome.applet.service.caropenapi_uc_news_dealerservice;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer.*;

import java.util.List;

public interface DealerService {
    ResponseContent<?> GetCompleteDealerInfo(String dealerid, String classIds, String platform, int isNeedVirtual);

    ResponseContent<?> getSubOpenModuleByDealerId(int dealerid, int cityid, int pageSize, int orderType, int bdstatus);

    DealerSales getUserSales(String dealerid, int cityId, int pageIndex, int pageSize);

    HotSpecList GetHotSpecListByDealerId(String dealerid);

    ResponseContent<?> getDealers(String seriesId, String cityid, String _appid, String orderType, String pageIndex, String pageSize, String myLat, String myLon);

    ResponseContent<?> getOpenModuleByDealerId(int dealerid, int cityid, int pageSize, int orderType, int bdstatus);

    ResponseContent<?> GetDealersByFctid(int brandId, int fctid, int cityid, String _appid, String orderType, int pageIndex, int pageSize, String myLat, String myLon, String _encoding);

    DealerVR getSeriesVRList(String dealerid);

    TestDriveOrder getUserTestDriveOrderList(String mobile, String dealerid, int pageIndex, int pageSize);

    MaintainOrder maintainorderlist(String userid, String dealerid, int pageIndex, int pageSize);

    ResponseContent<SpecDealerPrice> getSpecsMinPrice(String _appid, int cityId, String specids, int dealerid);

    ResponseContent<List<SeriesDealerPrice>> getSeriesMinOriginalPriceSpec(String _appid, int cityId, String seriesIds);


    ResponseContent<?> getseriesvideolatest(String imei, String series_id, int startindex, int count);
}
