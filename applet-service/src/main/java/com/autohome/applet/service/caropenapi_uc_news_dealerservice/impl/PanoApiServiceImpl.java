package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarSeriesList;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.carmodel.CarVrInfo;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.CarApiService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.PanoApiService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

@Slf4j
@Service
public class PanoApiServiceImpl implements PanoApiService {
    @Autowired
    private CarApiService carApiService;

    /**
     * 车系全景
     *
     * @param _appid
     * @param category
     * @param seriesid
     * @return
     */
    @Override
    public ResponseContent<CarVrInfo> getcarvrinfobyseriesid(String _appid, String category, int seriesid) {
        try {
            //String getcarvrinfobyseriesid = "http://pano.api.autohome.com.cn/v1/vr/getcarvrinfobyseriesid";//?_appid=app&category=car&seriesid=18
            Map<String, Object> param = new HashMap<>();

            param.put("_appid", _appid);
            param.put("category", category);
            param.put("seriesid", Integer.toString(seriesid));
//            String result = carApi.get("pano.api.getcarvrinfobyseriesid", param);//HttpHelper.getResponse(getcarvrinfobyseriesid, param, null, 3000).getContent();
//
//            if (StringUtil.isBlank(result)) {
//                return new ResponseContent(101, "暂无数据");
//            }
//            ResponseContent<CarVrInfo> detailResponseContent = JSON.parseObject(result, new TypeReference<ResponseContent<CarVrInfo>>() {
//            });
//
//            if (detailResponseContent == null || detailResponseContent.getResult() == null) {
//                return new ResponseContent(101, "暂无数据");
//            }
//            return detailResponseContent;
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://pano.api.autohome.com.cn/v1/vr/getcarvrinfobyseriesid", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(101, "暂无数据");
            } else {
                ResponseContent<CarVrInfo> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<CarVrInfo>>() {
                });
                if (responseContent == null || responseContent.getResult() == null) {
                    return new ResponseContent<>(101, "暂无数据");
                }
                return responseContent;
            }
        } catch (Exception e) {
            log.error("getcarvrinfobyseriesid error", e);
            return new ResponseContent<>(500, "暂无数据");
        }
    }

    /**
     * 外观素材查询 - 根据车系ID尺寸查询全部素材url
     *
     * @param _appid
     * @param category
     * @param seriesid
     * @return
     */
    public ResponseContent<?> getextframelist(String _appid, String category, int seriesid, String sizelevel) {
        try {
            CompletableFuture<ResponseContent<HashMap>> extframeFuture = CompletableFuture.supplyAsync(new Supplier<ResponseContent<HashMap>>() {
                @Override
                public ResponseContent<HashMap> get() {
                    Map<String, Object> param = new HashMap<>();
                    param.put("_appid", _appid);
                    param.put("category", category);
                    param.put("sizelevel", sizelevel);
                    param.put("seriesid", Integer.toString(seriesid));
//                    String result = carApi.get("panocms.api.getextframelist", param);//HttpHelper.getResponse(getcarvrinfobyseriesid, param, null, 3000).getContent();
//                    if (StringUtil.isBlank(result)) {
//                        return new ResponseContent(101, "暂无数据");
//                    }
//                    ResponseContent<HashMap> detailResponseContent = JSON.parseObject(result, new com.alibaba.fastjson.TypeReference<ResponseContent<HashMap>>() {
//                    });
//                    if (detailResponseContent == null || detailResponseContent.getResult() == null) {
//                        return new ResponseContent(101, "暂无数据");
//                    }
////                    if(seriesid==3691){
////                        detailResponseContent.getResult().put("color_list",new ArrayList<>());
////                    }
                    HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://panocms.api.autohome.com.cn/v1/exterior/getextframelist", param);
                    if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                        return new ResponseContent<>(101, "暂无数据");
                    } else {
                        ResponseContent<HashMap> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ResponseContent<HashMap>>() {
                        });
                        if (responseContent == null || responseContent.getResult() == null) {
                            return new ResponseContent<>(101, "暂无数据");
                        }
                        return responseContent;
                    }

                }
            });
            CompletableFuture<CarSeriesList> carFuture = CompletableFuture.supplyAsync(new Supplier<CarSeriesList>() {
                @Override
                public CarSeriesList get() {
                    return carApiService.getBaseInfoBySeriesList(Integer.toString(seriesid));
                }
            });

            CompletableFuture.allOf(extframeFuture, carFuture).join();


            CarSeriesList carSeriesList = carFuture.get();
            ResponseContent<HashMap> detailResponseContent = extframeFuture.get();
            if (carSeriesList != null && carSeriesList.getList() != null && carSeriesList.getList().size() > 0) {
                if (detailResponseContent == null || detailResponseContent.getResult() == null) {
                    Map<String, Object> seriespic = new HashMap<>();
                    seriespic.put("seriespic", carSeriesList.getList().get(0).getPic());
                    seriespic.put("color_list", new ArrayList<>());
                    return new ResponseContent(0, "", seriespic);
                }
                detailResponseContent.getResult().put("seriespic", carSeriesList.getList().get(0).getPic());
            }
            return new ResponseContent<>(detailResponseContent.getResult());
        } catch (Exception e) {
            return new ResponseContent<>(101, "暂无数据");
        }
    }


}
