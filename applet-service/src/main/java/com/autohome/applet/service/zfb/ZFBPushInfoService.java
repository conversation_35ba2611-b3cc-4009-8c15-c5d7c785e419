package com.autohome.applet.service.zfb;

import com.alipay.api.FileItem;
import com.alipay.api.response.AlipayOpenFileUploadResponse;
import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentPublishResponse;
import com.alipay.api.response.AlipaySocialBaseLifeprodStandardcontentPublishResponse;
import com.autohome.applet.model.dto.maindata.MainDataArticleDataDto;

import java.io.InputStream;

public interface ZFBPushInfoService {

    /**
     * 上报素材
     */
    AlipayOpenFileUploadResponse uploadMaterial(FileItem fileContent);

    /**
     * 上报素材（流式上传版本）
     * 支持从 InputStream 直接流式上传，避免文件落地
     * @param fileName 文件名
     * @param inputStream 输入流
     * @param contentLength 内容长度（字节数），如果未知可传入-1
     * @return 上传响应
     */
    AlipayOpenFileUploadResponse uploadMaterialStream(String fileName, InputStream inputStream, long contentLength);
    /**
     * 长图文内容发布（支持富文本）
     */
    AlipaySocialBaseLifeprodStandardcontentPublishResponse pushLongContent(MainDataArticleDataDto.ArticleData articleData);

    /**
     * 内容发布
     */
    AlipaySocialBaseContentlibStandardcontentPublishResponse pushContent(MainDataArticleDataDto.ArticleData articleData);

    /**
     * 删除素材
     */
    void deleteMaterial(String contentId);

    /**
     * 单条内容状态查询
     */
    void  queryContentStatus(String contentId);

}
