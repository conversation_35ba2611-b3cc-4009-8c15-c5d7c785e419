package com.autohome.applet.service.zfb;

import com.alipay.api.FileItem;
import com.alipay.api.response.AlipayOpenFileUploadResponse;
import com.alipay.api.response.AlipaySocialBaseContentlibStandardcontentPublishResponse;
import com.alipay.api.response.AlipaySocialBaseLifeprodStandardcontentPublishResponse;
import com.autohome.applet.model.dto.maindata.MainDataArticleDataDto;

public interface ZFBPushInfoService {

    /**
     * 上报素材
     */
    AlipayOpenFileUploadResponse uploadMaterial(FileItem fileContent);
    /**
     * 长图文内容发布（支持富文本）
     */
    AlipaySocialBaseLifeprodStandardcontentPublishResponse pushLongContent(MainDataArticleDataDto.ArticleData articleData);

    /**
     * 内容发布
     */
    AlipaySocialBaseContentlibStandardcontentPublishResponse pushContent(MainDataArticleDataDto.ArticleData articleData);

    /**
     * 删除素材
     */
    void deleteMaterial(String contentId);

    /**
     * 单条内容状态查询
     */
    void  queryContentStatus(String contentId);

}
