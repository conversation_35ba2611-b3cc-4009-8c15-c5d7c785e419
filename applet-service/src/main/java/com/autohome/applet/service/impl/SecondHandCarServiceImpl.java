package com.autohome.applet.service.impl;

import com.autohome.applet.config.BaiduUploadConfig;
import com.autohome.applet.dao.javaapi.mapper.*;
import com.autohome.applet.dao.javaapi.model.*;
import com.autohome.applet.model.dto.baidu.BaiduResult;
import com.autohome.applet.model.dto.baidu.SecondCarToAppletDto;
import com.autohome.applet.model.dto.baidu.SecondCarToAppletOrgDto;
import com.autohome.applet.model.dto.baidu.SecondCarToAppletProductDto;
import com.autohome.applet.service.SecondHandCarService;
import com.autohome.applet.util.*;
import com.autonews.springboot.util.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SecondHandCarServiceImpl implements SecondHandCarService {
    @Autowired
    private SecondHandCarOrgInfoMapper secondHandCarOrgInfoMapper;
    @Autowired
    private SecondHandCarProductInfoMapper secondHandCarProductInfoMapper;
    @Autowired
    private SecondHandCarOrgInfoLogMapper secondHandCarOrgInfoLogMapper;
    @Autowired
    private SecondHandCarProductInfoLogMapper secondHandCarProductInfoLogMapper;
    @Autowired
    private SecondHandCarUploadBaiduLogMapper secondHandCarUploadBaiduLogMapper;
    @Qualifier("lightapp")
    @Autowired
    RedisClient redisClient;
    @Autowired
    private BaiduUploadConfig baiduUploadConfig;

    //组装product的数量
    private static final Integer SIZE = 20;

    @Override
    public boolean saveSecondHandCarOrgInfo(SecondHandCarOrgInfo secondHandCarOrgInfo) {
        secondHandCarOrgInfo.setModifiedStime(DateHelper.getNow());
        secondHandCarOrgInfo.setVersion(getVersion());
        //保存百度的区县
        String orginDistrict = secondHandCarOrgInfo.getOrgDistrict();
        String newDistrict = ConvertDistrictUtil.convertToBaiduDistrict(secondHandCarOrgInfo.getOrgId());
        secondHandCarOrgInfo.setOrgDistrict(StringUtils.isEmpty(newDistrict) ? orginDistrict : newDistrict);
        return secondHandCarOrgInfoMapper.save(secondHandCarOrgInfo) > 0;
    }

    @Override
    public boolean saveSecondHandCarOrgInfoLog(SecondHandCarOrgInfoLog secondHandCarOrgInfoLog) {
        return secondHandCarOrgInfoLogMapper.insert(secondHandCarOrgInfoLog) > 0;
    }

    @Override
    public boolean saveSecondHandCarProductInfo(SecondHandCarProductInfo secondHandCarProductInfo) {
        secondHandCarProductInfo.setModifiedStime(DateHelper.getNow());
        secondHandCarProductInfo.setVersion(System.currentTimeMillis());
        boolean result = secondHandCarProductInfoMapper.save(secondHandCarProductInfo) > 0;
        if(result){
            SecondHandCarOrgInfo secondHandCarOrgInfo = new SecondHandCarOrgInfo();
            secondHandCarOrgInfo.setVersion(getVersion());
            secondHandCarOrgInfo.setOrgId(secondHandCarProductInfo.getOrgId());
            secondHandCarOrgInfoMapper.updateVersionByOrgId(secondHandCarOrgInfo.getOrgId(), getVersion());
        }
        return result;
    }

    @Override
    public boolean saveSecondHandCarProductInfoLog(SecondHandCarProductInfoLog secondHandCarProductInfoLog) {
        return secondHandCarProductInfoLogMapper.insert(secondHandCarProductInfoLog) > 0;
    }

    @Override
    public boolean updateOrgVersion(SecondHandCarOrgInfo secondHandCarOrgInfo) {
        return secondHandCarOrgInfoMapper.updateVersionByOrgId(secondHandCarOrgInfo.getOrgId(), secondHandCarOrgInfo.getVersion()) > 0;
    }

    @Override
    public boolean updateOrgStatus(SecondHandCarOrgInfo secondHandCarOrgInfo) {
        secondHandCarOrgInfo.setVersion(System.currentTimeMillis());
        return secondHandCarOrgInfoMapper.updateStatusByOrgId(secondHandCarOrgInfo.getOrgId(), secondHandCarOrgInfo.getVersion(), secondHandCarOrgInfo.getOrgStatus()) > 0;
    }

    @Override
    public boolean updateProductStatus(SecondHandCarProductInfo secondHandCarProductInfo) {
        secondHandCarProductInfo.setVersion(System.currentTimeMillis());
        return secondHandCarProductInfoMapper.updateStatusByServiceId(secondHandCarProductInfo.getServiceId(), secondHandCarProductInfo.getVersion(), secondHandCarProductInfo.getStatus()) > 0;
    }

    @Override
    public List<SecondHandCarOrgInfo> getSecondCarToAppletOrgDtoList(long lastVersion) {
        //查询上次上报的version
        //获取当前需要上报的门店信息, top 2000
        return secondHandCarOrgInfoMapper.listByVersion(lastVersion);
    }

    @Override
    public List<SecondHandCarOrgInfo> listByOrgIds(List<String> orgIds) {
        return secondHandCarOrgInfoMapper.listByOrgIds(orgIds);
    }

    @Override
    public boolean updateProductVersionBatch(List<String> serverIdList, Long version) {
        if(CollectionUtils.isEmpty(serverIdList)){
            return true;
        }
        return secondHandCarProductInfoMapper.updateVersionByServiceIds(serverIdList, version) > 0;
    }

    @Override
    public List<SecondHandCarProductInfo> listByVersion(String orgId, Long version) {
        return secondHandCarProductInfoMapper.listByVersion(orgId, version);
    }

    @Override
    public int countProductByVersion(String orgId, Long version) {
        return secondHandCarProductInfoMapper.countMoreThanByVersion(orgId, version);
    }

    @Override
    public void uploadToBaidu(SecondHandCarOrgInfo secondHandCarOrgInfo, Long lastVersion, Long currentVersion, Long errorVersion) {
        //1.获取org下面的product
        List<SecondHandCarProductInfo> secondHandCarProductInfoList = listByVersion(secondHandCarOrgInfo.getOrgId(), lastVersion);
        if(CollectionUtils.isEmpty(secondHandCarProductInfoList)){
            if(0 == secondHandCarOrgInfo.getOrgStatus()){
                //如果是下线门店, 重新传门店对应的车源
                log.info("SecondHandCarService upload to baidu orgid:{}, off line", secondHandCarOrgInfo.getOrgId());
                secondHandCarProductInfoList = listByVersion(secondHandCarOrgInfo.getOrgId(), 0L);
            }
            else{
                //更新org的版本号,退出
                secondHandCarOrgInfo.setVersion(currentVersion);
                updateOrgVersion(secondHandCarOrgInfo);
                log.info("SecondHandCarService upload to baidu orgid:{}, no product", secondHandCarOrgInfo.getOrgId());
                return;
            }
        }
        //判断必填项
        if(!checkRequiredFields(secondHandCarOrgInfo)){
            return;
        }
        //替换封面图
        replaceOrgIcon(secondHandCarOrgInfo, secondHandCarProductInfoList);
        //生成版本号, 一次机构拆分后,机构的版本号必须一致, 商品不影响
        Long orgVersion = getVersion();

        //2.组装25条product
        int pages = secondHandCarProductInfoList.size() / SIZE + 1;
        for(int i=0; i<pages; i++){
            List<SecondHandCarProductInfo> tempSecondHandCarProductInfoList = secondHandCarProductInfoList.stream().skip(i * SIZE)
                    .limit(SIZE)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tempSecondHandCarProductInfoList)){
                continue;
            }

            //组装上报百度的参数
            SecondCarToAppletDto secondCarToAppletDto = assemblySecondCarToAppletDto(secondHandCarOrgInfo, tempSecondHandCarProductInfoList);
            //设置结构版本号
            secondCarToAppletDto.getDisplay().setVersion(String.valueOf(orgVersion));

            //上传百度
            boolean resultStatus = uploadToBaidu(secondCarToAppletDto);
//            boolean resultStatus = true;
            //批量更新product的version = currentVersion
            List<String> serverIdList = tempSecondHandCarProductInfoList.stream().map(p -> p.getServiceId()).collect(Collectors.toList());
            if(resultStatus){
                //更新版本为当前版本
                updateProductVersionBatch(serverIdList, currentVersion);
            }
            else{
                //更新版本为下次上传版本, 防止漏掉上传失败数据
                updateProductVersionBatch(serverIdList, errorVersion);
            }
        }
        //查询org下是否还有未上报的product, 如果有,org.version=errorVersion; 否则org.version=currentVersion
        int c = countProductByVersion(secondHandCarOrgInfo.getOrgId(), currentVersion);
        if(c == 0){
            secondHandCarOrgInfo.setVersion(currentVersion);
            updateOrgVersion(secondHandCarOrgInfo);
        }
        else{
            secondHandCarOrgInfo.setVersion(errorVersion);
            updateOrgVersion(secondHandCarOrgInfo);
        }
    }

    @Override
    public String getUploadBaiduKey(){
        return "applet:sc:bd:v";
    }

    private Long getVersion(){
        return System.currentTimeMillis();
    }

    private SecondCarToAppletDto assemblySecondCarToAppletDto(SecondHandCarOrgInfo secondHandCarOrgInfo, List<SecondHandCarProductInfo> secondHandCarProductInfoList){
        SecondCarToAppletDto secondCarToAppletDto = new SecondCarToAppletDto();
        String tempOrgJson = JacksonHelper.serialize(secondHandCarOrgInfo);
        SecondCarToAppletOrgDto secondCarToAppletOrgDto = JacksonHelper.deserialize(tempOrgJson, SecondCarToAppletOrgDto.class);
        //补全SecondCarToAppletOrgDto
        fillSecondCarToAppletOrgDto(secondCarToAppletOrgDto);
        //设置product
        List<SecondCarToAppletProductDto> secondCarToAppletProductDtoList = new ArrayList<>();
        secondHandCarProductInfoList.forEach(p ->{
            //判断必填项
            if(checkRequiredFields(secondHandCarOrgInfo)){
                String tempProductJson = JacksonHelper.serialize(p);
                SecondCarToAppletProductDto secondCarToAppletProductDto = JacksonHelper.deserialize(tempProductJson, SecondCarToAppletProductDto.class);
                //补全SecondHandCarProductInfo
                //补全version, 每次上报都将此字段更新为当前的时间戳
                secondCarToAppletProductDto.setVersion(String.valueOf(System.currentTimeMillis()));
                secondCarToAppletProductDto.setCategory(baiduUploadConfig.getProductCategory());
                secondCarToAppletProductDto.setInfo_url(String.format(baiduUploadConfig.getProductUrl(), p.getServiceId()));
//                secondCarToAppletProductDto.setXcx_params(SecondCarToAppletXcxParamsDto.builder()
//                        .appkey(baiduUploadConfig.getAppkey())
//                        .from(baiduUploadConfig.getFrom())
//                        .params(String.format(baiduUploadConfig.getProductParams(), p.getServiceId()))
//                        .url(String.format(baiduUploadConfig.getProductUrl(), p.getServiceId()))
//                        .path(baiduUploadConfig.getProductPath())
//                        .build());
                secondCarToAppletProductDtoList.add(secondCarToAppletProductDto);
            }
        });
        secondCarToAppletOrgDto.setOrg_category(baiduUploadConfig.getOrgCategory());
        secondCarToAppletOrgDto.setOrg_sourcename(baiduUploadConfig.getOrgSourcename());
        secondCarToAppletOrgDto.setOrg_url(String.format(baiduUploadConfig.getOrgUrl(), secondCarToAppletOrgDto.getOrg_id()));
        secondCarToAppletOrgDto.setService_result(secondCarToAppletProductDtoList);

        //补全SecondCarToAppletDto
        secondCarToAppletDto.setKey(String.valueOf(System.currentTimeMillis()));//唯一标识, 此处使用时间戳
        secondCarToAppletDto.setDisplay(secondCarToAppletOrgDto);

        return secondCarToAppletDto;
    }
    private void fillSecondCarToAppletOrgDto(SecondCarToAppletOrgDto secondCarToAppletOrgDto){
        //补全version, 每次上报都将此字段更新为当前的时间戳
        secondCarToAppletOrgDto.setVersion(String.valueOf(System.currentTimeMillis()));
        secondCarToAppletOrgDto.setSiteid(baiduUploadConfig.getSiteid());
        secondCarToAppletOrgDto.setUrl("https://www.autohome.com.cn/");
        //更新省份和城市(本地和百度格式不一样,此处做转换)
        secondCarToAppletOrgDto.setOrg_province(ConvertProvinceUtil.convertToBaiduProvince(secondCarToAppletOrgDto.getOrg_province()));
        secondCarToAppletOrgDto.setOrg_city(ConvertCityUtil.convertToBaiduCity(secondCarToAppletOrgDto.getOrg_city()));
//        SecondCarToAppletXcxParamsDto secondCarToAppletXcxParamsDto = SecondCarToAppletXcxParamsDto.builder()
//                .appkey(baiduUploadConfig.getAppkey())
//                .from(baiduUploadConfig.getFrom())
//                .params(String.format(baiduUploadConfig.getOrgParams(), secondCarToAppletOrgDto.getOrg_id()))
//                .url(String.format(baiduUploadConfig.getOrgUrl(), secondCarToAppletOrgDto.getOrg_id()))
//                .path(baiduUploadConfig.getOrgPath())
//                .build();
//        secondCarToAppletOrgDto.setXcx_params(secondCarToAppletXcxParamsDto);
    }

    /**
     * wiki: https://doc.autohome.com.cn/docapi/page/share/share_p0wKahrPCi
     * baidu文档: 登录百度号,去后台下载
     * */
    private boolean uploadToBaidu(SecondCarToAppletDto secondCarToAppletDto){

        String paramJson = JacksonHelper.serialize(secondCarToAppletDto);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance(3000).httpPostJsonV2(baiduUploadConfig.getUploadUrl(), paramJson, headers);

        //记录上报日志
        saveUploadLog(secondCarToAppletDto, httpResult.getStatusCode(), httpResult.getBody());

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            //失败
            return false;
        }
        //记录日志
        BaiduResult resp = JacksonHelper.deserialize(httpResult.getBody(), BaiduResult.class);
        return resp.getCode() == 200 ? true : false;
    }

    private boolean saveUploadLog(SecondCarToAppletDto secondCarToAppletDto, int statusCode, String body){
        try{
            List<SecondCarToAppletProductDto> secondCarToAppletProductDtoList = secondCarToAppletDto.getDisplay().getService_result();
            String serviceIds = null;
            if(!CollectionUtils.isEmpty(secondCarToAppletProductDtoList)){
                serviceIds = secondCarToAppletProductDtoList.stream().map(p -> p.getService_id()).collect(Collectors.joining(","));
            }
            BaiduResult baiduResult = new BaiduResult();
            if(!StringUtils.isEmpty(body)) {
                baiduResult = JacksonHelper.deserialize(body, BaiduResult.class);
            }
            SecondHandCarUploadBaiduLog secondHandCarUploadBaiduLog = new SecondHandCarUploadBaiduLog();
            secondHandCarUploadBaiduLog.setOrgId(secondCarToAppletDto.getDisplay().getOrg_id());
            secondHandCarUploadBaiduLog.setOrgName(secondCarToAppletDto.getDisplay().getOrg_name());
            secondHandCarUploadBaiduLog.setServiceId(serviceIds);
            secondHandCarUploadBaiduLog.setStatus(secondCarToAppletDto.getDisplay().getOrg_status());
            secondHandCarUploadBaiduLog.setRequestUrl(baiduUploadConfig.getUploadUrl());
            secondHandCarUploadBaiduLog.setRequestData(JacksonHelper.serialize(secondCarToAppletDto));
            secondHandCarUploadBaiduLog.setRequestCode(String.valueOf(baiduResult.getCode()));
            secondHandCarUploadBaiduLog.setRequestStatus(statusCode);
            secondHandCarUploadBaiduLog.setResponseData(body);
            return secondHandCarUploadBaiduLogMapper.insert(secondHandCarUploadBaiduLog) > 0;
        }
        catch (Exception ex){
            log.error("saveUploadLog error", ex);
        }
        return false;
    }

    private boolean checkRequiredFields(SecondHandCarOrgInfo secondHandCarOrgInfo){
        if(StringUtils.isEmpty(secondHandCarOrgInfo.getOrgName())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgIcon())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgScore())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgProvince())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgCity())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgDistrict())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getLng())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getLat())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgId())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgServicetab())
                || StringUtils.isEmpty(secondHandCarOrgInfo.getOrgAddress())
                || "0.0".equals(secondHandCarOrgInfo.getLat())
                || "0.0".equals((secondHandCarOrgInfo.getLng()))){
            log.warn("----------checkRequiredFields org value is null, {}", JacksonHelper.serialize(secondHandCarOrgInfo));
            return false;
        }
        return true;
    }

    /**
     * 替换封面图
     * 如果封面图是打底图, 替换为第一个车源的封面图
    */
    private void replaceOrgIcon(SecondHandCarOrgInfo secondHandCarOrgInfo, List<SecondHandCarProductInfo> secondHandCarProductInfoList){
        if(CollectionUtils.isEmpty(secondHandCarProductInfoList)){
            log.warn("没有车源信息, secondHandCarOrgInfo: {}", secondHandCarOrgInfo);
            return;
        }
        //替换封面图,如果是默认打底, 替换为第一个车源的封面图
        if("https://x.autoimg.cn/2sc/2023/2023-2/saas_placeholder_375x281_5.png".equals(secondHandCarOrgInfo.getOrgIcon())){
            //获取第一个车源的封面图
            SecondHandCarProductInfo secondHandCarProductInfo = secondHandCarProductInfoList.stream().findFirst().orElse(null);
            if(secondHandCarProductInfo != null && !StringUtils.isEmpty(secondHandCarProductInfo.getPic())){
                String pic = secondHandCarProductInfo.getPic();
                pic = pic.replace("autohomecar__", "300x300_c40_autohomecar__");
                secondHandCarOrgInfo.setOrgIcon(pic);
            }
        }
    }
}
