package com.autohome.applet.service.dealer;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.dealer.SpecMinPrice;

import javax.servlet.http.HttpServletRequest;

public interface DealerNewService {

    /**
     * 商家列表接口实现
     * @param request
     * @return
     */
    ReturnValue<?>  getDealers(HttpServletRequest request);

    /**
     * 报价的商家ID列表实现
     * @param request
     * @return
     */
    ReturnValue<?>  getQuoteDealer(HttpServletRequest request);

    /**
     * 最低成交价车型接口实现
     * @param request
     * @return
     */
    ReturnValue<?>  getMinSeriesDealPriceFormat(HttpServletRequest request);

    /**
     * 获取降幅
     * @param seriesId 车系id
     * @param cityId 城市id
     * @return
     */
    ReturnValue<?>  getMinPrice(Integer seriesId, Integer cityId);
    /**
     * 获取降幅
     * @param seriesId 车系id
     * @param cityId 城市id
     * @return
     */
    SpecMinPrice getMinPriceDTO(Integer seriesId, Integer cityId);

    /**
     * 程序化弹窗接口
     * @param cityId 城市id
     * @param specId 车型id
     * @param seriesId 车系id
     * @param areaId 区域id
     * @param deviceId 用户设备id
     * @param deviceType 设备类型
     * @param appVersion App版本
     * @param traceType 透传给程序化接口
     * @return
     */
    ReturnValue<?> getListSmartAreaButton (Integer cityId, String specId, Integer seriesId, Integer areaId, String deviceId, String deviceType,String appVersion, String traceType);

}
