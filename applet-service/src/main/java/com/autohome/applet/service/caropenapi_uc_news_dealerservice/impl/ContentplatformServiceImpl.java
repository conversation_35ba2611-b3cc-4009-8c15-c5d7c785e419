package com.autohome.applet.service.caropenapi_uc_news_dealerservice.impl;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chejiahao.CheInfoDetail;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer.SeriesDealerPrice;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.dealer.SpecDealerPrice;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.ContentplatformService;
import com.autohome.applet.service.caropenapi_uc_news_dealerservice.DealerService;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.autohome.applet.util.caropenapi_uc_news_dealerservice_common.CarPriceUtils;
import com.autohome.applet.util.netcoreapi.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hanshanfeng on 2019/3/6.
 */
@Slf4j
@Service
public class ContentplatformServiceImpl implements ContentplatformService {

    @Autowired
    private DealerService dealerService;

    public ResponseContent<?> GetInfoDetail(String _appid, int infoId, boolean fromWriteDb) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", _appid);
            param.put("infoId", Integer.toString(infoId));
            param.put("fromWriteDb", Boolean.toString(fromWriteDb));
//            String result = HttpHelper.getResponse(GetInfoDetail, param, "UTF-8", 3000).getContent();
//            if (!StringUtils.isNotNullAndWhiteSpace(result)) {
//                return new ResponseContent(200, "暂无数据");
//            }
//            ResponseContent<CheInfoDetail> responseContent = JSON.parseObject(result, new TypeReference<ResponseContent<CheInfoDetail>>() {
//            });
            HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://chejiahao.api.lq.autohome.com.cn/InfoService.svc/GetInfoDetail", param);
            if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
                return new ResponseContent<>(200, "暂无数据");
            }
            ResponseContent<CheInfoDetail> responseContent = JacksonHelper.deserialize(httpResult.getBody(), new com.fasterxml.jackson.core.type.TypeReference<ResponseContent<CheInfoDetail>>() {
            });

            if (responseContent != null && responseContent.getResult() != null && responseContent.getResult().getCarmonads() != null && responseContent.getResult().getCarmonads().size() > 0) {
                //优先判断车型id车型id为0则取车系id经销商城市报价
                String specids = responseContent.getResult().getSpecids();

                int cityid = StringUtil.isBlank(responseContent.getResult().getCityid()) ? 110100 : Integer.parseInt(responseContent.getResult().getCityid());
                if (StringUtil.isNotNullAndWhiteSpace(specids) && !specids.equals("0")) {
                    ResponseContent<SpecDealerPrice> specdealerprice = dealerService.getSpecsMinPrice(_appid, cityid, specids, 0);
                    if (specdealerprice != null && specdealerprice.getResult().getList() != null && specdealerprice.getResult().getList().size() > 0) {
                        specdealerprice.getResult().getList().forEach(s -> {
                            for (CheInfoDetail.Carmonads item : responseContent.getResult().getCarmonads()) {
                                if (s.getSpecid() == item.getSpecid()) {
                                    item.setDealerprice(CarPriceUtils.GetSpecPrice(s.getNewsprice(), s.getNewsprice()));
                                    int offprice = s.getMaxoriginalprice() - s.getNewsprice();
                                    if (s.getNewsprice() > 0 && offprice > 0) {
                                        item.setOffprice(CarPriceUtils.GetSpecPrice(offprice, offprice));
                                    } else {
                                        item.setOffprice("0");
                                    }
                                }
                            }
                        });
                    }
                }
                String serieids = responseContent.getResult().getSeriesids();
                if (StringUtil.isNotNullAndWhiteSpace(serieids)) {
                    ResponseContent<List<SeriesDealerPrice>> seriesdealerprice = dealerService.getSeriesMinOriginalPriceSpec(_appid, cityid, serieids);
                    if (null != seriesdealerprice && null != seriesdealerprice.getResult() && seriesdealerprice.getResult().size() > 0) {
                        seriesdealerprice.getResult().forEach(s -> {
                            for (CheInfoDetail.Carmonads item : responseContent.getResult().getCarmonads()) {
                                if (s.getSeriesid() == item.getSeriesid()) {
                                    item.setDealerprice(CarPriceUtils.GetSpecPrice(s.getNewsprice(), s.getNewsprice()));

                                    if (s.getMaxpriceoff() > 0) {
                                        item.setOffprice(CarPriceUtils.GetSpecPrice(s.getMaxpriceoff(), s.getMaxpriceoff()));
                                    } else {
                                        item.setOffprice("0");
                                    }
                                }
                            }
                        });
                    }
                }
            }
            return responseContent;
        } catch (Exception e) {
            log.error("GetInfoDetail error", e);
            return new ResponseContent<>(500, "暂无数据");
        }
    }

}
