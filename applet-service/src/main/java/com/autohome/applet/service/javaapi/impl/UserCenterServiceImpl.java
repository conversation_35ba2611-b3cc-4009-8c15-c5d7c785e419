package com.autohome.applet.service.javaapi.impl;

import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.user.*;
import com.autohome.applet.model.dto.usercenter.AutoUserInfo;
import com.autohome.applet.model.dto.usercenter.EncryptItem;
import com.autohome.applet.model.dto.usercenter.User;
import com.autohome.applet.model.dto.usercenter.UserInfoDTO;
import com.autohome.applet.service.javaapi.UserCenterService;
import com.autohome.applet.util.*;
import com.autohome.applet.util.netcoreapi.StringUtil;
import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.HttpClientUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserCenterServiceImpl implements UserCenterService {

    private static final String URL_GET_USER_INFO_LIST = "https://user.api.autohome.com.cn/api/go_userInfo/getuserinfolist";

    @Override
    public User registerThenGetUser(String mobilePhone) {
        Map param = new HashMap<>();
        param.put("_appid", "miniprogram");
        param.put("serkey", "Kmfj5ASgfh");
        param.put("mobilePhone", mobilePhone);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPost("http://user.api.autohome.com.cn/api/a/UserLogin/RegCloudMobile", param);
        String paramJson = JacksonHelper.serialize(param);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            log.info("request registerCloudMobile url response:{}", httpResult.getBody());
            ReturnValue<User> returnValue = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<User>>() {
            });

            if (returnValue.getReturncode() == 0) {
                return returnValue.getResult();
            } else {
                log.warn("request registerCloudMobile returncode error, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
            }
        } else {
            log.warn("request registerCloudMobile url failed, params:{}, response:{}", paramJson, JacksonHelper.serialize(httpResult));
        }
        return null;
    }

    @Override
    public String encryptPhone(String mobilePhone) {
        Map<String, String> map = encrypt(Arrays.asList(mobilePhone));
        return map.get(mobilePhone);
    }

    public static Map<String, String> encrypt(List<String> datas) {
        String userName = "news-lightapp-apiRpkVlwQG";
        String password = "S588IZ6NM36X";

        String url = "http://se.api.cloud.corpautohome.com/v1/mobile/encrypt?_appid=lapp&consumerId=usrprd.dev.news&t=" + System.currentTimeMillis() / 1000;
        String authorization = new String(Base64.encodeBase64((userName + ":" + password).getBytes(StandardCharsets.UTF_8)));
        Map<String, Object> headers = new HashMap<>();
        headers.put("Authorization", "Basic " + authorization);
        String params = JacksonHelper.serialize(datas);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance(1000).httpPostJson(url, params, headers);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            Map<String, String> result = new HashMap<>();
            if (StringUtils.isBlank(httpResult.getBody())) {
                datas.forEach(x -> result.put(x, ""));
                return result;
            }
            ReturnValue<List<EncryptItem>> enResult = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<EncryptItem>>>() {
            });
            if (enResult == null || enResult.getReturncode() != 0 || enResult.getResult() == null || enResult.getResult().size() == 0) {
                datas.forEach(x -> result.put(x, ""));
                return result;
            }
            datas.forEach(item -> {
                if (enResult.getResult().stream().anyMatch(x -> x != null && x.getMobile() != null && x.getMobile().equals(item))) {
                    EncryptItem enDnItem = enResult.getResult().stream().filter(x -> x != null && x.getMobile() != null && x.getMobile().equals(item)).findFirst().orElse(null);
                    result.put(item, enDnItem == null ? "" : enDnItem.getEncrypted());
                } else {
                    result.put(item, "");
                }
            });
            return result;
        } else {
            log.warn("request user encrypt url failed, params:{}, response:{}", params, JacksonHelper.serialize(httpResult));
        }

        return new HashMap<>();

    }

    @Override
    public User getUserInfoByUserId(long userId) {

        Map<String, Object> requestMap = new HashMap<>(3);
        requestMap.put("userId", userId);
        String timestamp = Long.toString(System.currentTimeMillis() / 1000);
        requestMap.put("timestamp", timestamp);
        requestMap.put("ctoken", AESUtil.md5("nvWPt7" + timestamp));

        final String url = "http://user.api.autohome.com.cn/api/userInfo/getuserInfo?_appid=lightapp";
        ReturnValue<User> result = HttpHelper.getInstance().httpGet(url, requestMap, null, new TypeReference<ReturnValue<User>>() {
        });

        if (result == null || result.getReturncode() != 0) {
            return null;
        }

        return result.getResult();
    }

    @Override
    public User getUserInfoByPhone(String phone) {

        Map<String, Object> requestMap = new HashMap<>(1);
        requestMap.put("mobilephone", phone);

        final String url = "http://user.api.autohome.com.cn/api/userInfo/getuserInfobymobilephone?_appid=lightapp";

        ReturnValue<User> result = HttpHelper.getInstance().httpPost(url, requestMap, null, ReturnValue.class, User.class);

        if (result == null || result.getReturncode() != 0) {
            return null;
        }

        return result.getResult();
    }

    public AutoUserInfo getAutoUser(int userId) {

        String timestamp = Long.toString(System.currentTimeMillis() / 1000);
        String ctoken = StringUtil.md5("nvWPt7" + timestamp);
//        String param = ;
        String url = "http://user.api.autohome.com.cn/api/userInfo/getuserInfo?_appid=lightapp&userid=" + Integer.toString(userId) + "&ctoken=" + ctoken + "&_timestamp=" + timestamp;
        BaseModel<AutoUserInfo> result = HttpClientUtils.get(url, new TypeReference<BaseModel<AutoUserInfo>>() {
        });
        if (result == null || result.getReturncode() != 0) {
            return null;
        }
        return result.getResult();
    }

    @Override
    public ReturnValue<?> RegOrLoginByMobileCode(HttpServletRequest request) {
        final String url = "http://user.api.autohome.com.cn/api/userlogin/RegOrLoginByMobileCode";

        return postReturnValue(request, url);
    }

    @Override
    public DecryptUserResultDto decryptPcPopClubCookie(String auth) {
        String url = "http://user.api.autohome.com.cn/api/cookie/DecryptPcPopClubCookie?_appid=cms&pcpopclub=" + auth;
        BaseModel<DecryptUserDto> user = HttpClientUtils.get(url, new TypeReference<BaseModel<DecryptUserDto>>() {
        });
        if (user == null || (user.getReturncode() != 0 && user.getReturncode() != 124 && user.getReturncode() != 125)) {
            return null;
        }
        String[] ui = user.getResult().getDecryptCookie().split("[|]");
        if (ui.length != 5) {
            return null;
        }

        DecryptUserResultDto result = new DecryptUserResultDto();
        result.setUserId(Integer.parseInt(ui[0]));
        result.setUserName(ui[1]);
        return result;
    }

    @Override
    public String getPhoneNumber(int myUid, String clientIp) {
        String autoKey = "nvWPt7";
        long _timestamp = System.currentTimeMillis() / 1000;

        Map<String, Object> param = new HashMap<>();
        param.put("userids", String.valueOf(myUid));
        param.put("bizid", "yhyyyhzzqjhm");
        param.put("eventid", "0");
        param.put("cip", clientIp);
        param.put("deviceid", "0");
        param.put("_timestamp", _timestamp);
        param.put("_appid", "lightapp");
        param.put("ctoken", Md5.md5(autoKey + _timestamp));
        HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://user.api.autohome.com.cn/api/u/userinfo/getusermobile", param);
        if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
            ReturnValue<List<UserPhone>> deserialize = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<List<UserPhone>>>() {
            });
            if (deserialize != null && deserialize.getReturncode() == 0 && !CollectionUtils.isEmpty(deserialize.getResult())) {
                UserPhone userPhone = deserialize.getResult().get(0);
                return userPhone.getPhone();
            }
        }
        return null;
    }

    @Override
    public List<UserInfoDto> getUserInfos(String userIds, int myUid) {
        Map<Integer, UserRelation> userRelationMap = new HashMap<>();
        if (myUid > 0) {
            Map<String, Object> param = new HashMap<>();
            param.put("_appid", "lightapp");
            param.put("userid", myUid);
            param.put("targetuserids", userIds);
            HttpHelper.HttpResult httpGet = HttpHelper.getInstance().httpGet("http://user.api.autohome.com.cn/api/friendship/GetRelationships", param);
            if (httpGet.getStatusCode() == 200 && httpGet.getBody() != null) {
                ReturnValue<List<UserRelation>> deserialize = JacksonHelper.deserialize(httpGet.getBody(), new TypeReference<ReturnValue<List<UserRelation>>>() {
                });
                if (deserialize != null && deserialize.getReturncode() == 0 && !CollectionUtils.isEmpty(deserialize.getResult())) {
                    userRelationMap = deserialize.getResult().stream().collect(Collectors.toMap(UserRelation::getUserid, it -> it));
                }
            }
        }

        String url = "https://user.api.autohome.com.cn/api/go_userInfo/getuserinfolist";
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", "lightapp");
        param.put("useridlist", userIds);
        param.put("fields", "userid,newnickname,headimage");
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet(url, param);
        if (httpResult.getStatusCode() == 200 && httpResult.getBody() != null) {
            ReturnValue<List<UserInfo>> userResp = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<UserInfo>>>() {
            });
            if (userResp != null && userResp.getReturncode() == 0 && !CollectionUtils.isEmpty(userResp.getResult())) {
                List<UserInfoDto> list = new ArrayList<>();
                for (UserInfo userInfo : userResp.getResult()) {
                    UserInfoDto dto = new UserInfoDto();
                    dto.setUserId(userInfo.getUserId());
                    dto.setNickname(userInfo.getNewNickname());
                    String headImage = userInfo.getHeadImage();
                    if (StringUtils.isNotBlank(headImage)) {
                        if (!headImage.contains("http")) {
                            if (!headImage.contains("//i2.autoimg")) {
                                headImage = "https://i2.autoimg.cn/userscenter" + headImage;
                            }
                        }
                    } else {
                        headImage = "";
                    }
                    dto.setHeadImage(headImage);
                    UserRelation userRelation = userRelationMap.get(userInfo.getUserId());
                    dto.setRealtion(userRelation != null ? userRelation.getRelationLevel() : 0);
                    list.add(dto);
                }
                return list;
            }
        }
        return null;
    }

    @Override
    public ReturnValue<?> getInfoByauthorid(String appid, Integer page_size, String authorId, String infoType, String search_after, String v, String timestamp, String sign, int isSelf) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", appid);
        param.put("page_size", page_size);
        param.put("authorId", authorId);
        param.put("infoType", infoType);
        param.put("search_after", search_after);
        param.put("_v", v);
        param.put("_timestamp", timestamp);
        param.put("_sign", sign);
        param.put("isSelf", isSelf);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://maindata.api.autohome.com.cn/data/more/chejiahao_get_info_byauthorid", param);
        ReturnValue<Map> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Map>>() {
        });
        return result;
    }

    @Override
    public ReturnValue<?> getMyStatistics(String appid, Integer author_id) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", appid);
        param.put("author_id", author_id);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("http://la.corpautohome.com/user/get_my_statistics", param);
        ReturnValue<Map> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<Map>>() {
        });
        return result;
    }

    @Override
    public ReturnValue<?> getuserinfolist(String appid, String useridlist) {
        Map<String, Object> param = new HashMap<>();
        param.put("_appid", appid);
        param.put("fields", "userid,sex,mobilephone,nickname,newnickname,headimage,birthday,fanscount,followcount");
        param.put("useridlist", useridlist);
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpGet("https://user.api.autohome.com.cn/api/go_userInfo/getuserinfolist", param);
        ReturnValue<List<UserInfoConstellationDto>> result = JacksonHelper.deserialize(httpResult.getBody(), new TypeReference<ReturnValue<List<UserInfoConstellationDto>>>() {
        });
        if (result != null && result.getReturncode() == 0 && !CollectionUtils.isEmpty(result.getResult())) {
            List<UserInfoConstellationDto> userInfoConstellationList = result.getResult();
            for (UserInfoConstellationDto userInfoConstellation : userInfoConstellationList) {
                String birthday = userInfoConstellation.getBirthday();
                Date birthdayDate = DateHelper.deserialize(birthday, DateHelper.DATEFORMAT_ONLY_DATE);
                String constellation = getConstellation(birthdayDate);
                userInfoConstellation.setConstellation(constellation);
            }
        }
        return result;
    }

    //获取星座信息
    public static final String[] CONSTELLATION_ARR = {"水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "魔羯座"};
    public static final int[] CONSTELLATION_EDGE_DAY = {20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22};
    public String getConstellation(Date date) {
        try {
            if (date == null) {
                return "";
            }
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int month = cal.get(Calendar.MONTH);
            int day = cal.get(Calendar.DAY_OF_MONTH);
            if (day < CONSTELLATION_EDGE_DAY[month]) {
                month = month - 1;
            }
            if (month >= 0) {
                return CONSTELLATION_ARR[month];
            }
            return CONSTELLATION_ARR[11];
        } catch (Exception e) {
            log.error("getConstellation exception", e);
        }
        return "";
    }

    private ReturnValue<?> postReturnValue(HttpServletRequest request, String url) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Set<String> keySets = parameterMap.keySet();

        Map<String, Object> params = new HashMap<>();
        for (String _temp : keySets) {
            String[] values = parameterMap.get(_temp);
            params.put(_temp, values[0]);
        }


        String paramJson = JacksonHelper.serialize(params);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPost(url, params);

        if (httpResult.getStatusCode() != 200 || httpResult.getBody() == null) {
            return ReturnValue.buildErrorResult(1001, "源接口请求失败");
        }

        ReturnValue<?> resp = JacksonHelper.deserialize(httpResult.getBody(), ReturnValue.class);
        return resp;
    }

    @Override
    public List<UserInfoDTO> getUserInfoList(List<Integer> userIds) {
        if (userIds.size() > 50) {
            throw new BusinessException("单次获取用户不能超50个", 11001);
        }

        userIds = userIds.stream().distinct().collect(Collectors.toList());
        String userIdsStr = org.springframework.util.StringUtils.collectionToDelimitedString(userIds, ",");

        Map<String, Object> params = new HashMap<>();
        params.put("_appid", "user");
        params.put("fields", "userid,nickname,newnickname,headimage,mobilephone,sex");
        params.put("useridlist", userIdsStr);

        ReturnValue<List<UserInfoDTO>> returnValue = HttpHelper.getInstance()
                .httpGet(URL_GET_USER_INFO_LIST, params, null,
                        new TypeReference<ReturnValue<List<UserInfoDTO>>>() {
                        });
        if (returnValue != null && returnValue.getReturncode() == 0 && returnValue.getResult() != null) {
            return returnValue.getResult();
        }
        return new ArrayList<>();
    }

}
