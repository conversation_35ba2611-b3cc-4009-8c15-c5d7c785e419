package com.autohome.applet.service.qrcode;

import com.autohome.applet.dao.javaapi.model.qrcode.QrCodeModel;

import java.util.List;

public interface QrCodeCleanService {
    List<QrCodeModel> getByPage(long lsatId,long maxId,int pageSize, int shardingIndex, int shardingTotal,boolean isDel);

    boolean cleanQrCodeAndImg(QrCodeModel qrCodeModel,boolean logInfo);

    boolean cleanQrCodeImg(QrCodeModel qrCodeModel,boolean logInfo);

    boolean updateQrCodeStatus(long qrCodeId,int status);

    int updateQrCodeStatus(List<Long> qRCodeIds, int status);
}
