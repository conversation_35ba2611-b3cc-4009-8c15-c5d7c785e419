package com.autohome.applet.service.impl;

import com.autohome.applet.dao.javaapi.mapper.ShortVideoPush2345LogMapper;
import com.autohome.applet.dao.javaapi.model.ShortVideoPush2345Log;
import com.autohome.applet.model.dto.BusinessException;
import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.ShortVideo.VideoAppletDto;
import com.autohome.applet.model.dto.ShortVideo.VideoResultHitsDto;
import com.autohome.applet.model.dto.ShortVideo.VideoSourceDto;
import com.autohome.applet.service.HttpService;
import com.autohome.applet.service.ShortVideoPushService;
import com.autohome.applet.service.utils.UtlisFor2345;
import com.autohome.applet.util.DateHelper;
import com.autohome.applet.util.HttpHelper;
import com.autohome.applet.util.JacksonHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShortVideoPushServiceImpl implements ShortVideoPushService {
    @Autowired
    private HttpService httpService;
    @Autowired
    private ShortVideoPush2345LogMapper shortVideoPush2345LogMapper;

    @Value("${duanshipin_2345.upload.url}")
    private String UPLOADURL;

    //private String UPLOADURL = "http://v-api.2345.com/shortVideo/interface/pushData";
    @Override
    public List<VideoAppletDto> getVideoApplet(String startTime , String endTime) {
        Map<String, Object> param = new HashMap<>();
        try {
            VideoResultHitsDto videoHitAppletDtoPageNum1List = getVideoHitAppletDtoList(startTime, endTime, 1);
            List<VideoAppletDto> AllHits = videoHitAppletDtoPageNum1List.getHits().getHits();
            int total = videoHitAppletDtoPageNum1List.getHits().getTotal();
            //int total = 201;
            if (total / PAGESIZE > 0){
                for (int i = 2; i <= total / PAGESIZE + 1; i++) {
                    VideoResultHitsDto videoHitAppletDtoList = getVideoHitAppletDtoList(startTime, endTime, i);
                    List<VideoAppletDto> hits = videoHitAppletDtoList.getHits().getHits();
                    if (!CollectionUtils.isEmpty(hits)){
                        AllHits.add(hits.get(0));
                    }
                }
            }
            return AllHits;
        } catch (BusinessException e) {
            log.error("getVideoApplet ", e);
        }
        return null;
    }

    private VideoResultHitsDto getVideoHitAppletDtoList(String startTime , String endTime ,Integer pageNum){
        Map<String, Object> param = new HashMap<>();
        String url = "http://la.corpautohome.com/userincr/video_applet?" +
                "_appid=*&end_time="
                + endTime + "&begin_time=" + startTime +
                "&main_data_type=" + VIDEO
                +"&page_size="+ PAGESIZE
                +"&page_num="+ pageNum;
        VideoResultHitsDto videoHitAppletDtoList = httpService.httpGetForReturnValue(url, param
                , new TypeReference<ReturnValue<VideoResultHitsDto>>() {
                });
        if(videoHitAppletDtoList == null){
            return null;
        }
        log.info("request getVideoHitAppletDtoList url:{},response:{},total:{}", url,videoHitAppletDtoList.getHits().getHits(),videoHitAppletDtoList.getHits().getTotal());
        return videoHitAppletDtoList;
    }

    @Override
    public void uploadToShortVideoPush(List<VideoAppletDto> videoAppletDtoList , String startTime , String endTime) {

        ArrayList<VideoAppletDto> videoAppletDtosAddList = new ArrayList<>();
        ArrayList<VideoAppletDto> videoAppletDtosDelList = new ArrayList<>();
        for (VideoAppletDto videoAppletDto : videoAppletDtoList) {
            VideoSourceDto source = videoAppletDto.getSource();
            if (source.getIsPublish() == 1 && source.getIsDelete() == 0 && source.getParentBizId() != 6){
                videoAppletDtosAddList.add(videoAppletDto);
            }else {
                videoAppletDtosDelList.add(videoAppletDto);
            }
        }

        //新增 + 修改
        uploadToShortVideoAdd(videoAppletDtosAddList, "add", startTime, endTime);
        //删除 + 下线
        uploadToShortVideoAdd(videoAppletDtosDelList, "del", startTime, endTime);

    }

    private void uploadToShortVideoAdd(List<VideoAppletDto> videoAppletDtoList, String type, String startTime, String endTime){
        if (!CollectionUtils.isEmpty(videoAppletDtoList)){
            int addListPages = videoAppletDtoList.size() / SIZE + 1;
            if (videoAppletDtoList.size() > SIZE){
                for (int i=0; i<addListPages; i++){
                    List<VideoAppletDto> videoAppletDtos = videoAppletDtoList.stream().skip(i * SIZE)
                            .limit(SIZE)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(videoAppletDtoList)){
                        continue;
                    }
                    //推送短视频平台
                    boolean resultStatus = uploadToShortVideo(videoAppletDtos,type , startTime , endTime);
                }
            }else {
                //推送短视频平台
                boolean resultStatus = uploadToShortVideo(videoAppletDtoList,type , startTime , endTime);
            }
        }
    }


    private boolean uploadToShortVideo(List<VideoAppletDto> videoAppletDtos , String type , String startTime , String endTime) {
        String secretKey = SECRETKEY;
        Map<String, Object> data = new HashMap<>();
        data.put("type", type);
        data.put("source", SOURCE);
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (VideoAppletDto videoAppletDto : videoAppletDtos) {
            VideoSourceDto source = videoAppletDto.getSource();
            if (StringUtils.isEmpty(source.getPcUrl())){
                continue;
            }
            Map<String, Object> item = new HashMap<>();
            item.put("title",source.getTitle());    //标题
            String pcurl = source.getPcUrl().split("#")[0] + "#pvareaid=6861016";
            item.put("link",pcurl);     //URL
            String imgUrl = source.getImgUrl();
            String newImgUrl = imgUrl.replace("160x90", "550x310");
            String newHttpImgUrl = newImgUrl.replace("www2.autoimg.cn", "mall3.autoimg.cn");
            item.put("images",newHttpImgUrl);   //封面图  //todo 需要确认封面图是16x9还是4x3
            String publishTime = source.getPublishTime();
            Date publishDate = DateHelper.deserialize(publishTime, DateHelper.DATEFORMAT_NEW);
            String publishDateStr = DateHelper.serialize(publishDate, DateHelper.DATEFORMAT_STANDARD);
            item.put("date",publishDateStr);   //日期
            item.put("categoryName",source.getMainDataType());  //分类  原创视频、车家号精选/论坛精选、视频广场
            Integer duration = source.getDuration();
            String secondsToHMS = DateHelper.secondsToHMS(duration);
            item.put("duration",secondsToHMS);//播放时长
            item.put("author",source.getAuthorName());  //作者
            item.put("tab",source.getVideoOriginalTagNames()); //标签
            item.put("play_count",source.getVv() == null ?  0 : source.getVv() );    //播放量
            item.put("car_series",source.getSeriesIds());   //车系
            item.put("describe",source.getSummary());      //简介
            dataList.add(item);
        }

        data.put("timestamp", System.currentTimeMillis());
        // 生成签名
        String signature = UtlisFor2345.generateSignature(data, secretKey);
        data.put("data", dataList);
        // 将签名添加到请求数据中
        data.put("signature", signature);
        // 将请求数据转换为 JSON 字符串
        String body = JacksonHelper.serialize(data);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        ShortVideoPush2345Log shortVideoPush2345Log = saveShortVideoPush2345Log(videoAppletDtos, type, JacksonHelper.serialize(dataList), startTime, endTime);

        // 发送 POST 请求并获取响应
        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJsonV2(UPLOADURL, body, headers);
        if (httpResult.getStatusCode() == 200) {
            //推送日志信息
            updateShortVideoPush2345Log(shortVideoPush2345Log,httpResult);
            return true;
        } else {
            updateShortVideoPush2345Log(shortVideoPush2345Log,httpResult);
            return false;
        }
    }

    private ShortVideoPush2345Log saveShortVideoPush2345Log(List<VideoAppletDto> videoAppletDtos , String type , String body , String startTime , String endTime){
        ShortVideoPush2345Log shortVideoPush2345Log = new ShortVideoPush2345Log();
        List<VideoSourceDto> videoSourceDtolist = videoAppletDtos.stream().map(VideoAppletDto :: getSource).collect(Collectors.toList());
        String authorIds = videoSourceDtolist.stream().map(m -> String.valueOf(m.getAuthorId())).collect(Collectors.joining(","));
        String bizIds = videoSourceDtolist.stream().map(m -> String.valueOf(m.getBizId())).collect(Collectors.joining(","));
        try {
            shortVideoPush2345Log.setAuthorIds(authorIds);
            shortVideoPush2345Log.setBizIds(bizIds);
            shortVideoPush2345Log.setRequestData(body);
            shortVideoPush2345Log.setMainDataType(VIDEO);
            shortVideoPush2345Log.setPushType(type);
            shortVideoPush2345Log.setSynBeginTime(startTime);
            shortVideoPush2345Log.setSynEndTime(endTime);
            shortVideoPush2345Log.setRequestUrl(UPLOADURL);
            shortVideoPush2345LogMapper.insert(shortVideoPush2345Log);
        } catch (Exception ex) {
            log.error("saveShortVideoPush2345Log error, :", ex);
        }
        return shortVideoPush2345Log;
    }

    private ShortVideoPush2345Log updateShortVideoPush2345Log(ShortVideoPush2345Log shortVideoPush2345Log , HttpHelper.HttpResult httpResult){
        try {
            Map deserialize = JacksonHelper.deserialize(httpResult.getBody(), Map.class);
            shortVideoPush2345Log.setRequestCode(String.valueOf(deserialize.get("msg")));
            shortVideoPush2345Log.setRequestStatus(Integer.valueOf(deserialize.get("code").toString()));
            shortVideoPush2345Log.setResponseData(String.valueOf(deserialize.get("data")));
            shortVideoPush2345LogMapper.updateByPrimaryKey(shortVideoPush2345Log);
        } catch (Exception ex) {
            log.error("updateShortVideoPush2345Log error, :", ex);
        }
        return shortVideoPush2345Log;
    }

}
