package com.autohome.applet.service.resourcefeedpush.impl;

import java.util.Date;

import com.autohome.applet.dao.javaapi.mapper.HistoryDouyinSitemapMapper;
import com.autohome.applet.dao.javaapi.model.HistoryDouyinSitemap;
import com.autohome.applet.dao.javaapi.model.dto.PushResourceFeedCountDTO;
import com.autohome.applet.model.dto.douyin.DouYinBaseReturn;
import com.autohome.applet.model.dto.resourcefeedpush.PushAtomicResultDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataDTO;
import com.autohome.applet.model.dto.resourcefeedpush.PushResourceFeedDataQuery;
import com.autohome.applet.model.enums.MainDataTypeToBizTypeEnum;
import com.autohome.applet.service.douyin.DouYinService;
import com.autohome.applet.service.douyin.impl.DouYinServiceImpl;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedPushService;
import com.autohome.applet.service.resourcefeedpush.ResourceFeedService;
import com.autohome.applet.util.DateHelper;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@Slf4j
public class ResourceFeedPushToDBServiceImpl implements ResourceFeedPushService {
    @Autowired
    private ResourceFeedService resourceFeedService;
    @Autowired
    private DouYinService douYinService;

    @Autowired
    private HistoryDouyinSitemapMapper historyDouyinSitemapMapper;
    private Set<String> daySitemap = new HashSet<>();

    /**
     * 计数器
     */
    private PushAtomicResultDTO atomicPushAtomicResultDTO;
    private HashMap<String, PushAtomicResultDTO> atomicResultDTOHashMap;

    @Override
    public void doPush(PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        atomicResultDTOHashMap = new HashMap<>();
        atomicPushAtomicResultDTO = new PushAtomicResultDTO(0);
        List<PushResourceFeedCountDTO> pushResourceFeedCountDTOList = resourceFeedService.countPushResourceFeedDataDTO(pushResourceFeedDataQuery);
        pushResourceFeedCountDTOList.forEach(obj -> {
            atomicResultDTOHashMap.put(obj.getBizType(), new PushAtomicResultDTO(obj.getCount()));
            atomicPushAtomicResultDTO.setTotal(atomicPushAtomicResultDTO.getTotal() + obj.getCount());
        });

        sendLog(String.format("ResourceFeedDBPushService 推送时间：%s %s, 推送类型：%s, 推送步长：%s, 需要推送总数：%s", pushResourceFeedDataQuery.getStarttime(), pushResourceFeedDataQuery.getEndtime(), pushResourceFeedDataQuery.getBizTypeList(), pushResourceFeedDataQuery.getCount(), atomicPushAtomicResultDTO.getTotal()));
        sendLog(2, pushResourceFeedDataQuery.getBizTypeList(), pushResourceFeedDataQuery);
        //执行push操作
        if (atomicPushAtomicResultDTO.getTotal() > 0) {
            push(pushResourceFeedDataQuery);
        }

        sendLog(1, pushResourceFeedDataQuery.getBizTypeList(), pushResourceFeedDataQuery);
    }

    private void push(PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        List<PushResourceFeedDataDTO> pushResourceFeedDataDTOList;
        do {
            List<HistoryDouyinSitemap> douyinList = new ArrayList<>();

            //获取feed数据流
            pushResourceFeedDataDTOList = resourceFeedService.listPushResourceFeedDataDTO(pushResourceFeedDataQuery);
            if (!CollectionUtils.isEmpty(pushResourceFeedDataDTOList)) {
                //更新计数器
                String bizType = "";
                for (PushResourceFeedDataDTO ext : pushResourceFeedDataDTOList) {
                    bizType = ext.getBizType();
                    PushAtomicResultDTO pushAtomicResultDTO = atomicResultDTOHashMap.get(ext.getBizType());
                    String key = pushResourceFeedDataQuery.getStarttime() + "-" + ext.getBizType() + "-" + ext.getIsDelete() + "-" + ext.getBizId();

                    if (!daySitemap.contains(key)) {
                        daySitemap.add(key);
                        String bizSitMap = getBizSitMap(ext);
                        if (bizSitMap.equals("")) {
                            log.error("没有获取到对应sitemap，请及时关注 bizType={}", bizType);
                            continue;
                        }
                        if (!ext.getIsDelete().equals("1")) {
                            pushAtomicResultDTO.getPushDone().addAndGet(1);
                            HistoryDouyinSitemap row = new HistoryDouyinSitemap();
                            row.setPagepath(bizSitMap);
                            row.setSource(Integer.parseInt(bizType));
                            row.setStatus(0);
                            row.setIsDelete(0);
                            row.setCreatedStime(DateHelper.deserialize(ext.getPublishTime(),DateHelper.DATEFORMAT_STANDARD) );
                            row.setModifiedStime(new Date());
                            douyinList.add(row);
                        }
                    } else {
                        pushAtomicResultDTO.getIgnore().addAndGet(1);
                    }
                }
                PushAtomicResultDTO pushAtomicResultDTO = atomicResultDTOHashMap.get(bizType);

                //推送新增数据
                if (douyinList.size() >= 1) {
                    //保存到数据库
                    int insert = historyDouyinSitemapMapper.insertBatch(douyinList);

                    if (insert == 0) {
                        pushAtomicResultDTO.getPushError().addAndGet(douyinList.size());
                        pushAtomicResultDTO.getSuccess().addAndGet(douyinList.size());
                    }
                }
                //设置offset
                if (pushResourceFeedDataDTOList.size() > 0) {
                    pushResourceFeedDataQuery.setOffset(pushResourceFeedDataQuery.getOffset() + pushResourceFeedDataDTOList.size());
                }
                sendLog(String.format("ResourceFeedDBPushService 已推送: %s, 当前偏移量: %s, 最后一条id: %s", pushAtomicResultDTO.getPushDone().get(), pushResourceFeedDataQuery.getOffset(), pushResourceFeedDataDTOList.get(pushResourceFeedDataDTOList.size() - 1).getId()));
            }
        } while (!CollectionUtils.isEmpty(pushResourceFeedDataDTOList));
    }

    private String getBizSitMap(PushResourceFeedDataDTO dto) {
        String url = "";
        switch (dto.getBizType()) {
            case "1":
            case "5":
            case "12":
            case "13":
            case "33": {
                url = String.format(MainDataTypeToBizTypeEnum.fromCode(dto.getBizType()).getSiteMap()
                        , dto.getBizId());
                break;
            }
            default: {

            }
        }
        return url;
    }

    private void sendLog(int type, List<Integer> bizList, PushResourceFeedDataQuery pushResourceFeedDataQuery) {
        for (Integer i : bizList) {
            String key = String.valueOf(i);
            String des = MainDataTypeToBizTypeEnum.fromCode(key).getDes();
            PushAtomicResultDTO pushAtomicResultDTO = atomicResultDTOHashMap.get(key);
            if (pushAtomicResultDTO == null) {
                pushAtomicResultDTO = new PushAtomicResultDTO(0);
            }
            String logContent = "";
            switch (type) {
                case 1: {
                    logContent = String.format("ResourceFeedDBService 推送时间：%s %s, 数据类别: %s-%s, 新增需推送: %s, 实际推送: %s,删除需推送: %s, 新增有效推送: %s,删除有效推送: %s, 忽略: %s, tag 忽略: %s, 推送报错: %s, 报错: %s"
                            , pushResourceFeedDataQuery.getStarttime(), pushResourceFeedDataQuery.getEndtime(), key, des, pushAtomicResultDTO.getTotal(),
                            pushAtomicResultDTO.getPushDone().get(), pushAtomicResultDTO.getDelPushDone().get(), pushAtomicResultDTO.getSuccess().get(),
                            pushAtomicResultDTO.getDelSuccess().get(), pushAtomicResultDTO.getIgnore().get()
                            , pushAtomicResultDTO.getTagIgnore().get(), pushAtomicResultDTO.getPushError().get(), pushAtomicResultDTO.getError().get());
//
//                    douYinService.addDouYinSiteMapRedis(DouYinServiceImpl.DOUYIN_SITEMAP_KEY,Integer.parseInt(key), 1, 0, pushAtomicResultDTO.getPushDone().get());
//                    douYinService.addDouYinSiteMapRedis(DouYinServiceImpl.DOUYIN_SITEMAP_KEY,Integer.parseInt(key), 1, 1, pushAtomicResultDTO.getSuccess().get());
//                    douYinService.addDouYinSiteMapRedis(DouYinServiceImpl.DOUYIN_SITEMAP_KEY,Integer.parseInt(key), 1, 2, pushAtomicResultDTO.getPushError().get());
//
//                    douYinService.addDouYinSiteMapRedis(DouYinServiceImpl.DOUYIN_SITEMAP_KEY,Integer.parseInt(key), 2, 0, pushAtomicResultDTO.getDelPushDone().get());
//                    douYinService.addDouYinSiteMapRedis(DouYinServiceImpl.DOUYIN_SITEMAP_KEY,Integer.parseInt(key), 2, 1, pushAtomicResultDTO.getDelSuccess().get());
//                    douYinService.addDouYinSiteMapRedis(DouYinServiceImpl.DOUYIN_SITEMAP_KEY,Integer.parseInt(key), 2, 2, pushAtomicResultDTO.getError().get());
                    break;
                }
                case 2: {
                    logContent = String.format("ResourceFeedDBService 推送时间：%s %s, 数据类别: %s, 需推送: %s", pushResourceFeedDataQuery.getStarttime(), pushResourceFeedDataQuery.getEndtime(), key, pushAtomicResultDTO.getTotal());
                    break;
                }
            }
            XxlJobHelper.log(logContent);
            log.info(logContent);
        }
    }

    private void sendLog(String logContent) {
        XxlJobHelper.log(logContent);
        log.info(logContent);
    }

    private int getIntegerValue(String value) {
        try {
            int result = Integer.valueOf(value);
            return result < 0 ? 0 : result;
        } catch (Exception ex) {
            return 0;
        }
    }
}
