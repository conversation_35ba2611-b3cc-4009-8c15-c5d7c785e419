package com.autohome.applet.service.caropenapi_uc_news_dealerservice;

import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.ResponseContent;
import com.autohome.applet.model.dto.caropenapi_uc_news_dealerservice.chat.BusinessInfo;

import java.util.List;

public interface ChatService {
    List<BusinessInfo> getHomeChatsByFctId(int fctid, String state, String _appid);

    ResponseContent<?> getmaintainbyspecid(String specId);

    ResponseContent<?> getAllSeriesChatsByFctId(int fctid, String state, String _appid);

    ResponseContent<?> getRescuePhoneAndDealer(int fctid);
}
