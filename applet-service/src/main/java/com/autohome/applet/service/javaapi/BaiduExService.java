package com.autohome.applet.service.javaapi;

import com.autohome.applet.model.dto.ReturnValue;
import com.autohome.applet.model.dto.baiduex.FaultLightDao;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface BaiduExService {

    ReturnValue<?> getNumberPlateInfo() throws IOException;

    ReturnValue<?> getfaultLightInfo() throws IOException;

    ReturnValue<?> getfaultLightInfoById(int id) throws IOException;

    ReturnValue<?> updateFaultLightInfo(FaultLightDao[] faultLight);

    List<Map<String, Object>> analyzeFaultLight(String uuid, String base64Img) throws IOException;
}
