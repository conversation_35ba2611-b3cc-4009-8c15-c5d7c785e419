package com.autohome.applet.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class BrandForQQUtil {
    private static final List<BrandForQQ> brandForQQList = new ArrayList<>();
    private static final List<BrandForQQ> brandForQQListV2 = new ArrayList<>();
    private static final List<BrandForQQ> brandForQQListV3 = new ArrayList<>();
    private static final List<BrandForQQ> brandForQQListV3202501 = new ArrayList<>();

    static {
        brandForQQList.addAll(LoadLoaclFileFaultLightContent());
        brandForQQListV2.addAll(LoadLoaclFileFaultLightContentV2());
        brandForQQListV3.addAll(LoadLoaclFileFaultLightContentV3());
        brandForQQListV3202501.addAll(LoadLoaclFileFaultLightContentV3202501());
    }
    private static List<BrandForQQ> LoadLoaclFileFaultLightContent() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/brand_qq.xlsx");
            List<BrandForQQ> brandForQQList = ExcelUtils.read(path, BrandForQQ.class);
            log.info("load brand success, size:{}", brandForQQList.size());
            return brandForQQList;
        }
        catch (Exception ex){
            log.error("load brand info error, ", ex);
        }
        return new ArrayList<>();
    }
    private static List<BrandForQQ> LoadLoaclFileFaultLightContentV2() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/brand_qq_v2.xlsx");
            List<BrandForQQ> brandForQQList = ExcelUtils.read(path, BrandForQQ.class);
            log.info("load brand_v2 success, size:{}", brandForQQList.size());
            return brandForQQList;
        }
        catch (Exception ex){
            log.error("load brand_v2 info error, ", ex);
        }
        return new ArrayList<>();
    }

    private static List<BrandForQQ> LoadLoaclFileFaultLightContentV3() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/brand_qq_v3.xlsx");
            List<BrandForQQ> brandForQQList = ExcelUtils.read(path, BrandForQQ.class);
            log.info("load brand_v3 success, size:{}", brandForQQList.size());
            return brandForQQList;
        }
        catch (Exception ex){
            log.error("load brand_v3 info error, ", ex);
        }
        return new ArrayList<>();
    }

    private static List<BrandForQQ> LoadLoaclFileFaultLightContentV3202501() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/brand_qq_v3_202501.xlsx");
            List<BrandForQQ> brandForQQList = ExcelUtils.read(path, BrandForQQ.class);
            log.info("load brand_v3_202501 success, size:{}", brandForQQList.size());
            return brandForQQList;
        }
        catch (Exception ex){
            log.error("load brand_v3_202501 info error, ", ex);
        }
        return new ArrayList<>();
    }

    public static List<BrandForQQ> getAllBrandForQQ(){
        return brandForQQList;
    }

    public static List<BrandForQQ> getAllBrandForQQV2(){
        return brandForQQListV2;
    }
    public static List<BrandForQQ> getAllBrandForQQV3(){
        return brandForQQListV3;
    }
    public static List<BrandForQQ> getAllBrandForQQV3202501(){
        return brandForQQListV3202501;
    }

    public static class BrandForQQ{
        private String brandId;
        private String brandName;
        private String query;
        private String cityCode;
        private List<String> cityCodeList;

        public String getBrandId() {
            return brandId;
        }

        public void setBrandId(String brandId) {
            this.brandId = brandId;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public String getCityCode() {
            return cityCode;
        }

        public void setCityCode(String cityCode) {
            this.cityCode = cityCode;
        }

        public List<String> getCityCodeList() {
            if(StringUtils.isNotEmpty(this.cityCode)){
                String[] cityCodes = this.cityCode.split("\n");
                return Arrays.asList(cityCodes);
            }
            return new ArrayList<>();
        }

        public void setCityCodeList(List<String> cityCodeList) {
            this.cityCodeList = cityCodeList;
        }
    }

    public static void main(String[] args) {
    }
}
