package com.autohome.applet.util;

import com.autohome.applet.model.dto.DingDingModel;
import com.google.common.base.Preconditions;
import lombok.SneakyThrows;
import org.springframework.util.ObjectUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

public class DingDingUtil {

    public static final String GROUP_FEEDS = "https://oapi.dingtalk.com/robot/send?access_token=ecfe18bc3fb600af41332091b19691765291fcc1f70f80965cffb9c62b69be37";

    public static void send(String message, String... mobile) {
        sendWithGroupName(GROUP_FEEDS, message, mobile);
    }


    public static void sendWithGroupName(String dingdingGroupName, String message, String... mobile) {
        Preconditions.checkNotNull(mobile);
        Preconditions.checkState(mobile.length > 0, "mobile is required");
        DingDingModel dingModel = new DingDingModel();
        dingModel.setMsgtype("text");

        DingDingModel.TextBean textBean = new DingDingModel.TextBean();
        textBean.setContent(message);
        dingModel.setText(textBean);

        DingDingModel.AtBean atBean = new DingDingModel.AtBean();
        atBean.setAtMobiles(Arrays.asList(mobile));

        dingModel.setAt(atBean);

        doSend(dingdingGroupName, dingModel);
    }

    public static void sendAll(String message) {
        sendAll(GROUP_FEEDS, message);
    }

    public static void sendAll(String dingdingGroupName, String message) {


        DingDingModel dingModel = new DingDingModel();
        dingModel.setMsgtype("text");

        DingDingModel.TextBean textBean = new DingDingModel.TextBean();
        textBean.setContent(message);
        dingModel.setText(textBean);

        DingDingModel.AtBean atBean = new DingDingModel.AtBean();
        atBean.setIsAtAll(true);

        dingModel.setAt(atBean);

        doSend(dingdingGroupName, dingModel);
    }

    public static void doSend(String dingdingGroupName, DingDingModel dingDingModel) {
        String url = dingdingGroupName;
        HttpHelper.getInstance().httpPostJson(url, JacksonHelper.serialize(dingDingModel));
    }

    @SneakyThrows
    public static boolean sendMarkdownMessage(String groupApi, String secret, String markdownTitle, String markdownText, String... mobiles) {
        long timestamp = System.currentTimeMillis();
        String url = groupApi + "&timestamp=" + timestamp + "&sign=" + sign(timestamp, secret);

        DingDingModel dingModel = new DingDingModel();
        dingModel.setMsgtype("markdown");

        DingDingModel.Markdown markdown = new DingDingModel.Markdown();
        markdown.setTitle(markdownTitle);
        markdown.setText(markdownText);
        dingModel.setMarkdown(markdown);

        DingDingModel.AtBean atBean = new DingDingModel.AtBean();
        atBean.setIsAtAll(true);
        if (!ObjectUtils.isEmpty(mobiles)) {
            atBean.setAtMobiles(Arrays.asList(mobiles));
        }
        dingModel.setAt(atBean);

        HttpHelper.HttpResult httpResult = HttpHelper.getInstance().httpPostJson(url, JacksonHelper.serialize(dingModel));
        return httpResult.getStatusCode() == 200;
    }

    private static String sign(long timestamp, String secret) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));

        String sign = URLEncoder.encode(Base64.getEncoder().encodeToString(signData), "UTF-8");
        return sign;
    }


}
