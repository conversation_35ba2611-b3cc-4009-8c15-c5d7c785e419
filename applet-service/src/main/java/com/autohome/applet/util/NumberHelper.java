package com.autohome.applet.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

public class NumberHelper {
    public static int parseInt(String s) {
        return parseInt(s, 0);
    }

    public static Integer parseInt(String s, int def) {
        try {
            return Integer.parseInt(s);
        } catch (NumberFormatException e) {
            return def;
        }
    }

    public static Long parseLong(String s, long def) {
        try {
            return Long.parseLong(s);
        } catch (NumberFormatException e) {
            return def;
        }
    }

    public static String removePoint(String s , Integer def){
        DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
        try {
            return decimalFormat.format(new BigDecimal(s));
        } catch (Exception e) {
            return decimalFormat.format(def);
        }
    }

    /*
     * 如果是小数，保留两位，非小数，保留整数
     * @param number
     */
    public static String getDoubleString(double number) {
        String numberStr;
        if (((int) number * 1000) == (int) (number * 1000)) {
            //如果是一个整数
            numberStr = String.valueOf((int) number);
        } else {
            DecimalFormat df = new DecimalFormat("######0.00");
            numberStr = df.format(number);
        }
        return numberStr;
    }

    public static Integer getSafeByteVal(Integer val){
        if(!Objects.isNull(val))
        {
            if (val > 128) {
                return 128;
            } else if (val < -128) {
                return -128;
            }
            return val;
        }
        return 0;
    }

    public static Integer getSafeUnSignedByteVal(Integer val){
        if(!Objects.isNull(val))
        {
            if (val > 256) {
                return 256;
            } else if (val < 0) {
                return 0;
            }
            return val;
        }
        return 0;
    }

    public static int parseDoubleStrToInt (String str,int def){

        try {
            Double d = Double.parseDouble(str);
            return d.intValue();
        } catch (Exception e) {
            return def;
        }

    }

    public static double parseDouble (String str,double def){

        try {
            return Double.parseDouble(str);
        } catch (Exception e) {
            return def;
        }

    }

}
