package com.autohome.applet.util;

import com.autohome.applet.model.dto.baidu.BaiduOrgIdDistrict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ConvertDistrictUtil {
    private static final List<BaiduOrgIdDistrict> baiduOrgIdDistrictList = new ArrayList<>();

    static {
        baiduOrgIdDistrictList.addAll(LoadLoaclFileFaultLightContent());
    }
    private static List<BaiduOrgIdDistrict> LoadLoaclFileFaultLightContent() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/baidu-orgid-district.xlsx");
            List<BaiduOrgIdDistrict> baiduOrgIdDistrictList = ExcelUtils.read(path, BaiduOrgIdDistrict.class);
            log.info("load baidu success, size:{}", baiduOrgIdDistrictList.size());
            return baiduOrgIdDistrictList;
        }
        catch (Exception ex){
            log.error("load baidu district error, ", ex);
        }
        return new ArrayList<>();
    }

    public static String convertToBaiduDistrict(String orgId) {
        if(!CollectionUtils.isEmpty(baiduOrgIdDistrictList)){
            BaiduOrgIdDistrict baiduOrgIdDistrict = baiduOrgIdDistrictList.stream().filter(d->d.getOrgId().equals(orgId)).findAny().orElse(null);
            if(baiduOrgIdDistrict!=null){
                return baiduOrgIdDistrict.getDistrict();
            }
        }
        log.warn("no district, orgid:{}", orgId);
        return "";
    }

    public static void main(String[] args) {
        String a = convertToBaiduDistrict("323010");
    }
}
