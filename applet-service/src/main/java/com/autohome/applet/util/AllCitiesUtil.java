package com.autohome.applet.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AllCitiesUtil {
    private static final List<CityItem> cityItemList = new ArrayList<>();
    private static final List<CityItem> cityItemListV2 = new ArrayList<>();

    static {
        cityItemList.addAll(LoadLoaclFileFaultLightContent());
        cityItemListV2.addAll(LoadLoaclFileFaultLightContentV2());
    }
    private static List<CityItem> LoadLoaclFileFaultLightContent() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/citys.xlsx");
            List<CityItem> cityItemListList = ExcelUtils.read(path, CityItem.class);
            log.info("load city success, size:{}", cityItemListList.size());
            return cityItemListList;
        }
        catch (Exception ex){
            log.error("load city info error, ", ex);
        }
        return new ArrayList<>();
    }
    private static List<CityItem> LoadLoaclFileFaultLightContentV2() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/citys_v2.xlsx");
            List<CityItem> cityItemListList = ExcelUtils.read(path, CityItem.class);
            log.info("load city v2 success, size:{}", cityItemListList.size());
            return cityItemListList;
        }
        catch (Exception ex){
            log.error("load city v2 info error, ", ex);
        }
        return new ArrayList<>();
    }

    public static List<CityItem> getAllCity(){
        return cityItemList;
    }

    public static List<CityItem> getAllCityV2(){
        return cityItemListV2;
    }

    public static class CityItem{
        private String cityId;
        private String cityName;
        private String provinceId;
        private String provinceName;

        public String getProvinceName() {
            return provinceName;
        }

        public void setProvinceName(String provinceName) {
            this.provinceName = provinceName;
        }

        public String getCityId() {
            return cityId;
        }

        public void setCityId(String cityId) {
            this.cityId = cityId;
        }

        public String getCityName() {
            return cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }

        public String getProvinceId() {
            return provinceId;
        }

        public void setProvinceId(String provinceId) {
            this.provinceId = provinceId;
        }
    }

    public static void main(String[] args) {
    }
}
