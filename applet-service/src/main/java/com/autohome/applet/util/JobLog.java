package com.autohome.applet.util;

import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.log.XxlJobFileAppender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class JobLog {
    private static final Logger logger = LoggerFactory.getLogger("xxl-job logger");

    public static void info(String appendLog) {
        String formatAppendLog = formatLog(appendLog);
        if (XxlJobContext.getXxlJobContext() == null) {
            logger.info(appendLog);
            return;
        }
        String jobLogFileName = XxlJobContext.getXxlJobContext() == null ? null : XxlJobContext.getXxlJobContext().getJobLogFileName();
        if (jobLogFileName != null && jobLogFileName.trim().length() != 0) {
            XxlJobFileAppender.appendLog(jobLogFileName, formatAppendLog);
            logger.info("[{}]: {}", jobLogFileName, appendLog);
        } else {
            logger.info(appendLog);
        }
    }

    public static void warn(String appendLog) {
        String formatAppendLog = formatLog(appendLog);
        if (XxlJobContext.getXxlJobContext() == null) {
            logger.warn(appendLog);
            return;
        }
        String jobLogFileName = XxlJobContext.getXxlJobContext().getJobLogFileName();
        if (jobLogFileName != null && jobLogFileName.trim().length() != 0) {
            XxlJobFileAppender.appendLog(jobLogFileName, formatAppendLog);
            logger.warn("[{}]: {}", jobLogFileName, appendLog);
        } else {
            logger.warn(appendLog);
        }
    }

    public static void error(String appendLog, Throwable e) {
        String formatAppendLog = formatLog(appendLog);
        if (XxlJobContext.getXxlJobContext() == null) {
            logger.error(appendLog, e);
            return;
        }
        String jobLogFileName = XxlJobContext.getXxlJobContext().getJobLogFileName();
        if (jobLogFileName != null && jobLogFileName.trim().length() != 0) {
            XxlJobFileAppender.appendLog(jobLogFileName, formatAppendLog);
            StringWriter out = new StringWriter();
            PrintWriter pr = new PrintWriter(out);
            e.printStackTrace(pr);
            XxlJobFileAppender.appendLog(jobLogFileName, out.toString());

            logger.error("[{}]: {}", jobLogFileName, appendLog, e);
        } else {
            logger.error(appendLog, e);
        }
    }

    private static String formatLog(String appendLog) {
        StackTraceElement[] stackTraceElements = (new Throwable()).getStackTrace();
        StackTraceElement callInfo = stackTraceElements[1];
        String formatLocalDateTime = DateFormateUtil.formatLocalDateTime("yyyy-MM-dd HH:mm:ss", LocalDateTime.now());
        return formatLocalDateTime + " [" +
                callInfo.getClassName() + "]-[" +
                callInfo.getMethodName() + "]-[" +
                callInfo.getLineNumber() + "]-[" +
                Thread.currentThread().getName() + "] " +
                (appendLog != null ? appendLog : "");
    }
}
