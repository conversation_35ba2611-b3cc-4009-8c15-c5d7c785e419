package com.autohome.applet.util;

import com.autonews.comm.utils.StringUtils;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.conn.ssl.X509HostnameVerifier;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocket;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Iterator;
import java.util.Map;

public class HttpClientHelperNew {


    public static String post(String url, String body, Map<String, String> headers, String mimeType, String charset, Integer connTimeout, Integer readTimeout) {
        HttpClient client = null;
        HttpPost post = new HttpPost(url);
        String result = "";

        try {
            if (StringUtils.isNotBlank(body)) {
                HttpEntity entity = new StringEntity(body, ContentType.create(mimeType, charset));
                post.setEntity(entity);
            }

            RequestConfig.Builder customReqConf = RequestConfig.custom();
            if (connTimeout != null) {
                customReqConf.setConnectTimeout(connTimeout);
            }

            if (readTimeout != null) {
                customReqConf.setSocketTimeout(readTimeout);
            }


            if (headers != null && !headers.isEmpty()) {
                Iterator var20 = headers.entrySet().iterator();

                while(var20.hasNext()) {
                    Map.Entry<String, String> entry = (Map.Entry)var20.next();
                    post.addHeader((String)entry.getKey(), (String)entry.getValue());
                }
            }


            post.setConfig(customReqConf.build());
            HttpResponse res;
            if (url.startsWith("https")) {
                client = createSSLInsecureClient();
                res = ((HttpClient)client).execute(post);
            } else {
                client = HttpClientHelperNew.client;
                res = ((HttpClient)client).execute(post);
            }

            result = IOUtils.toString(res.getEntity().getContent(), charset);
        } catch (Exception var18) {
            throw new RuntimeException(var18);
        } finally {
            post.releaseConnection();
            if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
                try {
                    ((CloseableHttpClient)client).close();
                } catch (IOException var17) {
                    throw new RuntimeException(var17);
                }
            }

        }

        return result;
    }

    private static HttpClient client;

    static {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(50);
        cm.setDefaultMaxPerRoute(100);
        client = HttpClients.custom().setConnectionManager(cm).build();
    }


    private static CloseableHttpClient createSSLInsecureClient() {
        try {
            SSLContext sslContext = (new SSLContextBuilder()).loadTrustMaterial((KeyStore)null, new TrustStrategy() {
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, new X509HostnameVerifier() {
                public boolean verify(String arg0, SSLSession arg1) {
                    return true;
                }

                public void verify(String host, SSLSocket ssl) throws IOException {
                }

                public void verify(String host, X509Certificate cert) throws SSLException {
                }

                public void verify(String host, String[] cns, String[] subjectAlts) throws SSLException {
                }
            });
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (GeneralSecurityException var2) {
            throw new RuntimeException(var2);
        }
    }

}
