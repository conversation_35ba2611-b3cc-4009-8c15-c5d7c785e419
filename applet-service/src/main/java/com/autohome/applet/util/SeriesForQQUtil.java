package com.autohome.applet.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class SeriesForQQUtil {
    private static final List<SeriesForQQ> seriesForQQList = new ArrayList<>();
    private static final List<SeriesForQQ> seriesForQQListV2 = new ArrayList<>();
    private static final List<SeriesForQQ> seriesForQQListV3 = new ArrayList<>();
    private static final List<SeriesForQQ> seriesForQQListV3202501 = new ArrayList<>();
    private static final List<SeriesForQQ> seriesForQQListV3ForDelete = new ArrayList<>();

    static {
        seriesForQQList.addAll(LoadLoaclFileFaultLightContent());
        seriesForQQListV2.addAll(LoadLoaclFileFaultLightContentV2());
        seriesForQQListV3.addAll(LoadLoaclFileFaultLightContentV3());
        seriesForQQListV3202501.addAll(LoadLoaclFileFaultLightContentV3202501());
        seriesForQQListV3ForDelete.addAll(LoadLoaclFileFaultLightContentV3ForDelete());
    }
    private static List<SeriesForQQ> LoadLoaclFileFaultLightContent() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/series_qq.xlsx");
            List<SeriesForQQ> seriesForQQList = ExcelUtils.read(path, SeriesForQQ.class);
            log.info("load series success, size:{}", seriesForQQList.size());
            return seriesForQQList;
        }
        catch (Exception ex){
            log.error("load series info error, ", ex);
        }
        return new ArrayList<>();
    }

    private static List<SeriesForQQ> LoadLoaclFileFaultLightContentV2() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/series_qq_v2.xlsx");
            List<SeriesForQQ> seriesForQQList = ExcelUtils.read(path, SeriesForQQ.class);
            log.info("load series_v2 success, size:{}", seriesForQQList.size());
            return seriesForQQList;
        }
        catch (Exception ex){
            log.error("load series_v2 info error, ", ex);
        }
        return new ArrayList<>();
    }
    private static List<SeriesForQQ> LoadLoaclFileFaultLightContentV3() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/series_qq_v3.xlsx");
            List<SeriesForQQ> seriesForQQList = ExcelUtils.read(path, SeriesForQQ.class);
            log.info("load series_v3 success, size:{}", seriesForQQList.size());
            return seriesForQQList;
        }
        catch (Exception ex){
            log.error("load series_v3 info error, ", ex);
        }
        return new ArrayList<>();
    }
    private static List<SeriesForQQ> LoadLoaclFileFaultLightContentV3202501() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/series_qq_v3_202501.xlsx");
            List<SeriesForQQ> seriesForQQList = ExcelUtils.read(path, SeriesForQQ.class);
            log.info("load series_v3_202501 success, size:{}", seriesForQQList.size());
            return seriesForQQList;
        }
        catch (Exception ex){
            log.error("load series_v3_202501 info error, ", ex);
        }
        return new ArrayList<>();
    }
    private static List<SeriesForQQ> LoadLoaclFileFaultLightContentV3ForDelete() {
        try{
            InputStream path = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/series_qq_v3_delete.xlsx");
            List<SeriesForQQ> seriesForQQList = ExcelUtils.read(path, SeriesForQQ.class);
            log.info("load series_v3_delete success, size:{}", seriesForQQList.size());
            return seriesForQQList;
        }
        catch (Exception ex){
            log.error("load series_v3_delete info error, ", ex);
        }
        return new ArrayList<>();
    }
//    public static List<SeriesForQQ> getAllBrandForQQV3(){
//        return seriesForQQListV3;
//    }

    public static List<SeriesForQQ> getSeriesForQQListV3(){
        return seriesForQQListV3;
    }
    public static List<SeriesForQQ> getSeriesForQQListV3ForDelete(){
        return seriesForQQListV3ForDelete;
    }
    public static List<SeriesForQQ> getSeriesForQQListV3202501(){
        return seriesForQQListV3202501;
    }

    public static class SeriesForQQ{
        private String seriesId;
        private String seriesName;
        private String alias;
        private String query;
        private String cityCode;
        private List<String> cityCodeList;

        public String getAlias() {
            return alias;
        }

        public void setAlias(String alias) {
            this.alias = alias;
        }

        public String getSeriesId() {
            return seriesId;
        }

        public void setSeriesId(String seriesId) {
            this.seriesId = seriesId;
        }

        public String getSeriesName() {
            return seriesName;
        }

        public void setSeriesName(String seriesName) {
            this.seriesName = seriesName;
        }

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public String getCityCode() {
            return cityCode;
        }

        public void setCityCode(String cityCode) {
            this.cityCode = cityCode;
        }

        public List<String> getCityCodeList() {
            if(StringUtils.isNotEmpty(this.cityCode)){
                String[] cityCodes = this.cityCode.split("\n");
                return Arrays.asList(cityCodes);
            }
            return new ArrayList<>();
        }

        public void setCityCodeList(List<String> cityCodeList) {
            this.cityCodeList = cityCodeList;
        }
    }

    public static void main(String[] args) {
    }
}
