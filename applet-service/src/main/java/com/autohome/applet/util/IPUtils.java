package com.autohome.applet.util;

import com.autohome.applet.util.netcoreapi.StringUtil;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;

/**
 * Created by hanshanfeng on 2017/7/6.
 */
public class IPUtils {

    private static DatabaseReader databaseReader;

    static {
        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("static/GeoLite2-City.mmdb");
        try {
            databaseReader = new DatabaseReader.Builder(is).build();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String getRemoteIp(HttpServletRequest request) {
        if (request == null) {
            return null;
        } else {
            String result = request.getHeader("cip");
            if (!isNullOrEmpty(result) && result.indexOf("\\|") > -1) {
                result = result.split("\\|")[0];
            }

            if (isNullOrEmpty(result)) {
                result = request.getHeader("HTTP_CIP");
            }

            if (isNullOrEmpty(result)) {
                result = request.getHeader("x-forwarded-for");
                if (!isNullOrEmpty(result)) {
                    result = result.split(",")[0].trim();
                }
            }

            if (isNullOrEmpty(result)) {
                result = request.getRemoteAddr();
            }

            return result;
        }
    }

    private static boolean isNullOrEmpty(String str) {
        return str == null || "".equals(str);
    }

    //    public static String getRemoteIp(HttpServletRequest request) {
//        try {
//            String ip = request.getHeader("cip");
//            if (StringUtils.isNotNullAndWhiteSpace(ip) && !"unKnown".equalsIgnoreCase(ip)) {
//                return ip;
//            }
//            ip = request.getHeader("Cdn-Src-Ip");
//            if (StringUtils.isNotNullAndWhiteSpace(ip) && !"unKnown".equalsIgnoreCase(ip)) {
//                return ip;
//            }
//            ip = request.getHeader("X-Forwarded-For");
//            if (StringUtils.isNotNullAndWhiteSpace(ip) && !"unKnown".equalsIgnoreCase(ip)) {
//                //多次反向代理后会有多个ip值，第一个ip才是真实ip
//                int index = ip.indexOf(",");
//                if (index != -1) {
//                    return ip.substring(0, index);
//                } else {
//                    return ip;
//                }
//            }
//            ip = request.getRemoteAddr();
//            if (StringUtils.isNotNullAndWhiteSpace(ip)) {
//                return ip;
//            }
//            return ip;
//        } catch (Exception e) {
//            return "";
//        }
//    }
    public static String getIp2(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtil.isNotEmpty(ip) && !"unKnown".equalsIgnoreCase(ip)) {
            //多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = ip.indexOf(",");
            if (index != -1) {
                return ip.substring(0, index);
            } else {
                return ip;
            }
        }
        ip = request.getHeader("X-Real-IP");
        if (StringUtil.isNotEmpty(ip) && !"unKnown".equalsIgnoreCase(ip)) {
            return ip;
        }
        return request.getRemoteAddr();
    }

    public static String getRemoteIpNew(HttpServletRequest request) {
        String[] headers = new String[] {
                "X-Real-IP",
                "X-Forwarded-For",
                "HTTP_CIP",
                "HTTP_X_FORWARDED_FOR",
                "X-Original-Forwarded-For",
                "REMOTE_ADDR",
                "HTTP_CDN_SRC_IP"
        };
        for (String header : headers) {
            String headerValue = request.getHeader(header);
            if (!StringUtils.isEmpty(headerValue) && "unknown".equals(headerValue)) {
                return headerValue;
            }
        }
        return "127.0.0.1";
    }

    public static String getGeoIp2City(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            CityResponse city = databaseReader.city(address);
            if (city != null) {
                City city1 = city.getCity();
                if (city1 != null) {
                    return city1.getName();
                }
                System.out.println(city1);
            }
        } catch (IOException | GeoIp2Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
