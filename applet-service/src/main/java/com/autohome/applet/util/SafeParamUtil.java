package com.autohome.applet.util;

public class SafeParamUtil {

    public static int toSafeInt(Object obj) {
        return toSafeInt(obj, -1);
    }

    public static String toSafeString(Object obj) {
        return toSafeString(obj, "");
    }

    public static int toSafeInt(Object obj, Integer defaultValue) {
        if (defaultValue == null) {
            defaultValue = -1;
        }

        if (obj != null && !obj.toString().trim().isEmpty()) {
            String str = obj.toString().trim().toLowerCase();
            if ("true".equals(str)) {
                return 1;
            } else if ("false".equals(str)) {
                return 0;
            } else {
                try {
                    int i = Integer.parseInt(str);
                    return i;
                } catch (NumberFormatException var4) {
                    NumberFormatException e = var4;
                    e.printStackTrace();
                    return defaultValue;
                }
            }
        } else {
            return defaultValue;
        }
    }

    public static String toSafeString(Object obj, String defaultValue) {
        if (defaultValue == null) {
            defaultValue = "";
        }

        return obj == null ? defaultValue : obj.toString();
    }

    public static Long toSafeLong(Object obj) {
        return toSafeLong(obj, 0L);
    }

    public static Long toSafeLong(Object obj, Long defaultValue) {
        if (defaultValue == null) {
            defaultValue = -1L;
        }

        if (obj != null && !"".equals(obj.toString().trim())) {
            String str = obj.toString().trim().toLowerCase();
            if ("true".equals(str)) {
                return 1L;
            } else if ("false".equals(str)) {
                return 0L;
            } else {
                try {
                    Long i = Long.parseLong(str);
                    return i;
                } catch (NumberFormatException var4) {
                    NumberFormatException e = var4;
                    e.printStackTrace();
                    return defaultValue;
                }
            }
        } else {
            return defaultValue;
        }
    }
}
