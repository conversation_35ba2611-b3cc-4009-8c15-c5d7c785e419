package com.autohome.applet.util;


import org.apache.commons.lang3.StringUtils;

/**
 * Html过滤工具类(防止跨站攻击)
 * <AUTHOR>
 * @date 2017年6月29日 下午1:35:53
 * @since
 */
public class HtmlXssUtil {
    
    /**
     * 过滤html标签
     * <AUTHOR>
     * @date 2017年7月11日 下午1:21:04
     * @param htmlStr
     * @return
     */
    public static String filterHtml(String htmlStr) {
        if(StringUtils.isEmpty(htmlStr)) return "";
    	htmlStr = htmlStr.replaceAll("<script[^>]*?>.*?</script>", "");
    	htmlStr = htmlStr.replaceAll("<(.[^>]*)>", "");
    	htmlStr = htmlStr.replaceAll("([\r\n])[\\s]+", "");
    	htmlStr = htmlStr.replace("-->", "");
    	htmlStr = htmlStr.replaceAll("<!--.*", "");
    	htmlStr = htmlStr.replaceAll("&(quot|#34);", "\"");
    	htmlStr = htmlStr.replaceAll("&(amp|#38);", "&");
    	htmlStr = htmlStr.replaceAll("&(lt|#60);", "<");
    	htmlStr = htmlStr.replaceAll("&(gt|#62);", ">");
    	htmlStr = htmlStr.replaceAll("&(nbsp|#160);", "   ");
    	htmlStr = htmlStr.replaceAll("&(iexcl|#161);", "\\xa1");
        htmlStr = htmlStr.replaceAll("&(cent|#162);", "\\xa2");
        htmlStr = htmlStr.replaceAll("&(pound|#163);", "\\xa3");
        htmlStr = htmlStr.replaceAll("&(copy|#169);", "\\xa9");
        htmlStr = htmlStr.replaceAll("&#(\\d+);", "");
        htmlStr = htmlStr.replace("<", "");
        htmlStr = htmlStr.replace(">", "");
        htmlStr = htmlStr.replace("\r\n", "");
        return htmlStr.trim();
    }
}
