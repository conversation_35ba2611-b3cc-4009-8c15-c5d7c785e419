package com.autohome.applet.util;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ConvertCityUtil {
    private static final Map<String, String> proMap = new HashMap<>();

    static {
        // 添加键值对
        proMap.put("之家城市", "百度城市");
        proMap.put("厦门", "厦门市");
        proMap.put("莆田", "莆田市");
        proMap.put("三明", "三明市");
        proMap.put("郑州", "郑州市");
        proMap.put("南宁", "南宁市");
        proMap.put("柳州", "柳州市");
        proMap.put("桂林", "桂林市");
        proMap.put("梧州", "梧州市");
        proMap.put("兰州", "兰州市");
        proMap.put("乌鲁木齐", "乌鲁木齐市");
        proMap.put("洛阳", "洛阳市");
        proMap.put("平顶山", "平顶山市");
        proMap.put("安阳", "安阳市");
        proMap.put("广州", "广州市");
        proMap.put("北京", "北京市");
        proMap.put("天津", "天津市");
        proMap.put("泉州", "泉州市");
        proMap.put("漳州", "漳州市");
        proMap.put("龙岩", "龙岩市");
        proMap.put("新乡", "新乡市");
        proMap.put("石家庄", "石家庄市");
        proMap.put("唐山", "唐山市");
        proMap.put("邯郸", "邯郸市");
        proMap.put("邢台", "邢台市");
        proMap.put("保定", "保定市");
        proMap.put("北海", "北海市");
        proMap.put("张家口", "张家口市");
        proMap.put("承德", "承德市");
        proMap.put("沧州", "沧州市");
        proMap.put("廊坊", "廊坊市");
        proMap.put("太原", "太原市");
        proMap.put("大同", "大同市");
        proMap.put("长治", "长治市");
        proMap.put("晋城", "晋城市");
        proMap.put("运城", "运城市");
        proMap.put("呼和浩特", "呼和浩特市");
        proMap.put("沈阳", "沈阳市");
        proMap.put("大连", "大连市");
        proMap.put("钦州", "钦州市");
        proMap.put("贵港", "贵港市");
        proMap.put("来宾", "来宾市");
        proMap.put("重庆", "重庆市");
        proMap.put("焦作", "焦作市");
        proMap.put("濮阳", "濮阳市");
        proMap.put("许昌", "许昌市");
        proMap.put("漯河", "漯河市");
        proMap.put("三门峡", "三门峡市");
        proMap.put("南阳", "南阳市");
        proMap.put("商丘", "商丘市");
        proMap.put("鞍山", "鞍山市");
        proMap.put("丹东", "丹东市");
        proMap.put("锦州", "锦州市");
        proMap.put("朝阳", "朝阳市");
        proMap.put("长春", "长春市");
        proMap.put("哈尔滨", "哈尔滨市");
        proMap.put("鹤岗", "鹤岗市");
        proMap.put("大庆", "大庆市");
        proMap.put("佳木斯", "佳木斯市");
        proMap.put("牡丹江", "牡丹江市");
        proMap.put("上海", "上海市");
        proMap.put("南京", "南京市");
        proMap.put("信阳", "信阳市");
        proMap.put("驻马店", "驻马店市");
        proMap.put("长沙", "长沙市");
        proMap.put("包头", "包头市");
        proMap.put("乌海", "乌海市");
        proMap.put("赤峰", "赤峰市");
        proMap.put("通辽", "通辽市");
        proMap.put("无锡", "无锡市");
        proMap.put("徐州", "徐州市");
        proMap.put("韶关", "韶关市");
        proMap.put("深圳", "深圳市");
        proMap.put("鄂尔多斯", "鄂尔多斯市");
        proMap.put("呼伦贝尔", "呼伦贝尔市");
        proMap.put("常州", "常州市");
        proMap.put("株洲", "株洲市");
        proMap.put("惠州", "惠州市");
        proMap.put("盘锦", "盘锦市");
        proMap.put("吉林", "吉林市");
        proMap.put("延边", "延边朝鲜族自治州");
        proMap.put("苏州", "苏州市");
        proMap.put("梅州", "梅州市");
        proMap.put("河源", "河源市");
        proMap.put("阳江", "阳江市");
        proMap.put("清远", "清远市");
        proMap.put("东莞", "东莞市");
        proMap.put("秦皇岛", "秦皇岛市");
        proMap.put("齐齐哈尔", "齐齐哈尔市");
        proMap.put("内江", "内江市");
        proMap.put("南充", "南充市");
        proMap.put("宜宾", "宜宾市");
        proMap.put("淮安", "淮安市");
        proMap.put("盐城", "盐城市");
        proMap.put("镇江", "镇江市");
        proMap.put("泰州", "泰州市");
        proMap.put("宿迁", "宿迁市");
        proMap.put("杭州", "杭州市");
        proMap.put("宁波", "宁波市");
        proMap.put("温州", "温州市");
        proMap.put("嘉兴", "嘉兴市");
        proMap.put("金华", "金华市");
        proMap.put("台州", "台州市");
        proMap.put("丽水", "丽水市");
        proMap.put("昆明", "昆明市");
        proMap.put("合肥", "合肥市");
        proMap.put("抚顺", "抚顺市");
        proMap.put("曲靖", "曲靖市");
        proMap.put("玉溪", "玉溪市");
        proMap.put("南通", "南通市");
        proMap.put("连云港", "连云港市");
        proMap.put("扬州", "扬州市");
        proMap.put("绍兴", "绍兴市");
        proMap.put("衢州", "衢州市");
        proMap.put("中山", "中山市");
        proMap.put("芜湖", "芜湖市");
        proMap.put("淮南", "淮南市");
        proMap.put("安庆", "安庆市");
        proMap.put("滁州", "滁州市");
        proMap.put("阜阳", "阜阳市");
        proMap.put("宿州", "宿州市");
        proMap.put("亳州", "亳州市");
        proMap.put("宣城", "宣城市");
        proMap.put("福州", "福州市");
        proMap.put("南昌", "南昌市");
        proMap.put("景德镇", "景德镇市");
        proMap.put("赣州", "赣州市");
        proMap.put("宜春", "宜春市");
        proMap.put("泰安", "泰安市");
        proMap.put("济南", "济南市");
        proMap.put("青岛", "青岛市");
        proMap.put("淄博", "淄博市");
        proMap.put("枣庄", "枣庄市");
        proMap.put("东营", "东营市");
        proMap.put("烟台", "烟台市");
        proMap.put("海口", "海口市");
        proMap.put("珠海", "珠海市");
        proMap.put("汕头", "汕头市");
        proMap.put("佛山", "佛山市");
        proMap.put("马鞍山", "马鞍山市");
        proMap.put("淮北", "淮北市");
        proMap.put("铜陵", "铜陵市");
        proMap.put("潍坊", "潍坊市");
        proMap.put("济宁", "济宁市");
        proMap.put("威海", "威海市");
        proMap.put("日照", "日照市");
        proMap.put("莱芜", "莱芜市");
        proMap.put("临沂", "临沂市");
        proMap.put("贵阳", "贵阳市");
        proMap.put("德州", "德州市");
        proMap.put("南平", "南平市");
        proMap.put("宁德", "宁德市");
        proMap.put("遵义", "遵义市");
        proMap.put("聊城", "聊城市");
        proMap.put("滨州", "滨州市");
        proMap.put("菏泽", "菏泽市");
        proMap.put("吉安", "吉安市");
        proMap.put("江门", "江门市");
        proMap.put("湛江", "湛江市");
        proMap.put("茂名", "茂名市");
        proMap.put("西安", "西安市");
        proMap.put("开封", "开封市");
        proMap.put("武汉", "武汉市");
        proMap.put("咸阳", "咸阳市");
        proMap.put("渭南", "渭南市");
        proMap.put("延安", "延安市");
        proMap.put("银川", "银川市");
        proMap.put("十堰", "十堰市");
        proMap.put("襄阳", "襄阳市");
        proMap.put("葫芦岛", "葫芦岛市");
        proMap.put("湖州", "湖州市");
        proMap.put("舟山", "舟山市");
        proMap.put("衡阳", "衡阳市");
        proMap.put("邵阳", "邵阳市");
        proMap.put("怀化", "怀化市");
        proMap.put("娄底", "娄底市");
        proMap.put("六安", "六安市");
        proMap.put("晋中", "晋中市");
        proMap.put("临汾", "临汾市");
        proMap.put("揭阳", "揭阳市");
        proMap.put("云浮", "云浮市");
        proMap.put("周口", "周口市");
        proMap.put("潜江", "潜江市");
        proMap.put("本溪", "本溪市");
        proMap.put("防城港", "防城港市");
        proMap.put("玉林", "玉林市");
        proMap.put("湘潭", "湘潭市");
        proMap.put("岳阳", "岳阳市");
        proMap.put("常德", "常德市");
        proMap.put("益阳", "益阳市");
        proMap.put("郴州", "郴州市");
        proMap.put("永州", "永州市");
        proMap.put("三亚", "三亚市");
        proMap.put("成都", "成都市");
        proMap.put("上饶", "上饶市");
        proMap.put("四平", "四平市");
        proMap.put("鹤壁", "鹤壁市");
        proMap.put("宜昌", "宜昌市");
        proMap.put("肇庆", "肇庆市");
        proMap.put("儋州", "儋州市");
        proMap.put("广元", "广元市");
        proMap.put("遂宁", "遂宁市");
        proMap.put("达州", "达州市");
        proMap.put("雅安", "雅安市");
        proMap.put("文山", "文山壮族苗族自治州");
        proMap.put("西双版纳", "西双版纳傣族自治州");
        proMap.put("大理", "大理白族自治州");
        proMap.put("泸州", "泸州市");
        proMap.put("绵阳", "绵阳市");
        proMap.put("毕节", "毕节市");
        proMap.put("黔西南", "黔西南布依族苗族自治州");
        proMap.put("黔东南", "黔东南苗族侗族自治州");
        proMap.put("乐山", "乐山市");
        proMap.put("六盘水", "六盘水市");
        proMap.put("黔南", "黔南布依族苗族自治州");
        proMap.put("红河", "红河哈尼族彝族自治州");
        proMap.put("西宁", "西宁市");
        proMap.put("巴音郭楞", "巴音郭楞蒙古自治州");
        proMap.put("宝鸡", "宝鸡市");
        proMap.put("汉中", "汉中市");
        proMap.put("榆林", "榆林市");
        proMap.put("安康", "安康市");
        proMap.put("黄冈", "黄冈市");
        proMap.put("潮州", "潮州市");
        proMap.put("九江", "九江市");
        proMap.put("自贡", "自贡市");
        proMap.put("德阳", "德阳市");
        proMap.put("新余", "新余市");
        proMap.put("萍乡", "萍乡市");
        proMap.put("抚州", "抚州市");
        proMap.put("济源", "济源市");
        proMap.put("孝感", "孝感市");
        proMap.put("荆州", "荆州市");
        proMap.put("巴中", "巴中市");
        proMap.put("保山", "保山市");
        proMap.put("昭通", "昭通市");
        proMap.put("普洱", "普洱市");
        proMap.put("临沧", "临沧市");
        proMap.put("吴忠", "吴忠市");
        proMap.put("昌吉", "昌吉回族自治州");
    }

    public static String convertToBaiduCity(String localCity){
        String baiduCity = proMap.get(localCity);
        return baiduCity;
    }
}
