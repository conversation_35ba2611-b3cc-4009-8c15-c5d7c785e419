package com.autohome.applet.util;

import com.autohome.applet.util.uploadfile.ImageUploader;
import com.autohome.applet.util.uploadfile.UploadFileInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
/**
 * 百度笔记加载固定内容
 * */
public class BaiduNoteUtil {
    private static final int EXID_COLUMN_INDEX = 0; // exid列索引
    private static final int TITLE_COLUMN_INDEX = 1; // 标题列索引
    private static final int CONTENT_COLUMN_INDEX = 2; // 内容列索引
    private static final int IMAGE_START_COLUMN_INDEX = 3; // 图片开始列索引

    private static ImageUploader imageUploader = null;

    static {
        imageUploader = new ImageUploader("www3.autoimg.cn", "lightapp", "appletdfs", "305c300d06092a864886f70d0101010500034b0030480241008aaba57f910bd6a6732a7dc9404756ff30d3181fb79f30e4e4207e6b95fea51176665213fad2d76ba201e1251f12feef0c54fc4bb9dd3782d22ff44e15dbbd2d0203010001");
    }

    /**
     * 读取Excel文件并转换为BaiduNoteExcel列表
     * @param filePath Excel文件路径
     * @return BaiduNoteExcel列表
     */
    public List<BaiduNoteExcel> readExcelWithImages(String filePath) {
        List<BaiduNoteExcel> result = new ArrayList<>();

        try (InputStream fis = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath)) {
            if (fis == null) {
                throw new IOException("文件未找到: " + filePath);
            }

            Workbook workbook = new XSSFWorkbook(fis);

            Sheet sheet = workbook.getSheetAt(0); // 读取第一个sheet
            Map<Integer, List<PictureData>> pictures = extractAllPictures(workbook); // 提取所有图片

            // 从第二行开始读取（假设第一行是标题行）
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) break;

                // 读取标题和内容
                String exid = getCellStringValue(row.getCell(EXID_COLUMN_INDEX));
                String title = getCellStringValue(row.getCell(TITLE_COLUMN_INDEX));
                String content = getCellStringValue(row.getCell(CONTENT_COLUMN_INDEX));
                BaiduNoteExcel note = new BaiduNoteExcel(exid,title, content);

                List<String> imgList = new ArrayList<>();

                List<PictureData> cellPictures = pictures.getOrDefault(rowIndex, Collections.emptyList())
                        .stream()
//                        .filter(pd -> isPictureInCell(pd, finalRowIndex, finalColIndex, workbook))
                        .collect(Collectors.toList());
                // 上传图片并获取URL
                for (PictureData picture : cellPictures) {
                    String imageUrl = uploadImage(picture.getData());
                    if (imageUrl != null) {
                        imgList.add(imageUrl.indexOf("autohomecar__") >0 ? imageUrl.replace("autohomecar__", "480x0_autohomecar__") : "");
                    }
                }
                note.setImgList(imgList);
                note.setVersion(DateHelper.getNowString(DateHelper.DATEFORMAT_ONLY_DATE));
                result.add(note);
            }
        } catch (IOException e) {
            log.error("readExcelWithImages error ", e);
        }

        return result;
    }

    /**
     * 提取工作簿中的所有图片
     */
    private Map<Integer, List<PictureData>> extractAllPictures(Workbook workbook) {
        Map<Integer, List<PictureData>> pictures = new HashMap<>();

        Sheet sheet = workbook.getSheetAt(0);
        if (sheet instanceof XSSFSheet) {
            XSSFSheet xssfSheet = (XSSFSheet) sheet;
            XSSFDrawing drawing = xssfSheet.getDrawingPatriarch();

            if (drawing != null) {
                for (XSSFShape shape : drawing.getShapes()) {
                    if (shape instanceof XSSFPicture) {
                        XSSFPicture picture = (XSSFPicture) shape;
                        XSSFClientAnchor anchor = picture.getPreferredSize();
                        int rowIndex = anchor.getRow1();

                        if (!pictures.containsKey(rowIndex)) {
                            pictures.put(rowIndex, new ArrayList<>());
                        }
                        pictures.get(rowIndex).add(picture.getPictureData());
                    }
                }
            }
        }

        return pictures;
    }

    /**
     * 判断图片是否在指定单元格中
     */
    private boolean isPictureInCell(PictureData picture, int rowIndex, int colIndex, Workbook workbook) {
        // 这里简化处理，实际应根据锚点位置精确判断图片属于哪个单元格
        // 更精确的实现需要检查XSSFClientAnchor的坐标
        return colIndex >= IMAGE_START_COLUMN_INDEX;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 上传图片到接口并返回URL
     */
    private String uploadImage(byte[] imageData) {
        try {
            UploadFileInfo fileInfo = imageUploader.Save(new ByteArrayInputStream(imageData), "jpg");
            if (fileInfo.getReturnCode() == 0) {
                return fileInfo.getImgUrl();
            }
            return null;
            // 这里使用模拟实现，实际应根据接口文档实现
            // 示例使用HTTP POST上传图片

//            URL url = new URL(uploadUrl);
//            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//            connection.setRequestMethod("POST");
//            connection.setRequestProperty("Content-Type", "application/octet-stream");
//            connection.setDoOutput(true);
//
//            // 发送图片数据
//            connection.getOutputStream().write(imageData);
//
//            // 获取响应
//            int responseCode = connection.getResponseCode();
//            if (responseCode == HttpURLConnection.HTTP_OK) {
//                // 假设接口返回JSON格式: {"url": "http://example.com/image.jpg"}
//                // 这里简化处理，实际应解析JSON响应
//                try (Scanner scanner = new Scanner(connection.getInputStream())) {
//                    String response = scanner.useDelimiter("\\A").next();
//                    // 简单提取URL，实际应使用JSON解析库
//                    if (response.contains("\"url\"")) {
//                        return response.split("\"url\"")[1].split("\"")[1];
//                    }
//                }
//            }
        } catch (Exception e) {
            log.error("readExcelWithImages uploadImage error ", e);
        }
        return null;
    }

    public static class BaiduNoteExcel{
        /**
         * 提前预制好id， 有id修改，没有id插入
         * */
        private String exid;
        private String title;
        private String cotent;
        private HashMap<String, String> cityInfo;

        private String authorName;
        private String authorImg;
        /**
         * 版本 yyyy-mm-dd
         * */
        private String version;
        private List<String> imgList;


        public BaiduNoteExcel(String exid, String title, String cotent) {
            this.exid = exid;
            this.title = title;
            this.cotent = cotent;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCotent() {
            return cotent;
        }

        public void setCotent(String cotent) {
            this.cotent = cotent;
        }

        public List<String> getImgList() {
            return imgList;
        }

        public void setImgList(List<String> imgList) {
            this.imgList = imgList;
        }

        public HashMap<String, String> getCityInfo() {
            return cityInfo;
        }

        public void setCityInfo(HashMap<String, String> cityInfo) {
            this.cityInfo = cityInfo;
        }

        /**
         * 按需求，传输固定的作者名称
         * */
        public String getAuthorName() {
//            return authorName;
            return "之家车友2274263";
        }

//        public void setAuthorName(String authorName) {
//            this.authorName = authorName;
//        }

        /**
         * 按需求，传输固定的作者头像
         * */
        public String getAuthorImg() {
            return "https://img3.autoimg.cn/ugcfollowdfs/g30/M07/2A/CC/1400x0_autohomecar__Chtk2Gf993uAWkb9ACeTXqPZtjQ216.png";
//            return authorImg;
        }

//        public void setAuthorImg(String authorImg) {
//            this.authorImg = authorImg;
//        }


        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getExId() {
            return exid;
        }

        public void setExId(String exid) {
            this.exid = exid;
        }
    }

    public static void main(String[] args) {
    }
}
