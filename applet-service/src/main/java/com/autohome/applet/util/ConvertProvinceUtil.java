package com.autohome.applet.util;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ConvertProvinceUtil {
    private static final Map<String, String> proMap = new HashMap<>();

    static {
        // 添加键值对
        proMap.put("福建", "福建省");
        proMap.put("河南", "河南省");
        proMap.put("广西", "广西壮族自治区");
        proMap.put("甘肃", "甘肃省");
        proMap.put("新疆", "新疆维吾尔自治区");
        proMap.put("广东", "广东省");
        proMap.put("北京", "北京市");
        proMap.put("天津", "天津市");
        proMap.put("河北", "河北省");
        proMap.put("山西", "山西省");
        proMap.put("内蒙古", "内蒙古自治区");
        proMap.put("辽宁", "辽宁省");
        proMap.put("重庆", "重庆市");
        proMap.put("吉林", "吉林省");
        proMap.put("黑龙江", "黑龙江省");
        proMap.put("上海", "上海市");
        proMap.put("江苏", "江苏省");
        proMap.put("湖南", "湖南省");
        proMap.put("四川", "四川省");
        proMap.put("浙江", "浙江省");
        proMap.put("云南", "云南省");
        proMap.put("安徽", "安徽省");
        proMap.put("江西", "江西省");
        proMap.put("山东", "山东省");
        proMap.put("海南", "海南省");
        proMap.put("贵州", "贵州省");
        proMap.put("陕西", "陕西省");
        proMap.put("湖北", "湖北省");
        proMap.put("宁夏", "宁夏回族自治区");
        proMap.put("青海", "青海省");
    }

    public static String convertToBaiduProvince(String localProvince){
        String baiduProvince = proMap.get(localProvince);
        return baiduProvince;
    }
}
