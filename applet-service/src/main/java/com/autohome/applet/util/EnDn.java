package com.autohome.applet.util;

import com.autonews.comm.BaseModel;
import com.autonews.comm.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnDn {

    final static String userName = "news-lightapp-apiRpkVlwQG";
    final static String password ="S588IZ6NM36X";

    public static String en(String data) {
        return en(Arrays.asList(data)).get(data);
    }
    public static String dn(String data){
        return dn(Arrays.asList(data)).get(data);
    }

    public static Map<String,String> en(List<String> datas){
        String url = "http://se.api.cloud.corpautohome.com/v1/mobile/encrypt?_appid=lapp&consumerId=usrprd.dev.news&t="+System.currentTimeMillis()/1000;
        String authorization = new String(Base64.encodeBase64((userName + ":" + password).getBytes(StandardCharsets.UTF_8)));
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization","Basic "+authorization);
        String jsonResult = HttpClientHelperNew.post(url, JsonUtils.toString(datas), headers,"application/json", "UTF-8", 10000, 10000);
        Map<String,String> result = new HashMap<>();
        if(StringUtils.isBlank(jsonResult)){
            datas.forEach(x->result.put(x,""));
            return result;
        }
        BaseModel<List<EnDnItem>> enResult = JsonUtils.toObject(jsonResult, new TypeReference<BaseModel<List<EnDnItem>>>() {
        });
        if(enResult==null||!enResult.getReturncode().equals(0)||enResult.getResult()==null||enResult.getResult().size()==0){
            datas.forEach(x->result.put(x,""));
            return result;
        }
        datas.forEach(item->{
            if(enResult.getResult().stream().anyMatch(x->x!=null&& x.getMobile()!=null && x.getMobile().equals(item))) {
                EnDnItem enDnItem = enResult.getResult().stream().filter(x ->x!=null && x.getMobile()!=null && x.getMobile().equals(item)).findFirst().orElse(null);
                result.put(item, enDnItem == null ? "" : enDnItem.getEncrypted());
            }else{
                result.put(item, "");
            }
        });
        return result;
    }


    public static Map<String,String> dn(List<String> datas){
        String url = "http://se.api.cloud.corpautohome.com/v1/mobile/decrypt?_appid=lapp&consumerId=usrprd.dev.news&t="+System.currentTimeMillis()/1000;
        String authorization = new String(Base64.encodeBase64((userName + ":" + password).getBytes(StandardCharsets.UTF_8)));
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization","Basic "+authorization);
        String jsonResult = HttpClientHelperNew.post(url, JsonUtils.toString(datas), headers,"application/json", "UTF-8", 10000, 10000);
        Map<String,String> result = new HashMap<>();
        if(StringUtils.isBlank(jsonResult)){
            datas.forEach(x->result.put(x,""));
            return result;
        }
        BaseModel<List<EnDnItem>> enResult = JsonUtils.toObject(jsonResult, new TypeReference<BaseModel<List<EnDnItem>>>() {
        });
        if(enResult==null||!enResult.getReturncode().equals(0)||enResult.getResult()==null||enResult.getResult().size()==0){
            datas.forEach(x->result.put(x,""));
            return result;
        }
        datas.forEach(item->{
            EnDnItem enDnItem = enResult.getResult().stream().filter(x->x.getEncrypted().equals(item)).findFirst().orElse(null);
            result.put(item,enDnItem==null?"":enDnItem.getMobile());
        });
        return result;
    }
}
